<html>

<head>
<meta http-equiv=Content-Type content="text/html; charset=windows-1252">
<meta name=Generator content="Microsoft Word 11 (filtered)">
<title>Introduction</title>
<style>
<!--
 /* Font Definitions */
 @font-face
	{font-family:Wingdings;
	panose-1:5 0 0 0 0 0 0 0 0 0;}
 /* Style Definitions */
 p.<PERSON>, l<PERSON>.<PERSON>, div.Mso<PERSON>orm<PERSON>
	{margin:0cm;
	margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	color:windowtext;}
h1
	{margin:0cm;
	margin-bottom:.0001pt;
	page-break-after:avoid;
	font-size:10.0pt;
	font-family:"Times New Roman";
	color:windowtext;
	font-weight:bold;}
h2
	{margin:0cm;
	margin-bottom:.0001pt;
	page-break-after:avoid;
	font-size:14.0pt;
	font-family:Arial;
	color:windowtext;
	font-weight:bold;}
h3
	{margin:0cm;
	margin-bottom:.0001pt;
	page-break-after:avoid;
	font-size:10.0pt;
	font-family:Arial;
	color:windowtext;
	font-weight:bold;
	font-style:italic;}
h4
	{margin:0cm;
	margin-bottom:.0001pt;
	page-break-after:avoid;
	font-size:12.0pt;
	font-family:Arial;
	color:navy;
	font-weight:bold;}
h5
	{margin:0cm;
	margin-bottom:.0001pt;
	page-break-after:avoid;
	font-size:11.0pt;
	font-family:Arial;
	color:navy;
	font-weight:bold;
	font-style:italic;}
h6
	{margin-top:0cm;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:36.0pt;
	margin-bottom:.0001pt;
	page-break-after:avoid;
	font-size:11.0pt;
	font-family:Arial;
	color:navy;
	font-weight:bold;
	font-style:italic;}
p.MsoHeading7, li.MsoHeading7, div.MsoHeading7
	{margin-top:0cm;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:108.0pt;
	margin-bottom:.0001pt;
	page-break-after:avoid;
	font-size:11.0pt;
	font-family:Arial;
	color:#003366;
	font-style:italic;}
p.MsoHeading8, li.MsoHeading8, div.MsoHeading8
	{margin-top:0cm;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:36.0pt;
	margin-bottom:.0001pt;
	page-break-after:avoid;
	font-size:11.0pt;
	font-family:Arial;
	color:black;
	font-weight:bold;}
p.MsoBodyText, li.MsoBodyText, div.MsoBodyText
	{margin:0cm;
	margin-bottom:.0001pt;
	font-size:10.0pt;
	font-family:Arial;
	color:windowtext;}
p.MsoBodyTextIndent, li.MsoBodyTextIndent, div.MsoBodyTextIndent
	{margin-top:0cm;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:36.0pt;
	margin-bottom:.0001pt;
	font-size:10.0pt;
	font-family:Arial;
	color:windowtext;}
p.MsoBodyText2, li.MsoBodyText2, div.MsoBodyText2
	{margin:0cm;
	margin-bottom:.0001pt;
	font-size:11.0pt;
	font-family:Arial;
	color:windowtext;}
p.MsoBodyTextIndent2, li.MsoBodyTextIndent2, div.MsoBodyTextIndent2
	{margin-top:0cm;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:36.0pt;
	margin-bottom:.0001pt;
	font-size:11.0pt;
	font-family:Arial;
	color:#003366;}
p.MsoBodyTextIndent3, li.MsoBodyTextIndent3, div.MsoBodyTextIndent3
	{margin-top:0cm;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:36.0pt;
	margin-bottom:.0001pt;
	font-size:11.0pt;
	font-family:Arial;
	color:black;
	font-style:italic;}
a:link, span.MsoHyperlink
	{color:blue;
	text-decoration:underline;}
a:visited, span.MsoHyperlinkFollowed
	{color:purple;
	text-decoration:underline;}
p
	{margin-right:0cm;
	margin-left:0cm;
	font-size:12.0pt;
	font-family:"Times New Roman";
	color:black;}
p.msonormalc12, li.msonormalc12, div.msonormalc12
	{margin-right:0cm;
	margin-left:0cm;
	font-size:12.0pt;
	font-family:"Times New Roman";
	color:black;}
@page Section1
	{size:595.3pt 841.9pt;
	margin:72.0pt 90.0pt 72.0pt 90.0pt;}
div.Section1
	{page:Section1;}
 /* List Definitions */
 ol
	{margin-bottom:0cm;}
ul
	{margin-bottom:0cm;}
-->
</style>

</head>

<body lang=EN-GB link=blue vlink=purple>

<div class=Section1>

<h1><span style='font-size:14.0pt;font-family:Arial;color:navy;font-weight:
normal'>ezSQL Overview </span><span style='font-size:7.5pt;font-family:Arial;
color:navy;font-weight:normal'><a
href="http://php.justinvincent.com/download.php?ez_sql">download ez_sql.zip</a><br>
To email the creator: justin_at_jvmultimedia_dot_com</span></h1>

<p class=MsoNormal style='margin-left:18.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></b></p>

<p class=MsoBodyText style='margin-left:36.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Symbol;color:#003366'>·</span><span
style='font-size:7.0pt;font-family:"Times New Roman";color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;color:#003366'>ezSQL is a widget that
makes it very fast and easy for you to use database(s) within your PHP scripts
( mySQL / Oracle8/9 / InterBase/FireBird / PostgreSQL / MS-SQL / SQLite /
SQLite c++).</span></p>

<p class=MsoNormal style='text-indent:3.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoBodyText2 style='margin-left:36.0pt;text-indent:-18.0pt'><span
style='font-family:Symbol;color:#003366'>·</span><span style='font-size:7.0pt;
font-family:"Times New Roman";color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='color:#003366'>It is one php file that you include at the
top of your script. Then, instead of using standard php database functions
listed in the php manual, you use a much smaller (and easier) set of
ezSQL&nbsp; functions.</span></p>

<p class=MsoBodyText2><span style='color:#003366'>&nbsp;</span></p>

<p class=MsoBodyText2 style='margin-left:36.0pt;text-indent:-18.0pt'><span
style='font-family:Symbol;color:#003366'>·</span><span style='font-size:7.0pt;
font-family:"Times New Roman";color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='color:#003366'>It automatically caches query results and
allows you to use easy to understand functions to manipulate and extract them
without causing extra server overhead</span></p>

<p class=MsoBodyText2><span style='color:#003366'>&nbsp;</span></p>

<p class=MsoBodyText2 style='margin-left:36.0pt;text-indent:-18.0pt'><span
style='font-family:Symbol;color:#003366'>·</span><span style='font-size:7.0pt;
font-family:"Times New Roman";color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='color:#003366'>It has excellent debug functions making it
lightning-fast to see what’s going on in your SQL code</span></p>

<p class=MsoBodyText2><span style='color:#003366'>&nbsp;</span></p>

<p class=MsoBodyText2 style='margin-left:36.0pt;text-indent:-18.0pt'><span
style='font-family:Symbol;color:#003366'>·</span><span style='font-size:7.0pt;
font-family:"Times New Roman";color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='color:#003366'>Most ezSQL functions can return results as
Objects, Associative Arrays, or Numerical Arrays</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Symbol;color:#003366'>·</span><span
style='font-size:7.0pt;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:#003366'>It can
dramatically decrease development time and in most cases will streamline your
code and make things run faster as well as making it very easy to debug and
optimise your database queries.</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Symbol;color:#003366'>·</span><span
style='font-size:7.0pt;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:#003366'>It is a
small class and will not add very much overhead to your website.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:gray'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><i><span style='font-size:
11.0pt;font-family:Arial;color:#003366'>Note:</span></i></b><i><span
style='font-size:11.0pt;font-family:Arial;color:#003366'> It is assumed that
you are familiar with PHP, basic Database concepts and basic SQL constructs.
Even if you are a complete beginner ezSQL can help you once you have read and
understood <u><a href="http://www.jvmultimedia.com/portal/node/14">this
tutorial</a></u>.</span></i></p>

<p class=MsoNormal style='margin-left:36.0pt'><i><span style='font-size:10.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></i></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:#003366'>&nbsp;</span></i></p>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:#003366'>&nbsp;</span></i></p>

</div>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:gray'>&nbsp;</span></i></p>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:gray'>&nbsp;</span></i></p>

<h1><span style='font-size:14.0pt;font-family:Arial;color:navy;font-weight:
normal'>Quick Examples..</span></h1>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>Note:
In all these examples no other code is required other than including ez_sql.php</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
----------------------------------------------------</span></p>

<h1 style='margin-left:36.0pt'><i><span style='font-size:11.0pt;font-family:
Arial;color:navy'>Example 1</span></i></h1>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Select multiple records from the database
and print them out..</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$users = $db-&gt;get_results(&quot;SELECT
name, email FROM users&quot;);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>foreach ( $users as $user )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>{</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Access
data using object syntax</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user-&gt;name;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user-&gt;email;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>}</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<h1 style='margin-left:36.0pt'><i><span style='font-size:11.0pt;font-family:
Arial;color:navy'>Example 2</span></i></h1>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Get one row from the database and print it
out..</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$user = $db-&gt;get_row(&quot;SELECT
name,email FROM users WHERE id = 2&quot;);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>echo $user-&gt;name;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>echo $user-&gt;email;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<h6>Example 3</h6>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Get one variable from the database and print
it out..</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$var = $db-&gt;get_var(&quot;SELECT count(*)
FROM users&quot;);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>echo $var;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<h6>Example 4</h6>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Insert into the database</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$db-&gt;query(&quot;INSERT INTO users (id,
name, email) VALUES (NULL,'justin','<EMAIL>')&quot;);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<h6>Example 5</h6>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Update the database</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$db-&gt;query(&quot;UPDATE users SET name =
'Justin' WHERE id = 2)&quot;);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<h6>Example 6</h6>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Display last query and all associated
results</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$db-&gt;debug();</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<h6>Example 7</h6>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Display the structure and contents of any
result(s) .. or any variable</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$results = $db-&gt;get_results(&quot;SELECT
name, email FROM users&quot;);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$db-&gt;vardump($results);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<h6>Example 8</h6>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Get 'one column' (based on column index) and
print it out..</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$names = $db-&gt;get_col(&quot;SELECT
name,email FROM users&quot;,0)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>foreach ( $names as $name )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>{</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $name;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>}</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<h6>Example 9</h6>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Same as above ‘but quicker’</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>foreach ( $db-&gt;get_col(&quot;SELECT
name,email FROM users&quot;,0) as $name )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>{</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $name;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>}</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<h6>Example 10</h6>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>----------------------------------------------------</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Map out the full schema of any given
database and print it out..</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$db-&gt;select(&quot;my_database&quot;);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>foreach ( $db-&gt;get_col(&quot;SHOW
TABLES&quot;,0) as $table_name )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>{</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;debug();</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;get_results(&quot;DESC $table_name&quot;);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>}</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>$db-&gt;debug();</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:#003366'>&nbsp;</span></i></p>

</div>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:gray'>&nbsp;</span></i></p>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:gray'>&nbsp;</span></i></p>

<h1><span style='font-size:14.0pt;font-family:Arial;color:navy;font-weight:
normal'>Introduction</span></h1>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></b></p>

<p class=MsoBodyText style='margin-left:36.0pt'><span style='font-size:11.0pt;
color:#003366'>When working with databases most of the time you will want to do
one of four types of basic operations.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:90.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>1.</span><span
style='font-size:7.0pt;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>Perform a query such
as Insert or Update (without results)</span></p>

<p class=MsoNormal style='margin-left:90.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>2.</span><span
style='font-size:7.0pt;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>Get a single variable
from the database</span></p>

<p class=MsoNormal style='margin-left:90.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>3.</span><span
style='font-size:7.0pt;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>Get a single row from
the database</span></p>

<p class=MsoNormal style='margin-left:90.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>4.</span><span
style='font-size:7.0pt;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>Get a list of results
from the database</span></p>

<p class=MsoBodyText style='margin-left:36.0pt'><span style='font-size:11.0pt;
color:#003366'>&nbsp;</span></p>

<p class=MsoBodyText style='margin-left:36.0pt'><span style='font-size:11.0pt;
color:#003366'>ezSQL wraps up these four basic actions into four very easy to
use functions. </span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>bool &nbsp;&nbsp;&nbsp; <b>$db-&gt;query</b>(query)</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>var &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <b>$db-&gt;get_var</b>(query)</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>mixed <b>$db-&gt;get_row</b>(query)</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>mixed <b>$db-&gt;get_results</b>(query)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></b></p>

<p class=MsoBodyText style='margin-left:36.0pt'><span style='font-size:11.0pt;
color:#003366'>With ezSQL these four functions are all you will need 99.9% of
the time. Of course there are also some other useful functions but we will get
into those later.</span></p>

<p class=MsoBodyText style='margin-left:36.0pt'><span style='font-size:11.0pt;
color:#003366'>&nbsp;</span></p>

<p class=MsoBodyText style='margin-left:36.0pt'><b><i><span style='font-size:
11.0pt;color:#003366'>Important Note:</span></i></b><i><span style='font-size:
11.0pt;color:#003366'> If you use ezSQL inside a function you write, you will
need to put <b>global $db;</b> at the top.</span></i></p>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></b></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:#003366'>&nbsp;</span></i></p>

</div>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:gray'>&nbsp;</span></i></p>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></b></p>

<h1><span style='font-size:14.0pt;font-family:Arial;color:navy;font-weight:
normal'>Installation</span></h1>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></b></p>

<p class=MsoBodyText style='margin-left:36.0pt'><span style='font-size:11.0pt;
color:#003366'>To install ezSQL download, unzip and install the contents of <b><a
href="http://php.justinvincent.com/download.php?ez_sql">ez_sql.zip</a></b> into
the same directory within your web server.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>Put the following at the top of your script:</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'>&nbsp;</p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>            // Include ezSQL core</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>            include_once &quot;ez_sql_core.php&quot;;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>            // Include ezSQL database specific
component (in this case mySQL)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>            include_once
&quot;ez_sql_mysql.php&quot;;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>            // Initialise database object and
establish a connection</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>            // at the same time - db_user /
db_password / db_name / db_host</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>            $db = new
ezSQL_mysql('db_user','db_password','db_name','db_host');</span></p>

<p class=MsoNormal style='margin-left:36.0pt'>&nbsp;</p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>Note: On most systems <b>localhost</b> will be
fine for the dbhost value. If you are unsure about any of the above settings
you should contact your provider or look through your providers documentation.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>If you are running on a local machine and have
just installed mySQL for the first time, you can probably leave the user name
and password empty ( i.e.&nbsp; = “”) until you set up a mySQL user account.</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:#003366'>&nbsp;</span></i></p>

</div>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:gray'>&nbsp;</span></i></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><span style='font-size:14.0pt;font-family:Arial;color:navy;font-weight:
normal'>Running the ezSQL demo</span></h1>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>Once you have installed ezSQL as described
above you can see it in action by running ez_demo.php via your web browser. To
do this simply go to..</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>http://yourserver.com/install_path/mysql/demo.php</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>If you are running your web server on your
local machine this will be..</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>http://127.0.0.1/install_path/mysql/demo.php</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>What the demo does… is use ezSQL functions to
map out the table structure of your database (i.e the database you specified at
the top of ez_sql.php). You will be surprised how little code is required to do
this when using ezSQL. I have included it here so you can get a quick feel for
the compactness and speed of ezSQL.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&lt;?php</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>             // Include ezSQL core</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>             include_once &quot;ez_sql_core.php&quot;;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>             // Include ezSQL database
specific component</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>             include_once
&quot;ez_sql_mysql.php&quot;;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>             // Initialise database object and
establish a connection</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>             // at the same time - db_user /
db_password / db_name / db_host</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>             $db = new
ezSQL_mysql('db_user','db_password','db_name','db_host');</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$my_tables = $db-&gt;get_results(&quot;SHOW TABLES&quot;,ARRAY_N);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;debug();</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach ( $my_tables as $table )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;get_results(&quot;DESC $table[0]&quot;);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;debug();</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>?&gt;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:#003366'>&nbsp;</span></i></p>

</div>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:gray'>&nbsp;</span></i></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><span style='font-size:14.0pt;font-family:Arial;color:navy;font-weight:
normal'>The ezSQL demo explained</span></h1>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&lt;?php</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>This is the standard way to start php
executing within your web page.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><b><span
style='font-size:10.0pt;font-family:Arial;color:red'>include_once “ez_sql.php”;</span></b></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>This is how you include ezSQL in your script.
Normally you include it at the top of your script and from that point forward
you have access to any ezSQL function.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$my_tables = $db-&gt;get_results(“SHOW TABLES”,ARRAY_N);</span></b></p>

<p class=MsoBodyTextIndent2 style='margin-left:72.0pt'>get_results() is how you
get ‘a list’ of things from the database using ezSQL. The list is returned as
an array. In this case the std mySQL command&nbsp; of ‘SHOW TABLES’ is called
and the resulting list is stored in a&nbsp; newly created array $my_tables. </p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>When using $db-&gt;get_results(), if there are
any results, they are always returned as multi-dimensional array. The first
dimension is a numbered index. Each of the numbered indexes is either an
object, associative array or numerical array containing all the values for ‘one
row’.</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><b><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>For example using the switch ARRAY_A would
produce an array that looked something like this.</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><b><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><b><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></b><span style='font-size:11.0pt;font-family:Arial;color:#003366'>$users
= $db-&gt;get_results(“SELECT id,name FROM users”,ARRAY_A);</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><b><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users[0] = array
(“id” =&gt; “1”, “name” =&gt; “Amy”);</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users[1] = array
(“id” =&gt; “2”, “name” =&gt; “Tyson”);</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>If you wanted a numerical array use the switch
ARRAY_N.</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><b><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></b><span style='font-size:11.0pt;font-family:Arial;color:#003366'>$users
= $db-&gt;get_results(“SELECT id,name FROM users”,ARRAY_N);</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users[0] = array (0
=&gt; “1”, 1 =&gt; “Amy”);</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users[1] = array (0
=&gt; “2”, 1 =&gt; “Tyson”);</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>If you wanted an object (which is the default
option) you don’t need a switch..</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users =
$db-&gt;get_results(“SELECT id,name FROM users”);</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users[0]-&gt;id =
“1”;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users[0]-&gt;name =
“Amy”;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users[1]-&gt;id =
“2”;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users[1]-&gt;name =
“Tyson”;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>Results returned as an object make it very
easy to work with database results using the numerous array functions that php
offers. For example, to loop through results returned as an object all one
needs to do is..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$users =
$db-&gt;get_results(“SELECT id,name FROM users”);</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach( $users as $user )</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user-&gt;id;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user-&gt;name;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
If you are 100% sure that there will be results you can skip a step and do
this..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach( $db-&gt;get_results(“SELECT id,name FROM users”) as $user )</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user-&gt;id;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user-&gt;name;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
If you don’t know whether there will be results or not you can do this..</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>If ( $users=
$db-&gt;get_results(“SELECT id,name FROM users”) )</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach( $users as $user )</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user-&gt;id;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user-&gt;name;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>}</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>else</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>{</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “No results”;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>}</span></p>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial;
color:red'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><b><span
style='font-size:10.0pt;font-family:Arial;color:red'>$db-&gt;debug();</span></b></p>

<p class=MsoBodyTextIndent2 style='margin-left:72.0pt'>This function prints the
most recently called sql query along with a well formatted table containing any
results that the query generated (if any) and the column info.</p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><b><span
style='font-size:10.0pt;font-family:Arial;color:red'>foreach ( $my_tables as
$table)</span></b></p>

<p class=MsoBodyTextIndent style='margin-left:72.0pt'><span style='font-size:
11.0pt;color:#003366'>This is the standard way to easily loop through an array
in php. In this case the array $my_tables was created with the ezSQL command
$db-&gt;get_results(“SHOW TABLES”,ARRAY_N). Because of the ARRAY_N switch the
results are returned as a numerical array.</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>The resulting array
will look something like..</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$my_tables[0] = array
(0 =&gt; “users”);</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$my_tables[1] = array
(0 =&gt; “products”);</span></p>

<p class=MsoNormal style='margin-left:72.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>$my_tables[2] = array
(0 =&gt; “guestbook”);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></b></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>The foreach is looping through each primary
element of $my_tables[n] which are in turn numerical arrays, with the format
like so..</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
array(0 =&gt; “value”, 1 =&gt; “value”, etc.);</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>Thus, during the foreach loop of $my_tables we
have access to the value of the first column like so:</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach ($my_tables as $table)</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $table[0];</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>If we did the same
thing using an associative array it might look like this..</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$users = $db-&gt;get_results(“SELECT id,name FROM users”,ARRAY_A);</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach ( $users as $user )</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user[‘id’];</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user[‘name’];</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:72.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>But if there were no results foreach might
generate a warning. So a safer way to do the above is..</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
if ( $users = $db-&gt;get_results(“SELECT id,name FROM users”,ARRAY_A))</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach ( $users as $user )</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user[‘id’];</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user[‘name’];</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
else</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “No Users”:</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:#003366'>This works because if
no results are returned then get_results() returns false.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;get_results(“DESC $table[0]”);</span></b></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>This database query is nested within the
foreach loop. Note that we are using the results of the previous call to make a
new call. Traditionally you would have to be concerned about using different
db_resource identifiers in a case like this but ezSQL takes care of that for you,
making it very easy to nest database queries.</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>You may be wondering why I have used a
numerical array output and not object or associative array. The reason is
because in this case I do not know what the name of the first column will be.
So I can make sure that I can always get its value by using numerical array
output and targeting the first column by element [0].</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></i></p>

<p class=MsoNormal style='margin-left:108.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>FYI: The SQL command SHOW TABLES always names
the first column a different value depending on the database being used. If the
database was named <b>users</b> the column would be called <b>Tables_in_users</b>
if the database was called <b>customers</b> the column would be called <b>Tables_in_customers</b>
and so on.</span></i></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;debug();</span></b></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>This function will always print the last query
and its results (if any) to the browser. In this case it will be for the above
query.. </span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;get_results(“DESC $table[0]”);</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>You may have noticed that the above
get_results function is not assigning a value. (i.e. $var = val). This is
because even if you do not assign the output value of any ezSQL function the
query results are always stored and made ready for any ezSQL function to use.
In this case $db-&gt;debug() is displaying the stored results. Then, by calling
any ezSQL function using a <b>null</b> query you will be accessing the stored
results from the last query. Here is a more detailed
example.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p class=MsoNormal style='margin-left:108.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></i></p>

<p class=MsoNormal style='margin-left:108.0pt'><b><i><span style='font-size:
11.0pt;font-family:Arial;color:#003366'>Users Table..</span></i></b></p>

<p class=MsoNormal style='margin-left:108.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>amy, <EMAIL></span></i></p>

<p class=MsoNormal style='margin-left:108.0pt'><i><span lang=FR
style='font-size:11.0pt;font-family:Arial;color:#003366'>tyson, <EMAIL></span></i></p>

<p class=MsoNormal style='margin-left:108.0pt'><span lang=FR style='font-size:
11.0pt;font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span lang=FR style='font-size:
11.0pt;font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:#003366'>// Any
ezSQL function will store query results..</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$users = $db-&gt;get_results(“SELECT name,email FROM users”);</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
// This gets a variable from the above results (offset by $x = 1, $y = 1).</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $db-&gt;get_var(null,1,1);</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
// Note: Because a <b>null</b> query is passed to get_var it uses results from
the previous query.</span></p>

<p class=MsoNormal style='margin-left:108.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></p>

<p class=MsoHeading7>Output:
<EMAIL>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </p>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial;
color:red'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
This closes the foreach loop</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>&nbsp;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial;color:red'>?&gt;</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#003366'>This stops php executing code</span></p>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial;
color:red'>&nbsp;</span></b></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><i><span style='font-size:10.0pt;font-family:Arial;
color:#003366'>&nbsp;</span></i></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial;color:gray'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial;
color:red'>&nbsp;</span></b></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>ezSQL functions</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;get_results</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- get multiple row result set from the database (or
previously cached results)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;get_row</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- get one row from the database (or previously cached
results)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;get_col</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- get one column from query (or previously cached results)
based on column offset </span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;get_var</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- get one variable, from one row, from the database (or
previously cached results)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;query</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- send a query to the database (and if any results, cache
them)</span></p>

<p class=MsoBodyTextIndent><b>$db-&gt;debug</b> -- print last sql query and
returned results (if any)</p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;vardump</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- print the contents and structure of any variable</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;select</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- select a new database to work with</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;get_col_info</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- get information about one or all columns such as column
name or type</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;hide_errors</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- turn ezSQL error output to browser off</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;show_errors</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- turn ezSQL error output to browser on</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;escape</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- Format a string correctly to stop accidental mal formed
queries under all PHP conditions</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db = new db</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- Initiate new db object.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>ezSQL variables</span></b></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;num_rows</span></b><span style='font-size:10.0pt;
font-family:Arial'> – Number of rows that were returned (by the database) for
the last query (if any)</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;insert_id</span></b><span style='font-size:10.0pt;
font-family:Arial'> -- ID generated from the AUTO_INCRIMENT of the previous
INSERT operation (if any)</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;rows_affected </span></b><span style='font-size:
10.0pt;font-family:Arial'>-- Number of rows affected (in the database) by the
last INSERT, UPDATE or DELETE (if any)</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;num_queries </span></b><span style='font-size:10.0pt;
font-family:Arial'>-- Keeps track of exactly how many 'real' (not cached)
queries were executed during the lifetime of the current script</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;debug_all </span></b><span style='font-size:10.0pt;
font-family:Arial'>– If set to true (i.e. $db-&gt;debug_all = true;) Then it
will print out ALL queries and ALL results of your script.</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;cache_dir – </span></b><span style='font-size:10.0pt;
font-family:Arial'>Path to mySQL caching dir.</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;cache_queries – </span></b><span style='font-size:
10.0pt;font-family:Arial'>Boolean flag (see mysql/disk_cache_example.php)</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;cache_inserts – </span></b><span style='font-size:
10.0pt;font-family:Arial'>Boolean flag (see mysql/disk_cache_example.php)</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;use_disk_cache – </span></b><span style='font-size:
10.0pt;font-family:Arial'>Boolean flag (see mysql/disk_cache_example.php)</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-size:10.0pt;
font-family:Arial'>$db-&gt;cache_timeout – </span></b><span style='font-size:
10.0pt;font-family:Arial'>Number in hours (see mysql/disk_cache_example.php)</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial;
color:navy'>&nbsp;</span></b></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db = new db</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>$db = new db -- Initiate new db object. Connect to a
database server. Select a database.</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h4>Description</h4>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:maroon'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db = new db</span></b><span style='font-size:11.0pt;
font-family:Arial;color:black'>(string username, string password, string
database name, string database host)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>Does three things. (1) Initiates a new db object. (2)
Connects to a database server. (3) Selects a database. You can also re-submit
this command if you would like to initiate a second db object. This is
interesting because you can run two concurrent database connections at the same
time. You can even connect to two different servers at the same time if you
want to. </span></p>

<p class=MsoNormal style='margin-left:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></i></p>

<p class=MsoNormal style='margin-left:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial'>Note: For the sake of efficiency it is recommended that you
only run one instance of the <b>db</b> object and use <b>$db-&gt;select</b> to
switch between different databases on the same server connection.</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></i></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example</span></i></h1>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal><span style='font-size:14.0pt;color:fuchsia'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>//
Initiate new database object..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#333399'>$db2 = new db(</span><span style='font-size:
11.0pt;font-family:Arial;color:#333333'>”user_name”</span><span
style='font-size:11.0pt;font-family:Arial;color:#333399'>, </span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>”user_password”,
”database_name”, “database_host”</span><span style='font-size:11.0pt;
font-family:Arial;color:#333399'>); </span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:black'>&nbsp;</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><span style='font-size:11.0pt;font-family:Arial;color:purple'>//
Perform some kind of query..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$other_db_tables = $db2-&gt;get_results(</span><span style='font-size:11.0pt;
font-family:Arial;color:#333333'>“SHOW TABLES”</span><span style='font-size:
11.0pt;font-family:Arial;color:navy'>);</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:black'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// You can
still query the database you were already connected to..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$existing_connection_tables = $db-&gt;get_results(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SHOW TABLES”</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'>);</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:black'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:black'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Print
the results from both of these queries..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;debug();</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db2-&gt;debug();</span></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:black'>&nbsp;</span></i></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial;
color:navy'>&nbsp;</span></b></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;select</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;select -- select a new database to work with</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>bool <b>$db-&gt;select</b>(string database name)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;select</span></b><span style='font-size:11.0pt;
font-family:Arial'>() selects a new database to work with using the current
database connection as created with <b><span style='color:black'>$db = new db</span></b>.</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h5>Example</h5>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Get a
users name from the user’s database (as initiated with $db = new db)..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$user_name = $db-&gt;get_var(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT name FROM
users WHERE id = 22”</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>) ;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:fuchsia'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Select
the database stats..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#333399'>$db-&gt;select(“stats”); </span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Get a
users name from the user’s database..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$total_hours = $db-&gt;get_var(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT
sum(time_logged_in) FROM user_stats WHERE user = ‘$user_name’”</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'>) ;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:fuchsia'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Re-select
the ‘users’ database to continue working as normal..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:#333399'>$db-&gt;select(“users”); </span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial;
color:navy'>&nbsp;</span></b></p>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial;
color:navy'>&nbsp;</span></b></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;query</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;query -- send a query to the database (and if any
results, cache them)</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>bool <b>$db-&gt;query</b>(string query)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;query</span></b><span style='font-size:11.0pt;
font-family:Arial'>() sends a query to the currently selected database. It
should be noted that you can send any type of query to the database using this
command. If there are any results generated they will be stored and can be
accessed by any ezSQL function as long as you use a null query. If there are
results returned the function will return <b>true</b> if no results the return
will be <b>false</b></span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 1</span></i></h1>

<p class=MsoNormal><span style='color:purple'>&nbsp;</span></p>

<p class=MsoNormal><span style='color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Insert
a new user into the database..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;query(</span><span style='font-size:11.0pt;
font-family:Arial;color:#333333'>“INSERT INTO users (id,name) VALUES (1,’Amy’)”</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'>) ;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 2</span></i></h1>

<p class=MsoNormal><span style='color:purple'>&nbsp;</span></p>

<p class=MsoNormal><span style='color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Update
user into the database..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;query(</span><span style='font-size:11.0pt;
font-family:Arial;color:#333333'>“UPDATE users SET name = ‘Tyson’ WHERE id = 1”</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'>) ;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></i></h1>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 3</span></i></h1>

<p class=MsoNormal><span style='color:purple'>&nbsp;</span></p>

<p class=MsoNormal><span style='color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Query
to get full user list..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;query(</span><span style='font-size:11.0pt;
font-family:Arial;color:#333333'>“SELECT name,email FROM users”</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'>) ;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Get the
second row from the cached results by using a <b>null</b> query..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$user_details = $db-&gt;get_row(null, OBJECT,1);</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Display
the contents and structure of the variable $user_details..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;vardump($user_details);</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><b>&nbsp;</b></p>

<p class=MsoNormal><b>&nbsp;</b></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;get_var</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_var -- get one variable, from one row, from the
database (or previously cached results)</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>var <b>$db-&gt;get_var</b>(string query / null [,int column
offset[, int row offset])</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_var</span></b><span style='font-size:11.0pt;
font-family:Arial'>() gets one single variable from the database or previously
cached results. This function is very useful for evaluating query results
within logic statements such as <b>if</b> or <b>switch</b>. If the query
generates more than one row the first row will always be used by default. If
the query generates more than one column the leftmost column will always be
used by default. Even so, the full results set will be available within the
array $db-&gt;last_results should you wish to use them.</span></p>

<h1><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></h1>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 1</span></i></h1>

<p class=MsoNormal><span style='color:purple'>&nbsp;</span></p>

<p class=MsoNormal><span style='color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Get
total number of users from the database..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$num_users = $db-&gt;get_var(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT count(*) FROM
users”</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>) ;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 2</span></i></h1>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal><span style='color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Get a
users email from the second row of results (note: col 1, row 1 [starts at 0])..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$user_email = $db-&gt;get_var(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT name, email
FROM users”,1,1</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>) ;</span></p>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal><span style='color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Get the
full second row from the cached results (row = 1 [starts at 0])..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$user = $db-&gt;get_row(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>null,OBJECT,1</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'>);</span></p>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span style='font-size:11.0pt;font-family:Arial;color:purple'>// Both are the
same value..</span></p>

<p class=MsoNormal>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span style='font-size:11.0pt;font-family:Arial;color:navy'>echo $user_email;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user-&gt;email;</span></p>

<p class=MsoNormal>&nbsp;</p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 3</span></i></h1>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Find
out how many users there are called Amy..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>if ( $n = $db-&gt;get_var(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT count(*) FROM
users WHERE name = ‘Amy’”</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>) )</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// If
there are users then the if clause will evaluate to true. This is useful
because </span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:purple'>// we can extract a
value from the DB and test it at the same time.</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo </span><span style='font-size:11.0pt;font-family:Arial;color:#333333'>“There
are $n users called Amy!”</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>else</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:purple'>// If there are no
users then the if will evaluate to false..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo </span><span style='font-size:11.0pt;font-family:Arial;color:#333333'>“There
are no users called Amy.”</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 4</span></i></h1>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Match a
password from a submitted from a form with a password stored in the DB</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>if ( $pwd_from_form == $db-&gt;get_var(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT pwd FROM users
WHERE name = ‘$name_from_form’”</span><span style='font-size:11.0pt;font-family:
Arial;color:navy'>) )</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Once
again we have extracted and evaluated a result at the same time..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo </span><span style='font-size:11.0pt;font-family:Arial;color:#333333'>“Congratulations
you have logged in.”</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>else</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// If has
evaluated to false..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo </span><span style='font-size:11.0pt;font-family:Arial;color:#333333'>“Bad
password or Bad user ID”</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><b><span style='font-size:10.0pt;font-family:Arial;
color:navy'>&nbsp;</span></b></p>

<b><span style='font-size:10.0pt;font-family:Arial;color:navy'><br clear=all
style='page-break-before:always'>
</span></b>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;get_row</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_row -- get one row from the database (or previously
cached results)</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>object <b>$db-&gt;get_ row</b>(string query / null [, OBJECT
/ ARRAY_A / ARRAY_N [, int row offset]])</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_row</span></b><span style='font-size:11.0pt;
font-family:Arial'>() gets a single row from the database or cached results. If
the query returns more than one row and no row offset is supplied the first row
within the results set will be returned by default. Even so, the full results
will be cached should you wish to use them with another ezSQL query.</span></p>

<h1><i><span style='font-family:Arial'>&nbsp;</span></i></h1>

<h5>Example 1</h5>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:purple'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
// Get a users name and email from the database and extract it into an object
called user..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$user = $db-&gt;get_row(“SELECT name,email FROM
users WHERE id = 22”) ;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Output
the values..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “$user-&gt;name has the email of $user-&gt;email”;</span></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span style='font-size:11.0pt;font-family:Arial;color:purple'>Output:</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
Amy has the <NAME_EMAIL></span></i></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<h3><span style='font-size:11.0pt;color:navy'>Example 2</span></h3>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></i></b></p>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i></b><span
style='font-size:11.0pt;font-family:Arial;color:purple'>// Get users name and
date joined as associative array</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// (Note: we must specify the row offset index
in order to use the third argument)</span></p>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i></b><span
style='font-size:11.0pt;font-family:Arial;color:navy'>$user =
$db-&gt;get_row(“SELECT name, UNIX_TIMESTAMP(my_date_joined) as date_joined
FROM users WHERE id = 22”,ARRAY_A) ;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Note
how the unix_timestamp command is used with <b>as</b> this will ensure that the
resulting data will be easily </span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// accessible via the created object or
associative array. In this case $user[‘date_joined’] (object would be
$user-&gt;date_joined)</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $user[‘name’] . “ joined us on ” . date(“m/d/y”,$user[‘date_joined’]);</span></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span style='font-size:11.0pt;font-family:Arial;color:purple'>Output:</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
Amy joined us on 05/02/01</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;</span></i></p>

<h3><span style='font-size:11.0pt;color:navy'>Example 3</span></h3>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></i></b></p>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i></b><span
style='font-size:11.0pt;font-family:Arial;color:purple'>// Get second row of
cached results. </span></p>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i></b><span
style='font-size:11.0pt;font-family:Arial;color:navy'>$user =
$db-&gt;get_row(null,OBJECT,1) ;</span></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i><span
style='font-size:11.0pt;font-family:Arial;color:purple'>// Note: Row offset
starts at 0</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “$user-&gt;name joined us on ” . date(“m/d/y”,$user-&gt;date_joined);</span></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span style='font-size:11.0pt;font-family:Arial;color:purple'>Output:</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
Tyson joined us on 05/02/02</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;</span></i></p>

<h3><span style='font-size:11.0pt;color:navy'>Example 4</span></h3>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></i></b></p>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i></b><span
style='font-size:11.0pt;font-family:Arial;color:purple'>// Get one row as a
numerical array..</span></p>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i></b><span
style='font-size:11.0pt;font-family:Arial;color:navy'>$user =
$db-&gt;get_row(“SELECT name,email,address FROM users WHERE id = 1”,ARRAY_N);</span></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;</span></i></p>

<p class=MsoNormal><b><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i></b><span
style='font-size:11.0pt;font-family:Arial;color:purple'>// Output the results
as a table..</span></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i><span
style='font-size:11.0pt;font-family:Arial;color:navy'>echo “&lt;table&gt;”;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
for ( $i=1; $i &lt;= count($user); $i++ )</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo
“&lt;tr&gt;&lt;td&gt;$i&lt;/td&gt;&lt;td&gt;$user[$I]&lt;/td&gt;&lt;/tr&gt;”;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “&lt;/table&gt;”;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span style='font-size:11.0pt;font-family:Arial;color:purple'>Output:</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:purple'>&nbsp;</span></i></p>

<p class=MsoNormal style='margin-left:108.0pt;text-indent:-36.0pt'><i><span
style='font-size:11.0pt;font-family:Arial;color:navy'>1</span></i><i><span
style='font-size:7.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>amy</span></i></p>

<p class=MsoNormal style='margin-left:108.0pt;text-indent:-36.0pt'><i><span
style='font-size:11.0pt;font-family:Arial;color:navy'>2</span></i><i><span
style='font-size:7.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span style='font-size:11.0pt;font-family:Arial;color:navy'><EMAIL></span></i></p>

<p class=MsoNormal style='margin-left:108.0pt;text-indent:-36.0pt'><i><span
style='font-size:11.0pt;font-family:Arial;color:navy'>3</span></i><i><span
style='font-size:7.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>123
Foo Road</span></i></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;get_results</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_results – get multiple row result set from the
database (or previously cached results)</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>array <b>$db-&gt;get_results</b>(string query / null [,
OBJECT / ARRAY_A / ARRAY_N ] )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_row</span></b><span style='font-size:11.0pt;
font-family:Arial'>() gets multiple rows of results from the database based on <i>query</i>
and returns them as a multi dimensional array. Each element of the array
contains one row of results and can be specified to be either an object,
associative array or numerical array. If no results are found then the function
returns false enabling you to use the function within logic statements such as <b>if.</b></span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></i></h1>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 1 –
Return results as objects (default)</span></i></h1>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:black'>Returning results as an object is the quickest
way to get and display results. It is also useful that you are able to put
$object-&gt;var syntax directly inside print statements without having to worry
about causing php parsing errors.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:14.0pt;
color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Extract
results into the array $users (and evaluate if there are any results at the
same time)..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>if ( $users = $db-&gt;get_results(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT name, email
FROM users”</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>)
)</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Loop
through the resulting array on the index $users[n]</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach ( $users as $user )</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Access
data using column names as associative array keys</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “$user-&gt;name - $user-&gt;email&lt;br</span><span style='font-family:
Arial;color:navy'>&gt;</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>”;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>else</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// If no
users were found then <b>if</b> evaluates to false..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo </span><span style='font-size:11.0pt;font-family:Arial;color:#333333'>“No
users found.”</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span style='font-size:11.0pt;font-family:Arial;color:purple'>Output:</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
Amy - <EMAIL>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span lang=FR style='font-size:11.0pt;font-family:Arial;
color:navy'>Tyson - <EMAIL></span></i></p>

<h1><i><span lang=FR style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></i></h1>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 2 –
Return results as associative array</span></i></h1>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:black'>Returning results as an associative array is
useful if you would like dynamic access to column names. Here is an example. </span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Extract
results into the array $dogs (and evaluate if there are any results at the same
time)..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>if ( $dogs = $db-&gt;get_results(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT breed, owner,
name FROM dogs”, ARRAY_A</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>) )</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Loop
through the resulting array on the index $dogs[n]</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach ( $dogs as $dog_detail )</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Loop
through the resulting array</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach ( $dogs_detail as $key =&gt; $val )</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Access
and format data using $key and $val pairs..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “&lt;b&gt;” . ucfirst($key) . “&lt;/b&gt;: $val&lt;br&gt;”;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Do a P
between dogs..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “&lt;p&gt;”;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>else</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// If no
users were found then <b>if</b> evaluates to false..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo </span><span style='font-size:11.0pt;font-family:Arial;color:#333333'>“No
dogs found.”</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></i><i><span style='font-size:11.0pt;font-family:Arial;color:purple'>Output:</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<b>Breed:</b> Boxer</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<b>Owner:</b> Amy</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<b>Name:</b> Tyson</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<b>Breed:</b> Labrador</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<b>Owner:</b> Lee</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<b>Name:</b> Henry</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<b>Breed:</b> Dachshund</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<b>Owner:</b> Mary</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><i><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<b>Name:</b> Jasmine</span></i></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 3 –
Return results as numerical array</span></i></h1>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>Returning
results as a numerical array is useful if you are using completely dynamic
queries with varying column </span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:black'>names but still need a
way to get a handle on the results. Here is an example of this concept in use.
Imagine that this </span></p>

<p class=MsoNormal style='margin-left:36.0pt;text-indent:36.0pt'><span
style='font-size:11.0pt;font-family:Arial;color:black'>script was responding to
a form with $type being submitted as either ‘fish’ or ‘dog’.</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Create
an associative array for animal types..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$animal = array ( “fish” =&gt; “num_fins”, “dog” =&gt; “num_legs” );</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Create
a dynamic query on the fly..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
if ( $results = $db-&gt;(“SELECT $animal[$type] FROM $type”,ARRAY_N))</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
foreach ( $results as $result )</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “$result[0]&lt;br&gt;”;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
else</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
{</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “No $animal\s!”;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
}</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </i></span><i><span
style='font-size:11.0pt;font-family:Arial;color:purple'>Output:</span></i></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
4</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
4</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
4</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><i><span style='font-size:11.0pt;font-family:Arial;color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
Note: The dynamic query would be look like one of the following...</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:purple'>&nbsp;</span></i></p>

<p class=MsoNormal style='margin-left:108.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Symbol;color:purple'>·</span><span
style='font-size:7.0pt;color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><i><span style='font-size:11.0pt;font-family:Arial;color:purple'>SELECT
num_fins FROM fish</span></i></p>

<p class=MsoNormal style='margin-left:108.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Symbol;color:purple'>·</span><span
style='font-size:7.0pt;color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><i><span style='font-size:11.0pt;font-family:Arial;color:purple'>SELECT
num_legs FROM dogs</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:purple'>&nbsp;</span></i></p>

<p class=MsoNormal><i><span style='font-size:11.0pt;font-family:Arial;
color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
It would be easy to see which it was by using $db-&gt;debug(); after the
dynamic query call.</span></i></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;debug</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>$db-&gt;debug – print
last sql query and returned results (if any)</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;debug</span></b><span style='font-size:11.0pt;
font-family:Arial'>(void)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><b><span style='font-size:11.0pt'>$db-&gt;debug</span></b><span
style='font-size:11.0pt'>() prints last sql query and its results (if any)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 1</span></i></h1>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:black'>If you need to know what your last query was and
what the returned results are here is how you do it.</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:14.0pt;
color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Extract
results into the array $users..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$users = $db-&gt;get_results(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT name, email
FROM users”</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>);</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// See what just happened!</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;debug();</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;vardump</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;vardump – print the contents and structure of any
variable</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span lang=FR style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><span lang=FR style='font-size:
11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span lang=FR
style='font-size:11.0pt;font-family:Arial'>$db-&gt;vardump</span></b><span
lang=FR style='font-size:11.0pt;font-family:Arial'>(void)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span lang=FR style='font-size:
11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;vardump</span></b><span style='font-size:11.0pt;
font-family:Arial'>() prints the contents and structure of any variable. <span
style='color:black'>It does not matter what the structure is be it an object,
associative array or numerical array.</span></span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 1</span></i></h1>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:black'>If you need to know what value and structure any
of your results variables are here is how you do it. </span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:14.0pt;
color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Extract
results into the array $users..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$users = $db-&gt;get_results(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT name, email
FROM users”</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>);</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// View the contents and structure of $users</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;vardump($users);</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;get_col</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>$db-&gt;get_col – get
one column from query (or previously cached results) based on column offset </span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_col</span></b><span style='font-size:11.0pt;
font-family:Arial'>( string query / null [, int column offset] )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_col</span></b><span style='font-size:11.0pt;
font-family:Arial'>() extracts one column as one dimensional array based on a
column offset. If no offset is supplied the offset will defualt to column 0.
I.E the first column. If a null query is supplied the previous query results
are used.</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 1</span></i></h1>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:14.0pt;
color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Extract
list of products and print them out at the same time..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>foreach ( $db-&gt;get_col(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT product FROM
product_list”) as $product)</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $product;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 2 –
Working with cached results</span></i></h1>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:14.0pt;
color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Extract
results into the array $users..</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$users = $db-&gt;get_results(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT * FROM users”</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'>);</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:14.0pt;
color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Work out how many columns have been
selected..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$last_col_num = $db-&gt;num_cols - 1;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Print the last column of the query using
cached results..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>foreach ( $db-&gt;get_col(null, $last_col_num) as
$last_col )</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo $last_col;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:14.0pt;
color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;get_col_info</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_col_info - get information about one or all
columns such as column name or type</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_col_info</span></b><span style='font-size:11.0pt;
font-family:Arial'>(string info-type[, int column offset])</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;get_col_info</span></b><span style='font-size:11.0pt;
font-family:Arial'>()<span style='color:black'>returns meta information about
one or all columns such as column name or type. If no information type is
supplied then the default information type of <b>name</b> is used. If no column
offset is supplied then a one dimensional array is returned with the
information type for ‘all columns’. For access to the full meta information for
all columns you can use the cached variable $db-&gt;col_info</span></span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoHeading8><span lang=FR>Available Info-Types</span></p>

<p class=MsoHeading8><span lang=FR>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-family:Arial;
color:navy'>mySQL</span></b></p>

<p class=MsoNormal><span lang=FR>&nbsp;</span></p>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>name</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
column name </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>table</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
name of the table the column belongs to </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>max_length</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
maximum length of the column </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>not_null</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> - 1
if the column cannot be NULL </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>primary_key</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> - 1
if the column is a primary key </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>unique_key</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> - 1
if the column is a unique key </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>multiple_key</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> - 1
if the column is a non-unique key </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>numeric </span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'>- 1
if the column is numeric </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>blob</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> - 1
if the column is a BLOB </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>type</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
the type of the column </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>unsigned</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> - 1
if the column is unsigned </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>zerofill</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> - 1
if the column is zero-filled</span></h1>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-family:Arial;
color:navy'>ibase</span></b></p>

<p class=MsoNormal><span lang=FR>&nbsp;</span></p>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>name</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
column name&nbsp; </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>type</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
the type of the column</span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>length</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
size of column</span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>alias</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
undocumented</span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>relation </span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'>-
undocumented</span></h1>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-family:Arial;
color:navy'>MS-SQL / Oracle / Postgress</span></b></p>

<p class=MsoNormal><span lang=FR>&nbsp;</span></p>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>name</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
column name&nbsp; </span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>type</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
the type of the column</span></h1>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>length</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
size of column</span></h1>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal style='text-indent:36.0pt'><b><span style='font-family:Arial;
color:navy'>SQLite </span></b></p>

<p class=MsoNormal><span lang=FR>&nbsp;</span></p>

<h1 style='margin-left:72.0pt;text-indent:-18.0pt'><span style='font-size:11.0pt;
font-family:Symbol;color:black;font-weight:normal'>·</span><span
style='font-size:7.0pt;color:black;font-weight:normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:black'>name</span><span
style='font-size:11.0pt;font-family:Arial;color:black;font-weight:normal'> -
column name&nbsp; </span></h1>

<p class=MsoNormal><span style='font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 1</span></i></h1>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Extract
results into the array $users..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$users = $db-&gt;get_results(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT id, name,
email FROM users”</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>);</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Output the name for each column type</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>foreach ( $db-&gt;get_col_info(“name”)&nbsp; as
$name )</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “$name&lt;br&gt;”;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>Output:</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
id</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
name</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
email</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 2</span></i></h1>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Extract
results into the array $users..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$users = $db-&gt;get_results(</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“SELECT id, name,
email FROM users”</span><span style='font-size:11.0pt;font-family:Arial;
color:navy'>);</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// View
all meta information for all columns..</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>$db-&gt;vardump($db-&gt;col_info);</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;hide_errors</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>$db-&gt;hide_errors –
</span>turn ezSQL error output to browser off</p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;hide_errors</span></b><span style='font-size:11.0pt;
font-family:Arial'>( void )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;hide_errors</span></b><span style='font-size:11.0pt;
font-family:Arial'>() stops error output from being printed to the web client.
If you would like to stop error output but still be able to trap errors for
debugging or for your own error output function you can make use of the global
error array $EZSQL_ERROR. </span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><i><span style='font-size:
11.0pt;font-family:Arial'>Note:</span></i></b><i><span style='font-size:11.0pt;
font-family:Arial'> If there were no errors then the global error array
$EZSQL_ERROR will evaluate to false. If there were one or more errors then it
will have&nbsp; the following structure. Errors are added to the array in order
of being called.</span></i></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:10.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>$EZSQL_ERROR[0] = Array</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>(</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
[query] =&gt; SOME BAD QUERY</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
[error_str] =&gt; You have an error in your SQL syntax near ‘SOME BAD QUERY' at
line 1</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>$EZSQL_ERROR[1] = Array</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>(</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
[query] =&gt; ANOTHER BAD QUERY</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
[error_str] =&gt; You have an error in your SQL syntax near ‘ANOTHER BAD QUERY'
at line 1</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>)</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>$EZSQL_ERROR[2] = Array</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>(</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
[query] =&gt; THIRD BAD QUERY</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
[error_str] =&gt; You have an error in your SQL syntax near ‘THIRD BAD QUERY'
at line 1</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:9.0pt;
font-family:Arial'>)</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 1</span></i></h1>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:14.0pt;
color:navy'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:14.0pt;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// Using a
custom error function</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;hide_errors();</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// Make a silly query that will produce an
error</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;query(“INSERT INTO my_table A BAD QUERY
THAT GENERATES AN ERROR”);</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// And another one, for good measure</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;query(“ANOTHER BAD QUERY THAT GENERATES
AN ERROR”);</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:purple'>// If the global error array exists at all then
we know there was 1 or more ezSQL errors..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>if ( $EZSQL_ERROR )</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:purple'>// View
the errors</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
$db-&gt;vardump($EZSQL_ERROR);</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>else</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>{</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
echo “No Errors”;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>}</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;show_errors</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>$db-&gt;show_errors –
</span>turn ezSQL error output to browser on</p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;show_errors</span></b><span style='font-size:11.0pt;
font-family:Arial'>( void )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;show_errors</span></b><span style='font-size:11.0pt;
font-family:Arial'>() turns ezSQL error output to the browser on. If you have
not used the function $db-&gt;hide_errors this function (show_errors) will have
no effect.</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>$db-&gt;escape</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>$db-&gt;escape – </span>Format
a string correctly in order to stop accidental mal formed queries under all PHP
conditions.</p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-family:Arial;color:navy'>Description</span></b></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><b><span style='font-size:11.0pt;
font-family:Arial'>$db-&gt;escape</span></b><span style='font-size:11.0pt;
font-family:Arial'>( string )</span></p>

<p class=MsoNormal style='margin-left:36.0pt'><span style='font-size:11.0pt;
font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:11.0pt;font-family:Arial'>$db-&gt;escape</span></b><span
style='font-size:11.0pt;font-family:Arial'>() makes any string safe to use as a
value in a query under all PHP conditions. I.E. if magic quotes are turned on
or off. Note: Should not be used by itself to guard against SQL injection
attacks. The purpose of this function is to stop accidental mal formed queries.</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 1</span></i></h1>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:purple'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
// Escape and assign the value..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>$title =
$db-&gt;escape</span><span style='font-size:11.0pt;font-family:Arial;
color:#333333'>(“Justin’s and Amy’s Home Page”</span><span style='font-size:
11.0pt;font-family:Arial;color:navy'>);</span></p>

<p class=MsoNormal><span style='font-family:Arial;color:#993366'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-family:Arial;color:#993366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
// Insert in to the DB..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;query(</span><span style='font-size:11.0pt;
font-family:Arial;color:#333333'>“INSERT INTO pages (title) VALUES (’$title’)”</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'>) ;</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

<h1><i><span style='font-size:11.0pt;font-family:Arial;color:navy'>Example 2</span></i></h1>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:purple'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
// Assign the value..</span></p>

<p class=MsoNormal><span style='font-size:11.0pt;font-family:Arial;color:purple'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>$title = </span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>“Justin’s and Amy’s
Home Page”</span><span style='font-size:11.0pt;font-family:Arial;color:navy'>;</span></p>

<p class=MsoNormal><span style='font-family:Arial;color:#993366'>&nbsp;</span></p>

<p class=MsoNormal><span style='font-family:Arial;color:#993366'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
// Insert in to the DB and escape at the same time..</span></p>

<p class=MsoNormal style='text-indent:36.0pt'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>$db-&gt;query(</span><span style='font-size:11.0pt;
font-family:Arial;color:#333333'>“INSERT INTO pages (title) VALUES (’”.</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'> $db-&gt;escape</span><span
style='font-size:11.0pt;font-family:Arial;color:#333333'>($title).”’)”</span><span
style='font-size:11.0pt;font-family:Arial;color:navy'>) ;</span></p>

<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>

<p class=MsoNormal style='border:none;padding:0cm'><span style='font-size:11.0pt;
font-family:Arial;color:navy'>&nbsp;</span></p>

</div>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoNormal><b><span style='font-size:14.0pt;font-family:Arial;
color:navy'>Disk Caching</span></b></p>

<p class=MsoNormal><span style='font-size:10.0pt;font-family:Arial'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>ezSQL has the ability
to cache your queries which can make dynamic sites run much faster. </span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>If you want to cache
EVERYTHING just do..</span></p>

<p class=MsoBodyTextIndent style='margin-left:0cm'><span style='font-size:11.0pt'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>            <span
style='color:navy'>$db-&gt;use_disk_cache</span> = true;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>            <span
style='color:navy'>$db-&gt;cache_queries</span> = true;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>            <span
style='color:navy'>$db-&gt;cache_timeout</span> = 24;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>&nbsp;</span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>For full details and
more specific options please see: </span></p>

<p class=MsoBodyTextIndent><span style='font-size:11.0pt'>&nbsp;</span></p>

<p class=MsoBodyTextIndent style='margin-left:72.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Symbol'>·<span style='font:7.0pt "Times New Roman"'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></span><span style='font-size:11.0pt;color:navy'>mysql/disk_cache_example.php</span></p>

<p class=MsoBodyTextIndent style='margin-left:72.0pt;text-indent:-18.0pt'><span
style='font-size:11.0pt;font-family:Symbol'>·<span style='font:7.0pt "Times New Roman"'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span></span><span style='font-size:11.0pt;color:navy'>oracle8_9/disk_cache_example.php</span></p>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal>&nbsp;</p>

</div>

</body>

</html>
