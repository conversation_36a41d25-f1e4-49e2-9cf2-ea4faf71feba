<?php
use Du\Message\Message;
use Du\Utils\TimeUtil;

include 'Du.php';
Du::init();	// Demo因为单独使用TimeUtil类需要提前初始化，正常不需要显示调用init()

$startTimes = TimeUtil::currentTimeInMillis();

class ExceptionTest
{
    function methodThrowsException()
    {
        throw new Exception;
    }

    public function run()
    {
        try {
            $a = $this->methodThrowsException();
            echo $a;
            echo "hello, world!!!\n";
        } catch (Exception $e) {
            Du::logError('Error', get_class($e), $e);
        }
    }
}

//$test = new ExceptionTest();
//$test->run();
class TransactionTest
{
    public function run()
    {  
        $transaction = Du::newTransaction("URL", "/Test_6");  // 构建监控代码第一个参数 是type 第二个参数name
        {
			$t = Du::newTransaction('Invoke', 'method1()');
			$str = $_SERVER['HTTP_DUCONTEXT'];	// HTTP通过Header获取Context示例
			if (!$str) {
				$args = getopt('x:');		// CLI通过参数获取Context示例
				$str = $args['x'];
			}
			//$str = '{"_catChildMessageId":"megrez-executor-shell-ac1509b3-410386-10024","_catParentMessageId":"megrez-ac100245-410386-1136","_catRootMessageId":"megrez-ac100245-410386-1136"}';
			print_r($str);
			if ($str) {
				$ctx = json_decode($str);
				print_r($ctx);
				Du::logRemoteCallServer($ctx->_catRootMessageId,$ctx->_catParentMessageId,$ctx->_catChildMessageId);
			}
			try {
			    // 业务执行代码
			    // ......
			    $t->addData("Hello", "world");		// 记录数据 将在logview中显示
			    $t->setStatus(Message::SUCCESS);	// 设置状态，string类型，非0是失败。
			    //throw new Exception();
			} catch (Exception $e) {
			    Du::logError('Error', get_class($e), $e);	// 记录错误日志
			    //$t->setStatus("ERROR");		// 设置状态为失败。
			} finally {
				$t->complete();				// 提交事务 ，必须要提交
			}
        }

        {
            $t2 = Du::newTransaction('Invoke', 'method2()');
            //sleep(2);
            $t2->addData("TooLong", str_repeat("Test ", 200));
            $t2->setStatus(Message::SUCCESS);
            $t2->complete();
        }

        {
            $t3 = Du::newTransaction('Invoke', 'method3()');
            //sleep(1);
            {
                $t4 = Du::newTransaction('Invoke', 'method4()');
                $ctx = Du::logRemoteCallClient();
                print_r($ctx);
                //sleep(2);
                $t4->setStatus(Message::SUCCESS);
                $t4->complete();
            }

			// Cache
			$cacheType = 'Cache.redis';
			$cacheMethod = 'get';
			for ($i=0; $i<100; $i++) {
				$cacheName = 'key_abc_' . $i;
		        $t5 = Du::newTransaction($cacheType, $cacheName . ':' . $cacheMethod);
		        $eventStatus = rand(0,2) == 1 ? "missed" : 'hited'; // 可以为空 如果missed则缓存获取失败 上面的status必须是get，直接影响Hit Rate值
		        Du::logEvent($cacheType, $cacheName . ":" . $eventStatus);
		        Du::logEvent($cacheType . ".server", "127.0.0.1");
		        //usleep(rand(1,1000000));
	            $t5->setStatus(Message::SUCCESS);
	            $t5->complete();
            }
           		
            $t3->setStatus(Message::SUCCESS);
            $t3->complete();
        }

        $transaction->setStatus(Du::SUCCESS);
        $transaction->addData("Hello, world!");
        $transaction->complete();
    }
}

class MetricDemo
{
    public function run()
    {
        Du::logMetricForCount("支付笔数");		// 计数+1
        Du::logMetricForCount("购买数量", 5);	// 计数 +n
        Du::logMetricForSum("支付总额", 100.5);	// 总数
    }
}

//$metricTest = new MetricDemo();
//$metricTest->run();

$test = new TransactionTest();
$test->run();

//Du::logEvent('Dubhe', 'TooBig', 'len', '1234', 'Error');

echo 'ts:' . (TimeUtil::currentTimeInMillis() - $startTimes) . 'ms ';