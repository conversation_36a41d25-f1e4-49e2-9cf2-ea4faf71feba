<?php
/**
 * @author: ahua<PERSON><EMAIL>
 * Date: 16/7/19  下午2:16
 */

namespace Du\Utils;


class SharedMemory
{
    private $nameToKey = array();
    private $key;
    private $id;
    private $size;
    function __construct($key = null, $size = 1000){
        if($key === null) {
            $tmp = tempnam('/tmp', 'PHPSHM');
            $key = ftok($tmp, 'a');
        }
        $this->key = $key;
        $this->size = $size;
        $this->id = shm_attach($this->key, $this->size);
    	if (shm_has_var($this->id, 0))
        	$this->refreshMemoryVarList();
        else {
        	$this->nameToKey[] = '';
        	$this->nameToKey[] = '';
        	$this->updateMemoryVarList();
        }
    	if (shm_has_var($this->id, 1))
        	shm_put_var($this->id, 1, shm_get_var($this->id, 1) + 1);
        else
        	shm_put_var($this->id, 1, 1);
        if(!$this->id)
            die('Unable to create shared memory segment');
    }
    function __destruct(){
        shm_put_var($this->id, 1, shm_get_var($this->id, 1) - 1);
        shm_detach($this->id);
    }
    function getKey(){
        return $this->key;
    }
  	function getKeys() {
		return $this->nameToKey;
  	}
    function remove(){
       shm_remove($this->id);
    }
    function refreshMemoryVarList(){
       	$this->nameToKey = shm_get_var($this->id, 0);
    }
    function updateMemoryVarList(){
        shm_put_var($this->id, 0, $this->nameToKey);
    }
    function __get($var){
        if(!in_array($var, $this->nameToKey)){
            $this->refreshMemoryVarList();
        }
        if(in_array($var, $this->nameToKey))
        	$val = shm_get_var($this->id, array_search($var, $this->nameToKey));
        return $val;
    }
    function __set($var, $val){
        if(!in_array($var, $this->nameToKey)){
            $this->refreshMemoryVarList();
            $this->nameToKey[] = $var;
            $this->updateMemoryVarList();
        }
        shm_put_var($this->id, array_search($var, $this->nameToKey), $val);
    }
}

/*
// Example
$sharedMem = new SharedMemory(1627527136, 1024*1024*1024);
$pid = pcntl_fork();
if($pid){
    //parent
    sleep(1);
    echo "Parent Says: a=" . $sharedMem->a . "\n";
    echo "Parent Changed a to 0\n";
    $sharedMem->a = 0;
    //Parent just changed it to 0
    echo "Parent Says: a=" . $sharedMem->a . "\n";
    sleep(2);
    // Parent think's it's 0, but child has changed it to 1
    echo "Parent Says: a=" . $sharedMem->a . "\n";
}else{
    //child
    $sharedMem->a = 2;
    echo "Child Changed a to 2\n";
    // Should be 2 as child just set it to 2
    echo "Child Says: a=" . $sharedMem->a . "\n";
    sleep(2);
    // Child think's it's 2, but the parent set it to 0.
    echo "Child Says: a=" . $sharedMem->a . "\n";
    echo "Child Added a++\n";
    $sharedMem->a++;
    echo "Child Says: a=" . $sharedMem->a . "\n";

//    $sharedMem->b = 23;
    echo "Child Says: b=" . $sharedMem->b . "\n";
    echo "Child Added b++\n";
    $sharedMem->b++;
    echo "Child Says: b=" . $sharedMem->b . "\n";

    echo "key: ";
    print_r($sharedMem->getKeys());
}
*/