<?php
/**
 * @author: ah<PERSON><PERSON><PERSON>@gmail.com
 * Date: 16/7/19  下午6:00
 */

namespace Du\Transfer\Impl;

use Du\Codec\PlainTextCodec;
use Du\Config\Config;
use Du\Transfer\Sender;
use Du\Utils\TimeUtil;

class SocketSender implements Sender
{
	private static $m_socket = false;

    private $m_codec;


    public function __construct()
    {
        $this->m_codec = new PlainTextCodec();
    }

    function send($messageTree)
    {
        //TODO: performance consider, should keep the connection.
        $data = $this->m_codec->encode($messageTree);
        $len = strlen($data);

        $len_bin = pack('N', $len);
        $data_bin = pack("a{$len}", $data);

        $_data = $len_bin . $data_bin;

        
		$servers = Config::getServers();
        $ip = $servers['info'][0];
        $port = $servers['info'][1];

		$startTimes = TimeUtil::currentTimeInMillis();
		if (!is_resource(self::$m_socket)) {
			self::$m_socket = stream_socket_client('tcp://' . $ip . ':' . $port);
		}
		
        //var_dump($_data);
		
		fwrite(self::$m_socket, $_data);
		//fclose(self::$m_socket);
        //echo 'socket-ts:' . (TimeUtil::currentTimeInMillis() - $startTimes) . 'ms ';
    }

	function close() {
		if (is_resource(self::$m_socket))
			fclose(self::$m_socket);
	}
}