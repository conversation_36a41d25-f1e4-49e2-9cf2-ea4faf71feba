<?php
/**
 * @author: ah<PERSON><PERSON><PERSON>@gmail.com
 * Date: 16/7/19  下午6:00
 */

namespace Du\Transfer\Impl;

use Du\Codec\PlainTextCodec;
use Du\Config\Config;
use Du\Transfer\Sender;
use Du\Utils\TimeUtil;

class FlumeSender implements Sender
{
	private static $m_socket = false;

    private $m_codec;


    public function __construct()
    {
        $this->m_codec = new PlainTextCodec();
    }

	const MAX_SIZE = 65507;
    function send($messageTree)
    {
        $sdata = $this->m_codec->encode($messageTree);
        $data = 'N' . $sdata;
        $len = strlen($data);
		if ($len > self::MAX_SIZE && function_exists('gzencode')) {
	        $data = 'Z' . gzencode($sdata);
	        $len = strlen($data);
		}        
		if ($len > self::MAX_SIZE) {
			$arr = explode("\n", $sdata);
			$data = 'N' . $arr[0] . "\nE" . TimeUtil::format(TimeUtil::currentTimeInMillis()) . "	Dubhe	php too big	Error	source=" . substr(str_replace("\t", " ", $arr[1]), 0, 256) . "	\n";
			$len = strlen($data);
		}
		
        $len_bin = pack('N', $len);
        $data_bin = pack("a{$len}", $data);

        $_data = $len_bin . $data_bin;
        
		$servers = Config::getServers();
        $ip = $servers['info'][0];
        $port = $servers['info'][1];

		$startTimes = TimeUtil::currentTimeInMillis();
		if (!is_resource(self::$m_socket)) {
			self::$m_socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
		}
		//var_dump($_data);
		$ret = socket_sendto(self::$m_socket, $_data, strlen($_data), 0, $ip, $port);
    }

	function close() {
		if (is_resource(self::$m_socket))
			socket_close(self::$m_socket);
	}
}