<?php
/**
 * @author: ah<PERSON><PERSON><PERSON>@gmail.com
 * Date: 16/7/19  下午6:00
 */
namespace Du\Transfer\Impl;

use Du\Codec\PlainTextCodec;
use Du\Transfer\Sender;
use Du\Utils\TimeUtil;
use Du\Config\Config;
use <PERSON><PERSON><PERSON><PERSON>\Producer;
use <PERSON><PERSON>afka\Conf;

class KafkaSender implements Sender
{
	private static $m_producer = false;

    private $m_codec;

    public function __construct()
    {
       	$this->m_codec = new PlainTextCodec();   
        if (self::$m_producer === false) {
	        $servers = Config::getServers();
        	$HostList = $servers['info'][0];
        	$TopicName = $servers['info'][1];

	        $conf = new Conf();
	        $conf->set('internal.termination.signal', SIGIO);
	        $rk = new Producer($conf);
	        $rk->setLogLevel(LOG_ERR);
			$rk->addBrokers($HostList);
			self::$m_producer = $rk->newTopic($TopicName);
	    }
    }

    function send($messageTree)
    {
        //TODO: performance consider, should keep the connection.
        $data = $this->m_codec->encode($messageTree);
        $len = strlen($data);

        $len_bin = pack('N', $len);
        $data_bin = pack("a{$len}", $data);

        $_data = $len_bin . $data_bin;

        //var_dump($_data);

		$startTimes = TimeUtil::currentTimeInMillis();
		
		self::$m_producer->produce(RD_KAFKA_PARTITION_UA, 0, $_data);

        //echo 'kafka-ts:' . (TimeUtil::currentTimeInMillis() - $startTimes) . 'ms ';
    }

	function close() {
	}
}