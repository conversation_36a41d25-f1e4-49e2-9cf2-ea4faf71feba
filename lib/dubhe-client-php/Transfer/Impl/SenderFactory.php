<?php
/**
 * @author: ah<PERSON><PERSON><PERSON>@gmail.com
 * Date: 16/7/19  下午1:58
 */

namespace Du\Transfer\Impl;


use Du\Config\Config;

class SenderFactory {
    private static $sender;
	
    public static function getSender() {
       	if (self::$sender == null) {
			$servers = Config::getServers();
			$className = 'Du\\Transfer\\Impl\\' . $servers['sender'] . 'Sender';
			self::$sender = new $className();
        }

        return self::$sender;
    }
}