<?php
use Du\Message\Impl\DefaultMessageProducer;
use Du\Message\Context;
use Du\Message\Impl\DefaultContext;

/**
 * Class Du
 */
class Du
{
    const SUCCESS = "0";

    private static $messageProducer;

    public static function init()
    {
        if (function_exists('__autoload')) {
            spl_autoload_register('__autoload');
        }

        if (version_compare(PHP_VERSION, '5.3.0') >= 0) {
            spl_autoload_register(array('Du', 'autoload'), true, true);
        } else {
            spl_autoload_register(array('Du', 'autoload'));
        }

        self::$messageProducer = new DefaultMessageProducer();

        self::$messageProducer->init();
    }
    
	public static function autoload($class) {
		if ((class_exists($class,FALSE)) || (strpos($class, 'Du\\') !== 0)) {
             return false;
        }

	    $class = str_replace("Du\\", "", $class);
	    $path = str_replace("\\", "/", $class);
	    require_once($path . '.php');
	}

    public static function newTransaction($type, $name)
    {

        if (self::$messageProducer == null) {
            self::init();
        }

        return self::$messageProducer->newTransaction($type, $name);
    }

    public static function logEvent($type, $name, $key = null, $value = null, $status = self::SUCCESS)
    {
        $event = self::newEvent($type, $name);
        $event->setStatus($status);
        $event->addData($key, $value);
        $event->complete();
    }

    public static function logError($type, $name, Exception $error)
    {
        $event = self::newEvent($type, $name);
        $event->setStatus($error->getMessage());

        $trace = "\n" . $error->getMessage() . "\n";
        $trace .= $error->getTraceAsString() . "\n";

        $event->addData('Trace', $trace);

        $event->complete();
    }

    public static function logMetricForCount($name, $quantity = 1)
    {
        self::logMetricInternal($name, 'C', sprintf("%d", $quantity));
    }

    public static function logMetricForDuration($name, $durationInMillis)
    {
        //TODO implement logMetricForDuration
    }


    public static function logMetricForSum($name, $value = 1.0)
    {
        self::logMetricInternal($name, 'S', sprintf("%.2f", $value));
    }

	public static function logRemoteCallClient() {
        if (self::$messageProducer == null) {
            self::init();
        }
 		$ctx = new DefaultContext();
		self::$messageProducer->logRemoteCallClient($ctx);
		self::logEvent("RemoteCall", "", "nokey", $ctx->getChildMessageId());
		return $ctx;
	}

	public static function logRemoteCallServer($rootMessageId,$parentMessageId,$childMessageId) {
        if (self::$messageProducer == null) {
            self::init();
        }
		$ctx = new DefaultContext();
		$ctx->setRootMessageId($rootMessageId);
		$ctx->setParentMessageId($parentMessageId);
		$ctx->setChildMessageId($childMessageId);
		self::$messageProducer->logRemoteCallServer($ctx);
	}

    private static function logMetricInternal($name, $status, $keyValuePairs)
    {
        if (self::$messageProducer == null) {
            self::init();
        }

        $type = '';
        $metric = self::$messageProducer->newMetric($type, $name);

        if (isset($keyValuePairs)) {
            $metric->addData($keyValuePairs);
        }

        $metric->setStatus($status);
        $metric->complete();
    }

    public static function newEvent($type, $name)
    {
        if (self::$messageProducer == null) {
            self::init();
        }

        return self::$messageProducer->newEvent($type, $name);
    }

	public static function close() {
        if (self::$messageProducer != null)
			self::$messageProducer->close();
	}
}