<?php
/**
 * @author: ah<PERSON><PERSON><PERSON>@gmail.com
 * Date: 16/7/19  下午12:40
 */

namespace Du\Message\Impl;


use Du\Message\Event;

class DefaultEvent extends AbstractMessage implements Event
{

    public function __construct($type, $name, $messageManager)
    {
        parent::__construct($type, $name, $messageManager);
    }

    function complete()
    {
        parent::setCompleted(true);

        if (parent::getMessageManager() != null) {
            parent::getMessageManager()->add($this);
        }
    }
}