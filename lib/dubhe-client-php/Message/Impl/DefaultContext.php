<?php
/**
 * @author: ahua<PERSON><PERSON>@gmail.com
 * Date: 16/7/19  下午12:40
 */

namespace Du\Message\Impl;

use Du\Message\Context;

class DefaultContext implements Context
{
	public $_catRootMessageId;
	public $_catParentMessageId;
	public $_catChildMessageId;
	
	public function getRootMessageId() {
		return $this->_catRootMessageId;
	}
	
	public function getParentMessageId() {
		return $this->_catParentMessageId;
	}
	
	public function getChildMessageId() {
		return $this->_catChildMessageId;
	}

	public function setRootMessageId($rootMessageId) {
		$this->_catRootMessageId = $rootMessageId;
	}
	
	public function setParentMessageId($parentMessageId) {
		$this->_catParentMessageId = $parentMessageId;
	}
	
	public function setChildMessageId($childMessageId) {
		$this->_catChildMessageId = $childMessageId;
	}
}