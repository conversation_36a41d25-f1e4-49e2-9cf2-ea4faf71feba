<?php
/**
 * @author: ahua<PERSON><PERSON>@gmail.com
 * Date: 16/7/19  下午1:46
 */

namespace Du\Message\Impl;

use Du\Message\MessageIdFactory;
use Du\Message\MessageProducer;
use Du\Message\Context;

class DefaultMessageProducer implements MessageProducer
{

    private $m_messageManager;


    public function newEvent($type, $name)
    {
        $this->checkInit();
        $event = new DefaultEvent($type, $name, $this->m_messageManager);
        return $event;
    }

    public function newTransaction($type, $name)
    {
        $this->checkInit();
        $transaction = new DefaultTransaction($type, $name, $this->m_messageManager);
        $this->m_messageManager->start($transaction);
        return $transaction;
    }

    public function newMetric($type, $name)
    {
        $this->checkInit();
        $metric = new DefaultMetric(isset($type) ? $type : "", $name, $this->m_messageManager);
//        $this->m_messageManager->getThreadLocalMessageTree()->setSample(false);
        return $metric;
    }


    public function checkInit()
    {
        if ($this->m_messageManager == null) {
            $this->init();
        }
        if (!$this->m_messageManager->hasContext()) {
            $this->m_messageManager->setUp();
        }
    }

    public function init()
    {
        $this->m_messageManager = new SingleThreadMessageManager();
    }


    public function close()
    {
        $this->m_messageManager->close();
	}
	
	public function logRemoteCallClient($ctx) {
        $this->checkInit();
		$tree = $this->m_messageManager->getThreadLocalMessageTree();
      	
		$messageId = $tree->getMessageId();
		$rootId = $tree->getRootMessageId();
		$childId = DefaultMessageIdFactory::getNextId();

		if ($rootId == null)
			$rootId = $messageId;
		$ctx->setRootMessageId($rootId);
		$ctx->setParentMessageId($messageId);
		$ctx->setChildMessageId($childId);	
	}

	public function logRemoteCallServer($ctx) {
        $this->checkInit();
		$messageId = $ctx->getChildMessageId();
		$rootId = $ctx->getRootMessageId();
		$parentId = $ctx->getParentMessageId();

		$tree = $this->m_messageManager->getThreadLocalMessageTree();
		if ($messageId != null) {
			$tree->setMessageId($messageId);
		}
		if ($parentId != null) {
			$tree->setParentMessageId($parentId);
		}
		if ($rootId != null) {
			$tree->setRootMessageId($rootId);
		}		
	}
}