<?php
/**
 * @author: ah<PERSON><PERSON><PERSON>@gmail.com
 * Date: 16/7/19  下午12:42
 */

namespace Du\Message\Impl;


use Du\Message\Message;
use Du\Utils\TimeUtil;

abstract class AbstractMessage implements Message
{
	const MAX_DATA_SIZE = 512;
	
    private $m_type;

    private $m_name;

    protected $m_status = "unset";

    private $m_timestampInMillis;

    private $m_data;

    private $m_completed;

    private $m_manager;

    public function __construct($type, $name, $messageManager)
    {

        $this->m_type = $type;
        $this->m_name = $name;
        $this->m_manager = $messageManager;
        $this->m_timestampInMillis = TimeUtil::currentTimeInMillis();

    }


    public function addData($key, $value = null)
    {
        if ($key != null) {
            $pair = '';
            if ($key != "nokey") $pair = $key . "=";
            if ($value!= null) $pair .= strlen($value)>self::MAX_DATA_SIZE ? substr($value, 0, self::MAX_DATA_SIZE) . '...' : $value;

            if ($this->m_data == null) {
                $this->m_data = $pair;
            } else {
                $this->m_data = $this->m_data . '&' . $pair;
            }
        }

    }

    public function getData()
    {
        if ($this->m_data == null) {
            return "";
        } else {
            return $this->m_data;
        }
    }

    public function getType()
    {
        return $this->m_type;
    }

    public function getName()
    {
        return $this->m_name;
    }

    public function isCompleted()
    {
        return $this->m_completed;
    }

    public abstract function complete();

    public function isSuccess()
    {
        return strcmp($this->m_status, Message::SUCCESS) == 0;
    }

    public function setStatus($status)
    {
        $this->m_status = $status;
    }

    public function getStatus()
    {
        return $this->m_status;
    }

    public function setTimestamp($timestamp)
    {
        $this->m_timestampInMillis = $timestamp;
    }

    public function getTimestampInMillis()
    {
        return $this->m_timestampInMillis;
    }

    public function getMessageManager()
    {
        return $this->m_manager;
    }

    public function setCompleted($completed)
    {
        $this->m_completed = $completed;
    }


}