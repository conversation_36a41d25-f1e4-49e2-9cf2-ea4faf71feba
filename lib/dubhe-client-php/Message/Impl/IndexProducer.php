<?php
/**
 * @author: ah<PERSON><PERSON><PERSON>@gmail.com
 * Date: 16/7/20  下午2:37
 */

namespace Du\Message\Impl;


use Du\Utils\Env;

class IndexProducer
{
	const SEM_KEY = 1;
	
    private $m_sem_key;
    private $m_sem_length;

	/** 
     * $key: 每个独立�?key标记为一个计数器 
	 * $length: 分配内存大小 
	 */
    function __construct($key = 1, $length = 16) {
//	    if ($key == 1)
//	    	$this->m_sem_key = ftok(__FILE__, 't');
//	    else
	    	$this->m_sem_key = $key;
	    $this->m_sem_length = $length;
    }

    public function nextIndex($currentHourStamp)
    {
        //    $this->check($currentHourStamp);
        //    return $this->counter();
        if (Env::isLinux()) {
            //$nano = intval(system('date +%N'));
            //list($_, $sec) = explode(" ", microtime());
            //return (($sec % 3600) << 20) | ($nano >> 20);
            $this->check($currentHourStamp);
            return $this->counter();
        } else {
            list($usec, $sec) = explode(" ", microtime());
            //return (($sec % 3600) << 20) | ($usec * 1024 * 1024);
            return fmod($sec, 3600) . sprintf('%02d', rand(0, 99));
        }
    }

	/** 
	 * 计数生成�?
	 * 采用共享内存生成 
	 * $reseted: 重置标志
	 */  
	private function counter($reseted = false) {
		$sem_id = sem_get($this->m_sem_key);
		if (sem_acquire($sem_id)) {
		    $segment_id = shmop_open($this->m_sem_key, 'c', 0777, $this->m_sem_length);
		    if ($reseted) {
			    $now = 0;
		    	shmop_delete($segment_id);
		    } else {
		    	$now = intval(shmop_read($segment_id, 0, shmop_size($segment_id))) + 1;
		    	shmop_write($segment_id, (string)$now, 0);
		    }
		    shmop_close($segment_id);
		    sem_release($sem_id);
    	}
	    return $now;
	}

	private function check($currentHourStamp) {
		$sem_key = $this->m_sem_key + 1;
	    $segment_id = shmop_open($sem_key, 'c', 0777, $this->m_sem_length);
	    $lastHourStamp = intval(shmop_read($segment_id, 0, shmop_size($segment_id)));
		if ($currentHourStamp != $lastHourStamp) {
	    	shmop_write($segment_id, (string)$currentHourStamp, 0);
			$this->counter(true);
		}
	    shmop_close($segment_id);
	}
}