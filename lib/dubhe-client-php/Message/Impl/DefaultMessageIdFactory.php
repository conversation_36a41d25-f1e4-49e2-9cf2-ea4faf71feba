<?php
/**
 * @author: ah<PERSON><PERSON><PERSON>@gmail.com
 * Date: 16/7/19  下午1:58
 */

namespace Du\Message\Impl;


use Du\Config\Config;
use Du\Utils\NetUtil;
use Du\Utils\ThreadUtil;
use Du\Utils\TimeUtil;

class DefaultMessageIdFactory
{
    private static $domain;

    private static $hexIpAddress;

    public static function getNextId($domain=null)
    {
        self::checkInit();

        $currentHourStamp = self::currentHourStamp();
        $index = self::getIndexProducer()->nextIndex($currentHourStamp);

        $messageId = $domain == null ? self::$domain : $domain;
        $messageId .= "-";
        $messageId .= self::$hexIpAddress . "-";
        $messageId .= $currentHourStamp . "-";

        $messageId .= $index;

		return $messageId;

    }

    private static function checkInit()
    {
        if (self::$domain == null) {
            self::$domain = Config::getDomain();
        }

        if (self::$hexIpAddress == null) {
            self::$hexIpAddress = NetUtil::getHexIpAddress();
        }
    }


    private static $indexProducer;

    private static function currentHourStamp()
    {
        return (int) (TimeUtil::currentTimeInSecond() / 3600);
    }

    private static function getIndexProducer()
    {
        if (self::$indexProducer == null) {
            self::$indexProducer = new IndexProducer();
        }

        return self::$indexProducer;
    }

}