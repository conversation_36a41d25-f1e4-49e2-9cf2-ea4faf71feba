TCPDF - README
============================================================

I WISH TO IMPROVE AND EXPAND TCPDF BUT I NEED YOUR SUPPORT.
PLEASE MAKE A DONATION:
http://sourceforge.net/donate/index.php?group_id=128076

------------------------------------------------------------

Name: TCPDF
Version: 5.9.009
Release date: 2010-10-21
Author:	<PERSON> (c) 2002-2010:
	Nicola <PERSON>.com s.r.l.
	<PERSON>, 11
	09044 Quartucciu (CA)
	ITALY
	www.tecnick.com

URLs:
	http:  www.tcpdf.org
	http:  www.sourceforge.net/projects/tcpdf

Description:
	TCPDF is a PHP class for generating PDF files on-the-fly without requiring external extensions.

Main Features:
    * no external libraries are required for the basic functions;
    * all standard page formats, custom page formats, custom margins and units of measure;
    * UTF-8 Unicode and Right-To-Left languages;
    * TrueTypeUnicode, OpenTypeUnicode, TrueType, OpenType, Type1 and CID-0 fonts;
    * font subsetting;
    * methods to publish some XHTML + CSS code, Javascript and Forms;
    * images, graphic (geometric figures) and transformation methods;
    * supports JPEG, PNG and SVG images natively, all images supported by GD (GD, GD2, GD2PART, GIF, JPEG, PNG, BMP, XBM, XPM) and all images supported via ImagMagick (http:  www.imagemagick.org/www/formats.html)
    * 1D and 2D barcodes: CODE 39, ANSI MH10.8M-1983, USD-3, 3 of 9, CODE 93, USS-93, Standard 2 of 5, Interleaved 2 of 5, CODE 128 A/B/C, 2 and 5 Digits UPC-Based Extention, EAN 8, EAN 13, UPC-A, UPC-E, MSI, POSTNET, PLANET, RMS4CC (Royal Mail 4-state Customer Code), CBC (Customer Bar Code), KIX (Klant index - Customer index), Intelligent Mail Barcode, Onecode, USPS-B-3200, CODABAR, CODE 11, PHARMACODE, PHARMACODE TWO-TRACKS, QR-Code, PDF417;
    * Grayscale, RGB, CMYK, Spot Colors and Transparencies;
    * automatic page header and footer management;
    * document encryption up to 256 bit and digital signature certifications;
    * transactions to UNDO commands;
    * PDF annotations, including links, text and file attachments;
    * text rendering modes (fill, stroke and clipping);
    * multiple columns mode;
    * no-write page regions;
    * bookmarks and table of content;
    * text hyphenation;
    * text stretching and spacing (tracking/kerning);
    * automatic page break, line break and text alignments including justification;
    * automatic page numbering and page groups;
    * move and delete pages;
    * page compression (requires php-zlib extension);
    * XOBject Templates;

Installation (full instructions on http:  www.tcpdf.org):
	1. copy the folder on your Web server
	2. set your installation path and other parameters on the config/tcpdf_config.php
	3. call the examples/example_001.php page with your browser to see an example

Source Code Documentation:
	doc/index.html

For Additional Documentation:
	http:  www.tcpdf.org

License
	Copyright (C) 2002-2010  Nicola Asuni - Tecnick.com S.r.l.

	TCPDF is free software: you can redistribute it and/or modify it
	under the terms of the GNU Lesser General Public License as
	published by the Free Software Foundation, either version 3 of the
	License, or (at your option) any later version.

	TCPDF is distributed in the hope that it will be useful, but
	WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
	See the GNU Lesser General Public License for more details.

	You should have received a copy of the GNU Lesser General Public License
	along with TCPDF.  If not, see <http://www.gnu.org/licenses/>.

	See LICENSE.TXT file for more information.

============================================================
