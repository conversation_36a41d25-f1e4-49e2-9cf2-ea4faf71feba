<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

//健康检查
Route::get('/heath/check', 'HeathController@check');
Route::get('/heath/pushStation', 'HeathController@pushStation');

Route::any('api/v1/stationorgrule/getListPaginate', 'StationOrgRuleController@getListPaginate');
Route::any('api/v1/stationorgrule/showStationOrgRuleDetailsList', 'StationOrgRuleController@showStationOrgRuleDetailsList');

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

// 油站服务
Route::group(['prefix' => 'api/station/v1'], function () {
    Route::post('/station/list', 'StationController@list'); // 油站列表
    Route::post('/station/mobileList', 'StationController@getListForMobile'); // 油站列表For钉钉
    Route::post('/ownner/handle', 'OwnnerController@handle'); // 操作油站负责人
    Route::post('/station/detail', 'StationController@getDetail'); // 获取油站信息
    Route::post('/history/list', 'StationController@getHistoryList'); // 获取加油流水

    // 获取油站基本信息
    Route::post('/station/getStationBaseInfoByIdBatch', 'StationBatchController@getStationBaseInfoByIdBatch');
    Route::post('/station/stationPriceList','StationController@getStationAndPricePaginate');
    Route::post('/station/getStationBySelfOperatedSupplier','StationController@getStationBySelfOperatedSupplier');
    // Route::post('/station/getStationPriceList','StationController@getStationPriceList');
    Route::post('/station/pushListByOrgcode','StationController@pushListByOrgcode');
    //油站工具
    Route::post('/station/stationFormat','Ajax\StationController@stationFormat');
    Route::post('/station/stationPointFormat','Ajax\StationController@stationPointFormat');
});

Route::post('api/station/v1/station/getStationPriceList','StationController@getStationPriceList');

Route::group(['prefix' => 'api/coupon/v1', 'namespace'=>'Ajax'], function () {
    // 注销电子券
    Route::group(['namespace'=>'Coupon'],function (){
        Route::post('/revoke','CouponsController@revoke');
        Route::post('/getUnExpiredAndUnVerifiedCoupons','CouponsController@getUnExpiredAndUnVerifiedCoupons');
        Route::post('/getTripartiteCouponByTripartiteVoucherAndSupplierCode', 'CouponsController@getTripartiteCouponByTripartiteVoucherAndSupplierCode');
    });
});



//获取油站省市
Route::group(['prefix' => 'api/city/v1'], function () {
    Route::post('/city/list', 'CityController@list'); // 获取城市
});


//获取政策类别
Route::group(['prefix' => 'api/policy/v1', 'middleware'=> ['api']], function () {
    Route::post('/policy/list', 'PolicyController@list'); // 获取政策列表
    Route::post('/policy/classify', 'PolicyController@classify'); // pc端获取站点性质
    Route::post('/policy/addData', 'PolicyController@add'); // 添加政策

    Route::post('/policy/listById', 'PolicyController@listForMobile'); // 根据油站id获取政策列表
    Route::post('/policy/mobileClassify', 'PolicyController@classifyForMobile'); // 手机端获取政策类别

    Route::post('/policy/getOnePolicy', 'PolicyController@show'); // 根据油站id获取政策列表

    Route::post('/policy/delPolicy', 'PolicyController@delPolicy'); // 删除政策

    Route::post('/policy/allPolicyClassify', 'PolicyController@getAllClasify'); // 获取政策返利类别
});

//油站价格
Route::group(['namespace'=>'Api','prefix'=>'v1','middleware'=>['commonApi']],function (){
    Route::post('/station/getStationById','StationBatchController@getStation');
    Route::post('/station/getStationBySupplier','StationBatchController@getStationBySupplier');
    Route::post('/station/getAppStationIdByPcode','StationBatchController@getAppStationIdByPcode');
    Route::post('/station/compareStation','CompareTestController@getStationCompareTest');
    Route::post('/station/compareStationSupplier','CompareTestController@getStationBySupplierCompareTest');
    Route::post('/station/getStationBatch','StationBatchController@getStationByPcode');
    Route::post('/station/batchUpdateOrCreate','StationBatchController@batchUpdateOrCreate')->middleware('writeLog');
    Route::post('/station/batchStop','StationBatchController@batchStop')->middleware('writeLog');
    Route::post('/price/getOilPriceByTime','PriceBatchController@getOilPriceByTime')->middleware('writeLog');
//    Route::post('/price/batchUpdateOrCreate','PriceBatchController@batchUpdateOrCreate')->middleware('writeLog');
//    Route::post('/price/supplierPriceBatchUpdate','PriceBatchController@supplierPriceBatchUpdate')->middleware('writeLog');
    Route::post('/station/getStationByAppId','StationBatchController@getStationByAppId');

    Route::post('/supplier/getSelfOperatedSupplier','SupplierController@getSelfOperatedSupplier');
    Route::post('/stationorgrule/setPushStatus', 'StationBatchController@setPushStatus');//接收推送结果

    Route::post('/stationRule/getStationIds', 'StationRuleController@getStationIds');//2.0限站规则，限站后的站点id

});

Route::group(['namespace'=>'Api','prefix'=>'v1'],function (){
    Route::post('/station/getOnlyStation','StationBatchController@getOnlyStation');

    //为foss-task提供推送服务
    Route::post('/price/sendStationPrice','PriceBatchController@batchPushStationPrice');
    //限站工具查询
    Route::post('/stationRule/getStationLimitInfo','StationRuleController@getStationLimitInfo');

    //批量下发推站
    Route::post('/station/batchPush','StationBatchController@batchPush');
});


// api接口相关[阿里云、其他]
Route::group(['namespace' => 'Third', 'prefix' => 'third/api/v1'] , function () {
    Route::post('/api/getTodayOilPrice', 'ApiController@getTodayOilPrice'); // 获取今日各省份油品挂牌价
    Route::post('/api/setTodayOilPriceYiYuan', 'ApiController@setTodayOilPriceYiYuan'); // 更新今日各省份油品挂牌价[易源数据]
    Route::post('/api/clearTodayOilPriceYiYuan', 'ApiController@clearTodayOilPriceYiYuan'); // 清除挂牌价缓存[易源数据]
    Route::post('/api/setTodayOilPriceWangShang', 'ApiController@setTodayOilPriceWangShang'); // 更新今日各省份油品挂牌价[网尚科技]
    Route::post('/api/clearTodayOilPriceWangShang', 'ApiController@clearTodayOilPriceWangShang'); // 清除挂牌价缓存[网尚科技]

    Route::post('/api/setTodayOilPriceForGms', 'ApiController@setTodayOilPriceForGms'); // 更新今日各省市挂牌价ForGms
    Route::post('/api/getTodayOilPriceListForGms', 'ApiController@getTodayOilPriceForGms'); // 获取挂牌价列表
});

//G7S端接口路由
Route::group(['namespace'=>'Ajax','prefix'=>'g7s/v1','middleware'=>['g7s']],function (){
    Route::post('/station/oilNameRelationType','StationCommonController@oilNameRelationType');
    Route::post('/station/supplierList','StationCommonController@supplierList');
    Route::post('/station/stationNotes','StationCommonController@stationNotes');
    Route::post('/station/getClientRule','StationRuleController@getClientRule');
    Route::post('/station/clientRuleUpOrCreate','StationRuleController@clientRuleUpOrCreate');
    Route::post('/station/clientRuleDelete','StationRuleController@clientRuleDelete');
    Route::post('/station/getServiceStationFilterPaginate','StationFilterController@getServiceStationFilterPaginate');
    Route::post('/station/getGmsStationLimit','StationRuleController@getGmsStationLimit');
});

//移动端接口
Route::group(['namespace'=>'Admin','prefix'=>'mt','middleware'=>['mobileTerminal']],function () {
    Route::get('/menu/checkUserPermission','IndexController@checkUserPermission');
});
Route::group(['namespace'=>'Ajax','prefix'=>'mt','middleware'=>['mobileTerminal']],function () {
    Route::post('/station/stationNameFuzzySearch', 'StationMTController@stationNameFuzzySearch');
    Route::post('/station/getStationPaginate', 'StationMTController@getStationPaginate');
    Route::post('/station/getPrice', 'PriceController@getPrice');
    Route::post('/station/supplierPriceUpOrCreate', 'PriceController@supplierPriceUpdateOrCreate')->middleware('writeLog');
    Route::post('/station/platformPriceUpOrCreate', 'PriceController@platformPriceUpdateOrCreate')->middleware('writeLog');
    Route::post('/station/batchOffShelves', 'StationController@batchOffShelves')->middleware('writeLog');
    Route::post('/station/batchShelves', 'StationController@batchShelves')->middleware('writeLog');
    Route::post('/station/batchForeverOffline', 'StationController@batchForeverOffline')->middleware('writeLog');
    Route::post('/station/getStationStatusOperationReasonList', 'StationController@getStationStatusOperationReasonList');
    Route::post('/station/getOrgLimitList','StationRuleController@getOrgLimitList');

    Route::post('/workerOrder/pass','WorkerOrderController@pass');
    Route::post('/workerOrder/refuse','WorkerOrderController@refuse');

    Route::post('/station/uploadImg','StationCommonController@uploadImg');
    Route::post('/price/getMtPriceApprovePaginate', 'WorkerOrderController@getMtPriceApprovePaginate');

});

/*
|--------------------------------------------------------------------------
| No Middleware Routes
|--------------------------------------------------------------------------
|
| 非中间件路由
|
*/
Route::group(['namespace'=>'Ajax','prefix'=>'v1'],function (){
    Route::post('/log/createLog','LogController@createLog');
    Route::post('/station/getEditPricePcodes','StationCommonController@getEditPricePcodes'); // 进价可编辑的运营商编码
    Route::post('/station/checkRule','StationFilterController@checkRule');
    Route::post('/station/getAvailableOil','StationFilterController@getAvailableOil');
    Route::post('/station/getOilListAndPrice','StationFilterController@getOilListAndPrice');
    Route::post('/station/getOilListAndPriceStationList','StationFilterController@getOilListAndPriceStationList');
    Route::post('/station/getStationSpecialPriceForOrg','StationController@getStationSpecialPriceForOrg');

    Route::get('/gasUser/wxCheckToken','GasUserController@wxCheckSignature');
    Route::get('/gasUser/wxAccessToken','GasUserController@wxAccessToken');
    Route::get('/gasUser/wxJSSDKSignature','GasUserController@wxJSSDKSignature');
    Route::post('/gasUser/login', 'LoginController@gasUserLogin');
    //token登录
    Route::any('/gasUser/tokenLogin', 'LoginController@gasUserTokenLogin');
    Route::post('/gasUser/getGasUserInfoById', 'GasUserController@getGasUserEditInfoById');

    // 获取对客户开放站点
    Route::post('/station/getCustomerStationPriceList', 'StationFilterController@getCustomerStationPriceList');
});


/*
|--------------------------------------------------------------------------
| GasUser Middleware Routes
|--------------------------------------------------------------------------
|
| 加油员中间件路由
|
*/
Route::group(['prefix' => 'v1', 'namespace' => 'Ajax', 'middleware' => ['gasUser']], function (){
    // 获取用户基本信息
    Route::post('/gasUser/editPassword', 'GasUserController@editPassword');
    Route::post('/gasUser/bindGasUserWx', 'GasUserController@bindGasUserWx');
    Route::post('/gasUser/unbindGasUserWx', 'GasUserController@unbindGasUserWx');
    Route::post('/gasUser/broadcastVoice', 'GasUserController@broadcastVoice');
    Route::post('/gasUser/loginOut', 'LoginController@gasUserLoginOut');
    Route::post('/gasUser/uploadImg','StationCommonController@uploadImg');

    //获取用户信息
    Route::post('/gasUser/getUserInfo','GasUserController@getGasUserEditInfoById');

    // PDA-H5加油员站点价格列表
    Route::post('/station/getStationPricePaginate', 'StationController@getStationPricePaginate');
    // PDA-H5加油员站点价格详情
    Route::post('/price/getDoperStationPrice', 'PriceController@getDoperStationPrice');
    // PDA-H5加油员创建变价申请
    Route::post('/price/createChangePriceApprove', 'WorkerOrderController@createChangePriceApprove');
    // PDA-H5加油员变价申请列表
    Route::post('/price/getPdaPriceApprovePaginate', 'WorkerOrderController@getPdaPriceApprovePaginate');

    //PDA 获取关联账号列表
    Route::post('/gasUser/getUserRelation','GasUserController@getGasUserRelation');
    //PDA 添加关联账号
    Route::post('/gasUser/addUserRelation','GasUserController@addUserRelation');
    //PDA 删除关联账号
    Route::post('/gasUser/delUserRelation','GasUserController@delUserRelation');
    //PDA 切换关联账号
    Route::post('/gasUser/switchUserRelation','GasUserController@switchUserRelation');
});
