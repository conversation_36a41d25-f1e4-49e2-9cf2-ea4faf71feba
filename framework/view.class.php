<?php
/**
 * 模板基类。
 * 
 */
class View extends Blitz {
    public $moduleName;

    /**
     * 生成某一个模块某个方法的链接。
     *
     * @param   string  $moduleName    模块名，为空默认当前模块。
     * @param   string  $methodName    方法名。
     * @param   mixed   $vars          要传递的参数，可以是数组，array('var1'=>'value1')。也可以是var1=value1&var2=value2的形式。
     * @param   string  $viewType      视图格式。
     * @access  public
     * @return  string
     */
    function url($moduleName = null, $methodName = 'index', $vars = array(), $viewType = '') {
        if(empty($moduleName)) $moduleName = $this->moduleName;
        return helper::createLink($moduleName, $methodName, $vars, $viewType);
    }

    function link($roles = null, $moduleName = null, $methodName = 'index', $vars = array(), $viewType = '') {

    }

    function button($roles = null, $moduleName = null, $methodName = 'index', $vars = array(), $viewType = '') {

    }
    
    /**
     * 存入变量
     * @param <type> $name 变量名
     * @param <type> $content 值
     */
    function f($name, $content) {
		$this->setGlobals(array($name => $content));
	}

    /**
     * 取对象的属性值
     * @param <type> $object 对象
     * @param <type> $property 属性名
     */
    function p($object, $property) {
        if (is_object($object)) {
            $value = $object->$property;
        } elseif (is_array($object)) {
            $value = $object[$property];
        } else {
            $value = $object;
        }
        return $value;
    }

    function lang($key) {
        global $lang;
        return $lang->$key;
    }

	function setObjects($stack) {
		return $this->set($this->object2array($stack));
	}

	function object2array($result) {
    	$array = array();
		foreach ($result as $key=>$value) {
			if (is_object($value)) {
				$array[$key]=$this->object2array($value);
			} elseif (is_array($value)) {
				$array[$key]=$this->object2array($value);
			} else {
				$array[$key]=$value;
			}
		}
    	return $array;
	}
}