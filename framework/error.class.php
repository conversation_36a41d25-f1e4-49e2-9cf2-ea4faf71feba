<?php
/**
 * 错误处理类，自己处理所有的PHP错误
 */
error_reporting(E_ERROR | E_WARNING | E_PARSE);
set_error_handler('errorHandler');

/**
 * php 错误句柄
 * @param $errno
 * @param $errstr
 * @param $errfile
 * @param $errline
 * @param string $errcontext
 * @throws ErrorException
 */
function errorHandler($errno, $errstr, $errfile, $errline, $errcontext = '')
{
    Framework\Log::error('CODE:' .var_export($errstr,true),[], "ERROR");

    $errors = "Unknown";
    switch ($errno) {
        case E_NOTICE :
        case E_USER_NOTICE :
            $errors = "Notice";
            break;
        case E_WARNING :
        case E_USER_WARNING :
            $errors = "Warning";
            break;
        case E_PARSE:
            $errors = "Parse Error";
            break;
        case E_ERROR :
        case E_USER_ERROR :
            $errors = "Fatal Error";
            break;
        default :
            $errors = $errors .' 错误编号：'.$errno;
            break;
    }
    $message = sprintf("PHP %s:  %s in %s on line %d", $errors, $errstr, $errfile, $errline);
    Framework\Log::error('CODE:' .$message,[], "ERROR");

    var_dump($errstr);


//    if (class_exists('\Framework\Log')) {
//        \Framework\Log::error($message, ['errors'  => $errors, 'errorFile' => $errfile, 'errorLine' => $errline,'request' => $_REQUEST], 'PHP_ERROR');
//    } else {
//        $params = var_export($_REQUEST, TRUE);
//        \helper::datalog($message . var_export($params, TRUE), 'PHP_ERROR');
//    }

//    if ($errno == E_NOTICE || $errno == E_USER_NOTICE) {
//    }
    if ($errno != E_NOTICE && stripos($errors, 'Unknown') === FALSE) {
        throw new ErrorException($errors.':'.$errstr, 1, $errno, $errfile, $errline);
    }else{
        return;
    }
}