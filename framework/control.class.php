<?php
/**
 * The control class file of ZenTaoPHP.
 *
 * ZenTaoPHP is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.

 * ZenTaoPHP is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 * 
 * You should have received a copy of the GNU Lesser General Public License
 * along with ZenTaoPHP.  If not, see <http://www.gnu.org/licenses/>.
 *
 * @copyright   Copyright 2009-2010 青岛易软天创网络科技有限公司(www.cnezsoft.com)
 * <AUTHOR> <<EMAIL>>
 * @package     ZenTaoPHP
 * @version     $Id: control.class.php 127 2010-07-04 02:18:40Z wwccss $
 * @link        http://www.zentaoms.com
 */

/**
 * 控制器基类。
 * 
 * @package ZenTaoPHP
 */
class control
{
    /**
     * 全局的$app对象。
     * 
     * @var object
     * @access protected
     */
    protected $app;
    public $smarty;

    /**
     * 全局的$config对象。 
     * 
     * @var object
     * @access protected
     */
    protected $config;

    /**
     * 全局的$lang对象。
     * 
     * @var object
     * @access protected
     */
    protected $lang;

    /**
     * 全局的$dbh（数据库访问句柄）对象。
     * 
     * @var object
     * @access protected
     */
    public $dbh;

    /**
     * dao对象。
     * 
     * @var object
     * @access protected
     */
    public $dao;

    /**
     * POST对象。
     * 
     * @var ojbect
     * @access public
     */
    public $post;

    /**
     * get对象。
     * 
     * @var ojbect
     * @access public
     */
    public $get;

    /**
     * session对象。
     * 
     * @var ojbect
     * @access public
     */
    public $session;

    /**
     * server对象。
     * 
     * @var ojbect
     * @access public
     */
    public $server;

    /**
     * cookie对象。
     * 
     * @var ojbect
     * @access public
     */
    public $cookie;

    /**
     * global对象。
     * 
     * @var ojbect
     * @access public
     */
    public $global;

    /**
     * 所属模块的名字。
     * 
     * @var string
     * @access protected
     */
    public $moduleName;

    /**
     * @var
     */
    public $methodName;

    /**
     * 记录赋值到view的所有变量/模板引擎对象。
     * 
     * @var object
     * @access public
     */
    public $view; 

    /**
     * 视图类型
     * 
     * @var string
     * @access private
     */
    private $viewType;

    /**
     * 要输出的内容。
     * 
     * @var string
     * @access private
     */
    private $output;

    /**
     * 路径分隔符。
     * 
     * @var string
     * @access protected
     */
    protected $pathFix;
    
    /**
    * 实例化的对象Model
    */
    public $modelobj = array();

    /**
     * 构造函数：
     *
     * 1. 引用全局对象，使之可以通过成员变量访问。
     * 2. 设置模块相应的路径信息，并加载对应的model文件。
     * 3. 自动将$lang和$config赋值到模板。
     * 
     * @access public
     * @return void
     */
    public function __construct($moduleName = '', $methodName = '')
    {
        /* 引用全局对象，并赋值。*/
        global $app, $config, $lang, $dbh;
        $this->app        = $app;
        $this->config     = $config;
        $this->lang       = $lang;
        $this->dbh        = $dbh;
        $this->pathFix    = $this->app->getPathFix();
        $this->viewType   = $this->app->getViewType();
        $this->setModuleName($moduleName);
        $this->setMethodName($methodName);

        /* 自动加载当前模块的model文件。*/
        $this->loadModel();

        /* 设置当前模块的配置、语言等信息，并加载相应的文件 */
        $this->app->loadLang($this->moduleName,   $exit = false);
        $this->app->loadConfig($this->moduleName, $exit = false);

        /* 使用模板引擎，创建对象 */
		if ($this->viewType == 'blz') {
			$this->view	= new View();
            $this->view->moduleName = $this->moduleName;
        } else {
            $this->view = new stdClass();
            /* 自动将$app、$config、$theme和$lang赋值到模板中。*/
            $this->assign('app', $app);
            $this->assign('lang', $lang);
            $this->assign('config', $config);

            if(isset($config->super2OBJ) and $config->super2OBJ) $this->setSuperVars();
        }
//        $this->assign('theme', $app->getClientTheme());
    }

    //-------------------- model相关的方法。--------------------//
    //
    /* 设置模块名。*/
    private function setModuleName($moduleName = '')
    {
        $this->moduleName = $moduleName ? strtolower($moduleName) : $this->app->getModuleName();
    }

    /* 设置方法名。*/
    private function setMethodName($methodName = '')
    {
        $this->methodName = $methodName ? strtolower($methodName) : $this->app->getMethodName();
    }

    /**
     * 加载某一个模块的model文件。
     * 防止model中重复实例出现冲突bug 同时fixed bug#2976 @2013-08-27
     * @param   string  $moduleName     模块名字，如果为空，则取当前的模块名作为model名。
     * @access  public
     * @return  void
     */    
    public function loadModel($moduleName = '') {
        /* 如果没有指定module名，则取当前加载的模块的名作为model名。*/
        if(empty($moduleName)) $moduleName = $this->moduleName;
        
        if(isset($this->modelobj[$moduleName]) && $this->modelobj[$moduleName]){
        	return $this->modelobj[$moduleName];
        }
        
        $classNameExt = $this->app->getModuleExt() . $moduleName. 'model';
        $modelClass = class_exists($classNameExt) ? $classNameExt : $moduleName . 'model';
        if(!class_exists($modelClass)) $this->app->error(" The model $modelClass not found", __FILE__, __LINE__, $exit = true);

        $this->modelobj[$moduleName] = $this->$moduleName = new $modelClass();
        return $this->$moduleName;
    }

    /**
     * 设置超全局变量。
     * 
     * @access protected
     * @return void
     */
    protected function setSuperVars()
    {
        $this->post    = $this->app->post;
        $this->get     = $this->app->get;
        $this->server  = $this->app->server;
        $this->session = $this->app->session;
        $this->cookie  = $this->app->cookie;
        $this->global  = $this->app->global;
    }

    //-------------------- 加载view相关的方法。--------------------//
    /**
     * 设置视图文件。
     * 
     * 某一个module的控制器可以加载另外一个module的视图文件。
     *
     * @param string $moduleName    模块名。
     * @param string $methodName    方法名。
     * @access private
     * @return string               对应的视图文件。
     */
    private function setViewFile($moduleName, $methodName)
    {
        $moduleName = strtolower(trim($moduleName));
        $methodName = strtolower(trim($methodName));

        $modulePath  = $this->app->getModulePath($moduleName);
        $viewExtPath = $this->app->getModuleExtPath($moduleName, 'view');

        /* 主视图文件，扩展视图文件和扩展钩子文件。*/
        $mainViewFile = $modulePath . 'view' . $this->pathFix . $methodName . '.' . $this->viewType . '.php';
        $extViewFile  = $viewExtPath . $methodName . ".{$this->viewType}.php";
        $extHookFile  = $viewExtPath . $methodName . ".{$this->viewType}.hook.php";

        $viewFile = file_exists($extViewFile) ? $extViewFile : $mainViewFile;
        if(!file_exists($viewFile)) $this->app->error("the view file $viewFile not found", __FILE__, __LINE__, $exit = true);
        if(file_exists($extHookFile)) return array('viewFile' => $viewFile, 'hookFile' => $extHookFile);
        return $viewFile;
    }
    
	/**
     * 设置HTML2视图文件。
     * 
     * 某一个module的控制器可以加载另外一个module的视图文件。
     *
     * @param string $moduleName    模块名。
     * @param string $methodName    方法名。
     * @access private
     * @return string               对应的视图文件。
     */
    private function setViewFileHTML2($moduleName, $methodName)
    {
        $moduleName = strtolower(trim($moduleName));
        $methodName = strtolower(trim($methodName));
		$appRoot = $this->app->getappRoot();
        $modulePath  = $this->app->getModulePath($moduleName);
        $viewExtPath = $this->app->getModuleExtPath($moduleName, 'view');

        /* 主视图文件，扩展视图文件和扩展钩子文件。*/
        $mainViewFile = $appRoot . 'www'.DIRECTORY_SEPARATOR.'view'.DIRECTORY_SEPARATOR. $moduleName.DIRECTORY_SEPARATOR.$methodName. '.html.php';
        $extViewFile  = $viewExtPath . $methodName . ".{$this->viewType}.php";
        $extHookFile  = $viewExtPath . $methodName . ".{$this->viewType}.hook.php";

        $headerViewFile = $appRoot . 'www'.DIRECTORY_SEPARATOR.'view'.DIRECTORY_SEPARATOR.'header.html.php';
        $footerViewFile = $appRoot . 'www'.DIRECTORY_SEPARATOR.'view'.DIRECTORY_SEPARATOR.'footer.html.php';
        
        $viewFile = file_exists($extViewFile) ? $extViewFile : $mainViewFile;
        if(!file_exists($viewFile)) $this->app->error("the view file $viewFile not found", __FILE__, __LINE__, $exit = true);
        if(file_exists($extHookFile)) return array('viewFile' => $viewFile, 'hookFile' => $extHookFile);
        if($moduleName == 'login' && $methodName == 'index'){
        	return array('viewFile' => $viewFile);
        }else{
        	return array('headerFile'=>$headerViewFile,'viewFile' => $viewFile,'footerFile'=>$footerViewFile);
        }
    }

    /**
     * 赋值一个变量到view视图。
     * 
     * @param   string  $name       赋值到视图文件中的变量名。
     * @param   mixed   $value      所对应的值。
     * @access  public
     * @return  void
     */
    public function assign($name, $value)
    {
		if ($this->viewType == 'blz')
			$this->view->setObjects(array($name => $value));
		else
			$this->view->$name = $value;
    }
    
    /**
     * 重置output内容。
     * 
     * @access public
     * @return void
     */
    public function clear()
    {
        $this->output = '';
    }

    /**
     * 解析视图文件。
     *
     * 如果没有指定模块名和方法名，则取当前模块的当前方法。
     *
     * @param string $moduleName    模块名。
     * @param string $methodName    方法名。
     * @access public
     * @return void
     */
    public function parse($moduleName = '', $methodName = '')
    {
        if(empty($moduleName)) $moduleName = $this->moduleName;
        if(empty($methodName)) $methodName = $this->methodName;

        if($this->viewType == 'json') {
            $this->parseJSON($moduleName, $methodName);
        } elseif ($this->viewType == 'jsonc') {
            $this->parseJSON($moduleName, $methodName,false);
        } elseif ($this->viewType == 'jsonh') {
            $this->parseJSONH($moduleName, $methodName);
        } elseif ($this->viewType == 'blz') {
            $this->parseBlitz($moduleName, $methodName);
        } elseif ($this->viewType == 'soap') {
            $this->parseSOAP($moduleName, $methodName);
		}elseif($this->viewType == 'html2'){
			$this->parseHTML2($moduleName, $methodName);
		}else {
            $this->parseDefault($moduleName, $methodName);
        }
        return $this->output;
    }

    private function parseSOAP($moduleName, $methodName) {
    	unset($this->view->app);
        unset($this->view->config);
        unset($this->view->lang);
        unset($this->view->pager);
        unset($this->view->header);
        unset($this->view->position);
        unset($this->view->moduleTree);
        $ps = get_object_vars($this->view);
		if (count($ps) == 1)
			$this->soapdata = array_pop($ps);
		else
			$this->soapdata = $this->view;

    }
    
    /* 解析JSON格式的输出。*/
    private function parseJSON($moduleName, $methodName,$flag=TRUE) {
//        header("Content-Type:	application/json");
    	unset($this->view->app);
        unset($this->view->config);
        unset($this->view->lang);
        unset($this->view->pager);
        unset($this->view->header);
        unset($this->view->position);
        unset($this->view->moduleTree);
//        unset($this->view->theme);

        $output['code'] = is_object($this->view) ? 0 : 2;
		$ps = get_object_vars($this->view);
		if (count($ps) == 1)
			$output['data'] = array_pop($ps);
		else
			$output['data'] = $this->view;
//        $output['md5'] = md5(json_encode($this->view));
		unset($ps, $this->view);
		if(TRUE===$flag) {
        	$this->output = @json_encode($output);
        }else {
        	$this->output = helper::customJsonEncode($output);
        }
    }

    /* 解析JSONH格式的输出。*/
    private function parseJSONH($moduleName, $methodName) {
        header("Content-Type:	application/json");
    	unset($this->view->app);
        unset($this->view->config);
        unset($this->view->lang);
        unset($this->view->pager);
        unset($this->view->header);
        unset($this->view->position);
        unset($this->view->moduleTree);

        $output['code'] = is_object($this->view) ? 0 : 2;
		$ps = get_object_vars($this->view);
		if (count($ps) == 1) {
			$data = array_pop($ps);
			if (is_array($data) && count($data) > 0 && (is_array($data[0]) || is_object($data[0]))) {
				// 用jsonh压缩数组
				include_once $this->app->getCoreLibRoot() . 'JSONH.class.php';
				$output['jsonh'] = 1;
				$data = JSONH::pack($data);
			} elseif ($data instanceof Page) {
				// 用jsonh压缩数组
				include_once $this->app->getCoreLibRoot() . 'JSONH.class.php';
				$data->jsonh = 1;
				$data->result = JSONH::pack($data->result);
			}
			$output['data'] = $data;
		} else
			$output['data'] = $this->view; 
		unset($ps, $this->view);
		
		$this->output = json_encode($output);
    }
    
    /* Blitz template的输出。*/
    private function parseBlitz($moduleName, $methodName)
    {
        /* 设置视图文件。*/
        $viewFile = $this->setViewFile($moduleName, $methodName);
        if(is_array($viewFile)) extract($viewFile);

        /* 切换到视图文件所在的目录，以保证视图文件中的包含路径有效。*/
        $currentPWD = getcwd();
        chdir(dirname($viewFile));
		
		$this->view->load('{{ include("'.basename($viewFile).'") }}');
		$this->output .= $this->view->parse();
//
//        if(isset($hookFile)) {
//            ob_start();
//            include $hookFile;
//            $this->output .= ob_get_contents();
//            ob_end_clean();
//        }

        /* 最后还要切换到原来的目录。*/
        chdir($currentPWD);
    }

    /* 默认的输出。*/
    private function parseDefault($moduleName, $methodName)
    {
        /* 设置视图文件。*/
        $viewFile = $this->setViewFile($moduleName, $methodName);
        if(is_array($viewFile)) extract($viewFile);

        /* 切换到视图文件所在的目录，以保证视图文件中的包含路径有效。*/
        $currentPWD = getcwd();
        chdir(dirname($viewFile));

        extract((array)$this->view);
        ob_start();
        include $viewFile;
//        if(isset($hookFile)) include $hookFile;
        $this->output .= ob_get_contents();
        ob_end_clean();

        /* 最后还要切换到原来的目录。*/
        chdir($currentPWD);
    }
    
	/* HTML2的输出。*/
    private function parseHTML2($moduleName, $methodName)
    {
        /* 设置视图文件。*/
        $viewFile = $this->setViewFileHTML2($moduleName, $methodName);
        if(is_array($viewFile)) extract($viewFile);
        
        /* 切换到视图文件所在的目录，以保证视图文件中的包含路径有效。*/
        $currentPWD = getcwd();
        chdir(dirname($viewFile));

        extract((array)$this->view);
        ob_start();
        if(isset($headerFile)) include $headerFile;
        include $viewFile;
        if(isset($footerFile)) include $footerFile;
//        if(isset($hookFile)) include $hookFile;
        $this->output .= ob_get_contents();
        ob_end_clean();

        /* 最后还要切换到原来的目录。*/
        chdir($currentPWD);
    }

    /**
     * 获取某一个模块的某一个方法的内容。
     * 
     * 如果没有指定模块名，则取当前模块当前方法的视图。如果指定了模块和方法，则调用对应的模块方法的视图内容。
     *
     * @param   string  $moduleName    模块名。
     * @param   string  $methodName    方法名。
     * @param   array   $params        方法参数。
     * @access  public
     * @return  string
     */
    public function fetch($moduleName = '', $methodName = '', $params = array())
    {
        if($moduleName == '') $moduleName = $this->moduleName;
        if($methodName == '') $methodName = $this->methodName;
        if($moduleName == $this->moduleName and $methodName == $this->methodName) 
        {
            $this->parse($moduleName, $methodName);
            return $this->output;
        }

        /* 设置被调用的模块的路径及相应的文件。*/
        $modulePath        = $this->app->getModulePath($moduleName);
        $moduleControlFile = $modulePath . 'control.php';
        $actionExtFile     = $this->app->getModuleExtPath($moduleName, 'control') . strtolower($methodName) . '.php';
        $file2Included     = file_exists($actionExtFile) ? $actionExtFile : $moduleControlFile;

        /* 加载控制文件。*/
        if(!file_exists($file2Included)) $this->app->error("The control file $file2Included not found", __FILE__, __LINE__, $exit = true);
        $currentPWD = getcwd();
        chdir(dirname($file2Included));
        if($moduleName != $this->moduleName) helper::import($file2Included);
        
        /* 设置要调用的类的名称。*/
        $className = class_exists("ext$moduleName") ? "ext$moduleName" : $moduleName;
        if(!class_exists($className)) $this->app->error(" The class $className not found", __FILE__, __LINE__, $exit = true);

        /* 处理参数，生成对象。*/
        if(!is_array($params)) parse_str($params, $params);
        $module = new $className($moduleName, $methodName);        
        
        /* 调用方法，获得输出。*/
        
        if($params['return'])
        	$output = call_user_func_array(array($module, $methodName), $params);
        else 
        {
        	ob_start();
	        $output = call_user_func_array(array($module, $methodName), $params);
	        $output = ob_get_contents();
	        ob_end_clean();
        }
        unset($module);
        chdir($currentPWD);
        return $output;
    }

    /**
     * 显示视图内容。 
     * 
     * @param   string  $moduleName    模块名。
     * @param   string  $methodName    方法名。
     * @access  public
     * @return  void
     */
    public function display($moduleName = '', $methodName = '')
    {
        if(empty($this->output)) 
        	$this->parse($moduleName, $methodName);
        if(!empty($this->output)) 
        	echo $this->output;
        //if($this->viewType == 'json') die();
    }

    /**
     * 直接输出文本数据
     * @param <type> $content 传入数组/对象，则序列化成JSON输出
     */
    public function render($content) {
        if (is_array($content) || is_object($content))
            echo json_encode($content);
        else
            echo $content;
        //die();
    }

    /**
     * 生成某一个模块某个方法的链接。
     * 
     * @param   string  $moduleName    模块名。
     * @param   string  $methodName    方法名。
     * @param   mixed   $vars          要传递的参数，可以是数组，array('var1'=>'value1')。也可以是var1=value1&var2=value2的形式。
     * @param   string  $viewType      视图格式。
     * @access  public
     * @return  string
     */
    public function createLink($moduleName = null, $methodName = 'index', $vars = array(), $viewType = '')
    {
        if(empty($moduleName)) $moduleName = $this->moduleName;
        return helper::createLink($moduleName, $methodName, $vars, $viewType);
    }

    /**
     * 生成对本模块某个方法的链接。
     * 
     * @param   string  $methodName    方法名。
     * @param   mixed   $vars          要传递的参数，可以是数组，array('var1'=>'value1')。也可以是var1=value1&var2=value2的形式。
     * @param   string  $viewType      视图格式。
     * @access  public
     * @return  string
     */
    public function inlink($methodName = 'index', $vars = array(), $viewType = '')
    {
        return helper::createLink($this->moduleName, $methodName, $vars, $viewType);
    }

    /**
     * 跳转到另外一个页面。
     * 
     * @param   string   $url   要跳转的url地址。
     * @access  public
     * @return  void
     */
    public function locate($url)
    {
        header("location: $url");
        exit;
    }
}
