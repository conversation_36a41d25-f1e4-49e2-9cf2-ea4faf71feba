<?php
/**
 * The helper class file of ZenTaoPHP.
 *
 * ZenTaoPHP is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * ZenTaoPHP is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with ZenTaoPHP.  If not, see <http://www.gnu.org/licenses/>.
 *
 * @copyright   Copyright 2009-2010 青岛易软天创网络科技有限公司(www.cnezsoft.com)
 * <AUTHOR> <<EMAIL>>
 * @package     ZenTaoPHP
 * @version     $Id: helper.class.php 127 2010-07-04 02:18:40Z wwccss $
 * @link        http://www.zentaoms.com
 */

/**
 * 工具类对象，存放着各种杂项的工具方法。
 *
 * @package ZenTaoPHP
 */

use Nette\Mail\Message;
use Framework\Mailer\MailSender;
use Framework\XssFilter\XssFilter;
use Framework\DingTalk\DingTalkAlarm;

class helper
{
    /**
     * 为一个对象设置某一个属性，其中key可以是“father.child”的形式。
     *
     * <code>
     * <?php
     * $lang->db->user = 'wwccss';
     * helper::setMember('lang', 'db.user', 'chunsheng.wang');
     * ?>
     * </code>
     * @param string $objName 对象变量名。
     * @param string $key 要设置的属性，可以是father.child的形式。
     * @param mixed $value 要设置的值。
     * @static
     * @access public
     * @return void
     */
    static public function setMember($objName, $key, $value)
    {
        global $$objName;
        if (!is_object($$objName) or empty($key)) return FALSE;
        $key = str_replace('.', '->', $key);
        $value = serialize($value);
        $code = ("\$${objName}->{$key}=unserialize(<<<EOT\n$value\nEOT\n);");
        eval($code);
    }
    
    /**
     * 生成某一个模块某个方法的链接。
     *
     * 在control类中对此方法进行了封装，可以在control对象中直接调用createLink方法。
     * <code>
     * <?php
     * helper::createLink('hello', 'index', 'var1=value1&var2=value2');
     * helper::createLink('hello', 'index', array('var1' => 'value1', 'var2' => 'value2');
     * ?>
     * </code>
     * @param string $moduleName 模块名。
     * @param string $methodName 方法名。
     * @param mixed $vars 要传递给method方法的各个参数，可以是数组，也可以是var1=value2&var2=value2的形式。
     * @param string $viewType 扩展名方式。
     * @static
     * @access public
     * @return string
     */
    static public function createLink($moduleName, $methodName = 'index', $vars = '', $viewType = '')
    {
        global $app, $config;
        
        $link = $config->webRoot;
        if ($config->requestType == 'GET') {
            if (strpos($_SERVER['SCRIPT_NAME'], 'index.php') === FALSE) {
                $link = $_SERVER['SCRIPT_NAME'];
            }
        }
        
        if (empty($viewType)) $viewType = $app->getViewType();
        
        /* 如果传递进来的vars不是数组，尝试将其解析成数组格式。*/
        if (!is_array($vars)) parse_str($vars, $vars);
        if ($config->requestType == 'PATH_INFO') {
            $link .= "$moduleName{$config->requestFix}$methodName";
            if ($config->pathType == 'full') {
                foreach ($vars as $key => $value) $link .= "{$config->requestFix}$key{$config->requestFix}$value";
            } else {
                foreach ($vars as $value) $link .= "{$config->requestFix}$value";
            }
            /* 如果访问的是/index/index.html，简化为/index.html。*/
            if ($moduleName == $config->default->module and $methodName == $config->default->method) $link = $config->webRoot . 'index';
            $link .= '.' . $viewType;
        } elseif ($config->requestType == 'GET') {
            $link .= "?{$config->moduleVar}=$moduleName&{$config->methodVar}=$methodName";
            if ($viewType != 'html') $link .= "&{$config->viewVar}=" . $viewType;
            foreach ($vars as $key => $value) $link .= "&$key=$value";
        }
        
        return $link;
    }
    
    //判断roleid = 22 (总办服务人员 可查看该角色 所在城市/区所有信息) - 仅佳吉网上营业厅model,control交叉调用
    static public function CNEX_getBycityid($roleid = 22, $prefix = "")
    {
        global $app;
        $sroleid = $app->user->roleid;
        $marea_tmp = $app->user->marea;
        if (!empty($marea_tmp)) {
            $marea_arr = helper::array_remove_empty(explode(",", $marea_tmp));
            if (count($marea_arr) > 0) {
                $marea = implode(",", $marea_arr);
                if ($sroleid == $roleid) {
                    return " and " . $prefix . "shippercityid in(" . $marea . ")";
                } else {
                    return "";
                }
            }
        }
        
    }
    
    //判断roleid = 23 (总办服务人员 可查看该角色 所在城市/区所有信息) - 仅佳吉网上营业厅model,control交叉调用
    static public function CNEX_getBystore($roleid = 23, $prefix = "")
    {
        global $app;
        $sroleid = $app->user->roleid;
        $marea_tmp = $app->user->msite;
        if (!empty($marea_tmp)) {
            $msite_arr = helper::array_remove_empty(explode(",", $marea_tmp));
            if (count($msite_arr) > 0) {
                $msite = implode(",", $msite_arr);
                if ($sroleid == $roleid) {
                    return " and " . $prefix . "shipperstoreid in(" . $msite . ")";
                } else {
                    return "";
                }
            }
        }
        
    }
    
    /**
     * 将一个数组转成对象格式。此函数只是返回语句，需要eval。
     *
     * <code>
     * <?php
     * $config['user'] = 'wwccss';
     * eval(helper::array2Object($config, 'configobj');
     * print_r($configobj);
     * ?>
     * </code>
     * @param array $array 要转换的数组。
     * @param string $objName 要转换成的对象的名字。
     * @param string $memberPath 成员变量路径，最开始为空，从根开始。
     * @param bool $firstRun 是否是第一次运行。
     * @static
     * @access public
     * @return void
     */
    static public function array2Object($array, $objName, $memberPath = '', $firstRun = TRUE)
    {
        if ($firstRun) {
            if (!is_array($array) or empty($array)) return FALSE;
        }
        static $code = '';
        $keys = array_keys($array);
        foreach ($keys as $keyNO => $key) {
            $value = $array[$key];
            if (is_int($key)) $key = 'item' . $key;
            $memberID = $memberPath . '->' . $key;
            if (!is_array($value)) {
                $value = addslashes($value);
                $code .= "\$$objName$memberID='$value';\n";
            } else {
                helper::array2object($value, $objName, $memberID, $firstRun = FALSE);
            }
        }
        
        return $code;
    }
    
    /**
     * 对象转数组
     * @param object $e 待转对象
     * @return array
     */
    static public function objectToArray($obj){
        $_arr = is_object($obj) ? get_object_vars($obj) : $obj;
        $arr = array();
        if($_arr){
            foreach ($_arr as $key => $val)
            {
                $val = (is_array($val) || is_object($val)) ? self::objectToArray($val) : $val;
                $arr[$key] = $val;
            }
        }
        return $arr;
    }
    
    /**
     * 包含一个文件。router.class.php和control.class.php中包含文件都通过此函数来调用，这样保证文件不会重复加载。
     *
     * @param string $file 要包含的文件的路径。
     * @static
     * @access public
     * @return void
     */
    static public function import($file)
    {
        if (!file_exists($file)) return FALSE;
        static $includedFiles = [];
        if (!isset($includedFiles[$file])) {
            include $file;
            $includedFiles[$file] = TRUE;
            
            return TRUE;
        }
        
        return FALSE;
    }
    
    /**
     * 设置model文件。
     *
     * @param   string $moduleName 模块名字。
     * @access  private
     * @return void
     */
    static public function setModelFile($moduleName)
    {
        global $app;
        
        /* 设定主model文件和扩展路径，并获得所有的扩展文件。*/
        $mainModelFile = $app->getModulePath($moduleName) . 'model.php';
        $modelExtPath = $app->getModuleExtPath($moduleName, 'model');
        $extFiles = helper::ls($modelExtPath, '.php');
        
        /* 不存在扩展文件，返回主配置文件。*/
        if (empty($extFiles)) return $mainModelFile;
        
        /* 存在扩展文件，判断是否需要更新。*/
        $mergedModelFile = $app->getTmpRoot() . 'model' . $app->getPathFix() . $moduleName . '.php';
        $needUpdate = FALSE;
        $lastTime = file_exists($mergedModelFile) ? filemtime($mergedModelFile) : 0;
        
        if (filemtime($mainModelFile) > $lastTime) {
            $needUpdate = TRUE;
        } else {
            foreach ($extFiles as $extFile) {
                if (filemtime($extFile) > $lastTime) {
                    $needUpdate = TRUE;
                    break;
                }
            }
        }
        
        /* 如果不需要更新，则直接返回合并之后的model文件。*/
        if (!$needUpdate) return $mergedModelFile;
        
        if ($needUpdate) {
            /* 加载主的model文件，并获得其方法列表。*/
            helper::import($mainModelFile);
            $modelMethods = get_class_methods($moduleName . 'model');
            foreach ($modelMethods as $key => $modelMethod) $modelMethods[$key] = strtolower($modelMethod);
            
            /* 将主model文件读入数组。*/
            $modelLines = rtrim(file_get_contents($mainModelFile));
            $modelLines = rtrim($modelLines, '?>');
            $modelLines = rtrim($modelLines);
            $modelLines = explode("\n", $modelLines);
            $lines2Delete = [count($modelLines) - 1];
            $lines2Append = [];
            
            /* 循环处理每个扩展方法文件。*/
            foreach ($extFiles as $extFile) {
                $methodName = strtolower(basename($extFile, '.php'));
                if (in_array($methodName, $modelMethods)) {
                    $method = new ReflectionMethod($moduleName . 'model', $methodName);
                    $startLine = $method->getStartLine() - 1;
                    $endLine = $method->getEndLine() - 1;
                    $lines2Delete = array_merge($lines2Delete, range($startLine, $endLine));
                }
                $lines2Append = array_merge($lines2Append, explode("\n", trim(file_get_contents($extFile))));
            }
            
            /* 生成新的model文件。*/
            $lines2Append[] = '}';
            foreach ($lines2Delete as $lineNO) unset($modelLines[$lineNO]);
            $modelLines = array_merge($modelLines, $lines2Append);
            if (!is_dir(dirname($mergedModelFile))) mkdir(dirname($mergedModelFile));
            $modelLines = join("\n", $modelLines);
            $modelLines = str_ireplace($moduleName . 'model', 'ext' . $moduleName . 'model', $modelLines); // 类名修改。
            file_put_contents($mergedModelFile, $modelLines);
            
            return $mergedModelFile;
        }
    }
    
    /**
     * 生成SQL查询中的IN(a,b,c)部分代码。
     *
     * @param   misc $ids id列表，可以是数组，也可以是使用逗号隔开的字符串。
     * @static
     * @access  public
     * @return  string
     */
    static public function dbIN($ids)
    {
        if (is_array($ids)) return "IN ('" . join("','", $ids) . "')";
        
        return "IN ('" . str_replace(',', "','", str_replace(' ', '', $ids)) . "')";
    }
    
    /**
     * 生成对框架安全的base64encode串。
     *
     * @param   string $string 要编码的字符串列表。
     * @static
     * @access  public
     * @return  string
     */
    static public function safe64Encode($string)
    {
        return strtr(base64_encode($string), '/', '.');
    }
    
    /**
     * 解码。
     *
     * @param   string $string 要解码的字符串列表。
     * @static
     * @access  public
     * @return  string
     */
    static public function safe64Decode($string)
    {
        return base64_decode(strtr($string, '.', '/'));
    }
    
    /**
     *  计算两个日期的差。
     *
     * @param   date $date1 第一个时间
     * @param   date $date2 第二个时间
     * @access  public
     * @return  string
     */
    static public function diffDate($date1, $date2)
    {
        return round((strtotime($date1) - strtotime($date2)) / 86400, 0);
    }
    
    /* 获得当前的时间。*/
    static public function now()
    {
        return date(DT_DATETIME1);
    }
    
    /* 获得今天的日期。*/
    static public function today()
    {
        return date(DT_DATE1);
    }
    
    /* 判断是否0000-00-00格式的日期。*/
    static public function isZeroDate($date)
    {
        return substr($date, 0, 4) == '0000';
    }
    
    /* 获得某一个目录下面含有某个特征字符串的所有文件。*/
    static public function ls($dir, $pattern = '')
    {
        $files = [];
        $dir = realpath($dir);
        if (is_dir($dir)) {
            $dh = opendir($dir);
            if ($dh) {
                while (($file = readdir($dh)) !== FALSE) {
                    if (strpos($file, $pattern) !== FALSE) $files[] = $dir . DIRECTORY_SEPARATOR . $file;
                }
                closedir($dh);
            }
        }
        
        return $files;
    }
    
    /* 切换目录。*/
    static function cd($path = '')
    {
        static $cwd = '';
        if ($path) {
            $cwd = getcwd();
            chdir($path);
        } else {
            chdir($cwd);
        }
    }
    
    /**
     * 根据FlexGrid提交的数据生成Page对象
     */
    static public function createPage()
    {
        $param = $_REQUEST;
        $page = new Page ();
        if (array_key_exists('page_no', $param)) {
            $page->pageNo = $param ['page_no'];
            $page->pageSize = $param ['page_size'];
        }
        if (array_key_exists('sortname', $param) && $param ['sortname'] != 'undefined') {
            $orderBy = $param ['sortname'];
            if ($param ['sortorder'] != 'undefined')
                $orderBy .= ' ' . $param ['sortorder'];
            $page->orderBy = $orderBy;
        }
        
        return $page;
    }
    
    /**
     * 根据提交的参数和model生成记录数据
     */
    static public function D($model)
    {
        $q = $_REQUEST;
        $result = [];
        $fields = $model->fields;
        foreach ($_REQUEST as $key => $value) {
            if (array_key_exists($key, $fields)) {
                $f = $fields [$key];
                // 类型转换
                if (in_array($f ['native_type'], ['INT24', 'LONG']))
                    $result[$key] = (int)$value;
                else
                    $result[$key] = $value;
            }
        }
        
        return $result;
    }
    
    /**
     * 快速文件数据读取和保存 针对简单类型数据 字符串、数组
     */
    static public function F($name, $value = '', $path = NULL)
    {
        global $app;
        // 取默认路径，缓存根目录/data
        if (empty($path))
            $path = $app->getCacheRoot();
        
        static $_cache = [];
        $filename = $path . $name . '.php';
        if ('' !== $value) {
            if (is_null($value)) {
                // 删除缓存
                return unlink($filename);
            } else {
                // 缓存数据
                $dir = dirname($filename);
                // 目录不存在则创建
                if (!is_dir($dir)) mkdir($dir);
                
                return file_put_contents($filename, "<?php\nreturn " . var_export($value, TRUE) . ";\n?>");
            }
        }
        if (isset($_cache[$name])) return $_cache[$name];
        // 获取缓存数据
        if (is_file($filename)) {
            $value = include $filename;
            $_cache[$name] = $value;
        } else {
            $value = FALSE;
        }
        
        return $value;
    }
    
    /**
     * 创建查询条件
     * 字段:fields=>id,title,nick,pic_url
     * 排序  order_by =>column/asc|desc(单个排序)
     * 前缀  $configtable =>array("name" =>"d")   (单个排序)
     * 查询条件
     */
    static public function createQuery($pararms, $operator = NULL, $configtable = NULL)
    {
        $actionQuery = new ActionQuery ();
        $actionQuery->fields = $pararms ['fields'];
        $order_by = $pararms ['order_by'];
        if ($order_by) {
            $orderby = explode("/", $order_by);
            $orderfield = $orderby [0];
            if ($configtable[$orderfield]) {
                $orderfield = $configtable[$orderfield] . "." . $orderfield;
            }
            $actionQuery->addOrderbys($orderfield, $orderby [1]);
        }
        unset ($pararms ['fields']);
        unset ($pararms ['order_by']);
        foreach ($pararms as $key => $value) {
            if ($configtable[$key]) {
                $field = $configtable[$key] . "." . $key;
            } else {
                $field = $key;
            }
            if ($operator [$key]) {
                if (preg_match("/>|<|<>/", $value)) {
                    $actionQuery->addCondition($field, $value);
                } else {
                    if ($operator[$key] == "like" && strpos($value, '%') === FALSE) {
                        $value = "%" . $value . "%";
                    } elseif ($operator[$key] == "like" && strpos($value, '%') !== FALSE)
                        $value = $value;
                    $actionQuery->addCondition($field, $value, $operator [$key]);
                }
            } else {
                $actionQuery->addCondition($field, $value);
            }
        }
        
        return $actionQuery;
    }
    
    /**
     * 将page转化为FlexGrid需要的数据
     * @param <type> $pk 主键属性名
     */
    static public function page2FlexGrid($page, $pk = "id")
    {
        $list = [];
        foreach ($page->result as $value)
            $list[] = ["id" => $value->$pk, "cell" => $value];
        $data = ["page" => $page->pageNo, "total" => $page->totalCount, "rows" => $list];
        
        return $data;
    }
    
    /**
     * 过滤参数(需要更好的方式)
     * $p=*, 表示过滤所有为空的$pararms,
     */
    static public function filterParams($p = NULL)
    {
        self::sqlScan();
        $pararms = array_merge($_POST, $_GET);
//        $pararms = XssFilter::getInstance()->execute($pararms);
        if (isset($pararms['start']) && $pararms['start'] && isset($pararms['limit']) && $pararms['limit']) {
            $pararms['page'] = intval($pararms['start']) / intval($pararms['limit']) + 1;
        }
        
        unset($pararms['m']);
        unset($pararms['f']);
        unset($pararms['t']);
//        unset($pararms['page_no']);;
//        unset($pararms['page_size']);
        unset($pararms['sortname']);
        unset($pararms['sortorder']);
        unset($pararms['query']);
        unset($pararms['qtype']);
        unset($pararms['qop']);
        unset($pararms['XDEBUG_SESSION_START']);
        unset($pararms['KEY']);
        foreach ($pararms as $key => &$value) {
            // 过滤html/script/css
            // $value = preg_replace('/<([^>]*)>/i', '&lt;${1}&gt;', $value);
            // // 防sql注入
//            $value = preg_replace("/([';]+|(--)+)/i", '', $value);
            //部标测试去sql关键词    liyonghua 2013-05-24
//            $value = str_replace(array('select','SELECT','delete','DELETE','INSERT','insert','update','UPDATE','FROM','from','iframe',
//            'group','GROUP','http://','HTTP://','https://','HTTPS://','<script>','<>','VBScript','src','href','SRC','HREF'), array(), $value);
            if ($p) {
                if (!is_array($value)) $value = trim($value);
                if ($value === NULL || $value === "" || $value == "undefined") {
                    unset($pararms[$key]);
                } elseif (strpos($key, "confilter_") > -1) {
                    $array_key = explode("_", $key);
                    $newkey = $array_key[1] . ' ' . $array_key[2] . ' ' . $array_key[3];
                    $pararms[$newkey] = $value;
                    unset($pararms[$key]);
                }
            }
        }
        
        return $pararms;
    }
    
    private static function sqlScan()
    {
        $getfilter = "`|' |make_set|\\*|<[^>]*?>|^\\+\/v(8|9)|\\b(and|or)\\b.+?(>|<|=|\\bin\\b|\\blike\\b)|\\/\\*.+?\\*\\/|<\\s*script\\b|\\bEXEC\\b|UNION.+?SELECT|UPDATE.+?SET|INSERT\\s+INTO.+?VALUES|(SELECT|DELETE).+?FROM|(CREATE|ALTER|DROP|TRUNCATE)\\s+(TABLE|DATABASE)";
        $postfilter = "`|' |make_set|\\*|^\\+\/v(8|9)|\\b(and|or)\\b.{1,6}?(=|>|<|\\bin\\b|\\blike\\b)|\\/\\*.+?\\*\\/|<\\s*script\\b|<\\s*img\\b|\\bEXEC\\b|UNION.+?SELECT|UPDATE.+?SET|INSERT\\s+INTO.+?VALUES|(SELECT|DELETE).+?FROM|(CREATE|ALTER|DROP|TRUNCATE)\\s+(TABLE|DATABASE)";
        $cookiefilter = "' |make_set|\\*|`|\\b(and|or)\\b.{1,6}?(=|>|<|\\bin\\b|\\blike\\b)|\\/\\*.+?\\*\\/|<\\s*script\\b|\\bEXEC\\b|UNION.+?SELECT|UPDATE.+?SET|INSERT\\s+INTO.+?VALUES|(SELECT|DELETE).+?FROM|(CREATE|ALTER|DROP|TRUNCATE)\\s+(TABLE|DATABASE)";
        
        foreach ($_GET as $key => $value) {
            self::StopAttack($key, $value, $getfilter);
        }
        foreach ($_POST as $key => $value) {
            // G7WALLET-5572 【对接】主动付款、扫码付款支持手机号包含掩码 需要支持扣费接口不验证手机号字段的合法性，交由前台服务验证
            if ($key == 'password' or (in_array(
                                           ($_REQUEST['method'] ?? ''), [
                                               'g7s.trade.tradePayFosGms',
                                               'g7s.trade.checkSysPayLimit',
                                           ]
                                            //车牌号含有掩码，需要放开
                                       ) and in_array($key, ['drivertel', 'truck_no', 'drivername'])) ) {
                continue;
            }
            self::StopAttack($key, $value, $postfilter);
        }
        foreach ($_COOKIE as $key => $value) {
            //处理显微镜生成的cookie的值为*
            if(stripos($key,"_pk_ses_") !== false){
                $cookiefilter = str_replace("|\\*","",$cookiefilter);
            }
            self::StopAttack($key, $value, $cookiefilter);
        }
    }
    
    /**
     * xssClear
     * @param $arr
     * <AUTHOR>
     * @since ${DATE}
     */
    private static function xssClear(&$arr)
    {
        
        $ra = ['/([\x00-\x08,\x0b-\x0c,\x0e-\x19])/', '/script/', '/javascript/', '/vbscript/', '/expression/', '/applet/', '/meta/', '/xml/', '/blink/', '/link/', '/style/', '/embed/', '/object/', '/frame/', '/layer/', '/title/', '/bgsound/', '/base/', '/onload/', '/onunload/', '/onchange/', '/onsubmit/', '/onreset/', '/onselect/', '/onblur/', '/onfocus/', '/onabort/', '/onkeydown/', '/onkeypress/', '/onkeyup/', '/onclick/', '/ondblclick/', '/onmousedown/', '/onmousemove/', '/onmouseout/', '/onmouseover/', '/onmouseup/', '/onunload/'];
        
        if (is_array($arr)) {
            foreach ($arr as $key => $value) {
                if (!is_array($value)) {
                    if (!get_magic_quotes_gpc())             //不对magic_quotes_gpc转义过的字符使用addslashes(),避免双重转义。
                    {
                        $value = addslashes($value);           //给单引号（'）、双引号（"）、反斜线（\）与 NUL（NULL 字符）加上反斜线转义
                    }
                    $value = preg_replace($ra, '', $value);     //删除非打印字符，粗暴式过滤xss可疑字符串
                    $arr[$key] = htmlentities(strip_tags($value)); //去除 HTML 和 PHP 标记并转换为 HTML 实体
                } else {
                    xssClear($arr[$key]);
                }
            }
        }
    }
    
    private static function StopAttack($StrFilterKey, $StrFilterValue, $ArrFiltReq)
    {
        $StrFilterValue = self::arr_foreach($StrFilterValue);
        if (json_encode($StrFilterValue) != "'") {
            if (preg_match("/" . $ArrFiltReq . "/is", json_encode($StrFilterValue)) == 1) {
                $errorContent = "<br><br>操作IP: " . $_SERVER["REMOTE_ADDR"] . "<br>操作时间: " . strftime("%Y-%m-%d %H:%M:%S") . "<br>操作页面:" . $_SERVER["PHP_SELF"] . "<br>提交方式: " .
                    $_SERVER["REQUEST_METHOD"] . "<br>提交参数: " . $StrFilterKey . "<br>提交数据: " . $StrFilterValue;
                helper::datalog('allParams' . $errorContent, 'filterArgs');
                throw new \RuntimeException('非法参数' . $StrFilterKey . ':' . htmlspecialchars($StrFilterValue), 2);
            }
            if (preg_match("/" . $ArrFiltReq . "/is", $StrFilterKey) == 1) {
                $errorContent = "<br><br>操作IP: " . $_SERVER["REMOTE_ADDR"] . "<br>操作时间: " . strftime("%Y-%m-%d %H:%M:%S") . "<br>操作页面:" . $_SERVER["PHP_SELF"] . "<br>提交方式: " .
                    $_SERVER["REQUEST_METHOD"] . "<br>提交参数: " . $StrFilterKey . "<br>提交数据: " . $StrFilterValue;
                helper::datalog('allParams' . $errorContent, 'filterArgs');
                throw new \RuntimeException('非法参数' . $StrFilterKey . ':' . htmlspecialchars($StrFilterValue), 2);
            }
        } else {
            throw new \RuntimeException('非法参数' . $StrFilterKey . ':' . htmlspecialchars($StrFilterValue), 2);
        }
    }
    
    private static function arr_foreach($arr)
    {
        static $str;
        if (!is_array($arr) && !is_object($arr)) {
            return $arr;
        }
        foreach ($arr as $key => $val ) {
            if (is_array($val) || is_object($val)) {
                self::arr_foreach($val);
            }else {
                $str[] = $val;
            }
        }
        
        return is_array($str) ? implode('',(array)$str) : $str;
    }
    
    /**
     * 时间格式化输出
     *
     * @param string $format
     * @param string $time
     * @return string
     * <AUTHOR>
     */
    static public function nowTime($format = 'Y-m-d H:i:s', $time = '')
    {
        if ($time)
            $datetime = $time;
        else
            $datetime = time();
        
        return date($format, $datetime);
    }
    
    /**
     * 抛出异常
     * @param string $msg 错误描述
     * @param int $code 错误编码
     */
    static public function throwException($msg, $code = 1)
    {
        if ($code != 403)
            helper::datalog('helper|' . $code . '|' . $msg, 'syslog_');
        throw new Exception($msg, $code);
    }
    
    static public function formattime($time, $chinese = FALSE)
    {
        $units = ["天", ":", "'", "''"];
        if ($chinese) $units = ["天", "时", "分", "秒"];
        
        $str = '';
        if ($time > 0) {
            $day = round($time / 86400 + 0.5) - 1;
            $hour = round(($time - $day * 86400) / 3600 + 0.5) - 1;
            $min = round(($time - $day * 86400 - $hour * 3600) / 60 + 0.5) - 1;
            $sec = round($time - $day * 86400 - $hour * 3600 - $min * 60);
        } else {
            $day = $hour = $min = $sec = 0;
        }
        if ($day > 0) {
            $str = $day . $units[0];
        }
        if ($hour > 0) {
            $str .= $hour . $units[1];
        }
        if ($min > 0) {
            $str .= $min . $units[2];
            if ($day > 0) {
                return $str;
            }
        } else {
            $str .= "0" . $units[2];
        }
        if ($sec > 0) {
            $str .= $sec . $units[3];
        } else {
            $str .= "0" . $units[3];
        }
        
        return $str;
    }
    
    static public function condToString($conditions)
    {
        $sql = ' ';
        if (is_string($conditions)) {
            return $sql . $conditions;
        } elseif (is_array($conditions)) {
            $join_char = ''; // 第一个条件前面 没有 and 连接符
            foreach ($conditions as $field => $cond) {
                // 支持 like / or 等操作 例如: 'name' => array('%Bob%','like')
                $op_char = '=';
                if (is_array($cond)) {
                    $value = array_shift($cond);
                    // if $value is array , will use "in" [] opchar
                    if (is_array($value))
                        $value = '[' . implode(',', $value) . ']';
                    
                    $_op_char = array_shift($cond);
                    if (!empty ($_op_char) && is_string($_op_char))
                        $op_char = $_op_char;
                    if (strpos($op_char, "not") > -1) {
                        $value = "not in ('" . str_replace(',', "','", str_replace(' ', '', $value)) . "')";
                    } elseif (strpos($op_char, "in") > -1) {
                        $value = "in ('" . str_replace(',', "','", str_replace(' ', '', $value)) . "')";
                    } else {
                        $value = $op_char . " '" . $value . "'";
                    }
                } elseif (strpos($cond, "null") > -1) {
                    if (strpos($cond, "is") < 0) {
                        $value = " is " . $cond;
                    } else {
                        $value = $cond;
                    }
                } elseif (strpos($cond, ",") > -1) {
                    $value = " in (" . $cond . ")";
                } elseif (preg_match("/>|<|<>/", $cond)) {
                    $value = $cond;
                } else {
                    $value = ' = ' . $cond;
                }
                $sql .= "{$join_char} {$field} {$value}  ";
                $join_char = ' and ';
            }
            
            return $sql;
        }
    }
    
    /**
     * 逐级建目录
     *
     * @param string $path
     * @param int $mode
     * @return string
     */
    static public function createDir($path, $mode = 0777)
    {
        if (!is_dir($path)) {
            self::createDir(dirname($path));
            mkdir($path, $mode);
            @chmod($path, $mode);
        }
        
        return $path;
    }
    
    /**
     * 编码转换
     *
     * @param unknown_type $str
     * @param unknown_type $char
     * @return unknown
     */
    static public function charseticonv($str, $char = 'gbk')
    {
        if (is_array($str)) {
            foreach ($str as $k => $v) {
                $str[$k] = self::charseticonv($v, $char);
            }
        } else {
            if ($char == 'gbk') {
                $str = iconv('utf-8', 'gbk', trim($str));
            } else {
                $str = iconv('gbk', 'utf-8', trim($str));
            }
        }
        
        return $str;
    }
    
    /**
     * 数组去空
     *
     * @param array $array
     * @return array
     */
    static function array_remove_empty($array)
    {
        if (!empty($array)) {
            foreach ($array as $key => $value) {
                if ($value == '') unset($array[$key]);
            }
        }
        
        return $array;
    }
    
    /**
     * 日志记录
     * 2010-12-19
     * <AUTHOR>
     *
     * @param unknown_type $content
     * @param unknown_type $type
     */
    static public function datalog($content, $type = 'log_', $format = 'N', $params = FALSE)
    {
        global $app, $timeStart;
        //文件名称 1--7
        $basePath = $app->getCacheRoot() . 'log' . DIRECTORY_SEPARATOR . date('Y-m-d') . DIRECTORY_SEPARATOR;
        if (!is_dir($basePath)) {
            @mkdir($basePath, 0777);
            @chmod($basePath, 0777);
        }
        
        $logstr = '';
        $logfile = $basePath . $type . date($format, time()) . '.php';
        if (is_file($logfile)) {
            //文件最后修改时间
//			$lastModify = @filemtime($logfile);
//			$diff = time() - $lastModify;
//			//非今天文件 删除
//			if($diff>86400){
//				$filepath = glob($logfile.'*');
//					foreach ($filepath as $unfile) {
//							@unlink($unfile);
//					}
//			} else
            if (@filesize($logfile) >= 2048000) {
                //2M 分文件
                $logfilebak = $logfile . '_' . date('His') . '_bak.php';
                @rename($logfile, $logfilebak);
            }
        }
        $logstr .= ' IP:' . self::getIP();
        $logstr .= ' ' . $content;
        $mtime = explode(' ', microtime());
        $totaltime = @number_format(($mtime[1] + $mtime[0] - $timeStart), 4);
        $url = '';
        if ($params)
            $url = '浏览器:' . $_SERVER["HTTP_USER_AGENT"] . ',参数:' . var_export(self::filterParams(), TRUE);
        $url .= ' ' . $_SERVER['SCRIPT_NAME'] . '?';
        if (isset($_SERVER['QUERY_STRING'])) {
            $url .= $_SERVER['QUERY_STRING'];
        }
        $newlog = date('Y-m-d H:i:s', time()) . ' ' . $totaltime . ' ' . $logstr . ' ' . $url;
        
        if ($fp = @fopen($logfile, 'a')) {
            @flock($fp, 2);
            fwrite($fp, "<?PHP exit;?>\t" . str_replace(['<?', '?>'], '', $newlog) . "\t\n");
            fclose($fp);
        }
        unset($content, $logstr, $newlog);
    }
    
    /* 获取IP*/
    static public function getIP()
    {
        if (getenv("HTTP_CLIENT_IP"))
            $ip = getenv("HTTP_CLIENT_IP");
        elseif (getenv("HTTP_X_FORWARDED_FOR"))
            $ip = getenv("HTTP_X_FORWARDED_FOR");
        elseif (getenv("REMOTE_ADDR"))
            $ip = getenv("REMOTE_ADDR");
        else
            $ip = "UNKNOWN";
        
        return $ip;
    }

    static public function getRealIp()
    {
        $ip = $_SERVER['REMOTE_ADDR'];
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && preg_match_all('#\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}#s', $_SERVER['HTTP_X_FORWARDED_FOR'], $matches)) {
            foreach ($matches[0] AS $xip) {
                if (!preg_match('#^(10|172\.16|192\.168)\.#', $xip)) {
                    $ip = $xip;
                    break;
                }
            }
        } elseif (isset($_SERVER['HTTP_CLIENT_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_CF_CONNECTING_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_CF_CONNECTING_IP'])) {
            $ip = $_SERVER['HTTP_CF_CONNECTING_IP'];
        } elseif (isset($_SERVER['HTTP_X_REAL_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_X_REAL_IP'])) {
            $ip = $_SERVER['HTTP_X_REAL_IP'];
        }
        return $ip;
    }
    
    /**
     * @title 拼接添加SQL
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @param $tableName
     * @param $apiData
     * @return string
     * @returns
     * []
     * @returns
     */
    static public function getSql($tableName, $apiData)
    {
        $fieldArr = [];
        $valuesArr = [];
        foreach ($apiData as $key => $val) {
            $fieldArr[] = "`" . $key . "`";
            $valuesArr[] = "'$val'";
        }
        
        return "insert into $tableName (" . implode(",", $fieldArr) . ") values (" . implode(",", $valuesArr) . ")";
    }
    
    /* 向一个URL地址post数据 */
    static public function init_post($post_url, $data, $header = '')
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $post_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 'application/x-www-form-urlencoded');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        if ($header)
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        $output = curl_exec($ch);
        curl_close($ch);
        
        return $output;
    }
    
    /**
     * 标点类型
     *
     * @return array
     */
    static public function markerType()
    {
        $typearr = [
            '0'   => '个人',
            '100' => '自己位置',
            '300' => '办事处',
            '400' => '办事处区域',
        ];
        
        return $typearr;
    }
    
    /**
     * 根据经纬度计算距离
     * $lng1经度1,$lat1纬度1,$lng2经度2,$lat2纬度2
     *
     * @return float 单位：KM
     */
    static public function getdistance($lat1, $lng1, $lat2, $lng2)
    {
        $radLat1 = deg2rad($lat1);//角度转为狐度
        $radLat2 = deg2rad($lat2);
        $radLng1 = deg2rad($lng1);
        $radLng2 = deg2rad($lng2);
        $a = $radLat1 - $radLat2;//两纬度之差,纬度<90
        $b = $radLng1 - $radLng2;//两经度之差,经度<180
        $s = 2 * asin(sqrt(pow(sin($a / 2), 2) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2))) * 6378.137;
        $s = Round($s * 10000) / 10000;
        
        return $s;
    }
    
    /**
     *
     * 用户信息COOKIE加密码串
     * @param string $username
     * @param string $passwd
     */
    static public function md5Cookie($username, $passwd, $auth = 'ips2HyrCom')
    {
        return md5($passwd . substr(md5(strtolower($username)), 19) . $auth);
        
    }
    
    /**
     *
     * 加密、解密码算法
     * @param string $string
     * @param string $operation DECODE表示解密,ENCODE表示加密
     * @param string $key 密匙
     * @param int $expiry 密文有效期
     */
    static public function authcode($string, $operation = 'DECODE', $key = '', $expiry = 0)
    {
        $ckey_length = 4;    // 随机密钥长度 取值 0-32;
        // 加入随机密钥，可以令密文无任何规律，即便是原文和密钥完全相同，加密结果也会每次不同，增大破解难度。
        // 取值越大，密文变动规律越大，密文变化 = 16 的 $ckey_length 次方
        // 当此值为 0 时，则不产生随机密钥
        
        $key = md5($key ? $key : '020');
        $keya = md5(substr($key, 0, 16));
        $keyb = md5(substr($key, 16, 16));
        $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length) : substr(md5(microtime()), -$ckey_length)) : '';
        
        $cryptkey = $keya . md5($keya . $keyc);
        $key_length = strlen($cryptkey);
        
        $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0) . substr(md5($string . $keyb), 0, 16) . $string;
        $string_length = strlen($string);
        
        $result = '';
        $box = range(0, 255);
        
        $rndkey = [];
        for ($i = 0; $i <= 255; $i++) {
            $rndkey[$i] = ord($cryptkey[$i % $key_length]);
        }
        
        for ($j = $i = 0; $i < 256; $i++) {
            $j = ($j + $box[$i] + $rndkey[$i]) % 256;
            $tmp = $box[$i];
            $box[$i] = $box[$j];
            $box[$j] = $tmp;
        }
        
        for ($a = $j = $i = 0; $i < $string_length; $i++) {
            $a = ($a + 1) % 256;
            $j = ($j + $box[$a]) % 256;
            $tmp = $box[$a];
            $box[$a] = $box[$j];
            $box[$j] = $tmp;
            $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
        }
        
        if ($operation == 'DECODE') {
            if ((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26) . $keyb), 0, 16)) {
                return substr($result, 26);
            } else {
                return '';
            }
        } else {
            return $keyc . str_replace('=', '', base64_encode($result));
        }
    }
    
    static public function createPcode($id)
    {
        if (!$id) {
            return;
        }
        $datestr = substr(date('Ymd', time()), -5);
        $id = str_pad(substr($id, -5), 5, '0', STR_PAD_LEFT);
        
        return $datestr . $id;
    }
    
    /**
     *
     * 全角转化为半角
     * @param string $strnum
     */
    static public function charAlabNum($strnum)
    {
        $arr1 = ['ａ', 'ｂ', 'ｃ', 'ｄ', 'ｅ', 'ｆ', 'ｇ', 'ｈ', 'ｉ', 'ｊ', 'ｋ',
            'ｌ', 'ｍ', 'ｎ', 'ｏ', 'ｐ', 'ｑ', 'ｒ', 'ｓ', 'ｔ', 'ｕ', 'ｖ', 'ｗ', 'ｘ', 'ｙ', 'ｚ',
            'Ａ', 'Ｂ', 'Ｃ', 'Ｄ', 'Ｅ', 'Ｆ', 'Ｇ', 'Ｈ', 'Ｉ', 'Ｊ', 'Ｋ', 'Ｌ', 'Ｍ', 'Ｎ', 'Ｏ',
            'Ｐ', 'Ｑ', 'Ｒ', 'Ｓ', 'Ｔ', 'Ｕ', 'Ｖ', 'Ｗ', 'Ｘ', 'Ｙ', 'Ｚ', '１', '２', '３', '４',
            '５', '６', '７', '８', '９', '０', '，', '．', '；', '：', '～', '！', '（', '）', '｛', '｝',
            '［', '］', '＜', '＞', '？', '＄', '＃', '％', '＠', '＆', '＊'];
        $arr2 = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p',
            'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
            'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '1', '2', '3', '4',
            '5', '6', '7', '8', '9', '0', ',', '.', ';', ':', '~', '!', '(', ')', '{', '}', '[', ']', '<', '>',
            '?', '$', '#', '%', '@', '&', '*'];
        
        return str_replace($arr1, $arr2, $strnum);
    }
    
    /**
     * 导出cvs
     * $title  array("name"=>"名字")
     */
    static public function export_csv($filename, $title, $data, $proto = [], $path = NULL, $page = 1, $pagesize = 0)
    {
        if (!$data) {
            header("Content-Type: text/html; charset=utf-8");
            exit('导出数据为空');
        }
        $filename1 = $filename;
        if (empty($filename)) {
            $filename = 'export' . date('m-d_H-i-s', time()) . '.csv';    //导出文件名称
            $filename1 = $filename;
        } else {
            $filename = helper::charseticonv($filename);
            if (!strpos($filename, ".csv")) {
                $filename = $filename . '.csv';
            }
        }
        
        //输出流
        header("Content-Type:application:text/csv;charset=UTF-8");
        header('Content-Disposition:attachment;filename="' . $filename . '"');
        header('Cache-Control:must-revalidate,post-check=0,pre-check=0');
        header('Expires:0');
        header('Pragma:public');
        
        if ($path) {//有传入文件路径时导出为文件
            $out = fopen($path . $filename1 . ".csv", "a+");
        } else {
            $out = fopen('php://output', 'w');
        }
        //导出标题, 编码转换, 加一列序号
        
        foreach ($title as $value) {
            $dtitle[] = $value['name'];
        }
        //导出标题, 编码转换, 加一列序号
        array_unshift($dtitle, '序号');
        $dtitle = helper::charseticonv($dtitle);
        if ($page == 1) fputcsv($out, $dtitle);//页数大于第一页时不写表头
        
        //导出内容
        if ($page > 1 && $pagesize > 0) $i = ($page - 1) * $pagesize + 1;//页数大于第一页时处理序号
        else $i = 1;
        foreach ($data as $value) {
            $tmpval = [];
            $tmpval[] = $i;
            //列
            $j = 1;
            foreach ($title as $key => $sv) {
                $val = '';
                if (is_object($value)) {
                    $val = $value->$key;
                }
                if (is_array($value)) {
                    $val = $value[$key];
                }
                if (in_array($j, $proto)) $val = '="' . $val . '"';
                $tmpval[] = $val;
                $j++;
            }
            //编码转换
            $tmpval = helper::charseticonv($tmpval);
            fputcsv($out, $tmpval);
            $i++;
        }
        unset($data);
        if ($path) return;//导出为文件的时候返回
        else exit();
    }
    
    
    /**
     * 追加导入cvs
     * $title  array("name"=>"名字")
     */
    public function add_export_csv($filename, $title, $data, $proto = [], $path = NULL, $page = 1, $pagesize = 0)
    {
        
        static $autokey = 1;
        
        
        $appRoot = '../www/download/share/datacache/public_export/';
        
        self::createDir($appRoot);
        
        if (!$data) {
            header("Content-Type: text/html; charset=utf-8");
            exit('导出数据为空');
        }
        $filename1 = $filename;
        if (empty($filename)) {
            $filename = 'export' . date('m-d_H-i-s', time()) . '.csv';    //导出文件名称
            $filename1 = $filename;
        } else {
            $filename = helper::charseticonv($filename);
            if (!strpos($filename, ".csv")) {
                $filename = $filename . '.csv';
            }
        }
        
        $xls_name = $appRoot . $path . $filename1 . '.csv';
        
        $out = fopen($xls_name, "a+");
        
        
        //导出标题, 编码转换, 加一列序号
        foreach ($title as $value) {
            $dtitle[] = $value['name'];
        }
        //导出标题, 编码转换, 加一列序号
        array_unshift($dtitle, '序号');
        $dtitle = helper::charseticonv($dtitle);
        
        if ($page == 1) fputcsv($out, $dtitle);//页数大于第一页时不写表头
        
        //导出内容
        if ($page > 1 && $pagesize > 0) $i = ($page - 1) * $pagesize + 1;//页数大于第一页时处理序号
        else $i = 1;
        foreach ($data as $value) {
            
            $tmpval = [];
            $tmpval[] = $i;
            
            
            //列
            $j = 1;
            foreach ($title as $key => $sv) {
                $val = '';
                if (is_object($value)) {
                    $val = $value->$key;
                }
                if (is_array($value)) {
                    $val = $value[$key];
                }
                if (in_array($j, $proto)) $val = '="' . $val . '"';
                $tmpval[] = $val;
                $j++;
            }
            //编码转换
            $tmpval = helper::charseticonv($tmpval);
            fputcsv($out, $tmpval);
            $i++;
        }
        
        return $filename1;
        
    }
    
    /**
     *
     * 将一个二维数组按照一个指定元素大小排序
     * @param array $array
     * @param string $id
     */
    static public function msort($array, $id = "id")
    {
        $temp_array = [];
        while (count($array) > 0) {
            $lowest_id = 0;
            $index = 0;
            foreach ($array as $item) {
                if (isset($item[$id]) && $array[$lowest_id][$id]) {
                    if ($item[$id] < $array[$lowest_id][$id]) {
                        $lowest_id = $index;
                    }
                }
                $index++;
            }
            $temp_array[] = $array[$lowest_id];
            $array = array_merge(array_slice($array, 0, $lowest_id), array_slice($array, $lowest_id + 1));
        }
        
        return $temp_array;
    }
    
    /**
     * 根据车辆状态获取车辆图标
     */
    static public function getTruckIcon($status, $speed, $timestamp = 0, $course = -1)
    {
        $status = (int)$status;
        $speed = (int)$speed;
        $image = 'mm_20_0';
        
        $time = time();
        $diffminutes = ($time - $timestamp) / 60;
        if ($diffminutes > 20) {
            $image = 'mm_20_0';    //离线
        } else {
            if ($speed <= 5) {
                $image = 'mm_20_5';    //静止
            } elseif ($speed > 5 && $speed <= 80)
                $image = 'mm_20_2';
            elseif ($speed > 80 && $speed <= 120)
                $image = 'mm_20_3';
            elseif ($speed > 120)
                $image = 'mm_20_4';
        }
        if ($course != -1) {
            $iconarr = ['mm_20_0' => 'white', 'mm_20_5' => 'blue', 'mm_20_2' => 'green', 'mm_20_3' => 'yellow', 'mm_20_4' => 'red'];
            $image = 't_' . $iconarr[$image] . '_';
            if ($course == 0) $image .= 0;
            elseif ($course > 0 && $course <= 30) $image .= 0;
            elseif ($course > 30 && $course <= 60) $image .= 30;
            elseif ($course > 60 && $course <= 90) $image .= 60;
            elseif ($course > 90 && $course <= 120) $image .= 90;
            elseif ($course > 120 && $course <= 150) $image .= 120;
            elseif ($course > 150 && $course <= 180) $image .= 150;
            elseif ($course > 180 && $course <= 210) $image .= 180;
            elseif ($course > 210 && $course <= 240) $image .= 210;
            elseif ($course > 240 && $course <= 270) $image .= 240;
            elseif ($course > 270 && $course <= 300) $image .= 270;
            elseif ($course > 300 && $course <= 330) $image .= 300;
            elseif ($course > 330 && $course <= 360) $image .= 330;
            else $image .= 0;
        }
        
        return $image;
    }
    
    /**
     * 截取中文字符串
     * Enter description here ...
     * @param string $string
     * @param int $sublen
     * @param int $start
     * @param string $code
     */
    static public function cut_str($string, $sublen, $start = 0, $code = 'UTF-8')
    {
        if ($code == 'UTF-8') {
            $pa = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|\xe0[\xa0-\xbf][\x80-\xbf]|[\xe1-\xef][\x80-\xbf][\x80-\xbf]|\xf0[\x90-\xbf][\x80-\xbf][\x80-\xbf]|[\xf1-\xf7][\x80-\xbf][\x80-\xbf][\x80-\xbf]/";
            preg_match_all($pa, $string, $t_string);
            if (count($t_string[0]) - $start > $sublen) return join('', array_slice($t_string[0], $start, $sublen));
            
            return join('', array_slice($t_string[0], $start, $sublen));
        } else {
            $start = $start * 2;
            $sublen = $sublen * 2;
            $strlen = strlen($string);
            $tmpstr = '';
            for ($i = 0; $i < $strlen; $i++) {
                if ($i >= $start && $i < ($start + $sublen)) {
                    if (ord(substr($string, $i, 1)) > 129) {
                        $tmpstr .= substr($string, $i, 2);
                    } else {
                        $tmpstr .= substr($string, $i, 1);
                    }
                }
                if (ord(substr($string, $i, 1)) > 129) $i++;
            }
            if (strlen($tmpstr) < $strlen) $tmpstr .= "";
            
            return $tmpstr;
        }
    }
    
    /**
     * IPS2 点|区域 类型对应关系
     * @param int $typevalue
     */
    static function pointType($typevalue)
    {
        switch ($typevalue) {
            case "510":
                $type = '站点';
                break;
            
            case "511":
                $type = '站点区域';
                break;
            
            case "520":
                $type = '停泊点';
                break;
            
            case "310":
                $type = '机构标点';
                break;
            
            case "0":
                $type = '标注点';
                break;
            
            case "1":
                $type = '区域';
                break;
            
            default:
                $type = '其他';
        }
        
        return $type;
    }
    
    /**
     *
     * 得到百分比
     * @param $up 分子
     * @param   $down 分母
     * @param $round 保留小数位。默认2
     * @param   $flag 是否加百分号
     */
    static public function getrate($up, $down, $round = 2, $flag = TRUE)
    {
        $up = floatval($up) * 100;
        $down = floatval($down);
        $str = '';
        if ($down != 0) {
            $str = round($up / $down, $round);
            if ($flag) $str .= '%';
        } else {
            $str = '--';
        }
        
        return $str;
    }
    
    /**
     * excel日期转换成可读日期
     */
    static public function exceltime($time, $format = 'Y-m-d H:i:s')
    {
        return date($format, round(($time - 25569) * 86400) - 28800);
    }
    
    /**
     * excel列转换成数字索引
     * A - 1
     * B - 2
     * ...
     * Z - 26
     * AA - 27
     * ...
     * AZ - 52
     * ...
     * ZZ - 702
     * AAA - 703
     */
    static public function excelcol2index($code)
    {
        $i = 0;
        $len = strlen($code);
        for ($j = 0; $j < $len; $j++) {
            $char = substr($code, $j, 1);
            $p = (ord($char) - 64) * pow(26, $len - $j - 1);
            $i += $p;
        }
        
        return $i;
    }
    
    /**
     * excel数字索引转换成列
     */
    static public function excelindex2col($index)
    {
        $code = '';
        $index--;
        while ($index >= 0) {
            $code = chr($index % 26 + 65) . $code;
            $index = floor($index / 26) - 1;
        }
        
        return $code;
    }
    
    /**
     * 由于php的json扩展自带的函数json_encode会将汉字转换成unicode码
     * 所以我们在这里用自定义的json_encode，这个函数不会将汉字转换为unicode码
     */
    static public function customJsonEncode($a = FALSE)
    {
        if (is_null($a) || (is_string($a) && strtoupper($a) == 'NULL'))
            return "\"\"";
        if ($a === FALSE)
            return 'false';
        if ($a === TRUE)
            return 'true';
        if (is_scalar($a)) {
            if (is_float($a)) {
                // Always use "." for floats.
                return floatval(str_replace(",", ".", strval($a)));
            }
            
            if (is_string($a)) {
                static $jsonReplaces = [["\\", "/", "\n", "\t", "\r", "\b", "\f", '"'], ['\\\\', '\\/', '\\n', '\\t', '\\r', '\\b', '\\f', '\"']];
                
                return '"' . str_replace($jsonReplaces [0], $jsonReplaces [1], $a) . '"';
            } else {
                return $a;
            }
        }
        
        $isList = TRUE;
        for ($i = 0, reset($a); $i < count($a); $i++, next($a)) {
            if (key($a) !== $i) {
                $isList = FALSE;
                break;
            }
        }
        
        $result = [];
        if ($isList) {
            foreach ($a as $v)
                $result [] = self::customJsonEncode($v);
            
            return '[' . join(',', $result) . ']';
        } else {
            foreach ($a as $k => $v)
                $result [] = self::customJsonEncode($k) . ':' . self::customJsonEncode($v);
            
            return '{' . join(',', $result) . '}';
        }
    }
    
    /**
     *  Google经纬度标准转成BaiDu经纬度标准
     *  array $latlng
     *  array(
     *       'lat'=>'',
     *      'lng'=>''
     *  )
     */
    static public function g2b($latlng = [])
    {
        if (!$latlng) {
            return;
        }
        $latlng = (object)$latlng;
        if (isset($latlng->lat) && $latlng->lat) {
            $latlng->lat = round(floatval($latlng->lat + 0.0060) * 10000000) / 10000000;
        }
        if (isset($latlng->lng) && $latlng->lng) {
            $latlng->lng = round(floatval($latlng->lng + 0.0065) * 10000000) / 10000000;
        }
        
        return $latlng;
    }
    
    /**
     *  BaiDu经纬度标准转成Google经纬度标准
     *  array $latlng
     *  array(
     *       'lat'=>'',
     *      'lng'=>''
     *  )
     */
    static public function b2g($latlng = [])
    {
        if (!$latlng) {
            return;
        }
        $latlng = (object)$latlng;
        if (isset($latlng->lat) && $latlng->lat) {
            $latlng->lat = round(floatval($latlng->lat - 0.0060) * 10000000) / 10000000;
        }
        if (isset($latlng->lng) && $latlng->lng) {
            $latlng->lng = round(floatval($latlng->lng - 0.0065) * 10000000) / 10000000;
        }
        
        return $latlng;
    }
    
    /**
     * 根据所传字段数组，弹出不符合规则元素
     * @param array $fields
     * @param array $params
     */
    static public function UnsetField($fields = [], $params)
    {
        if (is_object($params)) {
            $params = (array)$params;
        }
        //		if(!empty($fields) && !empty($params)){
        if (isset($fields) && !empty($params)) {
            foreach ($params as $k => $v) {
                if (!in_array($k, $fields)) {
                    unset($params[$k]);
                }
            }
        }
        
        return $params;
    }
    
    /**
     * 提供一个点的经纬度，提供边长，返回以此点为中心的矩形区域经纬度串 2013-07-04 by lizw
     * $lng经度,$lat纬度,$x东西向边长(米),$y南北向边长(米)
     *
     * @return str (例：111.1105309,32.2204492;111.1105309,32.2195508;111.1094691,32.2195508;111.1094691,32.2204492)
     */
    static public function getlatlngfromdis($lng, $lat, $x, $y)
    {
        $str = '';
        if ($lng && $lat && $x > 0 && $y > 0) {
            $radLat1x = $radLat2x = deg2rad($lat);
            $sx = abs(2 * asin(sqrt(pow(sin($x / (2 * 2 * 6378137)), 2) / (cos($radLat1x) * cos($radLat2x)))));
            $sy = abs($y / (2 * 6378137));
            $dx = round(rad2deg($sx), 7);
            $dy = round(rad2deg($sy), 7);
            $maxlng = $lng + $dx;
            $minlng = $lng - $dx;
            $maxlat = $lat + $dy;
            $minlat = $lat - $dy;
            $str = $maxlng . ',' . $maxlat;
            $str .= ';' . $maxlng . ',' . $minlat;
            $str .= ';' . $minlng . ',' . $minlat;
            $str .= ';' . $minlng . ',' . $maxlat;
        }
        
        return $str;
    }
    
    /*
        文件缓存set方法
        $key  string 类型
        $data string 数据
        zhaowenda 2013-07-17
    */
    static public function setKcache($key, $data)
    {
        global $app;
        self::import($app->getAppRoot() . "lib/Kcache.php");
        $kcache = new Kcache($key);
        $arr = (array)json_decode($_COOKIE['cacheuniqid']);
        if ($arr[$key]) {
            $oldcachekey = $key . '_' . $app->user->uid . '_' . $arr[$key];
            $kcache->delete_cache($oldcachekey);
        }
        $arr[$key] = str_replace('.', '', uniqid(NULL, TRUE));
        $uniqid = json_encode($arr);
        setcookie("cacheuniqid", $uniqid, time() + 3600);
        $cachekey = $key . '_' . $app->user->uid . '_' . $arr[$key];
        $data = $kcache->set($cachekey, $data);
        unset($kcache);
        
        return $data;
    }
    
    /*
        文件缓存set方法
        $key  string 类型
        zhaowenda 2013-07-17
    */
    static public function getKcache($key)
    {
        global $app;
        self::import($app->getAppRoot() . "lib/Kcache.php");
        $uniqid = (array)json_decode($_COOKIE['cacheuniqid']);
        $cachekey = $key . '_' . $app->user->uid . '_' . $uniqid[$key];
        $kcache = new Kcache($key);
        $data = $kcache->get($cachekey, 3600);
        unset($kcache);
        
        return $data;
    }
    
    /*
        分段导出
    */
    static public function putcsv($arr)
    {
        $cache = json_decode($_COOKIE['cacheuniqid']);
        $filename = $cache->search_cache;
        $temp = $arr['data'];
        $fields = $arr['fields'];
        $string = $arr['string'];
        unset($arr);
        $list = [];
        if ($temp) {
            foreach ($temp as $v) {
                $arr = [];
                foreach ($fields as $kk => $vv) {
                    if (in_array($kk, $string)) {
                        $arr[$kk] = '="' . $v->$kk . '"';
                    } else {
                        $arr[$kk] = $v->$kk;
                    }
                }
                array_push($list, $arr);
                unset($arr);
            }
        }
        set_time_limit(0);
        global $app;
        $root = $app->getAppRoot();
        $path = "{$root}www/download/share/export/";
        $first = !file_exists($path . $filename . ".csv");
        $file = fopen($path . $filename . ".csv", "a+");
        if ($first) {
            fputcsv($file, helper::charseticonv($fields));
        }
        if ($list) {
            foreach ($list as $key => $line) {
                $arr = $line;
                $arr = self::charseticonv($arr);
                fputcsv($file, $arr);
            }
            fclose($file);
            
            return TRUE;
        } else {
            return FALSE;
        }
    }
    
    /**
     * 必填参数检测
     * @param array $checkFields
     * @param array $args
     * @param bool|TRUE $is_null
     * @param int $error_code
     * @param array $reportErrorData
     * @return bool|void
     */
    public static function argumentCheck($checkFields = [], $args = [], $is_null = TRUE, $error_code = 2, $reportErrorData=[])
    {
        if (!$checkFields) {
            return;
        } elseif (!$args) {
            throw new \RuntimeException ('参数全部为空', $error_code);
        }
        
        $args = (array)$args;
        
        foreach ($checkFields as $key => $value) {
            if (!array_key_exists($value, $args)) {
                self::reportError($reportErrorData, $value);
                throw new \RuntimeException ($value . '缺失', $error_code);
            } elseif (array_key_exists($value, $args) && $is_null) {
                if (is_null($args [$value]) || $args[$value] === '' || trim($args[$value]) === '') {
                    self::reportError($reportErrorData, $value);
                    throw new \RuntimeException ($value . '不能为空', $error_code);
                }
            }
        }
        
        return TRUE;
    }
    
    /**
     * 告警
     * @param $data
     * @param $value
     * @return bool
     */
    public static function reportError($data, $value)
    {
        if (!empty($data['title']) && !empty($data['content'])) {
            global $app;
            $token = "b3aa33b8aabe4dfba25450ce3e9f268a";
            if ($app->config->api_env == "pro") {
                $token = 'bf93e5c8f2774e0185314a5f952f5dba';
            }
            (new DingTalkAlarm())->sendByTokenForMarkDown(
                [
                    'title'    => $data['title'],
                    'content'  => $data['content'].$value,
                    'token'    => $token
                ]
            );
        }
        
        return true;
    }
    
    /**
     * 生成随机码
     * @param int $len
     * @return bool|string
     */
    public static function generateRandomCode($len=4)
    {
        $str  = '1234567890';
        $str  = str_shuffle($str);
        $code = substr($str, 1, $len);
        
        return $code;
    }
    
    /**
     *发送邮件报警
     */
    static public function alarmMail(array $params)
    {
        //发送邮件报警
        $mail = new Message;
        $mail->addTo('<EMAIL>')
            ->addTo('<EMAIL>')
            ->addTo('<EMAIL>')
            ->addTo('<EMAIL>')
            ->setSubject($params['title'])
            ->setBody($params['content']);
        
        MailSender::send($mail);
    }
    
    /**
     * 根据图片流生成图片
     */
    static public function createImg($imgliu, $type = NULL)
    {
        if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $imgliu, $result)) {
            $imgliu = substr($imgliu, strpos($imgliu, ',') + 1);
            $type = $result[2];
        } elseif ($type) {
            $type = str_replace('image/', '', $type);
        } else {
            $type = 'jpg';
        }
        
        $imgliu = str_replace(' ', '+', $imgliu);
        $tmp_Photo_url = APP_ROOT . '/www/data/licenseImg/' . date('Ymd') . '/';
        if (!is_dir($tmp_Photo_url)) {
            @mkdir($tmp_Photo_url, 0777, TRUE);
        }
        $tmp_Photo_name = guid() . '.' . $type;
        $uploadFile = $tmp_Photo_url . $tmp_Photo_name;
        if (file_put_contents($uploadFile, base64_decode(str_replace($result[1], '', $imgliu)))) {
            return $uploadFile;
        } else {
            \Framework\Log::error('上传失败', [$imgliu, $type], 'ERROR');
            throw new \RuntimeException('上传失败', 5);
        }
    }
    
    /**
     * 把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
     * @param $para
     * @return string
     */
    static public function createLinkString($para,$strArr=[])
    {
        $str = '';
        if($para){
            foreach($para as $k=>$v){
                if(is_array($v) || is_object($v)){
                    self::createLinkString($v,$strArr);
                }else{
                    $strArr[] = $k.''.$v;
                }
            }
            
            $str = implode('', $strArr);;
            //如果存在转义字符，那么去掉转义
            if (get_magic_quotes_gpc()) {
                $str = stripslashes($str);
            }
        }
        
        return $str;
    }
    
    /**
     * 数组元素转小写
     * @param $whiteList
     * @return mixed
     */
    static public function arrayToLower($whiteList)
    {
        foreach ($whiteList as &$v) {
            $v = strtolower($v);
        }
        
        return $whiteList;
    }
    
    static public function getFileType($file)
    {
        $data = NULL;
        
        if (file_exists($file)) {
            $fileInfo = pathinfo($file);
            $data = strtolower($fileInfo['extension']);
        }
        
        return $data;
    }
    
    /**
     * 根据数组键值对转换sql批量语句
     */
    /**
     * 批量操作消费记录表以及副卡表
     * batchInsert to oil_card_vice_trades
     * @param array $apiData
     * @return bool|int
     * <AUTHOR>
     * @since ${DATE}
     */
    static function batchInsertToSqlStr($insertData = [], $tbName = '')
    {
        //批量入库
        $batchInsertSql = FALSE;
        if ($insertData) {
            $batchInsertSqlArr = [];
            foreach ($insertData as $v) {
                $fieldArr = [];
                $valuesArr = [];
                
                foreach ($v as $key => $val) {
                    if ($key != 'is_exist' && $key != 'id') {
                        $fieldArr[] = "`" . $key . "`";
                        $valuesArr[] = "'$val'";
                    }
                }
                if ($v['is_exist'] == 0) {
                    $batchInsertSqlArr[] = "insert into " . $tbName . " (" . implode(",", $fieldArr) . ") values (" . implode(",", $valuesArr) . ")";
                } elseif ($v['is_exist'] == 1) {
                    $temp_arr = [];
                    foreach ($fieldArr as $key => $val) {
                        $temp_arr[] = $fieldArr[$key] . " = " . $valuesArr[$key] . "";
                    }
                    $batchInsertSqlArr[] = "update " . $tbName . " set " . implode(",", $temp_arr) . " where id = " . $v['id'] . "";
                }
            }
            $batchInsertSql = implode(";", $batchInsertSqlArr);
        }
        
        return $batchInsertSql;
    }
    
    static public function batchUpdateToSqlStrExt($data = [], $tbName = '', $pk = 'id', $where = '')
    {
        //批量入库
        $batchSql = FALSE;
        if ($data) {
            $batchUpdateToSqlStr = [];
            foreach ($data as $v) {
                $fieldArr = [];
                $valuesArr = [];
                
                foreach ($v as $key => $val) {
                    if ($key != 'is_exist' && $key != $pk) {
                        $fieldArr[] = "`" . $key . "`";
                        $valuesArr[] = "'$val'";
                    }
                }
                
                $temp_arr = [];
                foreach ($fieldArr as $key => $val) {
                    $temp_arr[] = $fieldArr[$key] . " = " . $valuesArr[$key] . "";
                }
                $batchUpdateToSqlStr[] = "update " . $tbName . " set " . implode(",", $temp_arr) . " where $pk = '" . $v[$pk] . "'" . $where;
            }
            $batchSql = implode(";", $batchUpdateToSqlStr);
        }
        
        return $batchSql;
    }
    
    static public function substr_chiness($str, $start=0, $length, $charset="utf-8", $suffix="")
    {
        if(function_exists("mb_substr")){
            return mb_substr($str, $start, $length, $charset).$suffix;
        }
        elseif(function_exists('iconv_substr')){
            return iconv_substr($str,$start,$length,$charset).$suffix;
        }
        $re['utf-8'] = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|[\xe0-\xef][\x80-\xbf]{2}|[\xf0-\xff][\x80-\xbf]{3}/";
        $re['gb2312'] = "/[\x01-\x7f]|[\xb0-\xf7][\xa0-\xfe]/";
        $re['gbk']  = "/[\x01-\x7f]|[\x81-\xfe][\x40-\xfe]/";
        $re['big5']  = "/[\x01-\x7f]|[\x81-\xfe]([\x40-\x7e]|\xa1-\xfe])/";
        preg_match_all($re[$charset], $str, $match);
        $slice = join("",array_slice($match[0], $start, $length));
        return $slice.$suffix;
    }

    public static function getWeekStartAndEndDate($year, $week)
    {
        return [
            'start' => date("Y-m-d", strtotime("$year-W$week-1")),
            'end'   => date("Y-m-d", strtotime("$year-W$week-7")),
        ];
    }
}//end helper


/* 别名函数，生成对内部方法的链接。 */
function inLink($methodName = 'index', $vars = '', $viewType = '')
{
    global $app;
    
    return helper::createLink($app->getModuleName(), $methodName, $vars, $viewType);
}

/* 循环一个数组。*/
function cycle($items)
{
    static $i = 0;
    if (!is_array($items)) $items = explode(',', $items);
    if (!isset($items[$i])) $i = 0;
    
    return $items[$i++];
}

function guid()
{
    $charid = strtoupper(md5(uniqid(mt_rand(), TRUE)));
    $hyphen = chr(45);// "-"
    $uuid = //chr(123)// "{"
        substr($charid, 0, 8) . $hyphen
        . substr($charid, 8, 4) . $hyphen
        . substr($charid, 12, 4) . $hyphen
        . substr($charid, 16, 4) . $hyphen
        . substr($charid, 20, 12);
    
    //.chr(125);// "}"
    return $uuid;
}

//  生成UUID，并去掉分割符
function guid32()
{
    if (function_exists('com_create_guid')) {
        $uuid = com_create_guid();
    } else {
        mt_srand((double)microtime() * 10000);//optional for php 4.2.0 and up.
        $charid = strtoupper(md5(uniqid(rand(), TRUE)));
        $hyphen = chr(45);// "-"
        $uuid = chr(123)// "{"
            . substr($charid, 0, 8) . $hyphen
            . substr($charid, 8, 4) . $hyphen
            . substr($charid, 12, 4) . $hyphen
            . substr($charid, 16, 4) . $hyphen
            . substr($charid, 20, 12)
            . chr(125);// "}"
    }
    $uuid = str_replace(['-', '{', '}'], '', $uuid);
    
    return $uuid;
}

function formatMoney($money)
{
//    if(intval($money) > 10000 )
//    {
//        return substr(number_format($money / 10000,3),0,-2).'万';
//    }else{
    return number_format($money, 2, '.', ',');
//    }
}

/* 时间格式化常量 */
define('DF_DATETIME', 'Y-m-d H:i:s');
define('DF_DATE', 'Y-m-d');
define('DF_TIME', 'H:i:s');