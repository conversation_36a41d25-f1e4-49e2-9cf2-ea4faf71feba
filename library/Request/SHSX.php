<?php


namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class SHSX extends Base implements ThirdParty
{
    /**
     * @param string $method
     * @param array $data
     * @return array
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 16:11
     */
    public static function handle(string $method = '', array $data = []): array
    {
        $domain = AuthConfigData::getAuthConfigValByName("SHSX_APP_DOMAIN");
        $signData = self::sign($data);
        Log::handle("Request shsx started", "善宏(山西)", $method, [
            "requestParams" => $data,
        ], "", "info");
        $result = self::curl("$domain$method?" . http_build_query($signData), $data, 20, [
            "Content-Type: application/json",
            'Expect:',
        ], "post", true, 320);
        Log::handle("Request shsx finish", "善宏(山西)", $method, [
            "requestParams" => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('善宏(山西)接口输出数据格式错误', 5000999);
        }
        if ($result['code'] != 0) {
            throw new Exception($result['msg'] ?? '', 5000999);
        }
        return $result;
    }

    public static function sign(array $signData, $timestamp = ''): array
    {
        $returnData = [
            'appid'     => AuthConfigData::getAuthConfigValByName("SHSX_APP_ID"),
            'timestamp' => empty($timestamp) ? getMillisecond() : $timestamp,
        ];
        $signData = array_merge($returnData, $signData);
        $secret = AuthConfigData::getAuthConfigValByName("SHSX_APP_SECRET");
        ksort($signData);
        $stringToBeSigned = $secret;
        foreach ($signData as $k => $v) {
            if (!is_array($v) && "@" != substr($v, 0, 1)) {
                $stringToBeSigned .= "$k$v";
            }
        }
        unset($k, $v);
        $stringToBeSigned .= $secret;
        $returnData['sign'] = strtoupper(md5($stringToBeSigned));
        return $returnData;
    }
}
