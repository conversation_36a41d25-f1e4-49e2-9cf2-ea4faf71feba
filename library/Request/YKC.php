<?php
// 云快充

namespace Request;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\Log\RequestLog as Log;
use Exception;

class YKC extends Base implements ThirdParty
{
    /**
     * @throws Exception
     */
    public static function handle(
        $method = null,
        $data = null,
        int $timeOut = 20,
        int $jsonRule = 336,
        $isAuth = true
    ) {
        $aesKey = AuthConfigData::getAuthConfigValByName("YKC_APP_AES_KEY");
        $aesIv = AuthConfigData::getAuthConfigValByName("YKC_APP_AES_IV");
        Log::handle("Request ykc started", "云快充", $method, [
            'requestData' => $data,
        ], "", "info");
        $requestData['Data'] = base64_encode(
            openssl_encrypt(
                json_encode($data, $jsonRule),
                'AES-128-CBC',
                $aesKey,
                OPENSSL_RAW_DATA,
                $aesIv
            )
        );
        $requestData['OperatorID'] = AuthConfigData::getAuthConfigValByName("YKC_APP_KEY");
        $requestData['TimeStamp'] = date('YmdHis');
        $redis = app('redis');
        $requestData['Seq'] = str_pad(
            $redis->incrby("ykc_sequence_id", 1),
            4,
            '0',
            STR_PAD_LEFT
        );
        $requestData['Sig'] = strtoupper(hash_hmac(
            'md5',
            $requestData['OperatorID'] .
            $requestData['Data'] .
            $requestData['TimeStamp'] .
            $requestData['Seq'],
            AuthConfigData::getAuthConfigValByName("YKC_APP_SIGN_SECRET")
        ));
        $requestHeaders = [];
        $apiUrl = AuthConfigData::getAuthConfigValByName('YKC_APP_DOMAIN');
        if ($isAuth) {
            $token = $redis->get("ykc_token");
            if (empty($token)) {
                $responseData = self::handle(
                    'query_token',
                    [
                        'OperatorID'     => AuthConfigData::getAuthConfigValByName("YKC_APP_KEY"),
                        'OperatorSecret' => AuthConfigData::getAuthConfigValByName("YKC_APP_SECRET"),
                    ],
                    $timeOut,
                    true,
                    false
                );
                $redis->setex("ykc_token", 86340, $responseData['data']['token']);
                $token = $responseData['data']['token'];
            }
            $requestHeaders[] = "Authorization: Bearer $token";
        }
        $result = self::curl("$apiUrl$method", $requestData, $timeOut, $requestHeaders, 'post', true);
        $result['']
        Log::handle("Request ykc finished", "云快充", $method, [
            'requestData' => $data,
        ], $result, "info");
        if (!array_has($result, ['code'])) {
            throw new Exception('云快充接口输出数据格式异常', 5000999);
        }
        if ($result['code'] != 200) {
            throw new Exception($result['message'] ?? '', 5000999);
        }
        return $result;
    }
}