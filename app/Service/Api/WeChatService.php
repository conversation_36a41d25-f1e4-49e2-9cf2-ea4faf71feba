<?php


namespace App\Service\Api;


use EasyWeChat\Factory;

class WeChatService
{
    public $weChat;

    public function __construct()
    {
        if (!$this->weChat) {
            $this->weChat = Factory::miniProgram(config('weChat'));
        }
    }

    public function auth($code)
    {
        return $this->weChat->auth->session($code);
    }

    public function decrypt($sessionKey, $iv, $encryptedData)
    {
        return $this->weChat->encryptor->decryptData($sessionKey, $iv, $encryptedData);
    }


}