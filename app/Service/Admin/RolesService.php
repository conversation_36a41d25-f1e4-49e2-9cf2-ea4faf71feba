<?php
namespace App\Service\Admin;

use App\Library\Defines\ApiConf;
use App\Library\Request;
use App\Library\Helper\Common;
use App\Repositories\Admin\RolesRepository;

class RolesService
{
    public $rolesRepository;
    public function __construct(RolesRepository $rolesRepository)
    {
        $this->rolesRepository = $rolesRepository;

    }

    /**
     * 角色名称模糊搜索
     * @param $params
     * @return array
     */
    public function getRoleNameByFuzzySearch($params)
    {
        $page = $params['page'] = array_get($params, 'page', 1);
        $limit = $params['limit'] = array_get($params, 'limit', 10);
        unset($params['page'], $params['limit']);$source = Request::get('source') ?? '1';
        $ruleList = $this->rolesRepository->getRolesPaginate($params, $page, $limit, ['id','name','status']);
        return $ruleList;
    }

    /**
     * 角色详情
     * @param array $params
     * @return array
     */
    public function getRole(array $params)
    {
        $params['source'] = Request::get('source') ?? '1';$gmsMenu = config('gmsMenu');
        if (!empty($params)) {
            $role = $this->rolesRepository->getRole($params);
        }
        $gmsRoutes = isset($role['routes']) ? explode(',', $role['routes']) : [];
        $gmsMenu = collect($gmsMenu)->map(function($item) use ($gmsRoutes){
            $item['checked'] = in_array($item['id'], $gmsRoutes) && $item['is_menu'] != 1 ? true : false;
            $item['spread'] = true;
            return $item;
        })->toArray();
        $mtMenu = collect($gmsMenu)->filter(function($item, $key){
            return ($item['pid'] == 90 || $item['id'] == 90) && $key != 91;
        })->toArray();
        $gmsMenu = collect($gmsMenu)->reject(function($item, $key){
            return $item['pid'] == 90 || $item['id'] == 90 || $key == 91;
        })->toArray();
        $data = [
            'name' => $role['name'] ?? '',
            'status' => $role['status'] ?? '',
            'status_name' => $role['status_name'] ?? '',
        ];
        $data['menu'] = [
            'gms' => [
                'tree' => Common::quoteMakeTree($gmsMenu)
            ],
            'mt' => [
                'source' => $params['source'],
                'tree' => Common::quoteMakeTree($mtMenu)
            ],
        ];
        if (isset($role['id'])) {
            $data['menu']['gms']['id'] = $role['id'];
        }
        return $data;
    }

    /**
     * 角色分页
     * @param array $params
     * @return array
     */
    public function getRolesPaginate(array $params)
    {
        //参数组装
        if (isset($params['id'])){
            $params['id'] = explode(',', $params['id']);
        }
        $params['source'] = Request::get('source') ?? '1';
        $page = $params['page'] = array_get($params, 'page', 1);
        $limit = $params['limit'] = array_get($params, 'limit', 10);
        unset($params['page'], $params['limit']);
        $roleList = $this->rolesRepository->getRolesPaginate($params, $page, $limit);
        return $roleList;
    }

    /**
     * 角色新建或编辑
     * @param array $params
     * @return RolesRepository|bool|\Illuminate\Database\Eloquent\Model
     */
    public function rolesUpOrCreate(array $params)
    {
        //GMS参数过滤
        $gmsRoleParam = $role = [];$roleSource = config('appKey.role_source');$gmsMenu = config('gmsMenu');
        if (isset($params['gms']['id'])) {
            $role = $this->rolesRepository->getRole(['id' => $params['gms']['id'],'source' => $roleSource['GMS']]);
        }
        $roleParamName = $this->rolesRepository->getRole(['name' => $params['name'],'source' => $roleSource['GMS']]);
        if (isset($params['gms']['routes']) && !empty($roleParamName) && (!empty($role) && $roleParamName['name'] != $role['name'] || empty($role))) {
            throw new \RuntimeException('错误：角色名称已存在，请核实', ApiConf::CODE_ERR_PARAMS);
        }
        if (isset($params['gms']['routes'])) {
            $gmsRoutes = explode(',', $params['gms']['routes']);
            if (count($gmsRoutes) != collect($gmsMenu)->reject(function($item, $key){ return in_array($key, ['91']);})->pluck('id')->intersect($gmsRoutes)->count()) {
                throw new \RuntimeException('错误：角色名称不正确，请核实', ApiConf::CODE_ERR_PARAMS);
            }
        }
        //参数组装
        if(isset($params['gms']['routes'])) {
            $gmsRoleParam = [
                'name' => $params['name'],
                'status' => $params['status'],
                'source' => $roleSource['GMS'],
                'routes' => $params['gms']['routes'],
                'creator' => !empty($role) ? $role['creator'] : Request::get('user_name'),
                'modifier' => Request::get('user_name'),
            ];
        }
        if (isset($params['gms']['id'])) {
            $gmsRoleParam['id'] = $params['gms']['id'];
        }
        $data['gms'] = $this->rolesRepository->rolesUpOrCreate($gmsRoleParam);

        return $data;
    }

}
