<?php

namespace App\Console\Commands;

use App\Models\Foss\Station;
use App\Models\Gas\StationModel;
use App\Repositories\Station\StationRepository;
use Illuminate\Console\Command;
use App\Services\Station\StationService;
use Library\Monitor\Falcon;
use App\Services\Station\StationBatchService;

class PriceGosCheckCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Price:gosCheck';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '1号卡油站验证gos价格预警';

    /**
     * Create a new command instance.
     *
     * @return void
     */


    public function __construct()
    {
        parent::__construct();

    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //记得增加上线条件--card_classify=2
        $stationList = StationModel::getClassAllStationIds();
        //站点信息
        $station_ids = [];
        foreach ($stationList as $_item){
            $station_ids[] = $_item->id;
        }
        $gos_station_price = [];
        //获取gos上线状态数据
        $gosStationPriceList = \App\Models\Gos\StationServicePrice::whereIn('station_id',$station_ids)->get();
        foreach ($gosStationPriceList as $value){
            $gos_station_price[$value->station_id][$value->oil_name] = $value->price;
        }
        //查询gos的数据
        $stationList = StationBatchService::getPriceForDownStream($station_ids, 'GOS', '20003JCP');
        $stationList = count($station_ids) == 1 ? [$stationList] : $stationList;
        $station_code = [];
        $station_name = [];
        $GMS_Price = [];
        $GOS_Price = [];
        foreach ($stationList as $key => $item) {
            $station_info = $item['GOS'];
            if (!empty($station_info) && isset($station_info['price_list']) && $station_info['price_list']) {

                //gos没有这个站点的价格
                if(!isset($gos_station_price[$station_info['id']]) || empty($gos_station_price[$station_info['id']])){
                    $station_code[] = $station_info['id'];
                    // $station_name[] = $station_info['station_name'];
                    foreach ($station_info['price_list'] as $key=>$val){
                        //oil_id=oil_type/oil_name/oil_level
                        $GMS_Price[] = $val['price'];
                        $GOS_Price[] = 0.00;
                    }
                }

                //gos有这个站点 但是不对
                $gos_price_list = $gos_station_price[$station_info['id']];
                foreach ($station_info['price_list'] as $key=>$val){
                    $oil_name = $val['oil_name'];
                    //oil_id=oil_type/oil_name/oil_level
                    $gms_price = $val['price'];
                    $gos_price = isset($gos_price_list[$oil_name]) ?  $gos_price_list[$oil_name] : 0;
                    if($gms_price!=$gos_price){
                        $GMS_Price[] = $gms_price;
                        $GOS_Price[] = $gos_price;
                        //不存在
                        if(!in_array($station_info['id'],$station_code)){
                            $station_code[] = $station_info['id'];
                            //$station_name[] = $station_info['station_name'];
                        }
                    }
                }
            }
        }

        //数据为空～
        if(!empty($station_code)){
            $station_name = $warn_station_code = [];
            $station_warn_info = StationModel::whereIn('id',$station_code)->get();
            foreach ($station_warn_info as $info){
                $warn_station_code[] = $info->station_code;
                $station_name[] = $info->station_name;
            }
            $text  = '小程序站点通用价格异常报警'."\n\n";
            $text .= '站点编码：'. implode(',',$warn_station_code) ."\n";
            $text .= '站点名称：'. implode(',',$station_name) ."\n";
            $text .= '小程序价格：'. implode(',',$GOS_Price) ."\n";
            $text .= 'GMS价格：'. implode(',',$GMS_Price) ."\n";
            Falcon::feishu(config('feishu.rd'), $text);
        }
    }

}
