<?php
/**
 * Created by PhpStorm.
 * User: zhanglingxiang
 * Date: 2019/1/16
 * Time: 下午3:14
 */

namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Illuminate\Console\Command;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;

class PullOil extends Command
{
    public const OIL_STATION_CACHE_CHECK = 'oil_station_check';

    protected $nameAbbreviation = 'base';
    protected $platformCode     = '';

    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:base';
    protected $name         = 'pull oil';

    protected $authData = [];

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return true|void
     */
    public function handle()
    {
        return true;
    }

    public function getExistsStation(): array
    {
        return array_column(
            StationPriceData::getDataByWhere([
                [
                    'field'    => 'platform_code',
                    'value'    => $this->platformCode,
                    'operator' => '=',
                ],
            ], [
                'station_id',
            ]),
            'station_id'
        );
    }

    protected function dealNotExistsStation(array $existsStationIds)
    {
        //判断拉取回来的油站删除了哪些,作停用处理
        $oldStationIds = array_column(
            StationPriceData::getDataByWhere([
                [
                    'field'    => 'platform_code',
                    'value'    => $this->platformCode,
                    'operator' => '=',
                ],
            ], [
                'station_id',
            ]),
            'station_id'
        );
        $oldExistsStationIds = array_intersect($oldStationIds, $existsStationIds);
        $notExitsStationIds = array_diff($oldStationIds, $oldExistsStationIds) ?? [];
        $redisConn = app('redis');

        if (!empty($notExitsStationIds)) {
            $notExitsStationData = $redisConn->hmget(
                static::OIL_STATION_CACHE_CHECK . "_" . $this->nameAbbreviation,
                $notExitsStationIds
            );
            foreach ($notExitsStationData as $v) {
                if ($v) {
                    try {
                        $oilTemp = json_decode($v, true);
                        if ($oilTemp['is_stop'] == 1) {
                            continue;
                        }
                        $oilTemp['is_stop'] = 1;
                        $oilTemp['assoc_id'] = $oilTemp['id'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        BasicJob::pushStationToStationHub($oilTemp);
                        StationPriceData::updateByWhere([
                            [
                                'field'    => 'station_id',
                                'value'    => $oilTemp['id'],
                                'operator' => '=',
                            ],
                            [
                                'field'    => 'platform_code',
                                'value'    => $this->platformCode,
                                'operator' => '=',
                            ],
                        ], [
                            'enabled_state' => 2
                        ]);
                        $redisConn->hdel(
                            static::OIL_STATION_CACHE_CHECK . "_" . $this->nameAbbreviation,
                            $oilTemp['id']
                        );
                    } catch (Throwable $exception) {
                        Log::handle(
                            "Stop station failed",
                            [
                                'stationData' => $v,
                                'exception'   => $exception,
                            ],
                            DockingPlatformInfoData::getFieldsByNameAbbreviation(
                                $this->nameAbbreviation,
                                "platform_name",
                                ""
                            )['platform_name'],
                            "oil_station_data",
                            "error"
                        );
                    }
                }
            }
        }
    }

    protected function stopStation(array $stopStationIds = [])
    {
        if (!empty($stopStationIds)) {
            try {
                $total = count($stopStationIds);
                $totalPage = ceil($total / 50);
                $oilStationCacheCheckKey = self::OIL_STATION_CACHE_CHECK . "_" . $this->nameAbbreviation;
                $cacheStation = app('redis')->hmget($oilStationCacheCheckKey, $stopStationIds);
                $disabledStation = [];
                foreach ($cacheStation as $k => $v) {
                    $stationData = json_decode($v, true) ?? [];
                    if (!$stationData) {
                        continue;
                    }
                    $cacheStation[$stationData['id']] = $stationData;
                    unset($cacheStation[$k]);
                    if ($stationData['is_stop'] == 1) {
                        $disabledStation[] = $stationData['id'];
                    }
                }
                $stopStationIds = array_diff($stopStationIds, $disabledStation);
                if (empty($stopStationIds)) {
                    return;
                }
                for ($p = 0; $p < $totalPage; $p++) {
                    $currentData = array_slice($stopStationIds, $p * 50, 50);
                    foreach ($currentData as &$cv) {
                        $cv = [
                            "app_station_id" => $cv,
                            "pcode"          => $this->platformCode,
                        ];
                    }
                    try {
                        FOSS_STATIONRequest::handle("v1/station/batchStop", [
                            "id_list" => $currentData,
                            "app_key" => $this->authData['access_key'],
                        ]);
                    } catch (Throwable $exception) {
                        Log::handle("Stop failed about the station's price of sqZsh", [
                            "data"      => $currentData,
                            'exception' => json_decode(Log::getMessage([
                                'exception' => $exception,
                            ]), true)['exception'],
                        ], '上汽中石化', 'oil_station_data', 'error');
                    }
                }
                StationPriceData::updateByWhere([
                    [
                        'field'    => "station_id",
                        'operator' => "in",
                        'value'    => $stopStationIds,
                    ],
                    [
                        'field'    => "platform_code",
                        'operator' => "=",
                        'value'    => $this->platformCode,
                    ],
                ], [
                    'enabled_state' => 2,
                ]);
                $updateStationCache = [];
                foreach ($stopStationIds as $stv) {
                    if (!isset($cacheStation[$stv])) {
                        continue;
                    }
                    $cacheStation[$stv]['is_stop'] = 1;
                    $updateStationCache[$stv] = json_encode($cacheStation[$stv]);
                }
                if (!empty($updateStationCache)) {
                    app('redis')->hmset($oilStationCacheCheckKey, $updateStationCache);
                }
            } catch (Throwable $exception) {
                Log::handle(
                    "Stop failed about the station's price of mtlSy",
                    [
                        "stopStationIds" => $stopStationIds,
                        'exception'      => json_decode(Log::getMessage([
                            'exception' => $exception,
                        ]), true)['exception'],
                    ],
                    DockingPlatformInfoData::getFieldsByNameAbbreviation(
                        $this->nameAbbreviation,
                        "platform_name",
                        ""
                    )['platform_name'],
                    'oil_station_data',
                    'error'
                );
            }
        }
    }
}
