<?php
// 云快充
namespace App\Console\Commands;


use App\Jobs\BasicJob;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\StationPrice as StationPriceData;
use Request\YKC as YKCRequest;
use Throwable;


class PullOilForYKC extends PullOil
{
    /**
     * 命令行执行命令
     * @var string
     */
    protected $signature = 'pull-oil:ykc';
    protected $name      = 'pull oil for ykc';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = 'pull oil for ykc';

    protected $nameAbbreviation = 'ykc';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->platformCode = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->nameAbbreviation
        )['role_code'] ?? '';
    }

    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 11:50 上午
     */
    public function handle()
    {
        $page = 1;
        $totalPage = 0;
        $existsStationIds = [];
        do {
            try {
                $data = YKCRequest::handle("query_stations_info", [
                    "PageSize" => 100,
                    "PageNo"   => $page,
                ]);
                $totalPage = $data['parsedData']['PageCount'];

                $waitCoordinates = [];
                foreach ($data['parsedData']['StationInfos'] as $v) {
                    $waitCoordinates[] = "{$v["StationLng"]},{$v["StationLat"]}";
                }
                if ($waitCoordinates) {
                    $regionData = getRegionForCoordinateByGdApi($waitCoordinates);
                }

                foreach ($data['parsedData']['StationInfos'] as $v) {
                    try {
                        //油站数据
                        $oilTemp = [];
                        $oilTemp['station_id'] = $v['StationID'];
                        $oilTemp['assoc_id'] = $v['StationID'];
                        $existsStationIds[] = $v['StationID'];
                        $oilTemp['station_name'] = $v['StationName'];
                        $oilTemp['name_abbreviation'] = $this->nameAbbreviation;
                        $oilTemp['station_type'] = $v['StationType'];
                        $oilTemp['province_code'] = $regionData["{$v["StationLng"]},{$v["StationLat"]}"]['provinceCode'] ?? '';
                        $oilTemp['city_code'] = $regionData["{$v["StationLng"]},{$v["StationLat"]}"]['cityCode'] ?? '';
                        if (checkIsMunicipality($oilTemp['city_code'])) {
                            $oilTemp['province_code'] = $oilTemp['city_code'];
                        }

                        $oilTemp['station_lng'] = $v['StationLng'];
                        $oilTemp['station_lat'] = $v['StationLat'];
                        $oilTemp['station_status'] = $v['StationStatus'];
                        $oilTemp['park_nums'] = $v['ParkNums'];
                        $oilTemp['site_guide'] = $v['SiteGuide'] ?? '';
                        $oilTemp['construction'] = $v['Construction'];
                        $oilTemp['pictures'] = json_encode($v['Pictures'] ?? []);
                        $oilTemp['match_cars'] = $v['MatchCars'] ?? '';
                        $oilTemp['park_info'] = $v['ParkInfo'] ?? '';
                        $oilTemp['business_hours'] = $v['BusineHours'] ?? '';
                        $oilTemp['park_fee'] = $v['ParkFee'] ?? '';
                        $oilTemp['overtime_fee'] = $v['OvertimeFee'] ?? '';
                        $oilTemp['payment'] = $v['Payment'] ?? '';
                        $oilTemp['support_order'] = $v['SupportOrder'] ?? 0;
                        $oilTemp['remark'] = $v['Remark'] ?? '';
                        $oilTemp['station_tel'] = $v['StationTel'] ?? '';
                        $oilTemp['service_tel'] = $v['ServiceTel'];
                        $oilTemp['address'] = $v['Address'];
                        $oilTemp['prices'] = [];
                        foreach ($v['ChargeFeeDetail'] as $cv) {
                            $oilTemp['prices'][] = [
                                'start_time'      => $cv['StartTime'],
                                'end_time'        => $cv['EndTime'],
                                'electricity_fee' => $cv['ElectricityFee'],
                                'service_fee'     => $cv['ServiceFee'],
                                'price_type'      => 1,
                            ];
                        }
                        foreach ($v['DiscountChargeFeeDetail'] ?? [] as $dv) {
                            $oilTemp['prices'][] = [
                                'start_time'      => $cv['StartTime'],
                                'end_time'        => $cv['EndTime'],
                                'electricity_fee' => $cv['ElectricityFee'],
                                'service_fee'     => $cv['ServiceFee'],
                                'price_type'      => 2,
                            ];
                        }
                        $oilTemp['devices'] = [];
                        $oilTemp['connectors'] = [];
                        foreach ($v['EquipmentInfos'] as $ev) {
                            $oilTemp['devices'][] = [
                                'equipment_id'      => $ev['EquipmentID'],
                                'manufacturer_id'   => $ev['ManufacturerID'] ?? '',
                                'manufacturer_name' => $ev['ManufacturerName'] ?? '',
                                'equipment_model'   => $ev['EquipmentModel'] ?? '',
                                'production_date'   => $ev['ProductionDate'] ?? '',
                                'equipment_type'    => $ev['EquipmentType'],
                                'equipment_lng'     => $ev['EquipmentLng'] ?? '',
                                'equipment_lat'     => $ev['EquipmentLat'] ?? '',
                                'power'             => $ev['Power'],
                                'equipment_name'    => $ev['EquipmentName'] ?? '',
                            ];
                            foreach ($ev['ConnectorInfos'] as $cev) {
                                $oilTemp['connectors'][] = [
                                    'connector_id'         => $cev['ConnectorID'],
                                    'equipment_id'         => $ev['EquipmentID'],
                                    'station_id'           => $v['StationID'],
                                    'connector_name'       => $cev['ConnectorName'] ?? '',
                                    'connector_type'       => $cev['ConnectorType'],
                                    'voltage_upper_limit'  => $cev['VoltageUpperLimits'],
                                    'voltage_lower_limit'  => $cev['VoltageLowerLimits'],
                                    'current'              => $cev['Current'],
                                    'power'                => $cev['Power'],
                                    'park_no'              => $cev['ParkNo'] ?? '',
                                    'equipment_lng'        => $ev['EquipmentLng'],
                                    'equipment_lat'        => $ev['EquipmentLat'],
                                    'national_standard'    => $cev['NationalStandard'],
                                ];
                            }
                        }
//                        BasicJob::pushStationToStationHub($oilTemp);
                    } catch (Throwable $throwable) {
                        Log::handle("Push station failed", [
                            'exception'   => $throwable,
                            'stationData' => $v,
                        ], "云快充", "oil_station_data", "error");
                    }
                }
                $page++;
            } catch (Throwable $throwable) {
                Log::handle("Pulling the oil search site failed", [
                    'exception' => $throwable,
                ], "云快充", "oil_station_data", "error");
            }
        } while ($page <= $totalPage);

        $this->dealNotExistsStation($existsStationIds);
    }
}
