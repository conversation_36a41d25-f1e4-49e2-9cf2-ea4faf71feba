<?php

namespace App\Console\Commands;

use App\Library\Helper\Common;
use App\Services\Rule\RuleService;
use Illuminate\Console\Command;

class StationRuleSyncV1ToV2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'StationRule:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '限站规则V1同步V2';

    /**
     * Create a new command instance.
     *
     * @return void
     */

    private  $ruleService;

    public function __construct(RuleService $ruleService)
    {
        parent::__construct();
        $this->ruleService = $ruleService;

    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Common::log('info', '限站规则V1同步V2', ['step' => 'START']);
        $params = [
            'orgcodeList' => []
        ];
        request()->offsetSet('user_name', 'system');
        $this->ruleService->syncV1RuleToV2($params);
        Common::log('info', '限站规则V1同步V2', ['step' => 'END']);
    }

}
