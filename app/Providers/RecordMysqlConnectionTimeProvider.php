<?php

namespace App\Providers;

use App\Library\CustomMysqlConnector;
use Illuminate\Support\ServiceProvider;

class RecordMysqlConnectionTimeProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('db.connector.mysql', function ($app) {
            return new CustomMysqlConnector();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
    }
}
