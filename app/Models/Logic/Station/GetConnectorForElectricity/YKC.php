<?php
// 云快充

namespace App\Models\Logic\Station\GetConnectorForElectricity;


use App\Models\Data\AuthConfig as AuthConfigData;
use Exception;
use Faker\Provider\Uuid;
use Illuminate\Http\JsonResponse;
use Request\YKC as YKCRequest;

class YKC extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function handle(): JsonResponse
    {
        $redis = app('redis');
        $queryEquipBizSeqKey = "ykc_query_equip_business_policy_sequence_id_" . date('Ymd');
        $redis->expire($queryEquipBizSeqKey, 2);
        $queryEquipBizSeq = str_pad(
            $redis->incrby($queryEquipBizSeqKey, 1),
            5,
            '0',
            STR_PAD_LEFT
        );
        $redis->expire($queryEquipBizSeqKey, 86400);
        $result = YKCRequest::handle('query_equip_business_policy', [
            'EquipBizSeq' => AuthConfigData::getAuthConfigValByName("YKC_APP_KEY") .
                             getMillisecond() . $queryEquipBizSeq,
            'ConnectorID' => $this->data['connector_id'],
        ]);
        foreach ($result['parsedData']['ConnectorList'] as $v) {

        }
    }
}