<?php


namespace App\Models\Logic\Station\GetConnectorForElectricity;

use App\Models\Data\AuthInfo;
use App\Models\Logic\Base;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class Main extends Base
{
    private $workerMapping = [
        'ykc' => YKC::class,
    ];
    /**
     * @var ThirdParty
     */
    private $worker;

    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
        $authInfo = AuthInfo::getAuthInfoByRoleCode($this->data["supplier_code"]);
        if (!empty($authInfo)) {
            //把授权信息数据存入真实数据
            $this->data['data']['auth_info_data'] = $authInfo[0];
            if (isset($this->jobMapping[$authInfo[0]['name_abbreviation']])) {
                $this->worker = (new $this->workerMapping[$authInfo[0]['name_abbreviation']]($this->data));
            }
        }
        if (!$this->worker) {
            responseFormat(0, [], true, "非法请求");
        }
    }

    /**
     * @return JsonResponse|Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/28 3:45 下午
     */
    public function handle()
    {
        return $this->worker->handle();
    }
}
