<?php


namespace App\Models\Logic\Station\Receive;

use App\Models\Logic\Base;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class Main extends Base
{
    private $workerMapping = [
        'dd'        => DIANDI::class,
        'zy'        => ZY::class,
        'ezt'       => EZT::class,
        'hjy'       => HJY::class,
        'xy'        => XY::class,
        'wjy'       => WJY::class,
        'saic'      => SAIC::class,
        'gb'        => GB::class,
        'gb_tj'     => GB::class,
        'cy'        => CY::class,
        'hsy'       => HSY::class,
        'sqzl'      => SQZL::class,
        'zhyk'      => ZHYK::class,
        'zdc'       => ZDC::class,
        'xyn'       => XYN::class,
        'sqZsh'     => SQZSH::class,
        'mtlSy'     => MTLSY::class,
        'sh'        => SH::class,
        'shsx'      => SHSX::class,
        'jt'        => JT::class,
        'mj'        => MJ::class,
        'xmsk'      => XMSK::class,
        'yundatong' => YUNDATONG::class,
        'gaodeng'   => GAODENG::class,
        'jq'        => JQ::class,
    ];
    /**
     * @var DIANDI|ZY
     */
    private $worker;

    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
        $this->worker = new $this->workerMapping[$this->name_abbreviation]($this->data);
    }

    /**
     * @return JsonResponse|Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/28 3:45 下午
     */
    public function handle()
    {
        $result = $this->worker->handle();
        if ($result instanceof Response) {
            return $result;
        }
        return responseFormat();
    }
}
