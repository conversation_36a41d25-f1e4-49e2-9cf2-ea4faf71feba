<?php


namespace App\Models\Logic\Account\Query;


use App\Models\Logic\Base;
use Illuminate\Http\JsonResponse;
use Throwable;


class Main extends Base
{
    private $workerMapping = [
        'jdwc' => JDWC::class,
        ''     => SIMPLE::class,
        'kl'   => KL::class,
        'htx'  => HTX::class,
        'ygy'  => YGY::class,
    ];

    /**
     * @var ThirdParty
     */
    private $worker;

    /**
     * Main constructor.
     * @param array $parameter
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);

        if (!isset($this->workerMapping[$this->name_abbreviation])) {

            $this->worker = new $this->workerMapping['']($this->data);
            return;
        }

        $this->worker = new $this->workerMapping[$this->name_abbreviation]($this->data);
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/17 4:20 下午
     */
    public function handle()
    {
        return $this->worker->handle();
    }
}
