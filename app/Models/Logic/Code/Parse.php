<?php
/**
 * Created by PhpStor<PERSON>.
 * User: zdl
 * Date: 2019-01-17
 * Time: 19:03
 */

namespace App\Models\Logic\Code;


use App\Models\Data\DecodingQrCodeAdaptation\BASE as BASEDecodingQrCodeAdaptation;
use App\Models\Data\DecodingQrCodeAdaptation\DIANDI as DIANDIDecodingQrCodeAdaptation;
use App\Models\Data\DecodingQrCodeAdaptation\SP as SPDecodingQrCodeAdaptation;
use App\Models\Data\DecodingQrCodeAdaptation\WJY as WJYDecodingQrCodeAdaptation;
use App\Models\Data\DecodingQrCodeAdaptation\XY as XYDecodingQrCodeAdaptation;
use App\Models\Data\DecodingQrCodeAdaptation\ZY as ZYDecodingQrCodeAdaptation;
use App\Models\Logic\Base;
use App\Models\Logic\Code\Parse\AD;
use App\Models\Logic\Code\Parse\AJSW;
use App\Models\Logic\Code\Parse\BBSP;
use App\Models\Logic\Code\Parse\BDT_ORG;
use App\Models\Logic\Code\Parse\BQ;
use App\Models\Logic\Code\Parse\CFHY;
use App\Models\Logic\Code\Parse\CFT;
use App\Models\Logic\Code\Parse\CHTX;
use App\Models\Logic\Code\Parse\CIEC;
use App\Models\Logic\Code\Parse\CN;
use App\Models\Logic\Code\Parse\DDE;
use App\Models\Logic\Code\Parse\DESP2A6LA9;
use App\Models\Logic\Code\Parse\DESP2C637M;
use App\Models\Logic\Code\Parse\DESP2H8BBD;
use App\Models\Logic\Code\Parse\FY;
use App\Models\Logic\Code\Parse\G7;
use App\Models\Logic\Code\Parse\GBDW;
use App\Models\Logic\Code\Parse\GHC;
use App\Models\Logic\Code\Parse\HG;
use App\Models\Logic\Code\Parse\HLJH;
use App\Models\Logic\Code\Parse\HR;
use App\Models\Logic\Code\Parse\HSL;
use App\Models\Logic\Code\Parse\HYJY;
use App\Models\Logic\Code\Parse\HYT_ORG;
use App\Models\Logic\Code\Parse\HZ;
use App\Models\Logic\Code\Parse\JDWC;
use App\Models\Logic\Code\Parse\JF;
use App\Models\Logic\Code\Parse\JTXY;
use App\Models\Logic\Code\Parse\KY;
use App\Models\Logic\Code\Parse\LF;
use App\Models\Logic\Code\Parse\LHYS;
use App\Models\Logic\Code\Parse\LT;
use App\Models\Logic\Code\Parse\MB;
use App\Models\Logic\Code\Parse\MK;
use App\Models\Logic\Code\Parse\MY;
use App\Models\Logic\Code\Parse\MYB;
use App\Models\Logic\Code\Parse\MYCF;
use App\Models\Logic\Code\Parse\PCKJ;
use App\Models\Logic\Code\Parse\QDMY;
use App\Models\Logic\Code\Parse\RQ;
use App\Models\Logic\Code\Parse\RRS;
use App\Models\Logic\Code\Parse\RY;
use App\Models\Logic\Code\Parse\SHENGMAN;
use App\Models\Logic\Code\Parse\SM;
use App\Models\Logic\Code\Parse\SP;
use App\Models\Logic\Code\Parse\SQ;
use App\Models\Logic\Code\Parse\TC;
use App\Models\Logic\Code\Parse\WSY;
use App\Models\Logic\Code\Parse\WZYT;
use App\Models\Logic\Code\Parse\XC;
use App\Models\Logic\Code\Parse\XM;
use App\Models\Logic\Code\Parse\XYDS;
use App\Models\Logic\Code\Parse\YB;
use App\Models\Logic\Code\Parse\YBT;
use App\Models\Logic\Code\Parse\YGJ;
use App\Models\Logic\Code\Parse\YGY;
use App\Models\Logic\Code\Parse\YLZ;
use App\Models\Logic\Code\Parse\YXT;
use App\Models\Logic\Code\Parse\ZEY;
use App\Models\Logic\Code\Parse\ZJ;
use App\Models\Logic\Code\Parse\ZJKA;
use App\Models\Logic\Code\Parse\ZLGX;
use App\Models\Logic\Code\Parse\ZZ;
use App\Models\Logic\Code\Parse\ZZ_AH;
use App\Models\Logic\Code\Parse\ZZ_BJ;
use App\Models\Logic\Code\Parse\ZZ_TJ;
use Illuminate\Http\JsonResponse;
use Throwable;


class Parse extends Base
{
    private $classMapping          = [
        'fykc'     => FY::class,
        'ghc'      => GHC::class,
        'ygj'      => YGJ::class,
        'BT'       => BDT_ORG::class,
        'bbsp'     => BBSP::class,
        'HLMD'     => HLJH::class,
        'LHYS'     => LHYS::class,
        'HG'       => HG::class,
        'YB'       => YB::class,
        'SMI'      => SM::class,
        'JD'       => JDWC::class,
        'G7'       => G7::class,
        'HT'       => G7::class,
        'MK'       => MK::class,
        'xc'       => XC::class,
        'total'    => DDE::class,
        'tc'       => TC::class,
        'SQWL'     => SQ::class,
        'MYB'      => MYB::class,
        'CN'       => CN::class,
        'SPT5CWE'  => SP::class,
        'ZY'       => ZEY::class,
        'lf'       => LF::class,
        'HYJY'     => HYJY::class,
        'SPT3SB7'  => SP::class,
        'SPTXXTZ'  => SP::class,
        'SPTLHLX'  => SP::class,
        'SPTCDNF'  => SP::class,
        'SPT76YH'  => SP::class,
        'SPT5GZG'  => SP::class,
        'SPTMKCF'  => SP::class,
        'SPTHZTD'  => SP::class,
        'SPTG7PR'  => SP::class,
        'SPTRR3N'  => SP::class,
        'SPTNGHH'  => SP::class,
        'SPTYW4A'  => SP::class,
        'SPTHSQW'  => SP::class,
        'SPT5RLT'  => SP::class,
        'HYT'      => HYT_ORG::class,
        'CHTX'     => CHTX::class,
        'PCKJ'     => PCKJ::class,
        'ZLGX'     => ZLGX::class,
        'RQ'       => RQ::class,
        'XM'       => XM::class,
        'JF'       => JF::class,
        'AJSW'     => AJSW::class,
        'YGY'      => YGY::class,
        'ZGGJNY'   => CIEC::class,
        'BH'       => ZZ::class,
        '2H8BBD'   => DESP2H8BBD::class,
        '2A6LA9'   => DESP2A6LA9::class,
        'YXT'      => YXT::class,
        'HLJHY'    => HR::class,
        '2C637M'   => DESP2C637M::class,
        'GB'       => GBDW::class,
        'ZJ'       => ZJ::class,
        'SGMYW'    => MY::class,
        'WSY'      => WSY::class,
        'BQ'       => BQ::class,
        'ZZAH'     => ZZ_AH::class,
        'ZZBJ'     => ZZ_BJ::class,
        'TJZZ'     => ZZ_TJ::class,
        'CFT'      => CFT::class,
        'AD'       => AD::class,
        'LT'       => LT::class,
        'wlqq://'  => MB::class,
        'CFHY'     => CFHY::class,
        'ZJKA'     => ZJKA::class,
        'JTXY'     => JTXY::class,
        'YLZZJ'    => YLZ::class,
        'MYCF'     => MYCF::class,
        'SHENGMAN' => SHENGMAN::class,
        'RY'       => RY::class,
        'YBT'      => YBT::class,
        'HZ'       => HZ::class,
        'RRS'      => RRS::class,
        'XY'       => XYDS::class,
        'QDMY'     => QDMY::class,
        'KYE'      => KY::class,
        'HSL'      => HSL::class,
        'WZYT'     => WZYT::class,
        'HK'       => HK::class,
    ];
    private $responseEntityMapping = [
        'zy'  => ZYDecodingQrCodeAdaptation::class,
        'dd'  => DIANDIDecodingQrCodeAdaptation::class,
        'xy'  => XYDecodingQrCodeAdaptation::class,
        'sp'  => SPDecodingQrCodeAdaptation::class,
        'wjy' => WJYDecodingQrCodeAdaptation::class,
    ];
    // like common func responseFormat
    private $responseCallbacks = [
        'wjy' => "responseFormatForWjy",
    ];
    /**
     * @var BASEDecodingQrCodeAdaptation
     */
    private $responseEntityWorker;

    /**
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);

        if (isset($this->responseEntityMapping[$this->name_abbreviation])) {
            $this->responseEntityWorker = new $this->responseEntityMapping[$this->name_abbreviation]();
        } else {
            $this->responseEntityWorker = new BASEDecodingQrCodeAdaptation();
        }
        //区分不同解码调用者的参数进行转换
        switch ($this->name_abbreviation) {
            case "wjy":
            case "zy":

                $this->data['station_id'] = $this->data['stationId'];
                $this->data['qr_code'] = $this->data['qrcode'];
                break;
        }
        if (!empty($this->data['auth_data']['role_code']) and $this->data['auth_data']['role_code'] != config(
                "oil.one_card_supplier_code"
            )) {
            $this->data['gasStationId'] = $this->getGasStationIdByAppStationIdAndPCode($this->data['station_id']);
        }
        if (!isset($this->data['gasStationId'])) {
            $this->data['gasStationId'] = $this->data['station_id'];
        }
    }

    /**
     * @param bool $isReturn 是否直接返回数据,默认返回response对象
     * @return array|JsonResponse
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/12 3:50 下午
     */
    public function handle(bool $isReturn = false)
    {
        //根据第三方处理类配置数组初始化对应类
        $qrCodeData = explode("_", $this->data['qr_code']);
        if ($qrCodeData[0] == 'G7' and count($qrCodeData) > 2) {
            $qrCodePrefix = $qrCodeData[1];
        } elseif ($qrCodeData[0] == 'HT' and count($qrCodeData) > 2) {
            $qrCodePrefix = $qrCodeData[1];
        } else {
            $qrCodePrefix = $qrCodeData[0];
            $qrCodeData = explode("payment/", $this->data['qr_code']);
            if (count($qrCodeData) > 1) {
                $qrCodePrefix = $qrCodeData[0];
            }
        }

        //如果没有匹配到解析二维码的类,返回错误提示
        if (!isset($this->classMapping[$qrCodePrefix])) {
            return responseFormat(5000021, [], true);
        }

        $responseCallback = $this->responseCallbacks[$this->name_abbreviation] ?? 'responseFormat';
        $logicObj = new $this->classMapping[$qrCodePrefix]($this->data);
        try {
            $result = $logicObj->handle();
            $result->station_id = $this->data['station_id'];
            $result->qrCode = $this->data['qr_code'];
            $responseData = $this->responseEntityWorker->getAdaptationResult($result);
            if ($isReturn) {
                return $responseData;
            }
            return $responseCallback(0, $responseData);
        } catch (Throwable $throwable) {
            return $responseCallback(
                $throwable->getCode() == 5000666 ? 5000999 : $throwable->getCode(),
                [],
                false,
                $throwable->getMessage()
            );
        }
    }
}

