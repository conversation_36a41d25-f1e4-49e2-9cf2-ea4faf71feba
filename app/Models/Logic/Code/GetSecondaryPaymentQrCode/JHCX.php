<?php


namespace App\Models\Logic\Code\GetSecondaryPaymentQrCode;


use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\QrCode;
use Illuminate\Http\JsonResponse;
use Request\JHCX as JHCXRequest;
use Throwable;


class JHCX extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/9 5:03 下午
     */
    public function handle()
    {
        $orderInfo = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => $this->name_abbreviation,
            ],
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['order_id'],
            ],
        ], [
            'self_order_id',
            'extend',
            'id',
            'platform_order_id',
            'platform_name',
        ], true, true);
        if (!$orderInfo or !$orderInfo instanceof OrderAssocDao) {

            return responseFormat(5000024);
        }
        $redis = app('redis');
        if (!$qrCodeContent = $redis->get("verification_certificate_" . $this->data['order_id'] .
            $orderInfo->platform_order_id)) {

            $codeExpiredTime = AuthConfigData::getAuthConfigValByName("JHCX_CODE_EXPIRE");
            $redisConn = app("redis");
            do {

                $dy_code = getMillisecond();
                $lockKey = "{$this->name_abbreviation}_coupon_code_$dy_code";
                if (!$redisConn->set($lockKey, $dy_code, 'ex', $codeExpiredTime, 'nx')) {

                    $dy_code = '';
                }
                $dy_code = $orderInfo->platform_name . $dy_code . mt_rand(10, str_pad('', 19 -
                        strlen($orderInfo->platform_name) - strlen($dy_code), '9'));
            } while ($dy_code == '');
            try {

                $dy_code = strtoupper($dy_code);
                $result = JHCXRequest::handle('/Api/DiyCard/dycode_up', [
                    'code'        => $orderInfo->platform_order_id,
                    'dycode'      => $dy_code,
                    'expiredTime' => time() + (int)$codeExpiredTime,
                ]);
                releaseRedisLock($redisConn, $lockKey, $dy_code);
                $qrCodeContent = $result['new_dycode'];
                $redis->setex("verification_certificate_" . $this->data['order_id'] .
                    $orderInfo->platform_order_id, $codeExpiredTime - 1, $qrCodeContent);
            } catch (Throwable $throwable) {

                releaseRedisLock($redisConn, $lockKey, $dy_code);
                throw $throwable;
            }
        }
        $qrCodeGen = new QrCode($qrCodeContent);
        $qrCodeGen->setSize(300);
        $qrCodeGen->setWriterByName('png');
        $qrCodeGen->setEncoding('ISO-8859-1');
        $qrCodeGen->setErrorCorrectionLevel(ErrorCorrectionLevel::HIGH());
        $qrCodeGen->setValidateResult(false);
        $qrCodeGen->setRoundBlockSize(true);
        $qrCodeGen->setMargin(10);
        $qrCodeGen->setWriterOptions(['exclude_xml_declaration' => true]);
        return responseFormat(0, [
            'certificate_type'           => "image",
            'certificate_resource'       => $qrCodeGen->writeDataUri(),
            'platform_name_abbreviation' => $this->name_abbreviation,
            'expiration'                 => $redis->ttl("verification_certificate_" .
                $this->data['order_id'] . $orderInfo->platform_order_id),
        ]);
    }
}
