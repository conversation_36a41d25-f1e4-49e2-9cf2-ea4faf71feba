<?php
/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: zdl
 * Date: 2019-01-28
 * Time: 15:22
 */

namespace App\Models\Logic\Data\Push;


use App\Models\Dao\OrderAssoc as OrderAssocDao;
use App\Models\Data\AuthInfo;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\ResponseLog as Log;
use App\Models\Logic\Base;
use App\Models\Logic\Data\Push\ReToBePaid\AD;
use App\Models\Logic\Data\Push\ReToBePaid\HYT_ORG;
use App\Models\Logic\Data\Push\ReToBePaid\RQ;
use App\Models\Logic\Data\Push\ReToBePaid\SQ;
use App\Models\Logic\Data\Push\ReToBePaid\YGY;
use App\Models\Logic\Data\Push\ReToBePaid\YGYDEMO;
use App\Models\Logic\Data\Push\ReToBePaid\ZEY;
use Exception;
use Throwable;

class ReToBePaid extends Base
{
    /**
     * @var ZEY
     */
    protected $worker;
    protected $dockingPlatformName = '';
    protected $workerMapping       = [
        'zey'     => ZEY::class,
        'sq'      => SQ::class,
        'hytOrg'  => HYT_ORG::class,
        'rq'      => RQ::class,
        'ygy'     => YGY::class,
        'ygyDemo' => YGYDEMO::class,
        'ad'      => AD::class,
    ];

    /**
     * RefuelingStatement constructor.
     * @param array $parameter
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);

        $authInfo = AuthInfo::getAuthInfoByRoleCode($this->data["role_code"]);
        if (!empty($authInfo)) {
            $realNameAbbreviation = explode('_', $authInfo[0]['name_abbreviation'])[0];
            $this->dockingPlatformName = DockingPlatformInfoData::getFieldsByNameAbbreviation(
                $realNameAbbreviation,
                'platform_name',
                ''
            )['platform_name'] ?? '';

            if (isset($this->workerMapping[$realNameAbbreviation])) {
                $this->worker = (new $this->workerMapping[$realNameAbbreviation]($this->data));
            } else {
                Log::handle([
                    'msg'  => "非法请求",
                    'data' => $this->data,
                ]);
                throw new Exception("非法请求");
            }
        } else {
            Log::handle([
                'msg'  => "非法请求",
                'data' => $this->data,
            ]);
            throw new Exception("非法请求");
        }
    }

    /**
     * @return OrderAssocDao
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/3/12 7:15 下午
     */
    public function handle()
    {
        return $this->worker->handle();
    }
}
