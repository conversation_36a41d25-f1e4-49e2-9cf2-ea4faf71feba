<?php
/**
 * Created by <PERSON>pStor<PERSON>.
 * User: zdl
 * Date: 2019-01-28
 * Time: 15:22
 */

namespace App\Models\Logic\Data\Push;


use App\Models\Data\AuthInfo;
use App\Models\Data\Log\QueueLog;
use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base;
use App\Models\Logic\Data\Push\VerificationResult\AD;
use App\Models\Logic\Data\Push\VerificationResult\AJSW;
use App\Models\Logic\Data\Push\VerificationResult\BQ;
use App\Models\Logic\Data\Push\VerificationResult\CFHY;
use App\Models\Logic\Data\Push\VerificationResult\CFT;
use App\Models\Logic\Data\Push\VerificationResult\CIEC;
use App\Models\Logic\Data\Push\VerificationResult\CQGT;
use App\Models\Logic\Data\Push\VerificationResult\DESP2A6LA9;
use App\Models\Logic\Data\Push\VerificationResult\DESP2C637M;
use App\Models\Logic\Data\Push\VerificationResult\DESP2H8BBD;
use App\Models\Logic\Data\Push\VerificationResult\FY;
use App\Models\Logic\Data\Push\VerificationResult\GBDW;
use App\Models\Logic\Data\Push\VerificationResult\HLL;
use App\Models\Logic\Data\Push\VerificationResult\HR;
use App\Models\Logic\Data\Push\VerificationResult\HSL;
use App\Models\Logic\Data\Push\VerificationResult\HZ;
use App\Models\Logic\Data\Push\VerificationResult\JF;
use App\Models\Logic\Data\Push\VerificationResult\JTXY;
use App\Models\Logic\Data\Push\VerificationResult\LT;
use App\Models\Logic\Data\Push\VerificationResult\MLT;
use App\Models\Logic\Data\Push\VerificationResult\MY;
use App\Models\Logic\Data\Push\VerificationResult\MYCF;
use App\Models\Logic\Data\Push\VerificationResult\QDMY;
use App\Models\Logic\Data\Push\VerificationResult\RRS;
use App\Models\Logic\Data\Push\VerificationResult\RY;
use App\Models\Logic\Data\Push\VerificationResult\SHENGMAN;
use App\Models\Logic\Data\Push\VerificationResult\SP;
use App\Models\Logic\Data\Push\VerificationResult\WSY;
use App\Models\Logic\Data\Push\VerificationResult\WZYT;
use App\Models\Logic\Data\Push\VerificationResult\XM;
use App\Models\Logic\Data\Push\VerificationResult\XYDS;
use App\Models\Logic\Data\Push\VerificationResult\YBT;
use App\Models\Logic\Data\Push\VerificationResult\YLZ;
use App\Models\Logic\Data\Push\VerificationResult\YXT;
use App\Models\Logic\Data\Push\VerificationResult\ZJ;
use App\Models\Logic\Data\Push\VerificationResult\ZJKA;
use App\Models\Logic\Data\Push\VerificationResult\ZLGX;
use App\Models\Logic\Data\Push\VerificationResult\ZZ;
use App\Models\Logic\Data\Push\VerificationResult\ZZ_AH;
use App\Models\Logic\Data\Push\VerificationResult\ZZ_BJ;
use App\Models\Logic\Data\Push\VerificationResult\ZZ_TJ;
use Exception;
use Symfony\Component\HttpFoundation\Response;
use Throwable;


class VerificationResult extends Base
{
    private $jobMapping = [
        'SPT3SB7'     => SP::class,
        'SPT6DR8'     => SP::class,
        'SPSL8KY'     => SP::class,
        'SPTNS3M'     => SP::class,
        'SPTGF8D'     => SP::class,
        'zlgx'        => ZLGX::class,
        'fy'          => FY::class,
        'xm'          => XM::class,
        'jf'          => JF::class,
        'zz'          => ZZ::class,
        'ajsw'        => AJSW::class,
        'ciec'        => CIEC::class,
        'desp|2H8BBD' => DESP2H8BBD::class,
        'desp|2A6LA9' => DESP2A6LA9::class,
        'mlt'         => MLT::class,
        'gbdw'        => GBDW::class,
        'yxt'         => YXT::class,
        'desp|2C637M' => DESP2C637M::class,
        'zj'          => ZJ::class,
        'my'          => MY::class,
        'hr'          => HR::class,
        'hll'         => HLL::class,
        'wsy'         => WSY::class,
        'bq'          => BQ::class,
        'zzah'        => ZZ_AH::class,
        'zzbj'        => ZZ_BJ::class,
        'zztj'        => ZZ_TJ::class,
        'cft'         => CFT::class,
        'lt'          => LT::class,
        'ad'          => AD::class,
        'cfhy'        => CFHY::class,
        'cqgt'        => CQGT::class,
        'ylz'         => YLZ::class,
        'ylzZj'       => YLZ::class,
        'zjka'        => ZJKA::class,
        'jtxy'        => JTXY::class,
        'mycf'        => MYCF::class,
        'shengman'    => SHENGMAN::class,
        'ry'          => RY::class,
        'rrs'         => RRS::class,
        'ybt'         => YBT::class,
        'hz'          => HZ::class,
        'xyds'        => XYDS::class,
        'qdmy'        => QDMY::class,
        'hsl'         => HSL::class,
        'wzyt'        => WZYT::class,
    ];

    /**
     * @var SP
     */
    private $worker = null;

    /**
     * VerificationResult constructor.
     * @param array $parameter
     * @return void
     * @throws Exception
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
        $orderInfoModel = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['order_id'],
            ],
        ], ["*"], false, true);
        if (!$orderInfoModel) {
            responseFormat(5000018, [], true);
        }
        $authInfo = AuthInfo::getAuthInfoByWhere([
            [
                'field'    => 'name_abbreviation',
                'operator' => 'in',
                'value'    => $orderInfoModel->pluck("platform_name")->toArray(),
            ],
            [
                'field'    => 'role',
                'operator' => '=',
                'value'    => 6,
            ],
        ], ['*']);
        if ($orderInfoModel->count() > 1) {
            foreach ($orderInfoModel as $v) {
                if ($v->platform_name == $authInfo['name_abbreviation']) {
                    $orderInfoModel = $v;
                    break;
                }
            }
        } else {
            $orderInfoModel = $orderInfoModel[0];
        }
        $this->data['order_info_model'] = $orderInfoModel;
        if (!empty($authInfo)) {
            if (isset($this->jobMapping[$authInfo['name_abbreviation']])) {
                //把授权信息数据存入真实数据
                $this->data['auth_data'] = $authInfo;
                $this->worker = (new $this->jobMapping[$authInfo['name_abbreviation']]($this->data));
            } else {
                switch ($parameter['logClass'] ?? '') {
                    case QueueLog::class:
                        QueueLog::handle('no need to push', [
                            'parameters' => $this->data,
                        ], 'sys', 'warning', 'run_task');
                        break;
                    default:
                        ResponseLog::handle([
                            'msg'        => 'no need to push',
                            'parameters' => $this->data,
                        ]);
                }
            }
        }
    }

    /**
     * @return Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-08-22 18:02
     */
    public function handle(): Response
    {
        if ($this->worker) {
            $this->worker->handle();
        }
        return responseFormat(0, []);
    }
}
