<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Models\Data\OrderAssoc as OrderAssocData;
use Request\CNPC as CNPCRequest;
use Throwable;


class CNPC extends ThirdParty
{
    /**
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $orderAssocDao = OrderAssocData::insert(1, 2, $this->data['id'],
            '', $this->name_abbreviation, '', "", json_encode([
                "owner" => $this->data
            ]));
        try {
            $response = CNPCRequest::handle('/open/json/open_coupon/distcoupons', [
                'alias'   => $this->data['coupon_alias'],
                'num'     => 1,
                'orderno' => $this->data['id'],
            ]);
            $orderAssocDao->platform_order_id = $response['coupons'][0]['voucher'];
            $orderAssocDao->extend = json_encode([
                "owner"  => $this->data,
                "either" => $response,
            ]);
            $orderAssocDao->save();
            responseFormat(0, array_merge($response, [
                'order_id'         => $this->data['id'],
                'secondaryPayment' => true,
            ]), true);
        } catch (Throwable $throwable) {

            $this->refundToTradeCenterForPayCallbackFailed($orderAssocDao, $throwable->getMessage());
        }
    }
}
