<?php


namespace App\Models\Logic\Data\Push\AutonomousOrder;


use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Data\StationUniqueMapping as StationUniqueMappingData;
use Request\EZT as EZTRequest;
use Throwable;
use Tool\Alarm\DingDing;


class EZT extends ThirdParty
{
    /**
     * @return void
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2021/3/10 11:14 上午
     */
    public function handle()
    {
        $orderInfoModel = OrderAssocData::insert(1, 2,
            $this->data['id'], '', $this->name_abbreviation, '', "",
            json_encode($this->data));
        if (!$this->data['pushExtends'] or !isset($this->data['pushExtends']['price']) or
            !isset($this->data['pushExtends']['priceGun'])) {

            $this->refundToTradeCenterForPayCallbackFailed($orderInfoModel, "油枪、应收单价不能为空");
        }
        if (!isset($this->data['pushExtends']['amountGun'])) {
            $this->refundToTradeCenterForPayCallbackFailed($orderInfoModel, '油枪金额不能为空');
        }

        $stationPriceInfo = StationUniqueMappingData::getStationPriceByStationId([
            'station_id' => $this->data['station_id'],
            [
                'field'    => 'oil_type',
                'operator' => '=',
                'value'    => array_flip(config('oil.oil_type'))[$this->data['oil_name_id']],
            ],
            [
                'field'    => 'oil_no',
                'operator' => '=',
                'value'    => '',
            ],
            [
                'field'    => 'oil_level',
                'operator' => '=',
                'value'    => '',
            ],
            [
                'field'    => 'platform_code',
                'operator' => '=',
                'value'    => $this->data['auth_info_data']['role_code'],
            ]
        ]);
        if (empty($stationPriceInfo)) {
            $this->refundToTradeCenterForPayCallbackFailed($orderInfoModel, '站点不存在');
        }
        if (bccomp($stationPriceInfo['sale_price'], $stationPriceInfo['listing_price']) == 1) {
            (new DingDing())->saleGeGunForPrice([
                'platformName' => 'E站途',
                'stationName'  => $stationPriceInfo['station_name'],
                'oilInfo'      => "{$stationPriceInfo['oil_no']}{$stationPriceInfo['oil_type']}{$stationPriceInfo['oil_level']}",
                'gunPrice'     => $stationPriceInfo['listing_price'],
                'salePrice'    => $stationPriceInfo['sale_price'],
            ]);
            $this->refundToTradeCenterForPayCallbackFailed($orderInfoModel, '价格异常：结算价高于枪价');
        }
        if ($this->data['pushExtends']['price'] != $stationPriceInfo['sale_price']) {
            $this->refundToTradeCenterForPayCallbackFailed(
                $orderInfoModel,
                "结算价异常,无法下单。传入价格：" .
                "{$this->data['pushExtends']['price']}，当前价格：{$stationPriceInfo['sale_price']}"
            );
        }
        if ($this->data['pushExtends']['priceGun'] != $stationPriceInfo['listing_price']) {
            $this->refundToTradeCenterForPayCallbackFailed(
                $orderInfoModel,
                "枪价异常,无法下单。传入价格：" .
                "{$this->data['pushExtends']['priceGun']}，当前价格：{$stationPriceInfo['listing_price']}"
            );
        }

        //判断车牌号是否为空,为空填充约定的默认车牌号
        checkAndAssignmentEmptyAndNull($this->data['truck_no'], '京A01234');
        $genOrderData = [
            "salePrice"         => $this->data['supplier_price'],
            "cash"              => (int)bcmul($this->data['supplier_money'], 100),
            "amount"            => (int)bcmul($this->data['pushExtends']['amountGun'], 100),
            "mobilePhone"       => $this->data['drivertel'] ?? '15701599887',
            "plateNum"          => $this->data['truck_no'],
            "stationId"         => $stationPriceInfo['station_id'],
            "driverName"        => $this->data['driver_name'] ?? 'G7',
            "deviceType"        => 'wechat',
            "orderSerialNumber" => $this->data['id'],
        ];

        try {

            $orderData = EZTRequest::handle("consume", $genOrderData);
            OrderAssocData::updateOrderInfoByOrderId($this->data['id'], [
                'platform_order_status' => 1,
                'platform_order_id'     => $orderData['data']['orderId'],
            ]);
        } catch (Throwable $exception) {

            OrderAssocData::updateOrderInfoByOrderId($this->data['id'], [
                'platform_reason' => $exception->getMessage(),
            ]);

            if ($exception->getCode() == 5000999) {

                $this->refundToTradeCenterForPayCallbackFailed($orderInfoModel, $exception->getMessage());
            }
        }
    }
}
