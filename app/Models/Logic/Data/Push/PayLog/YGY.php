<?php


namespace App\Models\Logic\Data\Push\PayLog;

use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Exception;
use Request\YGY as YGYRequest;
use Throwable;
use Tool\Alarm\FeiShu;


class YGY extends ThirdParty
{
    /**
     * @return void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-12 15:31
     */
    public function handle()
    {
        $requestData = [];
        $extends = json_decode($this->data["data"]["extends"], true);
        $realExtends = $extends['extends'] ?? [];
        $requestData["qrcode"] = $realExtends["qrcode"];
        $requestData["driverId"] = $realExtends["driverId"];
        $requestData["stationId"] = $this->data["data"]["station_id"];
        $requestData["count"] = (float)$this->data["data"]["oil_num"];
        $requestData["price"] = (float)$this->data["data"]["price"];
        $requestData["amountPrice"] = (float)$this->data["data"]["mac_money"];
        $requestData["totalPrice"] = (float)$this->data["data"]["money"];
        $requestData["foreignUniqueId"] = $this->data["data"]["id"];
        $requestData["sourceKey"] = AuthConfigData::getAuthConfigValByName("YGY_SOURCE_KEY");
        $requestData["fuelNo"] = config(
            "oil.oil_mapping.ygy.mapping." . $this->data['data']['oil_name'] . '_' .
            $this->data['data']['oil_type'] . '_' . $this->data['data']['oil_level'],
            ""
        );
        $requestData["fuelName"] = $this->data['data']["oil_type_val"] . $this->data['data']["oil_name_val"] .
                                   $this->data['data']['oil_level_val'];
        $this->name_abbreviation = 'ygy';
        $orderAssocModel = $this->createOrder(
            2,
            2,
            $this->data['data']['id'],
            ''
        );
        $mainAuthInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation('ygy');
        $this->accessKey = $mainAuthInfo['access_key'];
        $this->secret = $mainAuthInfo['secret'];

        try {
            $result = YGYRequest::handle(
                "other/callback/orderInfo",
                $requestData,
                60,
                true
            );
        } catch (Throwable $exception) {
            $orderAssocModel->platform_reason = $exception->getMessage();
            $orderAssocModel->reason = '';
            $orderAssocModel->self_order_status = 4;
            $orderAssocModel->platform_order_status = 4;
            $orderAssocModel->save();
            throw $exception;
        }

        try {
            (new TradePaySimpleLogic([
                'auth_data'  => [
                    'access_key'        => $this->accessKey,
                    'secret'            => $this->secret,
                    'name_abbreviation' => $this->name_abbreviation,
                ],
                "trade_id"   => $this->data["data"]["id"],
                "order_id"   => $result["result"]["orderSn"],
                "pay_status" => 1,
            ]))->handle(true);
        } catch (Throwable $throwable) {
            (new FeiShu())->deductionMainAccountFailed([
                'platform_name' => '易管油',
                "station_name"  => $this->data["data"]["station_name"],
                'oil'           => $this->data["data"]["oil_type_val"] . $this->data["data"]["oil_name_val"] .
                                   $this->data["data"]["oil_level_val"],
                "price"         => $this->data["data"]["price"],
                "oil_num"       => $this->data["data"]["oil_num"],
                "money"         => $this->data["data"]["money"],
                "plate_number"  => $this->data["data"]["truck_no"] ?? '',
                "reason"        => $throwable->getMessage(),
                "org_code"      => $this->data["data"]["orgcode"],
                "card_no"       => $this->data["data"]["card_no"],
            ]);
            YGYRequest::handle("other/callback/cancelOrder", [
                'orderSn'   => $result["result"]["orderSn"],
                'sourceKey' => AuthConfigData::getAuthConfigValByName("YGY_SOURCE_KEY"),
            ], 60, true);
            if ($throwable->getCode() == 5000999) {
                throw new Exception($throwable->getMessage(), 5000888);
            }
            throw $throwable;
        }
    }
}
