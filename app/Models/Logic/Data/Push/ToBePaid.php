<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: zdl
 * Date: 2019-01-28
 * Time: 15:22
 */

namespace App\Models\Logic\Data\Push;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Logic\Base;
use App\Models\Logic\Data\Push\ToBePaid\AD;
use App\Models\Logic\Data\Push\ToBePaid\AJSW;
use App\Models\Logic\Data\Push\ToBePaid\BBSP;
use App\Models\Logic\Data\Push\ToBePaid\BDT_ORG;
use App\Models\Logic\Data\Push\ToBePaid\BQ;
use App\Models\Logic\Data\Push\ToBePaid\CFHY;
use App\Models\Logic\Data\Push\ToBePaid\CFT;
use App\Models\Logic\Data\Push\ToBePaid\CIEC;
use App\Models\Logic\Data\Push\ToBePaid\DESP2A6LA9;
use App\Models\Logic\Data\Push\ToBePaid\DESP2C637M;
use App\Models\Logic\Data\Push\ToBePaid\DESP2H8BBD;
use App\Models\Logic\Data\Push\ToBePaid\GBDW;
use App\Models\Logic\Data\Push\ToBePaid\HG;
use App\Models\Logic\Data\Push\ToBePaid\HR;
use App\Models\Logic\Data\Push\ToBePaid\HSL;
use App\Models\Logic\Data\Push\ToBePaid\HZ;
use App\Models\Logic\Data\Push\ToBePaid\JF;
use App\Models\Logic\Data\Push\ToBePaid\JTXY;
use App\Models\Logic\Data\Push\ToBePaid\KY;
use App\Models\Logic\Data\Push\ToBePaid\LHYS;
use App\Models\Logic\Data\Push\ToBePaid\LT;
use App\Models\Logic\Data\Push\ToBePaid\MB;
use App\Models\Logic\Data\Push\ToBePaid\MY;
use App\Models\Logic\Data\Push\ToBePaid\MYCF;
use App\Models\Logic\Data\Push\ToBePaid\PCKJ;
use App\Models\Logic\Data\Push\ToBePaid\QDMY;
use App\Models\Logic\Data\Push\ToBePaid\RQ;
use App\Models\Logic\Data\Push\ToBePaid\RRS;
use App\Models\Logic\Data\Push\ToBePaid\RY;
use App\Models\Logic\Data\Push\ToBePaid\SHENGMAN;
use App\Models\Logic\Data\Push\ToBePaid\SM;
use App\Models\Logic\Data\Push\ToBePaid\WSY;
use App\Models\Logic\Data\Push\ToBePaid\WZYT;
use App\Models\Logic\Data\Push\ToBePaid\XM;
use App\Models\Logic\Data\Push\ToBePaid\XYDS;
use App\Models\Logic\Data\Push\ToBePaid\YBT;
use App\Models\Logic\Data\Push\ToBePaid\YGJ;
use App\Models\Logic\Data\Push\ToBePaid\YLZ;
use App\Models\Logic\Data\Push\ToBePaid\YXT;
use App\Models\Logic\Data\Push\ToBePaid\ZEY;
use App\Models\Logic\Data\Push\ToBePaid\ZJ;
use App\Models\Logic\Data\Push\ToBePaid\ZJKA;
use App\Models\Logic\Data\Push\ToBePaid\ZLGX;
use App\Models\Logic\Data\Push\ToBePaid\ZZ;
use App\Models\Logic\Data\Push\ToBePaid\ZZ_AH;
use App\Models\Logic\Data\Push\ToBePaid\ZZ_BJ;
use App\Models\Logic\Data\Push\ToBePaid\ZZ_TJ;
use Exception;
use Illuminate\Http\JsonResponse;
use Request\FOSS as FOSSRequest;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;


class ToBePaid extends Base
{
    private $workClassMapping = [
        'bbsp'        => BBSP::class,
        'bdtOrg'      => BDT_ORG::class,
        'ygj'         => YGJ::class,
        'hg'          => HG::class,
        'lhys'        => LHYS::class,
        'zey'         => ZEY::class,
        'sm'          => SM::class,
        'pckj'        => PCKJ::class,
        'rq'          => RQ::class,
        'zlgx'        => ZLGX::class,
        'xm'          => XM::class,
        'jf'          => JF::class,
        'ajsw'        => AJSW::class,
        'ciec'        => CIEC::class,
        'zz'          => ZZ::class,
        'desp|2H8BBD' => DESP2H8BBD::class,
        'desp|2A6LA9' => DESP2A6LA9::class,
        'yxt'         => YXT::class,
        'hr'          => HR::class,
        'desp|2C637M' => DESP2C637M::class,
        'gbdw'        => GBDW::class,
        'zj'          => ZJ::class,
        'my'          => MY::class,
        'wsy'         => WSY::class,
        'bq'          => BQ::class,
        'zzah'        => ZZ_AH::class,
        'zzbj'        => ZZ_BJ::class,
        'zztj'        => ZZ_TJ::class,
        'cft'         => CFT::class,
        'mb'          => MB::class,
        'ad'          => AD::class,
        'lt'          => LT::class,
        'cfhy'        => CFHY::class,
        'zjka'        => ZJKA::class,
        'jtxy'        => JTXY::class,
        'ylzZj'       => YLZ::class,
        'mycf'        => MYCF::class,
        'shengman'    => SHENGMAN::class,
        'ry'          => RY::class,
        'ybt'         => YBT::class,
        'hz'          => HZ::class,
        'rrs'         => RRS::class,
        'xyds'        => XYDS::class,
        'qdmy'        => QDMY::class,
        'ky'          => KY::class,
        'hsl'         => HSL::class,
        'wzyt'        => WZYT::class,
    ];

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/29 3:47 下午
     */
    public function handle()
    {
        $realAuthInfo = AuthInfoData::getAuthInfoByRoleCode([$this->data['orderData']["orgcode"]], true);
        $realNameAbbreviation = explode('_', $realAuthInfo['name_abbreviation'])[0];

        if (empty($realAuthInfo)) {
            return responseFormat(5000007, [
                'authInfo' => $realAuthInfo,
            ]);
        }

        if ($realNameAbbreviation != $realAuthInfo['name_abbreviation']) {
            $mainAuthInfo = AuthInfoData::getAuthInfoFieldByNameAbbreviation($realNameAbbreviation);
        } else {
            $mainAuthInfo = $realAuthInfo;
        }

        if (empty($mainAuthInfo)) {
            return responseFormat(5000007, [
                'authInfo'     => $realAuthInfo,
                'mainAuthInfo' => $mainAuthInfo
            ]);
        }

        if (!isset($this->workClassMapping[$realNameAbbreviation])) {
            return responseFormat(5000007, [
                'authInfo'     => $realAuthInfo,
                'mainAuthInfo' => $mainAuthInfo
            ]);
        }

        $data = FOSSRequest::handle('gas.org_account.account_balance', [
            'vice_no'       => $realAuthInfo['card_no'],
            'isCardBalance' => 1,
        ]);

        if (bccomp($data['data']['use_balance'], $this->data['money'], 2) == -1) {
            throw new Exception("", 5000012);
        }

        $stationInfo = FOSS_STATIONRequest::handle("v1/station/getStationById", [
            'id' => $this->data["gasStationId"],
        ]);
        foreach ($stationInfo['data']['stationInfo']["price_list"] as $v) {
            $v['oil_level_val'] = $v['oil_level_val'] ?? '';
            $v['oil_type_val'] = $v['oil_type_val'] ?? '';

            if ($v['oil_name_val'] == $this->data['gas_oil_name'] and
                $v['oil_type_val'] == $this->data['gas_oil_type'] and
                $v['oil_level_val'] == $this->data['gas_oil_level']) {
                $this->data['realOilNum'] = bcdiv($this->data['money'], $v['price'], 4);
                $this->data['realPrice'] = $v['price'];
                $this->data['realMacPrice'] = $v['mac_price'];
            }
        }

        if (!isset($this->data['realOilNum'])) {
            $this->data['realOilNum'] = $this->data['oil_num'];
            $this->data['realPrice'] = $this->data['price'];
            $this->data['realMacPrice'] = $this->data['price'];
        }
        if (isset($this->data['mac_money'])) {
            $this->data['realMacMoney'] = $this->data['mac_money'];
        }
        $this->data['auth_data'] = $mainAuthInfo;
        $work = (new $this->workClassMapping[$realNameAbbreviation]($this->data));
        $work->handle();
        return responseFormat();
    }
}
