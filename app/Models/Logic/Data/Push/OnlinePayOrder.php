<?php
/**
 * Created by <PERSON><PERSON><PERSON>torm.
 * User: zdl
 * Date: 2019-01-28
 * Time: 15:22
 */

namespace App\Models\Logic\Data\Push;


use App\Models\Data\AuthInfo;
use App\Models\Logic\Base;
use App\Models\Logic\Data\Push\OnlinePayOrder\CY;
use App\Models\Logic\Data\Push\OnlinePayOrder\CZB;
use App\Models\Logic\Data\Push\OnlinePayOrder\HYT;
use App\Models\Logic\Data\Push\OnlinePayOrder\JQ;
use App\Models\Logic\Data\Push\OnlinePayOrder\JT;
use App\Models\Logic\Data\Push\OnlinePayOrder\MJ;
use App\Models\Logic\Data\Push\OnlinePayOrder\XYN;
use App\Models\Logic\Data\Push\OnlinePayOrder\ZDC;
use Illuminate\Http\JsonResponse;
use Throwable;


class OnlinePayOrder extends Base
{
    private $jobMapping = [
        'czb'    => CZB::class,
        'hyt'    => HYT::class,
        'czb_sq' => CZB::class,
        'cy'     => CY::class,
        'zdc'    => ZDC::class,
        'xyn'    => XYN::class,
        'jt'     => JT::class,
        'mj'     => MJ::class,
        'jq'     => JQ::class,
    ];

    /**
     * @var CZB
     */
    private $worker = null;

    /**
     * RefuelingStatement constructor.
     * @param array $parameter
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);

        $authInfo = AuthInfo::getAuthInfoByRoleCode($this->data["role_code"]);
        if (!empty($authInfo)) {
            if (isset($this->jobMapping[$authInfo[0]['name_abbreviation']])) {
                //把授权信息数据存入真实数据
                $this->data['data']['auth_info_data'] = $authInfo[0];
                $this->worker = (new $this->jobMapping[$authInfo[0]['name_abbreviation']]($this->data));
            } else {
                responseFormat(0, [], true, "非法请求");
            }
        } else {
            responseFormat(0, [], true, "非法请求");
        }
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-08-22 18:02
     */
    public function handle(): JsonResponse
    {
        $this->worker->handle();
        return responseFormat(0, []);
    }
}
