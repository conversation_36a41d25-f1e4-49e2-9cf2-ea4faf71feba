<?php


namespace App\Models\Logic\Tool;


use App\Console\Commands\PullOilForBdt;
use App\Console\Commands\PullOilForCy;
use App\Console\Commands\PullOilForCzb;
use App\Console\Commands\PullOilForDt;
use App\Console\Commands\PullOilForEzt;
use App\Console\Commands\PullOilForGaoDeng;
use App\Console\Commands\PullOilForGB;
use App\Console\Commands\PullOilForHsy;
use App\Console\Commands\PullOilForHyt;
use App\Console\Commands\PullOilForJQ;
use App\Console\Commands\PullOilForJT;
use App\Console\Commands\PullOilForMj;
use App\Console\Commands\PullOilForMtlSy;
use App\Console\Commands\PullOilForSaic;
use App\Console\Commands\PullOilForSg;
use App\Console\Commands\PullOilForSh;
use App\Console\Commands\PullOilForShSx;
use App\Console\Commands\PullOilForSqzl;
use App\Console\Commands\PullOilForSqZsh;
use App\Console\Commands\PullOilForTBJX;
use App\Console\Commands\PullOilForWjy;
use App\Console\Commands\PullOilForXYN;
use App\Console\Commands\PullOilForYunDaTong;
use App\Console\Commands\PullOilForZdc;
use App\Console\Commands\PullOilForZhyk;
use App\Console\Commands\PullOilForZwl;
use App\Console\Commands\PullOilForZy;
use App\Console\Commands\PullPriceForCzb;
use App\Console\Commands\PullPriceForGS;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Queue;
use Throwable;

class PullOilStation
{
    private static $jobMapping    = [
        'sg'        => [
            PullOilForSg::class,
        ],
        'bdt'       => [
            PullOilForBdt::class,
        ],
        'czb'       => [
            PullOilForCzb::class,
            PullPriceForCzb::class,
        ],
        'zy'        => [
            PullOilForZy::class,
        ],
        'ezt'       => [
            PullOilForEzt::class,
        ],
        'gs'        => [
            PullPriceForGS::class,
        ],
        'hyt'       => [
            PullOilForHyt::class,
        ],
        'wjy'       => [
            PullOilForWjy::class,
        ],
        'saic_aj'   => [
            PullOilForSaic::class,
        ],
        'gb'        => [
            PullOilForGB::class,
        ],
        'saic_fl'   => [
            PullOilForSaic::class,
        ],
        'dt_zj'     => [
            PullOilForDt::class,
        ],
        'saic_ajsw' => [
            PullOilForSaic::class,
        ],
        'cy'        => [
            PullOilForCy::class,
        ],
        'saic_yc'   => [
            PullOilForSaic::class,
        ],
        'hsy'       => [
            PullOilForHsy::class,
        ],
        'sqzl'      => [
            PullOilForSqzl::class,
        ],
        'gb_tj'     => [
            PullOilForGB::class,
        ],
        'zhyk'      => [
            PullOilForZhyk::class,
        ],
        'zdc'       => [
            PullOilForZdc::class,
        ],
        'zwl'       => [
            PullOilForZwl::class,
        ],
        'xyn'       => [
            PullOilForXYN::class,
        ],
        'sqZsh'     => [
            PullOilForSqZsh::class,
        ],
        'mtlSy'     => [
            PullOilForMtlSy::class,
        ],
        'sh'        => [
            PullOilForSh::class,
        ],
        'shsx'        => [
            PullOilForShSx::class,
        ],
        'jt'        => [
            PullOilForJT::class,
        ],
        'mj'        => [
            PullOilForMj::class,
        ],
        'yundatong' => [
            PullOilForYunDaTong::class,
        ],
        'gaodeng' => [
            PullOilForGaoDeng::class,
        ],
        'tbjx'  => [
            PullOilForTBJX::class,
        ],
        'jq'  => [
            PullOilForJQ::class,
        ],
    ];
    private static $jobParameters = [
        'saic_aj'   => [
            '--supplier_identifier' => 'aj',
        ],
        'saic_fl'   => [
            '--supplier_identifier' => 'fl',
        ],
        'dt_zj'     => [
            '--provinceNameAbbreviation' => 'zj',
        ],
        'saic_ajsw' => [
            '--supplier_identifier' => 'ajsw',
        ],
        'saic_yc'   => [
            '--supplier_identifier' => 'yc',
        ],
        'gb'        => [
            '--name_abbreviation' => 'gb',
        ],
        'gb_tj'     => [
            '--name_abbreviation' => 'gb_tj',
        ],
    ];
    private        $nameAbbreviation;

    /**
     * PullOilStation constructor.
     * @param string $nameAbbreviation
     * @throws Exception
     */
    public function __construct(string $nameAbbreviation)
    {
        if (!isset(self::$jobMapping[$nameAbbreviation])) {
            throw new Exception("", 4120097);
        }

        $this->nameAbbreviation = $nameAbbreviation;
    }

    public static function checkHasJobMapped(string $nameAbbreviation): bool
    {
        return isset(self::$jobMapping[$nameAbbreviation]);
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/2/4 3:31 下午
     */
    public function handle()
    {
        if (isset(self::$jobMapping[$this->nameAbbreviation])) {
            foreach (self::$jobMapping[$this->nameAbbreviation] as $jobClass) {
                $worker = new $jobClass();

                if ($worker instanceof ShouldQueue) {
                    if (isset(self::$jobParameters[$this->nameAbbreviation])) {
                        Queue::push(new $jobClass(self::$jobParameters[$this->nameAbbreviation]));
                        continue;
                    }
                    Queue::push(new $jobClass());
                    continue;
                }
                if (isset(self::$jobParameters[$this->nameAbbreviation])) {
                    Artisan::call($jobClass, self::$jobParameters[$this->nameAbbreviation]);
                    continue;
                }
                Artisan::call($jobClass);
            }
        }

        return responseFormat();
    }
}
