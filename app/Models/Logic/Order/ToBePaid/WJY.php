<?php


namespace App\Models\Logic\Order\ToBePaid;


use App\Models\Data\Log\ResponseLog;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\ToBePaid;
use Illuminate\Http\JsonResponse;
use Request\FOSS_ORDER as FOSS_ORDERRequest;
use Throwable;


class WJY extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/31 10:14 上午
     */
    public function handle(): JsonResponse
    {
        try {
            $this->data['gasStationId'] = $this->getGasStationIdByAppStationIdAndPCode($this->data['oilsStationId']);
        } catch (Throwable $exception) {
            return responseFormatForWjy(5000999, [], $exception->getMessage());
        }

        $cardData = json_decode(app('redis')->get($this->data['foreign_driver_id']), true);
        if (!$cardData) {
            ResponseLog::handle([
                'data' => $this->data,
                'msg'  => '二维码解析失败',
            ]);
            return responseFormatForWjy(5000001);
        }

        if (empty($oilStr = config("oil.oil_mapping.wjy.{$this->data['fuelNo']}", ""))) {
            return responseFormatForWjy(5000116);
        }
        $oilData = explode('_', $oilStr);
        $this->data['oil_name'] = $oilData[0];
        $this->data['oil_type'] = $oilData[1];
        $this->data['oil_level'] = $oilData[2];
        $genOrderData = [
            'card_no'         => $cardData['card_no'],
            'app_station_id'  => $this->data['oilsStationId'],
            'oil_num'         => $this->data['count'],
            'oil_price'       => $this->data['price'],
            'oil_money'       => $this->data['totalPrice'],
            'pay_money'       => $this->data['totalPrice'],
            'oil_type'        => $this->data['oil_type'],
            'oil_name'        => $this->data['oil_name'],
            'oil_level'       => $this->data['oil_level'],
            'third_order_id'  => $this->data['orderSn'],
            'order_no'        => $cardData['order_no'],
            'pcode'           => $this->data['auth_data']['role_code'],
            'driver_source'   => $cardData['is_self'] ? 1 : 2,
            'client_type'     => $cardData['qr_code_source'],
            'driver_phone'    => $cardData['driver_phone'] ?? '',
            'driver_name'     => $cardData['driver_name'] ?? '',
            'truck_no'        => $cardData['truck_no'] ?? '',
            'trade_mode'      => $cardData['is_self'] ? 25 : 40,
            'ocr_truck_no_id' => $cardData['ocr_truck_no_id'] ?? '',
        ];
        try {
            $orderAssocModel = OrderAssocData::insert(
                2,
                2,
                '',
                $this->data['orderSn'],
                $this->name_abbreviation,
                '',
                '',
                json_encode([
                    'either' => $this->data,
                ])
            );
            $orderData = FOSS_ORDERRequest::handle('/api/oil_adapter/makeOrder', $genOrderData);
        } catch (Throwable $exception) {
            OrderAssocData::updateOrderInfoByOrderId($this->data['orderSn'], [
                "reason" => $exception->getMessage(),
            ], false, $this->name_abbreviation);
            return responseFormatForWjy(5000999, [], false, $exception->getMessage());
        }

        $orderAssocModel->reason = $orderData['msg'];
        $orderAssocModel->self_order_id = $orderData['data']['order_id'];
        $orderAssocModel->save();
        $this->data['gas_oil_type'] = config("oil.oil_no_simple.{$this->data['oil_type']}", "");
        $this->data['gas_oil_name'] = array_flip(config("oil.oil_type"))[$this->data['oil_name']] ?? '';
        $this->data['gas_oil_level'] = array_flip(config("oil.oil_level"))[$this->data['oil_level']] ?? '';
        $this->data['id'] = $orderData['data']['order_id'];
        $this->data['money'] = $this->data['totalPrice'];
        $this->data['extends'] = $cardData['extends'];
        $this->data['orderData'] = $orderData['data'];
        $this->data['oil_num'] = $this->data['count'];
        $this->data['access_key'] = $cardData['access_key'] ?? '';
        $this->data['secret'] = $cardData['secret'] ?? '';
        $this->data['name_abbreviation'] = $cardData['name_abbreviation'] ?? '';
        $this->data['mac_money'] = $this->data['amountPrice'];
        if (isset($cardData['is_self']) and !$cardData['is_self']) {
            try {
                (new ToBePaid($this->data))->handle();
            } catch (Throwable $exception) {
                ResponseLog::handle([
                    'exception' => $exception
                ]);
                return responseFormatForWjy(5000001);
            }
        }

        return responseFormatForWjy(0, [
            'foreign_unique_id' => $orderData['data']['order_id'],
        ]);
    }
}
