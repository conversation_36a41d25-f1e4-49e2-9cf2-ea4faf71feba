<?php


namespace App\Models\Logic\Order\ToBePaid\Downstream;


use App\Jobs\QueryOrderToThirdParty;
use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Common as CommonData;
use App\Models\Data\CompanyMapping as CompanyMappingData;
use App\Models\Data\OilMapping\Basic as BasicOilMapping;
use App\Models\Data\OilMapping\YGY as YGYOilMapping;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Data\Push\AutonomousOrder;
use App\Models\Logic\Order\ToBePaid\ThirdParty;
use App\Models\Logic\Trade\Pay\Simple as TradePaySimpleLogic;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Queue;
use Request\FOSS as FOSSRequest;
use Request\FOSS_ORDER as FOSS_ORDERequest;
use Request\FOSS_STATION as FOSS_STATIONRequest;
use Throwable;


class YGY extends ThirdParty
{
    /**
     * @return JsonResponse
     * @throws Exception|Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/12/31 11:22 上午
     */
    public function handle(): JsonResponse
    {
        $companyInfo = CompanyMappingData::getCompanyByWhere([
            [
                'field'    => 'platform_company_no',
                'operator' => '=',
                'value'    => $this->data['companyId'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '=',
                'value'    => 'ygy',
            ],
        ], ['company_no'], true, true);
        if (empty($companyInfo)) {
            return responseFormatForYgy(5000119);
        }
        $authInfo = AuthInfoData::getAuthInfoByRoleCode($companyInfo->company_no, true);
        $stationInfo = FOSS_STATIONRequest::handle("v1/station/getStationById", [
            'id' => $this->data['stationId']
        ])['data']['stationInfo'] ?? [];
        $stationInfo['push_target_code'] = $companyInfo->company_no;
        YGYOilMapping::getAvailableOil($stationInfo['price_list']);
        BasicOilMapping::getSpecialPrice($stationInfo);
        $oilName = $oilType = $oilLevel = "";
        foreach ($stationInfo['price_list'] as $v) {
            if (config(
                    "oil.oil_mapping.ygy.mapping.{$v['oil_name']}_{$v['oil_type']}_{$v['oil_level']}",
                    ""
                ) == $this->data["fuelNo"]) {
                $oilName = $v['oil_name'];
                $oilType = $v['oil_type'];
                $oilLevel = $v['oil_level'];
            }
        }
        if (!in_array($stationInfo['trade_type'], CommonData::RECEIVE_TRADE_FOR_DOWNSTREAM_BY_TRADE_ENUM)) {
            return responseFormatForYgy(5000117);
        }
        $selfSuppliers = array_keys(
            FOSSRequest::handle('g7s.station.getCanPcode', [
                'is_self_operated' => 1,
            ])['data']
        );
        $autonomousOrderObj = (new AutonomousOrder([
            'data' => [
                'pcode' => $stationInfo['pcode'],
            ],
        ], false));
        $response = FOSS_ORDERequest::handle('/api/oil_adapter/makeOrder', [
            'card_no'        => $authInfo['card_no'],
            'driver_name'    => $this->data['driverName'] ?? '',
            'driver_phone'   => $this->data['driverPhone'],
            'truck_no'       => $this->data['carNo'] ?? '',
            'station_id'     => $this->data['stationId'],
            'oil_name'       => $oilName,
            'oil_type'       => $oilType,
            'oil_level'      => $oilLevel,
            'oil_num'        => $this->data['count'],
            'oil_price'      => $this->data['discountPrice'],
            'oil_money'      => $this->data['totalPrice'],
            'pay_money'      => $this->data['totalPrice'],
            'third_order_id' => $this->data['orderSn'],
            'trade_mode'     => in_array(
                $stationInfo['pcode'],
                $selfSuppliers
            ) ? 10 : ($autonomousOrderObj->checkWorkerExists() ? 32 : 30),
            'driver_source'  => 2,
            'priceGun'       => $this->data['price'],
            'amountGun'      => $this->data['amountPrice'],
            'gunNumber'      => (int)$this->data['gunNumber'] ?? 0,
            'oil_unit'       => $this->data['deductionMode'],
        ]);
        OrderAssocData::insert(
            2,
            2,
            $response['data']['order_id'],
            $this->data['orderSn'],
            $this->name_abbreviation,
            "",
            "",
            json_encode(
                array_merge($response['data'], [
                    'trade_type'    => $stationInfo['trade_type'],
                    'supplier_code' => $stationInfo['pcode'],
                    'orgcode'       => $authInfo['role_code'],
                ])
            )
        );
        $tradeResponse = (new TradePaySimpleLogic([
            'auth_data'            => $this->data['auth_data'],
            "trade_id"             => $response["data"]["order_id"],
            "order_id"             => $this->data["orderSn"],
            "pay_status"           => 1,
            "pay_reason"           => '',
            "updatePlatformReason" => false,
        ]))->handle(true);
        Queue::later(
            Carbon::now()->addSeconds(1),
            new QueryOrderToThirdParty([
                'platform_order_id' => $this->data['orderSn'],
                'name_abbreviation' => $this->name_abbreviation,
                'pay_status'        => 1,
            ]),
            '',
            'adapter_deal_trade'
        );
        return responseFormatForYgy(0, [
            'foreignUniqueId'               => $response['data']['order_id'],
            'orderStatus'                   => 1,
            'show_verification_certificate' => $tradeResponse->getData(true)['data']['show_verification_certificate'],
        ]);
    }
}
