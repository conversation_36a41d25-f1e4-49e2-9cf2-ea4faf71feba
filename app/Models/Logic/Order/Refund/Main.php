<?php


namespace App\Models\Logic\Order\Refund;


use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\OrderAssoc as OrderAssocData;
use App\Models\Logic\Base;
use App\Models\Logic\Order\Refund\Check\Main as RefundCheckMain;
use Illuminate\Http\JsonResponse;
use Throwable;


class Main extends Base
{
    private $workerMapping = [
        'fy'          => FY::class,
        'hljh'        => HLJH::class,
        'lhys'        => LHYS::class,
        'hg'          => HG::class,
        'czb'         => CZB::class,
        'yb'          => YB::class,
        'sm'          => SM::class,
        'dd'          => DIANDI::class,
        'jdwc'        => JDWC::class,
        'mk'          => MK::class,
        'xc'          => XC::class,
        'dde'         => DDE::class,
        'tc'          => TC::class,
        'sq'          => SQ::class,
        'myb'         => MYB::class,
        'cn'          => CN::class,
        'sp'          => SP::class,
        'SPTXXTZ'     => SP::class,
        'SPT5CWE'     => SP::class,
        'SPT3SB7'     => SP::class,
        'SPTLHLX'     => SP::class,
        'SPTCDNF'     => SP::class,
        'SPT76YH'     => SP::class,
        'SPT5GZG'     => SP::class,
        'SPTMKCF'     => SP::class,
        'SPTHZTD'     => SP::class,
        'SPTG7PR'     => SP::class,
        'SPTRR3N'     => SP::class,
        'SPTNGHH'     => SP::class,
        'SPTYW4A'     => SP::class,
        'SPTHSQW'     => SP::class,
        'SPT5RLT'     => SP::class,
        'zey'         => ZEY::class,
        'lf'          => LF::class,
        'hyjy'        => HYJY::class,
        'hytOrg'      => HYT_ORG::class,
        'chtx'        => CHTX::class,
        'pckj'        => PCKJ::class,
        'rq'          => RQ::class,
        'zlgx'        => ZLGX::class,
        'xm'          => XM::class,
        'jf'          => JF::class,
        'sffy'        => SFFY::class,
        'ajsw'        => AJSW::class,
        'ygy'         => YGY::class,
        'ciec'        => CIEC::class,
        'zz'          => ZZ::class,
        'desp|2H8BBD' => DESP2H8BBD::class,
        'desp|2A6LA9' => DESP2A6LA9::class,
        'yxt'         => YXT::class,
        'hr'          => HR::class,
        'gbdw'        => GBDW::class,
        'desp|2C637M' => DESP2C637M::class,
        'my'          => MY::class,
        'zj'          => ZJ::class,
        'hll'         => HLL::class,
        'wsy'         => WSY::class,
        'bq'          => BQ::class,
        'zzah'        => ZZ_AH::class,
        'zzbj'        => ZZ_BJ::class,
        'zztj'        => ZZ_TJ::class,
        'cft'         => CFT::class,
        'mb'          => MB::class,
        'ad'          => AD::class,
        'cfhy'        => CFHY::class,
        'ylz'         => YLZ::class,
        'ylzZj'       => YLZ::class,
        'zjka'        => ZJKA::class,
        'jtxy'        => JTXY::class,
        'mycf'        => MYCF::class,
        'shengman'    => SHENGMAN::class,
        'ry'          => RY::class,
        'ybt'         => YBT::class,
        'hz'          => HZ::class,
        'rrs'         => RRS::class,
        'xyds'        => XYDS::class,
        'qdmy'        => QDMY::class,
        'ky'          => KY::class,
    ];

    private $notCheckOrderStatusPlatform = [
        'ad'
    ];

    private $asyncRefundPlatform = [
        'ygy',
        'ygyDemo',
    ];

    /**
     * @var MYB
     */
    private $worker;

    /**
     * Main constructor.
     * @param array $parameter
     * @return void|JsonResponse
     * @throws Throwable
     */
    public function __construct(array $parameter)
    {
        parent::__construct($parameter);
        if (isset($parameter['skip_init']) and $parameter['skip_init']) {
            return;
        }
        $this->data['orderAssocData'] = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'id',
                'operator' => '=',
                'value'    => $this->data['id'],
            ],
        ], ['*']);
        if (!in_array($this->data['orderAssocData']['platform_name'], $this->notCheckOrderStatusPlatform)) {
            if (!$this->data['orderAssocData'] or ($this->data['orderAssocData']['self_order_status'] != 1 and
                                                   $this->data['orderAssocData']['platform_order_status'] != 1)) {
                responseFormat(5000014, [], true);
            }
        }
        if (!isset($this->workerMapping[$this->data['orderAssocData']['platform_name']])) {
            return responseFormat(5000121, [], true);
        }
        $orderAssocData = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['orderAssocData']['self_order_id'],
            ],
            [
                'field'    => 'platform_name',
                'operator' => '!=',
                'value'    => $this->data['orderAssocData']['platform_name'],
            ],
        ], ['*'], false, true);
        if ($orderAssocData->count() > 1) {
            foreach ($orderAssocData as $v) {
                (new RefundCheckMain([
                    'self_order_id' => $v->self_order_id,
                    'platform_name' => $v->platform_name,
                ]))->handle();
            }
        }
        $this->data['auth_data'] = AuthInfoData::getAuthInfoFieldByNameAbbreviation(
            $this->data['orderAssocData']['platform_name']
        );
        $this->worker = new $this->workerMapping[$this->data['orderAssocData']['platform_name']]($this->data);
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/8 4:37 下午
     */
    public function handle(): JsonResponse
    {
        $this->worker->handle();
        if (!in_array($this->data['orderAssocData']['platform_name'], $this->asyncRefundPlatform)) {
            OrderAssocData::updateOrderInfoById($this->data['id'], [
               'platform_order_status' => 3,
            ]);
        }
        return responseFormat();
    }

    public function refundCustomerForOrderCenter(): JsonResponse
    {
        $authInfoData = AuthInfoData::getAuthInfoByWhere([
            [
                'field'    => 'role_code',
                'operator' => '=',
                'value'    => $this->data['org_code'],
            ],
        ], ['*'], true, true);
        $realNameAbbreviation = explode('_', $authInfoData->name_abbreviation)[0];
        $authInfoData = AuthInfoData::getAuthInfoFieldByNameAbbreviation($realNameAbbreviation, ['*'], true);
        if (!isset($this->workerMapping[$authInfoData->name_abbreviation])) {
            return responseFormat(5000121, [], true);
        }
        $orderAssocData = OrderAssocData::getOrderInfoByWhere([
            [
                'field'    => 'self_order_id',
                'operator' => '=',
                'value'    => $this->data['order_id'],
            ],
        ], ['*'], false, true);
        if ($orderAssocData->count() > 1) {
            $authInfos = AuthInfoData::getAuthInfoByWhere([
                [
                    'field'    => 'name_abbreviation',
                    'operator' => 'in',
                    'value'    => $orderAssocData->pluck('platform_name')->toArray(),
                ],
                [
                    'field'    => 'role',
                    'operator' => '=',
                    'value'    => 7,
                ]
            ], ['name_abbreviation'], false, true)->keyBy('name_abbreviation');
            foreach ($orderAssocData as $k => $v) {
                if (isset($authInfos[$v->platform_name])) {
                    (new RefundCheckMain([
                        'self_order_id' => $v->self_order_id,
                        'platform_name' => $v->platform_name,
                    ]))->handle();
                    unset($orderAssocData[$k]);
                }
            }
        }
        (new $this->workerMapping[$authInfoData->name_abbreviation](
            array_merge(
                $orderAssocData->first()->toArray(),
                [
                    'orderAssocData' => $orderAssocData->first()->toArray(),
                    'refund_remark'  => $this->data['reason'] ?? '',
                    'auth_data'      => $authInfoData->toArray(),
                ]
            )
        ))->handle();
        if (!in_array($authInfoData->name_abbreviation, $this->asyncRefundPlatform)) {
            OrderAssocData::updateOrderInfoById($orderAssocData->first()->id, [
                'platform_order_status' => 3,
            ]);
        }
        return responseFormat(0, []);
    }
}
