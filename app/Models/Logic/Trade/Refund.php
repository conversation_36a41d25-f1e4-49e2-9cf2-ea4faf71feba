<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-16
 * Time: 15:34
 */

namespace App\Models\Logic\Trade;


use App\Models\Logic\Base;
use App\Models\Logic\Trade\Refund\CY;
use App\Models\Logic\Trade\Refund\CZB;
use App\Models\Logic\Trade\Refund\DIANDI;
use App\Models\Logic\Trade\Refund\GAODENG;
use App\Models\Logic\Trade\Refund\GB;
use App\Models\Logic\Trade\Refund\GDQP;
use App\Models\Logic\Trade\Refund\HSY;
use App\Models\Logic\Trade\Refund\HYT;
use App\Models\Logic\Trade\Refund\JQ;
use App\Models\Logic\Trade\Refund\JT;
use App\Models\Logic\Trade\Refund\MTLSY;
use App\Models\Logic\Trade\Refund\SC;
use App\Models\Logic\Trade\Refund\SG;
use App\Models\Logic\Trade\Refund\SP;
use App\Models\Logic\Trade\Refund\SQZL;
use App\Models\Logic\Trade\Refund\SQZSH;
use App\Models\Logic\Trade\Refund\TBJX;
use App\Models\Logic\Trade\Refund\WJY;
use App\Models\Logic\Trade\Refund\XY;
use App\Models\Logic\Trade\Refund\XYN;
use App\Models\Logic\Trade\Refund\YUNDATONG;
use App\Models\Logic\Trade\Refund\ZDC;
use App\Models\Logic\Trade\Refund\ZHYK;
use App\Models\Logic\Trade\Refund\ZY;
use Illuminate\Http\JsonResponse;
use Throwable;

class Refund extends Base
{
    private $workMapping = [
        'sg'        => SG::class,
        'czb'       => CZB::class,
        'dd'        => DIANDI::class,
        'zy'        => ZY::class,
        'sc'        => SC::class,
        'sp'        => SP::class,
        'xy'        => XY::class,
        'hyt'       => HYT::class,
        'wjy'       => WJY::class,
        'gb'        => GB::class,
        'gb_tj'     => GB::class,
        'cy'        => CY::class,
        'hsy'       => HSY::class,
        'sqzl'      => SQZL::class,
        'gdqp'      => GDQP::class,
        'zhyk'      => ZHYK::class,
        'zdc'       => ZDC::class,
        'jq'        => JQ::class,
        'xyn'       => XYN::class,
        'sqZsh'     => SQZSH::class,
        'mtlSy'     => MTLSY::class,
        'jt'        => JT::class,
        'yundatong' => YUNDATONG::class,
        'gaodeng'   => GAODENG::class,
        'tbjx'      => TBJX::class,
    ];

    /**
     * @return JsonResponse|void
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/10/11 2:41 下午
     */
    public function handle()
    {
        $worker = new $this->workMapping[$this->name_abbreviation]($this->data);
        return $worker->handle();
    }
}
