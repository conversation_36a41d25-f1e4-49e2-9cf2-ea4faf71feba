<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;
use App\Models\MySQL\Station;

class StationModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_station';

    protected $guarded = ["id"];

    protected $fillable = ['id','station_code',"card_classify","isdel","pcode"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function tank()
    {
        return $this->hasMany(StationTankModel::class, 'station_id', 'id');
    }

    /**
     * 获取1条站点的基本信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneStationBasicInfoByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->first();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 获取批量站点的基本信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchStationInfoByParams(array $whereParams, $select = ['*'], $toArray = false)
    {
        $result = BaseModel::scopeWithCondition(self::select($select), $whereParams)->get();

        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    public static function getOneField(array $params, $field = "remark_name", $group = "pcode")
    {
        return BaseModel::scopeWithCondition(self::select($field), $params)->groupby($group)->pluck($field);
    }
}