<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-31
 * Time: 11:40
 */

namespace App\Models\Data\OilMapping;


use App\Jobs\BasicJob;
use App\Models\Data\AuthConfig as AuthConfigData;
use Exception;

class Ad extends Basic
{
    private const IS_HIGH_WAY_MAPPING = [
        0 => 1,
        1 => 0
    ];

    /**
     * @param array $oilStationData
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2019-07-24 11:05
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'id'                => 'oilStationNo',
            'station_name'      => 'oilStationName',
            'init_provice_code' => 'provinceCode',
            'init_city_code'    => 'cityCode',
            'push_stop'         => 'stationStatus',
            'is_highway'        => 'isHighWay',
            'trade_type'        => 'payMethod',
            'lng'               => 'stationLng',
            'lat'               => 'stationLat',
            'province_name'     => 'provinceName',
            'city_name'         => 'cityName',
            'address'           => 'stationDetailAddr',
            'contact'           => 'contactName',
            'contact_phone'     => 'contactWay',
            'price_list'        => 'oilList',
            'supplier_name'     => 'prompt',
        ]);
        $oilStationData['prompt'] = "请告诉加油员使用{$oilStationData['prompt']}付款";
        $oilStationData['stationStatus'] += 1;
        $oilStationData['isHighWay'] = self::IS_HIGH_WAY_MAPPING[$oilStationData['isHighWay']];
        $oilStationData['brandType'] = config("brand.ad.{$oilStationData['station_brand']}", 8);
        $convertedCoordinate = self::gcJ02CoordinateConvertToBd(
            $oilStationData['stationLng'],
            $oilStationData['stationLat']
        );
        $oilStationData['stationLng'] = round($convertedCoordinate['lng'], 8);
        $oilStationData['stationLat'] = round($convertedCoordinate['lat'], 8);
        if ($oilStationData['payMethod'] != 3) {
            $oilStationData['payMethod'] = 2;
        }
        if ($oilStationData['payMethod'] == 3) {
            $oilStationData['payMethod'] = 1;
            if (in_array(
                $oilStationData['pcode'],
                json_decode(
                    AuthConfigData::getAuthConfigValByName(
                        'WRITTEN_OFF_BY_QR_CODE_SUPPLIER'
                    ),
                    true
                )
            )) {
                $oilStationData['payMethod'] = 3;
            }
            if ($oilStationData['fixed_face_value_transaction'] == 1) {
                $oilStationData['payMethod'] = 4;
            }
        }
        $oilStationData['districtName'] = $oilStationData['cityName'];
        $oilStationData['districtCode'] = $oilStationData['cityCode'];
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'station_line_tag',
            'rebate_grade',
            'station_brand_name',
            'oil_unit',
            'order_need_gun',
            'provice_code',
            'city_code',
            'station_event_tag',
            'station_type',
            'coupon_list',
            'business_hours',
            'payment_certificate_type',
        ]);

        $repeatOil = [];
        $repeatOilGtZero = [];
        foreach ($oilStationData["oilList"] as $k => &$pv) {
            $pv['waitMappingOil'] = ($pv['oil_name'] ?? '') . '_' . ($pv['oil_type'] ?? '') . '_' .
                                    ($pv['oil_level'] ?? '');
            $mappedOil = config("oil.oil_mapping.ad.mapping.{$pv['waitMappingOil']}", '');
            if (config("oil.oil_mapping.ad.unit_mapping.{$pv['oil_name']}") == null or $mappedOil == '') {
                unset($oilStationData['oilList'][$k]);
                continue;
            }
            if (!isset($repeatOil[$mappedOil])) {
                $repeatOil[$mappedOil] = [];
            }
            $repeatOil[$mappedOil][] = $pv['waitMappingOil'];
        }
        foreach ($repeatOil as $rv) {
            if (count($rv) > 1) {
                array_push($repeatOilGtZero, ...$rv);
            }
        }
        $oilStationData['vouchers'] = [];
        foreach ($oilStationData["oilList"] as $k => &$v) {
            if (in_array($v['waitMappingOil'], $repeatOilGtZero)) {
                unset($oilStationData['oilList'][$k]);
                continue;
            }
            if (!empty($v['coupon_list'])) {
                array_push($oilStationData['vouchers'], ...array_column($v['coupon_list'], 'amount'));
            }
            $v = [
                'goodsSpecifications' => config("oil.oil_mapping.ad.mapping.{$v['waitMappingOil']}", ''),
                'goodsNo'             => array_flip(
                                             config("oil.oil_mapping.ad.short_oil_mapping")
                                         )[$v['waitMappingOil']],
                'goodsType'           => config("oil.oil_mapping.ad.type_mapping.{$v['oil_name']}", ''),
                'oilGunNo'            => implode(',', $v['gun_numbers']),
                'settlementPrice'     => $v['price'],
                'oilStationPrice'     => $v['gun_price'],
                'dnrcPrice'           => $v['ndrc_price'] ?? 0,
                'isEnable'            => 0,
                'unit'                => config("oil.oil_mapping.ad.unit_mapping.{$v['oil_name']}", ''),
            ];
            $v['goodsName'] = config("oil.oil_mapping.ad.config.{$v['goodsSpecifications']}");
        }
        $oldOilStationData = json_decode(
                                 app('redis')->hget(
                                     BasicJob::OIL_STATION_CACHE_CHECK . '_ad',
                                     $oilStationData['oilStationNo']
                                 ),
                                 true
                             ) ?? [];
        if (!empty($oldOilStationData)) {
            $oldOil = array_column($oldOilStationData['oilList'], 'goodsNo');
            $notEnableOils = array_diff($oldOil, array_column($oilStationData['oilList'], 'goodsNo'));
            foreach ($oldOilStationData['oilList'] as &$ov) {
                if (in_array($ov['goodsNo'], $notEnableOils)) {
                    $ov['isEnable'] = 1;
                    $ov['dnrcPrice'] = $ov['oilStationPrice'];
                    $oilStationData['oilList'][] = $ov;
                }
            }
        }
        $oilStationData['oilList'] = array_values($oilStationData['oilList']);
        if (empty($oilStationData['oilList'])) {
            $oilStationData['stationStatus'] = 2;
        }
    }

    public static function gcJ02CoordinateConvertToBd($gg_lon, $gg_lat): array
    {
        $x_pi = 3.14159265358979324 * 3000.0 / 180.0;
        $x = $gg_lon;
        $y = $gg_lat;
        $z = sqrt($x * $x + $y * $y) - 0.00002 * sin($y * $x_pi);
        $theta = atan2($y, $x) - 0.000003 * cos($x * $x_pi);
        $data['lng'] = $z * cos($theta) + 0.0065;
        $data['lat'] = $z * sin($theta) + 0.006;
        return $data;
    }
}
