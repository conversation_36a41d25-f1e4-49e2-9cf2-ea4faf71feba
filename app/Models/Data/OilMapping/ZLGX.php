<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\RegionalInfo as RegionalInfoData;
use Throwable;

class ZLGX extends Basic
{
    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhudel<PERSON>@zhongqixing.com>
     * @since 2020/6/5 4:37 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'push_stop'                    => 'isStop',
            'provice_code'                 => 'provinceCode',
            'city_code'                    => 'cityCode',
            'contact_phone'                => 'contactPhone',
            'is_highway'                   => 'isHighway',
            'oil_unit'                     => 'oilUnit',
            'order_need_gun'               => 'orderNeedGun',
            'price_list'                   => 'priceList',
            'city_name'                    => 'cityName',
            'province_name'                => 'provinceName',
            'rebate_grade'                 => 'rebateGrade',
            'station_brand_name'           => 'stationBrandName',
            'station_name'                 => 'stationName',
            'station_event_tag'            => 'stationEventTag',
            'station_line_tag'             => 'stationLineTag',
            'station_type'                 => 'stationType',
            'trade_type'                   => 'tradeType',
            'supplier_name'                => 'supplierName',
            'coupon_list'                  => 'couponList',
            'fixed_face_value_transaction' => 'fixedFaceValueTransaction',
            'station_transaction_desc'     => 'stationTransactionDesc',
        ]);
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'station_brand',
            'business_hours',
            'payment_certificate_type',
        ]);
        foreach ($oilStationData["priceList"] as &$v) {
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'oil_level',
                'ndrc_price',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oilType',
                'oil_name_val'  => 'oilName',
                'oil_level_val' => 'oilLevel',
                'gun_numbers'   => 'gunNumbers',
                'gun_price'     => 'gunPrice',
            ]);
        }
        $oilStationData['priceList'] = array_values($oilStationData['priceList']);
        if (empty($oilStationData['priceList'])) {
            $oilStationData['isStop'] = 1;
        }
        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [$oilStationData['provinceCode'], $oilStationData['cityCode']]
            );
            $oilStationData['provinceName'] = $regionalMapping[$oilStationData['provinceCode']];
            $oilStationData['cityName'] = $regionalMapping[$oilStationData['cityCode']];
        }
    }
}
