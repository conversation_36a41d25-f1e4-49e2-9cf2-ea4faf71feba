<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\OilMapping\Common as CommonData;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use Throwable;

class SFFYSIMPLE extends Basic
{
    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:37 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'push_stop'    => 'is_stop',
            'provice_code' => 'province_code',
        ]);
        CommonData::removeRepeatOilAll(
            $oilStationData['price_list'],
            'oil.oil_mapping.sffy.mapping'
        );
        foreach ($oilStationData["price_list"] as $k => &$v) {
            if (empty(config("oil.oil_mapping.sffy.mapping.{$v['oil_name']}_{$v['oil_type']}_{$v['oil_level']}"))) {
                unset($oilStationData["price_list"][$k]);
                continue;
            }
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'ndrc_price',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oil_type',
                'oil_name_val'  => 'oil_name',
                'oil_level_val' => 'oil_level',
            ]);
        }
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
        ]);
        $oilStationData['price_list'] = array_values($oilStationData['price_list']);
        if (empty($oilStationData['price_list'])) {
            $oilStationData['is_stop'] = 1;
        }
        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [$oilStationData['province_code'], $oilStationData['city_code']]
            );
            $oilStationData['province_name'] = $regionalMapping[$oilStationData['province_code']];
            $oilStationData['city_name'] = $regionalMapping[$oilStationData['city_code']];
        }
    }
}
