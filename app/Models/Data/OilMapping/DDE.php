<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\RegionalInfo as RegionalInfoData;
use Throwable;


class DDE extends Basic
{
    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <zhu<PERSON><PERSON>@zhongqixing.com>
     * @since 2020/6/5 4:23 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'push_stop'            => 'is_stop',
            'provice_code'         => 'province_code',
            'station_brand_name'   => 'brand_name',
            'contact_phone'        => 'contact_number',
            'contact'              => 'contact_person',
            'station_display_name' => 'remark_name',
        ]);
        $oilStationData['station_tag'] = trim(
            implode(
                array_merge(
                    $oilStationData['station_line_tag'],
                    [$oilStationData['station_event_tag']]
                ),
                ','
            ),
            ','
        );
        self::filterField($oilStationData, [
            'pcode',
            'tag_list',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
        ]);

        foreach ($oilStationData["price_list"] as &$v) {
            self::filterField($v, [
                'oil_title_name',
                'oil_type',
                'oil_name',
                'ndrc_price',
                'coupon_list',
                'discount_rate',
            ]);
            self::replaceKey($v, [
                'oil_type_val'  => 'oil_type',
                'oil_name_val'  => 'oil_name',
                'oil_level_val' => 'oil_level',
                'starttime'     => 'start_time',
                'endtime'       => 'end_time',
            ]);
        }

        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [$oilStationData['province_code'], $oilStationData['city_code']]
            );
            $oilStationData['province_name'] = $regionalMapping[$oilStationData['province_code']];
            $oilStationData['city_name'] = $regionalMapping[$oilStationData['city_code']];
        }

        $oilStationData['rebate'] = 1;
    }
}
