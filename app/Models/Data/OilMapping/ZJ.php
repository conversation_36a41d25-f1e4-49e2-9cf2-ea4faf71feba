<?php


namespace App\Models\Data\OilMapping;


use App\Models\Data\AuthConfig as AuthConfigData;
use App\Models\Data\RegionalInfo as RegionalInfoData;
use App\Models\Logic\StationPrice\GetData;
use Throwable;

class ZJ extends Basic
{
    /**
     * @param array $oilStationData
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:37 下午
     */
    public static function getOilStationData(array &$oilStationData)
    {
        self::replaceKey($oilStationData, [
            'id'                           => 'stationId',
            'station_name'                 => 'stationName',
            'push_stop'                    => 'isStop',
            'provice_code'                 => 'provinceCode',
            'city_code'                    => 'cityCode',
            'is_highway'                   => 'isHighway',
            'trade_type'                   => 'tradeType',
            'station_brand_name'           => 'brandName',
            'province_name'                => 'provinceName',
            'city_name'                    => 'cityName',
            'supplier_name'                => 'supplierName',
            'concat_phone'                 => 'concatPhone',
            'rebate_grade'                 => 'rebateGrade',
            'station_event_tag'            => 'stationEventTag',
            'station_line_tag'             => 'stationLineTag',
            'order_need_gun'               => 'orderNeedGun',
            'oil_unit'                     => 'oilUnit',
            'fixed_face_value_transaction' => 'fixedFaceValueTransaction',
            'station_transaction_desc'     => 'stationTransactionDesc',
            'station_type'                 => 'stationType',
        ]);
        $oilStationData['isHighway'] = (int)$oilStationData['isHighway'];
        $oilStationData['stationType'] = (int)$oilStationData['stationType'];
        $oilStationData['isStop'] = (int)$oilStationData['isStop'];
        $oilStationData['logo'] = AuthConfigData::getAuthConfigValByName("STATION_DEFAULT_THUMBNAIL");
        $oilStationData['gunList'] = [];
        $genGunNumbers = [];
        if (in_array(
            $oilStationData['pcode'],
            json_decode(
                AuthConfigData::getAuthConfigValByName(
                    'ORDER_NEED_GUN_INFO_FOR_SUPPLIER_CODE'
                )
            )
        )) {
            $oilGunNumbers = (new GetData([
                'stationId' => $oilStationData['stationId']
            ]))->getGunNosByStation(false);
        }
        $oils = [];
        $gunNumber = 1;
        foreach ($oilStationData['price_list'] as $pv) {
            $oils[$pv['oil_name'] . $pv['oil_type'] . $pv['oil_level']] = $pv['oil_name_val'] . $pv['oil_type_val'] . $pv['oil_level_val'];
        }
        asort($oils);
        foreach ($oils as $ok => $ov) {
            $genGunNumbers[$ok] = [$gunNumber];
            $gunNumber++;
        }
        self::filterField($oilStationData, [
            'pcode',
            'station_code',
            'tag_list',
            'role_code',
            'fixed_face_value_transaction',
            'station_transaction_desc',
            'station_brand',
            'payment_certificate_type',
        ]);
        foreach ($oilStationData['price_list'] as &$v) {
            $v['gun_numbers'] = $oilGunNumbers[$v['oil_name'] . '_' . $v['oil_type'] . '_' . $v['oil_level']] ??
                                $genGunNumbers[$v['oil_name'] . $v['oil_type'] . $v['oil_level']];
        }
        foreach ($oilStationData['price_list'] as $pv) {
            foreach ($pv['gun_numbers'] as $gv) {
                $oilStationData['gunList'][] = [
                    'oilType'   => $pv['oil_type_val'],
                    'oilName'   => $pv['oil_name_val'],
                    'oilLevel'  => $pv['oil_level_val'],
                    'gunPrice'  => (float)$pv['gun_price'],
                    'price'     => (float)$pv['price'],
                    'gunNumber' => $gv,
                    'status'    => $oilStationData['isStop'],
                ];
            }
        }
        unset($oilStationData['price_list']);

        if (self::$dbActionInner) {
            $regionalMapping = RegionalInfoData::getNameByCode(
                [
                    $oilStationData['provinceCode'],
                    $oilStationData['cityCode']
                ]
            );
            $oilStationData['provinceName'] = $regionalMapping[$oilStationData['provinceCode']];
            $oilStationData['cityName'] = $regionalMapping[$oilStationData['cityCode']];
        }
    }
}
