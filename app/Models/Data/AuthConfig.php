<?php


namespace App\Models\Data;

use App\Models\Dao\AuthConfig as AuthConfigDao;
use Exception;
use Illuminate\Support\Collection;

class AuthConfig
{
    public const AUTH_CONFIG_HASH_CACHE_KEY = "auth_config";

    public static function getAuthConfigValByName(string $configName)
    {
        if (!$data = app('redis')->hget(self::AUTH_CONFIG_HASH_CACHE_KEY, $configName)) {
            $data = (new AuthConfigDao())->select('val')->where('name', '=', $configName)->limit(1)->first();

            if ($data) {
                $data = $data->toArray();
                app('redis')->hset(self::AUTH_CONFIG_HASH_CACHE_KEY, $configName, $data['val']);
                return $data['val'];
            }

            return '';
        }

        return $data;
    }

    public static function getAuthConfigNameByVal(string $configVal)
    {
        if (!$data = app('redis')->hget(self::AUTH_CONFIG_HASH_CACHE_KEY, $configVal)) {
            $data = (new AuthConfigDao())->select('name')->where('val', '=', $configVal)->limit(1)->first();

            if ($data) {
                $data = $data->toArray();
                app('redis')->hset(self::AUTH_CONFIG_HASH_CACHE_KEY, $configVal, $data['name']);
                return $data['name'];
            }

            return '';
        }

        return $data;
    }

    public static function getData(array $parameter)
    {
        $model = new AuthConfigDao();
        if (isset($parameter['configName'])) {
            $model = $model->where('name', 'like', "%{$parameter['configName']}%");
        }
        if (isset($parameter['configVal'])) {
            $model = $model->where('val', 'like', "%{$parameter['configVal']}%");
        }
        $count = $model->count();
        $model = $model
            ->offset(($parameter['page'] - 1) * $parameter['limit'])
            ->limit($parameter['limit'])
            ->orderBy('created_at', 'desc');
        return [
            'count' => $count,
            'list'  => $model->get()->toArray(),
        ];
    }

    public static function update(array $params)
    {
        (new AuthConfigDao())->where('id', '=', $params['id'] ?? '')->update([
            'val'  => $params['val'] ?? '',
            'name' => $params['name'] ?? '',
        ]);
        app('redis')->del([self::AUTH_CONFIG_HASH_CACHE_KEY]);
    }

    public static function create(array $params)
    {
        $model = new AuthConfigDao();
        $model->val = $params['configVal'];
        $model->name = $params['configName'];
        $model->save();
        app('redis')->hset(
            self::AUTH_CONFIG_HASH_CACHE_KEY,
            $params['configName'] ?? '',
            $params['configVal'] ?? ''
        );
    }

    /**
     * @throws Exception
     */
    public static function delete(array $params)
    {
        (new AuthConfigDao())->where('id', $params['id'])->delete();
        app('redis')->hdel(self::AUTH_CONFIG_HASH_CACHE_KEY, [$params['id']]);
    }

    /**
     * @param array $configNames
     * @param array $fields
     * @return AuthConfigDao[]|Collection|\Illuminate\Database\Eloquent\Collection
     */
    public static function getFieldsByNames(array $configNames, array $fields)
    {
        return (new AuthConfigDao())->whereIn('name', $configNames)->get($fields);
    }

    public static function updateByName(string $name, array $params)
    {
        (new AuthConfigDao())->where('name', '=', $name)->update([
            'val' => $params['val'] ?? '',
        ]);
        app('redis')->del([self::AUTH_CONFIG_HASH_CACHE_KEY]);
    }
}
