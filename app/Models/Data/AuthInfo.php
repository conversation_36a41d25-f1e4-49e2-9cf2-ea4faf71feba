<?php
/**
 * Created by PhpStorm.
 * User: zdl
 * Date: 2019-01-10
 * Time: 15:20
 */

namespace App\Models\Data;

use App\Models\Dao\AuthInfo as AuthInfoDao;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Throwable;

class AuthInfo
{
    public const AUTH_INFO_CACHE_KEY       = "auth_info";
    public const AUTH_INFO_FIELD_CACHE_KEY = "auth_info_field";

    public static function getAuthInfoByAccessKey(string $accessKey)
    {
        if (!$data = app('redis')->hget(self::AUTH_INFO_CACHE_KEY, $accessKey)) {
            $data = (new AuthInfoDao())->where('access_key', '=', $accessKey)->limit(1)->get()->
            toArray();

            if (!empty($data)) {
                app('redis')->hset(self::AUTH_INFO_CACHE_KEY, $accessKey, json_encode($data[0]));
                return $data[0];
            }

            return [];
        }

        return json_decode($data, true);
    }

    public static function getAuthInfoById(string $id): array
    {
        $data = (new AuthInfoDao())->where('id', '=', $id)->limit(1)->first();

        if ($data) {
            return $data->toArray();
        }

        return [];
    }

    public static function getAuthInfoFieldByNameAbbreviation(
        string $nameAbbreviation,
        $field = '*',
        bool $returnModel = false
    ) {
        $cacheKey = md5(var_export(func_get_args(), true));
        if (!$data = @unserialize(app('redis')->hget(self::AUTH_INFO_FIELD_CACHE_KEY, $cacheKey))) {
            $data = (new AuthInfoDao())->select($field)->where('name_abbreviation', '=', $nameAbbreviation)
                                       ->limit(1)->first();
            if (!$data) {
                return null;
            }
            app('redis')->hset(self::AUTH_INFO_FIELD_CACHE_KEY, $cacheKey, serialize($data));
        }
        if ($returnModel) {
            return $data;
        }
        return $data->toArray();
    }

    /**
     * @param array $whereParameters
     * @param array $fields
     * @param bool $only
     * @param bool $returnModel
     * @return AuthInfoDao|AuthInfoDao[]|array|Collection|Model|
     * Builder|Builder[]|\Illuminate\Support\Collection|object|null
     * <AUTHOR> <<EMAIL>>
     * @since 2020/1/2 11:42 上午
     */
    public static function getAuthInfoByWhere(
        array $whereParameters,
        array $fields,
        bool $only = true,
        bool $returnModel = false
    ) {
        $authInfoModel = (new AuthInfoDao());
        $authInfoFillAble = $authInfoModel->getFillable();

        foreach ($whereParameters as $value) {
            if (!isset($value['field']) or !isset($value['operator']) or !isset($value['value'])) {
                continue;
            }

            if (is_object($value['value']) or is_array($value['field']) or is_object($value['field']) or
                !is_string($value['operator'])) {
                continue;
            }

            if (in_array($value['field'], $authInfoFillAble)) {
                switch ($value['operator']) {
                    case 'in':
                        $authInfoModel = $authInfoModel->whereIn(
                            "{$value['field']}",
                            $value['value']
                        );
                        break;
                    default:
                        $authInfoModel = $authInfoModel->where(
                            "{$value['field']}",
                            $value['operator'],
                            $value['value']
                        );
                }
            }
        }

        if ($only) {
            $authInfoObj = $authInfoModel->first($fields);
        } else {
            $authInfoObj = $authInfoModel->get($fields);
        }
        if (!$authInfoObj) {
            return $returnModel ? null : [];
        }
        return $returnModel ? $authInfoObj : $authInfoObj->toArray();
    }

    public static function getAuthInfoByRoleCode($roleCode, bool $only = false)
    {
        if (!is_string($roleCode) and !is_array($roleCode)) {
            return [];
        }

        $roleCode = is_string($roleCode) ? [$roleCode] : $roleCode;
        $roleCode = array_filter($roleCode);
        $field = md5(implode(',', $roleCode));

        if (!$data = app('redis')->hget(self::AUTH_INFO_CACHE_KEY, $field)) {
            $data = (new AuthInfoDao())->whereIn('role_code', $roleCode)->get()->
            toArray();

            if (!$data) {
                return [];
            }

            app('redis')->hset(self::AUTH_INFO_CACHE_KEY, $field, json_encode($data));
        } else {
            $data = json_decode($data, true);
        }

        if (!$only) {
            if (checkIsAssocArray($data)) {
                $data = [$data];
            }
        } else {
            return $data[0] ?? [];
        }

        return $data;
    }

    public static function getData(array $parameter): array
    {
        $model = new AuthInfoDao();
        if (isset($parameter['access_key'])) {
            $model = $model->where('access_key', 'like', "%{$parameter['access_key']}%");
        }
        if (isset($parameter['role_code'])) {
            $model = $model->where('role_code', 'like', "%{$parameter['role_code']}%");
        }
        if (isset($parameter['name_abbreviation'])) {
            $model = $model->where('name_abbreviation', 'like', "%{$parameter['name_abbreviation']}%");
        }
        $count = $model->count();
        $listData = $model
            ->leftJoin('role', 'auth_info.role', '=', 'role.id')
            ->offset(($parameter['page'] - 1) * $parameter['limit'])
            ->limit($parameter['limit'])
            ->orderBy('created_at', 'desc')
            ->get([
                'auth_info.*',
                'role.role_name as role_value'
            ])
            ->toArray();
        return [
            'count' => $count,
            'list'  => $listData,
        ];
    }

    public static function update(array $params)
    {
        $authInfoModel = new AuthInfoDao();
        Base::popNotInFillAbleElement($authInfoModel, $params);
        $params['updated_at'] = date('Y-m-d H:i:s');
        $authInfoModel->where('id', '=', $params['id'] ?? '')->update($params);
        app('redis')->del([self::AUTH_INFO_CACHE_KEY]);
        app('redis')->del([self::AUTH_INFO_FIELD_CACHE_KEY]);
    }

    /**
     * @param array $params
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/8/26 3:33 下午
     */
    public static function delete(array $params)
    {
        (new AuthInfoDao())->where('id', '=', $params['id'] ?? '')->delete();
        app('redis')->del([self::AUTH_INFO_CACHE_KEY]);
        app('redis')->del([self::AUTH_INFO_FIELD_CACHE_KEY]);
    }

    /**
     * @param array $params
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/8/26 10:55 上午
     */
    public static function create(array $params): JsonResponse
    {
        $model = new AuthInfoDao();
        $model->access_key = $params["access_key"];
        $model->secret = $params["secret"];
        $model->name_abbreviation = $params["name_abbreviation"] ?? "";
        $model->card_no = $params["card_no"] ?? "";
        $model->role = $params["role"] ?? 1;
        $model->role_code = $params["role_code"] ?? "";
        $model->save();
        app('redis')->del([self::AUTH_INFO_CACHE_KEY]);
        app('redis')->del([self::AUTH_INFO_FIELD_CACHE_KEY]);
        return responseFormat(0, ["id" => $model->id]);
    }
}
