<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */

namespace App\Models;

use App\Models\BaseModel;

class StationRuleModel extends BaseModel
{
    protected $connection = 'mysql_gas';
    /**
     * 表名
     */
    protected $table = 'gas_station_rule';

    protected $guarded = ["id"];

    protected $fillable = ['id','category',"rule_type","rule_val","orgcode","is_white","status","source","pcode"];

    public $incrementing = false;

    public function getFillAble()
    {
        return $this->fillable;
    }
}