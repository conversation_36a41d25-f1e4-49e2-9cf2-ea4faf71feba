<?php

namespace App\Models\Gas;

use App\Models\BaseModel;
use \Illuminate\Support\Facades\DB;

class PriceSaleExportTaskModel extends BaseModel
{
    protected $connection = 'mysql_gas';

    protected $table = 'gas_price_sale_export_task';

    protected $primaryKey = 'id';

    protected $keyType = 'string';

    protected $fillAble = [
        'id','batch_no','type','status','createtime'
    ];

    //disable incrementing id;
    public $incrementing = FALSE;


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return mixed
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
        //Search By batch_no
        if (isset($params['batch_no']) && $params['batch_no'] != '') {
            $query->where('batch_no', '=', $params['batch_no']);
        }
        return $query;
    }
}
