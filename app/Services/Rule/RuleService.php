<?php

namespace App\Services\Rule;
use App\Exceptions\ParamInvalidException;
use App\Exceptions\ReponseException;
use App\Library\Helper\Common;
use App\Library\Helper\Org;
use App\Library\Helper\StationCreate;
use App\Library\Request;
use App\Models\Gas\RuleModel;
use App\Models\Gas\StationModel;
use App\Models\Gas\StationOrgRuleModel;
use App\Models\Gas\SupplierModel;
use App\Models\Station\Station;
use App\Repositories\Goods\OilRepository;
use App\Repositories\Rule\RuleRepository;
use App\Repositories\Station\StationRepository;
use App\Repositories\StationOrgRule\StationOrgRuleRepository;
use App\Repositories\Supplier\SupplierRepository;
use App\Services\Station\StationService;
use App\Services\StationOrgRule\StationOrgRuleService;
use App\Servitization\Gos;
use RuntimeException;
use Illuminate\Support\Facades\DB;

class RuleService
{
    public function __construct(){}

    public static function getRulePaginate($params)
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        unset($params['page'], $params['limit']);
        $orgCode = $params['orgcode'] ?? '';
        $ruleList = RuleRepository::getRulePaginate($page, $limit, $params);
        if (empty($ruleList['count'])) {
            return $ruleList;
        }
        // 有效性
        // 上级若与当前有相似/相同限制的规则则无效，其他都为有效
        // 例如：本规则为 限制油站1；上级如果为 限制油站1 或 限制运营商1(包含限制油站1) 则无效；其他都为有效
        if (!empty($orgCode)) {
            $ruleList['data'] = RuleRepository::getRuleTimeLine($ruleList['data']);
        }

        //结果输出
        $ruleList['data'] = collect($ruleList['data'])->map(function($item) use ($orgCode){
            $item = collect($item)->toArray();
            $value['id'] = $item['id'];
            $value['source'] = $item['source'];
            $value['source_name'] = $item['source_name'];
            $value['rule_type_name'] = $item['rule_type_name'];
            $value['rule_val_name'] = $item['rule_val_name'];
            $value['white_name'] = $item['source'] == 1 ? ($item['is_white'] == 1 ? '只给指定机构用' : '只用机构不能用') : ($item['is_white'] == 1 ? '客户只选用' : '客户不选用');
            $value['orgcode_name'] = $item['orgcode'].' '.$item['orgcode_name'];
            $value['status_name'] = $item['status_name'];
            $value['available'] = !empty($orgCode) ? $item['available'] : '-';
            $value['create_time'] = $item['create_time'];
            return $value;
        });
        return $ruleList;
    }

    public static function getClientRule($params)
    {
        $data = $result = [];$orgcode = $params['orgcode'];$params['source'] = 2;
        $rule = RuleRepository::getClientRule($params);
        if (empty($rule)) {
            return $data;
        }
        $rule = $rule->toArray();
        $i = $j = 0;
        foreach ($rule as $k => $v) {
            if (in_array($v['rule_type'],[1,2,3,4])) {
                $result[$v['category']]['rule_val'][$i]['category'] = $v['category'];
                $result[$v['category']]['rule_val'][$i]['is_white'] = $v['is_white'];
                $result[$v['category']]['rule_val'][$i]['status'] = $v['status'];
                $result[$v['category']]['rule_val'][$i]['status_name'] = $v['status_name'];
                $result[$v['category']]['rule_val'][$i]['rule_type'] = $v['rule_type'];
                $result[$v['category']]['rule_val'][$i]['rule_type_name'] = $v['rule_type_name'];
                $result[$v['category']]['rule_val'][$i]['value'] = $v['rule_val'];
                $result[$v['category']]['rule_val'][$i]['value_name'] = $v['rule_val_name'];
                $result[$v['category']]['rule_val'][$i]['orgcode'] = $v['orgcode'];
                if ($v['rule_type'] == 1) {
                    $result[$v['category']]['rule_val'][$i]['pcode'] = $v['pcode'];
                }
                $i++;
            }
            if (in_array($v['rule_type'],[5,6])) {
                $oil = explode(',', $v['rule_val']);
                $result[$v['category']]['rule_val'][$j]['oil_type'] = $oil['1'] ?? '';
                $result[$v['category']]['rule_val'][$j]['oil_name'] = $oil['0'];
                $result[$v['category']]['rule_val'][$j]['oil_level'] = $oil['2'] ?? '';
                $result[$v['category']]['rule_val'][$j]['oil_type_val'] = $v['oil_type_val'] ?? '';
                $result[$v['category']]['rule_val'][$j]['oil_name_val'] = $v['oil_name_val'] ?? '';
                $result[$v['category']]['rule_val'][$j]['oil_level_val'] = $v['oil_level_val'] ?? '';
                $result[$v['category']]['rule_val'][$j]['oil_title_name'] = $v['rule_val_name'] ?? '';
                $result[$v['category']]['rule_val'][$j]['is_white'] = $v['is_white'];
                $result[$v['category']]['rule_val'][$j]['status'] = $v['status'];
                $result[$v['category']]['rule_val'][$j]['status_name'] = $v['status_name'];
                $result[$v['category']]['rule_val'][$j]['rule_type'] = $v['rule_type'];
                $result[$v['category']]['rule_val'][$j]['rule_type_name'] = $v['rule_type_name'];
                $result[$v['category']]['rule_val'][$j]['category'] = $v['category'];
                $result[$v['category']]['rule_val'][$j]['orgcode'] = $v['orgcode'];
                $j++;
            }
        }
        if (isset($result['1'])) {
            $data['station_rule'] = $result['1'];
        }
        if (isset($result['2'])) {
            $data['oil_rule'] = $result['2'];
        }
        //修复获取油价时报错
        if(count($rule) >= 1) {
            $data['is_disable'] = $rule['0']['orgcode'] != $orgcode ? 1 : 2;
            $data['orgcode'] = $rule['0']['orgcode'];
            $data['orgcode_name'] = $rule['0']['orgcode_name'];
        }else{
            $data['is_disable'] = 2;
            $data['orgcode'] = '';
            $data['orgcode_name'] = '';
        }
        return $data;
    }

    public static function serviceRuleCreate($params)
    {
        // 参数校验
        if ($params['rule_type'] == 1) {
            $ruleValNum = collect($params['rule_val'])->pluck('value')->count();
            $pCodeNum = collect($params['rule_val'])->pluck('pcode')->count();
            if ($ruleValNum != $pCodeNum) {
                throw new ParamInvalidException('参数错误！运营商pcode数量与rule_val数量不一致', 20);
            }
        }
        // 参数初始化
        $param = $ruleExists = [];
        foreach($params['orgcode'] as $k => $org) {
            foreach($params['rule_val'] as $key => $rule) {
                $item['category'] = 1;
                $item['rule_type'] = $params['rule_type'];
                $item['rule_val'] = $rule['value'];
                $item['pcode'] = $params['rule_type'] == 1 ? $rule['pcode'] : '';
                $item['orgcode'] = $org;
                $item['is_white'] = $params['is_white'];
                $item['source'] = 1;
                $item['status'] = $params['status'];
                $item['uid'] = Request::get('uid');
                $item['modifier'] = Request::get('user_name');
                $param[] = $item;
            }
        }
        // 编码校验
        $codeLegal = self::stationCodeCheck($params);
        if (!empty($codeLegal)) {
            throw new ParamInvalidException('参数错误！站点限制类型中站点编码/运营商编码错误', 20);
        }
        // 运营商编码校验
        $supplierLegal = self::supplierCodeCheck($params);
        if (!empty($supplierLegal)) {
            throw new ParamInvalidException('参数错误！运营商限制类型中运营商编码错误', 20);
        }
        // 重复规则校验
        foreach ($param as $key => $item) {
            $attribute = [
                'source' => 1,
                'orgcode' => $item['orgcode'],
                'rule_type' => $item['rule_type'],
                'rule_val' => $item['rule_val'],
                'pcode' => $item['pcode']
            ];
            $ruleCheckExists = RuleRepository::checkServiceRuleExist($attribute);
            if (!empty($ruleCheckExists)) {
                $ruleExists[] = $ruleCheckExists;
            }
//            if (!empty($rule)) {
//                $ruleType = $rule['rule_type'] == 1 ? '油站' : '运营商';
//                throw new ParamInvalidException('参数错误！已存在对机构('.$rule['orgcode'].')进行的'.$ruleType.'('.$rule['rule_val'].')限制', 20);
//            }
        }
        if (!empty($ruleExists)) {
            $orgCodeExists = collect($ruleExists)->map(function($item){
                return collect($item)->only(['orgcode']);
            })->unique()->toArray();
            return ['code' => config('errorcode.PARAMS_IS_INVALID'), 'msg' => '规则已存在', 'success' => false , 'data' => $orgCodeExists];
        }

        DB::connection('mysql_gas')->beginTransaction();
        try{
            $id = RuleRepository::serviceRuleCreate($param);
            DB::connection('mysql_gas')->commit();
        }catch (\Exception $e) {
            DB::connection('mysql_gas')->rollback();
            throw new RuntimeException($e->getMessage(), 2);
        }
        return $id;
    }

    public static function batchOn($params)
    {
        $rule = RuleRepository::getRuleByWhere(['id' => $params['id_list'], 'status' => 2, 'source' => 1])->toArray();
        if (empty($rule)) {
            return ['code' => config('errorcode.PARAMS_IS_INVALID'), 'msg' => '只允许启用运营侧停用状态的规则', 'success' => false , 'data' => $params['id_list']];
        }
        DB::connection('mysql_gas')->beginTransaction();
        try{
            RuleRepository::batchOnById($params['id_list']);
            DB::connection('mysql_gas')->commit();
        }catch (\Exception $e) {
            DB::connection('mysql_gas')->rollback();
            throw new RuntimeException($e->getMessage(), 2);
        }
        return $params['id_list'];
    }

    public static function batchOff($params)
    {
        $rule = RuleRepository::getRuleByWhere(['id' => $params['id_list'], 'status' => 1, 'source' => 1])->toArray();
        if (empty($rule)) {
            return ['code' => config('errorcode.PARAMS_IS_INVALID'), 'msg' => '只允许停用运营侧启用状态的规则', 'success' => false , 'data' => $params['id_list']];
        }
        DB::connection('mysql_gas')->beginTransaction();
        try{
            RuleRepository::batchOffById($params['id_list']);
            DB::connection('mysql_gas')->commit();
        }catch (\Exception $e) {
            DB::connection('mysql_gas')->rollback();
            throw new RuntimeException($e->getMessage(), 2);
        }
        return $params['id_list'];
    }

    public static function batchDelete($params)
    {
        $id = array_column($params['id_list'], 'id');
        $rule = RuleRepository::getRuleByWhere(['id' => $id, 'source' => 1, 'status' => 2])->toArray();
        if (empty($rule)) {
            return ['code' => config('errorcode.PARAMS_IS_INVALID'), 'msg' => '只允许删除运营侧停用状态的规则', 'success' => false , 'data' => $params['id_list']];
        }
        DB::connection('mysql_gas')->beginTransaction();
        try{
            RuleRepository::batchDeleteById($id);
            DB::connection('mysql_gas')->commit();
        }catch (\Exception $e) {
            DB::connection('mysql_gas')->rollback();
            throw new RuntimeException($e->getMessage(), 2);
        }
        return $params['id_list'];
    }

    public static function getRuleLog($params)
    {
        $ruleHistory = RuleRepository::getRuleLogByRuleId($params);
        $logList = collect($ruleHistory)->map(function($item){
            $value['time'] = $item['update_time'];
            $value['action'] = $item['createtime'] == $item['update_time'] ? '添加' : ($item['status'] == '1' ? '启用' : '停用');
            $value['status_name'] = $item['status'] == 1 ? '已启用' : '未启用';
            $value['modifier'] = $item['modifier'];
            return $value;
        });
        return $logList;
    }

    public static function clientRuleUpOrCreate($params)
    {
        // 站点限制参数过滤
        $stationRuleParam = [];
        if (isset($params['station_rule'])) {
            $stationRule = $params['station_rule'];
            $ruleVal = collect($stationRule['rule_val'])->pluck('value')->toArray();
            // 编码校验
            $codeLegal = self::stationCodeCheck($stationRule);
            if (!empty($codeLegal)) {
                throw new ParamInvalidException('参数错误！站点限制类型中站点编码/运营商编码错误', 20);
            }
            // 运营商编码校验
            $supplierLegal = self::supplierCodeCheck($stationRule);
            if (!empty($supplierLegal)) {
                throw new ParamInvalidException('参数错误！运营商限制类型中运营商编码错误', 20);
            }
            // 品牌校验
            $stationConfig = config('station');
            if ($stationRule['rule_type'] == 3) {
                $stationBrandLegal = collect($ruleVal)->diff($stationConfig['station_brand'])->toArray();
                if (!empty($stationBrandLegal)) {
                    throw new ParamInvalidException('参数错误！参数：品牌不存在！请核实', 20);
                }
            }
            // 返利档校验
            if ($stationRule['rule_type'] == 4) {
                $rebateGradeLegal = collect($ruleVal)->diff($stationConfig['rebate_grade'])->toArray();
                if (!empty($rebateGradeLegal)) {
                    throw new ParamInvalidException('参数错误！参数：返利档不存在！请核实', 20);
                }
            }
            // 判断上级的相同类别规则是否存在；若相同类别规则存在，返回错误
            $orgCodeList = Org::orgCodeTree($params['orgcode']);
            $orgCodeRoot = array_shift($orgCodeList);
            $ruleCheckParentExists = RuleRepository::checkTreeRuleExist($orgCodeRoot, ['source' => 2]);
            if ($ruleCheckParentExists && ($params['orgcode'] != $orgCodeRoot)) {
                throw new ParamInvalidException('参数错误！规则已存在', 20);
            }
            // 参数拼接
            foreach ($stationRule['rule_val'] as $k => $value) {
                $item['category'] = 1;
                $item['rule_type'] = $stationRule['rule_type'];
                $item['rule_val'] = $value['value'];
                $item['pcode'] = $stationRule['rule_type'] == 1 ? $value['pcode'] : '';
                $item['orgcode'] = $params['orgcode'];
                $item['is_white'] = 1;
                $item['source'] = 2;
                $item['status'] = $stationRule['status'];
                $item['uid'] = Request::get('uid');
                $item['modifier'] = !empty($params['nick_name']) ? $params['nick_name'] : $params['user_name'];
                $stationRuleParam[] = $item;
            }
        }

        // 商品限制参数过滤
        $oilRuleParam = [];
        if (isset($params['oil_rule'])) {
            $oilMap = OilRepository::getOilMapTree();
            $oilRule = $params['oil_rule'];
            // 油品校验
            foreach ($oilRule['rule_val'] as &$oil) {
                $oilNameLegal = collect($oilMap['oil_name'])->has($oil['oil_name']);
                if (empty($oilNameLegal)) {
                    throw new ParamInvalidException('参数错误！oil_name错误！请核实', 20);
                }
                $oilName = collect($oilMap['oil_name'])->get($oil['oil_name']);
                $oilTypeLegal = $oilRule['rule_type'] == 6 && isset($oilMap['oil_name_relation_type'][$oil['oil_name']]) ? collect($oilMap['oil_name_relation_type'][$oil['oil_name']])->has($oil['oil_type']) : true;
                if (!$oilTypeLegal) {
                    throw new ParamInvalidException($oilName.'对应的油品标号错误！', 20);
                }
                $oilLevelLegal = isset($oilMap['oil_name_relation_type'][$oil['oil_name']]) && isset($oil['oil_level']) && !empty($oil['oil_level']) ? collect($oilMap['oil_level'])->has($oil['oil_level']) : true;
                if (!$oilLevelLegal) {
                    throw new ParamInvalidException($oilName.'对应的油品级别错误！', 20);
                }
                if (!isset($oilMap['oil_name_relation_type'][$oil['oil_name']])) {
                    $oil['oil_type'] = $oil['oil_level'] = '';
                }
            }
            // 重复规则校验 判断上级的相同类别规则是否存在；若相同类别规则存在，返回错误
            $orgCodeList = Org::orgCodeTree($params['orgcode']);
            $orgCodeRoot = array_shift($orgCodeList);
            $ruleCheckParentExists = RuleRepository::checkTreeRuleExist($orgCodeRoot, ['source' => 2]);
            if ($ruleCheckParentExists && ($params['orgcode'] != $orgCodeRoot)) {
                throw new ParamInvalidException('参数错误！规则已存在', 20);
            }
            // 拼装参数
            foreach ($oilRule['rule_val'] as $key => $value) {
                $item['category'] = 2;
                $item['rule_type'] = $oilRule['rule_type'];
                // name/type/level顺序逗号分隔
                $item['rule_val'] = $oilRule['rule_type'] == 5 ? $value['oil_name'] : implode(',', [$value['oil_name'],$value['oil_type'],$value['oil_level']]);
                $item['orgcode'] = $params['orgcode'];
                $item['is_white'] = 1;
                $item['source'] = 2;
                $item['status'] = $oilRule['status'];
                $item['uid'] = Request::get('uid');
                $item['modifier'] = !empty($params['nick_name']) ? $params['nick_name'] : $params['user_name'];
                $oilRuleParam[] = $item;
            }
        }
        $ruleParam = array_merge($stationRuleParam, $oilRuleParam);

        // 本级规则直接删除、创建
        DB::connection('mysql_gas')->beginTransaction();
        try{
            $id = RuleRepository::clientRuleCreate($ruleParam);
            DB::connection('mysql_gas')->commit();
        }catch (\Exception $e) {
            DB::connection('mysql_gas')->rollback();
            throw new RuntimeException($e->getMessage(), 2);
        }
        return $id;
    }

    public static function clientRuleDelete($params)
    {
        // 删除客户侧对应机构的所有规则
        DB::connection('mysql_gas')->beginTransaction();
        try{
            RuleRepository::clientRuleDelete([$params['orgcode']]);
            DB::connection('mysql_gas')->commit();
        }catch (\Exception $e) {
            DB::connection('mysql_gas')->rollback();
            throw new RuntimeException($e->getMessage(), 2);
        }
        return $params;
    }

    private static function stationCodeCheck(array $ruleVal)
    {
        if ($ruleVal['rule_type'] == 1) {
            $stationCode = array_column($ruleVal['rule_val'], 'value');
            $station = StationRepository::getStationByWhere(['station_code' => $stationCode], ['station_code','pcode']);
            $station = collect($station)->mapWithKeys(function($item){
                return [$item['station_code'] => $item['pcode']];
            })->toArray();
            $codeLegal = collect($ruleVal['rule_val'])->filter(function($item) use ($station){
                return !isset($station[$item['value']]) || ($station[$item['value']] != $item['pcode']);
            })->toArray();

            return $codeLegal;
        }
    }

    private static function supplierCodeCheck(array $ruleVal)
    {
        if ($ruleVal['rule_type'] == 2) {
            $pCode = array_unique(array_column($ruleVal['rule_val'], 'value'));
            $supplier = SupplierRepository::getSupplierByWhere(['scode' => $pCode],['scode']);
            $supplier = $supplier->pluck('scode')->toArray();
            $supplierLegal = collect($pCode)->diff($supplier)->toArray();

            return $supplierLegal;
        }
    }

    public static function getOrgLimitList(array $params)
    {
        $data = app(Gos::class)->getOrgLimitList(['org_code' => $params['orgcode']]);
        if (empty($data)) {
            return $data;
        }
        $ruleList = [];
        foreach ($data as $item => $rule) {
            $rule = self::orgLimitListFormatForGms($rule, $item);
            if (empty($rule)) continue;
            if (in_array($item, ['station','pcode','rebate','brand'])) {
                $ruleList['station_limit'] = $rule;
            }
            if (in_array($item, ['oil_name'])) {
                $ruleList['shop_limit'] = $rule;
            }
        }
        return $ruleList;
    }

    private static function orgLimitListFormatForGms(array $rule, $item)
    {
        switch ($item) {
            case 'station':
                $rule = collect($rule)->map(function($list){
                    return ['type_name' => '指定油站可用', 'name' => $list, 'type' => 1];
                })->toArray();
                break;
            case 'pcode':
                $rule = collect($rule)->map(function($list){
                    return ['type_name' => '指定运营商可用', 'name' => $list, 'type' => 2];
                })->toArray();
                break;
            case 'rebate':
                $rule = collect($rule)->map(function($list){
                    return ['type_name' => '指定返利档位可用', 'name' => $list, 'type' => 3];
                })->toArray();
                break;
            case 'brand':
                $rule = collect($rule)->map(function($list){
                    return ['type_name' => '指定品牌可用', 'name' => $list, 'type' => 4];
                })->toArray();
                break;
            case 'oil_name':
                $rule = collect($rule)->map(function($list){
                    return ['type_name' => '可用商品', 'name' => $list, 'type' => 5];
                })->toArray();
                break;
        }

        return $rule;
    }

    // 同步V1历史数据到V2
    public static function syncV1RuleToV2($params) {

        $orgcodeList = $params['orgcodeList'];
        if (empty($params['orgcodeList'])) {
            // 查询全部V1 orgcode  gas_station_rule
            $ruleAllOrgcode = RuleRepository::getAllOrgcode();
            $orgcodeList = array_keys(array_flip(array_column($ruleAllOrgcode, 'orgcode')));
        }

        // 遍历
        foreach ($orgcodeList as $orgcode) {
            DB::connection('mysql_gas')->beginTransaction();
            try {
                // 查询是否有V2的记录  有 -> 跳过  ， 没有 ->新增
                $stationRuleV2 = StationOrgRuleService::getNowRuleIdByOrgCode($orgcode);
                // 已有的continue
                if (!empty($stationRuleV2)) {
                    Common::log('error',"V1同步V2已有限制规则:".$orgcode,[]);
                    DB::connection('mysql_gas')->commit();
                    continue;
                }
                Common::log('error',"V1同步V2限制规则:".$orgcode,[]);
                $id = empty($stationRuleV2) ?  '' : $stationRuleV2['id'];
                // 查询所有V1限站规则
                $ruleList = RuleModel::where('orgcode', '=', $orgcode)->where('source', '=', 2)->where('category', '=', 1)->get();
                if (empty($ruleList->toArray())) {
                    Common::log('error',"V1同步V2限制规则,无规则不需要同步:".$orgcode,[]);
                    DB::connection('mysql_gas')->commit();
                    continue;
                }

                // V1->V2   参数转换
                $whiteStationCodeList = [];
                $blackStationCodeList = [];
                $pcodeList = [];
                $stationBrandList = [];
                $rebateGradeList = [];
                foreach ($ruleList as $rule) {
                    // category 1 限站 2 商品
                    if ($rule['category'] == 2) {
                        continue;
                    }
                    // 规则类型；1：站点，2：运营商，3：品牌，4：返利档，5：品类，6：具体商品
                    switch ($rule['rule_type']){
                        case '1':
                            //根据id查到相关油站
//                        $third_ids = array_column($params['oil_station_rule']['typeVal'],'third_id');
//                        $staitonList = Station::whereIn('third_id',$third_ids)->selectRaw('station_code as value,pcode')->get()->toArray();
//                        $station_rule = ['rule_type'=>1,'rule_val'=>$staitonList,'status'=>1];
                            if ($rule['is_white'] == 1) {
                                $whiteStationCodeList[] = $rule['rule_val'];
                            } elseif($rule['is_white'] == 2) {
                                $blackStationCodeList[] = $rule['rule_val'];
                            }
                            break;
                        case '2':
                            //运营商限制
                            $pcodeList[] = $rule['rule_val'];
                            break;
                        case '3':
                            //品牌
                            $stationBrandList[] = $rule['rule_val'];
                            break;
                        case '4':
                            //返利档位
                            $rebateGradeList[] = $rule['rule_val'];
                            break;
                    }

                }
                /*$params = [
                    'id' => '017955f3-ceb9-f674-0fce-8374f26cbcbc',
                    'orgcode' => '200NXX0I',//客户机构
                    'trade_type' => ['has_rule'=>1,'value'=>''],//收款方式
                    'station_oil_unit' => ['has_rule'=>2,'value'=>'1,2'],//加注模式：按升||元
                    'rebate_grade' => ['has_rule'=>1,'value'=>''],//返利档位
                    'station_brand' => ['has_rule'=>1,'value'=>''],//站点品牌
                    'pcode_list' => ['has_rule'=>2,'value'=>'200033M5,2000AT422A'],//站点运营商
                ];*/
                if (!empty($stationRuleV2) && $stationRuleV2['has_rule'] == 2) {
                    //  gms v2 已设置 ， 这里的逻辑不会执行
                    $orgRuleList = StationOrgRuleService::getStationOrgRuleList($id);
                    if (!empty($orgRuleList)) {
                       $rebateGradeRuleV2 = $orgRuleList['rebate_grade'];
                       $stationBrandRuleV2 = $orgRuleList['station_brand'];
                       $pcodeListRuleV2 = $orgRuleList['pcode_list'];

                       if (isset($rebateGradeRuleV2) && $rebateGradeRuleV2['has_rule'] == 2 && !empty($rebateGradeRuleV2['value'])) {
                           $rebateGradeRuleV2ValueList = explode(',', $rebateGradeRuleV2['value']);

                           $rebateGradeList = array_values(array_unique(array_merge($rebateGradeList, $rebateGradeRuleV2ValueList)));
                       }

                        if (isset($stationBrandRuleV2) && $stationBrandRuleV2['has_rule'] == 2 && !empty($stationBrandRuleV2['value'])) {
                            $stationBrandValueList = explode(',', $stationBrandRuleV2['value']);
                            $stationBrandList = array_values(array_unique(array_merge($stationBrandList, $stationBrandValueList)));
                        }

                        if (isset($pcodeListRuleV2) && $pcodeListRuleV2['has_rule'] == 2 && !empty($pcodeListRuleV2['value'])) {
                            $valueList = explode(',', $pcodeListRuleV2['value']);
                            $pcodeList = array_values(array_unique(array_merge($pcodeList, $valueList)));
                        }
                    }
                }


                // 非平台客户 加注模式和收款方式不能设置
                $createParams = [
                    'id' => $id,
                    'orgcode' => $orgcode,
                    'trade_type' => ['has_rule'=>1,'value'=>''],
                    'station_oil_unit' => ['has_rule'=>1,'value'=>''],
                    'rebate_grade' =>  empty($rebateGradeList) ? ['has_rule'=>1,'value'=>''] : ['has_rule'=>2,'value'=>implode(',',$rebateGradeList)],
                    'station_brand' =>  empty($stationBrandList) ? ['has_rule'=>1,'value'=>''] : ['has_rule'=>2,'value'=>implode(',',$stationBrandList)],
                    'pcode_list' =>  empty($pcodeList) ? ['has_rule'=>1,'value'=>''] : ['has_rule'=>2,'value'=>implode(',',$pcodeList)],
                ];
//            dd(json_encode($createParams));
                // 限站规则
                $data = (new StationOrgRuleService())->createStationOrgRule($createParams);
                $id = $data['id'];
                // 黑白名单
                /* $params = [
                                 'id' => '554cda58-5fd4-c002-431e-c42c5dcfa9c5',
                                 'orgcode' => '200NXX0I',//客户机构
                                 'list_type' => '2',//黑白名单类型
                                 'edit_type' => 'addunique',//addunique剔重并添加||addcover覆盖并添加
                                 'station_ids' => ['fd1ea154cc9b11eda76efa163eb21c0e','001dd3b2bbcb11eca6e5fa163e8e643e'],//站点名单
                             ];*/


                if (!empty($whiteStationCodeList)) {

                    $staitonIdList = Station::whereIn('station_code',$whiteStationCodeList)->selectRaw('id')->get()->toArray();

                    $stationOrgParamList = [
                        'id' => $id,
                        'orgcode' => $orgcode,//客户机构
                        'list_type' => '1',//黑白名单类型  1白名单 2 黑名单
                        'edit_type' => 'addunique',//addunique剔重并添加||addcover覆盖并添加
                        'station_ids' => array_column($staitonIdList, 'id')
                    ];

//                dd(json_encode($stationOrgParamList));
                    $data = (new StationOrgRuleService())->updateStationOrgList($stationOrgParamList);
                }

                if (!empty($blackStationCodeList)) {

                    $staitonIdList = Station::whereIn('station_code',$blackStationCodeList)->selectRaw('id')->get()->toArray();

                    $stationOrgParamList = [
                        'id' => $id,
                        'orgcode' => $orgcode,//客户机构
                        'list_type' => '2',//黑白名单类型  1白名单 2 黑名单
                        'edit_type' => 'addunique',//addunique剔重并添加||addcover覆盖并添加
                        'station_ids' => array_column($staitonIdList, 'id')
                    ];

//                dd(json_encode($stationOrgParamList));
                    $data = (new StationOrgRuleService())->updateStationOrgList($stationOrgParamList);
                }
                DB::connection('mysql_gas')->commit();
            } catch (\Exception $e) {
                DB::connection('mysql_gas')->rollback();
                Common::log('error',"V1同步V2失败:".$orgcode,[]);
                Common::log('error',"error:".$e->getMessage().",code:".$e->getCode(),[]);
            }
        }
    }

    /**
     * 上线后该方法废弃
     * @param $params
     * @return void
     */
    public static function initRuleV2($params) {
        $orgcodeList = $params['orgcodeList'];
        // 遍历
        foreach ($orgcodeList as $orgcode) {
            DB::connection('mysql_gas')->beginTransaction();
            try {
                // 查询是否有V2的记录  有 -> 跳过  ， 没有 ->新增
                $stationRuleV2 = StationOrgRuleService::getNowRuleIdByOrgCode($orgcode);
                // 已有的continue
                if (!empty($stationRuleV2)) {
                    Common::log('error',"限站规则初始化 V2已存在规则，跳过:".$orgcode,[]);
                    DB::connection('mysql_gas')->commit();
                    continue;
                }
                Common::log('error',"限站规则初始化:".$orgcode,[]);

                // 非平台客户 加注模式和收款方式不能设置
                $createParams = [
                    'orgcode' => $orgcode,
                    'trade_type' => ['has_rule'=>1,'value'=>''],
                    'station_oil_unit' => ['has_rule'=>1,'value'=>''],
                    'rebate_grade' => ['has_rule'=>1,'value'=>''],
                    'station_brand' =>  ['has_rule'=>1,'value'=>''],
                    'pcode_list' => ['has_rule'=>1,'value'=>''],
                ];
                // 限站规则
                $data =  (new StationOrgRuleService())->createStationOrgRule($createParams);
                \Log::info(array('stationUpdateOrCreate' => '专属站点初始化限站规则','orgcode'=>$orgcode, 'id'=>$data['id']));
                // 设置专用站点未仅我可用
                StationOrgRuleModel::where('orgcode', '=', $orgcode)->where('id', '=', $data['id'])->update([
                    'has_only_station' => 2
                ]);

                DB::connection('mysql_gas')->commit();
            } catch (\Exception $e) {
                DB::connection('mysql_gas')->rollback();
                Common::log('error',"限站规则初始化失败:".$e->getMessage().",code:".$e->getCode(),[]);
            }
        }
    }


}
