<?php

namespace App\Services\Station;
use App\Exceptions\ParamInvalidException;
use App\Exceptions\ReponseException;
use App\Jobs\PriceCalculation;
use App\Library\Helper\StationCreate;
use App\Library\Request;
use App\Models\Gas\StationModel;
use App\Models\Gas\SupplierModel;
use App\Repositories\Account\OrgAccountRepository;
use App\Repositories\Station\StationPCodeRepository;
use App\Repositories\Station\StationRepository;
use App\Repositories\Price\PriceRepository;
use App\Repositories\Station\StationToolRepository;
use App\Repositories\Supplier\SupplierRepository;
use App\Repositories\Tag\StationTagRepository;
use App\Repositories\Dict\DictRepository;
use App\Repositories\City\CityRepository;
use App\Repositories\Goods\OilRepository;
use App\Repositories\User\UserRepository;
use App\Services\Corner\FieldMapService;
use App\Services\ErrorCodeService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use RuntimeException;

class StationBatchService
{
    protected $supplierRepository;
    protected $cityRepository;

    public function __construct
    (
        SupplierRepository $supplierRepository,
        CityRepository $cityRepository
    )
    {
        $this->cityRepository = $cityRepository;
        $this->supplierRepository = $supplierRepository;
    }

    public static function stationInfo($stationInfo, $pcode = '20003JCP', $time = '', $getPart = 'stationPrice')
    {
        //1. 获取站下的所有油品（油品id oil_id=oil_type/oil_name/oil_level，条件：station_id）
        $oil = OilRepository::getOil($stationInfo->keys()->toArray());
        switch ($getPart)
        {
            case 'station':
                $stationTag = StationTagRepository::getStationTag($stationInfo->keys()->toArray());
                $dictTag = DictRepository::getDictTag($stationTag);
                break;
            case 'price':
                $extend = $stationInfo->map(function($station){
                    return collect($station)->only(['city_code','provice_code']);
                });
                $stationPriceList = PriceRepository::getPriceByStationId($oil, $time);
                $oilPrice = PriceRepository::getSalePrice($stationPriceList, $pcode, $extend, 1)
                    ->map(function($item) use ($oil){
                        return collect($item)->merge($oil[$item['unique_station_oil']]);
                    })
                    ->groupBy('station_id');
                break;
            default:
                //2. 获取油品的价格（条件：pcode/oil_id/station_id）
                $extend = $stationInfo->map(function($station){
                    return collect($station)->only(['city_code','provice_code']);
                });
                $stationPriceList = PriceRepository::getPriceByStationId($oil, $time);
                $oilPrice = PriceRepository::getSalePrice($stationPriceList, $pcode, $extend, 1)
                    ->map(function($item) use ($oil){
                        return collect($item)->merge($oil[$item['unique_station_oil']]);
                    })
                    ->groupBy('station_id');
                //3. 获取站的路线标签
                $stationTag = StationTagRepository::getStationTag($stationInfo->keys()->toArray());
                $dictTag = DictRepository::getDictTag($stationTag);
        }
        foreach ($stationInfo as $key => $sites) {
            if (isset($oilPrice[$sites['id']])) {
                $stationInfo[$key]['price_list'] = $oilPrice[$sites['id']];
            } else {
                $stationInfo[$key]['price_list'] = [];
            }
            if (isset($stationTag[$sites['id']])) {
                $stationInfo[$key]['tag_list'] = $stationTag[$sites['id']]->pluck('tag_id')->toArray();
            } else {
                $stationInfo[$key]['tag_list'] = [""];
            }
            if (isset($dictTag[$sites['id']])) {
                $stationInfo[$key]['station_line_tag'] = array_column($dictTag[$sites['id']]['station_line'] ?? [], 'dict_data');
                $stationInfo[$key]['station_area_tag'] = array_column($dictTag[$sites['id']]['station_area'] ?? [], 'dict_data');
                $stationInfo[$key]['station_other_tag'] = array_column($dictTag[$sites['id']]['station_other'] ?? [], 'dict_data');
                $stationInfo[$key]['station_xc_tag'] = array_column($dictTag[$sites['id']]['station_xc'] ?? [], 'dict_data');
            } else {
                $stationInfo[$key]['station_line_tag'] = [];
                $stationInfo[$key]['station_area_tag'] = [];
                $stationInfo[$key]['station_other_tag'] = [];
                $stationInfo[$key]['station_xc_tag'] = [];
            }
            $stationInfo[$key]['city_code'] = substr(CityRepository::cityCode($sites, 'city_code'), 0, 6);
            $stationInfo[$key]['provice_code'] = substr(CityRepository::cityCode($sites, 'provice_code'), 0, 6);
        }
        return $stationInfo->values()->toArray();
    }

    /**
     * 运营商站点信息数据
     * @param string $pcode
     * @param string $caller
     * @return array
     */
    public static function supplierStation($pcode = '', $caller = 'OA')
    {
        $data = [];$stationInfo = [];
        $station = StationRepository::getStationByPcode($pcode);
        if (empty($station->toArray())){
            return $data;
        }
        foreach ($station->chunk(300) as $chunk) {
            $stationInfo = array_merge($stationInfo, self::stationInfo($chunk));
        }
        foreach ($stationInfo as $key => $stationData) {
            if ($stationData['isstop'] == 1 || $stationData['card_classify'] == 1) {
                $stationData['price_list'] = [];
            }
            $data[$key] = FieldMapService::getStationPriceList($caller, $stationData);

        }
        return $data;
    }

    /**
     * 单个站点信息和价格数据
     * @param array $stationId
     * @param string $caller
     * @param string $pcode
     * @return array
     */
    public static function getStationPrice(array $stationId, $caller = 'OA', $pcode = '20003JCP', $time = '')
    {
        $data = [];$stationInfo = [];$time = !empty($time) ? $time : date('Y-m-d H:i:s');
        $station = stationRepository::getStation($stationId);
        $station = StationRepository::getTransformStation($station);
        if (empty($station->toArray())) {
            return $data;
        }
        foreach ($station->chunk(300) as $chunk) {
            $stationInfo = array_merge($stationInfo, self::stationInfo($chunk, $pcode, $time, 'stationPrice'));
        }
        foreach ($stationInfo as $key => $station) {
            switch ($caller) {
                case 'GMS':
                    $result = $station;
                    break;
                case 'OA':
                    $result['OA'] = FieldMapService::getStationPriceList('OA', $station);
                    break;
                case 'GOS':
                    $result['GOS'] = FieldMapService::getStationPriceList('GOS', $station);
                    break;
                default:
                    $result['OA'] = FieldMapService::getStationPriceList('OA', $station);
                    $result['GOS'] = FieldMapService::getStationPriceList('GOS', $station);
            }
            $data[$key] = $result;
        }

        return count($stationId) == 1 ? collect($data)->first() : $data;
    }

    /**
     * 单个站点信息For下游
     * @param array $stationId
     * @param string $caller
     * @param string $pcode
     * @param string $time
     * @return array|mixed
     */
    public static function getStation(array $stationId, $caller = 'OA', $pcode = '20003JCP', $time = '')
    {
        $data = [];$stationInfo = [];$time = !empty($time) ? $time : date('Y-m-d H:i:s');
        $station = stationRepository::getStation($stationId);
        $station = StationRepository::getTransformStation($station);
        if (empty($station->toArray())) {
            return $data;
        }
        foreach ($station->chunk(300) as $chunk) {
            $stationInfo = array_merge($stationInfo, self::stationInfo($chunk, $pcode, $time, 'station'));
        }
        foreach ($stationInfo as $key => $station) {
            switch ($caller) {
                case 'OA':
                    $result['OA'] = FieldMapService::getStationList($caller, $station);
                    break;
                case 'GOS':
                    $result['GOS'] = FieldMapService::getStationList($caller, $station);
                    break;
                default:
                    $result['OA'] = FieldMapService::getStationList('OA', $station);
                    $result['GOS'] = FieldMapService::getStationList('GOS', $station);
            }
            $data[$key] = $result;
        }

        return count($stationId) == 1 ? collect($data)->first() : $data;
    }

    /**
     * 价格信息For下游
     * @param array $stationId
     * @param string $caller
     * @param string $pcode
     * @param string $time
     * @return array|mixed
     */
    public static function getPriceForDownStream(array $stationId, $caller = 'OA', $pcode = '20003JCP', $time = '')
    {
        $data = [];$stationInfo = [];$time = !empty($time) ? $time : date('Y-m-d H:i:s');
        $station = stationRepository::getStation($stationId);
        if (empty(collect($station)->toArray())) {
            return $data;
        }
        $i = 0;
        foreach (collect($station)->chunk(300) as $chunk) {
            $i++;
            //$only_id = 'pricelist==='.$i;
          //  file_put_contents('/data/log/foss-station/station_price_log_'.date('Y-m-d'),$only_id.var_export(['sale_price_开始执行---',$chunk],true), FILE_APPEND);

            $stationInfo = array_merge($stationInfo, self::stationInfo($chunk, $pcode, $time, 'price'));

         //   file_put_contents('/data/log/foss-station/station_price_log_'.date('Y-m-d'),$only_id.var_export(['sale_price_结束---',$stationInfo],true), FILE_APPEND);

         //   file_put_contents('/data/log/foss-station/station_price_number_'.date('Y-m-d'),date('Y-m-d H:i:s').'=='.count($chunk).'thenumber===:'.$only_id, FILE_APPEND);

        }
        foreach ($stationInfo as $key => $station) {
            switch ($caller) {
                case 'OA':
                    $result['OA'] = FieldMapService::getPriceList($caller, $station);
                    break;
                case 'GOS':
                    $result['GOS'] = FieldMapService::getPriceList($caller, $station);
                    break;
                default:
                    $result['OA'] = FieldMapService::getPriceList('OA', $station);
                    $result['GOS'] = FieldMapService::getPriceList('GOS', $station);
            }
            $data[$key] = $result;
        }

        return count($stationId) == 1 ? collect($data)->first() : $data;
    }

    public static function getSomeStationForDownStream(array $stationId, $caller = 'OA', $pcode = '20003JCP', $field = 'card_classify', $value = 1)
    {
        $data = [];$stationInfo = [];
        $stationInfo = stationRepository::getStation($stationId)->toArray();
        if (empty($stationInfo)) {
            return $data;
        }
        foreach ($stationInfo as $key => $station) {
            switch ($caller) {
                case 'OA':
                    $result['OA'] = FieldMapService::getSomeStationList('OA', $station, $field, $value);
                    break;
                case 'GOS':
                    $result['GOS'] = FieldMapService::getSomeStationList('GOS', $station, $field, $value);
                    break;
                default:
                    $result['OA'] = FieldMapService::getSomeStationList('OA', $station, $field, $value);
                    $result['GOS'] = FieldMapService::getSomeStationList('GOS', $station, $field, $value);
            }
            $data[$key] = $result;
        }

        return count($stationId) == 1 ? collect($data)->first() : $data;
    }

    /**
     * 初始化建站参数等信息
     * @param $params
     * @return array
     * @throws ParamInvalidException
     */
    public static function initCreateParam($params)
    {
        // 验证用户合法
        if (collect($params)->has('area_code')) {
            unset($params['area_code']);
        }
        if (collect($params)->has('user_name')) {
            unset($params['user_name']);
        }
        if (collect($params)->has('uid')) {
            unset($params['uid']);
        }
        if (collect($params)->has('header_info')) {
            unset($params['header_info']);
        }
        //兼容pcode传值
        if(isset($params['pcode'])) {
            $supplier = SupplierModel::where('scode', $params['pcode'])->first();
            if (empty($supplier)) {
                throw new ParamInvalidException('供应商编码不存在', 30);
            }
            $user = ['pcode' => $params['pcode'], 'truename' => $supplier['supplier_name']];
        } else {
            $userData = UserRepository::getUserByUserName($params['app_key'], ['truename','pcode']);
            $user = collect($userData)->where('pcode','!=','20003JCP')->toArray();
        }

        if (empty($user) || empty($user['pcode'])) {
            throw new ParamInvalidException('app_key或pcode传入错误', 20);
        }



        // 验证省市合法
        $provinceAndCityCode = CityRepository::provinceAndCityCode();
        $cityCodeLegal = $provinceAndCityCode->pluck('city_code')->contains($params['city_code']);
        if (!$cityCodeLegal) {
            throw new ParamInvalidException('city_code错误！请核实', 20);
        }
        $provinceCode = $provinceAndCityCode->groupBy('level');
        $provinceCodeLegal = $provinceCode['2']->pluck('city_code')->contains($params['province_code']);
        if (!$provinceCodeLegal) {
            throw new ParamInvalidException('province_code错误！请核实', 20);
        }

        // 验证油品重复
        $priceList = collect($params['price_list'])->unique(function($item){
            return $item['oil_type'].$item['oil_name'].$item['oil_level'];
        });
        if (count($priceList) != count($params['price_list'])) {
            throw new ParamInvalidException('油品有重复！请核实', 20);
        }
        // 验证油品类型合法
        $oilMap = OilRepository::getOilMapTree();
        foreach ($priceList as $key => &$price) {
            $oilNameLegal = collect($oilMap['oil_name'])->has($price['oil_name']);
            if (!$oilNameLegal) {
                throw new ParamInvalidException('oil_name错误！请核实', 20);
            }
            $oilTypeLegal = isset($oilMap['oil_name_relation_type'][$price['oil_name']]) ? collect($oilMap['oil_name_relation_type'][$price['oil_name']])->has($price['oil_type']) : true;
            if (!$oilTypeLegal) {
                throw new ParamInvalidException('oil_type错误！请核实', 20);
            }
            $oilLevelLegal = isset($oilMap['oil_name_relation_type'][$price['oil_name']]) && isset($price['oil_level']) && !empty($price['oil_level']) ? collect($oilMap['oil_level'])->has($price['oil_level']) : true;
            if (!$oilLevelLegal) {
                throw new ParamInvalidException('oil_level错误！请核实', 20);
            }
            if (!isset($oilMap['oil_name_relation_type'][$price['oil_name']])) {
                $price['oil_type'] = $price['oil_level'] = '';
            }
        }

        $appStationId = $params['id'];
        $stationUniqueCode = $user['pcode'].$appStationId;$spcodeTradeType = config('station.spcode_trade_type_3');
        $stationData = StationRepository::getStationByWhere(['station_unique_code' => $stationUniqueCode, 'isdel' => 0], ['id','station_code','creator','station_type','trade_type','rebate_grade','station_brand'])->first();
        $stationId = $stationData['id'] ?? StationCreate::getOnlyId();
        unset($params['price_list'], $params['app_key'], $params['id']);
        $station = $params + [
            'id'                    => $stationId, //待优化，SnowFlake
            'station_name'          => $params['station_name'],
            'station_code'          => $stationData['station_code'] ?? StationCreate::generateStationCode(),
            'city_code'             => $params['city_code'],
            'provice_code'          => $params['province_code'],
            'pcode'                 => $user['pcode'],
            'isstop'                => $params['is_stop'],
            'station_type'          => $stationData['station_type'] ?? 1,
            'creator'               => $stationData['creator'] ?? $user['truename'],
            'modifier'              => $user['truename'],
            'isdel'                 => 0,
            'purch_way'             => 2,
            'address'               => $params['address'],
            'trade_type'            => isset($stationData['trade_type']) && !empty($stationData['trade_type']) ? $stationData['trade_type'] : (in_array($user['pcode'], $spcodeTradeType) ? 3 : 1),
            'oil_unit'              => 1,
            'open_type'             => 3,
            'app_station_id'        => $appStationId,
            'show_qrcode'           => 1,
            'station_unique_code'   => $stationUniqueCode,
            'lat'                   => $params['lat'],
            'lng'                   => $params['lng'],
            'rebate_grade'          => self::checkStationRebateGrade(array_get($params, 'rebate_grade', $stationData['rebate_grade'] ?? 'D')),
            'station_brand'         => self::checkStationBrand(array_get($params, 'station_brand', $stationData['station_brand'] ?? '8')),
        ];

        // 创建运营商关联关系 1号卡=汇通天下石油化工（大连）有限公司
        $stationPCode = [
            'id'                    => StationCreate::getOnlyId(),
            'station_id'            => $station['id'],
            'pcode'                 => $user['pcode'],
            'xpcode'                => '20003JCP',
            'createuser'            => $user['truename'],
            'createtime'            => date('Y-m-d H:i:s')
        ];

        // 创建商品（油罐/油枪）
        $tank = $guns = $stock = $lastStock = $supplierPrice = $platformPrice = [];
        $tankList = OilRepository::getOilByStationId($station['id']);
        $tankList = collect($tankList)->keyBy(function($item){return $item['station_id'].$item['oil_name'].$item['oil_type'].$item['oil_level'];})->toArray();
        $maxTankNum = collect($tankList)->pluck('name')->max();
        $gunList = OilRepository::getGun([$station['id']]);
        $gunList = collect($gunList)->keyBy('tank_id')->toArray();
        $maxGunName = collect($gunList)->pluck('name')->max();

        //上游浮动进价运营商 -- G7WALLET-1324
        $upper_float_pcode = env("UPPER_FLOAT_PCODE");
        $floatPcodeArr = explode(",",$upper_float_pcode);

        foreach ($priceList as $key => $oil) {
            $keyIndex = $station['id'].$oil['oil_name'].$oil['oil_type'].$oil['oil_level'];
            $tankNum = $tankList[$keyIndex]['name'] ?? ((int)$maxTankNum+$key+1).'号罐';
            $tankId = isset($tankList[$keyIndex]) ? $tankList[$keyIndex]['id'] : StationCreate::getOnlyId();
            $tank[] = [
                'id' => $tankId,
                'station_id' => $station['id'],
                'name' => $tankNum,
                'capacity' => 6000,
                'oil_type' => $oil['oil_type'],
                'oil_name' => $oil['oil_name'],
                'oil_level' => $oil['oil_level'],
            ];
            $guns[] = [
                    'id' => StationCreate::getOnlyId(),
                    'tank_id' => $tankId,
                    'station_id' => $station['id'],
                    'name' => $gunList[$tankId]['name'] ?? (int)$maxGunName+$key+1 . '号枪',
                    'gpsno' => ''
            ];
            $stock[] = [
                'id' => StationCreate::getOnlyId(),
                'station_id' => $station['id'],
                'pcode' => $user['pcode'],
                'oil_type' => $oil['oil_type'],
                'oil_name' => $oil['oil_name'],
                'oil_level' => $oil['oil_level'],
                'stock' => 0,
            ];
            $lastStock[] = [
                'id' => StationCreate::getOnlyId(),
                'station_id' => $station['id'],
                'tank_id' => $tankId,
                'pcode' => $user['pcode'],
                'oil_type' => $oil['oil_type'],
                'oil_name' => $oil['oil_name'],
                'oil_level' => $oil['oil_level']
            ];
//            $supplierPriceExistsParam = PriceRepository::getPriceByWhere(['station_id' => $station['id'],'pcode' => $user['pcode'],'spcode' => $user['pcode'],'orgcode' => '20003JCP','card_name' => 'all', 'oil_name' => $oil['oil_name'],'oil_type' => $oil['oil_type'], 'oil_level' => $oil['oil_level']])->toArray();
            $price = [
                'station_id' => $station['id'],
                'price_source' => 4,
                'float_price' => 0,
                'card_name' => 'all',
                'spcode' => $user['pcode'],
                'price' => $oil['price'],
                'pay_price' => $oil['price'],
//                'mac_price' => $oil['mac_price'] ?? (!empty($supplierPriceExistsParam) && isset($supplierPriceExistsParam['mac_price']) ? $supplierPriceExistsParam['mac_price'] : '0.00'),
                'mac_price' => $oil['mac_price'] ?? '0.00',
                'oil_type' => $oil['oil_type'],
                'oil_name' => $oil['oil_name'],
                'oil_level' => $oil['oil_level'],
                'other_id' => $station['app_station_id'],
                'list_md5text' => md5($user['pcode'].$station['id'].$oil['oil_type'].$oil['oil_name'].$oil['oil_level'].'2003JCP'.'all'),
                'modifier' => $user['truename'],
                'sort' => 1000,
                'isdisplay' => 1,
                'from_source' => 4,
                'starttime' => date('Y-m-d H:i:s'),
                'endtime' => '2099-12-31 23:59:59',
            ];
            //G7WALLET-1324
//            $price['mac_price'] = empty($price['mac_price']) ? $price['price'] : $price['mac_price'];
            $price['mac_price'] = ($price['mac_price'] == 0) ? $price['price'] : $price['mac_price'];

            $supplierPrice[] = $price + [
                'id' => StationCreate::getOnlyId(),
                'orgcode' => '20003JCP',
                'pcode' => $user['pcode'],
                'support_type' => 1,
                'ispcode' => 1
                ];
            if(in_array($user['pcode'],$floatPcodeArr)){
                $price['price_source'] = 6;
                $price['float_price'] = 0;
            }
            $platformPrice[] = $price + [
                'id' => StationCreate::getOnlyId(),
                'orgcode' => 'all',
                'pcode' => '20003JCP',
                'support_type' => 2,
                'ispcode' => 0
            ];
        }

        // 创建账户
        $account = [
            'id' => StationCreate::getOnlyId(),
            'account_no' => StationCreate::generateAccountNo(),
            'orgcode' => '20003JCP',
            'pcode' => $user['pcode'],
            'account_style' => 1,
        ];

        return [$station, $stationPCode, $tank, $guns, $stock, $lastStock, $supplierPrice, $platformPrice, $account];
    }

    /**
     * 创建站
     * @param $params
     * @return mixed
     * @throws ParamInvalidException
     * @throws \Exception
     */
    public static function batchUpdateOrCreateStation($params)
    {
        $redisConn = Redis::connection();$config = config('redis');
        //开始加锁，剔除重复提交的问题
        //操作数据
        $uniqueKey = ($params['app_key'] ?? '').($params['pcode'] ?? '').$params['id'];
        $lockValue = $uniqueKey;
        $lockKey = $config['s_upOrCreate_prefix'].'stationUpdateOrCreate_'.$lockValue;

        //修复判断缓存是否已经存在
        $hasKeyExist = $redisConn->setnx($lockKey,$lockValue);
        if($hasKeyExist) {
            list($station, $stationPCode, $tank, $guns, $stock, $lastStock, $supplierPrice, $platformPrice, $account) = self::initCreateParam($params);
            DB::connection('mysql_gas')->beginTransaction();
            try{
                $stationInfo = StationRepository::getStationByWhere([
                    'station_unique_code' => $station['station_unique_code']
                ], ['card_classify']);
                //创建站基本信息
                StationRepository::stationUpdateOrCreate($station);
                StationPCodeRepository::stationPCodeFirstOrCreate($stationPCode);

                //创建商品（油品、油枪）
                OilRepository::oilUpdateOrCreate($tank);
                OilRepository::gunUpdateOrCreate($guns);
                OilRepository::oilStockFirstOrCreate($stock);
                OilRepository::oilLastStockUpdateOrCreate($lastStock);

                //创建价格
                PriceRepository::supplierPriceUpdateOrCreate($supplierPrice, 'OA');
                PriceRepository::commonPlatformPriceUpdateOrCreate($platformPrice, 'OA');

                //创建账户
                OrgAccountRepository::orgAccountFirstOrCreate($account);
                //如果油品为空，那么删该站点所有油品
                if(empty($params['price_list']) && is_array($params['price_list'])) {
                    PriceRepository::delOilByStationId($station['id']);
                }
                DB::connection('mysql_gas')->commit();
                //上游所有站点及价格都接收，gms都同步更新，但是永久下线的不推下游oa
                if (($stationInfo[0]['card_classify'] ?? 0) == StationModel::STATION_FOREVER_OFFLINE) {
//                    DB::connection('mysql_gas')->commit();
                    $redisConn->del($lockKey);
                    return [$station['id'],'站点永久下线不接收数据推送'];
//                    throw new RuntimeException("站点永久下线不接收数据推送", 5000111);
                }
                //推送下游oa
                PriceCalculation::dispatch(['id' => $station['id'],'header_info' => Request::get('header_info')])->onQueue('batch-price');
                $redisConn->del($lockKey);
            }catch (\Exception $e) {
                $redisConn->del($lockKey);
                DB::connection('mysql_gas')->rollback();
                throw new RuntimeException($e->getMessage(), $e->getCode() != 0 ? $e->getCode() : 2);
            }
            return [$station['id'],''];
        } else {
            // 防止死锁
            if($redisConn->ttl($lockKey) == -1){
                $redisConn->expire($lockKey, 1);
            }
            throw new ParamInvalidException('请勿重复提交，稍后再试!');
        }


    }

    /**
     * 批量停用
     * @param array $params
     * @return mixed
     * @throws ReponseException
     */
    public static function batchStop(array $params)
    {
        $params = collect($params['id_list'])->unique(function($item){
            return $item['pcode'].$item['app_station_id'];
        })->toArray();
        DB::connection('mysql_gas')->beginTransaction();
        try{
            $stationId = StationRepository::stationBatchStopByAppStationId($params);
            DB::connection('mysql_gas')->commit();
        }catch (\Exception $e) {
            DB::connection('mysql_gas')->rollback();
            throw new ReponseException($e->getMessage(), 2);
        }
        return $stationId == 'true' ? [] : $stationId;
    }

    /**
     * 校验油站品牌编码是否符合G7规则
     *
     * @param $station_brand
     * @return int
     * @throws \Exception
     */
   public static function checkStationBrand($station_brand)
   {
       $g7_station_brand = array_get(config('station'), 'station_brand', []);
       if (empty($g7_station_brand)) {
           throw new ParamInvalidException('G7油站品牌配置文件丢失,请联系管理员!');
       }
       if (!in_array($station_brand, $g7_station_brand)) {
           throw new ParamInvalidException('油站品牌号码不符合G7编码标准,请参考:'.json_encode($g7_station_brand, JSON_UNESCAPED_UNICODE));
       }

       return $station_brand;
   }

   public static function checkStationRebateGrade($rebateGrade)
   {
       $rebateGradeConfig = array_get(config('station'), 'rebate_grade', []);
       if (empty($rebateGradeConfig)) {
           throw new ParamInvalidException('返利档位配置文件丢失,请联系管理员!');
       }
       $rebateGradeData = array_values($rebateGradeConfig);
       if (!in_array($rebateGrade, $rebateGradeData)) {
           throw new ParamInvalidException('返利档位不符合G7编码标准,请参考:'.json_encode($rebateGradeData, JSON_UNESCAPED_UNICODE));
       }
       return $rebateGrade;
   }

    /**
     * 根据站点ID批量获取站点信息
     *
     * @param $params
     * @return array
     */
   public static function getStationBaseInfoByIdBatch($params)
   {
        $stationIdBatch = array_get($params, 'station_id', []);
        $select = array_get($params, 'select', []);
        if (empty($stationIdBatch)) {
            return [];
        }
        // gas_station基本信息
        $stationList = StationToolRepository::getBatchStationBasicInfoByParams(['id' => $stationIdBatch], $select, true);
        if (empty($stationList)) {
            return [];
        }
        // gas_supplier获取经销商信息
        $supplierList = [];
        if (in_array('pcode', $select)) {
            // 获取运营商名称
            $supplierList = SupplierRepository::getSupplierListByScode(
                ['scode', 'supplier_name'],
                array_column($stationList, 'pcode')
            )->keyBy('scode')->toArray();
        }
        // gas_city获取省、市信息
        $cityList = [];
        if (in_array('provice_code', $select) || in_array('city_code', $select)) {
            // 获取省市区信息
            $cityList = CityRepository::getCityList()->keyBy('city_code')->toArray();
        }

        // 赋值
        foreach ($stationList as &$item) {
            // 运营商
            if (!empty($supplierList)) {
                if (array_key_exists($item['pcode'], $supplierList)) {
                    $item['supplier_name'] = $supplierList[$item['pcode']]['supplier_name'];
                }
            }

            // 省、市信息
            if (!empty($cityList)) {
                if (array_key_exists($item['provice_code'], $cityList)) {
                    $item['province_name'] = $cityList[$item['provice_code']]['city_name'];
                }
                if (array_key_exists($item['city_code'], $cityList)) {
                    $item['city_name'] = $cityList[$item['city_code']]['city_name'];
                }
            }
        }

        return $stationList;
   }

    /**
     * 校验站点的真实性
     *
     * @param $station_id
     * @throws \Exception
     */
   public static function checkStationId($station_id)
   {
       $stationIdBatch = explode(',', $station_id);
       $stationBatch = collect(StationToolRepository::getBatchStationBasicInfoByParams(['id' => $stationIdBatch], ['*'], true))->pluck('id')->all();
       if (empty($stationBatch)) {
           throw new \Exception('站点ID:'.$station_id.'不存在!', ErrorCodeService::addUserErrorCode(40303));
       }
       $errorStation = array_diff($stationIdBatch, $stationBatch);
       if (!empty($errorStation)) {
           throw new \Exception('站点ID:'.implode(',', $errorStation).'不存在!', ErrorCodeService::addUserErrorCode(40303));
       }
   }

   public static function getAppStationIdByPcode($pcode)
   {
       $stationData = StationRepository::getStationByWhere(['pcode' => $pcode], ['id','app_station_id']);
       if (collect($stationData)->isEmpty()) {
           return [];
       }
       return collect($stationData)->toArray();
   }
}
