<?php


namespace App\Services\Station;

use App\Jobs\GetStationCompareAndSendMail;
use App\Library\Helper\GaoDeApi;
use App\Library\Helper\StationSearch;
use App\Library\Validate;
use App\Models\Station\StationFilterExtend;
use App\Repositories\City\CityRepository;
use App\Repositories\Dict\DictRepository;
use App\Repositories\Export\StationExportRepository;
use App\Repositories\Price\PriceRepository;
use App\Repositories\Station\StationToolRepository;
use App\Repositories\Supplier\SupplierRepository;
use App\Services\History\HistoryService;
use App\Services\Supplier\SupplierService;
use App\Servitization\Gaode;

class StationToolService
{
    public function __construct()
    {
    }

    /**
     * 获取单个站点的基础信息
     *
     * @param $params
     * @return array
     */
    public static function getOneStationBasicInfoByParams($params)
    {
        return StationToolRepository::getOneStationBasicInfoByParams($params);
    }

    /**
     * 获取所有省
     *
     * @return mixed
     * @throws \Exception
     */
    public static function getAllProvince()
    {
        $redis = app('redis');
        $redisConfig = config('redis');
        if ($province = $redis->get($redisConfig['get_all_province']['key_name'])) {
            return json_decode($province, true);
        }

        $province = CityRepository::province();
        if (empty($province)) {
            throw new \Exception('获取所有省信息异常,请联系管理员!');
        }
        // 缓存到redis
        $redis->setex($redisConfig['get_all_province']['key_name'], $redisConfig['get_all_province']['left_time'], json_encode($province));

        return $province;
    }

    /**
     * 根据省ID获取所属市
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public static function getCityByLimit($params)
    {
        $redis = app('redis');
        $redisConfig = config('redis');
        $provinceCode = array_get($params, 'province_code', '110000000000');
        $page = array_get($params, 'page', 1);
        $limit = array_get($params, 'limit', 10);

        if (empty($provinceCode)) {
            throw new \Exception('invalid param province_code:'.$provinceCode);
        }
        if ($city = $redis->get($redisConfig['get_all_city_by_province']['key_name'].(string)$provinceCode)) {
            $city = json_decode($city, true);
        } else {
            $city = CityRepository::getCityByProvinceCode($provinceCode, true);
            // 缓存到redis
            $redis->setex($redisConfig['get_all_city_by_province']['key_name'].(string)$provinceCode, $redisConfig['get_all_city_by_province']['left_time'], json_encode($city));
        }

        $count = collect($city)->count();
        $data = collect($city)->forPage($page, $limit)->all();

        return ['count' => $count, 'data' => $data, 'page' => $page, 'limit' => $limit];
    }

    //查询油站列表
    public static function getThirdStationList($param, $limit)
    {
        return StationToolRepository::getThirdStationListByContion($param, $limit)->toArray();
    }

    //发现新站
    public static function findNewStation($params)
    {
        return StationToolRepository::findNewStation($params);
    }

    //发现差别的站
    public static function getDifferentStationList($params)
    {
        return StationToolRepository::getDifferentStationList($params);
    }

    /**
     * 发现重复站点
     *
     * @param array $params
     * @param int $page
     * @param int $limit
     * @return mixed
     */
    public static function getStationDumplicateList($params = [], $page = 1, $limit = 10)
    {
        $distance = array_get($params, 'distance', 200);

        $_isExport = array_get($params,"_isExport",0);
        $color = [
            '#FFB800',
            '#FF5722',
            '#01AAED',

        ];
        // mysql过滤
        $andWhereField = ['card_classify', 'isstop', 'provice_code', 'city_code', 'pcode'];
        // php过滤
        $phpField = ['is_same_pcode', 'id', 'is_not_repeat', 'is_not_repeat_group'];
        $condition = [];
        $phpCondition = [];
        foreach ($params as $field => $value) {
            if (in_array($field, $andWhereField) && !is_null($value)) {
                $condition[$field] = $value;
                continue;
            }
            if (in_array($field, $phpField) && !is_null($value)) {
                $phpCondition[$field] = $value;
            }
        }
        // 数组
        if (!empty($condition['pcode'])) {
            $condition['pcode'] = explode(',', $condition['pcode']);
        }
        if (!empty($phpCondition['id'])) {
            $phpCondition['id'] = explode(',', $phpCondition['id']);
        }

        //定时任务导出
        if($_isExport == 1){
            return StationExportRepository::getStationDumplicate($condition, $phpCondition, $distance);
        }

        // 获取油站列表
        $stationList =  StationToolRepository::getStationDumplicateList($condition,$phpCondition, $page, $limit, $distance);

        if (empty($stationList['data'])) {
            return $stationList;
        }
        // 获取运营商名称
        $supplierListByScode = SupplierRepository::getSupplierListByScode(['scode', 'supplier_name'], array_column($stationList['data'], 'pcode'))->keyBy('scode');

        // 获取省市区信息
        $cityListByCode = CityRepository::getCityList()->keyBy('city_code');

        // 获取站点交易笔数
        $transactionLogCount = [];
        $stationIds = array_column($stationList['data'],"id");
        $transactionLogCount = HistoryService::getTransactionLogByStationIdBatch($stationIds);

        foreach ($stationList['data'] as $key => &$item) {
            $item['supplier_name'] = $supplierListByScode[$item['pcode']]['supplier_name'] ?? '';
            $item['city_name'] = $cityListByCode[$item['city_code']]->city_name ?? '';
            $item['provice_name'] = $cityListByCode[$item['provice_code']]->city_name ?? '';

            // 拼接交易笔数
            if (array_key_exists($item['id'], $transactionLogCount)) {
                $item['latest_one_month_order_count'] = $transactionLogCount[$item['id']]['latest_one_month_order_count'];
                $item['latest_three_month_order_count'] = $transactionLogCount[$item['id']]['latest_three_month_order_count'];
                $item['latest_one_month_order_money'] = $transactionLogCount[$item['id']]['latest_one_month_order_money'];
            }

            $item['color'] = $color[($item['group_id']%count($color))];
            // 解析扩展表属性
            $is_not_repeat = empty($item['is_not_repeat']) ? StationFilterExtend::IS_REPEAT : StationFilterExtend::IS_NOT_REPEAT;
            $item['is_not_repeat'] = StationFilterExtend::$IS_NOT_REPEAT[$is_not_repeat]['msg'];
        }

        return $stationList;
    }

    /**
     * 获取非重复站点
     *
     * @param array $params
     * @param int $page
     * @param int $limit
     * @return array
     */
    public static function getUniqueStationList($params = [], $page = 1, $limit = 10)
    {
        // 重复站点距离,默认200m
        $distance = array_get($params, 'distance', 200);
        // mysql过滤
        $andWhereField = ['card_classify', 'isstop', 'provice_code', 'city_code', 'id', 'pcode'];

        $condition = [];
        foreach ($params as $field => $value) {
            if (in_array($field, $andWhereField) && !is_null($value)) {
                $condition[$field] = $value;
                continue;
            }
        }
        // 数组
        if (!empty($condition['pcode'])) {
            $condition['pcode'] = explode(',', $condition['pcode']);
        }
        if (!empty($condition['id'])) {
            $condition['id'] = explode(',', $condition['id']);
        }
        // 获取油站列表
        $stationList =  StationToolRepository::getUniqueStationList($condition, $page, $limit, $distance);
        if (empty($stationList['data'])) {
            return $stationList;
        }
        // 获取运营商名称
        $supplierListByScode = SupplierRepository::getSupplierListByScode(['scode', 'supplier_name'], array_column($stationList['data'], 'pcode'))->keyBy('scode');

        // 获取省市区信息
        $cityListByCode = CityRepository::getCityList()->keyBy('city_code');

        foreach ($stationList['data'] as $key => &$item) {
            $item['supplier_name'] = $supplierListByScode[$item['pcode']]['supplier_name'] ?? '';
            $item['city_name'] = $cityListByCode[$item['city_code']]->city_name ?? '';
            $item['provice_name'] = $cityListByCode[$item['provice_code']]->city_name ?? '';
        }

        return $stationList;
    }

    /**
     * 站点比价
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public static function getStationComparePrice($params)
    {
        $station_id = array_get($params, 'station_id', []);
        if (empty($station_id)) {
            throw new \Exception('invalid params station_id:'.implode(',', $station_id));
        }
        $station_id = is_array($station_id) ? $station_id : explode(',', $station_id);

        // 获取站点信息
        $stationInfo = StationToolRepository::getStationInfoByIdBatch($station_id);
        // 获取运营商名称
        $supplierListByScode = SupplierRepository::getSupplierListByScode(['scode', 'supplier_name'], array_column($stationInfo, 'pcode'))->keyBy('scode')->toArray();
        // 获取站点交易笔数
        $transactionLogCount = HistoryService::getTransactionLogByStationIdBatch($station_id);
        // 获取站点油品价格表
        $stationPriceBatch = PriceRepository::getBasePriceByStationId($station_id);

        // 将前端数据整理的压力放在后端,暂时返回规范后的二维数组
        foreach ($stationInfo as &$item) {
            // 拼接运营商名称
            if (array_key_exists($item['pcode'], $supplierListByScode)) {
                $item['supplier_name'] = $supplierListByScode[$item['pcode']]['supplier_name'];
            }
            // 拼接交易笔数
            if (array_key_exists($item['id'], $transactionLogCount)) {
                $item['latest_one_month_order_count'] = $transactionLogCount[$item['id']]['latest_one_month_order_count'];
                $item['latest_three_month_order_count'] = $transactionLogCount[$item['id']]['latest_three_month_order_count'];
            }
            // 拼接价格表
            if (array_key_exists($item['id'], $stationPriceBatch)) {
                $item['last_update_time'] = $stationPriceBatch[$item['id']]['need_time'];
                // 价格
                $item = array_merge($item, $stationPriceBatch[$item['id']]['need_array']);
            }
        }

        return $stationInfo;
    }

    /**
     * 非重复站点标记
     *
     * @param $param
     * @return bool
     * @throws \Exception
     */
    public static function updateIsNotRepeatByStationIdBatch($param)
    {
        $stationIdArray = array_get($param, 'station_id', []);
        $isNotRepeat = array_get($param, 'is_not_repeat', 2);

        if (empty($stationIdArray)) {
            throw new \Exception('invalid param station_id:'.implode(',', $stationIdArray));
        }

        if (!array_key_exists($isNotRepeat, StationFilterExtend::$IS_NOT_REPEAT)) {
            throw new \Exception('invalid param is_not_repeat:'.$isNotRepeat);
        }
        // 幂等校验
        $result = StationToolRepository::getFilterExtendByStationIdBatch($stationIdArray, false);
        // 过滤重复station_id
        $filterArray = [];
        if (!empty($result->toArray())) {
            $filterArray = $result->filter(function ($item, $key) use ($isNotRepeat, $filterArray) {
                if (strcmp($item->is_not_repeat, StationFilterExtend::$IS_NOT_REPEAT[$isNotRepeat]['msg'])) {
                    return false;
                }
                return true;
            })->pluck('station_id')->toArray();
        }

        $stationIdArray = array_diff($stationIdArray, $filterArray);
        if (empty($stationIdArray)) {
            throw new \Exception('请勿重复提交!');
        }

        $needArray = [];
        foreach ($stationIdArray as $station_id) {
            $needArray[] = [
                'station_id' => $station_id,
                'is_not_repeat' => $isNotRepeat,
            ];
        }

        // 插入或更新
        StationToolRepository::insertOrUpdateExtendInfo($needArray, ['is_not_repeat']);

        return true;
    }

    /**
     * 批量站点比较的结果以邮件形式发送
     *
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public static function stationCompareAndSendMail($params)
    {
        $email = collect(explode(',', array_get($params, 'email', '')))->filter(function ($value, $key) {
            return Validate::checkEmail($value);
        });
        $stationCode = explode(',', array_get($params, 'station_code', ''));
        if (empty($email)) {
            throw new \Exception('邮箱不能为空!');
        }
        if (empty($stationCode)) {
            throw new \Exception('站点编码不能为空!');
        }

        // 队列处理
        $job = (new GetStationCompareAndSendMail($params, $email))->onQueue('long-time-queue');
        dispatch($job);

        return true;
    }

    /**
     * 获取低收益站点和价格信息
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public static function getLowIncomeStationDetail($params)
    {
        $stationCode = explode(',', array_get($params, 'station_code', ''));
        $oil_time_start = array_get($params, 'ge_oil_time', '');
        $oil_time_end = array_get($params, 'le_oil_time', '');
        $perStationLimit = array_get($params, 'per_station_limit', 10);
        $repeatStationDistance = array_get($params, 'repeat_station_distance', 200);
        $notRepeatStationDistance = array_get($params, 'not_repeat_station_distance', 50000);
        $getGaoDeDistance = array_get($params, 'get_gaode_distance', 1);
        if (empty($stationCode)) {
            throw new \Exception('站点编码格式错误,或站点编码无效!');
        }
        if (!empty($oil_time_start) && !empty($oil_time_end)) {
            // 不能超过180天
            if (strtotime($params['le_oil_time']) - strtotime($params['ge_oil_time']) > 180*86400) {
                throw new \Exception('最多可查180天加油记录!');
            }

            $oil_time_start = date('Y-m-d 00:00:00', strtotime($oil_time_start));
            $oil_time_end = date('Y-m-d 23:59:59', strtotime($oil_time_end));
        }

        // 站点信息
        $needArray = [
            'station_code' => $stationCode
        ];
        $stationInfo = self::getLimitStation($needArray, $perStationLimit, $repeatStationDistance, $notRepeatStationDistance, $getGaoDeDistance);

        if (empty($stationInfo)) {
            throw new \Exception('站点编码格式错误,或站点编码无效!');
        }

        // 价格信息
        $stationIdBatch = array_column($stationInfo, 'id');
        $stationPriceInfo = self::getStationOliPriceSale($stationIdBatch);
        // 交易流水信息
        $needArray = [
            'station_id' => $stationIdBatch,
            'ge_oil_time' => $oil_time_start,
            'le_oil_time' => $oil_time_end,
        ];
        $stationPayInfo = self::getStationPayInfo($needArray);
        $needArray = [];
        foreach ($stationInfo as $item) {
            $middleArray = [
                'group_id' => $item['group_id'],
                'station_name' => $item['station_name'],
                'station_code' => $item['station_code'],
                'supplier_name' => '',
                'station_brand_name' => $item['station_brand_name'],
                'special_tag_name' => $item['special_tag_name'],
                'distance' => $item['distance'],
                'gaode_distance' => $item['gaode_distance'],
                'province_name' => '',
                'city_name' => '',
                'address' => $item['address'],
                'lng' => $item['lng'],
                'lat' => $item['lat'],
                'rebate_grade_name' => $item['rebate_grade_name'],
                'is_stop_name' => $item['is_stop_name'],
                'card_classify_name' => $item['card_classify_name'],

            ];
            $needArray[] = array_merge($middleArray, array_get($stationPriceInfo, $item['id'], []), array_get($stationPayInfo, $item['id'], []));
        }

        return $needArray;
    }

    /**
     * 高德地图匹配站点
     *
     * @param $params
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function getRimStation($params)
    {
        $lng = array_get($params, 'lng', 0);
        $lat = array_get($params, 'lat', 0);
        $stationCode = array_get($params, 'station_code', '');
        $distance = array_get($params, 'distance', 0)*1000;
        if (empty($distance)) {
            throw new \Exception('筛选半径必填!');
        }
        // 查询条件
        $publicWhere = ['isdel' => 0, 'pcode' => StationPCodeService::getOneCardPcode()];

        // 按经纬度查询
        if (!empty($lng) && !empty($lat)) {
            $lngLatArray = StationSearch::getLngLatBetween($lng, $lat, $distance);
            $whereParams = array_merge($lngLatArray, $publicWhere);
            $rimStation = self::getRimStationByParams($whereParams, $distance, $lng, $lat);
            // 按站点编码查询
        } elseif(!empty($stationCode)) {
            $stationInfo = StationToolRepository::getOneStationBasicInfoByParams(['station_code' => $stationCode]);
            if (empty($stationInfo)) {
                return [];
            }
            $lngLatArray = StationSearch::getLngLatBetween($stationInfo['lng'], $stationInfo['lat'], $distance);
            $whereParams = array_merge($lngLatArray, $publicWhere);
            $rimStation = self::getRimStationByParams($whereParams, $distance, $stationInfo['lng'], $stationInfo['lat'], $stationCode);
        } else {
            throw new \Exception('无效的查询方式:支持按站点名称搜索、按经纬度搜索。优先按经纬度搜索!');
        }

        // 获取销价
        $stationIdBatch = array_column($rimStation, 'id');
        $stationOilPrice = self::getRimStationOilPrice($stationIdBatch);

        // 返利档位
        $rebateGradeBatch = array_flip(config('station')['rebate_grade']);
        // 组装参数
        $needArray = [];
        foreach ($rimStation as $item) {
            $item['is_online'] = $item['card_classify'];
            if ($item['isstop'] == 1) {
                $item['is_online'] = 1;
            }
            $array = [
                'lng' => $item['lng'],
                'lat' => $item['lat'],
                'is_online' => $item['is_online'] == 2,
                'special_tag' => $item['special_tag'], // 1中心站点 0非中心站点
                'station_name' => $item['station_name'],
                'station_code' => $item['station_code'],
                'supplier_name' => '',
                'rebate_grade' => $rebateGradeBatch[$item['rebate_grade']],
                'distance' => empty(round($item['distance']/1000, 2)) ? 0 : round($item['distance']/1000, 2).'km',
            ];

            $needArray[] = array_merge($array, $stationOilPrice[$item['id']]);
        }

        // 经纬度查询,请求高德信息
        if (!empty($lng) && !empty($lat)) {
            $location = Gaode::getInfoFromGaoDe($lng, $lat);
            $array = [
                'lng' => $lng,
                'lat' => $lat,
                'card_classify' => 2, // 默认上线
                'special_tag' => 1, // 中心站点
                'station_name' => array_get($location, 'regeocode.formatted_address', '未知'),
                'supplier_name' => '',
                'rebate_grade' => '',
                'distance' => 0,
                'oil_info' => [],
            ];

            array_unshift($needArray, $array);
        }

        return $needArray;
    }

    /**
     * 地图匹配站点[多个站点编码]
     *
     * @param $params
     * @return array
     */
    public static function getStationMapByCodeBatch($params)
    {
        $stationCodeBatch = explode(',', array_get($params, 'station_code', ''));
        $isstop = array_get($params, 'isstop', 2);
        if (empty($stationCodeBatch)) {
            return [];
        }
        // 站信息
        $stationInfo = self::getStationByCodeBatch($stationCodeBatch, $isstop);

        // 获取销价
        $stationIdBatch = array_column($stationInfo, 'id');
        $stationOilPrice = self::getRimStationOilPrice($stationIdBatch);

        // 返利档位
        $rebateGradeBatch = array_flip(config('station')['rebate_grade']);
        // 组装参数
        $needArray = [];
        foreach ($stationInfo as $item) {
            $array = [
                'lng' => $item['lng'],
                'lat' => $item['lat'],
                'card_classify' => $item['card_classify'],
                'special_tag' => $item['special_tag'], // 1中心站点 0非中心站点
                'station_name' => $item['station_name'],
                'supplier_name' => '',
                'rebate_grade' => $rebateGradeBatch[$item['rebate_grade']],
                'distance' => empty(round($item['distance']/1000, 2)) ? 0 : round($item['distance']/1000, 2).'km',
            ];

            $needArray[] = array_merge($array, $stationOilPrice[$item['id']]);
        }

        return $needArray;
    }

    /**
     * 站点坐标——获取站点油品销价
     *
     * @param $stationIdBatch
     * @return array
     */
    public static function getRimStationOilPrice($stationIdBatch)
    {
        /**
         * 格式化价格列表
         */
        $needArray = [];
        foreach ($stationIdBatch as $id) {
            $needArray[$id] = [
                'supplier_name' => '',
                'oil_info' => [],
            ];
        }

        // 将oil_type、oil_name、oil_level可视化
        $oilDictMap = DictRepository::getOilType();
        /**
         * 整理站:销价、浮动价关系
         */
        $getStation = StationBatchService::getStationPrice($stationIdBatch, 'GMS');
        if (count($stationIdBatch) == 1) {
            $getStation = [0 => collect($getStation)->all()];
        }
        collect($getStation)->each(function ($stationPriceBatch) use ($oilDictMap, &$needArray) {
            // 站点信息
            if (array_key_exists($stationPriceBatch['id'], $needArray)) {
                $needArray[$stationPriceBatch['id']]['supplier_name'] = $stationPriceBatch['supplier_name'];
            }
            // 价格信息
            collect($stationPriceBatch['price_list'])->each(function($price) use ($oilDictMap, &$needArray){
                $oilTypeVal = $oilDictMap['oil_type'][$price['oil_type']] ?? '';
                $oilNameVal = $oilDictMap['oil_name'][$price['oil_name']] ?? '';
                // 液化天然气、压缩天然气特殊处理
                if (!strcmp('液化天然气', $oilNameVal)) {
                    $oilNameVal = '';
                    $oilTypeVal = 'LNG';
                }
                if (!strcmp('压缩天然气', $oilNameVal)) {
                    $oilNameVal = '';
                    $oilTypeVal = 'CNG';
                }
                if (array_key_exists($price['station_id'], $needArray)) {
                    // 防止价格重复
                    if (empty($needArray[$price['station_id']]['oil_info'][$oilTypeVal.$oilNameVal])) {
                        $needArray[$price['station_id']]['oil_info'][] = [
                            'oil_name' => $oilTypeVal.$oilNameVal,
                            'oil_price' => $price['price'],
                        ];
                    }
                }
            });
        });
        return $needArray;
    }

    /**
     * 获取低收益站点周边的站点
     *
     * @param $whereArray
     * @param $perStationLimit
     * @param $repeatStationDistance
     * @param $notRepeatStationDistance
     * @param $getGaoDeDistance
     * @return array
     */
    public static function getLimitStation($whereArray, $perStationLimit, $repeatStationDistance, $notRepeatStationDistance, $getGaoDeDistance)
    {
        /**
         * 基础站点信息
         */
        $whereParams = array_merge(
            [
                // 'isdel' => 0, //2020-09-01春丽需要中心站点不过滤
                // 'isstop' => 0, // 在用
                'gt_lng' => 0,
                'gt_lat' => 0,
            ],
            $whereArray
        );

        $select = [
            'id', 'station_name', 'station_code', 'station_brand', 'lng', 'lat', 'address', 'rebate_grade',
            'isstop', 'rebate_grade', 'card_classify', 'station_type', 'trade_type', 'pcode'
        ];
        $middleStationInfo = StationToolRepository::getBatchStationBasicInfoByParams($whereParams, $select);
        if (empty($middleStationInfo)) {
            return [];
        }

        /**
         * 边界站点信息
         */
        $lngLatArray = [];
        foreach ($middleStationInfo as $item) {
            $lngLatArray[$item['station_code']] = StationSearch::getLngLatBetween($item['lng'], $item['lat'], $notRepeatStationDistance);
        }
        $whereParams = [
            'isdel' => 0,
            'isstop' => 0,
            'ge_lng' => collect($lngLatArray)->pluck('ge_lng')->min(),
            'le_lng' => collect($lngLatArray)->pluck('le_lng')->max(),
            'ge_lat' => collect($lngLatArray)->pluck('ge_lat')->min(),
            'le_lat' => collect($lngLatArray)->pluck('le_lat')->max(),
            'pcode'  => StationPCodeService::getOneCardPcode(),
        ];
        $allStation = StationToolRepository::getBatchStationBasicInfoByParams($whereParams, $select);

        // 分组
        $groupId = 1;
        $stationFilter = function ($middleStation, $lngLatArray) use ($allStation, $perStationLimit, $repeatStationDistance, $notRepeatStationDistance, $getGaoDeDistance, &$groupId) {

            /**
             * 捕获中心点
             */
            $starStation = array_merge($middleStation, ['distance' => '', 'gaode_distance' => '','special_tag' => 0, 'special_tag_name' => '标记站点', 'group_id' => $groupId]);

            /**
             * 计算距离中心点距离
             */
            $distanceStation = [];
            foreach ($allStation as $item) {
                // 纬度范围
                if (($item['lat'] > $lngLatArray['le_lat']) || ($item['lat'] < $lngLatArray['ge_lat'])) {
                    continue;
                }
                // 经度范围
                if (($item['lng'] > $lngLatArray['le_lng']) || ($item['lng'] < $lngLatArray['ge_lng'])) {
                    continue;
                }
                // 过滤特殊运营商
                if (in_array($item['pcode'], config('compare')['compare_ignore_pcodes'])) {
                    continue;
                }
                // 过滤中心点
                if (!strcmp($middleStation['station_code'], $item['station_code'])) {
                    continue;
                }
                $item['distance'] = (int)round(StationToolRepository::getdistance($item['lng'], $item['lat'], $starStation['lng'], $starStation['lat']));
                $distanceStation[] = $item;
            }
            unset($allStation);

            /**
             * 按距离排序
             */
            $distanceStation = collect($distanceStation)->sortBy('distance')->toArray();

            /**
             * 距离分析
             */
            $count = 1;
            $needStation = [];

            foreach ($distanceStation as $item) {
                // 重复站点筛选
                if (($item['distance'] <= $repeatStationDistance) && ($count <= $perStationLimit)) {
                    $item['gaode_distance'] = empty($getGaoDeDistance) ? '' : GaoDeApi::getDistanceFromStation($starStation['lng'], $starStation['lat'], $item['lng'], $item['lat']);
                    $item['special_tag'] = 1;
                    $item['special_tag_name'] = '疑似重复站点';
                    $item['group_id'] = $groupId;
                    $needStation[] = $item;
                    $count ++;
                    continue;
                }

                // 非重复站点筛选
                if (($item['distance'] <= $notRepeatStationDistance) && ($count <= $perStationLimit)) {
                    $item['gaode_distance'] =  empty($getGaoDeDistance) ? '' : GaoDeApi::getDistanceFromStation($starStation['lng'], $starStation['lat'], $item['lng'], $item['lat']);
                    $item['special_tag'] = 2;
                    $item['special_tag_name'] = '非重复站点';
                    $item['group_id'] = $groupId;
                    $needStation[] = $item;
                    $count ++;
                    continue;
                }
                break;
            }

            if (empty($needStation)) {
                return [];
            }
            // 压入星标站点
            array_unshift($needStation, $starStation);
            // 组
            $groupId ++;
            return $needStation;
        };

        $needStation = [];
        foreach ($middleStationInfo as $item) {
            if (empty($item['lng']) || empty($item['lat'])) {
                continue;
            }
            $needStation = array_merge($needStation, $stationFilter($item, $lngLatArray[$item['station_code']]));
        }
        return $needStation;
    }

    /**
     * 按半径匹配圆内站点
     *
     * @param array $whereParams
     * @param $distance
     * @param $lng
     * @param $lat
     * @param string $middleStationCode
     * @return array
     */
    public static function getRimStationByParams(array $whereParams, $distance, $lng, $lat, $middleStationCode = '')
    {
        $allStation = StationToolRepository::getBatchStationBasicInfoByParams($whereParams);
        if (empty($allStation)) {
            return [];
        }

        /**
         * 按stationCode查询时捕获中心点
         */
        $starStation = [];
        if (!empty($middleStationCode)) {
            foreach ($allStation as $key => $item) {
                if (strcmp($item['station_code'], $middleStationCode)) {
                    continue;
                }
                $item['distance'] = 0; // 中心点距离为0
                $item['special_tag'] = 1;
                $item['special_tag_name'] = '中心站点';
                $starStation = $item;
                unset($allStation[$key]);
                break;
            }
        }

        /**
         * 计算距离中心点距离
         */
        $distanceStation = [];
        foreach ($allStation as $item) {
            $item['distance'] = $nowDistance = (int)round(StationToolRepository::getdistance($item['lng'], $item['lat'], $lng, $lat));
            // 距离分析
            if ($nowDistance > $distance) {
                continue;
            }
            $item['special_tag'] = 0;
            $item['special_tag_name'] = '中心站点指定半径范围内的站';
            $distanceStation[] = $item;
        }
        unset($allStation);

        /**
         * 按距离排序
         */
        $distanceStation = collect($distanceStation)->sortBy('distance')->toArray();

        /**
         * 星标站点放首位
         */
        if (!empty($starStation)) {
            array_unshift($distanceStation, $starStation);
        }

        return $distanceStation;
    }

    /**
     * 根据站点编码获取地图展示的站点
     *
     * @param array $stationCodeBatch
     * @param $isStop
     * @return array
     */
    protected static function getStationByCodeBatch(array $stationCodeBatch, $isStop)
    {
        $needParams = [
            'isdel' => 0,
            'station_code' => $stationCodeBatch
        ];
        if (in_array($isStop,[0, 1])) {
            $needParams['isstop'] = $isStop;
        }

        $allStation = StationToolRepository::getBatchStationBasicInfoByParams($needParams);
        if (empty($allStation)) {
            return [];
        }

        /**
         * 规定第一个站点为中心站点
         */
        $starStation = array_shift($allStation);
        $starStation['distance'] = 0; // 中心点距离为0
        $starStation['special_tag'] = 1;
        $starStation['special_tag_name'] = '中心站点';

        /**
         * 计算距离中心点距离
         */
        $distanceStation = [];
        foreach ($allStation as $item) {
            $item['distance'] = (int)round(StationToolRepository::getdistance($item['lng'], $item['lat'], $starStation['lng'], $starStation['lat']));
            $item['special_tag'] = 0;
            $item['special_tag_name'] = '中心站点指定半径范围内的站';
            $distanceStation[] = $item;
        }
        unset($allStation);

        /**
         * 按距离排序
         */
        $distanceStation = collect($distanceStation)->sortBy('distance')->toArray();

        /**
         * 星标站点放首位
         */
        if (!empty($starStation)) {
            array_unshift($distanceStation, $starStation);
        }

        return $distanceStation;
    }

    /**
     * 获取站点对应油品的售价、浮动价
     *
     * @param $stationIdBatch
     * @return array
     */
    protected static function getStationOliPriceSale($stationIdBatch)
    {
        /**
         * 格式化价格列表
         */
        $needArray = [];
        foreach ($stationIdBatch as $id) {
            $needArray[$id] = [
                'supplier_name' => '',
                'province_name' => '',
                'city_name' => '',
                '0#_sale' => '',
                '-10#_sale' => '',
                '-20#_sale' => '',
                '-35#_sale' => '',
                '-40#_sale' => '',
                '90#_sale' => '',
                '92#_sale' => '',
                '95#_sale' => '',
                '98#_sale' => '',
                '101#_sale' => '',
                'lng_sale' => '',
                'cng_sale' => '',
                '0#_diff' => '',
                '-10#_diff' => '',
                '-20#_diff' => '',
                '-35#_diff' => '',
                '-40#_diff' => '',
                '90#_diff' => '',
                '92#_diff' => '',
                '95#_diff' => '',
                '98#_diff' => '',
                '101#_diff' => '',
                'lng_diff' => '',
                'cng_diff' => '',
            ];
        }

        // 将oil_type、oil_name、oil_level可视化
        $oilDictMap = DictRepository::getOilType();
        // 油品池,池子以外的油品不捕获(''兼容液化天然气部分数据)
        $oilTypeBatch = ['0#', '-10#', '-20#', '-35#', '-40#', '90#', '92#', '95#', '98#', '101#', 'LNG', 'CNG', ''];
        /**
         * 整理站、销价价、浮动价关系
         */
        $getStation = StationBatchService::getStationPrice($stationIdBatch, 'GMS');
        if (count($stationIdBatch) == 1) {
            $getStation = [0 => collect($getStation)->all()];
        }
        collect($getStation)->each(function ($stationPriceBatch) use ($oilDictMap, $oilTypeBatch, &$needArray) {
            // 站点信息
            if (array_key_exists($stationPriceBatch['id'], $needArray)) {
                $needArray[$stationPriceBatch['id']]['supplier_name'] = $stationPriceBatch['supplier_name'];
                $needArray[$stationPriceBatch['id']]['province_name'] = $stationPriceBatch['province_name'];
                $needArray[$stationPriceBatch['id']]['city_name'] = $stationPriceBatch['city_name'];
            }
            // 价格信息
            collect($stationPriceBatch['price_list'])->each(function($price) use ($oilDictMap, $oilTypeBatch, &$needArray){
                $oilTypeVal = $oilDictMap['oil_type'][$price['oil_type']] ?? '';
                $oilNameVal = $oilDictMap['oil_name'][$price['oil_name']] ?? '';
                // 池子过滤
                if (in_array($oilTypeVal, $oilTypeBatch)) {
                    // 特殊处理
                    if (!strcmp('液化天然气', $oilNameVal)) {
                        $oilTypeVal = 'lng';
                    }
                    if (!strcmp('压缩天然气', $oilNameVal)) {
                        $oilTypeVal = 'cng';
                    }
                    if (array_key_exists($price['station_id'], $needArray)) {
                        // 价格重复取第一个
                        if (empty($needArray[$price['station_id']][$oilTypeVal])) {
                            // 固定价price_source=4 浮动价price_source=6
                            $needArray[$price['station_id']][$oilTypeVal.'_sale'] = (float)$price['price'];
                            $needArray[$price['station_id']][$oilTypeVal.'_diff'] = $price['price_source'] == 4 ? (float)$price['price'] - (float)$price['supplier_price'] : (float)$price['float_price'];
                        }
                    }
                }
            });
        });
        return $needArray;
    }

    /**
     * 交易流水信息
     *
     * @param $whereParams
     * @return array
     * @throws \Exception
     */
    public static function getStationPayInfo($whereParams)
    {
        $stationIdBatch = $whereParams['station_id'];
        /**
         * 格式化参数
         */
        $needArray = [];

        foreach ($stationIdBatch as $stationId) {
            $needArray[$stationId] = [
                'diesel_num' => 0,
                'gasoline_num' => 0,
                'lng_num' => 0,
                'cng_num' => 0,
                'total_num' => 0,
                'diesel_money' => 0,// 柴油
                'gasoline_money' => 0,// 汽油
                'lng_money' => 0,
                'cng_money' => 0,
                'total_money' => 0,
            ];
        }

        /**
         * 获取交易流水
         */
        $needParams = array_merge([
            'select' => ['station_id', 'sum(pay_money) as total_money', 'count(*) as pay_num', 'oil_name'],
            'group_by_oil_type' => 0,
            'group_by_oil_level' => 0,
            'group_by_oil_name' => 1
        ], $whereParams);

        $stationPayInfo = HistoryService::getSailInfoByStationIdBatch($needParams);
        if (empty($stationPayInfo)) {
            return $needArray;
        }

        /**
         * 格式化流水
         */
        $oilNameEnum = ['液化天然气' => 'lng_', '压缩天然气' => 'cng_', '柴油' => 'diesel_', '汽油' => 'gasoline_'];
        foreach ($stationPayInfo as $stationId => $item) {
            foreach ($item as $oilName => $value) {
                if (array_key_exists($oilName, $oilNameEnum)) {
                    $needArray[$stationId][$oilNameEnum[$oilName].'money'] = (float)$value['total_money'];
                    $needArray[$stationId][$oilNameEnum[$oilName].'num'] = $value['pay_num'];
                }
                // 站点笔数、站点金额
                $needArray[$stationId]['total_money'] += (float)$value['total_money'];
                $needArray[$stationId]['total_num'] += $value['pay_num'];
            }
        }

        return $needArray;
    }

    /**
     * 获取进价可编辑的运营商编码
     *
     * @return \Illuminate\Config\Repository|mixed
     */
    public static function getEditPricePcodes()
    {
        return SupplierService::getEditSupplierPrice();
    }
}
