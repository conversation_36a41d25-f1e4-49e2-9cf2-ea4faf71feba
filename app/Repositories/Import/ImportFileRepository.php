<?php
namespace App\Repositories\Import;

use App\Models\Gas\ImportFileModel;

class ImportFileRepository
{
    public static function addFile($params = [])
    {
        return (new ImportFileModel())->insertData($params);
    }

    public static function getListByCondition($params = [])
    {
        $query = ImportFileModel::query();
        return ImportFileModel::scopeWithCondition($query, $params)->get();
    }

    public static function getInfoByCondition($params = [])
    {
        $query = ImportFileModel::query();
        return ImportFileModel::scopeWithCondition($query, $params)->first();
    }

    public static function edit($params = [],$data = [])
    {
        return (new ImportFileModel())->editData($params,$data);
    }

}
