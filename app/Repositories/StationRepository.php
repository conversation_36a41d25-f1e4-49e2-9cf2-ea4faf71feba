<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Http\Defines\CommonError;
use App\Models\StationExtModel;
use App\Models\StationModel;
use App\Servitization\FossStation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use RuntimeException;
use Throwable;

class StationRepository extends EloquentRepository
{
    protected $stationModel;
    protected $stationExtModel;

    public function __construct(StationModel $stationModel, StationExtModel $stationExtModel)
    {

        $this->model = $stationModel;
        $this->stationModel = $stationModel;
        $this->stationExtModel = $stationExtModel;
    }

    //查询
    public function getInfo(array $condition)
    {
        $condition['isdel'] = 0;
        $data = $this->stationModel->select("id","pcode","station_code","station_name","station_oil_unit")->withCondition($condition)->first();
        return $data;
    }

    public function stationTank(array $condition)
    {
        // 获取所有油罐和枪
        //$this->debugstart('mysql_gas');
        $query = $this->stationModel->select("id", "pcode", "station_code", "station_name", "remark_name", "lng", "lat", "mapaddress", "address","station_oil_unit")
                                    ->with([
                                        'tank' => function ($query) use ($condition) {
                                            if (isset($condition['oilNameIn']) && $condition['oilNameIn']) {
                                                $query->whereIn("oil_name", $condition['oilNameIn']);
                                            }
                                        },
                                        'tank.gun'
                                    ]);
        unset($condition['oilNameIn']);
        $station_info = $query->withCondition($condition)
            ->first();
        //$sql = $this->debugend('mysql_gas');
        //print_r($sql);
        return $station_info;
    }

    /**
     * 获取对G7能源账户开放的站点
     *
     * @param array $select
     * @param bool $toArray
     * @return array|Builder[]|Collection
     */
    public function getAllOneCardStation($select = ['*'], $toArray = false)
    {
        $select = collect($select)->map(function ($item) {
            return 'gs.'.$item;
        })->all();
        $result = StationModel::query()
            ->select($select)
            ->from('gas_station as gs')
            ->join('gas_station_pcode as gsp', function($join){
                $join->where('gsp.xpcode', '20003JCP')->on('gs.id', '=', 'gsp.station_id');
            })->get();
        if ($toArray) {
            $result = empty($result) ? [] : $result->toArray();
        }

        return $result;
    }

    /**
     * 获取1个站点的基本信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneStationBasicInfoByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->stationModel->getOneStationBasicInfoByParams($whereParams, $select, $toArray);
    }

    /**
     * 获取批量站点的基本信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchStationInfoByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->stationModel->getBatchStationInfoByParams($whereParams, $select, $toArray);
    }

    /**
     * 验证站点状态
     * @param int $enabledStatus 站点启用状态
     * @param int $shelfStatus 站点上线状态
     * @param bool $throwException 验证不通过时是否抛出异常
     * @return bool
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/21 6:34 下午
     */
    public static function checkStationStatus(int $enabledStatus, int $shelfStatus, bool $throwException = true): bool
    {
        if ($enabledStatus != 0) {

            if ($throwException) {

                throw new RuntimeException("", CommonError::STATION_NOT_ENABLED);
            }

            return false;
        }

        if ($shelfStatus != 2) {

            if ($throwException) {

                throw new RuntimeException("", CommonError::STATION_NOT_LISTED);
            }

            return false;
        }

        return true;
    }

    /**
     * 获取站点信息
     * @param string $stationId 站点ID
     * @param array $checkData 验证数据,例：['rules' => ['xxx' => 'in'], 'messages' => ['xxx.in' => '']]
     * @param bool $throwException 验证失败时是否直接抛出异常
     * @return array
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/8/25 5:20 下午
     */
    public static function getStationInfoById(string $stationId, array $checkData = [],
                                              bool $throwException = true): array
    {
        try {

            $responseData = (new FossStation())->getStation($stationId);
            self::checkArrayField($responseData, $checkData, $throwException);
        } catch (Throwable $exception) {

            throw new RuntimeException("", CommonError::SYSTEM_ERROR);
        }

        return $responseData;
    }
    /**
     * 获取一个站点的扩展信息
     *
     * @param array $whereParams
     * @param array $select
     * @return mixed|StationExtModel
     */
    public function getOneStationExtByParams(array $whereParams, array $select = ['*'])
    {
        return $this->stationExtModel->getOneStationExtByParams($whereParams, $select);
    }
}