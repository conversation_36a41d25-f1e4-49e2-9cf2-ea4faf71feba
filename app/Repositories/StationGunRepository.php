<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\StationGunModel;

class StationGunRepository extends \App\Repositories\EloquentRepository
{
    protected $stationGunModel;
    public function __construct(StationGunModel $stationGunModel)
    {
        $this->stationGunModel = $stationGunModel;
    }

    //查询
    public function getList(array $condition)
    {
        //$condition['is_lock'] = 2;
        $data = $this->stationGunModel->select("id","name","tank_id")->withCondition($condition)->get();
        return $data;
    }
    public function detail(array $condition)
    {
        //$condition['is_lock'] = 2;
        $data = $this->stationGunModel->select("id","name","tank_id","station_id")
            ->with('station')
            ->with('tank')
            ->withCondition($condition)->first();
        return $data;
    }

    /**
     * 获取1条枪信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getOneGunByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->stationGunModel->getOneGunByParams($whereParams, $select, $toArray);
    }

    /**
     * 获取多条枪信息
     *
     * @param array $whereParams
     * @param array $select
     * @param bool $toArray
     * @return array
     */
    public function getBatchGunByParams(array $whereParams, $select = ['*'], $toArray = true)
    {
        return $this->stationGunModel->getBatchGunByParams($whereParams, $select, $toArray);
    }
}