<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 2019/12/19
 * Time: 10:05
 */
namespace App\Repositories;

use App\Models\DictModel;

class DictRepository extends \App\Repositories\EloquentRepository
{
    protected $dictModel;
    public function __construct(DictModel $dictModel)
    {
        $this->dictModel = $dictModel;
    }

    //查询
    public function getListByType(array $condition)
    {
        $data = $this->dictModel->select("id","dict_type","dict_data","pcode")->whereIn("dict_type",$condition)->orderby("orderby","asc")->get();
        return $data;
    }

    /**
     * 获取油品字典
     *
     * @return array
     */
    public function getOilDict()
    {
        return $this->dictModel->getOilDict();
    }

    public function getOperateOrderReason()
    {
        return $this->dictModel->getOperateReason();
    }
}