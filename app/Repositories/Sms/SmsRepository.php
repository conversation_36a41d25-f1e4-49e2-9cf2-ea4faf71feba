<?php

namespace App\Repositories\Sms;

use App\Library\Request;
use App\Models\Sms\SmsValidateModel;

class SmsRepository extends \App\Repositories\EloquentRepository
{
    public function __construct()
    {

    }
    
    /**
     * 获取验证码信息
     * @param $params
     * @return object
     */
    public function getValidateInfo($params)
    {
        return SmsValidateModel::getInfoByFilter($params);
    }

    /**
     * 生成验证码信息
     * @param $info
     * @return object
     */
    public function createValidate($info)
    {
        return SmsValidateModel::add($info);
    }
    
    /**
     * 检测并生成验证码信息
     * @param $mobile
     * @param $code
     * @param $resName
     * @return mixed
     */
    public function checkAndUpdateValidate($mobile, $code, $resName)
    {
        $info = [
            'mobile'      => $mobile,
            'code'        => $code,
            'res_name'    => $resName,
            'client_ip'   => Request::getClientIp() ?? null,
            'expire_time' => time() + 60 * 5,
        ];

        $validateInfo = SmsValidateModel::getInfoByFilter([
            'mobile'   =>   $mobile,
            'res_name' => $resName
        ]);
        if (!$validateInfo) {
            $res = SmsValidateModel::add($info);
        } else {
            $res = SmsValidateModel::edit($validateInfo['id'], $info);
        }
        
        return $res;
    }
}