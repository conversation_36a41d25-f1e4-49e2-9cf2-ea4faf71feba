<?php
namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Log;
use Framework\Queue;

class ExportStation extends Queue
{
    protected $pageSize = 1000;

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        $params   = $this->params;
        $taskInfo = $this->jobs;

        try {
            unset($params['_export']);
            $params['_count'] = 1;
            $total            = \Fuel\Service\Station::getList($params);

            Log::error('total:' . $total, [$params], 'exportStation');

            if (!$total) {
                \Models\OilDownload::updateByJobsId([
                    'jobs_id'  => $taskInfo->jobId,
                    'status'   => -20,
                    'filename' => '没有可以导出的数据',
                    'rate'     => 100,
                ]);
            } else {
                unset($params['_count'], $params['_export']);

                \Models\OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 1,
                ]);

                $totalPage = ceil($total / $this->pageSize);

                Log::error('totalPage:' . $totalPage, [$params], 'exportStation');

                $_tmpData = [];
                for ($i = 0; $i < $totalPage; $i++) {
                    $params['skip'] = $i * $this->pageSize;
                    $params['take'] = $this->pageSize;

                    $record = \Fuel\Service\Station::getList($params);

                    $_tmpData = array_merge($_tmpData, $record);

                    unset($record);
                }

                \Models\OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 30,
                ]);

                if ($_tmpData) {
                    $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
                    $fileName = 'station_' . date("Ymd_H_i_s") . rand(100, 999);
                    $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'oil_fanli' . DIRECTORY_SEPARATOR;
                    if (!is_dir($filePath)) {//创建目录
                        mkdir($filePath, 0777);
                    }

                    //导出数据
                    $_data = array_chunk($_tmpData, 10000);
                    foreach ($_data as $k => $v) {
                        $filesArr[] = $this->exportJob($v, ($k + 1));
                        \Models\OilDownload::updateByJobsId([
                            'jobs_id' => $taskInfo->jobId,
                            'status'  => 10,
                            'rate'    => 29 + (($k + 1) / count($_data)) * 70,
                        ]);
                    }
                    unset($_data);

                    $zipFile = $fileName . '.zip';
                    $zip     = new \ZipArchive();
                    if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
                        exit("can't open " . $filePath . $zipFile . " zipFile!");
                    }
                    for ($i = 0; $i < count($filesArr); $i++) {
                        $zip->addFile($filesArr[$i], basename($filesArr[$i]));
                    }
                    $zip->close();


                    $filePath = $filePath . $zipFile;

                    $url = (new \commonModel())->fileUploadToOss($filePath);
                    Log::error('bbbb导出序号-$url:' . $url, [], 'exportStation');

                    \Models\OilDownload::updateByJobsId([
                        'jobs_id'  => $taskInfo->jobId,
                        'status'   => 20,
                        'filename' => $zipFile,
                        'filetype' => \helper::getFileType($filePath),
                        'filesize' => round(filesize($filePath) / 1024, 2),
                        'url'      => $url,
                        'rate'     => 100,
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Models\OilDownload::updateByJobsId([
                'jobs_id'  => $taskInfo->jobId,
                'status'   => -20,
                'filename' => '导出失败',
                'rate'     => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], 'exportStation');
        }


    }

    public function exportJob($result, $page)
    {
        if ($result) {

            $extType  = count($result) >= 20000 ? 'xls' : 'xls';
            $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;

            $exportData = [
                'filePath'  => $realPath . 'data' . DIRECTORY_SEPARATOR . 'oil_fanli',
                'fileName'  => '油站' . date("YmdHis") .rand(100,999). "_" . $page,
                'sheetName' => '油站',
                'fileExt'   => $extType,
                'download'  => 1,
                'title'     => [
                    'station_name' => "站点名称",
                    'station_code' => '站点编码',
//                    'pcode'  => '站点运营商编码',
//                    'pcode_name'  => '站点运营商名称',
                    'status_txt' => "站点状态",
                    'rebate_grade_txt' => "档位",
//                    'has_policy_txt' => "政策是否维护",
                    "provice_name" => "所属省",
                    "city_name" => "地级市",
                    "address"   => '详细地址',
                    "lng"   => '经度',
                    "lat"   => '纬度',
                    "station_type_txt" => "油站类型",
                    "is_highway_txt" => "是否高速站",
                    "station_brand_txt" => "站点品牌",
                    "station_classify_txt" => "站点性质",
                    "ownner_name" => "网络负责人",
                    "dockor_name" => "结算对接人",
                    'creator'   =>  '创建人',
                    'createtime'   =>  '创建时间',
                    'modifier'   =>  '最后修改人',
                    'updatetime'   =>  '最后修改时间'
                ],
                'data'      => $result,
            ];

            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            });

            Log::error('bbbb导出序号-$fileUrl:' . var_export($fileUrl, TRUE), [], 'exportStation');
            return $fileUrl;
        }
    }
}
