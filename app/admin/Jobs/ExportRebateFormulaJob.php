<?php
/**
 * 返利计算公式导出
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/4/5
 * Time: 上午10:18
 */

namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Helper;
use Framework\Log;
use Framework\Queue;
use Models\OilDownload;
use Models\OilRebateFormula;
use Models\OilRebatePolicy;
use Fuel\Service\RebateFormulaService;

class ExportRebateFormulaJob extends Queue
{
    protected $pageSize = 1000;
    
    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }
    
    public function handle()
    {
        ini_set('memory_limit', '1024M');
        $params = $this->params;
        $taskInfo = $this->jobs;
        
        Log::error(__METHOD__ . "#begin", [], "rebate_formula_export");
        try {
            $params['count'] = 1;
            unset($params['_export']);
            Log::error(__METHOD__ . "#统计总数", [], "rebate_formula_export");
            $total = OilRebateFormula::getList($params);
            $params['count'] = 0;
            
            Log::error(__METHOD__ . "#$total", [$total], "rebate_formula_export");
            if (!$total) {
                OilDownload::updateByJobsId([
                    'jobs_id'  => $taskInfo->jobId,
                    'status'   => -20,
                    'filename' => '没有可以导出的数据',
                    'rate'     => 100,
                ]);
            } else {
                unset($params['count'], $params['_export']);
                
                OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 1,
                ]);
                $totalPage = ceil($total / $this->pageSize);
                
                $_tmpData = [];
                $_policyData = [];
                $policyKeyValue = [];
                for ($i = 0; $i < $totalPage; $i++) {
                    $params['skip'] = $i * $this->pageSize;
                    $params['take'] = $this->pageSize;
                    $formulaIdMap = [];
                    
                    try {
                        Log::error(__METHOD__ . "#获取主表——" . $i, [], "rebate_formula_export");
                        $record = OilRebateFormula::getList($params);
                        $record = $record->toArray();
                        if (count($record) > 0) {
                            foreach ($record as $k => &$v) {
//                                foreach ($v as &$item) {
//                                    $item = htmlspecialchars($item);
//                                    $item = Helper::trimAll($item);
//                                }
                                $v['rebate_value_name'] = (new RebateFormulaService())->formatRebateValue($v);
                                $v['is_bind_policy_name'] = $v['is_bind_policy'] ? '是' : '否';
                                $v['is_cal_name'] = $v['is_cal'] ? '是' : '否';
                                $policyKeyValue[$v['id']] = ['formula_name' => $v['name']];
                                $formulaIdMap[] = $v['id'];
                                unset($record[$k]['ext']);
                            }
                            
                            $_tmpData = array_merge($_tmpData, $record);
                            unset($record);
                        }
                        
                        //获取匹配规则
                        if ($formulaIdMap) {
                            Log::error(__METHOD__ . "#获取匹配规则——" . $i, [count($formulaIdMap)], "rebate_formula_export");
                            $policyRecord = OilRebatePolicy::getFilterList(['formula_idIn' => $formulaIdMap]);
                            
                            if (count($policyRecord) > 0) {
                                foreach ($policyRecord as $key => $val) {
                                    $ret = [];
                                    $ret['policy_id']     = $val['id'];
                                    $ret['name']          = $val['name'];
                                    $ret['ca_status']     = $val['ca_status'];
                                    $ret['ca_status_name'] = \Fuel\Defines\RebatePolicy::$caStatus[$val['ca_status']];
                                    $ret['start_time']    = $val['start_time'];
                                    $ret['end_time']      = $val['end_time'];
                                    $ret['creator']       = $val['creator'];
                                    $ret['last_operator'] = $val['last_operator'];
                                    $ret['createtime']    = $val['createtime'];
                                    $ret['formula_name']  = isset($policyKeyValue[$val['formula_id']]) ? $policyKeyValue[$val['formula_id']]['formula_name'] : '-';
                                    $_policyData[] = $ret;
                                }
                            }
                            
                            unset($policyRecord, $formulaIdMap);
                        }
                        
                        Log::error(__METHOD__ . "#合计明细：" . $i, [count($_policyData)], "rebate_formula_export");
                        Log::error(__METHOD__ . "#合计总数" . $i, [count($_tmpData)], "rebate_formula_export");
                    } catch (\Exception $e) {
                        Log::error(__METHOD__ . "#exception", [strval($e)], "rebate_formula_export");
                    }
                    usleep(10);
                }
                
                Log::error(__METHOD__ . "#总记录数", [count($_tmpData)], "rebate_formula_export");
                
                if ($_tmpData) {
                    // 导出数据
                    $fileArr = [];
                    if (count($_policyData) > 100000 || count($_tmpData) > 100000) {
                        $_policyData = array_chunk($_policyData, 100000);
                        foreach ($_policyData as $key => $val) {
                            $fileArr[] = $this->exportRelationPolicy($val, ($key + 1));
                        }
                        unset($_policyData);
    
                        $_data = array_chunk($_tmpData, 100000);
                        unset($_tmpData);
    
                        foreach ($_data as $d_k => $d_v) {
                            $fileArr[] = $this->exportJob($d_v, ($d_k + 1));
                        }
                        unset($_data);
//                    for ($i = 0; $i < count($filesArr); $i++) {
//                        if (file_exists($filesArr[$i])) {
//                            @unlink($filesArr[$i]);
//                        }
//                    }
                    } else {
                        $ret = [
                            'policyData'  => $_policyData,
                            'formulaData' => $_tmpData,
                        ];
                        unset($_policyData);
                        unset($_tmpData);
                        $fileArr[] = $this->exportMulti($ret);
                    }
                    
                    $zipMap = $this->zipArchive($fileArr, 'oil_rebate_formula');
                    $url = (new \commonModel())->fileUploadToOss($zipMap['filePath']);
                    Log::error('bbbb导出序号-$url:' . $url, [], 'rebate_formula_export');
                    
                    OilDownload::updateByJobsId([
                        'jobs_id'  => $taskInfo->jobId,
                        'status'   => 20,
                        'filename' => $zipMap['zipName'],
                        'filetype' => \helper::getFileType($zipMap['filePath']),
                        'filesize' => round(filesize($zipMap['filePath']) / 1024, 2),
                        'url'      => $url,
                        'rate'     => 100,
                    ]);
                }
            }
        } catch (\Exception $e) {
            OilDownload::updateByJobsId([
                'jobs_id'  => $taskInfo->jobId,
                'status'   => -20,
                'filename' => '导出失败',
                'rate'     => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], 'rebate_formula_export');
        }
    }
    
    public function exportRelationPolicy($result, $page)
    {
        $fileUrl = null;
        $exportData = [
            'fileName'  => 'oil_rebate_formula_relation_policy_' . date("YmdHis") . rand(100, 999) . "_" . $page,
            'sheetName' => 'oil_rebate_formula_relation_policy',
            'fileExt'   => 'csv',
            'download'  => 1,
            'title'     => [
                'policy_id'         => '规则ID',
                'name'              => '匹配规则名称',
                'formula_name'      => '公式名称',
                'ca_status_name'    => '规则计算状态',
                'start_time'        => '规则开始时间',
                'end_time'          => '规则结束时间',
                'creator'           => '匹配人',
                'createtime'        => '匹配时间',
            ],
            'data'      => $result,
        ];
        
        unset($result);
        Log::error(__METHOD__ . "#开始导出匹配规则：", [], "rebate_formula_export");
        
        try {
            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            });
            
            Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], "oil_rebate_formula_relation_policy_export");
        } catch (\Exception $exception) {
            Log::error(__METHOD__ . "#导出异常：", [strval($exception)], "oil_rebate_formula_relation_policy_export");
        }
        
        
        return $fileUrl;
    }
    
    public function exportJob($result, $page)
    {
        $fileUrl = null;
        Log::error(__METHOD__ . "#页码：" . $page . "，条数：" . count($result), [], "rebate_formula_export");
        if ($result) {
            $exportData = [
                'fileName'  => 'rebate_formula_' . date("YmdHis") . rand(100, 999) . '_' . $page,
                'sheetName' => 'rebate_formula',
                'fileExt'   => 'csv',
                'download'  => 1,
                'title'     => [
                    'id'                  => '公式ID',
                    'name'                => '公式名称',
                    'ca_method_name'      => '计算方法',
                    'ca_obj_name'         => '计算对象',
                    'rebate_type_name'    => '返利形式',
                    'rebate_value_name'   => '详细参数',
                    'is_bind_policy_name' => '是否与规则匹配',
                    'is_cal_name'         => '是否计算',
                    'classify_txt'        => '公式类别',
                ],
                'data'      => $result,
            ];
            unset($result);
            
            Log::error(__METHOD__ . "#开始导出：", [], "rebate_formula_export");
            
            try {
                $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                });
                
                Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], "rebate_formula_export");
            } catch (\Exception $exception) {
                Log::error(__METHOD__ . "#导出异常：", [strval($exception)], "rebate_formula_export");
            }
            
            return $fileUrl;
        }
    }
    
    public function exportMulti($data)
    {
        $fileUrl = null;
        if ($data) {
            $exportData = [
                'fileName'  => 'oil_rebate_formula_' . date("YmdHis") . rand(100, 999),
                'sheetName' => '返利计算公式',
                'fileExt'   => 'xls',
                'download'  => 1,
                'title'     => [
                    'id'                  => '公式ID',
                    'name'                => '公式名称',
                    'ca_method_name'      => '计算方法',
                    'ca_obj_name'         => '计算对象',
                    'rebate_type_name'    => '返利形式',
                    'rebate_value_name'   => '详细参数',
                    'is_bind_policy_name' => '是否与规则匹配',
                    'is_cal_name' => '是否计算',
                    'classify_txt'        => '公式类别',
                ],
                'multiSheet' => [
                    [
                        'sheetName' => '匹配规则',
                        'title'     => [
                            'policy_id'         => '规则ID',
                            'name'              => '匹配规则名称',
                            'formula_name'      => '公式名称',
                            'ca_status_name'    => '规则计算状态',
                            'start_time'        => '规则开始时间',
                            'end_time'          => '规则结束时间',
                            'creator'           => '匹配人',
                            'createtime'        => '匹配时间',
                        ],
                        'data'      => $data['policyData'],
                    ]
                ],
                'data'       => $data['formulaData'],
            ];
            unset($result);
            
            Log::error(__METHOD__ . "#开始导出：", [], "rebate_formula_export");
            
            try {
                $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                });
                
                Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], "rebate_formula_export");
            } catch (\Exception $exception) {
                Log::error(__METHOD__ . "#导出异常：", [strval($exception)], "rebate_formula_export");
            }
            
            return $fileUrl;
        }
    }
    
    public function zipArchive($filesArr, $name)
    {
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $fileName = $name . '_' . date("Ymd_H_i_s") . rand(100, 999);
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . $name . DIRECTORY_SEPARATOR;
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }
        
        $zipFile = $fileName . '.zip';
        $zip = new \ZipArchive();
        if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
            exit("can't open " . $filePath . $zipFile . " zipFile!");
        }
        for ($i = 0; $i < count($filesArr); $i++) {
            $zip->addFile($filesArr[$i], basename($filesArr[$i]));
        }
        $zip->close();
        
        return ['filePath' => $filePath . $zipFile, 'zipName' => $zipFile];
    }
}