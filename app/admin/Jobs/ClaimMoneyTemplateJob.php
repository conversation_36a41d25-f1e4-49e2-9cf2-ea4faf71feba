<?php


namespace Jobs;

use Framework\DingTalk\DingTalkAlarm;
use Framework\Log;
use Framework\Queue;
use Framework\Sms\NoticeSender;
use Fuel\Service\ClaimMoney;
use Models\OilDownload;

class ClaimMoneyTemplateJob extends Queue
{
    private $title = '认领单';
    private $app;
    
    public function __construct(array $params = [])
    {
        parent::__construct($params);
        global $app;
        $this->app = $app;
    }
    
    public function handle()
    {
        Log::error('异步认领', ['jobs' => $this->jobs], 'chargeForTemplate');

        Log::error('begin', [], 'claimMoneyTemplate');
        
        $this->updateProcess(['remark' => '开始执行', 'status' => 20, 'rate' => 40]);

        $service = new ClaimMoney();
        try {
            Log::error('调用chargeForTemplate', [], 'claimMoneyTemplate');
            $res = $service->chargeForTemplate($this->params);
            Log::error('执行完毕', [], 'chargeForTemplate');
        } catch (\Exception $e) {
            //解锁
            $service->removeLock($this->params['pay_no']);

            Log::error('异常：' . __METHOD__, [strval($e)], "chargeForTemplate");
            $content[] = "* 项目：模版认领充值";
            $content[] = "* 错误信息：" . $e->getMessage();
            (new DingTalkAlarm())->alarmToGroup('模版认领充值异常', implode("\n\n", $content), [],true, false);
            $this->updateProcess(['remark' => '执行失败，' . $e->getMessage(), 'status' => -20, 'rate' => 100]);
            throw new \RuntimeException('执行失败-' . $e->getMessage(), 2);
        }
        $this->updateProcess(['remark' => '执行完毕', 'status' => 20, 'rate' => 100]);
        
        Log::error('结果：' . __METHOD__, [$res], "chargeForTemplate");
    }
    
    public function updateProcess($params)
    {
        $download = OilDownload::updateByJobsId([
            'jobs_id'  => $this->jobs->jobId,
            'filename' => $params['remark'],
            'status'   => $params['status'],
            'rate'     => $params['rate'],
        ]);
    }
}