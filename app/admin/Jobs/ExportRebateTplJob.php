<?php
namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Helper;
use Framework\Log;
use Framework\Queue;
use Fuel\Service\OrgConfigService;
use Models\OilAccountMoney;
use Models\OilDownload;
use Models\OilOrg;
use Models\OilOrgConfig;
use Models\OilRebateTpl;

class ExportRebateTplJob extends Queue
{
    private $title = '公式模板';
    protected $pageSize = 1000;

    protected $logChannel = "exportRebateTpl";
    protected $xlxFile = "oil_rebate_tpl";

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }
    
    public function handle()
    {
        Log::error($this->title, ['jobs' => $this->jobs], $this->logChannel);

        $params   = $this->params;
        $taskInfo = $this->jobs;


        try {
            unset($params['_export']);
            $params['count'] = 1;
            $total            = OilRebateTpl::getTotal($params);

            Log::error('total:' . $total, [$params], $this->logChannel);

            if (!$total) {
                \Models\OilDownload::updateByJobsId([
                    'jobs_id'  => $taskInfo->jobId,
                    'status'   => -20,
                    'filename' => '没有可以导出的数据',
                    'rate'     => 100,
                ]);
            } else {
                unset($params['count'], $params['_export']);

                \Models\OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 1,
                ]);

                $totalPage = ceil($total / $this->pageSize);

                Log::error('totalPage:' . $totalPage, [$params], $this->logChannel);

                $_tmpData = [];
                for ($i = 0; $i < $totalPage; $i++) {
                    $params['skip'] = $i * $this->pageSize;
                    $params['take'] = $this->pageSize;

                    $record = OilRebateTpl::getList($params);
                    $_tmpData = array_merge($_tmpData, $record->toArray());
                    unset($record);
                }

                \Models\OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 30,
                ]);

                if ($_tmpData) {
                    $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR ;
                    $fileName = $this->xlxFile.'_' . date("Ymd_H_i_s") . rand(100, 999);
                    $filePath = $realPath . 'data' . DIRECTORY_SEPARATOR . $this->xlxFile . DIRECTORY_SEPARATOR;

                    if (!is_dir($filePath)) {//创建目录
                        mkdir($filePath, 0777);
                    }

                    //导出数据
                    $_data = array_chunk($_tmpData, 10000);
                    foreach ($_data as $k => $v) {
                        $fileTmp = $this->exportJob($v, ($k + 1));
                        Log::error('bbbb导出表格地址:' . $fileTmp, [], $this->logChannel);
                        if (empty($fileTmp))
                        {
                            throw new \RuntimeException("读取文件为空",2);
                        }
                        $filesArr[] = $fileTmp;
                        \Models\OilDownload::updateByJobsId([
                            'jobs_id' => $taskInfo->jobId,
                            'status'  => 10,
                            'rate'    => 29 + (($k + 1) / count($_data)) * 70,
                        ]);
                    }
                    unset($_data);

                    $zipFile = $fileName . '.zip';
                    $zip     = new \ZipArchive();
                    if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
                        exit("can't open " . $filePath . $zipFile . " zipFile!");
                    }
                    for ($i = 0; $i < count($filesArr); $i++) {
                        $zip->addFile($filesArr[$i], basename($filesArr[$i]));
                    }
                    $zip->close();


                    $filePath = $filePath . $zipFile;
                    $url = (new \commonModel())->fileUploadToOss($filePath);
                    Log::error('bbbb导出序号-$url:' . $url, [], $this->logChannel);

                    \Models\OilDownload::updateByJobsId([
                        'jobs_id'  => $taskInfo->jobId,
                        'status'   => 20,
                        'filename' => $zipFile,
                        'filetype' => \helper::getFileType($filePath),
                        'filesize' => round(filesize($filePath) / 1024, 2),
                        'url'      => $url,
                        'rate'     => 100,
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Models\OilDownload::updateByJobsId([
                'jobs_id'  => $taskInfo->jobId,
                'status'   => -20,
                'filename' => '导出失败',
                'rate'     => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], $this->logChannel);
        }
    }
    
    public function exportJob($data, $page)
    {

        $extType  = count($data) >= 20000 ? 'xls' : 'xls';
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $exportData = [
            'filePath'  => $realPath . 'data' . DIRECTORY_SEPARATOR . $this->xlxFile ,
            'fileName'  => $this->xlxFile.'_'.date("YmdHis"). rand(100, 999) . '_'.$page,
            'sheetName' => $this->title,
            'fileExt'   => $extType,
            'download'  => 1,
            'title'     => [
                'id'                => '序号',
                'type_txt'                => '模板维度',
                'title'               => '模板名称',
                'edit_txt'     => '是否与公式匹配',
            ],
            'data'      => $data,
        ];

        try {
            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            });

            Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], $this->logChannel);
            return $fileUrl;
        } catch (\Exception $exception) {
            Log::error(__METHOD__ . "#导出异常：", [strval($exception)], $this->logChannel);
        }
    }

    public function updateProcess($params)
    {
        $download = OilDownload::updateByJobsId([
            'jobs_id'  => $this->jobs->jobId,
            'filename' => $params['remark'],
            'status'   => $params['status'],
            'rate'     => $params['rate'],
        ]);
    }
}