<?php
/**
 * 副卡信息导出
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/4/5
 * Time: 上午10:18
 */

namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Log;
use Framework\Queue;
use Models\OilBankRecords;
use Models\OilClaimMoney;
use Models\OilDownload;

class ExportBankRecordsJob extends Queue
{
    protected $pageSize = 1000;

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        $params   = $this->params;
        $taskInfo = $this->jobs;
        Log::error('$taskInfo' . var_export($taskInfo, TRUE), [], 'debugExport');
        try {
            $params['count'] = 1;
            Log::error('$params:' . var_export($params, TRUE), [], 'debugExport');

            $total = OilBankRecords::getList($params);
            Log::error('$total' . var_export($total, TRUE), [], 'debugExport');

            if (!$total) {
                OilDownload::updateByJobsId([
                    'jobs_id'  => $taskInfo->jobId,
                    'status'   => -20,
                    'filename' => '没有可以导出的数据',
                    'rate'     => 100,
                ]);
            } else {
                unset($params['count'], $params['_export']);

                OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 1,
                ]);

                $totalPage = ceil($total / $this->pageSize);
                Log::error('$totalPage' . var_export($totalPage, TRUE), [], 'debugExport');

                $_tmpData = [];
                for ($i = 0; $i < $totalPage; $i++) {
                    $params['skip'] = $i * $this->pageSize;
                    $params['take'] = $this->pageSize;
                    Log::error('$params:' . var_export($params, TRUE), [], 'debugExport');
                    $record = OilBankRecords::getList($params);
                    Log::error('$_data:' . var_export($record, TRUE), [], 'debugExport');

                    if (count($record) > 0) {
                        $_tmpData = array_merge($_tmpData, $record);
                    }
                    unset($record);
                }

                OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 30,
                ]);

                if ($_tmpData) {
                    $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
                    $fileName = 'bank_records_' . date("Ymd_H_i_s") . rand(100, 999);
                    $filePath = $realPath . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . 'oil_bank_records' . DIRECTORY_SEPARATOR;
                    if (!file_exists($filePath)) {//创建目录
                        @mkdir($filePath, 0777);
                    }

                    $filesArr = [];
                    //导出数据
                    $_data = array_chunk($_tmpData, 100000);

                    Log::error('$_data:' . count($_data), [], 'debugExport');

                    foreach ($_data as $k => $v) {
                        $filesArr[] = $this->exportJob($v, ($k + 1));
                        OilDownload::updateByJobsId([
                            'jobs_id' => $taskInfo->jobId,
                            'status'  => 10,
                            'rate'    => 29 + (($k + 1) / count($_data)) * 70,
                        ]);
                    }
                    unset($_data);
                    Log::error('$filesArr' . var_export($filesArr, TRUE), [], 'debugExport');

                    $zipFile = $fileName . '.zip';
                    $zip     = new \ZipArchive();
                    if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
                        exit("can't open " . $filePath . $zipFile . " zipFile!");
                    }
                    for ($i = 0; $i < count($filesArr); $i++) {
                        $zip->addFile($filesArr[$i], basename($filesArr[$i]));
                    }
                    $zip->close();
//                    for ($i = 0; $i < count($filesArr); $i++) {
//                        if (file_exists($filesArr[$i])) {
//                            @unlink($filesArr[$i]);
//                        }
//                    }

                    $filePath = $filePath . $zipFile;

                    $url = (new \commonModel())->fileUploadToOss($filePath);

                    OilDownload::updateByJobsId([
                        'jobs_id'  => $taskInfo->jobId,
                        'status'   => 20,
                        'filename' => $zipFile,
                        'filetype' => \helper::getFileType($filePath),
                        'filesize' => round(filesize($filePath) / 1024, 2),
                        'url'      => $url,
                        'rate'     => 100,
                    ]);
                }
            }
        } catch (\Exception $e) {
            OilDownload::updateByJobsId([
                'jobs_id'  => $taskInfo->jobId,
                'status'   => -20,
                'filename' => '导出失败',
                'rate'     => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], 'debugExport');
        }


    }

    public function exportJob($result, $page)
    {
        if ($result) {
            $extType  = count($result) >= 20000 ? 'xls' : 'xls';
            $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;

            $exportData = [
                'filePath'  => $realPath . 'download' . DIRECTORY_SEPARATOR . 'oil_bank_records',
                'fileName'  => 'bank_records_' . date('YmdHis') . rand(100, 999) . '_' . $page,
                'fileExt'   => $extType,
                'sheetName' => "收款记录",
                'download'  => 1,
                'title'     => [
                    'transfer_time'   => '收款时间',
                    'updated_at'      => '认领时间',
                    'time_use'        => '认领时长',
                    'RPYNAM'          => '付款方',
                    'RPYACC'          => '付款账号',
                    'RPYBNK'          => '付款方银行',
                    'account_no'      => '收款账号',
                    'operators_name'  => '收款方',
                    'transfer_amount' => '收款金额（元）',
                    'no_claim_money'  => '未认领金额（元）',
                    '_claim_status'   => '认领状态',
                    'claim_object_txt' => '认领方',
                    'NARYUR'          => '摘要',
                    'REFNBR'          => '流水号',
                ],
                'data'      => $result,
            ];

            $fileUrl = ExcelWriter::exportXls(
                $exportData, function ($phpExcelObj, $data, $lineCell) {
                if (in_array($data['name'], ['RPYACC','account_no','REFNBR'])) {
                    $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            }
            );

            Log::error('bbbb导出序号-$fileUrl:' . var_export($fileUrl, TRUE), [], 'debugExport');

            return $fileUrl;
        }
    }
}