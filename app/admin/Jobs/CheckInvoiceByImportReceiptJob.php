<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/3/25
 * Time: 下午2:55
 */

namespace Jobs;


use Framework\Log;
use Framework\Queue;
use Fuel\Broadcast\publishCheckSales;
use Fuel\Service\InvoiceCheckForSales;

class CheckInvoiceByImportReceiptJob extends Queue
{
    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }
    
    public function handle()
    {
        global $app;
        $app->myAdmin;
        
        $result = (new InvoiceCheckForSales())->checkInvoice();
        
        $broadcast = (new publishCheckSales())->pubCheckMessage($result,$app->myAdmin);
        Log::error(__METHOD__.'boradcase:'.var_export($broadcast,true),'','checkInvoice');
        
    }
}