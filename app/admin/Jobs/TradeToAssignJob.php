<?php
/**
 * Created by PhpStorm.
 * User: Tim
 * Date: 2025/3/24
 * Time: 11:35 PM
 */

namespace Jobs;

use Framework\Log;
use Framework\Queue;
use Framework\DingTalk\DingTalkAlarm;
use Framework\DingTalk\FeiShuNotify;
use Fuel\Service\CardViceAssign;
use Models\OilDownload;


/**
 * Class TradeToAssignJob
 * @package Jobs
 *
 * 注：
 *  异步交易转分配处理
 *
 */
class TradeToAssignJob extends Queue
{
    const LOG_CHANNEL = 'trade_to_assign_job';

    const TITLE = "异步交易转分配处理";

    const QUEUE = "tradeToAssignJob";

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        Log::error(self::TITLE . ' 参数', [$this->params], self::LOG_CHANNEL);

        try {

            (new CardViceAssign())->doTradeToAssign($this->params);

            $this->updateProcess(['remark' => '成功', 'status' => 20, 'rate' => 100]);
           
        } catch (\Exception $e) {
            Log::error('异常：' . __METHOD__, [strval($e)], self::LOG_CHANNEL);
            $this->sendAlerm($e->getMessage(),$this->params['station_name'],$this->params['api_id'],$this->params['station_code']);
            $this->updateProcess(['remark' => '执行失败，' . $e->getMessage(), 'status' => -20, 'rate' => 100]);

            throw new \RuntimeException('执行失败-' . $e->getMessage(), 2);
        }
    }

    /**
     * 更新oilDownLoad进度
     * @param array $params
     */
    public function updateProcess(array $params)
    {
        OilDownload::updateByJobsId([
            'jobs_id' => $this->jobs->jobId,
            'filename' => $params['remark'],
            'status' => $params['status'],
            'rate' => $params['rate'],
        ]);
    }

    public function sendAlerm($errstr,$station_name,$gms_order_id,$station_code)
    {
        if(API_ENV == 'pro'){
            $chat_id  = "oc_37229db5ec049cdaeb4ba4189e1b163e";
            $at = [
                '冼世文' => '<EMAIL>',
                '李明霞' => '<EMAIL>'
            ];
        }else{
            $chat_id  = "oc_37229db5ec049cdaeb4ba4189e1b163e";
            $at = [
                '雷庆' => '<EMAIL>',
            ];
        }
        $content = [
            '订单编号：'.$gms_order_id,
            '站点名称：'.$station_name,
            '站点编码：'.$station_code,
            '异常备注：'.$errstr,
        ];
        $evn = '[环境：'.API_ENV."]";
        $apiParams = [
            'title'    => '代管卡分配异常-分配异常'.$evn,
            'chat_id'  => $chat_id,
            'msg_type' => 'card',
            'content'  => implode("\n",$content),
            'at'       => $at
        ];
        
        (new FeiShuNotify())->Send($apiParams);
    }
}