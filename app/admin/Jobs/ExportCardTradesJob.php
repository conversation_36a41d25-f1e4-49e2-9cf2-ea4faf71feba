<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/3/26
 * Time: 下午8:05
 */

namespace Jobs;

//require APP_MODULE_ROOT.DIRECTORY_SEPARATOR.'common'.DIRECTORY_SEPARATOR.'model.php';

use Framework\Log;
use Framework\Queue;
use Fuel\Export\TradesExport;
use Models\OilCardViceTrades;
use Models\OilDownload;

class ExportCardTradesJob extends Queue
{
    /**
     * 每页查询条数
     * @var int
     */
    protected $pageSize = 1000;
    /**
     * csv单个文件最大数据条数
     * @var int
     */
    protected $csvSize = 50000;
    /**
     * xls单个文件最大条数
     * @var int
     */
    protected $xlsSize = 10000;

    /**
     * 同步导出数据条数
     * @var int
     */
    protected $syncSize = 10000;

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        global $app;

        $jobs = $this->jobs;
        $params = $this->params;

        $processRate = 1;
        try {
            $params['count'] = 1;
            $total = OilCardViceTrades::getTradesList($params);
            if (!$total) {
                throw new \RuntimeException('没有可以到处的数据', 2);
            }
            unset($params['count']);

            $excelType = isset($params['excelType']) ? $params['excelType'] : 'csv';
            $pageNum = $excelType == 'csv' ? $this->csvSize : $this->xlsSize;

            $exportTradesObj = new TradesExport();
            //获取需导出的数据
            $_tmpData = $exportTradesObj->getData($params, $total, function ($processRate) use ($jobs) {
                OilDownload::updateByJobsId([
                    'jobs_id' => $jobs->jobId,
                    'rate'    => $processRate,
                ]);
            });

            if ($_tmpData) {
                //导出数据
                $filesArr = $exportTradesObj->exportByPage($_tmpData, ['pageNum' => $pageNum, 'excelType' => $excelType],
                    function ($processRate) use ($jobs) {
                        OilDownload::updateByJobsId([
                            'jobs_id' => $jobs->jobId,
                            'rate'    => ($processRate + 40),
                        ]);
                    });

                //打包文件
                $zipFilePath = $exportTradesObj->exportArchive($filesArr, $excelType);

                if(isset($params['returnPath']) && $params['returnPath'] == 1){
                    return $zipFilePath;
                }

                //上传阿里云
                $url = (new \commonModel())->fileUploadToOss($zipFilePath);

                $_tmpFileNameArr = explode('/', $url);
                $zipFile = end($_tmpFileNameArr);
                unset($filesArr, $_tmpFileNameArr);

                OilDownload::updateByJobsId([
                    'jobs_id'  => $this->jobs->jobId,
                    'status'   => 20,
                    'filename' => $zipFile,
                    'filetype' => \helper::getFileType($zipFilePath),
                    'filesize' => round(filesize($zipFilePath) / 1024, 2),
                    'url'      => $url,
                    'rate'     => 100,
                ]);

            }
        } catch (\Exception $e) {
            OilDownload::updateByJobsId([
                'jobs_id'  => $this->jobs->jobId,
                'filename' => $e->getMessage(),
                'status'   => '-20',
                'rate'     => 100
            ]);
        }

        return TRUE;
    }
}