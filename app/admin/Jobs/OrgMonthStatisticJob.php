<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/3/25
 * Time: 下午8:24
 */

namespace Jobs;

use Framework\Log;
use Framework\Queue;
use Fuel\Service\OrgMonthStatistic;
use Models\OilDownload;


class OrgMonthStatisticJob extends Queue
{
    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        $_params = $this->params;
        $taskInfo = $this->jobs;
        try {
            $this->updateTask($taskInfo, 10, "开始", 10);

            $exportFileData = (new OrgMonthStatistic())->exportByMonty($_params,50000);
            $exportFileData['org_name'] = $_params['org_name'];
            Log::error("OrgMonthStatisticJob:data", [var_export($exportFileData, TRUE)], "orgMonthStatistic");
            $this->updateTask($taskInfo, 20, "成功", 100);
        } catch (\Exception $e) {
            $this->updateTask($taskInfo, -20, '失败，' . $e->getMessage(), 100);
            //Alarm
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    private function updateTask($taskInfo, $status, $name, $rate)
    {
        OilDownload::updateByJobsId([
            'jobs_id' => $taskInfo->jobId,
            'status' => $status,
            'filename' => '',
            'rate' => $rate,
        ]);
    }
}