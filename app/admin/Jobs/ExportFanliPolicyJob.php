<?php
/**
 * Created by PhpStorm.
 * User: mike
 * Date: 18-08-21
 * Time: 下午4:38
 */

namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Log;
use Framework\Queue;
use Models\OilDownload;
use Models\OilFanliPolicy;

class ExportFanliPolicyJob extends Queue
{
    protected $pageSize = 10000;

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        $params   = $this->params;
        $taskInfo = $this->jobs;
        try {
            $params['count'] = 1;

            $total = OilFanliPolicy::getList($params);

            if (!$total) {
                OilDownload::updateByJobsId([
                    'jobs_id'  => $taskInfo->jobId,
                    'status'   => -20,
                    'filename' => '没有可以导出的数据',
                    'rate'     => 100,
                ]);
            } else {
                unset($params['count'], $params['_export']);

                OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 1,
                ]);

                $totalPage = ceil($total / $this->pageSize);

                $_tmpData = [];
                for ($i = 0; $i < $totalPage; $i++) {
                    $params['skip'] = $i * $this->pageSize;
                    $params['take'] = $this->pageSize;
                    $record         = OilFanliPolicy::getList($params);
                    $record         = $record->toArray();
                    if (count($record) > 0) {
                        if (count($_tmpData) > 0) {
                            $_tmpData = array_merge($_tmpData, $record);
                        } else {
                            $_tmpData = $record;
                        }
                    }
                    unset($record);
                }

                OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 30,
                ]);
                if ($_tmpData) {
                    $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
                    $fileName = 'fanli_policy_' . date("Ymd_H_i_s") . rand(100, 999);
                    $filePath = $realPath . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . 'oil_fanli' . DIRECTORY_SEPARATOR;
                    if (!file_exists($filePath)) {//创建目录
                        @mkdir($filePath, 0777);
                    }

                    $filesArr = [];
                    //导出数据

                    if (count($_tmpData) > 30000) {
                        $_data = array_chunk($_tmpData, 30000);
                    } else {
                        $_data[] = $_tmpData;
                    }

                    foreach ($_data as $k => $v) {
                        $filesArr[] = $this->exportJob($v, ($k + 1));
                        OilDownload::updateByJobsId([
                            'jobs_id' => $taskInfo->jobId,
                            'status'  => 10,
                            'rate'    => 29 + (($k + 1) / count($_data)) * 70,
                        ]);
                    }
                    unset($_data);

                    $zipFile = $fileName . '.zip';
                    $zip     = new \ZipArchive();
                    if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
                        exit("can't open " . $filePath . $zipFile . " zipFile!");
                    }
                    for ($i = 0; $i < count($filesArr); $i++) {
                        $zip->addFile($filesArr[$i], basename($filesArr[$i]));
                    }
                    $zip->close();

                    $filePath = $filePath . $zipFile;

                    $url = (new \commonModel())->fileUploadToOss($filePath);

                    OilDownload::updateByJobsId([
                        'jobs_id'  => $taskInfo->jobId,
                        'status'   => 20,
                        'filename' => $zipFile,
                        'filetype' => \helper::getFileType($filePath),
                        'filesize' => round(filesize($filePath) / 1024, 2),
                        'url'      => $url,
                        'rate'     => 100,
                    ]);
                }
            }
        } catch (\Exception $e) {
            OilDownload::updateByJobsId([
                'jobs_id'  => $taskInfo->jobId,
                'status'   => -20,
                'filename' => '导出失败',
                'rate'     => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], 'debugExport');
        }


    }

    public function exportJob($result, $page)
    {
        foreach ($result as &$val) {
            if ($val['fanli_type'] == 1) {//按现金
                $val['fanli_value'] = $val['fanli_coe'] . '%';
            } elseif ($val['fanli_type'] == 2) {//按油量
                $val['fanli_value'] = $val['fanli_money'];
            } elseif (in_array(intval($val['fanli_type']), [3, 4])) {//阶梯返利
                if ($val['coe_unit'] == 1) {//按现金百分比
                    $val['fanli_edu_level1'] = $val['fanli_coe_level1'] . '%';
                    $val['fanli_edu_level2'] = $val['fanli_coe_level2'] . '%';
                    $val['fanli_edu_level3'] = $val['fanli_coe_level3'] . '%';
                    $val['fanli_edu_level4'] = $val['fanli_coe_level4'] . '%';
                    $val['fanli_edu_level5'] = $val['fanli_coe_level5'] . '%';
                } elseif ($val['coe_unit'] == 2) {//按每升返利金额
                    $val['fanli_edu_level1'] = $val['fanli_money_level1'];
                    $val['fanli_edu_level2'] = $val['fanli_money_level2'];
                    $val['fanli_edu_level3'] = $val['fanli_money_level3'];
                    $val['fanli_edu_level4'] = $val['fanli_money_level4'];
                    $val['fanli_edu_level5'] = $val['fanli_money_level5'];
                }
            }
        }

        $extType = count($result) >= 20000 ? 'xls' : 'xls';

        $exportData = [
            'filePath'  => APP_WWW_ROOT . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . 'oil_fanli' . DIRECTORY_SEPARATOR,
            'fileName'  => 'fanli_policy_'.date('YmdHis') . rand(100,999). '_' . $page,
            'sheetName' => '返利政策维护',
            'fileExt'   => $extType,
            'download'  => 1,
            'title'     => [
                'id'                 => 'ID',
                'policy_name'        => '政策名称',
                'policy_object_name' => '政策对象',
                'oil_com_name'       => '油卡类型',
                'main_no'            => '主卡',
                'supplyer_name'            => '卡供应商',
                'station_name'       => '可用油站',
                'main_jifen_no'      => '积分返利主卡',
                'orgcode'            => '返利机构编码',
                'org_name'           => '返利机构',
                'regions_id_name'    => '消费地区',
                'oil_type_name'      => '油品类型',
                'fanli_way_name'     => '返利形式',
                'fanli_type_name'    => '返利类型',
                'oil_amount_limit'   => '加油量限制',
                'oil_money_limit'    => '金额限制',
                'start_time'         => '开始时间',
                'end_time'           => '截止时间',
                'fanli_value'        => '返利系数/金额',
                'coe_unit_name'      => '系数单位',
                'fanli_level1_gt'    => '一级返利区间开始',
                'fanli_level1_le'    => '一级返利区间结束',
                'fanli_edu_level1'   => '一级返利额度',
                'fanli_level2_gt'    => '二级返利区间开始',
                'fanli_level2_le'    => '二级返利区间结束',
                'fanli_edu_level2'   => '二级返利额度',
                'fanli_level3_gt'    => '三级返利区间开始',
                'fanli_level3_le'    => '三级返利区间结束',
                'fanli_edu_level3'   => '三级返利额度',
                'fanli_level4_gt'    => '四级返利区间开始',
                'fanli_level4_le'    => '四级返利区间结束',
                'fanli_edu_level4'   => '四级返利额度',
                'fanli_level5_gt'    => '五级返利区间开始',
                'fanli_level5_le'    => '五级返利区间结束',
                'fanli_edu_level5'   => '五级返利额度',
                'fanli_min_money'    => '免惠最低价',
                'add_fanli_edu'      => '加油量叠加优惠',
            ],
            'data'      => $result,
        ];


        $file = \Framework\Excel\ExcelWriter::exportXls(
            $exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['main_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        }
        );
        return $file;
    }

}