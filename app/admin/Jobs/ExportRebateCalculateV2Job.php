<?php
namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Log;
use Framework\Queue;
use Fuel\Defines\CardViceConf;
use Fuel\Defines\FinalStatus;
use Fuel\Defines\RebatePolicy;
use Models\OilDownload;
use Models\OilRebateCalculate;
use Models\OilRebateCalDetails;

class ExportRebateCalculateV2Job extends Queue
{
    private $title;

    protected $pageSize = 500;

    protected $logChannel = "exportRebateCalculate";

    protected $xlxFile = "oil_rebate_calculate";

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }
    
    public function handle()
    {
        ini_set('memory_limit','4000M');

        Log::error($this->title, ['jobs' => $this->jobs], $this->logChannel);

        $params   = $this->params;

        $this->title = $params['file_prefix'] . '_';

        if (!isset($params['policy_type']) || empty($params['policy_type'])){
            $params['policy_type'] = RebatePolicy::POLICY_TYPE_ELEC;
        }

        $params['is_show'] = 1;
        $params['cal_start_time_neq_null'] = 1;

        try {
            if ($params['policy_type'] == RebatePolicy::POLICY_TYPE_ELEC) {

                $res = $this->generateExportFileForTmp($params);

//                if (isset($params['final_status']) && $params['final_status'] == FinalStatus::NO_ENTER_FINAL_STAGE) {
//                    $res = $this->generateExportFileForTmp($params);
//                } else {
//                    $res = $this->generateExportFileForMonth($params);
//                }
            } else {
                $res = $this->generateExportFileForEntityTmp($params);
//                if (isset($params['final_status']) && $params['final_status'] == FinalStatus::NO_ENTER_FINAL_STAGE) {
//                    $res = $this->generateExportFileForEntityTmp($params);
//                } else {
//                    $res = $this->generateExportFileForEntityMonth($params);
//                }
            }

            $filePath = $res['file_path'];

            Log::error('filePath', [$filePath], $this->logChannel);

            if (! $filePath)
                return;

            $url = (new \commonModel())->fileUploadToOss($filePath);

            Log::error('url', [$url], $this->logChannel);

            OilDownload::updateByJobsId([
                'jobs_id'  => $this->jobs->jobId,
                'status'   => 20,
                'filename' => $res['file_name'],
                'filetype' => \helper::getFileType($filePath),
                'filesize' => round(filesize($filePath) / 1024, 2),
                'url'      => $url,
                'rate'     => 100,
            ]);
        }catch (\Exception $e){
            Log::error('异常信息:', [$e->getMessage()], $this->logChannel);

            OilDownload::updateByJobsId([
                'jobs_id'  => $this->jobs->jobId,
                'status'   => -20,
                'filename' => '处理失败',
                'rate'     => 100,
            ]);
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    private function generateExportFileForTmp($params)
    {
        $res = [
            'file_path' => '',
            'file_name' => ''
        ];

        list($calculate, $details) = $this->getExportDataForTmp($params);
        if (empty($calculate))
            return $res;

        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . $this->xlxFile;

        $fileExt  = 'xlsx';
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }

        $fileName = $this->title . date("YmdHis");

        $exportData = [
            'filePath'   => $filePath,
            'download'   => 1,
            'fileName' => $fileName,
            'sheetName' => '工单视角',
            'fileExt'    => $fileExt,
            'title'     => [
                'no'                    => '返利工单号',
                'policy_id'             => '使用规则ID',
                'policy_name'           => '规则名称',
                'policy_level'          => '规则等级',
                'cal_object_txt'        => '计算对象',
                'total_money'           => '计算对象汇总值',
                'cal_count'             => '计算机构数量',
                'rebate_type_txt'       => '返利形式',
                'unit_txt'              => '返利单位',
                'method_txt'            => '计算方法',
                'rebate_content'        => '返利值',
                'total_fanli'           => '返利结果',
                'start_time'            => '规则起始时间',
                'end_time'              => '规则终止时间',
                'cal_start_time'        => '计算开始时间',
                'cal_end_time'          => '计算截止时间',
                'cal_day'               => '累计计算天数',
            ],
            'multiSheet' => [
                [
                    'sheetName'=> '返利工单详情',
                    'title' => [
                        'no'                 => '返利单号',
                        'policy_id'               => '使用规则ID',
                        'id' => '明细ID',
                        'object_name'           => '机构名称',
                        'object_code'           => '机构编码',
                        'policy_name'           => '规则名称',
                        'policy_level'          => '规则等级',
                        'cal_object_txt'        => '计算对象',
                        'total_money'           => '计算对象值',
                        'method_txt'            => '计算方法',
                        'fanli_val'             => '返利值',
                        'total_fanli'           => '返利结果',
                        'start_time'            => '规则起始时间',
                        'end_time'              => '规则终止时间',
                    ],
                    'data' => $details
                ]
            ],
            'data' => $calculate,
        ];

        try {
            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                if (in_array($data['name'], ['total_money', 'total_fanli', 'id', 'object_code'])) {
                    $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            });
            Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], $this->logChannel);

            $res['file_name'] = $fileName;
            $res['file_path'] = $fileUrl;

        } catch (\Exception $exception) {
            Log::error(__METHOD__ . "#导出异常：", [strval($exception)], $this->logChannel);
        }
        return $res;
    }
    private function getExportDataForTmp($params)
    {
        unset($params['_export']);
        $calculate = $details = [];

        $i = 0;
        $params['take'] = $this->pageSize;

        while (true) {
            $params['skip'] = $i * $this->pageSize;

            $data = OilRebateCalculate::getList($params);

            if ($i == 0 && $data->isEmpty()) {
                OilDownload::updateByJobsId([
                    'jobs_id'  => $this->jobs->jobId,
                    'status'   => 20,
                    'filename' => '没有要导出的数据',
                    'rate'     => 100,
                ]);
                return [$calculate, $details];
            } elseif ($data->isEmpty()) {
                return [$calculate, $details];
            }

            foreach ($data as $v) {
                $v->rebate_content = $this->formatFanLiVal($v);

                if ($params['policy_type'] == RebatePolicy::POLICY_TYPE_ENTITY) {
                    $v->card_type = RebatePolicy::getCardTypeTxt($v->card_type);
                }

                $detail = OilRebateCalDetails::getList(['cal_id' => $v->id, '_export' => 1]);
                foreach ($detail as &$d) {
                    $d->fanli_val = $v->rebate_content;
                }

                $details = array_merge($details, $detail->toArray());
            }

            $calculate = array_merge($calculate, $data->toArray());

            $i++;
        }
    }

    private function generateExportFileForMonth($params)
    {
        $res = [
            'file_path' => '',
            'file_name' => ''
        ];

        $calculate = $this->getExportDataForMonth($params);
        if (empty($calculate))
            return $res;

        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . $this->xlxFile;

        $fileExt  = 'xlsx';
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }

        $fileName = $this->title . date("YmdHis");

        $exportData = [
            'filePath'   => $filePath,
            'download'   => 1,
            'fileName' => $fileName,
            'sheetName' => '工单视角',
            'fileExt'    => $fileExt,
            'title'     => [
                'no'                    => '返利工单号',
                'policy_id'             => '使用规则ID',
                'policy_name'           => '规则名称',
                'policy_level'          => '规则等级',
                'cal_object_txt'        => '计算对象',
                'total_money'           => '计算对象汇总值',
                'cal_count'             => '计算机构数量',
                'rebate_type_txt'       => '返利形式',
                'unit_txt'              => '返利单位',
                'method_txt'            => '计算方法',
                'rebate_content'        => '返利值',
                'total_fanli'           => '返利结果',
                'start_time'            => '规则起始时间',
                'end_time'              => '规则终止时间',
                'cal_start_time'        => '计算开始时间',
                'cal_end_time'          => '计算截止时间',
                'cal_day'               => '累计计算天数',
            ],
            'data' => $calculate,
        ];

        try {
            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                if (in_array($data['name'], ['total_money', 'total_fanli'])) {
                    $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            });
            Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], $this->logChannel);

            $res['file_name'] = $fileName;
            $res['file_path'] = $fileUrl;

        } catch (\Exception $exception) {
            Log::error(__METHOD__ . "#导出异常：", [strval($exception)], $this->logChannel);
        }
        return $res;
    }
    private function getExportDataForMonth($params)
    {
        unset($params['_export']);
        $calculate = [];

        $i = 0;
        $params['take'] = $this->pageSize;

        while (true) {
            $params['skip'] = $i * $this->pageSize;

            $data = OilRebateCalculate::getList($params);

            if ($i == 0 && $data->isEmpty()) {
                OilDownload::updateByJobsId([
                    'jobs_id'  => $this->jobs->jobId,
                    'status'   => 20,
                    'filename' => '没有要导出的数据',
                    'rate'     => 100,
                ]);
                return $calculate;
            } elseif ($data->isEmpty()) {
                return $calculate;
            }

            foreach ($data as $v) {
                $v->rebate_content = $this->formatFanLiVal($v);

                if ($params['policy_type'] == RebatePolicy::POLICY_TYPE_ENTITY) {
                    $v->card_type = RebatePolicy::getCardTypeTxt($v->card_type);
                }
            }

            $calculate = array_merge($calculate, $data->toArray());

            $i++;
        }
    }

    protected function formatFanLiVal($record)
    {
        if ($record->cal_method == 30) {
            $unit = '升';
        } else {
            $unit = '元';
        }

        if ($record->cal_method == 10) {
            $oUnit = "元/升";
        } else {
            $oUnit = "%";
        }

        /**
         * 返利值 数据重组
         */
        if (in_array($record->cal_method,[30, 40, 50, 60])) {
            $fanli_val = !empty($record->fanli_val) ? json_decode($record->fanli_val,true) : [];

            $rebate_content_value = [];

            foreach ($fanli_val as $k => $it)
            {
                //返利百分比 区间计算
                $dis_data = $it['discount'];
                if (!empty($it['fanli_modelsTmpls'])) {
                    if (is_array($it['fanli_modelsTmpls'])) {
                        $jsonContent = $it['fanli_modelsTmpls'][0]['content'];
                    } else {
                        $jsonContent = $it['fanli_modelsTmpls']['content'];
                    }
                    $arrContent = json_decode($jsonContent,true);
                    $valueArray = array_column($arrContent,"value");
                    sort($valueArray);

                    $dis_data = $valueArray[0] . " - ".$valueArray[count($valueArray)-1];
                }

                $rebate_content_value[] = $it['start_value'].$unit." ~ ".$it['end_value'].$unit ." ".$dis_data.$oUnit;
            }

            $record->rebate_content = implode(" ",$rebate_content_value);
            if (in_array($record->ca_method,[50, 60])) {
                $record->rebate_content = $record->rebate_content."，最低消费额：".$record->min_amount."元";
            }
        } else {
            $record->rebate_content = $record->fanli_val . $oUnit;
        }

        return $record->rebate_content;
    }

    private function generateExportFileForEntityTmp($params)
    {
        $res = [
            'file_path' => '',
            'file_name' => ''
        ];

        list($calculate, $details) = $this->getExportDataForEntityTmp($params);
        if (empty($calculate))
            return $res;

        Log::error("DEBUG -----", [$calculate, $details], $this->logChannel);

        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . $this->xlxFile;

        $fileExt  = 'xlsx';
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }

        $fileName = $this->title . date("YmdHis");

        $exportData = [
            'filePath'   => $filePath,
            'download'   => 1,
            'fileName' => $fileName,
            'sheetName' => '返利工单',
            'fileExt'    => $fileExt,
            'title'     => [
                'no'                    => '返利工单号',
                'policy_id'             => '使用规则ID',
                'policy_name'           => '规则名称',
                'card_type'             => '卡类型',
                'policy_level'          => '规则等级',
                'cal_object_txt'        => '计算对象',
                'total_money'           => '计算对象汇总值',
                'cal_count'             => '计算机构数量',
                'rebate_type_txt'       => '返利形式',
                'unit_txt'              => '返利单位',
                'method_txt'            => '计算方法',
                'rebate_content'        => '返利值',
                'total_fanli'           => '返利结果',
                'start_time'            => '规则起始时间',
                'end_time'              => '规则终止时间',
                'cal_start_time'        => '计算开始时间',
                'cal_end_time'          => '计算截止时间',
                'cal_day'               => '累计计算天数',
            ],
            'multiSheet' => [
                [
                    'sheetName'=> '返利工单详情',
                    'title' => [
                        'no'                    => '返利单号',
                        'policy_id'             => '使用规则ID',
                        'id'                    => '明细ID',
                        'object_name'           => '机构名称',
                        'object_code'           => '机构编码',
                        'provice_name'          => '适用省份',
                        'card_type'             => '卡类型',
                        'vice_no'               => '副卡号',
                        'main_no'               => '对应主卡',
                        'cal_object_txt'        => '计算对象',
                        'total_money'           => '计算对象汇总值',
                        'rebate_type_txt'       => '返利形式',
                        'unit_txt'              => '返利单位',
                        'method_txt'            => '计算方法',
                        'rebate_content'        => '返利值',
                        'total_fanli'           => '返利结果',
                        'start_time'            => '规则起始时间',
                        'end_time'              => '规则终止时间',
                    ],
                    'data' => $details
                ]
            ],
            'data' => $calculate,
        ];

        try {
            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                if (in_array($data['name'], ['total_money', 'total_fanli', 'id', 'object_code', 'vice_no', 'main_no'])) {
                    $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            });
            Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], $this->logChannel);

            $res['file_name'] = $fileName;
            $res['file_path'] = $fileUrl;

        } catch (\Exception $exception) {
            Log::error(__METHOD__ . "#导出异常：", [strval($exception)], $this->logChannel);
        }
        return $res;
    }
    private function getExportDataForEntityTmp($params)
    {
        unset($params['_export']);
        $calculate = $details = [];

        $i = 0;
        $params['take'] = $this->pageSize;

        while (true) {
            $params['skip'] = $i * $this->pageSize;

            $data = OilRebateCalculate::getList($params);

            if ($i == 0 && $data->isEmpty()) {
                OilDownload::updateByJobsId([
                    'jobs_id'  => $this->jobs->jobId,
                    'status'   => 20,
                    'filename' => '没有要导出的数据',
                    'rate'     => 100,
                ]);
                return [$calculate, $details];
            } elseif ($data->isEmpty()) {
                return [$calculate, $details];
            }

            foreach ($data as $v) {
                $v->rebate_content = $this->formatFanLiVal($v);
                $v->card_type = RebatePolicy::getCardTypeTxt($v->card_type);

                $detail = OilRebateCalDetails::getList(['cal_id' => $v->id, '_export' => 1])->toArray();

                foreach ($detail['data'] as &$_v) {
                    $_v['rebate_content'] = $v->rebate_content;
                    $_v['card_type'] = $v->card_type;
                }

                $details = array_merge($details, $detail['data']);
            }

            $calculate = array_merge($calculate, $data->toArray());

            $i++;
        }
    }

    private function generateExportFileForEntityMonth($params)
    {
        $res = [
            'file_path' => '',
            'file_name' => ''
        ];

        $calculate = $this->getExportDataForMonth($params);
        if (empty($calculate))
            return $res;

        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . $this->xlxFile;

        $fileExt  = 'xlsx';
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }

        $fileName = $this->title . date("YmdHis");

        $exportData = [
            'filePath'   => $filePath,
            'download'   => 1,
            'fileName' => $fileName,
            'sheetName' => '工单视角',
            'fileExt'    => $fileExt,
            'title'     => [
                'no'                    => '返利工单号',
                'policy_id'             => '使用规则ID',
                'policy_name'           => '规则名称',
                'card_type'             => '卡类型',
                'policy_level'          => '规则等级',
                'cal_object_txt'        => '计算对象',
                'total_money'           => '计算对象汇总值',
                'cal_count'             => '计算机构数量',
                'rebate_type_txt'       => '返利形式',
                'unit_txt'              => '返利单位',
                'method_txt'            => '计算方法',
                'rebate_content'        => '返利值',
                'total_fanli'           => '返利结果',
                'start_time'            => '规则起始时间',
                'end_time'              => '规则终止时间',
                'cal_start_time'        => '计算开始时间',
                'cal_end_time'          => '计算截止时间',
                'cal_day'               => '累计计算天数',
            ],
            'data' => $calculate,
        ];

        try {
            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                if (in_array($data['name'], ['total_money', 'total_fanli'])) {
                    $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            });
            Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], $this->logChannel);

            $res['file_name'] = $fileName;
            $res['file_path'] = $fileUrl;

        } catch (\Exception $exception) {
            Log::error(__METHOD__ . "#导出异常：", [strval($exception)], $this->logChannel);
        }
        return $res;
    }
}