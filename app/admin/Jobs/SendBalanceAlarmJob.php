<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/4/3
 * Time: 下午3:38
 */

namespace Jobs;


use Framework\DingTalk\DingTalkAlarm;
use Framework\Excel\ExcelWriter;
use Framework\Log;
use Framework\Queue;
use Fuel\Defines\AccountBalanceAlarm;
use Fuel\Defines\OilCom;
use Models\OilCardSupplyer;
use Models\OilDownload;
use Models\OilFanliPolicy;

class SendBalanceAlarmJob extends Queue
{
    public    $dingTalkToken = '3efca675c2594042b89b5b3771a90763';
    public $params;

    public function __construct(array $params = [])
    {
        parent::__construct($params);
        $this->params;
    }

    public function handle()
    {
        global $app;

        $content   = [];
        $title     = $this->params['org_name'] . '(' . $this->params['orgcode'] . ')现金账户余额提醒';
        $content[] = '标题：' . $title;
        $content[] = '时间：' . date("Y-m-d H:i:s");
//        $content[] = '##### 账户：'.$this->params['account_no'];
        $content[] = '机构名：' . $this->params['org_name'];
        $content[] = '机构号：' . $this->params['orgcode'];
        $content[] = '可用余额：' . $this->params['balance'];
        Log::error(__METHOD__."#余额不足content",[$content],'balance_alarm');
        (new DingTalkAlarm())->sendByTokenForMarkDown(
            [
                'title'     => $title,
                'content'   => implode("\n", $content),
                'token'     => $this->dingTalkToken,
                'atMobiles' => [],
                'isAtAll'   => FALSE

            ]
        );

    }
}