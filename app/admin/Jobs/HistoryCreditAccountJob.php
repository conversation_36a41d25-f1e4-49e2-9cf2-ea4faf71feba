<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/3/26
 * Time: 下午8:05
 */

namespace Jobs;

use Framework\Log;
use Framework\Queue;
use Framework\Excel\ExcelWriter;
use Models\OilCreditAccount;
use Models\OilDownload;

class HistoryCreditAccountJob extends Queue
{

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        $params = $this->params;
        $taskInfo = $this->jobs;
        Log::error('$params:' . var_export($params,TRUE), [], 'HistoryCreditAccount');
        try {
            //todo 处理旧1号充值卡
            //todo 处理机构原有1号充值卡信用账户
            //todo 为机构新增卡库存
            require_once APP_MODULE_ROOT . DIRECTORY_SEPARATOR . 'oil_card_account' . DIRECTORY_SEPARATOR . 'control.php';
            $accountObj = new \oil_card_account();
            $accountObj->addCardAccountForCredit($params['orgcode']);

            $createtime = "2018-04-01";
            \Fuel\Service\CardAccountStock::historyCardCredit($params['org_id'],$params['orgcode'],$createtime);

        } catch (\Exception $e) {
            Log::error('导出序号-$e:' . strval($e), [], 'HistoryCreditAccount');
        }
    }
}