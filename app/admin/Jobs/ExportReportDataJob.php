<?php
/**
 * 副卡信息导出
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/4/5
 * Time: 上午10:18
 */

namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Log;
use Framework\Queue;
use Fuel\Defines\OrgDefine;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCustomerIssue;
use Models\OilOrg;
use Models\OilOrgDayCredit;
use Models\OilOrgDayRemain;

class ExportReportDataJob extends Queue
{
    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public $exceiptOrg = [
        "200000","200021","2001PP","200I1A","200IQP","200IWX","200NWI","201CGH","201CGI","201DUT","201F5L","201GK7",
        "201GNE","201JM6","201KMM","201LBN","201LHF","201Q4V","201SXZ","201YWU","2025NI","2026BD","2026BV","2026FO",
        "2026JW","202COT","202L1J","203N27","203XT3","2047WR"
    ];

    public function handle()
    {
        $params = $this->params;
        $taskInfo = $this->jobs;

        if (!in_array($params['report_type'], ['day', 'movement'])) {
            Log::error('类型不合法', [$params], 'report_');
            return;
        }

        try {
            if ($params['report_type'] == "day") {
                $this->exportDay($params, $taskInfo);
            } else {
                $this->exportMove($params, $taskInfo);
            }
        } catch (\Exception $e) {
            \Models\OilDownload::updateByJobsId([
                'jobs_id' => $taskInfo->jobId,
                'status' => -20,
                'filename' => '导出失败',
                'rate' => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], 'report_');
        }
    }

    public function exportDay($params = [], $taskInfo)
    {
        $daySql = "
SELECT
	a.report_date,
    sum(total_cash_remain) as total_cash_remain,
    0 as total_credit_remain, 
    sum(total_zsh_remain+total_zsy_remain+total_cylmk_remain+total_qiaopai_remain) as entity_cash_remain,
    sum(total_zsh_reserve_remain+total_zsy_reserve_remain+total_cylmk_reserve_remain+total_qiaopai_reserve_remain) as entity_reserve_remain,
    sum(total_sj_czk_remain) as total_sj_czk_remain,
    sum(total_sj_fck_remain) as total_sj_fck_remain,
    sum(total_clk_czk_remain) as total_clk_czk_remain,
    sum(total_sj_czk_remain + total_sj_fck_remain + total_clk_czk_remain) as total_elec_remain,
    sum(total_card_num) as total_card_num,
    sum(total_remain) as total_money,
    max(a.createtime) as createtime, max(a.updatetime) as updatetime,
	a.orgcode,
	org.org_name,
	a.orgroot
FROM
	oil_org_day_remain a 
	LEFT JOIN oil_org as org on a.orgcode = org.orgcode
WHERE
	a.report_date = '" . $params['date_time'] . "' and org.is_test = 1 and org.is_del = 0 and a.orgroot not in ('" . implode("','", $this->exceiptOrg) . "')
	GROUP BY a.orgcode";

        Log::error('exportDay-=-=：sql:'.$daySql, [$params], 'report_');

        $data = Capsule::connection('slave')->select($daySql);

        if (count($data) == 0) {
            \Models\OilDownload::updateByJobsId([
                'jobs_id' => $taskInfo->jobId,
                'status' => -20,
                'filename' => '没有可以导出的数据',
                'rate' => 100,
            ]);
            return;
        }

        \Models\OilDownload::updateByJobsId([
            'jobs_id' => $taskInfo->jobId,
            'status' => 10,
            'rate' => 1,
        ]);

        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $fileName = 'day_' . date("Ymd_H_i_s") . rand(100, 999);
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'day_report' . DIRECTORY_SEPARATOR;
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }

        $exportData = [];
        $orgRootMap = [];
        foreach ($data as $item) {
            $orgRoot = substr($item->orgcode, 0, 6);
            if ($orgRoot == OrgDefine::ORG_WC && strlen($item->orgcode) >= 10) {
                $orgRoot = substr($item->orgcode, 0, 10);
            }

            $root = OilOrg::getByCacheOrgcode($orgRoot);
            $one['org_root_name'] = $orgRoot." ".$root->org_name;
            $one['orgroot'] = $orgRoot . "\t";
            $one['org_name'] = $item->org_name;
            $one['orgcode'] = $item->orgcode . "\t";
            $one['total_cash_remain'] = $item->total_cash_remain;
            $one['entity_cash_remain'] = $item->entity_cash_remain;
            $one['entity_reserve_remain'] = $item->entity_reserve_remain;
            $one['total_sj_czk_remain'] = $item->total_sj_czk_remain;
            $one['total_sj_fck_remain'] = $item->total_sj_fck_remain;
            $one['total_clk_czk_remain'] = $item->total_clk_czk_remain;
            $one['total_elec_remain'] = $item->total_elec_remain;
            $one['total_credit_remain'] = 0;
            if ($orgRoot != OrgDefine::ORG_WC) {
                $credit = OilOrgDayCredit::where("orgcode", $item->orgcode)->where('report_date','like', substr($params['date_time'],0,16)."%")->sum('credit_blance');
                $one['total_credit_remain'] = $credit;
            }
            $one['total_money'] = $item->total_money+$one['total_credit_remain'];
            $one['total_card_num'] = $item->total_card_num;
            $exportData[] = $one;

            /*if (!empty($orgRoot)) {
                $root = OilOrg::getByCacheOrgcode($orgRoot);
                $orgRootMap[$orgRoot]['org_name'] = $root->org_name;
                $orgRootMap[$orgRoot]['orgroot'] = $orgRoot . "\t";
                $orgRootMap[$orgRoot]['orgcode'] = $orgRoot . "\t";
                $orgRootMap[$orgRoot]['cash_remain'] = bcadd($orgRootMap[$orgRoot]['cash_remain'], $item->cash_remain, 2);
                $orgRootMap[$orgRoot]['card_remain'] = bcadd($orgRootMap[$orgRoot]['card_remain'], $item->card_cash, 2);
                $orgRootMap[$orgRoot]['reserve_remain'] = bcadd($orgRootMap[$orgRoot]['reserve_remain'], $item->reserve_remain, 2);
                $orgRootMap[$orgRoot]['total_remain'] = bcadd($orgRootMap[$orgRoot]['total_remain'], $one['total_remain'], 2);
                $orgRootMap[$orgRoot]['card_num'] = bcadd($orgRootMap[$orgRoot]['card_num'], $item->card_num, 2);
                $orgRootMap[$orgRoot]['credit_remain'] = bcadd($orgRootMap[$orgRoot]['credit_remain'], $one['credit_remain'], 2);
            }*/
        }

        Log::error('机构总数:' . count($exportData) . ",顶级总数：" . count($orgRootMap), [], 'report_');


        $orgUrl = $this->exportDayJob($exportData, "机构余额");
        //$rootUrl = $this->exportDayJob(array_values($orgRootMap), "顶级机构余额");
        $rootUrl = "";

        Log::error('机构Url:' . $orgUrl . ",顶级Url：" . $rootUrl, [], 'report_');

        unset($exportData);
//        unset($orgRootMap);

        $zipFile = $fileName . '.zip';
        $zip = new \ZipArchive();
        if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
            exit("can't open " . $filePath . $zipFile . " zipFile!");
        }
        $zip->addFile($orgUrl, basename($orgUrl));
        //$zip->addFile($rootUrl, basename($rootUrl));

        $zip->close();


        $filePath = $filePath . $zipFile;

        $url = (new \commonModel())->fileUploadToOss($filePath);
        Log::error('$url:' . $url, [], 'report_');

        \Models\OilDownload::updateByJobsId([
            'jobs_id' => $taskInfo->jobId,
            'status' => 20,
            'filename' => $zipFile,
            'filetype' => \helper::getFileType($filePath),
            'filesize' => round(filesize($filePath) / 1024, 2),
            'url' => $url,
            'rate' => 100,
        ]);
    }

    public function exportDayJob($exportData, $name = "")
    {
        //导出数据
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $exportData = [
            'filePath' => $realPath . 'data' . DIRECTORY_SEPARATOR . 'day_report',
            'fileName' => $name . "_" . date("YmdHis") . rand(100, 999),
            'fileExt' => 'xls',
            'sheetName' => $name,
            'download' => 1,
            'title' => [
                'org_name' => '机构名称',
                'orgcode' => '机构编码',
                'org_root_name' => '顶级机构',
                'total_cash_remain' => 'a.机构现金余额',
                'total_credit_remain' => 'b.机构授信余额',
                'entity_cash_remain' => 'c.实体卡现金余额',
                'entity_reserve_remain' => 'd.实体卡备付金余额',
                'total_elec_remain' => 'e.电子卡现金余额',
                'total_sj_czk_remain' => 'f.司机充值卡余额',
                'total_sj_fck_remain' => 'g.司机发财卡余额',
                'total_clk_czk_remain' => 'h.车辆充值卡余额',
                'total_money' => 'i.机构总余额',
                'total_card_num' => '副卡数量',
            ],
            'data' => $exportData,
        ];

        $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });

        return $fileUrl;
    }

    public function exportMove($params = [], $taskInfo)
    {
        $moveSql = "
SELECT
	a.org_code,
	a.orgroot,
	org.org_name,
	sum( a.total_charge ) as charge,
	sum( a.total_cal_charge ) as cal_charge,
	sum( a.total_fanli_charge ) as fanli_charge,
	sum( a.total_credit_charge ) as credit_charge,
	sum( a.total_credit_borrow ) as credit_borrow,
	sum( a.total_credit_money ) as credit_money,
	sum( a.total_credit_repaid ) as credit_repaid,
	sum( a.total_not_credit_repay ) as not_credit_repay,
	sum( a.today_credit_trades ) as credit_trades,
	sum( a.total_trades ) as trades,
	sum( a.total_share_trades ) as share_trades,
	sum( a.total_site_assign ) as site_assign,
	sum( a.foss_entity_assign ) as entity_assign,
	sum( a.foss_elec_assign ) as elec_assign";
	if($params['flag'] == 3) {
        $moveSql .=", sum(a . total_direct_total) as direct_total";
    }
    $moveSql .= " FROM
	".$params['select_table']." a 
	LEFT JOIN oil_org as org on a.org_code = org.orgcode
WHERE
	a.cal_date = '" . $params['date_time'] . "' and org.is_test = 1 and org.is_del = 0 and a.orgroot not in ('" . implode("','", $this->exceiptOrg) . "')
	GROUP BY a.org_code";

        Log::error('exportMove-=-=：sql:'.$moveSql, [$params], 'report_');

        $data = Capsule::connection('slave')->select($moveSql);

        if (count($data) == 0) {
            \Models\OilDownload::updateByJobsId([
                'jobs_id' => $taskInfo->jobId,
                'status' => -20,
                'filename' => '没有可以导出的数据',
                'rate' => 100,
            ]);
            return;
        }

        \Models\OilDownload::updateByJobsId([
            'jobs_id' => $taskInfo->jobId,
            'status' => 10,
            'rate' => 1,
        ]);

        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $fileName = 'movement_' .substr( $params['date_time'],0,7)."_".date("Ymd_H_i_s") . "_" . rand(100, 99999);
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'move' . DIRECTORY_SEPARATOR;
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }

        $exportData = [];
        $orgRootMap = [];
        foreach ($data as $item) {

            $orgRoot = substr($item->org_code, 0, 6);

            $one['org_name'] = $item->org_name;
            $one['orgroot'] = $orgRoot . "\t";
            $one['orgcode'] = $item->org_code . "\t";
            $one['charge'] = $item->charge;
            $one['cal_charge'] = $item->cal_charge;
            $one['fanli_charge'] = $item->fanli_charge;
            $one['credit_charge'] = $item->credit_charge;
            $one['credit_borrow'] = $item->credit_borrow;
            $one['credit_money'] = $item->credit_money;
            $one['credit_repaid'] = $item->credit_repaid;
            $one['not_credit_repay'] = $item->not_credit_repay;
            $one['credit_trades'] = $item->credit_trades;
            $one['trades'] = $item->trades;
            $one['share_trades'] = $item->share_trades;
            $one['site_assign'] = $item->site_assign;
            $one['entity_assign'] = $item->entity_assign;
            $one['elec_assign'] = $item->elec_assign;
            if(isset($params['flag']) && $params['flag'] == 3) {
                $one['direct_total'] = $item->direct_total;
            }
            $exportData[] = $one;

            if (!empty($orgRoot)) {
                $root = OilOrg::getByCacheOrgcode($orgRoot);
                $orgRootMap[$orgRoot]['org_name'] = $root->org_name;
                $orgRootMap[$orgRoot]['orgroot'] = $orgRoot . "\t";
                $orgRootMap[$orgRoot]['orgcode'] = $orgRoot . "\t";
                $orgRootMap[$orgRoot]['charge'] = bcadd($orgRootMap[$orgRoot]['charge'], $item->charge, 2);
                $orgRootMap[$orgRoot]['cal_charge'] = bcadd($orgRootMap[$orgRoot]['cal_charge'], $item->cal_charge, 2);
                $orgRootMap[$orgRoot]['fanli_charge'] = bcadd($orgRootMap[$orgRoot]['fanli_charge'], $item->fanli_charge, 2);
                $orgRootMap[$orgRoot]['credit_charge'] = bcadd($orgRootMap[$orgRoot]['credit_charge'], $item->credit_charge, 2);
                $orgRootMap[$orgRoot]['credit_borrow'] = bcadd($orgRootMap[$orgRoot]['credit_borrow'], $item->credit_borrow, 2);
                $orgRootMap[$orgRoot]['credit_money'] = bcadd($orgRootMap[$orgRoot]['credit_money'], $item->credit_money, 2);
                $orgRootMap[$orgRoot]['credit_repaid'] = bcadd($orgRootMap[$orgRoot]['credit_repaid'], $item->credit_repaid, 2);
                $orgRootMap[$orgRoot]['not_credit_repay'] = bcadd($orgRootMap[$orgRoot]['not_credit_repay'], $item->not_credit_repay, 2);
                $orgRootMap[$orgRoot]['credit_trades'] = bcadd($orgRootMap[$orgRoot]['credit_trades'], $item->credit_trades, 2);
                $orgRootMap[$orgRoot]['trades'] = bcadd($orgRootMap[$orgRoot]['trades'], $item->trades, 2);
                $orgRootMap[$orgRoot]['share_trades'] = bcadd($orgRootMap[$orgRoot]['share_trades'], $item->share_trades, 2);
                $orgRootMap[$orgRoot]['site_assign'] = bcadd($orgRootMap[$orgRoot]['site_assign'], $item->site_assign, 2);
                $orgRootMap[$orgRoot]['entity_assign'] = bcadd($orgRootMap[$orgRoot]['entity_assign'], $item->entity_assign, 2);
                $orgRootMap[$orgRoot]['elec_assign'] = bcadd($orgRootMap[$orgRoot]['elec_assign'], $item->elec_assign, 2);
                if(isset($params['flag']) && $params['flag'] == 3) {
                    $orgRootMap[$orgRoot]['direct_total'] = bcadd($orgRootMap[$orgRoot]['direct_total'], $item->direct_total, 2);
                }
            }
        }

        Log::error('move机构总数:' . count($exportData) . ",move顶级总数：" . count($orgRootMap), [], 'report_');


        $orgUrl = $this->exportMoveJob($exportData, "机构movement", $params);
        $rootUrl = $this->exportMoveJob(array_values($orgRootMap), "顶级机构余额movement", $params);

        Log::error('move机构Url:' . $orgUrl . ",move顶级Url：" . $rootUrl, [], 'report_');

        unset($exportData);
        unset($orgRootMap);

        $zipFile = $fileName . '.zip';
        $zip = new \ZipArchive();
        if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
            exit("can't open " . $filePath . $zipFile . " zipFile!");
        }
        $zip->addFile($orgUrl, basename($orgUrl));
        $zip->addFile($rootUrl, basename($rootUrl));

        $zip->close();


        $filePath = $filePath . $zipFile;

        $url = (new \commonModel())->fileUploadToOss($filePath);
        Log::error('move-$url:' . $url, [], 'report_');

        \Models\OilDownload::updateByJobsId([
            'jobs_id' => $taskInfo->jobId,
            'status' => 20,
            'filename' => $zipFile,
            'filetype' => \helper::getFileType($filePath),
            'filesize' => round(filesize($filePath) / 1024, 2),
            'url' => $url,
            'rate' => 100,
        ]);
    }

    public function exportMoveJob($exportData, $name = "", $params = [])
    {
        //导出数据
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $title = [
            'org_name' => '机构名称',
            'orgcode' => '机构编码',
            'orgroot' => '顶级机构',
            'charge' => '常规充值',
            'cal_charge' => '计算返利',
            'fanli_charge' => '返利充值',
            'credit_charge' => '授信充值',
            'credit_borrow' => '授信借款',
            'credit_money' => '授信账单金额',
            'credit_repaid' => '授信还款',
            'not_credit_repay' => '未结账单',
            'credit_trades' => '授信今日消费',
            'trades' => '期间全部消费',

        ];
        if(isset($params['flag']) && $params['flag'] == 3) {
            $title['direct_total'] = '下游直降总额';
        }
        $title['share_trades'] = '共享卡消费';
        $title['site_assign'] = '主站累计分配';
        $title['entity_assign'] = 'foss实体卡分配';
        $title['elec_assign'] = '电子卡分配';
        $exportData = [
            'filePath' => $realPath . 'data' . DIRECTORY_SEPARATOR . 'move',
            'fileName' => $name . "_" . date("YmdHis") . rand(100, 999),
            'fileExt' => 'xls',
            'sheetName' => $name,
            'download' => 1,
            'title' => $title,
            'data' => $exportData,
        ];

        $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });

        return $fileUrl;
    }
}