<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2022/1/10
 * Time: 11:35 PM
 */

namespace Jobs;

use Framework\Log;
use Framework\Queue;
use Framework\DingTalk\DingTalkAlarm;

use Fuel\Defines\CardTradeConf;
use Fuel\Defines\OilCom;
use Fuel\Defines\SupplierAccountConf;
use Fuel\Service\CardTradeService;
use Fuel\Service\RebateCaculateService;
use Fuel\Service\Supplier;
use Models\OilAccountPoolDetail;
use Models\OilCardViceTrades;
use Models\OilCardViceTradesExt;
use Models\OilDownload;


/**
 * Class UpstreamSettleDataWriteJob
 * @package Jobs
 *
 * 注：
 *  异步写上游结算单价、结算金额
 *
 */
class UpstreamSettleDataWriteJob extends Queue
{
    const LOG_CHANNEL = 'upstream_settle_data_write';

    const TITLE = "异步写上游结算数据";

    const QUEUE = "addUpstreamSettleDataWriteJob";

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        Log::error(self::TITLE . ' 参数', [$this->jobs], self::LOG_CHANNEL);

        if (empty($this->params['id'])) {
            $msg = '参数不完整，执行失败';
            $this->updateProcess(['remark' => $msg, 'status' => -10, 'rate' => 100]);

            Log::error(self::TITLE . '-参数不完整，执行失败', [$this->params], self::LOG_CHANNEL);

            throw new \RuntimeException($msg, 100);
        }

        $trade = OilCardViceTrades::getById(['id' => $this->params['id']]);
        if (empty($trade)) {
            throw new \RuntimeException(self::TITLE . '，副卡交易记录不存在', '1100001');
        }
        $trade = $trade->toArray();

        $ext = OilCardViceTradesExt::getOneInfo(['trades_id' => $this->params['id']]);
        if (empty($ext)) {
            throw new \RuntimeException(self::TITLE . '，副卡交易记录扩展信息不存在', '1100001');
        }
        $trade['ext'] = $ext->toArray();

        $this->updateProcess(['remark' => '开始执行', 'status' => 20, 'rate' => 40]);

        // 获取上游供应商
        $supplier = (new CardTradeService())->getSupplierInfo($trade);
        $trade += $supplier;
        Log::error(self::TITLE . ' 供应商数据 ', $trade, self::LOG_CHANNEL);

        try {

            (new Supplier())->writeSettleData($trade);

        } catch (\Exception $e) {
            Log::error('异常：' . __METHOD__, [strval($e)], self::LOG_CHANNEL);

            $content[] = "* 项目：" . self::TITLE;
            $content[] = "* 错误信息：" . $e->getMessage();
            (new DingTalkAlarm())->alarmToGroup(self::TITLE, implode("\n\n", $content), [], true, false);

            $this->updateProcess(['remark' => '执行失败，' . $e->getMessage(), 'status' => -20, 'rate' => 100]);

            throw new \RuntimeException('执行失败-' . $e->getMessage(), 2);
        }

        if (OilCom::isElectronicCardTrade($trade['oil_com'])) {

            //G7WALLET-4874
            if ( !empty($trade['supplier_id']) && $trade['trade_type'] == CardTradeConf::ELECTRON_TRADE_TYPE ) {

                $resType = OilCardViceTradesExt::isTradeCancel($trade) ?
                    SupplierAccountConf::STATEMENT_RES_TYPE_CONSUME_CANCEL :
                    SupplierAccountConf::STATEMENT_RES_TYPE_CONSUME;

                $addSupplierAccountStatementParams = [
                    'res_type' => $resType,
                    'res_id' => $trade['id']
                ];
                //G7WALLET-1881
                /*if (bccomp($res['tradeInfo']['trade_money'], $res['tradeInfo']['xpcode_pay_money'],2) > 0) {
                    $addSupplierAccountStatementParams['res_type'] = [SupplierAccountConf::STATEMENT_RES_TYPE_CONSUME,SupplierAccountConf::STATEMENT_RES_TYPE_CONSUME_REBATE];
                }*/
                $task = (new SupplierAccountStatementJob($addSupplierAccountStatementParams))
                    ->setTaskName('异步写供应商账户流水')
                    ->onQueue('addSupplierAccountStatementJob')
                    ->setTries(2)
                    ->dispatch();
                Log::error(self::TITLE . ' 异步写供应商账户流水 分发', [$task], self::LOG_CHANNEL);
            }
        }
        $this->updateProcess(['remark' => self::TITLE, 'status' => 20, 'rate' => 100]);
        //新增价格规则id
        // $trade['price_id'] = $this->params['price_id'] ?? '';
        // $task = (new UpstreamRebateCalTempWriteJob($trade))
        //     ->setTaskName(UpstreamRebateCalTempWriteJob::TITLE)
        //     ->onQueue(UpstreamRebateCalTempWriteJob::QUEUE)
        //     ->setTries(2)
        //     ->dispatch();
        // Log::error(self::TITLE . ' 异步写入上游返利计算临时表 分发', [$task], self::LOG_CHANNEL);

        //todo 回退上游分摊充返金额
        (new RebateCaculateService())->rebackFanliPool($trade);
    }

    /**
     * 更新oilDownLoad进度
     * @param array $params
     */
    public function updateProcess(array $params)
    {
        OilDownload::updateByJobsId([
            'jobs_id' => $this->jobs->jobId,
            'filename' => $params['remark'],
            'status' => $params['status'],
            'rate' => $params['rate'],
        ]);
    }
}