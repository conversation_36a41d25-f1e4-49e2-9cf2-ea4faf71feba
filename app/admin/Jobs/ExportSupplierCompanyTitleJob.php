<?php
/**
 * 回票抬头信息导出
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/4/5
 * Time: 上午10:18
 */

namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Log;
use Framework\Queue;
use Models\OilDownload;
use Models\OilSupplierCompanyTitle;

class ExportSupplierCompanyTitleJob extends Queue
{
    protected $pageSize = 1000;

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        $params   = $this->params;
        $taskInfo = $this->jobs;
        Log::error('$taskInfo' . var_export($taskInfo, TRUE), [], 'debugExport');
        try {
            $total = OilSupplierCompanyTitle::getList($params);
            Log::error('$total' . var_export($total, TRUE), [], 'debugExport');

            if ($total == 0) {
                OilDownload::updateByJobsId([
                    'jobs_id'  => $taskInfo->jobId,
                    'status'   => -20,
                    'filename' => '没有可以导出的数据',
                    'rate'     => 100,
                ]);
            } else {
                unset($params['_count'], $params['_export'],$params['count']);
                OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 1,
                ]);
                $totalPage = ceil($total / $this->pageSize);
                Log::error('$totalPage' . var_export($totalPage, TRUE), [], 'debugExport');
                $_tmpData = [];
                for ($i = 0; $i < $totalPage; $i++) {
                    $params['page'] = $i+1;
                    $params['limit'] = $this->pageSize;
                    Log::error('$params:' . var_export($params, TRUE), [], 'debugExport');
                    $record = OilSupplierCompanyTitle::getList($params);
                    $record = $record->toArray();

                    if (count($record) > 0) {
                        $_tmpData = array_merge($_tmpData, $record['data']);
                    }
                    unset($record);
                }
                OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 30,
                ]);

                if ($_tmpData) {
                    $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
                    $fileName = 'OilSupplierCompanyTitle_' . date("Ymd_H_i_s") . rand(100, 999);
                    $filePath = $realPath . 'download' . DIRECTORY_SEPARATOR .'OilSupplierCompanyTitle'. DIRECTORY_SEPARATOR;
                    if (!file_exists($filePath)) {//创建目录
                        @mkdir($filePath, 0777);
                    }

                    $filesArr = [];
                    //导出数据
                    $_data = array_chunk($_tmpData, 100000);

                    Log::error('$_data:' . count($_data), [], 'debugExport');

                    foreach ($_data as $k => $v) {
                        $filesArr[] = $this->exportJob($v, ($k + 1));
                        OilDownload::updateByJobsId([
                            'jobs_id' => $taskInfo->jobId,
                            'status'  => 10,
                            'rate'    => 29 + (($k + 1) / count($_data)) * 70,
                        ]);
                    }
                    unset($_data);
                    Log::error('$filesArr' . var_export($filesArr, TRUE), [], 'debugExport');

                    $zipFile = $fileName . '.zip';
                    $zip     = new \ZipArchive();
                    if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
                        exit("can't open " . $filePath . $zipFile . " zipFile!");
                    }
                    for ($i = 0; $i < count($filesArr); $i++) {
                        $zip->addFile($filesArr[$i], basename($filesArr[$i]));
                    }
                    $zip->close();
                    
                    $filePath = $filePath . $zipFile;
                     
                    $url = (new \commonModel())->fileUploadToOss($filePath);

                    OilDownload::updateByJobsId([
                        'jobs_id'  => $taskInfo->jobId,
                        'status'   => 20,
                        'filename' => $zipFile,
                        'filetype' => \helper::getFileType($filePath),
                        'filesize' => round(filesize($filePath) / 1024, 2),
                        'url'      => $url,
                        'rate'     => 100,
                    ]);
                }
            }
        } catch (\Exception $e) {
            OilDownload::updateByJobsId([
                'jobs_id'  => $taskInfo->jobId,
                'status'   => -20,
                'filename' => '导出失败',
                'rate'     => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], 'debugExport');
        }


    }

    public function exportJob($result, $page)
    {
        if ($result) {
            $extType  = count($result) >= 20000 ? 'xls' : 'xls';
            $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;

            $exportData = [
                'filePath'  => $realPath . 'download' . DIRECTORY_SEPARATOR . 'OilSupplierCompanyTitle',
                'fileName'  => 'Oil_Supplier_CompanyTitle_' . date('YmdHis') . rand(100, 999) . '_' . $page,
                'fileExt'   => $extType,
                'sheetName' => "回票抬头",
                'download'  => 1,
                'title'     => [
                    'id'                     => '回票抬头id',
                    'supplier_company_title' => '回票抬头名称',
                    'supplier_id'            => '供应商ID',
                    'supplier_name'          => '归属供应商',
                    'collect_company_id'     => '收款公司ID',
                    'company_name'           => '归属收款公司',
                    'open_status'            => '审核状态',
                    'status'                 => '回票抬头状态',
                    'creator'                => '创建人',
                    'createtime'             => '创建时间',
                    'open_operator'          => '审核人',
                    'opentime'               => '审核时间',
                    'last_operator'          => '最后操作人',
                    'updatetime'             => '最后操作时间',
                ],
                'data'      => $result,
            ];

            $fileUrl = ExcelWriter::exportXls(
                $exportData, function ($phpExcelObj, $data, $lineCell) {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            );

            Log::error('bbbb导出序号-$fileUrl:' . var_export($fileUrl, TRUE), [], 'debugExport');

            return $fileUrl;
        }
    }
}