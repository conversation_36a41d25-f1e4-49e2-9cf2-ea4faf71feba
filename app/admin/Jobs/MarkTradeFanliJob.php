<?php


namespace Jobs;

use Framework\Helper;
use Framework\Log;
use Framework\Queue;
use Fuel\Defines\IsFinal;
use Fuel\Defines\OilCom;
use Fuel\Defines\OrgStatus;
use Fuel\Defines\Proxy;
use Fuel\Defines\TradesType;
use Fuel\Service\CardTradeService;
use Fuel\Service\FanliSrv;
use Fuel\Service\OrgConfigService;
use Fuel\Service\UpstreamRebate\TempTradeDataService;
use Models\OilAccountMoney;
use Models\OilAccountMoneyFanliRecords;
use Models\OilAccountMoneyRecords;
use Models\OilCardViceTrades;
use Models\OilDownload;
use Models\OilOrg;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCardViceTradesExt;
use Models\OilStationSupplier;
use Models\OilTrades;
use Models\OilUpstreamRebateTradesTemp;

class MarkTradeFanliJob extends Queue
{
    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    //flag
    //消费 1 or 退款 2
    public function handle()
    {
        Log::error('标记返利-params:', [$this->params], 'use_fanli_');
        if ( empty($this->params['trade']['id']) ) {
            $msg = '参数不完整，执行失败';
            $this->updateProcess(['remark' => $msg, 'status' => -10, 'rate' => 100]);
            Log::error('标记返利-参数不完整，执行失败', [$this->params], 'use_fanli_');

            throw new \RuntimeException($msg, 100);
        }
        $params = $this->params['trade'];
        $flag = $this->params['flag'];
        if(!in_array($flag,[1,2])){
            throw new \RuntimeException("标记标识不合法", 100);
        }

        //同步执行UpstremeRebateCalTempWriteJob里的内容
        try {
            $this->upstreamRebateCalTempWrite($params);
        }catch (\Exception $e){
            Log::error('标记返利-同步执行UpstremeRebateCalTempWriteJob里的内容失败', [$e->getMessage()], 'use_fanli_');
        }

        $org_info = OilOrg::getById(['id'=>$params['org_id']]);
        if(!OrgStatus::isUseNewRebateMark($org_info->orgcode)){
            $this->updateProcess(['remark' => '标记完成', 'status' => 20, 'rate' => 100]);
            return true;
        }

        $this->updateProcess(['remark' => '开始执行标记返利', 'status' => 20, 'rate' => 40]);

        //G7WALLET-6136
        $credit = substr($params['account_no'],0,3);
        //授信账户使用返利消费时，动账一次，产生两笔流水（资金账户 && 返利账户）
        if($credit == '208'){
            $record = OilAccountMoneyRecords::getInfoByFilter([
                "org_id" => $params['org_id'],
                "trade_type" => $flag == 2 ? 1 : -1,
                "sn" => $params['stream_no'],
                //"no_type_only" => $flag == 2 ? "XFCX" : "XF",
            ]);
            Log::error('授信账户-资金账户流水:', [$record], 'use_fanli_');
            if( $record && isset($record->id) ){
                $fanli_record = [];
                $remark = $flag == 2 ? $params['org_name']."的油卡消费".abs($params['trade_money'])."元被退款，其中包含".abs($record->money)."元返利" : $params['org_name']."的油卡消费".abs($params['trade_money'])."元，其中包含".abs($record->money)."元返利";

                $fanli_record["money_id"] = $record->money_id;
                $fanli_record["org_id"] = $record->org_id;
                $fanli_record["trade_type"] = $record->trade_type;
                $fanli_record["money"] = $record->money;
                $fanli_record["after_money"] = $record->after_money;
                $fanli_record["no_type"] = "XF";
                $fanli_record["no"] = $params['id'];
                $fanli_record["sn"] = $params['id'];
                $fanli_record["rate"] = number_format(abs($record->money)/$params['trade_money'],8,".","");
                $fanli_record["account"] = "cash_fanli_remain";
                $fanli_record["createtime"] = \helper::nowTime();
                $fanli_record["remark"] = $remark;
                $fanli_record["operator_id"] = 8888;
                $fanli_record["operator_name"] = "系统自动";
                OilAccountMoneyFanliRecords::add($fanli_record);
            }
            $this->updateProcess(['remark' => '标记完成', 'status' => 20, 'rate' => 100]);
            
            return true;
        }
        $orgInfo = OilOrg::getById(['id'=>$params['org_id']]);
        if(!$orgInfo){
            throw new \RuntimeException('执行失败-机构不存在', 2);
        }

        // 标用一体使用返利 仅支持共享卡
        if ($params['oil_com'] == OilCom::GAS_FIRST_TALLY && $credit == '208') {
            $orgRebateSetting = (new OrgConfigService())->getOrgRebateUseSetting($orgInfo->orgcode);
            Log::error('标记返利-标用一体数据：', [$orgRebateSetting], 'use_fanli_');
            $equalUse = isset($orgRebateSetting['equal_use']) ? $orgRebateSetting['equal_use'] : 0;
            if ($equalUse == 1 ) {
                $this->updateProcess(['remark' => '标记完成', 'status' => 20, 'rate' => 100]);
                return true;
            }
        }

        //G7WALLET-6871
        if( !in_array($params['trade_type'],TradesType::getCashAndSelfOilTradeTypeArr()) ){
            $this->updateProcess(['remark' => '标记完成', 'status' => 20, 'rate' => 100]);
            return true;
        }

        Capsule::connection()->beginTransaction();
        try {
            if($flag == 1) { //消费
                $useFanli = FanliSrv::getUseFanliByMoney([
                    'money' => $params['trade_money'],
                    "org_id" => $params['org_id']
                ],true);
                $use_fanli_money = $useFanli['use_fanli'];
                $money_id = $useFanli["money_id"];
                $after = $useFanli['after_money'];
                $account = $useFanli['account'];
                $rate = $useFanli['rate'];
            } else { // 退款

                //触发报警
                FanliSrv::getUseFanliByMoney([
                    'money' => $params['trade_money'],
                    "org_id" => $params['org_id']
                ],false);

                $use_fanli_money = $params['use_fanli_money'];
                $money_info = OilAccountMoney::getByOrgId(['org_id'=>$params['org_id']]);
                if(!$money_info){
                    throw new \RuntimeException('执行失败-机构现金账户不存在', 2);
                }
                $money_id = $money_info->id;
                $after = $money_info->cash_fanli_remain + abs($use_fanli_money);
                $account = "cash_fanli_remain";
                $rate = number_format(abs($use_fanli_money)/$params['trade_money'],8,".","");
            }

            Log::error('tradeid-flag-userfanliMoney',[$params['id'],$flag,$use_fanli_money] ,'MarkTradeFanliJob');
            if(bccomp($use_fanli_money,0,2) != 0) {
                OilAccountMoneyFanliRecords::updateFanliById($money_id,$use_fanli_money*-1,3,[
                    "org_id" => $params['org_id'],
                    "after_money" => $after,
                    "total_fee" => $params['trade_money'],
                    "org_name" => $params['org_name'],
                    "no" => $params['id'],
                    "sn" => $params['id'],
                    "rate" => abs($rate),
                    "account" => $account,
                ],true,false);
            }

            if( $flag == 1 ) {
                OilCardViceTrades::edit([
                    "id" => $params['id'],
                    "use_fanli_money" => $use_fanli_money,
                    "receipt_remain" => $params['trade_money'] - $use_fanli_money,
                    "updatetime" => \helper::nowTime()
                ]);

                OilTrades::editByFilter(["trades_id" => $params['id']],
                 ["use_fanli_money" => $use_fanli_money]);

                OilUpstreamRebateTradesTemp::editByFilter(["trade_id" => $params['id']],
                  ["use_fanli_money" => $use_fanli_money]);
                
            }

            Capsule::connection()->commit();
            $this->updateProcess(['remark' => '标记完成', 'status' => 20, 'rate' => 100]);

        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            $this->updateProcess( ['remark' => $e->getMessage(), 'status' => -20, 'rate' => 100]);
            Log::error('异常：' . __METHOD__, [strval($e)], "use_fanli_");

            FanliSrv::alarmException([
                "机构ID" => $params['org_id'],
                "信息" => "消费记录返利标记异常",
                "交易ID" => $params['id'],
                "交易金额" => $params['trade_money'],
                "异常信息" => $e->getMessage()
            ]);

            throw new \RuntimeException($e->getMessage(), 2);
        }


    }

    /**
     * 同步执行UpstremeRebateCalTempWriteJob
     */
    public function upstreamRebateCalTempWrite($params)
    {
        $trade = OilCardViceTrades::getById(['id' => $params['id']]);
        if (empty($trade)) {
            Log::error('同步写入上游返利计算临时表副卡交易记录不存在', ['trade_id' => $params['id']], 'MarkTradeFanliJob');
            return;
        }
        $trade = $trade->toArray();

        $trade["ext"] = OilCardViceTradesExt::getOneInfo(['trades_id' => $params['id']]);
        if (empty($trade["ext"])) {
            Log::error('同步写入上游返利计算临时表，副卡交易记录扩展信息不存在', ['trade_id' => $params['id']], 'MarkTradeFanliJob');
            return;
        }

        // 获取上游供应商
        $supplier = (new CardTradeService())->getSupplierInfo($trade);
        $trade += $supplier;
        Log::error('同步写入上游返利计算临时表, 供应商数据 ', 'MarkTradeFanliJob');

        $proxy = array_get($trade, 'is_proxy', Proxy::NO);

        $data = [
            'supplier_id'   => $trade['belong_supplier_id'],
            'area_id'       => array_get($trade, 'area_id', 0),
            'is_proxy'      => $proxy,
            'is_final'      => $proxy == Proxy::YES ? IsFinal::NO_NEED_CAL : IsFinal::NO_CAL,
            'updatetime'    => date('Y-m-d H:i:s')
        ];

        if($trade["ext"]->is_final == 310){
            unset($data['is_final']);
        }

        //增加供应商签约主体(operator_id)
        if(!empty($data['supplier_id'])){
            $supplier_info = OilStationSupplier::where('id',$data['supplier_id'])->first();
            if($supplier_info){
                $data['operator_id'] = $supplier_info->operator_id;
            }
        }
        OilCardViceTradesExt::where('trades_id', $params['id'])->update($data);

        //写入temp和rebate
        $trade['ext']->is_final = $proxy == Proxy::YES ? IsFinal::NO_NEED_CAL : IsFinal::NO_CAL;
        $trade['ext']->operator_id = isset($data['operator_id']) && $data['operator_id'] ? $data['operator_id'] : 0;
        //新增价格规则id
        $trade['price_id'] = $params['price_id'] ?? '';
        (new TempTradeDataService())->write($trade);
    
        if (OilCom::isElectronicCardTrade($trade['oil_com'])) {
            // 对属于代销站的且允许标记且能标记的归属供应商只有一个的进行自动标记
            $autoMarkProxyStationTradeTask = (new AutoMarkProxyStationTradeJob($trade))
                ->setTaskName('代销站消费归属供应商标记')
                ->onQueue('autoMarkProxyStationTradeJob')
                ->setTries(2)
                ->dispatch();
            Log::error('异步写上游结算数据 代销站消费归属供应商标记 分发', [$autoMarkProxyStationTradeTask], 'MarkTradeFanliJob');
        }
    }

    /**
     * 更新oilDownLoad进度
     * @param array $params
     */
    public function updateProcess(array $params)
    {
        OilDownload::updateByJobsId([
            'jobs_id'  => $this->jobs->jobId,
            'filename' => $params['remark'],
            'status'   => $params['status'],
            'rate'     => $params['rate'],
        ]);
    }
}