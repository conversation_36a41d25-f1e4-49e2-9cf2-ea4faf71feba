<?php
namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Helper;
use Framework\Log;
use Framework\Queue;
use Models\OilAccountMoney;
use Models\OilDownload;
use Models\OilOrg;

class ExportAccountMoneyJob extends Queue
{
    protected $pageSize = 1000;

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        $params = $this->params;
        $taskInfo = $this->jobs;

        try {
            if (isset($params['orgcode']) && $params['orgcode']) {
                if (isset($params['org_flag']) && $params['org_flag']) {
                    $params['org_id_list'] = OilOrg::getByOrgcodeLike($params['orgcode']);
                } else {
                    $org              = OilOrg::getByOrgcode($params['orgcode']);
                    $params['org_id'] = $org->id;
                }
            }

            $params['count'] = 1;
            unset($params['_export']);
            $total = OilAccountMoney::getList($params);
            Log::error('bbbb导出序号-$params:' . var_export($params,true), [], 'ExportAccountMoneyJob');
            Log::error('bbbb导出序号-$total1:' . $total, [], 'ExportAccountMoneyJob');
            if (!$total) {
                OilDownload::updateByJobsId([
                    'jobs_id'  => $taskInfo->jobId,
                    'status'   => -20,
                    'filename' => '没有可以导出的数据',
                    'rate'     => 100,
                ]);
            } else {
                unset($params['count']);

                OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 1,
                ]);

                $totalPage = ceil($total / $this->pageSize);
                Log::error('bbbb导出序号-$total2:' . $total, [], 'ExportAccountMoneyJob');
                Log::error('bbbb导出序号-$pageSize:' . $this->pageSize, [], 'ExportAccountMoneyJob');
                Log::error('bbbb导出序号-$totalPage:' . $totalPage, [], 'ExportAccountMoneyJob');

                $_tmpData = [];
                for ($i = 0; $i < $totalPage; $i++) {
                    $params['skip'] = $i * $this->pageSize;
                    $params['take'] = $this->pageSize;

                    $record = OilAccountMoney::getList($params);
                    Log::error('bbbb导出序号-$record:' . count($record), [], 'ExportAccountMoneyJob');
                    if (count($record) > 0) {
                        foreach ($record as &$v) {
                            $v->orgroot = substr($v->orgcode, 0, 6);
                        }
                        $orgRoots    = OilOrg::preOrgRoot($record, ['orgroot']);
                        $orgRootInfo = OilOrg::getByOrgCodesMap($orgRoots);
                        $record      = OilOrg::convertOrgRoot2OrgName($record, $orgRootInfo, ['orgroot']);
                    }
                    $record = !$record ? [] : $record->toArray();
                    $_tmpData = array_merge($_tmpData, $record);
                    unset($record);
                }

                Log::error('bbbb导出序号-$data:' . count($_tmpData), [], 'ExportAccountMoneyJob');

                OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 30,
                ]);

                if ($_tmpData) {
                    //导出数据
                    $fileArr = [];
                    $_data = array_chunk($_tmpData, 100000);
                    foreach ($_data as $k => $v) {
                        Log::error('bbbb导出序号-$chunk:' . count($v), [], 'ExportAccountMoneyJob');
                        $fileArr[] = $this->exportJob($v, ($k + 1));
                        OilDownload::updateByJobsId([
                            'jobs_id' => $taskInfo->jobId,
                            'status'  => 10,
                            'rate'    => 29 + (($k+1)/count($_data))*70,
                        ]);
                    }
                    unset($_data);

                    $zipMap = $this->zipArchive($fileArr, 'oil_account_money');
                    $url = (new \commonModel())->fileUploadToOss($zipMap['filePath']);
                    Log::error('bbbb导出序号-$url:' . $url, [], 'ExportAccountMoneyJob');

                    OilDownload::updateByJobsId([
                        'jobs_id'  => $taskInfo->jobId,
                        'status'   => 20,
                        'filename' => $zipMap['zipName'],
                        'filetype' => \helper::getFileType($zipMap['filePath']),
                        'filesize' => round(filesize($zipMap['filePath']) / 1024, 2),
                        'url'      => $url,
                        'rate'     => 100,
                    ]);
                }
            }
        } catch (\Exception $e) {
            OilDownload::updateByJobsId([
                'jobs_id'  => $taskInfo->jobId,
                'status'   => -20,
                'filename' => '导出失败',
                'rate'     => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], 'ExportAccountMoneyJob');
        }
    }

    public function exportJob($result, $page)
    {
        if ($result) {
            foreach ($result as &$v) {
                foreach ($v as &$item) {
                    $item = htmlspecialchars($item);
                    $item = Helper::trimAll($item);
                }
                $v['account_no']              = $v['account_no'] . "\t";
                $v['orgcode']                 = $v['orgcode'] . "\t";
                $v['charge_total']            = $v['charge_total'] . "\t";
                $v['total_transfer_in']       = $v['total_transfer_in'] . "\t";
                $v['total_transfer_out']      = $v['total_transfer_out'] . "\t";
                $v['assign_total']            = $v['assign_total'] . "\t";
                $v['fanli_total']             = $v['fanli_total'] . "\t";
                $v['shared_card_trade_total'] = $v['fanli_total'] . "\t";
            }
//            $extType      = count($result) >= 20000 ? 'xls' : 'xls';
            $extType = 'csv';
            $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;

            $exportData = [
                'filePath'  => $realPath . 'data' . DIRECTORY_SEPARATOR . 'oil_account_money',
                'fileName'  => 'account_money_'.date('YmdHis') .rand(100,999). '_' . $page,
                'fileExt'   => $extType,
                'sheetName' => "资金账户信息",
                'download'  => 1,
                'title'     => [
                    'account_no' => '帐号',
                    '_orgroot' => '顶级机构编码',
                    'orgroot' => '顶级机构',
                    'orgcode' => '机构编码',
                    'org_name' => '机构名称',
                    'money' => '现金余额',
                    'cash_fanli_remain' => '返利余额',
                    'charge_total' => '累计充值',
                    'total_transfer_in' => '累计转入',
                    'total_transfer_out' => '累计转出',
                    'assign_total' => '累计分配',
                    'fanli_total' => '累计现金返利',
                    'shared_card_trade_total' => '累计共享卡消费',
                    'vice_card_num' => '副卡数量',
                    'last_charge_time' => '最后充值时间',
                ],
                'data'      => $result,
            ];

            $fileUrl = ExcelWriter::exportXls(
                $exportData, function ($phpExcelObj, $data, $lineCell) {
                    if (in_array($data['name'], ['account_no','orgcode','charge_total','total_transfer_in','total_transfer_out','assign_total','fanli_total','shared_card_trade_total'])) {
                        $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
                    } else {
                        $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                    }
                }
            );

            Log::error('bbbb导出序号-$fileUrl:' . var_export($fileUrl,TRUE), [], 'ExportAccountMoneyJob');

            return $fileUrl;
        }
    }

    public function zipArchive($filesArr, $name)
    {
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $fileName = $name . '_' . date("Ymd_H_i_s") . rand(100, 999);
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . $name . DIRECTORY_SEPARATOR;
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }

        $zipFile = $fileName . '.zip';
        $zip = new \ZipArchive();
        if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
            exit("can't open " . $filePath . $zipFile . " zipFile!");
        }
        for ($i = 0; $i < count($filesArr); $i++) {
            $zip->addFile($filesArr[$i], basename($filesArr[$i]));
        }
        $zip->close();

        return ['filePath' => $filePath . $zipFile, 'zipName' => $zipFile];
    }
}