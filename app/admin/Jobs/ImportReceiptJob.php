<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/3/25
 * Time: 下午2:55
 */

namespace Jobs;

use Framework\Excel\ExcelReader;
use Framework\Log;
use Framework\Queue;
use Fuel\Broadcast\publishCheckSales;
use Fuel\Service\InvoiceCheckForSales;
use Models\OilReceiptManage;

class ImportReceiptJob extends Queue
{
    protected $params;
    
    public function __construct(array $params = [])
    {
        parent::__construct($params);
        
        $this->params = $params;
    }
    
    public function handle()
    {
        global $app;
        $app->myAdmin;
        
        $params = $this->params;
        Log::error('jobDebug:' . var_export($this->params, TRUE), [], 'checkInvoice');
        
        if (isset($params['url']) && $params['url']) {
            $file_contents = file_get_contents($params['url']);
            if (!empty($file_contents)) {
                $extMap = explode('.', $params['url']);
    
                $filePath = APP_WWW_ROOT . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . md5(time()) . rand(0, 9999) . '.'.end($extMap);
                file_put_contents($filePath, $file_contents);  //保存到本地的地址
    
                Log::error(__METHOD__ . '调用导入逻辑' . var_export($filePath, TRUE), '', 'checkInvoice');
                $result = (new InvoiceCheckForSales())->importInvoice($filePath);
    
                Log::error(__METHOD__ . '$result:' . var_export($result, TRUE), '', 'checkInvoice');
    
                $broadcast = (new publishCheckSales())->pubImportInvoiceMessage($result, $app->myAdmin);
                Log::error(__METHOD__ . 'boradcase:' . var_export($broadcast, TRUE), '', 'checkInvoice');
            }
        }
    }
}