<?php
/**
 * 返利审核异步任务
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/4/5
 * Time: 上午10:18
 */

namespace Jobs;


use Framework\Log;
use Framework\Queue;
use Fuel\Service\FanLiCalService;
use Models\OilDownload;
use Illuminate\Database\Capsule\Manager as Capsule;


class FanLiCalAuditJob extends Queue
{
	public function __construct(array $params = [])
	{
		parent::__construct($params);
	}
	
	public function handle()
	{
		$params = $this->params;
		$taskInfo = $this->jobs;
		
		if (isset($params['fanli_no']) && $params['fanli_no']) {
			$this->updateTask($taskInfo, -20, '返利审核无单号', 100);
		}
		
		Capsule::connection()->beginTransaction();
		try {
			Log::error(__METHOD__ . "#返利审核", [$params], 'fanLiCalAudit');
			
			$this->updateTask($taskInfo, 10, $params['fanli_no'] . '：返利审核', 10);
			
			(new FanLiCalService())->audit($params['fanli_no']);
			
			//返利推送任务
			(new \Fuel\Service\FanLiPushTaskService())->makePushTask(['fanLiNo' => $params['fanli_no']]);
			
			$this->updateTask($taskInfo, 20, $params['fanli_no'] . '：返利审核', 100);
			
			Capsule::connection()->commit();
		} catch (\Exception $e) {
			Capsule::connection()->rollBack();
			
			$this->updateTask($taskInfo, -20, (isset($params['fanli_no']) ? $params['fanli_no'] : $params['fanli_no']) . '返利审核失败，' . $e->getMessage(), 100);
			
			Log::error(__METHOD__ . "#返利审核失败", ['exception' => strval($e), 'params' => $params], 'fanLiCalAudit');
		}
		
		//下达异步推送任务
		(new FanLiToAccountCenterJob([]))->dispatch();
	}
	
	private function updateTask($taskInfo, $status, $name, $rate)
	{
		OilDownload::updateByJobsId([
			'jobs_id'  => $taskInfo->jobId,
			'status'   => $status,
			'filename' => '',
			'rate'     => $rate,
		]);
	}
}