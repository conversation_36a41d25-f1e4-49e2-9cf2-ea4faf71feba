<?php


namespace Jobs;

use Fuel\Defines\OilType;
use Fuel\Defines\ReceiptDetailTranslate;
use Fuel\Service\InvoiceStockService;
use Fuel\Service\OilTypeService;
use Illuminate\Database\Capsule\Manager as Capsule;
use Framework\Log;
use Framework\Queue;
use Models\OilDownload;
use Models\OilReceiptDetails;
use Models\OilReceiptDetailsTranslate;
use Models\OilReceiptManage;
use Models\OilStandardStockUnitRelation;
use Models\OilTypeCategory;

class InvoiceStockJob extends Queue
{
    private $title = '异步写入油票库存和销项票任务';

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        Log::error('销项票明细', [$this->jobs], 'invoiceLog_');
        if ( empty($this->params['receipt_manage_id']) ) {
            $msg = '参数不完整，执行失败';
            $this->updateProcess(['remark' => $msg, 'status' => -10, 'rate' => 100]);
            Log::error('销项票明细，执行失败', [$this->params], 'invoiceLog_');
            throw new \RuntimeException($msg, 100);
        }

        $this->updateProcess(['remark' => '开始执行', 'status' => 20, 'rate' => 40]);

        $manage_id = $this->params['receipt_manage_id'];
        $is_throw  = isset($this->params['is_throw']) ? $this->params['is_throw'] : false;

        //单位
        $unitArr = (new OilTypeService())->getOilUnit();
        $unitMap = array_flip($unitArr);
        //二级油品
        $classifyArr = [];
        $classifyMap = OilTypeCategory::getTypeName();
        if(count($classifyMap) > 0){
            foreach ($classifyMap as $_key => $_val){
                $classifyArr[$_val['name']] = ['id'=>$_key,"unitId"=>$_val['unitId']];
            }
        }
        Capsule::connection()->beginTransaction();
        try {
            // 发票信息
            $receiptInfo = OilReceiptManage::getById([
                'id' => $manage_id
            ]);
            if (!$receiptInfo) {
                throw new \RuntimeException("发票信息不存在：".$manage_id, 2);
            }
            //线下不验证勾稽状态
            if( $receiptInfo->choice_status != 20 && $receiptInfo->receipt_channel!=1){
                throw new \RuntimeException("该发票未勾稽：".$manage_id, 2);
            }
            $details = OilReceiptDetails::getFilterList(['receipt_manage_id'=>$manage_id]);
            if( count($details) == 0 ){
                throw new \RuntimeException("发票明细不存在：".$manage_id, 2);
            }
             //线下数据--过滤折扣的数据
            if($receiptInfo->receipt_channel==1){
                foreach ($details as $key=>$_item){
                    if(!$_item['unit']&&!$_item['sku']&&!$_item['num']&&$_item['amount']&&$_item['amount']<0){
                        unset($details[$key]);
                    }
                }
            }
            $errorMsg = [];
            foreach ($details as $_item){

                if(!isset($classifyArr[$_item['goods_name']]) || empty($classifyArr[$_item['goods_name']])){
                    Log::error('发票明细的二级分类不存在', ["mid"=>$manage_id,"did"=>$_item['id']], 'invoiceLog_');
                    $errorMsg[$_item['id']][] = "发票明细的二级分类不存在";
                    continue;
                }
                $classifyId = $classifyArr[$_item['goods_name']]['id'];

                if( !isset($unitMap[$_item['unit']]) || empty($unitMap[$_item['unit']]) ){
                    Log::error('发票明细的单位不合法', ["mid"=>$manage_id,"did"=>$_item['id']], 'invoiceLog_');
                    $errorMsg[$_item['id']][] = "发票明细的单位不合法";
                    continue;
                }
                //todo 获取吨升转换关系
                $operator = 3;
                $xs = 1;
                $transList = OilStandardStockUnitRelation::getListByFilter(['oil_id'=>$classifyId,"status"=>10]);
                if(count($transList) > 0){
                    foreach ($transList as $_val){
                        if($_val->standard_unit == $unitMap[$_item['unit']] && $_val->stock_unit == $classifyArr[$_item['goods_name']]['unitId']){
                            $operator = $_val->operator;
                            $xs = $_val->expression;
                            break;
                        }
                    }
                }

                switch ($operator){
                    case 1:
                        $outNum = bcadd( $_item['num'], $xs,10);
                        break;
                    case 2:
                        $outNum = bcsub($_item['num'],$xs,10);
                        break;
                    case 3:
                        $outNum = bcmul($_item['num'],$xs,10);
                        break;
                    case 4:
                        $outNum = bcdiv($_item['num'],$xs,10);
                        break;
                }

                $addData = [];
                $addData['receipt_detail_id'] = $_item['id'];
                $addData['receipt_no'] = $receiptInfo->receipt_no;
                $addData['receipt_code'] = $receiptInfo->receipt_code;
                $addData['classify_id'] = $classifyId;

                $addData['sku'] = $_item['sku'];
                $addData['num'] = $outNum;
                $addData['unit'] = $unitMap[$_item['unit']];
                $addData['tax_rate'] = $_item['tax_rate'] * 100;

                $addData['last_operator'] = $addData['creator'] = "系统";
                $addData['updatetime'] = $addData['createtime'] = \helper::nowTime();
                $detail_translate=OilReceiptDetailsTranslate::add($addData);
                //销项票翻译同出库解耦 翻译完成后不调用库存操作
                $stock = [];
                $stock['oil_type_id'] = $classifyId;
                $stock['classify'] = OilType::STOCK_CLASSIFY_OUT;
                $stock['res_id'] = $detail_translate->id;
                $stock['res_type'] = $receiptInfo->color_type == 10 ? 20 : 10;
                $res = (new InvoiceStockService())->insertStock(true,$stock,$is_throw);
                if($res['code'] == 0){
                    //出库成功
                    OilReceiptDetailsTranslate::edit([
                        'id'=>$detail_translate->id,
                        'out_status'=>ReceiptDetailTranslate::OUT_STATUS_SUCCESS,
                    ]);
                }elseif($res['code'] == 1){
                    //无需出库
                    OilReceiptDetailsTranslate::edit([
                        'id'=>$detail_translate->id,
                        'out_status'=>ReceiptDetailTranslate::OUT_STATUS_NO_NEED,
                    ]);
                }else{
                    //出库失败
                    OilReceiptDetailsTranslate::edit([
                        'id'=>$detail_translate->id,
                        'out_reason'=>"出库失败:".$res['msg'],
                        'out_status'=>ReceiptDetailTranslate::OUT_STATUS_FAIL,
                    ]);
                    $errorMsg[$_item['id']][] = "出库失败:".$res['msg'];
                }
            }

            if($is_throw && count($errorMsg) > 0) {
                $error_msg = '';
                Log::error('出库失败：', [$error_msg], "invoiceLog_");
                foreach ($errorMsg as $v){
                    $error_msg+=implode(',',$v);
                }
                throw new \RuntimeException("出库失败，详情查看发票明细".$error_msg, 2);
            }else{
                //出库状态
                OilReceiptManage::where('id',$manage_id)->update([
                    'out_status' => 3,
                    'updatetime' => date("Y-m-d H:i:s")
                ]);
            }
            Capsule::connection()->commit();
            Log::error('销项票明细成功', var_export($this->params,true), 'invoiceLog_');
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            Log::error('异常：' . __METHOD__, [strval($e)], "invoiceLog_");
            /*$content[] = "* 项目：异步创建上游充值申请单";
            $content[] = "* 错误信息：" . $e->getMessage();
            (new DingTalkAlarm())->alarmToGroup('销项票明细', implode("\n\n", $content), [],true, false);*/
            $this->updateProcess(['remark' => '执行失败，' . $e->getMessage(), 'status' => -20, 'rate' => 100]);
            OilReceiptManage::where('id',$manage_id)->update([
                'out_status' => 1,//出库失败
                'updatetime' => date("Y-m-d H:i:s")
            ]);
            throw new \RuntimeException('执行失败-' . $e->getMessage(), 2);
        }

        /*if(count($errorMsg) > 0) {
            foreach ($errorMsg as $_id => $_val) {
                $msg = implode(",", $_val);
                OilReceiptDetails::edit(['id' => $_id, "remark" => $msg]);
            }
        }*/

        $this->updateProcess(['remark' => '销项票明细和出库成功', 'status' => 20, 'rate' => 100]);
    }

    /**
     * 更新oilDownLoad进度
     * @param array $params
     */
    public function updateProcess(array $params)
    {
        OilDownload::updateByJobsId([
            'jobs_id'  => $this->jobs->jobId,
            'filename' => $params['remark'],
            'status'   => $params['status'],
            'rate'     => $params['rate'],
        ]);
    }
}