<?php
/**
 * 付款中心导出
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/4/5
 * Time: 上午10:18
 */

namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Helper;
use Framework\Log;
use Framework\Queue;
use Models\OilFossPaymentRecords;

class ExportPayRecordsDetailsJob extends Queue
{
    protected $pageSize = 1000;

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        $params   = $this->params;
        $taskInfo = $this->jobs;

        try {
            unset($params['_export']);
            $params['_count'] = 1;
            $total            = OilFossPaymentRecords::getRecordsDetail($params);

            Log::error('total:' . $total, [$params], 'exportpayRecord');

            if (!$total) {
                \Models\OilDownload::updateByJobsId([
                    'jobs_id'  => $taskInfo->jobId,
                    'status'   => -20,
                    'filename' => '没有可以导出的数据',
                    'rate'     => 100,
                ]);
            } else {
                unset($params['_count'], $params['_export']);

                \Models\OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 1,
                ]);

                $totalPage = ceil($total / $this->pageSize);

                Log::error('totalPage:' . $totalPage, [$params], 'exportpayRecord');

                $_tmpData = [];
                for ($i = 0; $i < $totalPage; $i++) {
                    $params['skip'] = $i * $this->pageSize;
                    $params['take'] = $this->pageSize;

                    $record = OilFossPaymentRecords::getRecordsDetail($params);

                    $_tmpData = array_merge($_tmpData, $record->toArray());

                    unset($record);
                }

                \Models\OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 30,
                ]);

                if ($_tmpData) {
                    $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
                    $fileName = 'payRecordDetail_' . date("Ymd_H_i_s") . rand(100, 999);
                    $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'oil_payRecord' . DIRECTORY_SEPARATOR;
                    if (!is_dir($filePath)) {//创建目录
                        mkdir($filePath, 0777);
                    }

                    //导出数据
                    $_data = array_chunk($_tmpData, 10000);
                    foreach ($_data as $k => $v) {
                        $filesArr[] = $this->exportJob($v, ($k + 1));
                        \Models\OilDownload::updateByJobsId([
                            'jobs_id' => $taskInfo->jobId,
                            'status'  => 10,
                            'rate'    => 29 + (($k + 1) / count($_data)) * 70,
                        ]);
                    }
                    unset($_data);

                    $zipFile = $fileName . '.zip';
                    $zip     = new \ZipArchive();
                    if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
                        exit("can't open " . $filePath . $zipFile . " zipFile!");
                    }
                    for ($i = 0; $i < count($filesArr); $i++) {
                        $zip->addFile($filesArr[$i], basename($filesArr[$i]));
                    }
                    $zip->close();


                    $filePath = $filePath . $zipFile;

                    $url = (new \commonModel())->fileUploadToOss($filePath);
                    Log::error('bbbb导出序号-$url:' . $url, [], 'exportpayRecord');

                    \Models\OilDownload::updateByJobsId([
                        'jobs_id'  => $taskInfo->jobId,
                        'status'   => 20,
                        'filename' => $zipFile,
                        'filetype' => \helper::getFileType($filePath),
                        'filesize' => round(filesize($filePath) / 1024, 2),
                        'url'      => $url,
                        'rate'     => 100,
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Models\OilDownload::updateByJobsId([
                'jobs_id'  => $taskInfo->jobId,
                'status'   => -20,
                'filename' => '导出失败',
                'rate'     => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], 'exportpayRecord');
        }


    }

    public function exportJob($result, $page)
    {
        if ($result) {

            foreach ($result as &$val) {
                $val = (object)$val;
                foreach ($val as &$item) {
                    $item = Helper::trimAll($item);
                }
                $val->out_trans_id          = $val->out_trans_id . "\t";
                $val->channel_info          = $val->channel_info . "\t";
                $val->recharge_amount       = $val->recharge_amount . "\t";
                $val->service_area_info     = $val->service_area_info . "\t";
                $val->createtime            = $val->createtime . "\t";
                $val->pay_time              = $val->pay_time . "\t";
                $val->refund_time           = $val->refund_time . "\t";
                $val->updatetime            = $val->updatetime . "\t";
                $val->account               = $val->account . "\t";
                $val->bank_account_no       = $val->bank_account_no . "\t";
                $val->platform_bank_account_no = $val->platform_bank_account_no . "\t";
                $val->pay_name = $val->pay_name."\t";
            }
            $extType  = "csv";
            $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;

            $exportData = [
                'filePath'  => $realPath . 'data' . DIRECTORY_SEPARATOR . 'oil_payRecord',
                'fileName'  => '付款中心导出明细_' . date("YmdHis") . rand(100, 999) . '_' . $page,
                'fileExt'   => $extType,
                'download'  => 1,
                'sheetName' => '付款中心',
                'title' => [
                    'no'   =>  '单号',
                    'out_trans_id'   =>  '交易流水号',
                    'supplier_name'   =>  '供应商',
                    'supplier_id'   =>  '供应商ID',
                    'service_area_info'   =>  '服务区(油站)/主卡号',
                    'recharge_type_txt'   =>  '充值方式',
                    'recharge_amount'   =>  '充值金额(元)',
                    'status_txt'   =>  '状态',
                    'cooperation_type_txt'   =>  '合作类型',
                    'channel_info'   =>  '渠道信息',
                    // 'account'   =>  '账号',
                    'bank_summary'   =>  '摘要',
                    // 'foss_remark'   =>  '内部反馈',
                    // 'GEC_remark'   =>  'GEC反馈',
                    // 'remark'   =>  '备注',
                    'company_name'   =>  '收款公司',
                    'bank_account_no'   =>  '收款账号',
                    'bank_name'   =>  '开户行',
                    // 'receipt_title'   =>  '回票抬头',
                    'all_operator_name'   =>  '签约运营商',
                    'platform_company_name'   =>  '付款公司',
                    'platform_bank_account_no' =>  '付款账号',
                    'settlement_docker'   =>  '运营对接人',
                    // 'network_docker'   =>  '网络对接人',
                    'createtime'   =>  '创建时间',
                    'pay_time'   =>  '支付时间',
                    // 'refund_time'   =>  '退款时间',
                    'creator_name'   =>  '创建人',
                    'audit_operator'   =>  '审核人',
                    'updatetime'   =>  '最后修改时间',
                    // 'last_operator'   =>  '最后修改人',
                    'pay_name' => '核算主体',
                    'settle_obj_id' => '核算主体ID',
                    'd_amount' => '付款金额',
                    'd_id' => '明细ID',
                    'd_remark' => '备注',
                ],
                'data' => $result,
            ];

            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                if (in_array($data['name'], ['out_trans_id'])) {
                    $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            });

            Log::error('bbbb导出序号-$fileUrl:' . var_export($fileUrl, TRUE), [], 'exportpayRecord');
            return $fileUrl;
        }
    }
}