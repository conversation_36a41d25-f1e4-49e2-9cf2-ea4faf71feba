<?php


namespace Jobs;

use Framework\Queue;
use Fuel\Defines\ReceiptReturn;
use Fuel\Service\ReceiptTranslate\EngineFactory;
use Fuel\Defines\ReceiptTranslateDetail;
use Models\OilDownload;
use Models\OilReceiptReturn;
use Models\OilReceiptTranslateDetail;
use Framework\Excel\ExcelWriter;
use Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Service\InvoiceStockService;
class ReceiptManualTranslate extends Queue
{
    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {

        $ids   = $this->params;
        $notTranslate=[];
        $receipts=OilReceiptTranslateDetail::getManualTranslateList(['id'=>$ids]);
        Log::error('手动翻译调试开始:', [], 'receiptAutoTranslate');
        if(!empty($receipts)){
            \Models\OilDownload::updateByJobsId([
                'jobs_id' => $this->jobs->jobId,
                'status'  => 10,
                'rate'    => 1,
            ]);
            Log::error('开始循环处理:', [], 'receiptAutoTranslate');
            try {
                foreach ($receipts as $k=>$item){
                    //判断是否能进行手段翻译
                    if((($item['translate_status'] == ReceiptTranslateDetail::TRANSLATE_STATUS_NOT || $item['translate_status'] == ReceiptTranslateDetail::TRANSLATE_STATUS_PART) && $item['is_return'] == ReceiptTranslateDetail::IS_RETURN_NO_NEED && $item['progress'] == ReceiptTranslateDetail::PROGRESS_SKU && $item['in_status'] != ReceiptTranslateDetail::IN_STATUS_REVOKE) ||
                        (($item['translate_status'] == ReceiptTranslateDetail::TRANSLATE_STATUS_NOT || $item['translate_status'] == ReceiptTranslateDetail::TRANSLATE_STATUS_PART) && ( ($item['is_return'] == ReceiptTranslateDetail::IS_RETURN_NEED) && $item['progress'] == ReceiptTranslateDetail::PROGRESS_RECEIPT)) && $item['in_status'] != ReceiptTranslateDetail::IN_STATUS_REVOKE){
                        //属于【未翻译】或【部分翻译】，且状态为【无需退票】 从sku翻译阶段翻译，进入后续的翻译流程
                        $skuTranslateEngine= EngineFactory::createEngine('sku_translate');
                        $res_skuTranslateEngine=$skuTranslateEngine->handlerEdit($item);
                        if($res_skuTranslateEngine['code'] == 1){
                            $receipts[$k]['in_second_oil_id']=$res_skuTranslateEngine['info']['in_second_oil_id'];
                            $receipts[$k]['in_sku']=$res_skuTranslateEngine['info']['in_sku'];
                            //判断是否进入单位翻译 折扣行不进行单位翻译
                            //继续进入单位翻译
                            if($item['money']<0 && $item['unit'] == ''){
                                //折扣行 直接进入税率翻译
                                //进入税率翻译
                                $taxTranslateEngine= EngineFactory::createEngine('tax_translate');
                                $res_taxTranslateEngine=$taxTranslateEngine->handlerEdit($receipts[$k]);
                                if($res_taxTranslateEngine['code'] != 1){
                                    //从列表中剔除 并加入未翻译完成的集合
                                    $notTranslate[]=$item['id'];
                                    unset($receipts[$k]);
                                }
                            }else{
                                $unitTranslateEngine= EngineFactory::createEngine('unit_translate');
                                $res_unitTranslateEngine=$unitTranslateEngine->handlerEdit($receipts[$k]);
                                if($res_unitTranslateEngine['in_unit'] != ''){
                                    $receipts[$k]['in_unit']=$res_unitTranslateEngine['info']['in_unit'];
                                    $receipts[$k]['in_num']=$res_unitTranslateEngine['info']['in_num'];
                                    //进入税率翻译
                                    $taxTranslateEngine= EngineFactory::createEngine('tax_translate');
                                    $res_taxTranslateEngine=$taxTranslateEngine->handlerEdit($receipts[$k]);
                                    if($res_taxTranslateEngine['code'] != 1){
                                        //从列表中剔除 并加入未翻译完成的集合
                                        $notTranslate[]=$item['id'];
                                        unset($receipts[$k]);
                                    }
                                }else{
                                    //从列表中剔除 并加入未翻译完成的集合
                                    $notTranslate[]=$item['id'];
                                    unset($receipts[$k]);
                                }
                            }
                        }else{
                            //从列表中剔除 并加入未翻译完成的集合
                            $notTranslate[]=$item['id'];
                            unset($receipts[$k]);
                        }
                    }else{
                        unset($receipts[$k]);
                    }
                }
            }catch (\Exception $e){
                Log::error('异常信息:', [$e->getMessage()], 'receiptAutoTranslate');
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }
            Log::error('翻译完成的:', [$receipts], 'receiptAutoTranslate');
            //翻译完成的 判断回票状态  内部票已签收的调用库存操作     外部票已审核的调用库存操作
            foreach ($receipts as $receipt){
                $receipt_return=OilReceiptReturn::getById(['id'=>$receipt['receipt_return_id']]);
                if(($receipt_return->is_inner ==  ReceiptReturn::IS_INNER_YES && $receipt_return->progress == ReceiptReturn::PROGRESS_SIGNED
                    && $receipt['in_status'] == ReceiptTranslateDetail::IN_STATUS_NOT) || ($receipt_return->is_inner ==  ReceiptReturn::IS_INNER_NO && $receipt_return->progress == ReceiptReturn::PROGRESS_AUDIT
                        && $receipt['in_status'] == ReceiptTranslateDetail::IN_STATUS_NOT)){
                    //内部票已签收或 外部票已审核 且 未入库
                    $result=(new  InvoiceStockService)->insertStock(true,[
                        'oil_type_id'  => $receipt['in_second_oil_id'],
                        'classify'     => 10,
                        'res_id'       => $receipt['id'],
                        'res_type'     => 10,
                    ],false);
                    //获取入库状态
                    if($result['code'] == 0){
                        //入库成功
                        OilReceiptTranslateDetail::edit([
                            'id'                => $receipt['id'],
                            'in_mode'           => ReceiptTranslateDetail::IN_MODE_AUTO,
                            'in_status'         => ReceiptTranslateDetail::IN_STATUS_OVER,
                        ]);
                    }elseif($result['code'] == 1){
                        //无需入库
                        OilReceiptTranslateDetail::edit([
                            'id'                => $receipt['id'],
                            'in_status'         => ReceiptTranslateDetail::IN_STATUS_NO_NEED,
                        ]);
                    }else{
                        OilReceiptTranslateDetail::edit([
                            'id'                => $receipt['id'],
                            'in_status'         => ReceiptTranslateDetail::IN_STATUS_FAIL,
                            'stock_reason'      => '入库失败:'.substr($result['msg'],0,200),
                        ]);
                    }
                }

            }
            $notTranslate=array_unique($notTranslate);
            //翻译失败的导入excel
            Log::error('翻译失败的:', [$notTranslate], 'receiptAutoTranslate');
            if(!empty($notTranslate)){
                \Models\OilDownload::updateByJobsId([
                    'jobs_id' => $this->jobs->jobId,
                    'status'  => 10,
                    'rate'    => 80,
                ]);
                $notReceipts=OilReceiptTranslateDetail::getExportList($notTranslate);
                Log::error('导出的数据:', [$notReceipts], 'receiptAutoTranslate');
                try {
                    $filePath=$this->writeExcel(array_values($notReceipts),1);
                    $url = (new \commonModel())->fileUploadToOss($filePath);
                }catch (\Exception $e){
                    Log::error('生成文件异常信息:', [$e->getMessage()], 'receiptAutoTranslate');
                    throw new \RuntimeException($e->getMessage(), $e->getCode());
                }
                Log::error('filepath&url:', [$filePath,$url], 'receiptAutoTranslate');
                \Models\OilDownload::updateByJobsId([
                    'jobs_id'  => $this->jobs->jobId,
                    'status'   => 20,
                    'filename' => '进项票自动翻译未翻译明细' . date("YmdHis") .rand(100,999). "_1",
                    'filetype' => \helper::getFileType($filePath),
                    'filesize' => round(filesize($filePath) / 1024, 2),
                    'url'      => $url,
                    'rate'     => 100,
                ]);
            }else{
                \Models\OilDownload::updateByJobsId([
                    'jobs_id'  => $this->jobs->jobId,
                    'status'   => 20,
                    'filename' => '全部翻译完成,没有可以导出的数据',
                    'rate'     => 100,
                    'message'     => '翻译结束',
                ]);
            }
        }else{
            \Models\OilDownload::updateByJobsId([
                'jobs_id'  => $this->jobs->jobId,
                'status'   => 20,
                'filename' => '没有可以导出的数据',
                'rate'     => 100,
                'message'     => '没有可以翻译的数据',
            ]);
        }
    }

    public function writeExcel(array $result,$page){
        if ($result) {
            $extType  = count($result) >= 20000 ? 'xls' : 'xls';
            $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
            $fileName = 'receiptManualTranslate__' . date("Ymd_H_i_s") . rand(100, 999);
            $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'receiptAutoTranslate' . DIRECTORY_SEPARATOR;
            if (!is_dir($filePath)) {//创建目录
                mkdir($filePath, 0777);
            }
            $exportData = [
                'filePath'  => $filePath,
                'fileName'  => $fileName,
                'sheetName' => '',
                'fileExt'   => $extType,
                'download'  => 1,
                'title'     => [
                    'receipt_return_detail_id'   =>  '进项票ID',
                    'receipt_no'   =>  '发票号码',
                    'receipt_code'   =>  '发票代码',
                    'name'   =>  '货物或应税劳务、服务名称',
                    'sku'   =>  '规格型号',
                    'unit'   =>  '单位',
                    'num'   =>  '数量',
                    'tax_rate'   =>  '税率',
                    'in_sku'   =>  '入库规格型号',
                    'in_second_oil'   =>  '入库二级油品',
                    'select_status_val'   =>  '2级油品选择状态',
                    'in_num'   =>  '入库数量',
                    'in_unit_val'   =>  '入库单位',
                    'translate_status_val'   =>  '翻译状态',
                    'is_return_val'   =>  '是否需要退票',
                    'in_status_val'   =>  '入库状态',
                    'return_reason'   =>  '退票原因',
                    'creator'   =>  '创建人',
                    'last_operator'   =>  '最后操作人',
                    'createtime'   =>  '创建时间',
                    'updatetime'   =>  '最后操作时间'
                ],
                'data'      => $result,
            ];

            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            });

            Log::error('导出序号-$fileUrl:' . var_export($fileUrl, TRUE), [], 'receiptAutoTranslate');
            return $fileUrl;
        }
    }
    /**
     * 更新oilDownLoad进度
     * @param array $params
     */
    public function updateProcess(array $params)
    {
        OilDownload::updateByJobsId([
            'jobs_id'  => $this->jobs->jobId,
            'filename' => $params['remark'],
            'status'   => $params['status'],
            'rate'     => $params['rate'],
        ]);
    }
}