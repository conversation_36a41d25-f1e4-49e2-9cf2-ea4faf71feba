<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/3/25
 * Time: 下午8:24
 */

namespace Jobs;

use Framework\Log;
use Framework\Queue;
use Nette\Mail\Message;
use Nette\Mail\SmtpMailer;


class SendAlarmEmailJob extends Queue
{
    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    public function handle()
    {
        $params = $this->params;
        try {
            global $app;

            $mail = new Message;
            if (is_array($params['mailList']) && $params['mailList']) {
                foreach ($params['mailList'] as $v) {
                    $mail->addTo($v);
                }
            }

            $mail->setSubject($params['title']);

            if ($params['isHtml']) {
                $mail->setHTMLBody($params['content']);
            } else {
                $mail->setBody($params['content']);
            }


            $mail->setFrom($app->config->email->from, $app->config->email->fromName);

            $mailer = new SmtpMailer(array(
                'host'     => $app->config->email->host,
                'username' => $app->config->email->from,
                'password' => $app->config->email->password,
                'port'     => $app->config->email->Port,
                'secure'   => $app->config->email->SMTPSecure,
            ));

            try {
                $mailer->send($mail);
            } catch (\Exception $e) {
                Log::error('Framework\Mailer ', [strval($e)], 'mail_Error');
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Framework\Mailer', [strval($e)], 'mail_Error');
        }
    }
}