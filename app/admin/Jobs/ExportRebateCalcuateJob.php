<?php
namespace Jobs;


use Framework\Excel\ExcelWriter;
use Framework\Log;
use Framework\Queue;
use Fuel\Defines\RebatePolicy;
use Fuel\Service\OrgConfigService;
use Models\OilDownload;
use Models\OilOrgConfig;
use Models\OilRebateCalculate;

class ExportRebateCalcuateJob extends Queue
{
    private $title = '返利工单（实体卡）';
    protected $pageSize = 1000;

    protected $logChannel = "exportRebateCalcuate";
    protected $xlxFile = "oil_rebate_calcuate";

    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }
    
    public function handle()
    {
        Log::error($this->title, ['jobs' => $this->jobs], $this->logChannel);

        $params   = $this->params;
        $taskInfo = $this->jobs;


        if(!isset($params['policy_type']) || empty($params['policy_type'])){
            $params['policy_type'] = \Fuel\Defines\RebatePolicy::POLICY_TYPE_ENTITY;
        }

        $params['is_del'] = 1;
        $params['statusNotIn'] = 50;

        try {
            unset($params['_export']);
            $params['count'] = 1;
            $total            = OilRebateCalculate::getTotal($params);

            Log::error('total:' . $total, [$params], $this->logChannel);

            if (!$total) {
                \Models\OilDownload::updateByJobsId([
                    'jobs_id'  => $taskInfo->jobId,
                    'status'   => -20,
                    'filename' => '没有可以导出的数据',
                    'rate'     => 100,
                ]);
            } else {
                unset($params['count'], $params['_export']);

                \Models\OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 1,
                ]);

                $totalPage = ceil($total / $this->pageSize);

                Log::error('totalPage:' . $totalPage, [$params], $this->logChannel);

                $_tmpData = [];
                for ($i = 0; $i < $totalPage; $i++) {
                    $params['skip'] = $i * $this->pageSize;
                    $params['take'] = $this->pageSize;

                    $record = OilRebateCalculate::getList($params);
                    $_tmpData = array_merge($_tmpData, $record->toArray());
                    unset($record);
                }

                \Models\OilDownload::updateByJobsId([
                    'jobs_id' => $taskInfo->jobId,
                    'status'  => 10,
                    'rate'    => 30,
                ]);

                if ($_tmpData) {
                    $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR ;
                    $fileName = $this->xlxFile.'_' . date("Ymd_H_i_s") . rand(100, 999);
                    $filePath = $realPath . 'data' . DIRECTORY_SEPARATOR . $this->xlxFile . DIRECTORY_SEPARATOR;

                    if (!is_dir($filePath)) {//创建目录
                        mkdir($filePath, 0777);
                    }

                    //导出数据
                    $_data = array_chunk($_tmpData, 10000);
                    foreach ($_data as $k => $v) {
                        $fileTmp = $this->exportJob($v, ($k + 1));
                        Log::error('bbbb导出表格地址:' . $fileTmp, [], $this->logChannel);
                        if (empty($fileTmp))
                        {
                            throw new \RuntimeException("读取文件为空",2);
                        }
                        $filesArr[] = $fileTmp;
                        \Models\OilDownload::updateByJobsId([
                            'jobs_id' => $taskInfo->jobId,
                            'status'  => 10,
                            'rate'    => 29 + (($k + 1) / count($_data)) * 70,
                        ]);
                    }
                    unset($_data);

                    $zipFile = $fileName . '.zip';
                    $zip     = new \ZipArchive();
                    if ($zip->open($filePath . $zipFile, \ZIPARCHIVE::CREATE) !== TRUE) {
                        exit("can't open " . $filePath . $zipFile . " zipFile!");
                    }
                    for ($i = 0; $i < count($filesArr); $i++) {
                        $zip->addFile($filesArr[$i], basename($filesArr[$i]));
                    }
                    $zip->close();


                    $filePath = $filePath . $zipFile;
                    $url = (new \commonModel())->fileUploadToOss($filePath);
                    Log::error('bbbb导出序号-$url:' . $url, [], $this->logChannel);

                    \Models\OilDownload::updateByJobsId([
                        'jobs_id'  => $taskInfo->jobId,
                        'status'   => 20,
                        'filename' => $zipFile,
                        'filetype' => \helper::getFileType($filePath),
                        'filesize' => round(filesize($filePath) / 1024, 2),
                        'url'      => $url,
                        'rate'     => 100,
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Models\OilDownload::updateByJobsId([
                'jobs_id'  => $taskInfo->jobId,
                'status'   => -20,
                'filename' => '导出失败',
                'rate'     => 100,
            ]);
            Log::error('导出序号-$e:' . strval($e), [], $this->logChannel);
        }
    }
    
    public function exportJob($data, $page)
    {
        /**
         * 整理计算 返利值 数据
         */
        foreach ($data as $key=>$item)
        {
            $data[$key]['card_type_value'] = !empty(RebatePolicy::$cardType[$item['card_type']]) ? RebatePolicy::$cardType[$item['card_type']] : "";

            if($item['cal_method'] == 30) {
                $unit = '升';
            }else {
                $unit = '元';
            }

            if ($item['cal_method'] == 10)
            {
                $oUnit = "元/升";
            }else
            {
                $oUnit = "%";
            }
            /**
             * 返利值 数据重组
             */
            if (in_array($item['cal_method'],[30, 40, 50, 60]))
            {
                $fanli_val = \GuzzleHttp\json_decode($item['fanli_val'],true);
                $rebate_content_value = [];
                foreach ($fanli_val as $k=>$it)
                {
                    //返利百分比 区间计算
                    $dis_data = $it['discount'];
                    if (!empty($it['fanli_modelsTmpls']))
                    {
                        if (is_array($it['fanli_modelsTmpls']))
                        {
                            $jsonContent = $it['fanli_modelsTmpls'][0]['content'];
                        }
                        else
                        {
                            $jsonContent = $it['fanli_modelsTmpls']['content'];
                        }
                        $arrContent = \GuzzleHttp\json_decode($jsonContent,true);
                        $valueArray = array_column($arrContent,"value");
                        sort($valueArray);

                        $dis_data = $valueArray[0] . " - ".$valueArray[count($valueArray)-1];
                    }

                    $rebate_content_value[] = $it['start_value'].$unit." ~ ".$it['end_value'].$unit ." ".$dis_data.$oUnit;
                }

                $data[$key]['rebate_content'] = implode(" ",$rebate_content_value);
                if (in_array($item['ca_method'],[50, 60]))
                {
                    $data[$key]['rebate_content'] = $data[$key]['rebate_content']."，最低消费额：".$item['min_amount']."元";
                }
            }else
            {
                $data[$key]['rebate_content'] = $item['fanli_val'] . $oUnit;
            }
        }

        $extType  = count($data) >= 20000 ? 'xls' : 'xls';
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $exportData = [
            'filePath'  => $realPath . 'data' . DIRECTORY_SEPARATOR . $this->xlxFile ,
            'fileName'  => $this->xlxFile.'_'.date("YmdHis"). rand(100, 999) . '_'.$page,
            'sheetName' => $this->title,
            'fileExt'   => $extType,
            'download'  => 1,
            'title'     => [
                'no'                => '返利工单号',
                'policy_id'               => '使用规则ID',
                'policy_name'              => '规则名称',
                'card_type_value'     => '卡类型',
                'policy_level'               => '规则等级',
                'cal_object_txt'          => '计算对象',
                'total_money'           => '计算对象汇总值',
                'total_nums'          => '计算机构数量',
                'status_txt'            => '审核结果',
                'rebate_type_txt'            => '返利形式',
                'method_txt'               => '计算方法',
                'unit_txt'         => '返利单位',
                'rebate_content'         => '返利值',
                'start_time'         => '起始时间',
                'end_time'         => '终止时间',
                'last_operator'         => '最后操作人',
                'updatetime'         => '最后操作时间',
            ],
            'data'      => $data,
        ];

        try {
            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            });

            Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], $this->logChannel);
            return $fileUrl;
        } catch (\Exception $exception) {
            Log::error(__METHOD__ . "#导出异常：", [strval($exception)], $this->logChannel);
        }
    }

    public function updateProcess($params)
    {
        $download = OilDownload::updateByJobsId([
            'jobs_id'  => $this->jobs->jobId,
            'filename' => $params['remark'],
            'status'   => $params['status'],
            'rate'     => $params['rate'],
        ]);
    }
}