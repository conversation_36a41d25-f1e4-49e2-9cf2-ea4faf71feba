<?php
/**
 * 公共控制类
 *
 */
set_time_limit(0);

class common extends baseControl
{

    private $cookiepre;
    private $appkey; //接口专用
    private $_uid = 0; //接口专用
    private $api_users; //接口专用

    public function __construct()
    {
        global $config;
        parent::__construct();

        $params = helper::filterParams();
        if (isset($params['app_key']) && $params['app_key']) {  //如果请求接口，走接口路线
            $this->runApi($params);
        } else {
            $this->cookiepre = $config->cookiepre;
            // session_start();	//Story #42524 by lixiaoli
            $this->setUserinfo();
            $this->checkLogin();
        }
    }

    /**
     * 访问白名单
     * @return array
     */
    private function whiteList()
    {
        return [
            'login.generatecaptcha',
            'login.getNotice',
            'login.login',
            'login.logout',
            'login.getNotice',
            'login.getconfig',
            'common.getsconfig',
            'common.getusermenus',//为新版运营后台提供获取菜单接口
            'common.editSource',
            'generators.makemodel',
            'oil_account_assign.checkAssign',
            'oil_card_main.my_open',
            'oil_jobs.execQueueTask',
            'oil_card_vice.updateTradeTime',
            'oil_card_vice_trades.updateViceTradeTime',
            'login.test',
            'login.getconfig',
            'login.getsconfig',
            'common.gspErrorsLog',//Story #42390 系统访问异常监控
            'oil_station.getOilRecord',//Story #49742 by wwy 调用 加油记录 接口，取回交易记录数据存入交易记录表中
            'oil_station.addOilExtraData',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'oil_card_vice.balanceRefreshTask',//Story #51726 定时任务--副卡余额刷新
            'oil_card_main.balanceRefreshTask',//Story #51725 定时任务--主卡余额刷新
            'oil_report_card_main.add',//Story #52038 总账查询--主卡每日数据生成（自动任务）
            'cron.syncBalanceRefreshTask',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'cron.syncMainBalanceRefreshTask',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'cron.syncGetOilRecord',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'cron.syncAddOilExtraData',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'cron.syncAccount',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'cron.synOilStation',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'cron.synSkidMountedPlatformViceTrades',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'cron.syncTimeOutChangeClose',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'cron.syncTimeOutAction',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'oil_account_assign.checkAssign',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'oil_card_main.my_open',//Story #49742 by wwy 取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
            'oil_station.getMainCardRecord',//Story #51721 by wwy 主卡充值记录同步
            'oil_account_charge_wechat.getOpenid',
            'oil_card_main_charge.syncChargeDetail',//主卡充值记录同步(增量方式)
            'oil_pre_assign.syncAssignOrderData',//分配订单同步(增量方式)
            'oil_vice_assign.syncAssignData',//副卡分配记录同步(增量方式)
            'oil_org_month_statistic.syncOrgOilData',//统计消费记录到月统计表中
            'oil_org_month_statistic.getAllOrgInfo',//统计消费记录到月统计表中
            'oil_org_month_statistic.syncOrgOilDataByOrgId',//统计消费记录到月统计表中
            'oil_org_month_statistic.generateAttachment',//生成月统计附件
            'oil_org.exportByMonty',//生成月统计附件
            'gsp_fetch.test',//生成月统计附件
            'oil_card_vice_trades.syncNewGasRecord',//增量同步新产生的加油记录
            'oil_card_vice_trades.syncUpdatedGasRecord',//增量修改已更新的加油记录
            'gsp_fetch.receiptSplit2',
            'oil_trades_out.tradesOutTask',//可疑交易记录计算计划任务
            'oil_station.locateStation',
            'oil_station.cron',
            'oil_account_assign.deleteTimeOutEWeiWorkOrder',//删除易维超时工单
            'oil_org.getExclusiveCustom',//查询专属客服
            'oil_card_vice.batchAddToGos',//批量添加副卡到GOS
            'oil_vice_tags.batchAddToGos',//批量添加卡标签到GOS
            'oil_tags.batchAddToGos',//批量添加标签到GOS
            'oil_account_assign.batchAddToGos',//批量添加分配单到GOS
            'oil_account_money_transfer.batchAddToGos',//批量添加转账申请-资金到GOS
            'oil_card_vice_app.batchAddToGos',//开卡申请单到GOS
            'oil_card_register.batchAddToGos',//用卡登记到GOS
            'oil_card_vice_trades.updateTradeProvince',//回填消费所属省份
            'oil_card_vice.getCardStatusList',//获取卡状态清单
            'oil_station_supplier.testGetInvoiceHead',//航信测试
            'oil_rebate_formula.getList',//返利公式列表
            'oil_rebate_formula.create',//返利公式添加
            'oil_rebate_formula.edit',//返利公式添加
            'oil_rebate_formula.remove',//返利公式添加
            'user.updatePwd',//更改密码
            'user.getInnerCompany',//内部公司列表
            'oil_card_main.getOilComWidth',
            'oil_card_main.getOilComList',//
            'oil_pay_config.getPayType',//支付方式sug
            'oil_station_area.getOilStationAreas',
            'oil_collect_company.searchList',//收款公司sug
            'oil_station_operators.oilOperatorSug',//运营商sug
            'oil_card_main.updateMainNo',
        ];
    }

    /**
     * 权限检测
     */
    public function validate()
    {
        $router = $this->getMF();
        helper::argumentCheck(['m', 'f'], $router);
        $currentRequest = strtolower($router['m'] . '.' . $router['f']);

        $params = helper::filterParams();
        $logParams = [];
        if( !empty($this->app->myAdmin->id) ) {
            $third_id = 0;
            if(isset($params['id']) && $params['id']){
                $third_id = $params['id'];
            }
            if(isset($params['ids']) && $params['ids']){
                $third_id = $params['ids'];
            }
            //todo 映射操作功能的中文名称（需缓存）
            $logParams['system_name'] = 'Foss';
            $logParams['log_time'] = helper::nowTime();
            $logParams['module_name'] = $router['m'];
            $logParams['method_name'] = $router['f'];
            $logParams['third_id'] = intval($third_id);
            $logParams['request_params'] = json_encode($params, JSON_UNESCAPED_UNICODE);
            $logParams['ip'] = helper::getRealIp();
            $logParams['ip2'] = helper::getIP();
            $logParams['operator_id'] = intval($this->app->myAdmin->id);
            $logParams['operator_account'] = $this->app->myAdmin->user_name;
            $logParams['operator_name'] = $this->app->myAdmin->true_name;

            //收集用户操作日志 并过滤查询类型的请求
            $pattern = '/get|search|show|count|editSource/i';
            if(API_ENV != "dev" && !preg_match($pattern, $router['f'])) {
                (new \Fuel\Service\CommonService())->createQuene("user_operatorlog","insertOperatorLog","task:user_operatorlog",$logParams);
            }
        }
        $whiteList = \helper::arrayToLower($this->whiteList());

        if (!in_array($currentRequest, $whiteList)) {
            if(!isset($this->app->myAdmin->id) || !$this->app->myAdmin){
                if (!in_array($currentRequest, $whiteList)) {
                    throw  new RuntimeException('Forbidden 无权限访问此服务', 403);
                }
            }else{
                if (isset($this->app->myAdmin->id) && $this->app->myAdmin->id && $this->app->myAdmin->id != 1) {
                    $allSourcesCacheName = __CLASS__.__METHOD__.'validate'.$this->app->myAdmin->id;
                    $allSources = \Framework\Cache::get($allSourcesCacheName);
                    if(!$allSources || 1){
                        //根据用户id找到他的roleIds，根据roleIds找出所有可用资源
                        $roleIds = [];
                        if (isset($this->app->myAdmin) && $this->app->myAdmin) {
                            $roleIds = Models\GspSysUserRoles::getRoleIdsByUid($this->app->myAdmin->id);
                        }

                        $sourcesIds = Models\GspSysRoleSource::getByRoleIds($roleIds);
                        $roleSources = Models\GspSysSource::getMethodMapByIds($sourcesIds);
                        $allSources = array_merge(\helper::arrayToLower($this->whiteList()), $roleSources);

                        \Framework\Cache::put($allSourcesCacheName, $allSources, 60*2);
                    }


                    if ($allSources) {
                        if (!in_array($currentRequest, $allSources)) {
                            throw  new RuntimeException('Forbidden 无权限访问此服务', 403);
                        }
                    } else {
                        throw  new RuntimeException('您还没有任何资源', 403);
                    }
                }
            }
        }

        return;
    }

    public function file_upload($file)
    {
        set_time_limit(0);
        if (empty($file) || empty($file['name'])) {
            //未选择文件
            return -9;
        }

        if ($file['name']) {
            $ext = strtolower(trim(substr(strrchr($file['name'], '.'), 1)));
            if ($ext != "csv") {
                //文件类型不正确
                return -8;
            }
        }

        $dir = '../tmp/data';
        //$dir = $this->app->getCacheRoot().'./data';
        if (!is_dir($dir)) {
            helper::createDir($dir, 0777);
        }

        //文件上传
        $tmp_name = $file['tmp_name'];
        $newname = $dir . '/import_' . date('m-d-H-i-s') . '.' . $ext;

        if (@copy($tmp_name, $newname)) {
            @unlink($tmp_name);
        } elseif (@move_uploaded_file($tmp_name, $newname)) {
        } elseif (@rename($tmp_name, $newname)) {
        } else {
            //上传文件失败
            return -7;
        }
        @chmod($newname, 0777);

        return $newname;
    }

    /**
     * 根据内容上传图片至OSS
     * @return mixed
     */
    public function uploadImgByContentToOSS()
    {
        $params = helper::filterParams();
        $params['content'] = substr($params['content'], strpos($params['content'], ',') + 1);
        $licenseImg = helper::createImg($params['content'], $params['type']);
        try {
            $url = $this->common->fileUploadToOss($licenseImg);
            if ($url) {
                $signUrl = \Fuel\Service\UploadService::getOssSignUrl($url);
                echo json_encode(['success' => TRUE, 'msg' => '上传完成', 'url' => $signUrl]);
            } else {
                echo json_encode(['success' => FALSE, 'msg' => '上传失败']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => FALSE, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 删除图片
     */
    public function deleteImg()
    {
        $params = helper::filterParams();
        $file = substr($params['url'], stripos($params['url'], '/fuel') + 1);
        try {
            if ($params['id']) {
                $this->common->exec("UPDATE oil_receipt_title SET $params[field]='' WHERE id=" . intval($params['id']));
            }
            $result = $this->common->file_delete_oss($file);
            if ($result) {
                echo json_encode(['success' => TRUE, 'msg' => '删除成功']);
            } else {
                echo json_encode(['success' => FALSE, 'msg' => '删除失败']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => FALSE, 'msg' => $e->getMessage()]);
        }
    }


    /**
     * 获取当前模块
     *
     * @return array
     */
    private function getMF()
    {
        $url = [];
        $string = isset($_SERVER["QUERY_STRING"]) && $_SERVER["QUERY_STRING"] ? $_SERVER["QUERY_STRING"] : $_SERVER["REQUEST_URI"];
        parse_str($string, $url);

        return $url;
    }

    private function checkLogin()
    {
        $url = $this->getMF();
        $m = $url['m'];
        $f = $url['f'];
        $whiteList = \helper::arrayToLower($this->whiteList());

        if (in_array(strtolower($m . '.' . $f), $whiteList)) {
            return;
        }

        if ($m == 'role') {
            return;
        }

        if (!isset($this->app->myAdmin) || !$this->app->myAdmin) {
            \Framework\Log::info('无权访问', $this->app->myAdmin, 'noAccess');
            $this->loadModel('login')->logout();
            \helper::throwException('无权访问', 403);
        }

    }

    private function checkResignation()
    {
        $url = $this->getMF();
        $m = $url['m'];
        $f = $url['f'];
        $whiteList = \helper::arrayToLower($this->whiteList());

        if (in_array(strtolower($m . '.' . $f), $whiteList)) {
            return;
        }
        //为了防止员工随时离职生效，但是取缓存导致登录系统，不会取缓存
        $status = $this->checkUserResign($this->app->myAdmin->user_email);
        if($status == 1){
            \Framework\Log::info('用户已离职，无权访问', $this->app->myAdmin, 'noAccess');
            $this->loadModel('login')->logout();
            \helper::throwException('用户已离职，无权访问', 403);
        }
    }

    /**
     * 获取某个项目的配置（cuiwei）
     * 左侧菜单
     * @return array
     */
    public function getsconfig()
    {
        $params = helper::filterParams();
        $data = $this->common->getsysconfig($params['id'], $params['sys']);
        $this->assign('result', $data);

        $this->display();
    }


    /**
     * 左侧菜单
     * @return array
     */
    public function getUserMenus()
    {
        $params = helper::filterParams();
        $data = $this->common->getsysconfig($params['id'], $params['sys']);
        $menus = $subMenus = [];
        //menuFlag 1:旧菜单,2:新菜单
        foreach($data as $_item){
            if( $_item->status != 1 || $_item->is_display != 1 ){
                continue;
            }
            $oneItem['source_code'] = $_item->source_code;
            $oneItem['parent_code'] = $_item->parent_code;
            $oneItem['menu_code'] = $this->splitCode($_item->source_code);
            $oneItem['label'] = $_item->source_name;
            $oneItem['meta'] = ["i18n"=>$_item->source_name];
            $oneItem['sort'] = $_item->sort;
            $oneItem['icon'] = "";
            $oneItem['menu_flag'] = 1;
            $oneItem['children'] = [];
            if(empty($_item->parent_code)){
                $oneItem['path'] = trim($_item->url,"/")."-".$_item->source_code;
                $menus[$_item->source_code] = $oneItem;
            }else{
                $oneItem['path'] = trim($_item->url,"/");
                //特殊处理四级菜单-卡务管理
                if( in_array($_item->url,["/","#","/XXX",""]) && !in_array($oneItem['label'] ,["卡务工单"]) ){
                    $menus[$_item->parent_code]['children']["sub_".$_item->source_code] = $oneItem;
                }else{
                    $subMenus['sub_' . $_item->parent_code][] = $oneItem;
                    $menus[$_item->parent_code]['children'][] = $oneItem;
                }
            }
        }
        foreach ($menus as $_fkey => &$_first){
            $newItem = [];
            foreach ($_first['children'] as $_sK => &$_sV){
                if(array_key_exists($_sK,$subMenus)){
                    $_one = $_sV;
                    $_one['children'] = $subMenus[$_sK];
                    //如再更深层次菜单，最好修改成递归方式
                    foreach ($_one['children'] as &$_third){
                        if( array_key_exists("sub_".$_third['source_code'],$subMenus) ){
                            $_third['children'] = $subMenus["sub_".$_third['source_code']];
                        }
                    }
                    $newItem[] = $_one;
                }else{
                    $newItem[] = $_sV;
                    if(!isset($_first['source_code'])){
                        unset($menus[$_fkey]);
                    }
                }
                unset($_first['children']);
                $_first['children'] = $newItem;
            }
        }
        $paixu = array_column($menus,'sort');
        array_multisort($paixu,SORT_ASC,$menus);

        \Fuel\Response::json($menus,0,"success");
    }

    private function splitCode($source_code = "")
    {
        if (strlen($source_code) == 0){
            return "";
        }
        $circle = ceil(strlen($source_code)/3);
        $str = "";
        for ($i = 0;$i < $circle;$i++){
            $start = $i * 3;
            $tmp = substr($source_code,$start,3);
            $str .= $tmp."-";
        }
        return rtrim($str,"-");
    }

    /**
     * 设置用户基本信息，权限信息，角色信息
     * <AUTHOR> li
     * @since 2013/11/7
     */
    private function setUserinfo()
    {
        $sessionId = $this->common->cookieUid();
        if (isset($_COOKIE['forget']) && $_COOKIE['forget'] == 'forever') {
            $this->app->myAdmin = \Framework\Session::get($sessionId);
        } else {
            $sessionConfig = \Framework\Config::get('session');

            if ($sessionConfig['driver'] == 'Database') {
                $this->app->myAdmin = Framework\Session::getBySessionId(session_id());
            } else {
                $this->app->myAdmin = \Framework\Session::get($sessionId);
            }
        }

        if (!isset($this->app->myAdmin) || !$this->app->myAdmin) {
            $this->app->myAdmin = new stdClass();
            $this->app->myAdmin->id = NULL;
            $this->app->myAdmin->user_name = NULL;
            $this->app->myAdmin->user_email = NULL;
            $this->app->myAdmin->org_id = NULL;
            $this->app->myAdmin->roles = [];
            $this->app->myAdmin->organ = new stdClass();
            $this->app->myAdmin->organ = (object)[
                'id'       => NULL,
                'org_code' => NULL,
                'org_name' => NULL,
            ];
        }

    }

    /**
     * 返回用户所拥有的资源
     * //Story #41941 只返回单个模块的控件列表
     * <AUTHOR>
     * @since 2013/12/19
     */
    public function editSource()
    {
        $params = helper::filterParams();
        if (!isset($this->app->myAdmin->roles)) {
            $this->app->myAdmin->roles = [];
        }

        $childSource = [];
        $currentSource = Models\GspSysSource::getSourcesByRoleIdsAndUrl($this->app->myAdmin->roles, $params['name']);
        if ($currentSource) {
            $childSource = Models\GspSysSource::getSourcesByRoleIdsAndLikeSourceCode($this->app->myAdmin->roles, $currentSource->source_code);
        }

        \Fuel\Response::json($childSource);
    }

    /**
     * 调用API
     * <AUTHOR>
     * @param type $params
     */
    public function runApi($params)
    {
        $this->api_users = [
            'oil_system' => '06398E117560D564BBDE870F6B3ADA58',
            'oil_hyr'    => '06398E117560D564BBDE870F6B3ADA58',
        ];
        $this->appkey = $params['app_key'];
        if ($this->appkey) {
            //必须存在合法的API用户，否者报错
            if (!array_key_exists($this->appkey, $this->api_users)) {
                $module = substr($params['method'], 4);
                helper::throwException('appkey在系统中不存在或未分配权限,appkey:' . $this->appkey, 403);
            } else {
                $this->checkSign();
                unset($_GET["app_key"]);
                unset($_POST["app_key"]);
                unset($_GET["timestamp"]);
                unset($_POST["timestamp"]);
                unset($_GET["sign"]);
                unset($_POST["sign"]);
            }
        }
    }

    /**
     * API调用时验证签名
     */
    private function checkSign()
    {
        $params = helper::filterParams("*");
        // 时间戳验证，20分钟有效
        $timestamp = strtotime($params['timestamp']);
        $module = substr($params['method'], 4);//Story #40084 监控报警--接口--系统参数报错时信息监控 by lixiaoli
        if (time() - $timestamp > 1200) {
            //记录接口错误日志 2013-06-26 by lizw
//			$this->saveapierror('31',"非法的时间戳参数,传的时间:".$params['timestamp'].',appkey:'.$this->appkey);
            //Story #42390
            helper::throwException("非法的时间戳参数,传的时间:" . $params['timestamp'] . ',appkey:' . $this->appkey, 31);
        }
        // 签名认证
//		$user = $this->app->user;
//		$passwd = $user->passwd;

        $sign = $params['sign'];
        unset($params["sign"]);//var_dump($params);
        $calsign = self::createSign($params, $this->api_users[$this->appkey]);
        \Framework\Log::dataLog('接口收到的签名:' . $sign . '----------签名结束', 'gsp_api_sign_');
        if ($sign != $calsign) {
            $str = "非法签名:" . $sign . var_export($params, TRUE);
            unset($params['data']);
            helper::throwException($str, 25);
        }
    }

    /**
     * 生成签名
     * @param $paramArr
     * @param $secret
     * @return string
     */
    static private function createSign($paramArr, $secret)
    {

        $sign = $secret;
        ksort($paramArr);
        foreach ($paramArr as $key => $val) {
            if ($key != '' && !is_null($val)) {
                $sign .= $key . $val;
            }
        }
        $sign .= $secret;
        $sign = strtoupper(md5($sign));

        return $sign;
    }


    /**
     * 自动任务（每1分钟执行一次）
     * 信息部下所有系统访问异常日志
     * <AUTHOR>
     * @since 2015/8/6
     */
    //Story #42390
    public function gspErrorsLog()
    {
        //所有服务报警日志短信接收人
        $commonMobile = [
            //    '13363255620',  //邵兴华
        ];

        $conf = [
            '1' => [
                'mobiles'     => [],   //接收短信的联系方式
                'url'         => 'http://gsp.huoyunren.com/',
                'description' => 'GSP系统',
            ],
            '2' => [
                'mobiles'     => [],
                'url'         => 'http://3gsp.huoyunren.com/Login/login',
                'description' => '手机工单系统',
            ],
            '3' => [
                'mobiles'     => [],
                'url'         => 'http://crm.huoyunren.com/Admin/User/login/',
                'description' => 'CRM客户关系管理系统',
            ],
            '4' => [
                'mobiles'     => [],
                'url'         => 'http://**************/HTTX_K3toGSP/tok3.asmx?wsdl',
                'description' => 'K3接口',
            ],
            //ldap
            '5' => [
                'mobiles' => [],
            ],
            '6' => [
                'mobiles'     => [],
                'url'         => 'http://oa.huoyunren.com/OA/login.aspx',
                'description' => 'OA办公系统',
            ],
        ];

        foreach ($conf as $key => $val) {
            $mobiles = '';
            $info = [];
            $mobiles = implode(',', array_filter(array_unique(array_merge($commonMobile, $val['mobiles']))));

            if ($key == 5) {
                $host = $this->app->config->ldap->host; //服务器地址
                $port = $this->app->config->ldap->port; //服务器端口
                $user = $this->app->config->ldap->user; //服务器DN
                $password = $this->app->config->ldap->password; //服务器密码

                if ($conn = ldap_connect($host, $port)) {
                    ldap_set_option($conn, LDAP_OPT_PROTOCOL_VERSION, 3); //更改版本号
                    if (!ldap_bind($conn, $user, $password)) {
                        $info = [
                            'info'    => "Can't contact LDAP server",
                            'content' => 'LDAP服务器连接失败',
                        ];
                    }
                } else {
                    $info = [
                        'info'    => 'Unable to bind to server',
                        'content' => 'LDAP服务器绑定失败',
                    ];
                }
            } else {
                $curl = curl_init($val['url']);
                $header[] = "Accept: application/json";
                $header[] = "Accept-Encoding: gzip";
                curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');

                //不取回数据
                curl_setopt($curl, CURLOPT_NOBODY, TRUE);
                //发送请求
                $result = curl_exec($curl);


                if ($result !== FALSE) {
                    //检查http响应码是否为200
                    $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                    $content = $val['description'] . '访问失败，错误码为：' . $statusCode;
                }/*else {
                    $statusCode = 0;
                    $content = $val['description'] . '访问域名有误或网页不存在';
                }*/

                if ($statusCode != 200) {
                    $info = [
                        'info'    => $statusCode,
                        'content' => $content,
                    ];
                }
            }

            if ($info) {
                $info['project'] = $key;
                $info['mobiles'] = $mobiles;
                $info['createtime'] = helper::nowTime();

                $this->common->gspErrorsLog($info);
            }
        }
    }

    /**
     * 获取资源信息
     */
    public function getSourceInfo()
    {
        $params = helper::filterParams();
        $wheresql = ' 1 AND a.type = 1 ';
        $changed = FALSE;
        if (isset($params['name']) && $params['name']) {
            $wheresql .= " AND a.source_name = '$params[name]' ";
            $changed = TRUE;
        }
        //添加url验证方式，防止修改资源名
        if (isset($params['url']) && $params['url']) {
            $wheresql .= " AND a.url = '$params[url]' ";
            $changed = TRUE;
        }
        //什么都不检索
        if (!$changed) {
            $wheresql = ' 0 ';
        }
        $data = $this->common->getSourceInfo($wheresql);
        echo json_encode(['data' => $data[0]]);
    }


    /**
     * 删除进项票电子票图片
     */
    public function deleteReceiptImg()
    {
        $params = helper::filterParams();
        $file = substr($params['url'], stripos($params['url'], '/fuel') + 1);
        try {
            if ($params['id']) {
                $this->common->exec("UPDATE oil_receipt_return SET $params[field]='' WHERE id=" . intval($params['id']));
            }
            $result = $this->common->file_delete_oss($file);
            if ($result) {
                echo json_encode(['success' => TRUE, 'msg' => '删除成功']);
            } else {
                echo json_encode(['success' => FALSE, 'msg' => '删除失败']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => FALSE, 'msg' => $e->getMessage()]);
        }
    }

}
