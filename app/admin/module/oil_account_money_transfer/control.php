<?php
/**
 *  转账申请-现金
 * <AUTHOR>
 */

use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilAccountMoneyTransfer as OilAccountMoneyTransfer;
use \Fuel\Response as Response;
use Fuel\Service\FrozenMoney as FrozenMoney;
use \Fuel\Service\AccountTransfer;
use Framework\Job;
use Framework\Excel\ExcelReader;
use \Fuel\Service\AccountMoneyTransferToGos;
use \Fuel\Service\AccountCenter\TransferService;

require_once APP_ROOT . DIRECTORY_SEPARATOR . 'library' . DIRECTORY_SEPARATOR . 'Framework' . DIRECTORY_SEPARATOR . 'Database' . DIRECTORY_SEPARATOR . 'Orm.php';

class oil_account_money_transfer extends baseControl
{
    public function __construct()
    {
        parent::__construct();
        $this->case_no = '';
    }

    /**
     * 搜索 默认搜全部
     * <AUTHOR>
     * @since 2016/02/22
     */
    public function search()
    {
        $params = helper::filterParams();

        $data = OilAccountMoneyTransfer::getList($params);

        /*if ($data) {
            foreach ($data as $k => &$v) {
                $v->true_name = $v->other_creator ? $v->other_creator : $v->true_name;
            }
        }*/

        if (isset($params['_export']) && $params['_export'] == 1) {
            if (count($data) == 0) {
                throw new \RuntimeException('没有可以导出的数据', 2);
            }
//            Capsule::connection()->beginTransaction();
            try {

                $task = (new \Jobs\ExportAccountMoneyTransferJob($params))
                    ->setTaskName('转账申请-现金')
                    ->setUserInfo($this->app->myAdmin)
                    ->onQueue('default')
                    ->dispatch();

//                $jobInfo = (new Job())
//                    ->setTaskName('oil_account_money_transfer_export')
//                    ->pushTask(function ($taskInfo, $error = FALSE) use ($data) {
//                        require_once APP_MODULE_ROOT . DIRECTORY_SEPARATOR . 'oil_account_money_transfer' . DIRECTORY_SEPARATOR . 'control.php';
//                        if ($error) {
//                            \Models\OilDownload::edit([
//                                'id'     => $taskInfo->id,
//                                'status' => '-20',
//                            ]);
//                        } else {
//                            $cardViceTradesObj = new oil_account_money_transfer();
//                            $cardViceTradesObj->getExportData(['taskInfo' => $taskInfo, 'params' => $data]);
//                        }
//                    })
//                    ->channel('export' . \Fuel\Defines\Rand::getRandExport())
//                    ->exec();
//
//                if ($jobInfo) {
//                    \Models\OilDownload::add([
//                        'jobs_id'    => $jobInfo->id,
//                        'project'    => '转账申请-现金导出',
//                        'createtime' => date("Y-m-d H:i:s"),
//                        'createuser' => $this->app->myAdmin->id,
//                    ]);
//                }
//                Capsule::connection()->commit();
            } catch (\Exception $e) {
//                Capsule::connection()->rollBack();
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }

//            Response::json(NULL, 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
            Response::json(["redirect_url"=>$task->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
            //$this->exportList($data->toArray());
        } else {

            $type = [
                //'from_account_no' => 14,
                'other_creator' => 1,
                'from_phone' => 2,
                'into_account_no' => 14,
                'receive_person' => 1,
                'into_phone' => 2,
            ];
            $data = \Fuel\Service\MaskingService::maskingData($data,$type);

            Response::json($data);
        }
    }

    /**
     * 导出
     */
    public function getExportData($_params)
    {
        $filesArr = [];
        $taskInfo = $_params['taskInfo'];
        $data = $_params['params'];

        \Models\OilDownload::updateByJobsId([
            'jobs_id' => $taskInfo->id,
            'status'  => 10,
        ]);

        if (count($data) == 0) {
            throw new \RuntimeException('没有可以导出的数据', 2);
        }

        $fileName = date("Ymd_H_i_s");
        $realPath = realpath(dirname(dirname(__DIR__))) . DIRECTORY_SEPARATOR . 'www';
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'oil_account_money_transfer' . DIRECTORY_SEPARATOR;
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }

        $_filesArr = $this->exportJob($data);
        if ($_filesArr) {
            $filesArr[] = $_filesArr;
        }

        Capsule::connection()->enableQueryLog();


        $zipFile = $fileName . '.zip';
        $zip = new ZipArchive();
        if ($zip->open($filePath . $zipFile, ZIPARCHIVE::CREATE) !== TRUE) {
            exit("can't open " . $filePath . $zipFile . " zipFile!");
        }
        for ($i = 0; $i < count($filesArr); $i++) {
            $zip->addFile($filesArr[$i], basename($filesArr[$i]));
        }
        $zip->close();

        for ($i = 0; $i < count($filesArr); $i++) {
            if (file_exists($filesArr[$i])) {
                @unlink($filesArr[$i]);
            }
        }

        $filePath = realpath($filePath . $zipFile);

        \Framework\Log::dataLog('exportTaskId:' . $taskInfo->id . ' | realPath:' . $filePath, 'jobOk');

        $url = $this->loadModel('common')->fileUploadToOss($filePath);
        \Models\OilDownload::updateByJobsId([
            'jobs_id'  => $taskInfo->id,
            'status'   => 20,
            'filename' => $zipFile,
            'filetype' => helper::getFileType($filePath),
            'filesize' => round(filesize($filePath) / 1024, 2),
            'url'      => $url,
        ]);
    }

    public function exportJob($data)
    {
        $realPath = realpath(dirname(dirname(__DIR__))) . '/www';
        $exportData = [
            'filePath'  => $realPath . '/data/oil_account_money_transfer',
            'fileName'  => date("YmdHis") . '_',
            'sheetName' => '转账申请-现金',
            'download'  => 1,
            'title'     => [
                'no'            => '申请单号',
                '_no_type'      => '转账类型',
                'money'         => '转账金额',
                'pay_name'      => '付款方',
                'from_account_no'  => '付款账号',
                'app_person'    => '申请人',
                'from_phone'    => '申请人手机号',
                'receive_name'  => '收款方',
                'into_account_no'  => '收款账号',
                'receive_person' => '收款人',
                'into_phone'     => '收款人手机号',
                'app_orgCode'   => '申请机构',
                'app_time'      => '申请时间',
                'orgroot'      => '顶级机构',
                '_orgroot'      => '顶级机构编码',
                'status'        => '状态',
                'data_from'     => '数据来源',
                'true_name'     => '创建人',
                'last_operator' => '最后修改人',
                'updatetime'    => '最新更新时间',
                'is_test'       => '测试机构',
                'remark'        => '备注/内',
                'remark_work'   => '备注/外',
            ],
            'data'      => $data->toArray(),
        ];

        return \Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['from_account_no', 'into_account_no', 'from_phone', 'into_phone'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });
    }

    /**
     * 添加转账
     * <AUTHOR> Du
     * @since 2016/05/12
     */
    public function addTransfer()
    {
        $uid = $this->app->myAdmin->id;
        $params = helper::filterParams();

        if(isset($params['no_type']) && $params['no_type'] == 'HB'){
            $result = \Fuel\Service\CardVice::cardTransferForFoss([
                'transfer_amount' => $params['money'],
                'out_vice_no' => $params['out_vice_no'],
                'mobile' => $params['mobile'],
                'receive_name' => $params['receive_name'],
                'remark_work' => $params['remark_work'],
                'remark' => $params['remark'],
                'sn'     => isset($params['sn']) && $params['sn'] ? $params['sn'] : \GosSDK\Lib\Helper::uuid(),
            ]);

            Response::json($result, 0, '添加成功');
        }

        $params['is_org'] = $params['is_org'] == 2 ? 2 : 1;
        $data_from = isset($params['data_from']) && $params['data_from'] ? $params['data_from'] : 1;
        \Framework\Log::info('添加参数--' . var_export($params, TRUE), [], 'accountMoneyTransfer');
        $params['sn'] = $params['data_from'] == 1 ? \GosSDK\Lib\Helper::uuid() : $params['sn'];

        helper::argumentCheck(['money','sn'], $params);

        //txb 2018.5.31 modify
        if($params['is_org'] == 2) {
            $accountInfo = \Models\OilAccountMoney::getByAccountNo(['account_no'=>$params['into_orgcode']]);
            if( empty($accountInfo) ){
                Response::json(NULL, 1, '帐号不存在');
            }
            $params['into_orgcode'] = $accountInfo->Org->orgcode;
        }

        //校验机构号是否有效
        $org_info = OilAccountMoneyTransfer::getOrgInfo($params);
        if (!$org_info) {
            Response::json(NULL, 1, '机构不存在');
        }

        if($org_info['into_org_info']->id == $org_info['from_org_info']->id) {
            Response::json(NULL, 1, '同机构不允许转账');
        }

        if($params['money'] <= 0){
            throw new \RuntimeException('转账不能小于0', 2);
        }

        //可用余额
        $used_money = FrozenMoney::accountBalance($org_info['from_org_info']->id);
        \Framework\Log::debug('addTransfer---', [$used_money, round(($used_money - $params['money']), 2)], 'addTransfer');
        if (round(($used_money - $params['money']), 2) < 0) {
            throw new \RuntimeException('现金账户余额不足，或存在冻结金额导致现金账户可用余额不足', 2);
        }

        global $app;
        //G7WALLET-399
        //独立核算规则（是独立核算，只能顶级给子集转，不能挎机构，和平级转）
        if( $org_info['from_org_info']->receipt_mode == 2 && !in_array(substr($org_info['from_org_info']->orgcode,0,6),$app->config->customer->transferOrgCode)
            && !in_array($this->app->myAdmin->id,$app->config->customer->transferUserIds) ){
            //不能跨机构
            if ($org_info['from_org_info']->orgcode != substr($org_info['into_org_info']->orgcode, 0, strlen($org_info['from_org_info']->orgcode))){
                throw new \RuntimeException('独立核算的机构，只能向自己的下级转账', 2);
            }
            //不能子集给上级转
            if (strlen($org_info['from_org_info']->orgcode) > strlen($org_info['into_org_info']->orgcode)) {
                throw new \RuntimeException('因开票模式是独立核算，不允许子集给顶级转账', 2);
            }
            //平级不能给平级转
            if(strlen($org_info['from_org_info']->orgcode) == strlen($org_info['into_org_info']->orgcode)){
                throw new \RuntimeException('因开票模式是独立核算，不允许平级之间转账', 2);
            }
        }

        $from_account_Info = \Models\OilAccountMoney::getByOrgId(['org_id'=>$org_info['from_org_info']->id]);
        $into_account_Info = \Models\OilAccountMoney::getByOrgId(['org_id'=>$org_info['into_org_info']->id]);

        Capsule::connection()->beginTransaction();
        try {
            //检查sn是否存在
            $transInfo = OilAccountMoneyTransfer::getBySnWithOrgCodeForUpdate(['sn'=>$params['sn'],'from_orgcode'=>$org_info['from_org_info']->orgcode]);
            if($transInfo){
                throw new \RuntimeException('该流水号已存在', 1);
            }

            //@todo 未锁定账户
            //$from_account_info = \Models\OilAccountMoney::getByOrgIdForLock(['org_id'=>$org_info['from_org_info']->id]);
            //计算返利可用余额
            //$faLiRemain = \Fuel\Service\Assign::getFanliRemainForUse($org_info['from_org_info']->id);
            $use_fanli = 0;
//            if($faLiRemain > 0){
//                $use_fanli = $params['money'] > $faLiRemain ? $faLiRemain : $params['money'];
//            }
            $resultsRe = [
                'no'               => OilAccountMoneyTransfer::createNo('ZZ'),
                'sn'               => $params['sn'],
                'no_type'          => 'ZZ',
                'org_id'           => $org_info['from_org_info']->id,
                'into_org_id'      => $org_info['into_org_info']->id,
                'from_orgname'     => $org_info['from_org_info']->org_name,
                'into_orgname'     => $org_info['into_org_info']->org_name,
                'from_orgcode'     => $org_info['from_org_info']->orgcode,
                'into_orgcode'     => $org_info['into_org_info']->orgcode,
                'from_account_no'  => $from_account_Info->account_no ? $from_account_Info->account_no : null,
                'into_account_no'  => $into_account_Info->account_no ? $into_account_Info->account_no : null,
                'status'           => 0,
                'app_time'         => isset($params['app_time']) && $params['app_time'] ? $params['app_time'] : date
                ("Y-m-d H:i:s"),
                'data_from'        => $data_from,
                'use_fanli'        => $use_fanli,
                'money'            => isset($params['money']) && $params['money'] ? $params['money'] : '',
                'remark'           => isset($params['remark']) && $params['remark'] ? $params['remark'] : '',
                'remark_work'      => $params['remark_work'],
                'creator_id'       => isset($params['other_creator_id']) ? NULL : $uid,
                'createtime'       => helper::nowTime(),
                'other_creator_id' => isset($params['other_creator_id']) && $params['other_creator_id'] ?
                    $params['other_creator_id'] : '',
                'other_creator'    => isset($params['other_creator']) && $params['other_creator'] ?
                    $params['other_creator'] : '',
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name,
                'updatetime'       => helper::nowTime(),
            ];
            $result = OilAccountMoneyTransfer::add($resultsRe);
            if (!$result) {
                throw new \RuntimeException('添加转账申请失败', 1);
            }

            //添加工单日志
            \Models\OilCardViceAppLog::add([
                'type'             => 5,
                'app_id'           => $result->id,
                'status'           => 0,
                'status_name'      => '待审核',
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => helper::nowTime(),
                'updatetime'       => helper::nowTime(),
            ]);

            //计算转账单使用的返利，并修改之
            AccountTransfer::calculateFanli($result->id);

            //push至G7Pay
            (new TransferService())->transferReserveById($result->id);

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        //push至Gos系统
        AccountMoneyTransferToGos::sendBatchCreateTask([$result->id],'sync');

        Response::json($result, 0, '添加成功');
    }

    /**
     * @title 修改备注
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function editTransferForRemark()
    {
        $params = helper::filterParams();
        \Framework\Log::info('修改参数--' . var_export($params, TRUE), [], 'accountMoneyTransfer');
        helper::argumentCheck(['id'], $params);

        if(isset($params['no_type']) && $params['no_type'] == 'HB'){
            throw new \RuntimeException('油费划拨禁止编辑，请重新创建，删除本条',2);
//            $result = \Fuel\Service\CardVice::cardTransferForFoss([
//                'transfer_amount' => $params['money'],
//                'out_vice_no' => $params['out_vice_no'],
//                'mobile' => $params['mobile'],
//                'receive_name' => $params['receive_name'],
//                'remark_work' => $params['remark_work'],
//                'remark' => $params['remark'],
//            ]);

            Response::json($result, 0, '添加成功');
        }

        $params['last_operator_id'] = $this->app->myAdmin->id;
        $params['last_operator'] = $this->app->myAdmin->true_name;
        $params['updatetime'] = helper::nowTime();

        try{
            OilAccountMoneyTransfer::edit($params);
            //push至Gos系统
            AccountMoneyTransferToGos::sendBatchUpdateTask([$params['id']], 'sync');
        }catch (\Exception $e){
            throw new \RuntimeException($e->getMessage(),$e->getCode());
        }

        Response::json(['msg' => '修改成功'],0,'编辑成功');
    }

    /**
     * 修改转账申请
     * <AUTHOR> Du
     * @since 2016/05/13
     */
    public function editTransfer()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        //校验机构号是否有效
        $org_info = OilAccountMoneyTransfer::getOrgInfo($params);

        if (!$org_info) {
            Response::json(NULL, 1, '机构不存在');
        }

        if($params['money'] <= 0){
            throw new \RuntimeException('转账不能小于0', 2);
        }

        $remark = isset($params['remark']) && $params['remark'] ? $params['remark'] : '';
        $remark_work = isset($params['remark_work']) && $params['remark_work'] ?
            $params['remark_work'] : '';

        Capsule::connection()->beginTransaction();
        try {
            //校验：已审核的单号不能编辑
            $info = OilAccountMoneyTransfer::getByIdForUpdate(['id' => $params['id']]);
            //可用余额
            $used_money = FrozenMoney::accountBalance($org_info['from_org_info']->id);
            if (round(($used_money + $info->money - $params['money']), 2) < 0) {
                throw new \RuntimeException('现金账户余额不足，或存在冻结金额导致现金账户可用余额不足', 2);
            }

            $from_org_info = \Models\OilOrg::where('id', '=', $params['from_org_id'])->first();
            $into_org_info = \Models\OilOrg::where('id', '=', $params['into_org_id'])->first();

            //pushToG7Pay
            (new TransferService())->transferReserveFailById($params['id']);

            //计算返利可用余额
            $use_fanli = 0;
//            $faLiRemain = \Fuel\Service\Assign::getFanliRemainForUse($org_info['from_org_info']->id);
//            if( $faLiRemain + $info->use_fanli > 0){
//                $use_fanli = $params['money'] > ($faLiRemain+$info->use_fanli) ? ($faLiRemain+$info->use_fanli) : $params['money'];
//            }

            $resultsRe = [
                'id'               => $params['id'],
                #'sn'               => \Framework\Helper::uuid(),
                'billID'               => \Framework\Helper::uuid(),
                'org_id'           => $from_org_info->id,
                'into_org_id'      => $into_org_info->id,
                'from_orgname'     => $from_org_info->org_name,
                'into_orgname'     => $into_org_info->org_name,
                'from_orgcode'     => $from_org_info->orgcode,
                'into_orgcode'     => $into_org_info->orgcode,
                'app_time'         => $params['app_time'],
                'use_fanli'        => $use_fanli,
                'money'            => $params['money'],
                'remark'           => $params['remark'],
                'remark_work'      => $params['remark_work'],
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name,
                'updatetime'       => helper::nowTime(),
            ];

            $result = OilAccountMoneyTransfer::edit($resultsRe);
            if (!$result) {
                throw new \RuntimeException('修改转账申请失败', 1);
            }

            //计算转账单使用的返利，并修改之
            AccountTransfer::calculateFanli($params['id']);

            //pushToG7Pay
            (new TransferService())->transferReserveById($params['id']);

            Capsule::connection()->commit();

            \Framework\Log::info('$params--' . var_export($params, TRUE));
            //push至Gos系统
            AccountMoneyTransferToGos::sendBatchUpdateTask([$params['id']], 'sync');

        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json(['msg' => '修改成功']);
    }

    /**
     * 删除
     * <AUTHOR> Du
     * @since 2016/05/16
     */
    public function delete()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        //开启事务
        Capsule::connection()->beginTransaction();
        $result = FALSE;
        try {
            $transferInfo = OilAccountMoneyTransfer::getById(['id' => $params['ids']]);

            if ($transferInfo->status == 1) {
                throw new \RuntimeException('该单号已审核', 2);
            }

            if ($transferInfo->billID) {
                //pushToG7Pay
                (new TransferService())->transferReserveFailById($params['ids']);
            }

            //删除加入回收站
            $recycle = [];
            $recycle['table_name'] = 'oil_account_money_transfer';
            $recycle['pk'] = $transferInfo->id;
            $recycle['org_id'] = $transferInfo->org_id;
            $recycle['no'] = $transferInfo->no;
            $recycle['data'] = json_encode($transferInfo);
            $recycle['operator_id'] = $this->app->myAdmin->id;
            $recycle['createtime'] = helper::nowTime();
            $recycles = \Models\OilRecycle::add($recycle);
            if ($recycles) {
                $result = OilAccountMoneyTransfer::remove(['ids' => $params['ids']]);
            }

            if (!$result) {
                throw new \RuntimeException('删除失败', 2);
            }

            //事务提交
            Capsule::connection()->commit();
        } catch (Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        //push至Gos系统
        AccountMoneyTransferToGos::sendBatchDeleteTask([$params['ids']],'sync');

        Response::json(['success' => FALSE, 'msg' => '删除成功'], 0, '删除成功');
    }

    /**
     * 审核
     * <AUTHOR> Du
     * @since 2016/05/13
     */
    public function cardAudit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);

        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            $transferInfo = OilAccountMoneyTransfer::getByIdForUpdate(['id' => $params['ids']]);

            //划拨工单处理逻辑
            if($transferInfo->no_type == 'HB'){
                \Framework\Log::error('111',[$transferInfo->id],'tim0528');
                //审核工单
                $result = AccountTransfer::auditFortune(['id'=>$transferInfo->id]);
                \Framework\Log::error('222',[$result],'tim0528');
                //事务提交
                Capsule::connection()->commit();

                Response::json($result,0,'成功');
            }

            //校验
            \Fuel\Service\AccountTransfer::auditByValidate($transferInfo);

            //账户相关操作
            $res = \Fuel\Service\AccountTransfer::transferMoney($transferInfo);
            $from_record = $res['from_record'];
            $into_record = $res['into_record'];

            //修改工单
            $result = $transferInfo->update([
                'status' => 1,
                'audit_time'    =>  date("Y-m-d H:i:s"),
                'use_fanli' => $res['use_fanli'],
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name,
                'updatetime'       => helper::nowTime()]);
            if (!$result) {
                throw new \RuntimeException('更新转账申请单失败', 7);
            }

            //添加工单日志
            \Models\OilCardViceAppLog::add([
                'type'             => 5,
                'app_id'           => $params['ids'],
                'status'           => 1,
                'status_name'      => '已审核',
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => helper::nowTime(),
                'updatetime'       => helper::nowTime(),
            ]);

            if ($transferInfo->billID) {
                //pushToG7Pay
                (new TransferService())->transferReserveSuccessById($params['ids']);
            } else {
                (new TransferService())->singleSend($params['ids']);
            }

            $returnData = ['msg' => '审核成功', 'status' => TRUE];

            //事务提交
            Capsule::connection()->commit();
        } catch (Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            $returnData['msg'] = '审核失败' . $e->getMessage();

            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        try{
            //push至Gos系统
            AccountMoneyTransferToGos::sendBatchUpdateTask([$params['ids']],'sync');
        }catch (\Exception $e){
            \Framework\Log::dataLog('pushToGos--'.strval($e),'cardAuditError');
        }

        //发送微信通知
        try {
            //转出机构微信提醒
            \Models\OilOrgWx::sendNotify([
                'org_id'       => $from_record['org_id'],
                'cash_type'    => '转出',
                'money'        => formatMoney($transferInfo->money),
                'blance_money' => formatMoney($from_record['after_money']),
                'remark'       => $from_record['remark'],
            ]);
            //小于5000块微信提醒
            if (intval($from_record['after_money']) < \Fuel\Defines\AccountMoneyTip::$AccountMoneyLimit) {
                \Models\OilOrgWx::sendMoneyLackNotify([
                    'org_id'       => $from_record['org_id'],
                    'blance_money' => formatMoney($from_record['after_money']),
                ]);
            }
            //转入机构微信提醒
            \Models\OilOrgWx::sendNotify([
                'org_id'       => $into_record['org_id'],
                'cash_type'    => '转入',
                'money'        => formatMoney($into_record['money']),
                'blance_money' => formatMoney($into_record['after_money']),
                'remark'       => $into_record['remark'],
            ]);
            //小于5000块微信提醒
            if (intval($into_record['after_money']) < \Fuel\Defines\AccountMoneyTip::$AccountMoneyLimit) {
                \Models\OilOrgWx::sendMoneyLackNotify([
                    'org_id'       => $into_record['org_id'],
                    'blance_money' => formatMoney($into_record['after_money']),
                ]);
            }
        } catch (Exception $e) {
              \Framework\Log::dataLog('sendNotify--'.strval($e),'wxDebug');
        }

        Response::json($returnData);
    }

    /**
     * 驳回
     * <AUTHOR> Du
     * @since 2016/05/16
     */
    public function cardReject()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);

        Capsule::connection()->beginTransaction();
        try {
            $transferInfo = OilAccountMoneyTransfer::getByIdForUpdate(['id' => $params['ids']]);
            if ($transferInfo->status == 1) {
                throw new \RuntimeException('该单号已审核', 2);
            }
            if ($transferInfo->status == -1) {
                throw new \RuntimeException('该单号已驳回', 2);
            }

            //pushToG7Pay
            (new TransferService())->transferReserveFailById($params['ids']);

            $result = OilAccountMoneyTransfer::edit([
                    'id'               => $params['ids'],
                    #'sn'               => \Framework\Helper::uuid(),
                    'billID'           => NULL,
                    'status'           => -1,
                    'last_operator_id' => $this->app->myAdmin->id,
                    'last_operator'    => $this->app->myAdmin->true_name,
                    'updatetime'       => helper::nowTime(),
                ]
            );
            if (!$result) {
                throw new \RuntimeException('驳回失败', 3);
            }
            //添加工单日志
            \Models\OilCardViceAppLog::add([
                'type'             => 5,
                'app_id'           => $params['ids'],
                'status'           => -1,
                'status_name'      => '已驳回',
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => helper::nowTime(),
                'updatetime'       => helper::nowTime(),
            ]);

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        //push至Gos系统
        AccountMoneyTransferToGos::sendBatchUpdateTask([$params['ids']], 'sync');

        Response::json(['msg' => '驳回成功']);
    }

    /**
     * 销审
     * <AUTHOR> Du
     * @since 2016/05/13
     */
    public function cardUnAudit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);

        $returnData = NULL;

        Capsule::connection()->beginTransaction();
        try {
            $transferInfo = OilAccountMoneyTransfer::getByIdForUpdate(['id' => $params['ids']]);

            //校验
            \Fuel\Service\AccountTransfer::unAuditByValidate($transferInfo);

            //账户相关操作
            $res = \Fuel\Service\AccountTransfer::unAuditTransferMoney($transferInfo);

            $result = $transferInfo->update([
                'status'           => 0,
                'use_fanli'        => $res['use_fanli'],
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name, 'updatetime' => helper::nowTime()]);
            if (!$result) {
                throw new \RuntimeException('更新转账申请单失败', 7);
            }

            //添加工单日志
            \Models\OilCardViceAppLog::add([
                'type'             => 5,
                'app_id'           => $params['ids'],
                'status'           => 0,
                'status_name'      => '待审核',
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => helper::nowTime(),
                'updatetime'       => helper::nowTime(),
            ]);

            Capsule::connection()->commit();

            $returnData = ['msg' => '销审成功'];
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        //push至Gos系统
        AccountMoneyTransferToGos::sendBatchUpdateTask([$params['ids']], 'sync');

        Response::json($returnData);
    }

    /**
     * 批量导入
     */
    public function batchImport()
    {
        //上传文件
        $upload = $this->file_upload($_FILES['userfile']);

        if (is_numeric($upload) && $upload < 0) {
            if ($upload == -9) {
                $msg = "未选择文件";
            } elseif ($upload == -8) {
                $msg = "文件格式不正确";
            } else {
                $msg = "上传导入失败";
            }
            throw new RuntimeException($msg, $upload);
        }

        $fieldMap = [
            'from_orgname' => '转出机构',
            'into_orgname' => '转入机构',
            'money'        => '转账金额',
            'app_time'     => '申请时间',
            'remark'       => '备注/内',
            'remark_work'  => '备注/外',
        ];

        $excelParams = [
            'filePath'  => $upload,
            'fieldsMap' => array_flip($fieldMap),
        ];

        $result = ExcelReader::read($excelParams, function ($rowNum, $fieldName, $cellValue) {
            return $this->preImportCellValue($rowNum, $fieldName, $cellValue);
        }, function ($rowData) {
            if(count($rowData) > 0) {
                $this->checkRowData($rowData);
            }
        });

        if (count($result[0]) == 0) {
            throw new \RuntimeException('未获取到导入数据', 2);
        }

        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            $oldViceNo = [];
            $data = array_values($result[0]);
            foreach ($data as $v) {
                //预处理写入数据
                $this->preInsertData($v);
                $insertData = OilAccountMoneyTransfer::add($v);

                //pushToG7Pay
                (new TransferService())->transferReserveById($insertData->id);
            }
            //提交事务
            Capsule::connection()->commit();
        } catch (Exception $e) {
            //回滚事务
            Capsule::connection()->rollBack();
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        $data = ['success' => TRUE, 'msg' => array_values($result[0])];
        echo json_encode($data);
        exit;
    }

    /**
     * 单元格数据预处理
     * @param $rowNum
     * @param $fieldName
     * @param $cellValue
     * @return array|string
     */
    private function preImportCellValue($rowNum, $fieldName, $cellValue)
    {
        $data = $cellValue;
        $msg = '';
        if ($rowNum > 1) {
            switch ($fieldName) {
                case 'from_orgname':
                    if ($cellValue) {
                        $info = explode(' ', $cellValue);
                        if (count($info) != 2) {
                            throw new RuntimeException('转出机构格式错误', 2);
                        }
                        $orgInfo = Models\OilOrg::getByOrgcode($info[0]);
                        if (!$orgInfo || !isset($orgInfo->id) || !$orgInfo->id) {
                            $data = ['success' => FALSE, 'msg' => '导入失败:【' . $cellValue . '】 转出机构编码不存在'];
                            echo json_encode($data);
                            exit;
                        }
                        $orgInfo = Models\OilOrg::getByOrgName($info[1]);
                        if (!$orgInfo || !isset($orgInfo->id) || !$orgInfo->id) {
                            $data = ['success' => FALSE, 'msg' => '导入失败:【' . $cellValue . '】 转出机构不存在'];
                            echo json_encode($data);
                            exit;
                        }
                    } else {
                        $msg = '导入失败: 转出机构不能为空';
                    }
                    break;
                case 'into_orgname':
                    if ($cellValue) {
                        $info = explode(' ', $cellValue);
                        if (count($info) != 2) {
                            throw new RuntimeException('转入机构格式错误', 2);
                        }
                        $orgInfo = Models\OilOrg::getByOrgcode($info[0]);
                        if (!$orgInfo || !isset($orgInfo->id) || !$orgInfo->id) {
                            $data = ['success' => FALSE, 'msg' => '导入失败:【' . $cellValue . '】 转入机构编码不存在'];
                            echo json_encode($data);
                            exit;
                        }
                        $orgInfo = Models\OilOrg::getByOrgName($info[1]);
                        if (!$orgInfo || !isset($orgInfo->id) || !$orgInfo->id) {
                            $data = ['success' => FALSE, 'msg' => '导入失败:【' . $cellValue . '】 转入机构不存在'];
                            echo json_encode($data);
                            exit;
                        }
                    } else {
                        $msg = '导入失败: 转入机构不能为空';
                    }
                    break;
                case 'money':
                    if ($cellValue) {
                        if (!preg_match('/^(-{1})?[0-9]+(.[0-9]{1,2})?$/', $cellValue)) {
                            $msg = '导入失败: 转账金额【' . $cellValue . '】格式不正确';
                        }
                    } else {
                        $msg = '导入失败: 转账金额不能为空';
                    }

                    break;
                case 'app_time':
                    if (!isset($cellValue) && $cellValue == '') {
                        $msg = '导入失败: 申请时间不能为空';
                    }
                    break;
                case 'remark':
                    if (mb_strlen($cellValue) > 200) {
                        $msg = '导入失败: 备注/内必须小于200个字符';
                    }
                    break;
                case 'remark_work':
                    if (mb_strlen($cellValue) > 200) {
                        $msg = '导入失败: 备注/外必须小于200个字符';
                    }
                    break;
                default :
                    $data = $cellValue;
            }
        }

        if ($msg) {
            throw new RuntimeException($msg, 2);
        }

        return $data;
    }

    //上传文件
    public function file_upload($file)
    {
        set_time_limit(0);
        if (empty($file) || empty($file['name'])) {
            //未选择文件
            return -9;
        }
        //判断文件类型
        if ($file['name']) {
            $ext = strtolower(trim(substr(strrchr($file['name'], '.'), 1)));
            if ($ext != "xls" && $ext != "xlsx") {
                //文件类型不正确
                throw new Exception('文件类型不正确', -8);
            }
            $dir = '../tmp/data';
            if (!is_dir($dir)) {
                helper::createDir($dir, 0777);
            }

            //文件上传
            $tmp_name = $file['tmp_name'];
            $newname = $dir . '/import_' . date('m-d-H-i-s') . '.' . $ext;

            if (@copy($tmp_name, $newname)) {
                @unlink($tmp_name);
            } elseif (@move_uploaded_file($tmp_name, $newname)) {
            } elseif (@rename($tmp_name, $newname)) {
            } else {
                //上传文件失败
                return -7;
            }
            @chmod($newname, 0777);

            return $newname;
        }
    }

    /**
     * 校验导入的行数据
     * @param $rowData
     */
    private function checkRowData($rowData)
    {
        $this->checkInput($rowData);
    }

    /**
     * @title   校验表单
     * <AUTHOR>
     * @param array $params
     * @param bool $record
     */
    private function checkInput(array $params)
    {
        if ($params['money'] <= 0) {
            throw new RuntimeException('金额不能小于零', 2);
        }
        if ($params['app_time']) {
            is_numeric($params['app_time']) ? $params['app_time'] = gmdate("Y-m-d H:i:s", PHPExcel_Shared_Date::ExcelToPHP($params['app_time'])) : $params['app_time'];
        }
    }


    private function preInsertData(&$params)
    {
        $info = explode(' ', $params['from_orgname']);
        if (count($info) != 2) {
            throw new RuntimeException('转出机构格式错误', 2);
        }
        $orgInfo = Models\OilOrg::getByOrgcode($info[0]);
        if ($orgInfo->org_name != $info[1]) {
            throw new \RuntimeException('导入失败: 转出机构编码与机构名称不对应', 3);
        }

        $into_info = explode(' ', $params['into_orgname']);
        if (count($into_info) != 2) {
            throw new RuntimeException('转入机构格式错误', 2);
        }
        $into_orgInfo = Models\OilOrg::getByOrgcode($into_info[0]);
        if ($into_orgInfo->org_name != $into_info[1]) {
            throw new \RuntimeException('导入失败: 转入机构编码与机构名称不对应', 3);
        }
        //验证机构
        if ($info[0] == $into_info[0]) {
            throw new RuntimeException('转出机构与转入机构不能为同一机构', 2);
        }
        //校验机构号是否有效
        $res['from_org_id'] = $orgInfo->id;
        $res['into_org_id'] = $into_orgInfo->id;
        $org_info = OilAccountMoneyTransfer::getOrgInfo($res);
        if (!$org_info) {
            Response::json(NULL, 1, '机构不合法');
        }
        //可用余额
        $used_money = FrozenMoney::accountBalance($orgInfo->id);
        if (round(($used_money - $params['money']), 2) < 0) {
            throw new \RuntimeException('现金账户余额不足，或存在冻结金额导致现金账户可用余额不足', 2);
        }
        $from_account_info = \Models\OilAccountMoney::where('org_id', '=', $orgInfo->id)->first();
        $use_fanli = $params['money'] > $from_account_info->cash_fanli_remain ? $from_account_info->cash_fanli_remain : $params['money'];
        $params['no'] = OilAccountMoneyTransfer::createNo('ZZ');
        $params['no_type'] = 'ZZ';
        $params['status'] = '0';
        if (!$params['app_time'] || $params['app_time'] == '0000-00-00 00:00:00') {
            $params['app_time'] = date("Y-m-d H:i:s");
        } else {
            is_numeric($params['app_time']) ? $params['app_time'] = gmdate("Y-m-d H:i:s", PHPExcel_Shared_Date::ExcelToPHP($params['app_time'])) : $params['app_time'];
        }
        $params['org_id'] = $orgInfo->id;
        $params['into_org_id'] = $into_orgInfo->id;
        $params['from_orgname'] = $orgInfo->org_name;
        $params['into_orgname'] = $into_orgInfo->org_name;
        $params['from_orgcode'] = $orgInfo->orgcode;
        $params['into_orgcode'] = $into_orgInfo->orgcode;
        $params['data_from'] = 1;
        $params['use_fanli'] = $use_fanli;
        $params['creator_id'] = $this->app->myAdmin->id ? $this->app->myAdmin->id : 1;
        $params['createtime'] = helper::nowTime();
        $params['other_creator_id'] = '';
        $params['other_creator'] = '';
        $params['last_operator_id'] = $this->app->myAdmin->id;
        $params['last_operator'] = $this->app->myAdmin->true_name;
        $params['updatetime'] = helper::nowTime();


    }

    /**
     * 返利模板下载
     */
    public function TplDownload()
    {
        $file_xls = $this->app->getAppRoot() . 'www' . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . 'account_money_transfer.xls';    //   文件的保存路径
        $example_name = basename($file_xls);  //获取文件名
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename=' . mb_convert_encoding($example_name, "gb2312", "utf-8"));  //转换文件名的编码
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        ob_clean();
        header('Content-Length: ' . filesize($file_xls));
        flush();
        readfile($file_xls);
    }

    /**
     * @title   初始化数据到gos
     * <AUTHOR>
     */
    public function batchAddToGos()
    {
        AccountMoneyTransferToGos::init();
    }

    /**
     * @title   回写sn流水号
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function reWriteSn()
    {
        try {
            $total = OilAccountMoneyTransfer::whereNull('sn')->count();
            if ($total > 0) {
                $pageSize = 1000;
                $totalPage = ceil($total/$pageSize);
                for($i=0;$i<=$totalPage;$i++){
                    $records = OilAccountMoneyTransfer::whereNull('sn')->orderBy('id','asc')->skip($i*$pageSize)->take($pageSize)->get();
                    if($records){
                        foreach($records as $v){
                            $v->update(['sn'=>\Framework\Helper::uuid()]);
                            $batchInsertSqlArr[] = "update oil_account_money_transfer set sn = '".\Framework\Helper::uuid()."' where id = ".$v->id." ";
                        }
                        $batchInsertSql = implode(";", $batchInsertSqlArr);

                        Capsule::connection()->getPdo()->exec($batchInsertSql);
                    }
                }
            }
        } catch (\Exception $e) {
            throw new \RuntimeException(strval($e), $e->getCode());
        }

    }

    public function transferForYT()
    {
        $params = helper::filterParams();
        $from_org_info = Models\OilOrg::getByOrgcode($params['from_orgcode']);
        $into_org_info = Models\OilOrg::getByOrgcode($params['into_orgcode']);
        $res = (new \Fuel\Service\AccountCenter\TransferService())->createTransfer($from_org_info->id,$into_org_info->id,$params['money']);
        var_dump($res);exit;
    }

    /**
     * 转账
     */
    public function transferForGas()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['sn','roll_in_org_code','roll_out_org_code','amount'], $params);

        $data = AccountTransfer::transferForGas($params);

        Response::json($data);
    }

    /*
     * 获取gas关联账户余额
     */
    public function getAccountByGas()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['org_code'], $params);

        $data = AccountTransfer::getAccountByGas($params);

        Response::json($data);
    }

    /*
     * 获取机构顶级列表
     */
    public function getOrgListForGas()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['org_code'], $params);

        $data = AccountTransfer::getOrgListForGas($params);

        Response::json($data);
    }
}
