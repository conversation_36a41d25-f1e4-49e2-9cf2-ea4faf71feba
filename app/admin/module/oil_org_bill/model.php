<?php
/**
 * 机构账单
 * <AUTHOR>
 */

class oil_org_billModel extends model
{
	public function __construct(){
		parent::__construct();
	}
	/**
	 * 信息列表
	 * @param string $where
	 * @param string $fields
	 * <AUTHOR>
	 * @since 2015/10/28
	 */
	public function search($where='',$limit='',$where1=''){
		$sql = "SELECT %s FROM oil_org a LEFT JOIN 
(SELECT org_id,SUM(money) cz_money FROM oil_account_money_records WHERE no_type='CZ' $where GROUP BY org_id) b ON a.id=b.org_id
LEFT JOIN 
(SELECT org_id,COUNT(*) oil_num,
SUM(fanli_money) fanli_money,SUM(fanli_jifen) fanli_jifen,SUM(trade_num) trade_num
 FROM oil_card_vice_trades WHERE trade_type='加油' $where GROUP BY org_id) c  ON a.id=c.org_id 
 LEFT JOIN 
(SELECT SUM(trade_money) consume_money,org_id
 FROM oil_card_vice_trades WHERE trade_type='加油' AND consume_type=1 $where GROUP BY org_id) d  ON a.id=d.org_id 
 LEFT JOIN 
(SELECT SUM(trade_money) consume_jifen,org_id
 FROM oil_card_vice_trades WHERE trade_type='加油' AND consume_type=2 $where GROUP BY org_id) e  ON a.id=e.org_id ";
		$fields = ' a.org_name,b.cz_money,d.consume_money,e.consume_jifen,ROUND(c.fanli_money,2) fanli_money,ROUND(c.fanli_jifen,2) fanli_jifen,c.trade_num,c.oil_num';
		$total = parent::findBySql($sql, $where1, null, null, 'count(1) count');
		$datas = array();
		if ($total[0]->count > 0) {
			$datas = parent::findBySql($sql, $where1, '', $limit, $fields);
		}
		$result = new stdClass();
		$result->total = $total[0]->count;
		$result->data = $datas;
		return $result;
	}
}
