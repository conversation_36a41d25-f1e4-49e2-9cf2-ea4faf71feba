<?php

use Framework\Excel\ExcelReader;
use Framework\Excel\ExcelWriter;
use Models\OilFanliPolicy;
use Fuel\Response;
use \Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;
use \Fuel\Defines\FanliType;

/**
 * 返利政策维护
 *
 */
class oil_fanli_policy extends baseControl
{
    public $modulePath;

    public function __construct()
    {
        parent::__construct();
        $this->modulePath = $this->app->getModulePath($this->moduleName);
    }


    /**
     * 查询返利政策信息
     */
    public function search()
    {
        $params = helper::filterParams();
        if (isset($params['_export']) && $params['_export'] == 1) {

            $params['count'] = 1;
            unset($params['_export']);
            $total = OilFanliPolicy::getList($params);
            if ($total == 0) {
                throw new \RuntimeException('没有可以导出的数据', 2);
            }

            $data = (new \Jobs\ExportFanliPolicyJob($params))
                ->setUserInfo($this->app->myAdmin)
                ->setTaskName('返利政策-导出')
                ->onQueue('export')
                ->dispatch();

            \Framework\Log::error('jobDebug:' . var_export($data, TRUE), [], 'jobDebug');
            if (isset($data->jobId) && $data->jobId) {
                Response::json(["redirect_url"=>$data->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
            } else {
                throw new \RuntimeException('异步任务下发失败', 2);
            }
        } else {
            $data = OilFanliPolicy::getList($params);
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param $data
     */
    private function exportList($data)
    {
        foreach ($data as &$val) {
            $val = (object)$val;
//            $val->main_no = $val->main_no . "\t";
            if ($val->fanli_type == 1) {//按现金
                $val->fanli_value = $val->fanli_coe . '%';
            } elseif ($val->fanli_type == 2) {//按油量
                $val->fanli_value = $val->fanli_money;
            } elseif (in_array(intval($val->fanli_type), [3, 4])) {//阶梯返利
                if ($val->coe_unit == 1) {//按现金百分比
                    $val->fanli_edu_level1 = $val->fanli_coe_level1 . '%';
                    $val->fanli_edu_level2 = $val->fanli_coe_level2 . '%';
                    $val->fanli_edu_level3 = $val->fanli_coe_level3 . '%';
                    $val->fanli_edu_level4 = $val->fanli_coe_level4 . '%';
                    $val->fanli_edu_level5 = $val->fanli_coe_level5 . '%';
                } elseif ($val->coe_unit == 2) {//按每升返利金额
                    $val->fanli_edu_level1 = $val->fanli_money_level1;
                    $val->fanli_edu_level2 = $val->fanli_money_level2;
                    $val->fanli_edu_level3 = $val->fanli_money_level3;
                    $val->fanli_edu_level4 = $val->fanli_money_level4;
                    $val->fanli_edu_level5 = $val->fanli_money_level5;
                }
            }
        }

        $exportData = [
            'filePath' => APP_WWW_ROOT . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . 'policy',
            'fileName' => 'policy_' . date("YmdHis") . rand(10, 99),
            'sheetName' => '返利政策维护',
            'fileExt' => 'xlsx',
            'download' => TRUE,
            'title' => [
                'id' => 'ID',
                'policy_name' => '政策名称',
                'policy_object_name' => '政策对象',
                'oil_com_name' => '油卡类型',
                'main_no' => '主卡',
                'station_name' => '可用油站',
                'main_jifen_no' => '积分返利主卡',
                'orgcode' => '返利机构编码',
                'org_name' => '返利机构',
                'regions_id_name' => '消费地区',
                'oil_type_name' => '油品类型',
                'fanli_way_name' => '返利形式',
                'fanli_type_name' => '返利类型',
                'oil_amount_limit' => '加油量限制',
                'oil_money_limit' => '金额限制',
                'start_time' => '开始时间',
                'end_time' => '截止时间',
                'fanli_value' => '返利系数/金额',
                'coe_unit_name' => '系数单位',
                'fanli_level1_gt' => '一级返利区间开始',
                'fanli_level1_le' => '一级返利区间结束',
                'fanli_edu_level1' => '一级返利额度',
                'fanli_level2_gt' => '二级返利区间开始',
                'fanli_level2_le' => '二级返利区间结束',
                'fanli_edu_level2' => '二级返利额度',
                'fanli_level3_gt' => '三级返利区间开始',
                'fanli_level3_le' => '三级返利区间结束',
                'fanli_edu_level3' => '三级返利额度',
                'fanli_level4_gt' => '四级返利区间开始',
                'fanli_level4_le' => '四级返利区间结束',
                'fanli_edu_level4' => '四级返利额度',
                'fanli_level5_gt' => '五级返利区间开始',
                'fanli_level5_le' => '五级返利区间结束',
                'fanli_edu_level5' => '五级返利额度',
                'fanli_min_money' => '免惠最低价',
                'add_fanli_edu' => '加油量叠加优惠',
            ],
            'data' => $data,
        ];


        $file = \Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['main_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });

        return $file;
    }

    public function exportDubbleData()
    {
        $ids = [5681, 9030, 36059, 36062, 96616, 96617, 96618, 96619, 96620, 96621, 96622, 96624, 96625, 96626, 96627, 96628, 96629, 96630, 96631, 96632, 96633, 96634, 96635, 96636, 96637, 96638, 96639, 96640, 96641, 96642, 96643, 96644, 96906, 96907, 96908, 96909, 96910, 96911, 96912, 96914, 96915, 96916, 96917, 96918, 96919, 96920, 96921, 96922, 96923, 96924, 96925, 96926, 96927, 96928, 96929, 96930, 96931, 96932, 96933, 96934, 96935, 96936, 96937, 96938, 96939, 96940, 96941, 96943, 96944, 96945, 96946, 96947, 96948, 96949, 96950, 96951, 96952, 96953, 96954, 96955, 96956, 96957, 96958, 96959, 96960, 96961, 96962, 96963, 97718, 97719, 97720, 97721, 97722, 97723, 97724, 97726, 97727, 97728, 97729, 97730, 97731, 97732, 97733, 97734, 97735, 97736, 97737, 97738, 97739, 97740, 97741, 97742, 97743, 97744, 97745, 97746, 97921, 97922, 97923, 97924, 97925, 97926, 97927, 97928, 97929, 97930, 97931, 97932, 97933, 97934, 97935, 97936, 97937, 97938, 97939, 97940, 97941, 97942, 97943, 97944, 97945, 97946, 97947, 97948, 97949, 100607, 100608, 100609, 100610, 100611, 100612, 100613, 100614, 100615, 100616, 100617, 100618, 100619, 100620, 100621, 100622, 100623, 100624, 100625, 100626, 100627, 100628, 100629, 100630, 100631, 100632, 100633, 100634, 101111, 101112, 101113, 101114, 101115, 101116, 101117, 101118, 101119, 101120, 101121, 101122, 101123, 101124, 101125, 101126, 101127, 101128, 101129, 101130, 101131, 101132, 101133, 101134, 101135, 101136, 101137, 101138, 101307, 101308, 101309, 101310, 101311, 101312, 101313, 101314, 101315, 101316, 101317, 101318, 101319, 101320, 101321, 101322, 101323, 101324, 101325, 101326, 101327, 101328, 101329, 101330, 101331, 101332, 101333, 101334, 101615, 101616, 101617, 101618, 101619, 101620, 101621, 101622, 101623, 101624, 101625, 101626, 101627, 101628, 101629, 101630, 101631, 101632, 101633, 101634, 101635, 101636, 101637, 101638, 101639, 101640, 101641, 101642, 101755, 101756, 101757, 101758, 101759, 101760, 101761, 101762, 101763, 101764, 101765, 101766, 101767, 101768, 101769, 101770, 101771, 101772, 101773, 101774, 101775, 101776, 101777, 101778, 101779, 101780, 101781, 101782, 101973, 101980, 101981, 101982, 101983, 101984, 101985, 101986, 101988, 102720, 102721, 102722, 102723, 102724, 102725, 102726, 102727, 102728, 102729, 102730, 102731, 102732, 102733, 102734, 102735, 102736, 102737, 102738, 102739, 102740, 102741, 102742, 102743, 102744, 102745, 102746, 102801, 102802, 102803, 102804, 102805, 102807, 102808, 102809, 102810, 102811, 102812, 102813, 102814, 102815, 102816, 102817, 102818, 102819, 102820, 102821, 102822, 102823, 102824, 102825, 102826, 102827, 108222, 108223, 108224, 108225, 108226, 108228, 108229, 108230, 108231, 108232, 108233, 108234, 108235, 108236, 108237, 108238, 108239, 108240, 108241, 108242, 108243, 108244, 108245, 108246, 108247, 108248, 108249, 108334, 108335, 108336, 108337, 108338, 108341, 108342, 108343, 108344, 108345, 108346, 108347, 108348, 108349, 108350, 108351, 108352, 108353, 108354, 108355, 108356, 108357, 108358, 108359, 108360, 108361];
        $data = OilFanliPolicy::getList([
            'ids' => $ids,
            '_export' => 1
        ])->toArray();

        $filePath = $this->exportList($data);

        echo $filePath;
    }

    function objarray_to_array($obj)
    {
        $ret = [];
        foreach ($obj as $key => $value) {
            if (gettype($value) == "array" || gettype($value) == "object") {
                $ret[$key] = $this->objarray_to_array($value);
            } else {
                $ret[$key] = $value;
            }
        }

        return $ret;
    }

    /**
     * 获取省份直辖市
     */
    public function getOilProvinces()
    {
        $oilProvinces = $this->loadModel('oil_station')->getOilProvinces(TRUE);
        echo json_encode($oilProvinces);

    }

    /**
     * 查询消费地区
     */
    public function oilProvinces()
    {

        $region = $this->loadModel('oil_station')->getOilProvinces();
        $region = $this->objarray_to_array($region);
        foreach ($region as $value) {
            $regionlist[$value['id']] = $value['province'];
        }

        return $regionlist;
    }

    /**
     * 获取机构名称
     */
    public function getOilOrg()
    {
        $where = ' is_del = 0 ';
        $sort = ' id DESC ';
        $orgs = $this->loadModel('oil_org')->search($where, $sort, NULL);

        Response::json($orgs);
    }

    public function getOilOrgCode()
    {
        $where = ' is_del = 0 ';
        $sort = ' id DESC ';
        $orgs = $this->loadModel('oil_org')->searchOrgCode($where, $sort, NULL);

        Response::json($orgs);
    }

    /**
     *获取机构名称
     */
    public function oilOrg()
    {
        $sort = '';
        $where = 'is_del=0';
        $orgs = $this->loadModel('oil_org')->search($where, $sort, NULL);
        $array = $this->objarray_to_array($orgs);
        foreach ($array['data'] as $key => $value) {
            $orgname[$value['id']] = $value['org_name'];
        }

        return $orgname;
    }


    public function getCardMain()
    {
        $where = 'id>0';
        $params = helper::filterParams();
        if (isset($params['oil_com']) && $params['oil_com']) {
            $where .= ' and oil_com =' . $params['oil_com'];
        }
        if (isset($params['main_no']) && $params['main_no']) {
            $where .= " and main_no like '%" . $params['main_no'] . "%'";
        }

        $orgs = $this->loadModel('oil_card_main')->getAll($where);

        $supplyerMap = \Models\OilCardSupplyer::getSupplyerMap([]);
        if ($orgs) {
            foreach ($orgs as &$v) {
                $v->supplyer_name = $supplyerMap[$v->supplyer_id] ? $supplyerMap[$v->supplyer_id] : '常规';
            }
        }

        Response::json(['total' => 10, 'data' => $orgs]);
    }


    /**
     *获取主卡id
     **/
    public function getMainCard()
    {
        $maincard = $this->loadModel('oil_station')->getCardMainNum();
        $array = $this->objarray_to_array($maincard);

        return $array;

    }

    /**
     * @title 处理阶梯返利数据
     * @param $params
     * @return string
     * <AUTHOR>
     */
    private function getStepFanliData($params)
    {
        //阶梯返利字段
        $fieldsArr = ['fanli_level1_gt', 'fanli_level1_le', 'fanli_level2_gt', 'fanli_level2_le', 'fanli_level3_gt', 'fanli_level3_le', 'fanli_level4_gt', 'fanli_level4_le', 'fanli_level5_gt', 'fanli_level5_le', 'fanli_coe_level1', 'fanli_coe_level2', 'fanli_coe_level3', 'fanli_coe_level4', 'fanli_coe_level5', 'fanli_money_level1', 'fanli_money_level2', 'fanli_money_level3', 'fanli_money_level4', 'fanli_money_level5'];

        $stepFanliData = \Framework\Helper::getValuesFromArr($params, $fieldsArr);

        if ($params['coe_unit'] == 1) {//按现金百分比
            $stepFanliData['fanli_money_level1'] = '';
            $stepFanliData['fanli_money_level2'] = '';
        } elseif ($params['coe_unit'] == 2) {//按每升返利金额
            $stepFanliData['fanli_coe_level1'] = '';
            $stepFanliData['fanli_coe_level2'] = '';
        }

        return $stepFanliData;
    }

    /**
     * 添加返利政策
     */
    public function addFanliPolicy()
    {
        $params = helper::filterParams();
        Log::info('添加参数--' . var_export($params, TRUE), [], 'fanliPolicy');

        $params['policy_object'] = 2;
        $params['main_id'] = $params['main_edit_id'];
        unset($params['main_edit_id']);
        if (OilFanliPolicy::checkExist($params)) {
            throw new \RuntimeException('返利政策已经存在', 2);
        }

        $params['oil_amount_limit'] = $params['oil_amount_limit'] ? $params['oil_amount_limit'] : 0;
        $params['oil_money_limit'] = $params['oil_money_limit'] ? $params['oil_money_limit'] : 0;

        if (in_array(intval($params['fanli_type']), [3, 4])) {
            $stepFanliData = $this->getStepFanliData($params);
            $params['step_fanli_data'] = json_encode($stepFanliData);
        } else {
            $params['step_fanli_data'] = NULL;
        }

        OilFanliPolicy::add($params);

        Response::json(NULL, 0, '添加成功');
    }

    /**
     * 更新返利政策
     */
    public function updateFanliPolicy()
    {
        $params = helper::filterParams();
        Log::info('更新参数--' . var_export($params, TRUE), [], 'fanliPolicy');

        $params['policy_object'] = 2;
        $params['main_id'] = $params['main_edit_id'];
        unset($params['main_edit_id']);

        $info = OilFanliPolicy::checkExist($params);
        if ($info && !(count($info) == 1 && $info[0] == $params['id'])) {
            throw new \RuntimeException('返利政策已经存在', 2);
        }

        $params['oil_amount_limit'] = $params['oil_amount_limit'] ? $params['oil_amount_limit'] : 0;
        $params['oil_money_limit'] = $params['oil_money_limit'] ? $params['oil_money_limit'] : 0;
        isset($params['fanli_money']) && $params['fanli_money'] ? $params['fanli_coe'] = NULL : '';
        isset($params['fanli_coe']) && $params['fanli_coe'] ? $params['fanli_money'] = NULL : '';

        if (in_array(intval($params['fanli_type']), [3, 4])) {
            $params['step_fanli_data'] = $this->getStepFanliData($params);
        } else {
            $params['step_fanli_data'] = [];
        }

        $params['step_fanli_data'] = json_encode($params['step_fanli_data']);


        OilFanliPolicy::edit($params);

        Response::json(NULL, 0, '更新成功');
    }

    /**
     * 批量更新
     */
    public function updateBatch()
    {
        $params = helper::filterParams();
        \helper::argumentCheck(['ids'], $params);
        Log::info('批量更新参数--' . var_export($params, TRUE), [], 'fanliPolicy');
        $data = OilFanliPolicy::filterUpdateParams($params);

        /**********************
         *  开启事务
         *********************/
        Capsule::connection()->beginTransaction();
        try {
            foreach ($data['args'] as $v) {
                //校验返利政策是否存在
                $info = OilFanliPolicy::checkExist($v);
                if ($info && !(count($info) == 1 && $info[0] == $v['id'])) {
                    throw new \RuntimeException('返利政策已经存在', 2);
                }
                $data['data']['id'] = $v['id'];
                if (in_array(intval($data['data']['fanli_type']), [3, 4])) {
                    $data['data']['step_fanli_data'] = $this->getStepFanliData($data['data']);
                } else {
                    $data['data']['step_fanli_data'] = [];
                }
                $data['data']['main_id'] = $v['main_edit_id'];
                unset($data['data']['main_edit_id']);
                $data['data']['step_fanli_data'] = json_encode($data['data']['step_fanli_data']);
                OilFanliPolicy::edit($data['data']);
            }

            /**********************
             *  提交事务
             *********************/
            Capsule::connection()->commit();
        } catch (\Exception $e) {
            /**********************
             *  回滚事务
             *********************/
            Capsule::connection()->rollBack();

            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json(NULL, 0, '批量更新成功');
    }


    /**
     * 删除返利政策
     */
    public function deleteFanliPolicy()
    {
        $params = helper::filterParams();
        $result = OilFanliPolicy::removeBatch($params);
        if ($result) {
            Response::json(NULL, 0, '删除成功');
        } else {
            Response::json(NULL, 2, '删除失败');
        }
    }

    /**
     * 导入文件上传
     * <AUTHOR>
     * @since  2015/10/14
     */
    public function file_upload($file)
    {
        set_time_limit(0);
        if (empty($file) || empty($file['name'])) {
            //未选择文件
            return -9;
        }
        //判断文件类型
        if ($file['name']) {
            $ext = strtolower(trim(substr(strrchr($file['name'], '.'), 1)));
//            if($ext != "xls") {
//                //文件类型不正确
//                return -8;
//            }
        }

        $dir = '../tmp/data';
        //$dir = $this->app->getCacheRoot().'./data';
        if (!is_dir($dir)) {
            helper::createDir($dir, 0777);
        }

        //文件上传
        $tmp_name = $file['tmp_name'];
        $newname = $dir . '/import_' . date('m-d-H-i-s') . '.' . $ext;

        if (@copy($tmp_name, $newname)) {
            @unlink($tmp_name);
        } elseif (@move_uploaded_file($tmp_name, $newname)) {
        } elseif (@rename($tmp_name, $newname)) {
        } else {
            //上传文件失败
            return -7;
        }
        @chmod($newname, 0777);

        return $newname;
    }

    public function checktitle($title)
    {
        $tit = ['返利政策名称', '主卡', '政策对象', '油卡类型', '油品类型', '返利机构', '消费地区', '返利方式', '返利类型', '加油量限制', '金额限制', '起始时间', '截止时间', '返利系数'];
        $title = array_slice($title, 0, 14);
        if ($tit != $title) {
            $data = ['success' => TRUE, 'msg' => '字段类型错误'];
            echo json_encode($data);
            exit;
        }
    }

    /**
     * 单元格数据预处理
     * @param $rowNum
     * @param $fieldName
     * @param $cellValue
     * @return array|string
     */
    private function preImportCellValue($rowNum, $fieldName, $cellValue)
    {
        $data = $cellValue;
        $msg = '';
        if ($rowNum > 1) {
            switch ($fieldName) {
                case 'policy_name':
                    if (!$cellValue) {
                        $msg = '导入失败: 政策名称不能为空';
                    }
                    break;
                case 'main_id':
                    if (!$cellValue) {
                        $msg = '导入失败: 主卡不能为空';
                    } else {
                        $cardMainInfo = \Models\OilCardMain::getByMainNo($cellValue);
                        if (!$cardMainInfo) {
                            $msg = '导入失败: 主卡【' . $cellValue . '】不存在';
                        } else {
                            $data = $cardMainInfo->id;
                        }
                    }
                    break;
                case 'policy_oil_com':
                    if ($cellValue) {
                        $policyObject = \Fuel\Defines\OilCom::getIdByName($cellValue);
                        if (!$policyObject) {
                            $msg = '导入失败: 油卡类型【' . $cellValue . '】不存在';
                        } else {
                            $data = $policyObject;
                        }
                    } else {
                        $msg = '导入失败: 油卡类型不能为空';
                    }
                    break;
                case 'oil_type':
                    if ($cellValue) {
                        $oilType = \Fuel\Defines\OilType::getAll();
                        if (!in_array($cellValue, $oilType)) {
                            $msg = '导入失败: 油品类型【' . $cellValue . '】必须是汽油/柴油/天然气';
                        } else {
                            $oilType = array_flip($oilType);
                            $data = $oilType[$cellValue];
                        }
                    } else {
                        $msg = '导入失败: 油品类型不能为空';
                    }
                    break;
                case 'orgcode':
                    if (!$cellValue) {
                        $msg = '导入失败: 返利机构编码不能为空';
                    }
                    break;
                case 'org_id':
                    if (!$cellValue) {
                        $msg = '导入失败: 返利机构不能为空';
                    }
                    break;
                /*case 'regions_id':
                    if (!$cellValue) {
                        $msg = '导入失败: 消费地区不能为空';
                    } else {
                        $provinceInfo = \Models\OilProvinces::getByProvince($cellValue);
                        if (!$provinceInfo) {
                            $msg = '导入失败: 消费地区【' . $cellValue . '】不存在';
                        } else {
                            $data = $provinceInfo->id;
                        }
                    }
                    break;*/
                case 'fanli_way':
                    if (!$cellValue) {
                        $msg = '导入失败: 返利形式不能为空';
                    } else {
                        $fanliWay = \Fuel\Defines\FanliWay::getAll();
                        if (!in_array($cellValue, $fanliWay)) {
                            $msg = '导入失败: 返利形式【' . $cellValue . '】必须是现金/积分';
                        } else {
                            $fanliWay = array_flip($fanliWay);
                            $data = $fanliWay[$cellValue];
                        }
                    }
                    break;
                case 'fanli_type':
                    if (!$cellValue) {
                        $msg = '导入失败: 返利类型不能为空';
                    } else {
                        $fanliType = \Fuel\Defines\FanliType::getAll();
                        if (!in_array($cellValue, $fanliType)) {
                            $msg = '导入失败: 返利类型【' . $cellValue . '】不存在';
                        } else {
                            $fanliType = array_flip($fanliType);
                            $data = $fanliType[$cellValue];
                        }
                    }
                    break;
                case 'start_time':
                    if (!$cellValue) {
                        $msg = '导入失败: 起始时间不能为空';
                    }
                    break;
                case 'end_time':
                    if (!$cellValue) {
                        $msg = '导入失败: 截止时间不能为空';
                    }
                    break;
                case 'coe_unit':
                    if ($cellValue) {
                        $coeUnit = \Fuel\Defines\CoeUnit::getAll();
                        if (!in_array($cellValue, $coeUnit)) {
                            $msg = '导入失败: 系数单位【' . $cellValue . '】不存在';
                        } else {
                            $coeUnit = array_flip($coeUnit);
                            $data = $coeUnit[$cellValue];
                        }
                    }
                    break;
                default :
                    $data = $cellValue;
            }
        }

        if ($msg) {
            throw new \RuntimeException($msg, 2);
        }
        return $data;
    }

    private function convertTableData($result)
    {
        $data = $group = [];
        $nowTime = helper::nowTime();
        $row = 2;
        foreach ($result as $v) {
            $tmp = [];
            $tmp['main_id'] = $v['main_id'];
            $tmp['policy_name'] = $v['policy_name'];
            $tmp['policy_object'] = 2;
            $tmp['policy_oil_com'] = $v['policy_oil_com'];
            $tmp['oil_type'] = $v['oil_type'];
            if ($v['orgcode'] && !$v['org_name']) {
                throw new \RuntimeException('导入失败: 返利政策[' . $v['policy_name'] . ']的机构缺失', 3);
            } elseif (!$v['orgcode'] && $v['org_name']) {
                throw new \RuntimeException('导入失败: 返利政策[' . $v['policy_name'] . ']的机构编码缺失', 3);
            } elseif ($v['orgcode'] && $v['org_name']) {
                $orgInfo = \Models\OilOrg::getByOrgcode(['orgcode' => $v['orgcode']]);
                if ($orgInfo->org_name != $v['org_name']) {
                    throw new \RuntimeException('导入失败: 返利政策[' . $v['policy_name'] . ']的机构与机构编码不对应', 3);
                }
                $tmp['org_id'] = $orgInfo->id;
            }
            if (in_array($tmp['policy_oil_com'], \Fuel\Defines\OilCom::getFirstList())) {
                if (empty($v['station_name'])) {
                    throw new \RuntimeException('导入失败: 返利政策[' . $v['policy_name'] . ']可用油站不能为空', 3);
                } else {
                    $condition['station_name'] = $v['station_name'];
                    $condition['is_del'] = 0;
                    $hasInfo = \Models\OilStation::getStationInfo($condition);
                    if (count($hasInfo) == 0) {
                        throw new \RuntimeException('导入失败: 返利政策[' . $v['policy_name'] . ']可用油站不存在', 3);
                    }
                    $tmp['station_id'] = $hasInfo[$v['station_name']];
                }
            } else {
                if (!$v['regions_id']) {
                    throw new \RuntimeException('导入失败: 消费地区不能为空', 3);
                } else {
                    $provinceInfo = \Models\OilProvinces::getByProvince($v['regions_id']);
                    if (!$provinceInfo) {
                        throw new \RuntimeException('导入失败: 消费地区【' . $v['regions_id'] . '】不存在', 3);
                    }
                    $tmp['regions_id'] = $provinceInfo->id;
                }
            }
            $tmp['fanli_way'] = $v['fanli_way'];
            $tmp['fanli_type'] = $v['fanli_type'];
            if (in_array(intval($v['fanli_type']), [1, 2]) && $v['fanli_value'] == '') {
                throw new \RuntimeException('返利系数/金额不能为空', 2);
            }
            if ($v['fanli_type'] == 1) {//按金额
                $tmp['fanli_coe'] = $v['fanli_value'];
                $tmp['fanli_money'] = NULL;
            } elseif ($v['fanli_type'] == 2) {//按加油量
                $tmp['fanli_coe'] = NULL;
                $tmp['fanli_money'] = $v['fanli_value'];
            } elseif (in_array(intval($v['fanli_type']), [3, 4])) {//阶梯返利
                if (!$v['coe_unit']) {
                    throw new \RuntimeException('系数单位不能为空', 2);
                } else {
                    $tmp['coe_unit'] = $v['coe_unit'];
                }
                //校验阶梯返利数据
                $this->checkStepFanliInput($v);
                //组装阶梯返利数据
                $tmp['step_fanli_data'] = $this->getStepFanliForImport($v);
                $tmp['add_fanli_edu'] = $v['add_fanli_edu'];//加油量叠加优惠
                $tmp['fanli_min_money'] = $v['fanli_min_money'];//免惠最低价
            }
            $tmp['oil_amount_limit'] = $v['oil_amount_limit'];
            $tmp['oil_money_limit'] = $v['oil_money_limit'];
            $tmp['start_time'] = $v['start_time'];
            $tmp['end_time'] = $v['end_time'];
            $tmp['start_time'] = $v['start_time'];
            $tmp['creator_id'] = $this->app->myAdmin->id;
            $tmp['createtime'] = $nowTime;
            //校验唯一性
            if (OilFanliPolicy::checkExist($tmp)) {
                throw new \RuntimeException('第' . $row . '行返利政策 [ ' . $v['policy_name'] . ' ] 已经存在', 2);
            }

            if (in_array($tmp['policy_oil_com'], \Fuel\Defines\OilCom::getFirstList())) {
                $group[$tmp['main_id'] . $tmp['policy_oil_com'] . $tmp['oil_type'] . $tmp['org_id'] . $tmp['station_id'] . $tmp['start_time'] . $tmp['end_time']][] = $row;
            } else {
                $group[$tmp['main_id'] . $tmp['policy_oil_com'] . $tmp['oil_type'] . $tmp['org_id'] . $tmp['regions_id'] . $tmp['start_time'] . $tmp['end_time']][] = $row;
            }

            $row++;
            $data[] = $tmp;

        }

        Log::error('group' . var_export($group, true), [], 'fanliCheck');

        if ($group) {
            foreach ($group as $value) {
                if (count($value) > 1) {
                    throw new \RuntimeException('第' . implode(',', $value) . '行返利政策重复', 2);
                }
            }
        }

        return $data;
    }

    /**
     * @title 校验阶梯返利数据
     * @param $stepFanli
     * <AUTHOR>
     */
    private function checkStepFanliInput($stepFanli)
    {
        /*********************校验阶梯区间数据**********************/
        if ($stepFanli['fanli_level1_gt'] === '') {
            throw new \RuntimeException('一级返利区间开始不能为空', 2);
        }
        if (!$stepFanli['fanli_level1_le']) {
            throw new \RuntimeException('一级返利区间结束不能为空', 2);
        }
        if (!$stepFanli['fanli_level2_gt']) {
            throw new \RuntimeException('二级返利区间开始不能为空', 2);
        }
        if (!$stepFanli['fanli_level2_le']) {
            throw new \RuntimeException('二级返利区间结束不能为空', 2);
        }
        $msg = '上级返利区间的结束值必须等于下级返利区间的起始值';
        if (intval($stepFanli['fanli_level1_gt']) >= intval($stepFanli['fanli_level1_le'])) {
            throw new \RuntimeException($msg, 2);
        }
        if (intval($stepFanli['fanli_level2_gt']) != intval($stepFanli['fanli_level1_le'])) {
            throw new \RuntimeException($msg, 2);
        }
        if (intval($stepFanli['fanli_level2_gt']) >= intval($stepFanli['fanli_level2_le'])) {
            throw new \RuntimeException($msg, 2);
        }
        if ($stepFanli['fanli_level3_gt']) {
            if (intval($stepFanli['fanli_level3_gt']) != intval($stepFanli['fanli_level2_le']) || !$stepFanli['fanli_level3_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level3_le']) {
            if (!$stepFanli['fanli_level3_gt'] || intval($stepFanli['fanli_level3_gt']) >= intval($stepFanli['fanli_level3_le'])) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level4_gt']) {
            if ($stepFanli['fanli_level4_gt'] != $stepFanli['fanli_level3_le'] || !$stepFanli['fanli_level4_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level4_le']) {
            if (!$stepFanli['fanli_level4_gt'] || $stepFanli['fanli_level4_gt'] >= $stepFanli['fanli_level4_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level5_gt']) {
            if ($stepFanli['fanli_level5_gt'] != $stepFanli['fanli_level4_le'] || !$stepFanli['fanli_level5_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level5_le']) {
            if (!$stepFanli['fanli_level5_gt'] || $stepFanli['fanli_level5_gt'] >= $stepFanli['fanli_level5_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }
        /************************校验返利系数/金额数据*********************/
        if (!$stepFanli['fanli_edu_level1']) {
            throw new \RuntimeException('一级返利额度不能为空', 2);
        }
        if (!$stepFanli['fanli_edu_level2']) {
            throw new \RuntimeException('二级返利额度不能为空', 2);
        }
        if ($stepFanli['fanli_level3_gt'] && $stepFanli['fanli_level3_le'] && !$stepFanli['fanli_edu_level3']) {
            throw new \RuntimeException('三级返利额度不能为空', 2);
        }
        if ($stepFanli['fanli_level4_gt'] && $stepFanli['fanli_level4_le'] && !$stepFanli['fanli_edu_level4']) {
            throw new \RuntimeException('四级返利额度不能为空', 2);
        }
        if ($stepFanli['fanli_level5_gt'] && $stepFanli['fanli_level5_le'] && !$stepFanli['fanli_edu_level5']) {
            throw new \RuntimeException('五级返利额度不能为空', 2);
        }
    }

    private function getStepFanliForImport($v)
    {
        $stepFanli = $this->getStepFanliData($v);
        if ($v['coe_unit'] == 1) {//按现金百分比
            $stepFanli['fanli_coe_level1'] = $v['fanli_edu_level1'];
            $stepFanli['fanli_coe_level2'] = $v['fanli_edu_level2'];
            $stepFanli['fanli_coe_level3'] = $v['fanli_edu_level3'];
            $stepFanli['fanli_coe_level4'] = $v['fanli_edu_level4'];
            $stepFanli['fanli_coe_level5'] = $v['fanli_edu_level5'];
        } elseif ($v['coe_unit'] == 2) {//按每升返利金额
            $stepFanli['fanli_money_level1'] = $v['fanli_edu_level1'];
            $stepFanli['fanli_money_level2'] = $v['fanli_edu_level2'];
            $stepFanli['fanli_money_level3'] = $v['fanli_edu_level3'];
            $stepFanli['fanli_money_level4'] = $v['fanli_edu_level4'];
            $stepFanli['fanli_money_level5'] = $v['fanli_edu_level5'];
        }

        return json_encode($stepFanli);
    }

    /**
     * @title 导入
     * <AUTHOR>
     */
    public function batchImport()
    {
        $params = helper::filterParams();

        if (isset($params['filePath']) && $params['filePath']) {
            //$upload = $params['filePath'];
            $upload = __DIR__ . DIRECTORY_SEPARATOR . $params['filePath'];
        } else {
            //上传文件
            $upload = $this->file_upload($_FILES['userfile']);
        }

        $fieldMap = [
            'policy_name' => '返利政策名称(必填)',
            'main_id' => '主卡(必填)',
            'station_name' => '可用油站',
            'policy_oil_com' => '油卡类型(必填)',
            'oil_type' => '油品类型(必填)',
            'orgcode' => '返利机构编码(必填)',
            'org_name' => '返利机构(必填)',
            'regions_id' => '消费地区(必填)',
            'fanli_way' => '返利形式(必填)',
            'fanli_type' => '返利类型(必填)',
            'oil_amount_limit' => '加油量限制',
            'oil_money_limit' => '金额限制',
            'start_time' => '起始时间(必填)',
            'end_time' => '截止时间(必填)',
            'fanli_value' => '返利系数/金额',
            'coe_unit' => '系数单位',
            'fanli_level1_gt' => '一级返利区间开始',
            'fanli_level1_le' => '一级返利区间结束',
            'fanli_edu_level1' => '一级返利额度',
            'fanli_level2_gt' => '二级返利区间开始',
            'fanli_level2_le' => '二级返利区间结束',
            'fanli_edu_level2' => '二级返利额度',
            'fanli_level3_gt' => '三级返利区间开始',
            'fanli_level3_le' => '三级返利区间结束',
            'fanli_edu_level3' => '三级返利额度',
            'fanli_level4_gt' => '四级返利区间开始',
            'fanli_level4_le' => '四级返利区间结束',
            'fanli_edu_level4' => '四级返利额度',
            'fanli_level5_gt' => '五级返利区间开始',
            'fanli_level5_le' => '五级返利区间结束',
            'fanli_edu_level5' => '五级返利额度',
            'fanli_min_money' => '免惠最低价',
            'add_fanli_edu' => '加油量叠加优惠',
        ];

        $excelParams = [
            'filePath' => $upload,
            'fieldsMap' => array_flip($fieldMap),
            'ignore' => false,
        ];

        $result = ExcelReader::read($excelParams, function ($rowNum, $fieldName, $cellValue) {
            return $this->preImportCellValue($rowNum, $fieldName, $cellValue);
        });
        Log::error('$result--' . var_export($result, TRUE), [], 'fanliPolicy');
        $tableData = $this->convertTableData($result[0]);
        //var_dump($tableData);exit;
        Log::error('$tableData--' . var_export($tableData, TRUE), [], 'fanliPolicy');

        Capsule::connection()->beginTransaction();
        try {
            foreach ($tableData as $v) {
                if (OilFanliPolicy::checkExist($v)) {
                    throw new \RuntimeException('返利政策已经存在', 2);
                }
                OilFanliPolicy::add($v);
            }

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            Log::error('导入失败--' . $e->getMessage(), [], 'fanliPolicy');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json(NULL, 0, '导入成功');
    }

    public function batchImportBak()
    {
        $params = helper::filterParams();

        if (isset($params['filePath']) && $params['filePath']) {
            $upload = $params['filePath'];
        } else {
            //上传文件
            $upload = $this->file_upload($_FILES['userfile']);
        }

        $fieldMap = [
            'policy_name' => '返利政策名称(必填)',
            'main_id' => '主卡(必填)',
            'policy_object' => '政策对象(必填)',
            'policy_oil_com' => '油卡类型(必填)',
            'oil_type' => '油品类型(必填)',
            'orgcode' => '返利机构编码(必填)',
            'org_name' => '返利机构(必填)',
            'regions_id' => '消费地区(必填)',
            'fanli_way' => '返利形式(必填)',
            'fanli_type' => '返利类型(必填)',
            'oil_amount_limit' => '加油量限制',
            'oil_money_limit' => '金额限制',
            'start_time' => '起始时间(必填)',
            'end_time' => '截止时间(必填)',
            'fanli_value' => '返利系数/金额',
            'coe_unit' => '系数单位',
            'fanli_level1_gt' => '一级返利区间开始',
            'fanli_level1_le' => '一级返利区间结束',
            'fanli_edu_level1' => '一级返利额度',
            'fanli_level2_gt' => '二级返利区间开始',
            'fanli_level2_le' => '二级返利区间结束',
            'fanli_edu_level2' => '二级返利额度',
            'fanli_level3_gt' => '三级返利区间开始',
            'fanli_level3_le' => '三级返利区间结束',
            'fanli_edu_level3' => '三级返利额度',
            'fanli_level4_gt' => '四级返利区间开始',
            'fanli_level4_le' => '四级返利区间结束',
            'fanli_edu_level4' => '四级返利额度',
            'fanli_level5_gt' => '五级返利区间开始',
            'fanli_level5_le' => '五级返利区间结束',
            'fanli_edu_level5' => '五级返利额度',
            'fanli_min_money' => '免惠最低价',
            'add_fanli_edu' => '加油量叠加优惠',
        ];

        $excelParams = [
            'filePath' => $upload,
            'fieldsMap' => array_flip($fieldMap),
        ];

        $result = ExcelReader::read($excelParams, function ($rowNum, $fieldName, $cellValue) {
            return $this->preImportCellValue($rowNum, $fieldName, $cellValue);
        });
        Log::info('$result--' . var_export($result, TRUE), [], 'fanliPolicy');
        $tableData = $this->convertTableData($result[0]);
        Log::info('$tableData--' . var_export($tableData, TRUE), [], 'fanliPolicy');

        /****************************
         *  开启事务
         ****************************/
        Capsule::connection()->getPdo()->beginTransaction();
        try {
            OilFanliPolicy::batchInsertByPdo($tableData);

            /****************************
             *  提交事务
             ****************************/
            Capsule::connection()->getPdo()->commit();
        } catch (Exception $e) {
            /****************************
             *  回滚事务
             ****************************/
            Capsule::connection()->getPdo()->rollBack();
            Log::error('导入失败--' . $e->getMessage(), [], 'fanliPolicy');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json(NULL, 0, '导入成功');
    }

    public function dataConvert($arr)
    {
        //数据转换 policy_object 政策对象
        if ($arr['policy_object'] == '主卡') {
            $arr['policy_object'] = 1;
        } else {
            $arr['policy_object'] = 2;
        }
        //数据转换 policy_object 政策对象
        if ($arr['policy_oil_com'] == '中石化') {
            $arr['policy_oil_com'] = 1;
        } elseif ($arr['policy_oil_com'] == '中石油') {
            $arr['policy_oil_com'] = 2;
        } else {
            $arr['policy_oil_com'] = 3;
        }
        //数据转换 oil_type油品类型
        if ($arr['oil_type'] == '汽油') {
            $arr['oil_type'] = 1;
        } elseif ($arr['oil_type'] == '柴油') {
            $arr['oil_type'] = 2;
        } else {
            $arr['oil_type'] = 5;
        }
        //数据转换 fanli_way返利方式
        if ($arr['fanli_way'] == '现金') {
            $arr['fanli_way'] = 1;
        } else {
            $arr['fanli_way'] = 2;
        }

        //数据转换 fanli_type返利方式
        if ($arr['fanli_type'] == '按金额') {
            $arr['fanli_type'] = 1;

        } else {
            $arr['fanli_type'] = 2;
            $arr['fanli_money'] = $arr['fanli_coe'];
            $arr['fanli_coe'] = NULL;
        }
        //机构名称
        $orgexist = $this->checkOrgIsExist($arr['org_id']);
        //echo $orgexist.'<br>';
        $arr['org_id'] = $orgexist;
        //echo $arr['org_id'].'<br>';
        //消费地区
        $regionexist = $this->checkRegionIsExist($arr['regions_id']);
        $arr['regions_id'] = $regionexist;
        //主卡id
        $mainid = $this->checkmainCardExist($arr['main_id']);
        $arr['main_id'] = $mainid;
        //时间
        is_numeric($arr['start_time']) ? $arr['start_time'] = gmdate("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP
        ($arr['start_time'])) : $arr['start_time'];
        is_numeric($arr['end_time']) ? $arr['end_time'] = gmdate("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($arr['end_time'])) : $arr['end_time'];

        return $arr;

    }


    public function checkImportValues($arr)
    {
        //校验政策对象
        if (!($arr['policy_object'] == '主卡' || $arr['policy_object'] == '副卡')) {
            //echo 'policy_object error'."<br>";
            return FALSE;
        }
        //校验油卡类型
        if (!($arr['policy_oil_com'] == '中石化' || $arr['policy_oil_com'] == '中石油' || $arr['policy_oil_com'] == '中车油')) {
            //echo 'policy_oil_com error'."<br>";
            return FALSE;
        }

        //校验油品类型
        if (!($arr['oil_type'] == '汽油' || $arr['oil_type'] == '柴油')) {
            // echo 'oil_type error'."<br>";
            return FALSE;
        }
        //校验返利方式
        if (!($arr['fanli_way'] == '现金' || $arr['fanli_way'] == '积分')) {
            //echo 'fanli_way error'."<br>";
            return FALSE;
        }
        //校验返利类型
        if (!($arr['fanli_type'] == '按金额' || $arr['fanli_type'] == '按加油量')) {
            //echo 'fanli_type error'."<br>";
            return FALSE;
        }
        //校验返利折扣
        if (!($arr['fanli_coe'] >= 0.01 && $arr['fanli_coe'] <= 9.99)) {
            //echo 'fanli_coe error'."<br>";
            return FALSE;
        }
        //检验起始时间截止时间
        is_numeric($arr['start_time']) ? $arr['start_time'] = gmdate("Y-m-d", PHPExcel_Shared_Date::ExcelToPHP($arr['start_time'])) : $arr['start_time'];
        is_numeric($arr['end_time']) ? $arr['end_time'] = gmdate("Y-m-d", PHPExcel_Shared_Date::ExcelToPHP($arr['end_time'])) : $arr['end_time'];
        if ($arr['start_time'] > $arr['end_time']) {
            return FALSE;
        }
        //判断金额限制
        if (!empty($arr['oil_money_limit'])) {
            // echo 'not empty';
            if (!is_numeric($arr['oil_money_limit'])) {
                //echo 'money_limit error1' . "<br>";
                return FALSE;
            } elseif ($arr['oil_money_limit'] > 2147483647) {
                // echo 'money_limit error3' . "<br>";
                return FALSE;
            }
        }
        //判断油量限制
        if (!empty($arr['oil_amount_limit'])) {
            // echo 'not empty';
            if (!is_numeric($arr['oil_amount_limit'])) {
                //echo 'money_limit error1' . "<br>";
                return FALSE;
            } elseif ($arr['oil_amount_limit'] > 2147483647) {
                // echo 'money_limit error3' . "<br>";
                return FALSE;
            }
        }
        //判断返利机构是否存在
        $orgexist = $this->checkOrgIsExist($arr['org_id']);
        if (!$orgexist) {
            //echo 'org error'."<br>";
            return FALSE;
        }
        //判断返利机构是否存在
        $orgcodeexist = $this->checkOrgcodeIsExist($arr['orgcode'] . ',' . $arr['org_id']);
        if (!$orgcodeexist) {
            return FALSE;
        }

        //判断消费地区是否存在  
        $regionexist = $this->checkRegionIsExist($arr['regions_id']);
        if (!$regionexist) {
            //echo 'region error'."<br>";
            return FALSE;
        }
        //判断主卡是否存在
        $maincardexist = $this->checkmainCardExist($arr['main_id']);
        if (!$maincardexist) {
            //echo 'maincard error'."<br>";
            return FALSE;
        }

        return TRUE;
    }

    public function checkOrgIsExist($org)
    {
        $orglist = $this->oilOrg();

        foreach ($orglist as $key => $value) {
            if ($org == $value) {
                return $key;
            }
        }

        return FALSE;
    }

    /**
     * 判断机构编码是都存在和机构编码是否与机构一致
     */
    public function checkOrgcodeIsExist($orgcode)
    {
        $orglist = $this->oilOrgde();
        if (isset($orglist) && !empty($orglist)) {
            foreach ($orglist as $key => $value) {
                if ($orgcode == $value) {
                    return TRUE;
                }
            }
        }

        return FALSE;
    }

    /**
     *获取机构名称和机构编码
     */
    public function oilOrgde()
    {
        $sort = '';
        $where = 'is_del=0';
        $orgs = $this->loadModel('oil_org')->search($where, $sort, NULL);
        $array = $this->objarray_to_array($orgs);
        foreach ($array['data'] as $key => $value) {
            $orgname[$value['id']] = $value['orgcode'] . ',' . $value['org_name'];
        }

        return $orgname;
    }

    public function checkRegionIsExist($region)
    {
        $regionlist = $this->oilProvinces();
//        echo '<pre>';
//        print_r($regionlist);
//        echo '</pre>';


        foreach ($regionlist as $key => $value) {
            if ($region == $value) {
                return $key;
            }
        }

        return FALSE;
    }

    public function checkmainCardExist($cardid)
    {
        $maincard = $this->getMainCard();
        foreach ($maincard as $key => $value) {
            if ($cardid == $value['main_no']) {
                return $value['id'];
            }
        }

        return FALSE;
    }

    function excelTime($date, $time = FALSE)
    {
        if (function_exists('GregorianToJD')) {
            if (is_numeric($date)) {
                $jd = GregorianToJD(1, 1, 1970);
                $gregorian = JDToGregorian($jd + intval($date) - 25569);
                $date = explode('/', $gregorian);
                $date_str = str_pad($date [2], 4, '0', STR_PAD_LEFT)
                    . "-" . str_pad($date [0], 2, '0', STR_PAD_LEFT)
                    . "-" . str_pad($date [1], 2, '0', STR_PAD_LEFT)
                    . ($time ? " 00:00:00" : '');

                return $date_str;
            }
        } else {
            $date = $date > 25568 ? $date + 1 : 25569;
            /*There was a bug if Converting date before 1-1-1970 (tstamp 0)*/
            $ofs = (70 * 365 + 17 + 2) * 86400;
            $date = date("Y-m-d", ($date * 86400) - $ofs) . ($time ? " 00:00:00" : '');
        }

        return $date;
    }

    /**
     * 返利模板下载
     */
    public function TplDownload()
    {
        $file_xls = $this->app->getAppRoot() . 'www' . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . 'fanli.xls';    //   文件的保存路径
        $example_name = basename($file_xls);  //获取文件名
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename=' . mb_convert_encoding($example_name, "gb2312", "utf-8"));  //转换文件名的编码
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        ob_clean();
        header('Content-Length: ' . filesize($file_xls));
        flush();
        readfile($file_xls);
    }

    /**
     * 错误模板下载
     */
    public function errorInfoDownload()
    {
        $params = helper::filterParams();
        if (!isset($params['data']) || !$params['data']) {
            throw new \RuntimeException('错误内容为空', 2);
        }
        $params = json_decode($params['data']);
        $params = $this->objarray_to_array($params);

        $title = [
            'policy_name' => "返利政策名称",
            'main_id' => "主卡",
            'policy_object' => "政策对象",
            'policy_oil_com' => "油卡类型",
            'oil_type' => "油品类型",
            'orgcode' => "返利机构编码",
            'org_id' => "返利机构",
            'regions_id' => "消费地区",
            'fanli_way' => "返利方式",
            'fanli_type' => "返利类型",
            'oil_amount_limit' => "加油量限制",
            'oil_money_limit' => "金额限制",
            'start_time' => "起始时间",
            'end_time' => "截止时间",
            'fanli_coe' => "返利系数",
            'errorinfo' => "错误",
        ];

        $filedsName = array_keys($title);

        $result = array_slice($params['excelData'], 1);
        $data = [];
        foreach ($result as $k => $v) {
            $data[$k] = [];
            foreach ($v as $a => $b) {
                $data[$k][$filedsName[$a]] = $b;
            }
        }
        $filePath = ExcelWriter::exportXls(
            [
                'fileName' => date('YmdHis') . "_导入结果",
                'fileExt' => 'xls',
                'sheetName' => "返利政策导入",
                'download' => 1,
                'title' => $title,
                'data' => $data,
            ],
            function ($phpExcelObj, $data, $lineCell) {
                if ($data['name'] == 'main_id') {
                    $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            }
        );

        echo json_encode(['filePath' => $filePath]);

    }

    /**
     * 通过主卡,获取绑定积分主卡
     * <AUTHOR> Du
     * @since  2016/2/29
     */
    public function getMainJifenNo()
    {
        $params = helper::filterParams();
        $id = isset($params['id']) ? $params['id'] : 0;
        //$id=1;
        $result = new stdClass();
        if ($id) {
            $result = $this->oil_fanli_policy->getMainJifenNo($id);
        }

        echo json_encode($result);
    }

    /**
     * @title   获取返利类型
     * <AUTHOR>
     */
    public function getFanliType()
    {
        $info = FanliType::getAll();
        $data = [];
        foreach ($info as $k => $v) {
            $tmp = [];
            $tmp['key'] = $k;
            $tmp['value'] = $v;
            $data[] = $tmp;
        }

        Response::json($data);
    }

    /**
     * @title   探测未维护的返利政策
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function checkPolicy()
    {
        $userInfo = isset($this->app->myAdmin) && $this->app->myAdmin ? $this->app->myAdmin : [];

        $params = helper::filterParams();
        if (isset($params['isWeek']) && $params['isWeek'] == 1) {
            $params['end_time'] = date("Y-m-d");
            $params['start_time'] = date("Y-m-d", strtotime($params['end_time'] . "-1 week"));
        }
        $jobs = (new \Jobs\ExportCheckPolicyJob($params))
            ->setTaskName('返利政策探测')
            ->setUserInfo($userInfo)
            ->onQueue('export')
            ->dispatch();

//        Response::json($jobs);
        Response::json(["redirect_url"=>$jobs->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
    }

    /**
     * @title 返利政策导入新方式
     * <AUTHOR>
     */
    public function batchImportNew()
    {
        $params = helper::filterParams();

        if (isset($params['filePath']) && $params['filePath']) {
            //$upload = $params['filePath'];
            $upload = __DIR__ . DIRECTORY_SEPARATOR . $params['filePath'];
        } else {
            //上传文件
            $upload_path = $this->file_upload($_FILES['userfile']);
            $upload = APP_ROOT . trim($upload_path, ".."); //异步方式，需要转换文件路径
        }

        //$path = "/data/web_data/web/app/gsp_fuel/app/admin/www/download/a10533b1497da66dde45f9219879d6b67146.csv";
        //$obj = (new \Jobs\ImportFanliPolicyJob(['data' => $path, "userInfo" => $this->app->myAdmin]));
        //$obj->handle();

        if (!empty($upload)) {

            //需要把文件上传到OSS
            $url = (new \commonModel())->fileUploadToOss($upload);
            Log::error('上传到OSS结果:' . $url, [$upload], 'fanliImport_');

            $task = (new \Jobs\ImportFanliPolicyJob(['data' => $url, "userInfo" => $this->app->myAdmin]))
                ->setTaskName('返利政策导入')
                ->setUserInfo($this->app->myAdmin)
                ->onQueue('default')
                ->setTries(3)
                ->dispatch();
            Log::error(__METHOD__ . "uploadFile:" . $upload, [$task], "fanliImport_");
            Response::json(NULL, 0, '导入任务已接收,系统处理中。。。');
        } else {
            Response::json(NULL, 2, '文件上传失败，请重试');
        }
    }
}