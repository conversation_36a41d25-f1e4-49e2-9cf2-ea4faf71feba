<?php
/**
 * 返利免惠最低价 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/04/14
 * Time: 15:05:08
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilRebateMinPrice;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_rebate_min_price extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();
        if (!empty($params['is_bind'])) {
            $resBind = (new \Fuel\Service\RebateMinPriceService())->getBindOrUnBindIds();
            if ($params['is_bind'] == 1) {
                $filterIds = $resBind['bind_ids'];
            } else {
                $filterIds = $resBind['unbind_ids'];
            }
            if (!empty($filterIds)) {
                $params['idIn'] = $filterIds;
            } else {
                $params['id'] = -1;
            }
        }
        if (!empty($params['ids'])) {
            $params['idIn'] = explode(',', $params['ids']);
        }
        $data = OilRebateMinPrice::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    public function exportRebateCalcuateData()
    {
        $params = helper::filterParams();
        $params['_export'] = 1;

        $task = (new \Jobs\ExportRebateMinPriceJob($params))
            ->setTaskName('免惠最低价（下游）')
            ->setUserInfo($this->app->myAdmin)
            ->onQueue('export')
            ->dispatch();

        Response::json(["redirect_url"=>$task->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '返利免惠最低价_' . date("YmdHis"),
            'sheetName' => '返利免惠最低价',
            'title' => [
            'id'   =>  'ID',
'name'   =>  '名称',
'content'   =>  '免惠最低价json数据',
'start_time'   =>  '开始时间',
'end_time'   =>  '结束时间',
'createtime'   =>  '创建时间',
'updatetime'   =>  '更新时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilRebateMinPrice::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = (new \Fuel\Service\RebateMinPriceService())->create($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = (new \Fuel\Service\RebateMinPriceService())->update($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = (new \Fuel\Service\RebateMinPriceService())->delete($params);

        Response::json($data,0,'删除成功');
    }

}