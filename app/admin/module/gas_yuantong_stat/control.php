<?php
/**
 * gas_yuantong_stat Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/10/28
 * Time: 19:40:30
 */

use Illuminate\Database\Capsule\Manager as Capsule;
use Models\GasYuantongStat;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class gas_yuantong_stat extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     *
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = GasYuantongStat::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $this->exportList($data);
        } else {
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     *
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName'  => 'gas_yuantong_stat_' . date("YmdHis"),
            'sheetName' => 'gas_yuantong_stat',
            'title'     => [
                'id'             => 'id',
                'code'           => '圆通在G7能源联合站消费的站点编码',
                'day'            => '统计日期',
                'oil_type'       => '油品类型 1柴油 2汽油 5天然气 100 未知',
                'oil_type_value' => 'oil_type_value',
                'trade_money'    => '交易金额（应付金额）',
                'trade_num'      => '交易数量',
                'createtime'     => 'createtime',
                'updatetime'     => 'updatetime'
            ],
            'data'      => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     *
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = GasYuantongStat::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     *
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data   = GasYuantongStat::add($params);

        Response::json($data, 0, '添加成功');
    }

    /**
     * 编辑
     *
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = GasYuantongStat::edit($params);

        Response::json($data, 0, '编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     *
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = GasYuantongStat::remove($params);

        Response::json($data);
    }

}