<?php
/**
 * oil_card_apply_template Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/07/11
 * Time: 16:54:42
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCardApplyTemplate;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_card_apply_template extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['orgcode'], $params);

        $orgInfo = \Models\OilOrg::getByOrgcode($params['orgcode']);
        if(!$orgInfo){
            throw new \RuntimeException('请设置所属机构',2);
        }
        //仅支持查看当前机构的数据
        $params['org_id'] = $orgInfo->id;
        unset($params['orgcode']);

        $data = OilCardApplyTemplate::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => 'oil_card_apply_template_' . date("YmdHis"),
            'sheetName' => 'oil_card_apply_template',
            'title' => [
                'id'   =>  'id',
                'qr_uuid'   =>  '二维码唯一标识',
                'name'   =>  '二维码名称',
                'oil_com'   =>  '油卡类型 20:充值账户,21:共享账户',
                'org_id'   =>  '创建机构id',
                'org_id_fanli'   =>  '返利机构id',
                'paylimit'   =>  '消费限制 1启用，非1禁用',
                'day_top'   =>  '当天加油上限',
                'oil_top'   =>  '单次加油上限',
                'month_top'   =>  '月加油限额',
                'set_passwd'   =>  '1：开启消费密码，2：免密',
                'vice_password'   =>  '卡密码',
                'status'   =>  '1正常，2：作废',
                'data_from'   =>  '来源（1.gsp，2.g7s，3.app）',
                'createtime'   =>  '创建时间',
                'updatetime'   =>  '更新时间',
                'creator_id'   =>  '创建人ID',
                'creator_name'   =>  '创建人名称',
                'last_operator_id'   =>  '最后修改者id',
                'last_operator'   =>  '最后修改者姓名'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['uuid'], $params);
        $data = OilCardApplyTemplate::getByUuidLock($params);

        $orgInfo = \Models\OilOrg::getById(["id"=>$data->org_id]);
        $data->org_name = $orgInfo->org_name;
        $data->orgcode = $orgInfo->orgcode;
        $fanliOrgInfo = \Models\OilOrg::getById(['id'=>$data->org_id_fanli]);
        $data->fanli_org_name = $fanliOrgInfo->org_name;
        $data->fanli_org_code = $fanliOrgInfo->orgcode;

        if($data->oil_com == 21) {
            if (substr($data->account_no, 0, 3) == '208') {
                $creditAccount = \Models\OilCreditAccount::getByAccountNoWithProvider($data->account_no);
                $data->account_name = $creditAccount->CreditProvider->name ? $creditAccount->CreditProvider->name : null;
            } else {
                $data->account_name = 'G7油品账户';
            }
        }else{
            $data->account_name = $data->fanli_org_name;
        }

        $data->id = null;
        $data->uuid = $data->qr_uuid;
        $data->qr_uuid = null;
        Response::json($data);
    }

    /**
     * 新增or编辑
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['create_orgcode','fanli_orgcode','name'], $params);
        $isAdd = true;
        if(isset($params['uuid']) && !empty($params['uuid'])){
            $info = OilCardApplyTemplate::getByUuidLock($params);
            if(!$info){
                throw new \RuntimeException('数据不合法',2);
            }
            $isAdd = false;
        }
        $params['oil_com'] = $params['oil_com'] == 21 ? 21 : 20;
        $params['set_passwd'] = $params['set_passwd'] == 1 ? 1 : 2;
        $params['paylimit'] = $params['paylimit'] == 1 ? 1 : 0;
        $params['able_transfer'] = $params['able_transfer'] == 1 ? 1 : 2;
        if( isset($params['paylimit']) && $params['paylimit'] == 1){
            if(empty($params['day_top']) || empty($params['oil_top']) || empty($params['month_top'])){
                throw new \RuntimeException('请完善消费限制',2);
            }
        }
        if(isset($params['set_passwd']) && $params['set_passwd'] == 1){
            if(empty($params['vice_password'])){
                throw new \RuntimeException('请完善消费密码',2);
            }
            if ( !preg_match('/^[0-9]{6}$/i',$params['vice_password'])) {
                throw new \RuntimeException('请设置6位数字消费密码',2);
            }
        }

        if(strtoupper(substr($params['create_orgcode'],0,6)) != strtoupper(substr($params['fanli_orgcode'],0,6))){
            throw new \RuntimeException('不允许跨机构设置',2);
        }

        if($params['oil_com'] == 21 && empty($params['account_no'])){
            throw new \RuntimeException('请设置扣款账户',2);
        }

        $orgInfo = \Models\OilOrg::getByOrgcode($params['create_orgcode']);
        if(!$orgInfo){
            throw new \RuntimeException('请设置所属机构',2);
        }
        $fanliInfo = \Models\OilOrg::getByOrgcode($params['fanli_orgcode']);
        if(!$fanliInfo){
            throw new \RuntimeException('请设置返利机构',2);
        }
        $params['org_id'] = $orgInfo->id;
        $params['org_id_fanli'] = $fanliInfo->id;
        $params['last_operator_id'] = $params['other_creator_id'];
        $params['last_operator'] = $params['other_creator'];
        $params['updatetime'] = helper::nowTime();
        $params['account_no'] = $params['account_no'] ? $params['account_no'] : null;
        if($isAdd){
            $params['qr_uuid'] = \Framework\Helper::uuid();
            $params['createtime'] = $params['updatetime'];
            $params['creator_name'] = $params['last_operator'];
            $params['creator_id'] = $params['last_operator_id'];
            $params['data_from'] = $params['data_from'] ? $params['data_from'] : 3;
            $data = OilCardApplyTemplate::add($params);
        }else{
            $params['id'] = $info->id;
            $data = OilCardApplyTemplate::edit($params);
        }
        $data->uuid = $data->qr_uuid;
        $data->qr_uuid = null;
        $data->id = null;
        Response::json($data,0,'成功');
    }

    /**
     * 更改状态
     * @return mixed
     */
    public function updateStatus()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['uuid'], $params);
        $info = OilCardApplyTemplate::getByUuidLock($params);
        if(!$info){
            throw new \RuntimeException('数据不合法',2);
        }
        if($info->status == 2){
            throw new \RuntimeException('已作废，请勿重复操作',2);
        }
        $params['last_operator_id'] = $params['other_creator_id'];
        $params['last_operator'] = $params['other_creator'];
        $params['updatetime'] = helper::nowTime();
        $params['id'] = $info->id;
        $params['status'] = 2;
        $data = OilCardApplyTemplate::edit($params);
        $data->id = null;
        Response::json($data,0,'成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilCardApplyTemplate::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilCardApplyTemplate::remove($params);

        Response::json($data);
    }

}