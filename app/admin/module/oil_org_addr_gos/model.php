<?php
/**
 * 油品机构维护
 */

class oil_org_addr_gosModel extends model
{
	public function __construct() 
	{
        parent::__construct();
    }


	/**
	 * 查询机构信息
	 * @param $where
	 * @param $sort
	 * @param $limit
	 * @param string $fields
	 * @param string $table
	 * @return stdClass
	 */
	public function search($where, $sort, $limit, $fields = '*', $table='oil_org')
	{
		$sql = 'SELECT %s  FROM '.$table;


		$total = $this->findBySql($sql, $where, null, null, 'count(id) as count');

		if($total[0]->count > 0) {
			$datas = $this->findBySql($sql,$where, $sort, $limit, $fields);
		}

		$result = new stdClass();
		$result->total = $total[0]->count;
		$result->data  = isset($datas) ? $datas : '';

		return $result;
	}

	/**
	 * @param $where
	 * @param $sort
	 * @param $limit
	 * @param string $fields
	 * @param string $table
	 * @return stdClass
	 */
    public function searchOrgCode($where, $sort, $limit, $fields = '*', $table='oil_org')
    {
        $sql = 'SELECT %s,concat(orgcode,org_name) org_name FROM '.$table;


        $total = $this->findBySql($sql, $where, null, null, 'count(id) as count');

        if($total[0]->count > 0) {
            $datas = $this->findBySql($sql,$where, $sort, $limit, $fields);
        }

        $result = new stdClass();
        $result->total = $total[0]->count;
        $result->data  = isset($datas) ? $datas : '';

        return $result;
    }


	/**
	 * 增加机构
	 * @param $input
	 * @param string $table
	 * @param bool $operator
	 * @return bool|int
	 */
	public function addOrg($input, $table = 'oil_org', $operator = true)
	{
		if(empty($input)) return false;

		if($operator){
			$input['creator_id']    = $this->app->myAdmin->id;
			$input['last_operator'] = $this->app->myAdmin->true_name;
		}

		$input['createtime']    = helper::nowTime();
		$input['updatetime']    = helper::nowTime();

		$id = $this->insert($input, $table);

		return $id;
	}


	/**
	 * 查询机构是否存在
	 * @param $where
	 * @param string $table
	 * @return bool|stdClass
	 */
	public function checkExist($where, $table = 'oil_org')
	{
		$result = $this->search($where, null, null, 'id', $table);

		if($result->total > 0) {
			return $result;
		}else{
			return false;
		}
	}


	/**
	 * 更新机构
	 * @param $input
	 * @param string $table
	 * @return bool|int
	 */
	public function updateOrg($input, $where, $table = 'oil_org')
	{
		if(empty($input)) return false;

		$input['last_operator'] = $this->app->myAdmin->true_name;
		$input['updatetime']    = helper::nowTime();

		$result = $this->edit($input, $where, $table);

		return $result;
	}

    /**
     * @title 编辑
     * <AUTHOR>
     * @param        $input
     * @param        $where
     * @param string $table
     * @return int
     */
	public function edit($input, $where, $table='gsp_sys_orgs')
	{
		return parent::update($input, $where, $table);
	}

	/**
	 * 检查副卡中是否有使用当前要删除的机构
	 * @param $org_id
	 * @return bool
	 */
	public function checkCardUsed($org_id)
	{
		$sql   = 'SELECT %s FROM oil_card_vice ';
		$where = ' org_id = '.$org_id;

		$total = parent::findBySql($sql, $where, null, null, 'count(id) as count');

		return $total[0]->count > 0 ? TRUE : FALSE;
	}


	/**
	 * 查询机构附加信息
	 * @param $org_id
	 * @param $table
	 * @return stdClass
	 */
	public function searchORgExtra($org_id, $table, $fields = '*')
	{
		$where = array('org_id'=>$org_id, 'is_del'=>0);
		$data = $this->search($where, null, null, $fields, $table);

		return $data;
	}


	/**
	 * 检测附加信息是否存在
	 * @param $params
	 * @return bool|stdClass
	 */
	public function checkExtraExist($params)
	{
		extract($params);

		if($target == 'addr'){
			$where = " org_id = $org_id AND name = '$name' AND mobile = '$mobile' ";
		}else{
			$where = " org_id = $org_id AND contact_name = '$contact_name' AND contact_mobile = '$contact_mobile' ";
		}
		$table = 'oil_org_' . $target;

		return $this->checkExist($where, $table);
	}


	/**
	 * 检查 开卡申请中是否有油品负责人
	 * @param $contact_id
	 * @return bool
	 */
	public function checkCardContact($contact_id)
	{
		$sql   = 'SELECT %s FROM oil_card_vice_app ';
		$where = ' org_contact_id = '.$contact_id;

		$total = parent::findBySql($sql, $where, null, null, 'count(id) as count');

		if($total[0]->count > 0){
			return true;
		}else{
			return false;
		}
	}

	/**
	 * 根据oil_org.id获取机构信息
	 * @param $orgid
	 * @return mixed
	 */
	public function getOrgById($orgid)
	{
		return parent::findUnique(['id'=>$orgid],null,'*', 'oil_org');
	}

	/**
	 * 根据oil_org.orgcode获取机构信息
	 * @param null $orgcode
	 * @return mixed
	 */
	public function getOrgByOrgCode($orgcode = NULL)
	{
		return parent::findUnique(['orgcode'=>$orgcode,'is_del' => '0'],null,'*', 'oil_org');
	}

	/**
     * 根据机构修改整个机构树的专属客服
     */
    public function updateExclusiveCustom($params)
    {
        if(isset($params['orgcode']) && !empty($params['orgcode']) && isset($params['exclusive_custom']) && !empty($params['exclusive_custom'])){
            $orgcode = substr($params['orgcode'],0,6);
            $sql = "UPDATE oil_org SET exclusive_custom = '$params[exclusive_custom]' WHERE orgcode LIKE '$orgcode%'";
            if(parent::exec($sql)){
                return true;
            }else{
                return false;
            }
        }
    }

}