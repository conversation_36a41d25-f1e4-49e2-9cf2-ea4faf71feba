<?php
/**
 * Created by PhpStorm.
 * User: 王吾阳Huoyunren
 * Date: 2015/6/11
 * Time: 16:13
 */

class readexcelModel extends model {

    /**
     * 插入品牌
     * @param $brands
     * @return bool
     */
    public function addBrand($brands)
    {
        foreach($brands as $val){
            $data  = array(
                'name'          => $val,
                'creator_id'    => $this->app->myAdmin->id,
                'last_operator' => $this->app->myAdmin->true_name,
                'parent_id'     => 0,
                'type'          => 1,
                'createtime'    => helper::nowTime(),
                'updatetime'    => helper::nowTime(),
            );
            $res = $this->insert($data, 'gsp_trucks_brand_detail');
            if(!$res){
                return false;
            }
        }
        return true;
    }


    public function writeProduct($products)
    {
        foreach($products as $val){
            $sql = 'SELECT %s FROM gsp_trucks_brand_detail';
            $where = ' name = "'.$val['brand'].'"';
            $parent_id = $this->findBySql($sql, $where, null ,null, 'id');

            $data  = array(
                'name'          => $val['product'],
                'creator_id'    => $this->app->myAdmin->id,
                'last_operator' => $this->app->myAdmin->true_name,
                'parent_id'     => $parent_id[0]->id,
                'type'          => 2,
                'createtime'    => helper::nowTime(),
                'updatetime'    => helper::nowTime(),
            );
            $res = $this->insert($data, 'gsp_trucks_brand_detail');
            if(!$res){
                return false;
            }
        }
        return true;
    }

    public function getIdByName($name)
    {
        $sql = 'SELECT %s FROM gsp_trucks_brand_detail';
        $where = ' name = "'.$name.'"';
        $res = $this->findBySql($sql, $where, ' id DESC ' ,null, 'id');
        return $res[0]->id;
    }


    public function writeModel($data)
    {
        foreach($data as $val){
            $data  = array(
                'name'          => $val['name'],
                'brand_id'      => $val['brand_id'],
                'product_id'    => $val['product_id'],
                'drive'         => $val['drive'],
                'horsepower'    => $val['horsepower'],
                'equipments'    => '',
                'creator_id'    => $this->app->myAdmin->id,
                'last_operator' => $this->app->myAdmin->true_name,
                'createtime'    => helper::nowTime(),
                'updatetime'    => helper::nowTime(),
            );
            $res = $this->insert($data, 'gsp_trucks_model');
            if(!$res){
                return false;
            }
        }
        return true;
    }
}