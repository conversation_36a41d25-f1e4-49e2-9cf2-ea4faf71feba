<?php
/**
 * 供应商站点副卡表 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/08/09
 * Time: 21:16:45
 */

use Fuel\Service\StationCard;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilStationCard;
use Framework\Excel\ExcelWriter;
use Fuel\Response;
use Fuel\Service\SupplierStation;
use Fuel\Defines\StationBalanceConf;

class oil_station_card extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();
        $data = OilStationCard::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }


    public function searchViceNos()
    {
        $params = helper::filterParams();

        $data   = OilStationCard::searchCardViceNos($params);

        Response::json($data);
    }
    
    /**
     * 列表查询
     * @return array
     */
    public function getStationCardList()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['balance_id'], $params);
        
        $balanceInfo = \Models\OilStationBalance::getById(['id'=>$params['balance_id']]);
        $data = [];
        if ($balanceInfo && $balanceInfo['res_type'] == StationBalanceConf::RES_TYPE_STATION
            && $balanceInfo['bill_type'] == StationBalanceConf::BILL_TYPE_CARD) {
            $params = [
                'supplier_relation_id' => $balanceInfo['res_id']
            ];
            $data = OilStationCard::getList($params);
        }
    
        Response::json($data);
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '供应商站点副卡表_' . date("YmdHis"),
            'sheetName' => '供应商站点副卡表',
            'title' => [
                'id'   =>  '自增ID',
                'supplier_relation_id'   =>  '供应商关联表id',
                'code'   =>  '站点编码',
                'vice_no'   =>  '副卡卡号',
                'card_main_id'   =>  '主卡id',
                'vice_balance'   =>  '副卡余额',
                'card_main_balance'   =>  '主卡余额',
                'property_txt'   =>  '油卡属性',
                'last_operator'   =>  '最后修改人',
                'createtime'   =>  '创建时间',
                'updatetime'   =>  '更新时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilStationCard::getById($params);

        Response::json($data);
    }
    
    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilStationCard::add($params);
        
        Response::json($data,0,'添加成功');
    }
    
    /**
     * 新增
     * @return mixed
     */
    public function createByBalance()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['balance_id'], $params);
        
        $data = (new SupplierStation())->addCardByBalance($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilStationCard::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        
        $data = (new SupplierStation())->unBindCardByBalance(['bind_card_id'=>$params['ids']]);

        Response::json($data);
    }

    /**
     * @throws Exception
     */
    public function createStationBindCard()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['station_code', 'vice_no', 'card_remain', 'reserve_remain',], $params);
        $params['creator'] = $this->app->myAdmin->true_name;
        $params['uid'] = $this->app->myAdmin->id;
        //绑卡时，不已爬取余额校验
        $params['task_id'] = "";
        $result = StationCard::createStationBindCard($params);
        if (isset($result['code']) and $result['code'] != 0) {

            Response::json($result['data'], $result['code'], $result['msg']);
        }
        Response::json([],0,'添加成功');
    }
    /**
     * @throws Exception
     */
    public function removeStationBindCard()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id', 'card_remain', 'reserve_remain'], $params);
        $params['id'] = explode(',', $params['id']);
        $params['creator'] = $this->app->myAdmin->true_name;
        $result = StationCard::removeStationBindCard($params);
        if (isset($result['code']) and $result['code'] != 0) {

            Response::json($result['data'], $result['code'], $result['msg']);
        }
        Response::json([],0,'删除成功');
    }
    /**
     * @throws Exception
     */
    public function switchStationBindCard()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id', 'code', 'card_remain', 'reserve_remain','vice_no', 'is_finance'], $params);
        $params['creator'] = $this->app->myAdmin->true_name;
        $params['task_id'] = '';
        $result = StationCard::switchStationBindCard($params);
        if (isset($result['code']) and $result['code'] != 0) {

            Response::json($result['data'], $result['code'], $result['msg']);
        }
        Response::json([],0,'转移成功');
    }

    /**
     * @throws Exception
     */
    public function preCheckForStationBindCard()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['card_no'], $params);
        $params['vice_no'] = $params['card_no'];
        $result = StationCard::preCheckForStationBindCard($params);
        Response::json([], $result['code'], $result['msg']);
    }
    
    /**
     * 初始化绑卡记录
     */
    public function initCardBind()
    {
        $result = StationCard::initCardBind();
        Response::json($result,0,'初始化完成');
    }
}