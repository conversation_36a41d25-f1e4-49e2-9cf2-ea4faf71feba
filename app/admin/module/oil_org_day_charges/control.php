<?php
/**
 * 机构日充值统计 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/08/14
 * Time: 14:23:16
 */

use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilOrgDayCharges;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_org_day_charges extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     *
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilOrgDayCharges::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $this->exportList($data);
        } else {
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     *
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName'  => '机构日充值统计_' . date("YmdHis"),
            'sheetName' => '机构日充值统计',
            'title'     => [
                'id'                  => 'id',
                'day'                 => '数据日期',
                'orgcode'             => '机构编码',
                'pay_company_id'      => '付款公司ID',
                'pay_company_name'    => '付款公司名称',
                'total_cash_money'    => '现金充值',
                'total_fanli_money'   => '返利充值',
                'total_credit_charge' => '授信充值',
                'total_credit_borrow' => '授信借款',
                'createtime'          => '创建时间',
                'updatetime'          => '更新时间'
            ],
            'data'      => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     *
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilOrgDayCharges::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     *
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data   = OilOrgDayCharges::add($params);

        Response::json($data, 0, '添加成功');
    }

    /**
     * 编辑
     *
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilOrgDayCharges::edit($params);

        Response::json($data, 0, '编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     *
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilOrgDayCharges::remove($params);

        Response::json($data);
    }

    /**
     * 充值日报
     */
    public function statisticByDay()
    {
        $params = helper::filterParams();
        $data   = OilOrgDayCharges::statisticByDay($params);

        return Response::json($data);
    }
}