<?php
/**
 * oil_owing_statistics_snapshot Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/05/17
 * Time: 11:52:04
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilOwingStatisticsSnapshot;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_owing_statistics_snapshot extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilOwingStatisticsSnapshot::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => 'oil_owing_statistics_snapshot_' . date("YmdHis"),
            'sheetName' => 'oil_owing_statistics_snapshot',
            'title' => [
                'id'   =>  '主键id',
                'select_end_time'   =>  '查询快照结束时间',
                'supplier_id'   =>  '供应商id',
                'supplier_name'   =>  '供应商名称',
                'cooperation_type'   =>  '合作类型',
                'operator_name'   =>  '签约运营商',
                'company_id'   =>  '收款公司id',
                'company_name'   =>  '收款公司名称',
                'oil_id'   =>  '油品id',
                'oil_name'   =>  '油品名称',
                'a_owing_amount'   =>  'a.欠票金额',
                'b_company_charge'   =>  'b.收款公司可用充值',
                'c_oil_consume'   =>  'c.油品可用消费',
                'd_company_charge_total'   =>  'd.收款公司累计充值',
                'e_oil_consume_total'   =>  'e.油品的累计消费',
                'f_oil_consume_flowing'   =>  'f.油品累计消费流水',
                'g_oil_rebate_flowing'   =>  'g.油品累计返利流水',
                'h_oil_adjustment_flowing'   =>  'h.油品累计消费流水差异调账',
                'i_receipt_money'   =>  'i.油品回票金额',
                'j_receipt_discount'   =>  'j.油品回票累计折扣',
                'k_receipt_num'   =>  'k.油品回票数量',
                'oil_unit'   =>  '单位'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilOwingStatisticsSnapshot::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilOwingStatisticsSnapshot::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilOwingStatisticsSnapshot::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilOwingStatisticsSnapshot::remove($params);

        Response::json($data);
    }

}