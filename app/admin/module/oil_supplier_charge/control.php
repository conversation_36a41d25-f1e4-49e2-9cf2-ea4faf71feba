<?php
/**
 * oil_supplier_charge Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/07/15
 * Time: 11:13:10
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilSupplierCharge;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_supplier_charge extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilSupplierCharge::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $redirect_url = \Fuel\Service\ExportJobSrv::syncExportTask($params,"exportList",$data);
            echo "<script>window.location.href = '/".strtolower(__CLASS__)."';window.open('".$redirect_url."')</script>";
//            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    public function exportList($data)
    {
        $exportData = [
            'fileName' => 'oil_supplier_charge_' . date("YmdHis"),
            'sheetName' => 'oil_supplier_charge',
            'download'  => 1, //增加
            'title' => [
                'id'   =>  '主键',
                'no'   =>  '单号',
                'account_id'   =>  '账号id',
                'account_no'   =>  '账号编号',
                'account_type'   =>  '账户类型',
                'account_name'   =>  '充值账户名称',
                'account_level'   =>  '账户等级',
                'amount'   =>  '充值金额',
                'charge_type'   =>  '充值类型 10 付款 20分配 30绑卡',
                'app_no'   =>  '关联业务单号',
                'app_id'   =>  '关联业务id',
                'status'   =>  '状态 0待审核 1已审核',
                'is_del'   =>  '是否删除 ',
                'creator_id'   =>  '创建人id',
                'creator_name'   =>  '创建人姓名',
                'last_operator_id'   =>  '更新人ID',
                'last_operator'   =>  '最后更新人',
                'createtime'   =>  '创建时间',
                'updatetime'   =>  '修改时间'
            ],
            'data' => $data->toArray(),
        ];

        $url = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
        return $url;
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilSupplierCharge::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilSupplierCharge::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 添加备注
     * @return mixed
     */
    public function addRemark()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id','remark'], $params);
        $_params['id'] = $params['id'];
        $_params['remark'] = $params['remark'];
        $_params['updatetime'] = helper::nowTime();
        $_params['last_operator_id'] = $this->app->myAdmin->id;
        $_params['last_operator'] = $this->app->myAdmin->true_name;
        $data = OilSupplierCharge::edit($_params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilSupplierCharge::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilSupplierCharge::remove($params);

        Response::json($data);
    }

    /**
     * 充值 for 付款/绑卡/分配
     */
    public function createForOther()
    {
        $params = helper::filterParams();
        $params['creator_id'] = $this->app->myAdmin->id;
        $params['creator_name'] = $this->app->myAdmin->true_name;

        $data = (new \Fuel\Service\SupplierCharge())->createForAccountId($params);

        Response::json($data);
    }

    public function createAssign()
    {
        $data = (new \Fuel\Service\SupplierCharge())->createAssign(27257);

        Response::json($data);
    }
}