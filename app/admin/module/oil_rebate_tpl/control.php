<?php
/**
 * 返利模板 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/01/22
 * Time: 16:22:54
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilRebateTpl;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_rebate_tpl extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();
        if( !isset($params['tpl_type']) || empty($params['tpl_type']) ){
            $params['tpl_type'] = 1;
        }
        $params['is_del'] = 0;
        $data = OilRebateTpl::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    public function exportRebateTpl()
    {
        $params = helper::filterParams();
        $params['_export'] = 1;
        $task = (new \Jobs\ExportRebateTplJob($params))
            ->setTaskName('公式模板')
            ->setUserInfo($this->app->myAdmin)
            ->onQueue('export')
            ->dispatch();
        Response::json(["redirect_url"=>$task->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '返利模板_' . date("YmdHis"),
            'sheetName' => '返利模板',
            'title' => [
            'id'   =>  '自增ID',
            'tpl_type'   =>  '模板类型 1省类型 ',
            'title'   =>  '模板标题',
            'content'   =>  '模板内容',
            'able_edit'   =>  '是否可编辑 1可编辑 2不可编辑',
            'is_del'   =>  '是否删除 0正常 1已删除',
            'createtime'   =>  '创建时间',
            'updatetime'   =>  '更新时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilRebateTpl::getById($params);

        Response::json($data);
    }

    /**
     * 获取省份列表
     */
    public function getProvices($isArray = false)
    {
        $result = \Models\OilProvinces::getProviceMap();
        Response::json(array_values($result), 0, "成功");
    }

    /**
     * 新增
     * @return mixed
     */
    public function createOrEdit()
    {
        $params = helper::filterParams();
        if( !isset($params['tpl_type']) || empty($params['tpl_type']) ){
            $params['tpl_type'] = 1;
        }
        //$params['content'] = "[{\"value\":0.9,\"provice\":\"others\"},{\"value\":1,\"provice\":[{\"code\":\"420000\",\"name\":\"湖北\"},{\"code\":\"360000\",\"name\":\"江西\"},{\"code\":\"510000\",\"name\":\"四川\"}]},{\"value\":1.5,\"provice\":[{\"code\":\"999901\",\"name\":\"中油BP\"},{\"code\":\"440300\",\"name\":\"深圳\"}]}]";
        //$params['content'] = "[{\"value\":1,\"provice\":[{\"code\":\"420000\",\"name\":\"湖北\"},{\"code\":\"360000\",\"name\":\"江西\"},{\"code\":\"510000\",\"name\":\"四川\"}]},{\"value\":1.5,\"provice\":[{\"code\":\"999901\",\"name\":\"中油BP\"},{\"code\":\"440300\",\"name\":\"深圳\"}]}]";
        helper::argumentCheck(['title','content'], $params);
        $data = (new \Fuel\Service\RebatePolicyService())->addTpl($params);

        Response::json($data,0,'成功');
    }

    //删除模板
    public function delTpl()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $info = OilRebateTpl::getById($params);
        if( OilRebateTpl::checkTplIsBind($info->id) > 0 ){
            throw new \RuntimeException('模板已使用不允许删除', 2);
        }
        if($info->is_del == 1){
            throw new \RuntimeException('模板删除，请勿重复操作', 2);
        }
        $params['is_del'] = 1;
        $params['updatetime'] = helper::nowTime();
        $data = OilRebateTpl::edit($params);

        Response::json($data,0,'成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilRebateTpl::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilRebateTpl::remove($params);

        Response::json($data);
    }

}