<?php
use Fuel\Response;
use Fuel\Service\ExternalApiService;


class external_api extends baseControl
{
    public $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = new ExternalApiService();
    }

    /**
     * 外部调用，导出用户角色列表接口
     */
    public function export_user_role()
    {
        $params = helper::filterParams();

        $data = $this->service->getUserRoleListData($params);

        Response::json($data);
    }

    /**
     * 查询角色 权限列表
     */
    public function export_role_info()
    {
        $params = helper::filterParams();
        
        $data = $this->service->getRoleSourceListData($params);

        Response::json($data);
    }

    /**
     * 删除用户的 权限
     */
    public function disable_user()
    {
        $params = helper::filterParams();
        if (empty($params['username']))
        {
            $result = [
                "code" => 400,
                "message" => "请提供需要删除的用户"
            ];
        }
        else
        {
            $result = $this->service->disableUserData($params);
        }


        exit(json_encode($result));
    }
}