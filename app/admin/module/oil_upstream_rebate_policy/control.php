<?php
use Fuel\Response;
use Fuel\Service\UpstreamRebate\CheckPolicy;
use Fuel\Service\UpstreamRebate\PolicyService;

class oil_upstream_rebate_policy extends baseControl
{
    /**
     * @var PolicyService
     */
    protected $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = PolicyService::getInstance();
    }

    /**
     * 查询平台
     *
     * @return void
     */
    public function getPlatform() : void
    {
        $params = helper::filterParams();
        $data = $this->service->getPlatform($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 查询主卡
     *
     * @return void
     */
    public function getMainCard() : void
    {
        $params = helper::filterParams();
        $data = $this->service->getMainCard($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 查询站点
     *
     * @return void
     */
    public function getOwnStation() : void
    {
        $params = helper::filterParams();
        $data = $this->service->getOwnStation($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 获得自有站点的一级供应商
     *
     * @return void
     */
    public function getOwnStationSupplier() : void
    {
        $params = helper::filterParams();
        $data = $this->service->getOwnStationSupplier($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 获得自有站点的二级服务区
     *
     * @return void
     */
    public function getOwnStationArea() : void
    {
        $params = helper::filterParams();
        $data = $this->service->getOwnStationArea($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 根据平台ID查找平台下所有站点
     *
     * @return void
     */
    public function getOwnStationBySupplier() : void
    {
        $params = helper::filterParams();
        $data = $this->service->getOwnStationBySupplier($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 根据服务区ID查找服务区下所有站点
     *
     * @return void
     */
    public function getOwnStationByArea() : void
    {
        $params = helper::filterParams();
        $data = $this->service->getOwnStationByArea($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 上传文件校验自有站点状态
     *
     * @return void
     */
    public function validateOwnStation() : void
    {
        $params = helper::filterParams();
        $data = $this->service->validateOwnStation($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 上传文件校验主卡状态
     *
     * @return void
     */
    public function validateMainCard() : void
    {
        $params = helper::filterParams();
        $data = $this->service->validateMainCard($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 新增
     * @return void
     */
    public function create() : void
    {
        $params = helper::filterParams();
        $data = $this->service->create($params);
        Response::json($data, 0, '添加成功');
    }

    /**
     * 编辑
     * @return void
     */
    public function update()
    {
        $params = helper::filterParams();
        $data = $this->service->update($params);
        Response::json($data, 0, '更新成功');
    }

    /**
     * 列表
     * @return void
     */
    public function list() : void
    {
        $params = helper::filterParams();
        $data = $this->service->getList($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 详情
     * @return void
     */
    public function detail() : void
    {
        $params = helper::filterParams();
        $data = $this->service->detail($params);
        Response::json($data ?? new stdClass, 0, '获取成功');
    }

    /**
     * 改变审核状态
     *
     * @return void
     */
    public function changeReview() : void
    {
        $params = helper::filterParams();
        $data = $this->service->changeReview($params);
        Response::json($data ?? new stdClass, 0, '操作成功');
    }

    /**
     * 获得规则的操作日志
     *
     * @return void
     */
    public function log() : void
    {
        $params = helper::filterParams();
        $data = $this->service->log($params);
        Response::json($data ?? new stdClass, 0, '获取成功');
    }

    /**
     * 复制规则
     *
     * @return void
     */
    public function copy() : void
    {
        $params = helper::filterParams();
        $data = $this->service->copy($params);
        Response::json($data ?? new stdClass, 0, '复制成功');
    }

    /**
     * 延长规则有效期
     *
     * @return void
     */
    public function postpone() : void
    {
        $params = helper::filterParams();
        $data = $this->service->postpone($params);
        Response::json($data ?? new stdClass, 0, '延长有效期成功');
    }

    /**
     * 提前终止
     *
     * @return void
     */
    public function finish() : void
    {
        $params = helper::filterParams();
        $data = $this->service->finish($params);
        Response::json($data ?? new stdClass, 0, '提前终止成功');
    }

    /**
     * 检测规则是否可以编辑
     *
     * @return void
     */
    public function checkEditAble() : void
    {
        $params = helper::filterParams();
        $data = [];
        $data['status'] = $this->service->checkEditAble($params);
        Response::json($data ?? new stdClass, 0, '获取成功');
    }

    /**
     * 查询核算主体
     *
     * @return void
     */
    public function searchAccount() : void
    {
        $params = helper::filterParams();
        Response::json($this->service->searchAccount($params), 0, '获取成功');
    }

    /**
     * 删除规则, 暂时提供
     *
     * @return void
     */
    public function deletePolicy() : void
    {
        $params = helper::filterParams();
        $data = [];
        $data = $this->service->deletePolicy($params);
        Response::json($data ?? new stdClass, 0, '删除成功');
    }

    /**
     * 定时任务检测政策是否存在
     *
     * @return void
     */
    public function cronCheckPolicy() : void
    {
        $params = helper::filterParams();
        $data = CheckPolicy::getInstance()->cronCheckPolicy($params);
        Response::json($data ?? new stdClass, 0, '检测完成');
    }

    /**
     * 获得省份信息
     *
     * @return void
     */
    public function province() : void
    {
        $data = $this->service->province();
        Response::json($data ?? new stdClass, 0, '获取成功');
    }
}