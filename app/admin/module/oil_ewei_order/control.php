<?php
/**
 * ewei与本系统工单关系表 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/09/29
 * Time: 11:43:34
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilEweiOrder;
use Framework\Excel\ExcelWriter;

class oil_ewei_order extends baseControl
{

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilEweiOrder::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Fuel\Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '{ewei与本系统工单关系表}_' . date("YmdHis"),
            'sheetName' => '{ewei与本系统工单关系表}',
            'title' => [
            
                'id'   =>  'id',

                'tb_name'   =>  '工单表名',

                'tb_pk'   =>  'tb_pk',

                'tb_no'   =>  '工单号',

                'status'   =>  '本系统工单状态',

                'ew_id'   =>  'ew_id',

                'ew_status'   =>  '易维状态',

                'createtime'   =>  'createtime',

                'updatetime'   =>  'updatetime'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilEweiOrder::getById($params);

        Fuel\Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilEweiOrder::add($params);

        Fuel\Response::json($data);
    }

    /**
     * 编辑
     * @return mixed
     */
    static public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilEweiOrder::edit($params);

        Fuel\Response::json($data);
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    static public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilEweiOrder::remove($params);

        Fuel\Response::json($data);
    }

}