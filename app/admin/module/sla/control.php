<?php

/**
 * Story #49039
 * 智能管车SLA动态图表-自定义列表管理
 * <AUTHOR>
 * @date 2015/8/31 11:32
 */
class sla extends baseControl
{
	/**
	 * 检查输入项是否正确
	 * <AUTHOR>
	 * @date 2015/9/6
	 * @param $params
	 * @return boolean
	 */
	private function _checkValid($params)
	{
		if (!isset($params['chart_name']) || empty($params['chart_name'])) return '图表名称不能为空';
		if (!isset($params['chart_type']) || empty($params['chart_type'])) return '图表类型不能为空';
		if (!isset($params['chart_statistic_type']) || empty($params['chart_statistic_type'])) return '图表统计类型不能为空';
		if (!isset($params['chart_mode']) || empty($params['chart_mode'])) return '数据范围不能为空';
		if (!isset($params['chart_datas'])) return '数据范围不能为空';

		if (count(explode(',', $params['chart_data'])) > 5) return '数据范围最多能选择5项';

		return true;
	}

	/**
	 * 列表
	 * <AUTHOR>
	 * @date 2015//9/7
	 * @return void
	 */
	public function index()
	{
		$testArr = [
			'data'  =>  ''
		];

		var_dump(isset($testArr['data']));
		exit;
		$params = helper::filterParams();
		$result = $this->sla->search($params);
		echo json_encode($result);
	}

	public function get()
	{
		$params = helper::filterParams();
		$data = $this->sla->get($params);
		echo json_encode($data);
	}

	/**
	 * 编辑自定义配置
	 * <AUTHOR>
	 * @date 2015//9/7
	 * @return void
	 */
	public function edit()
	{
		$params = helper::filterParams();

		if ($this->_checkValid($params) !== true) {
			echo json_encode(array('failure' => true, 'msg' => '表单项未按要求输入'));
			exit;
		}

		unset($params['chart_data']);
		$res = $this->sla->edit($params);
		if ($res) {
			$result = $this->sla->get($params);
			$data = array('success' => true, 'msg' => '操作成功', 'data' => $result[0]);
		} else {
			$data = array('failure' => true, 'msg' => '操作失败');
		}

		echo json_encode($data);
		exit;
	}

	/**
	 * 删除自定义配置
	 * <AUTHOR>
	 * @date 2015//9/7
	 * @return void
	 */
	public function del()
	{
		$params = helper::filterParams();
		$id = (int)$params['id'];
		$res = $this->sla->del($id);

		if ($res) {
			$data = array('success' => true, 'msg' => '删除成功');
		} else {
			$data = array('failure' => true, 'msg' => '删除失败');
		}

		echo json_encode($data);
		exit;
	}

	/**
	 * <AUTHOR>
	 * @date 2015//9/7
	 * @return void
	 */
	public function getData()
	{
		$params = helper::filterParams();
		$source = (int)$params['source'];
		$name = (string)$params['name'];

		switch ($source) {
			case 1:
				// 客户
				$res = $this->loadModel('customer')->getCustomers($name);
				break;
			case 2:
				// 设备
				$res = $this->loadModel('gps_items')->getItems(1, $name);
				break;
		}

		echo json_encode($res);
	}

	/**
	 * 图表导出到文件
	 * <AUTHOR>
	 * @date 2015//9/7
	 * @return void
	 */
	public function chartExport()
	{
		$params = helper::filterParams();
		$modulePath = $this->app->getModulePath($this->moduleName);
		include_once $modulePath . "export/svgConvert.php";
		$svgConvert = new svgConvert($params);
		$svgConvert->export();
	}

	/**
	 * 获取服务器年月
	 * <AUTHOR>
	 * @date 2015/9/11
	 * @return void
	 */
	public function getDates()
	{
		echo json_encode(array('year' => date('Y'), 'month' => date('m'), 'date' => date('d')));
	}

	/**
	 * 获取统计数据总入口
	 * <AUTHOR>
	 * @date 2015/9/10
	 * @return void
	 */
	public function getStat()
	{
		$params = helper::filterParams();
		$chart_type = (int)$params['chart_type'];
		$chart_statistic_type = (int)$params['chart_statistic_type'];
		$chart_mode = (int)$params['chart_mode'];
		$chart_datas = $params['chart_datas'];
		if (empty($chart_type) || empty($chart_mode) || empty($chart_statistic_type)) {
			echo json_encode(array('failure' => true, 'msg' => '参数错误'));
			exit;
		}

		switch ($chart_type) {
			case 1:
			case 2:
				$result = $this->sla->getBreakStat($chart_type, $chart_statistic_type, $chart_mode, explode(',', $chart_datas));
				break;
			case 3: // 油耗异常率
				break;
			case 4: // 里程异常率
				break;
			case 5: // 加油卡抓取率
				break;
			case 6: // 硬件安装合格率
				break;
		}

		echo json_encode($result);
		exit;
	}

	/**
	 * 获取二级页面统计数据
	 * <AUTHOR>
	 * @date 2015/9/16
	 * @return string
	 */
	public function getSecondStat()
	{
		$params = helper::filterParams();
		$chart_type = (int)$params['chart_type'];
		$chart_mode = (int)$params['chart_mode'];
		$chart_data = (string)$params['data'];
		$chart_name = (string)$params['name'];
		$chart_date = (string)$params['date'];
		$chart_statistic_type = (int)$params['chart_statistic_type'];
		if (empty($chart_type) || empty($chart_mode) || empty($chart_statistic_type) || empty($chart_date)) {
			echo json_encode(array('failure' => true, 'msg' => '参数错误'));
			exit;
		}

		switch ($chart_type) {
			case 1:
			case 2:
				$result = $this->sla->getSecondBreakStat($chart_type, $chart_mode, $chart_data, $chart_name, $chart_date, $chart_statistic_type);
				break;
			case 3: // 油耗异常率
				break;
			case 4: // 里程异常率
				break;
			case 5: // 加油卡抓取率
				break;
			case 6: // 硬件安装合格率
				break;
		}

		echo json_encode($result);
		exit;
	}

	/**
	 * 定时任务 获取DSP离线率数据并保存每个客户每月的离线数据
	 * 每天凌晨5点获取上一天的数据
	 * <AUTHOR>
	 * @date 2015/9/9
	 * @return void
	 */
	public function getBreakStatFromDsp()
	{
		$sla = $this->sla;

		// 获取当天的离线数据
		$day = strtotime(date('Y-m-d 00:00:00')) - 24 * 3600;
		$date = date('Y-m-d', $day);
		$datas = $this->loadModel('client')->GPSEVENTGETBREAKSTAT(array('date' => $date));
		$datas = json_decode($datas, true);
		if ($datas['code'] !== 0) {
			\Framework\Log::dataLog($datas['message']);
			exit;
		}

		$datas = $datas['data'];
		$count = count($datas);
		$temps = array();
		if (!empty($datas) && $count > 0) {
			$orgs = $sla->getOrgcodeFromGsp();
			$dspOrgs = array();
			foreach ($datas as $k => $item) {
				$dspOrgs[$item['orgcode']][$item['model']] = $item['totaldevice'];
			}

			foreach ($orgs as $orgcode => $org) {
				if (!isset($dspOrgs[$orgcode])) {
					// 没有该组织机构的数据
					foreach ($org as $model => $totaldevice) {
						$count++;
						$datas[$count]['orgcode'] = $orgcode;
						$datas[$count]['model'] = $model;
						$datas[$count]['date'] = $date;
						$datas[$count]['totaldevice'] = $totaldevice;
						$datas[$count]['abnormalratio'] = 0;
						$datas[$count]['ratio'] = 0;
					}
				} else {
					foreach ($org as $model => $totaldevice) {
						if (!isset($dspOrgs[$orgcode][$model])) {
							// 没有机构下某个设备类型的离线数据
							$count++;
							$datas[$count]['orgcode'] = $orgcode;
							$datas[$count]['model'] = $model;
							$datas[$count]['date'] = $date;
							$datas[$count]['totaldevice'] = $totaldevice;
							$datas[$count]['abnormalratio'] = 0;
							$datas[$count]['ratio'] = 0;
						}
					}
				}
			}

			foreach ($datas as $k => $item) {
				$item['date'] = $date;
				unset($item['gpsnos']);
				unset($item['seconds']);
				unset($item['count']);
				unset($item['breakdevice']);
				unset($item['abnormalcount']);
				unset($item['abnormalseconds']);

				if (strlen($item['orgcode']) > 6) continue; // @todo 上线是需要删除
				$sla->insertBreakstat($item);
				$temps[] = $item;
			}

			$sla->customerDayBreakStat($date, $temps);
		}
	}

	/**
	 * 定时任务
	 * 统计日数据和月数据
	 * <AUTHOR>
	 * @date 2015/9/9
	 * @return void
	 */
	public function dayBreakStat()
	{
		$day = strtotime(date('Y-m-d 00:00:00'));
		$month = strtotime(date('Y-m-01 00:00:00', strtotime(date('Y-m-d'))));
		// 如果是一个月第一天, 则计算上月最后一天的数据
		if ($day == $month) {
			$month = strtotime(date('Y-m-01 00:00:00', strtotime('last month')));
		}
		$day = $day - 24 * 3600;

		// 统计日数据
		$date = date('Y-m-d', $day);
		$this->sla->dayBreakStat($date);

		// 统计月数据
		$this->sla->monthBreakStat($day, $month);
	}

	/**
	 * @todo 测试
	 * 定时任务 获取DSP离线率数据并保存每个客户每月的离线数据
	 * 每天凌晨5点获取上一天的数据
	 * <AUTHOR>
	 * @date 2015/9/9
	 * @return void
	 */
	public function testGetBreakStatFromDsp($i)
	{
		$sla = $this->sla;

		// 获取当天的离线数据
		$day = strtotime(date('Y-m-d 00:00:00')) - 24 * 3600 * $i;
		$date = date('Y-m-d', $day);
		$datas = $this->loadModel('client')->GPSEVENTGETBREAKSTAT(array('date' => $date));
		$datas = json_decode($datas, true);
		if ($datas['code'] !== 0) {
			\Framework\Log::dataLog($datas['message']);
			exit;
		}

		$datas = $datas['data'];
		$count = count($datas);
		$temps = array();
		if (!empty($datas) && $count > 0) {
			$orgs = $sla->getOrgcodeFromGsp();
			$dspOrgs = array();
			foreach ($datas as $k => $item) {
				$dspOrgs[$item['orgcode']][$item['model']] = $item['totaldevice'];
			}

			foreach ($orgs as $orgcode => $org) {
				if (!isset($dspOrgs[$orgcode])) {
					// 没有该组织机构的数据
					foreach ($org as $model => $totaldevice) {
						$count++;
						$datas[$count]['orgcode'] = $orgcode;
						$datas[$count]['model'] = $model;
						$datas[$count]['date'] = $date;
						$datas[$count]['totaldevice'] = $totaldevice;
						$datas[$count]['abnormalratio'] = 0;
						$datas[$count]['ratio'] = 0;
					}
				} else {
					foreach ($org as $model => $totaldevice) {
						if (!isset($dspOrgs[$orgcode][$model])) {
							// 没有机构下某个设备类型的离线数据
							$count++;
							$datas[$count]['orgcode'] = $orgcode;
							$datas[$count]['model'] = $model;
							$datas[$count]['date'] = $date;
							$datas[$count]['totaldevice'] = $totaldevice;
							$datas[$count]['abnormalratio'] = 0;
							$datas[$count]['ratio'] = 0;
						}
					}
				}
			}

			foreach ($datas as $k => $item) {
				$item['date'] = $date;
				unset($item['gpsnos']);
				unset($item['seconds']);
				unset($item['count']);
				unset($item['breakdevice']);
				unset($item['abnormalcount']);
				unset($item['abnormalseconds']);

				if (strlen($item['orgcode']) > 6) continue; // @todo 上线是需要删除
				$sla->insertBreakstat($item);
				$temps[] = $item;
			}

			$sla->customerDayBreakStat($date, $temps);
		}
	}

	/**
	 * @todo 测试
	 * 统计月数据
	 * <AUTHOR>
	 * @date 2015/9/9
	 * @params $datas 每天的数据
	 * @return void
	 */
	public function testDayBreakStat($i)
	{
		$day = strtotime(date('Y-m-d 00:00:00')) - 24 * 3600 * $i;
		$date = date('Y-m-d', $day);
		$this->sla->dayBreakStat($date);
	}

	/**
	 * @todo 测试
	 * 增量统计月数据
	 * <AUTHOR>
	 * @date 2015/9/9
	 * @params $datas 每天的数据
	 * @return void
	 */
	public function testMonthBreakStat($i)
	{
		$day = strtotime(date('Y-m-d 00:00:00'));
		$day = $day - 24 * 3600 * $i;
		$month = strtotime(date('Y-m-01 00:00:00', $day));
		// 如果是一个月第一天, 则计算上月最后一天的数据
		//if ($day == $month) {
		//	$month = strtotime(date('Y-m-01 00:00:00', strtotime('last month')));
		//}

		$this->sla->monthBreakStat($day, $month);
	}

	/* @todo 测试 */
	public function testBreakStat()
	{
		$params = helper::filterParams();
		$date = date('Y-m-d 00:00:00', strtotime($params['date']));
		$date = strtotime($date);
		$now = strtotime(date('Y-m-d 00:00:00', time()));
		$i = ($now - $date) / (24 * 3600);

		set_time_limit(600);
		$this->testGetBreakStatFromDsp($i);
		$this->testDayBreakStat($i);
		$this->testMonthBreakStat($i);
	}

	/* @todo 测试机构接口 */
	public function testOrgcode()
	{
		$customer_code = $_GET['code'];

		$org = $this->sla->getOrgFromG7s(array('orgcodes' => array($customer_code)));
		print_r($org);
	}
}