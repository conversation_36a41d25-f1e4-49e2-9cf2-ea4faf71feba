<?php
/**
 * oil_account_settlement Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/07/09
 * Time: 18:17:21
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilAccountSettlement;
use Framework\Excel\ExcelWriter;
use Fuel\Response;
use \Framework\Log;
use Fuel\Service\AccountSettlementService;
class oil_account_settlement extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();
        $data = OilAccountSettlement::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $redirect_url = \Fuel\Service\ExportJobSrv::syncExportTask($params,"exportList",$data);
            echo "<script>window.location.href = '/".strtolower(__CLASS__)."';window.open('".$redirect_url."')</script>";
//            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    public function exportList($data)
    {
        $exportData = [
            'fileName' => '上游清分结算单' . date("YmdHis"),
            'sheetName' => '上游清分结算单',
            'download'  => 1, //增加
            'title' => [
                'id'                =>  '序号',
                'apply_no'          =>  '申请单号',
                'account_id'        =>  '清分账户',
                'account_name'      =>  '核算主体',
                'account_grade_val' =>  '账户等级',
                'account_type_val'  =>  '账户类型',
                'account_no'        =>  '清分账号',
                'clear_money'       =>  '清分金额',
                'clear_type_val'    =>  '清分类型',
                'business_no'       =>  '关联业务单号',
                'review_status_val' =>  '审核状态',
                'payment_status_val'=>  '收款状态',
                'creator'           =>  '创建人',
                'createtime'        =>  '创建时间',
                'auditor'           =>  '审核人',
                'audit_time'        =>  '审核时间',
                'last_operator'     =>  '最后操作人',
                'updatetime'        =>  '最后操作时间',
                'remark'            =>  '备注'
            ],
            'data' => $data->toArray(),
        ];

        $url = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
        return $url;
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilAccountSettlement::getById($params);

        Response::json($data);
    }
    /**
     * 新增预退款结算单
     * @return mixed
     */
    public function create()
    {
        $params = \helper::filterParams();
        //创建预退款清分结算单
        $data = (new AccountSettlementService)->handleSettlement($params,false);
        Response::json($data,0,'添加成功');
    }
    /**
     * 根据合作 模式账户等级条件 搜索清分账户
     * @return mixed
     */
    public function searchAccount(){
        $params = \helper::filterParams();
        $data = (new AccountSettlementService)->searchAccountByFilter($params);

        Response::json($data);
    }
    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $params = (new AccountSettlementService)->handlerFormData($params);
        $data = OilAccountSettlement::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilAccountSettlement::remove($params);

        Response::json($data);
    }
    /**
     * 审核清分结算单
     * @return mixed
     */
    public function review(){
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $res=(new AccountSettlementService)->review($params);

        Response::json($res,0,'审核成功');
    }
    /**
     * 获取供应商服务区统计方式
     * @return mixed
     */
    public function getStatisticalMethod(){
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $res=(new AccountSettlementService)->getStatisticalMethod($params);

        Response::json($res);
    }
    /**
     * 驳回清分结算单
     * @return mixed
     */
    public function reject(){
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $res=(new AccountSettlementService)->reject($params);

        Response::json($res,0,'驳回成功');
    }

}