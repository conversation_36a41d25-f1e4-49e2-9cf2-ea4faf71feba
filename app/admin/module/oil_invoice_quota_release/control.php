<?php
/**
 * 柴油专用卡开票额度释放表 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/04/18
 * Time: 10:50:22
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilInvoiceQuotaRelease;
use Framework\Excel\ExcelWriter;
use Fuel\Response;
use \Fuel\Service\InvoiceQuotaRelease;

class oil_invoice_quota_release extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    public function quotaRelease()
    {
        $params = helper::filterParams();
        \Framework\Log::debug('$params--'.var_export($params,true),[],'zzg');
        helper::argumentCheck(['orgcode'],$params);

        (new \Jobs\InvoiceQuotaReleaseJob($params))->dispatch();

        Response::json(null,0,'操作成功,正在异步释放中...');
    }

    public function cronQuotaRelease()
    {
        $orgcodeArr = [
            '201JNA',
            '201IWD',
            '201JI1',
            '201INM',
            '200B0M',
            '2007RL',
            '2007RL',
            '2007RL',
            '2007RL',
            '2014GB',
            '20007I',
            '200J3I',
            '20007Z',
            '2001CW',
            '200JCV',
            '200JM2',
            '200R80',
            '200WF3',
            '2016V4',
            '2016Z3',
            '20170P',
            '20170R',
            '201B24',
            '201B29',
            '201B3W',
            '201BUC',
            '201BUT',
            '201CQX',
            '201CQZ',
            '201CR0',
            '201CWJ',
            '201CWK',
            '201GK7',
            '201I3B',
            '200M1T',
            '200NYC',
            '20181V',
        ];

        foreach($orgcodeArr as $v){
            $params['orgcode'] = $v;
            $params['createtimeGe'] = '2018-04-01';
            (new InvoiceQuotaRelease())->quotaRelease($params);
        }
    }

    /**
     * @title 撤销联名卡开票额度释放
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function revokeQuotaRelease()
    {
        //先查询异步处理开票明细是否有进行中
        $releaseData = \Models\OilJobs::getSingleRecord(['channel'=>'addreceiptapplydetails']);
        if($releaseData){
            \Models\OilJobs::deleteJob(['channel'=>'addreceiptapplydetails']);
            //throw new \RuntimeException('请稍后，开票明细正在处理中...',2);
        }
        (new InvoiceQuotaRelease())->revokeQuotaRelease();

        Response::json(null,0,'操作成功');
    }

    /**
     * @title 期初联名卡消费记录是否已开票
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function initTradesForIsOpenInvoice()
    {
        $orgcodeArr = [
            '201JNA',
            '201IWD',
            '201JI1',
            '201INM',
            '200B0M',
            '2007RL',
            '2007RL',
            '2007RL',
            '2007RL',
            '2014GB',
            '20007I',
            '200J3I',
            '20007Z',
            '2001CW',
            '200JCV',
            '200JM2',
            '200R80',
            '200WF3',
            '2016V4',
            '2016Z3',
            '20170P',
            '20170R',
            '201B24',
            '201B29',
            '201B3W',
            '201BUC',
            '201BUT',
            '201CQX',
            '201CQZ',
            '201CR0',
            '201CWJ',
            '201CWK',
            '201GK7',
            '201I3B',
            '200M1T',
            '200NYC',
            '20181V',
        ];

        foreach($orgcodeArr as $v){
            $params['orgcode'] = $v;
            (new InvoiceQuotaRelease())->init($params);
        }
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilInvoiceQuotaRelease::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $this->exportList($data);
        } else {
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '柴油专用卡开票额度释放表_' . date("YmdHis"),
            'sheetName' => '柴油专用卡开票额度释放表',
            'title' => [
                'id' => '主键ID',
                'trade_id' => '消费记录ID',
                'createtime' => '创建时间',
                'updatetime' => 'updatetime'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilInvoiceQuotaRelease::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilInvoiceQuotaRelease::add($params);

        Response::json($data, 0, '添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilInvoiceQuotaRelease::edit($params);

        Response::json($data, 0, '编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilInvoiceQuotaRelease::remove($params);

        Response::json($data);
    }

}