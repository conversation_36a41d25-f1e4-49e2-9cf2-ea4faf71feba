<?php
/**
 * 油品充值金额表 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/05/20
 * Time: 17:21:04
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\FossRechargeT;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class foss_recharge_t extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = FossRechargeT::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '油品充值金额表_' . date("YmdHis"),
            'sheetName' => '油品充值金额表',
            'title' => [
            'REC_DATE'   =>  '日期',
'CARD_TYPE'   =>  '卡类',
'PRODUCT_CODE'   =>  '产品',
'PRODUCT_NAME'   =>  '产品说明',
'FUNDING_PARTY'   =>  '资金方',
'CUSTOMER_CODE'   =>  '客户编码',
'CUSTOMER_NAME'   =>  '客户名称',
'REC_AMOUNT'   =>  '充值金额',
'ENTITY_CODE'   =>  '法人公司编码',
'ENTITY_NAME'   =>  '法人公司名称',
'ORDER_LABEL'   =>  '订单标签',
'BACKUP1'   =>  '备用字段1',
'BACKUP2'   =>  '备用字段2',
'BACKUP3'   =>  '备用字段3',
'BACKUP4'   =>  '备用字段4',
'BACKUP5'   =>  '备用字段5',
'UPDATE_DATE'   =>  '修改时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = FossRechargeT::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = FossRechargeT::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = FossRechargeT::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = FossRechargeT::remove($params);

        Response::json($data);
    }

}