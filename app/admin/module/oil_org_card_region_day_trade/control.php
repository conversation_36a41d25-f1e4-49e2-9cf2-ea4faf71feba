<?php
/**
 * 机构主卡地区维度日交易数据 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/10/28
 * Time: 15:03:33
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilOrgCardRegionDayTrade;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

use Fuel\Service\OrgCardRegionDayTradeService;
use Fuel\Defines\ValueLabel;
use Fuel\Defines\OrgConf;

class oil_org_card_region_day_trade extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    public function getOrgConsumeList()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['month'], $params);

        if ($params['month']) {
            $params['date'] = $this->getDateRange($params['month']);
        }
        if (! empty($params['label'])) {
            $params['money'] = ValueLabel::getMoneyRange($params['label']);
        }
        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();

        $data = OrgCardRegionDayTradeService::getOrgConsumeList($params);
        Response::json($data);
    }

    public function getOrgConsumeTotal()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['month'], $params);

        if ($params['month']) {
            $params['date'] = $this->getDateRange($params['month']);
        }
        if (! empty($params['label'])) {
            $params['money'] = ValueLabel::getMoneyRange($params['label']);
        }
        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();

        $data = OrgCardRegionDayTradeService::getOrgConsumeTotal($params);
        Response::json($data);
    }

    public function getProvinceConsumeListByTopOrg()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['month', 'top_org_id'], $params);

        if ($params['month']) {
            $params['date'] = $this->getDateRange($params['month']);
        }
        $data = OrgCardRegionDayTradeService::getProvinceConsumeList($params);
        Response::json($data);
    }

    public function getCardTypeConsumeListByTopOrg()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['month', 'top_org_id'], $params);

        if ($params['month']) {
            $params['date'] = $this->getDateRange($params['month']);
        }
        $data = OrgCardRegionDayTradeService::getCardTypeConsumeList($params);
        Response::json($data);
    }

    public function exportConsumeList()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['month'], $params);

        if ($params['month']) {
            $params['date'] = $this->getDateRange($params['month']);
        }
        if (! empty($params['label'])) {
            $params['money'] = ValueLabel::getMoneyRange($params['label']);
        }

        //异步导出
        $task = (new \Jobs\TopOrgConsumeExport($params))
            ->setTaskName('机构消费统计导出')
            ->setUserInfo($this->app->myAdmin)
            ->onQueue('export_statistic')
            ->dispatch();

        Response::json(['redirect_url' => $task->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
    }
}