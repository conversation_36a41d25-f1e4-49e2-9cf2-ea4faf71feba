<?php
/**
 * 收款认领 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/04/19
 * Time: 17:49:35
 */

use Framework\Log;
use Fuel\Defines\OrgDefine;
use Fuel\Response;
use Fuel\Service\PayCompanyPushToCrm;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilAccountMoneyCharge;
use Models\OilBankRecords;
use Models\OilClaimMoney;
use Models\OilOrg;
use Models\OilPayCompany;
use Models\OilStationSupplier;
use Models\OilStationArea;

class oil_claim_money extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @title   列表查询
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @since
     * @params  type filedName required?
     * @return mixed
     */
    public function getList()
    {
        $params = helper::filterParams();

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = (new \Jobs\ExportClaimMoneyJob($params))
                ->setTaskName('认领单-导出')
                ->setUserInfo($this->app->myAdmin)
                ->onQueue('export')
                ->dispatch();

            \Framework\Log::error('jobDebug:' . var_export($data, TRUE), [], 'jobDebug');
            if (!isset($data->jobId) || !$data->jobId) {
                throw new \RuntimeException('异步任务下发失败', 2);
            }
            Response::json(["redirect_url"=>$data->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
        }else{
            $data = OilClaimMoney::getList($params);
        }

        Response::json($data);
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilClaimMoney::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilClaimMoney::add($params);

        Response::json($data, 0, '添加成功');
    }

    /**
     * 充值模版认领
     */
    public function chargeForTemplate()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['pay_no'], $params);

        Log::error('chargeForTemplate'.var_export($params,TRUE),[],'chargeForTemplate');

        if(!isset($params['details']) && !$params['details']){
            throw new \RuntimeException('导入明细不能为空', 2);
        }

        $bankInfo = OilBankRecords::getByPayNo(["pay_no" => $params['pay_no']]);
        if (!$bankInfo) {
            throw new \RuntimeException('收款账单不存在', 20);
        }
        if ($bankInfo->status == 20) {
            throw new \RuntimeException('收款账单已认领', 21);
        }
        if ($bankInfo->claim_object == \Fuel\Defines\ClaimMoneyStatus::CLAIM_OBJECT_UP) {
            throw new \RuntimeException('认领方为【上游】，无法进行下游认领', 21);
        }


        //lock
        $lock = \Framework\Cache::get($params['pay_no']);
        if($lock && $lock == 10){
            throw new \RuntimeException('此单别人正在操作中，不能进行操作',2);
        }

        $service = new \Fuel\Service\ClaimMoney();

        //add lock
        $service->lockGosBankStatus($params['pay_no']);

        $details = json_decode($params['details']);
        $nums = count($details);

        if($nums > 50){
            throw new \RuntimeException('模板导入不能大于50条',2);
        }

        $msg = '提交成功';
        if($nums <= 1){
            $service->chargeForTemplate($params);
        }else{
            //异步执行
            Log::error(__METHOD__, ['异步执行'], "chargeForTemplate");
            $task = (new \Jobs\ClaimMoneyTemplateJob($params))
                ->setTaskName('批量认领充值单')
                ->setUserInfo($this->app->myAdmin)
                ->onQueue('chargeForTemplate') //todo 生产上需要独立chanel
                ->setTries(3)
                ->dispatch();
            Log::error(__METHOD__, [$task], "chargeForTemplate");

            $msg = '异步任务已下发，在任务中心查看执行结果';
        }

        Response::json($params, 0, $msg);
    }

    /**
     * 充值认领新增
     * @return mixed
     *
     */
    public function createForCharge($args = NULL)
    {
        global $app;
        if($args){
            $params = $args;
            if(!$app->myAdmin || !$app->myAdmin->id){
                $app->myAdmin = new stdClass();
                $app->myAdmin->id = 8888;
                $app->myAdmin->true_name = '系统自动';
            }
        }else{
            $params = helper::filterParams();
        }

        Log::error('createForCharge'.var_export($params,TRUE),[],'createForCharge');

        helper::argumentCheck(['pay_company_id', 'pay_channel', 'operator_id', 'operator_name', 'payment_company', 'receipt_company',
                               'payment_fee', 'receipt_fee', 'org_id', 'money', 'arrival_money', 'transfer_amount',
                               'pay_no', 'paytime', 'account_no'], $params);

        Log::error('createForCharge'.var_export($params,TRUE),[],'createForCharge');
        if($params['arrival_money'] <= 0 || $params['money'] <= 0){
            throw new \RuntimeException('认领金额不能小于0', 2);
        }
        if ($params['arrival_money'] > $params['money']) {
            throw new \RuntimeException('到帐金额不能大于充值金额', 2);
        }

        //G7WALLET-4839
        $companyInfo = OilPayCompany::getById(['id'=>$params['pay_company_id']]);
        if(!$companyInfo){
            throw new \RuntimeException('付款公司不合法', 2001);
        }

        //对付款公司和打款公司判断。G7WALLET-5157
        if(str_replace(['(',')','（','）'],'',$params['payment_company']) != str_replace(['(',')','（','）'],'',$companyInfo->company_name)){
            throw new \RuntimeException('付款方与机构的付款公司不一致，无法认领！',2002);
        }

        $params['payment_company'] = $companyInfo->company_name;

        Capsule::connection()->beginTransaction();
        try {
            $orgInfo = \Models\OilOrg::getById(['id' => $params['org_id']]);

            $operatorInfo = \Models\OilOperators::getById(['id' => $orgInfo->operators_id]);
            if(!$operatorInfo){
                //todo 运营商状态
                throw new \RuntimeException('机构运营商不存在', 2);
            }

            if (!$orgInfo) {
                throw new \RuntimeException('机构信息不存在', 2);
            }

            $bankInfo = OilBankRecords::getByPayNo([ "pay_no"=>$params['pay_no'] ]);
            if (!$bankInfo) {
                throw new \RuntimeException('收款账单不存在', 20);
            }

            //取收款供应商信息
            $toOperator = $this->getOperatorByAccountNo($bankInfo->account_no);
            if(!$toOperator){
                throw new \RuntimeException('收款方运营商信息不存在', 21);
            }

            //operators_id 校验
            if($toOperator->id != $orgInfo->operators_id){
                throw new \RuntimeException('收款方与机构运营商不符，无法认领', 20);
            }

            if ($bankInfo->status == 20) {
                throw new \RuntimeException('收款账单已认领', 21);
            }
            if ( $bankInfo->claim_object == \Fuel\Defines\ClaimMoneyStatus::CLAIM_OBJECT_UP ) {
                throw new \RuntimeException('认领方为【上游】，无法进行下游认领', 21);
            }

            //校验认领额度
            $balanceMoney = $this->getBalanceByPayNo($params['transfer_amount'], $params['pay_no']);
            if ($params['money'] > $balanceMoney) {
                throw new \RuntimeException('认领金额超出认领额度', 2);
            }

            $params['orgcode'] = $orgInfo->orgcode;
            $params['org_name'] = $orgInfo->org_name;
            $params['status'] = 10; //默认认领成功
            $params['creator_id'] = $app->myAdmin->id;
            $params['creator_name'] = $app->myAdmin->true_name;
            $params['purpose_id'] = 1;
            $params['createtime'] = helper::nowTime();
            $params['operators_id'] = $orgInfo->operators_id;
            $params['operators_name'] = $operatorInfo->company_name;

            //自动模拟转换充值申请单提交
            $chargeObj = OilAccountMoneyCharge::claimChangeCharge($params, $orgInfo);

            $_POST['ids'] = $chargeObj->id;

            //自动模拟充值单审核
            $this->mockAudit($chargeObj->id);

            //添加认领单
            $params['business_no'] = $chargeObj->no;

            $data = OilClaimMoney::add($params);

            (new \Fuel\Service\ClaimMoney())->UpdateBankRecords($params['pay_no']);

            //圆通转账特殊处理
            $transferCheck = (new \Fuel\Service\OrgConfigService)->getOrgClaimTransferSetting($orgInfo->orgcode);

            if($transferCheck['auto_transfer'] == 1 && $transferCheck['target_org']){
                $into_org_info = Models\OilOrg::getByOrgcode($transferCheck['target_org']);
                if($into_org_info){
                    //发起自动转账操作
                    $transfer_res = (new \Fuel\Service\AccountCenter\TransferService())->createTransfer($orgInfo->id,$into_org_info->id,$params['money']);

                    $business_no = $chargeObj->no.','.$transfer_res->no;
                    OilClaimMoney::edit(['id'=>$data->id,'business_no'=>$business_no]);
                }
            }

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            Log::error('claim-exception' . strval($e), [], 'claimMoney');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
        $org_root = "";
        $org_root = substr($orgInfo->orgcode,0,6) == OrgDefine::ORG_WC ? substr($orgInfo->orgcode,0,6) : substr($orgInfo->orgcode,0,10);
        $return_info = OilPayCompany::getByOrgTreeCompanyName(['orgroot'=>$org_root, 'company_name'=>$params['payment_company']]);
        if($return_info && $return_info->is_to_crm == "1" && $orgInfo->crm_id){ //未传crm，然后开始加入查询的job里面
                $new_params = array();
                $new_params['paycompanyparentid'] = $orgInfo->crm_id; //顶级机构的crm_id
                $new_params['companyname'] = $params['payment_company']; //付款公司的名称
                $new_params['id'] = $return_info->id; //付款公司的id
                $new_params['accountgroup'] = 20;
                \Fuel\Service\ClaimMoney::addSearchCrmInfo($new_params);
        }
        //to gos
        \Fuel\Service\AccountMoneyChargeToGos::sendBatchCreateTask([$chargeObj->id]);
        PayCompanyPushToCrm::push($params['payment_company']);
        if($args){
            return TRUE;
        }else{
            Response::json($data, 0, '添加成功');
        }

    }

    public function getOperatorByAccountNo($account_no){
        return \Models\OilOperators::where('platform_bank_account_no',$account_no)->first();
    }

    public function getOperatorInfoByName($payCompanyName)
    {
        $name = [$payCompanyName];
        if( stripos($payCompanyName,")") !== false || stripos($payCompanyName,"(") !== false ){
            $_tmpName = str_replace("(","（",$payCompanyName);
            $_tmpName = str_replace(")","）",$_tmpName);
            array_push($name,$_tmpName);
        }
        if( stripos($payCompanyName,"）") !== false || stripos($payCompanyName,"（") !== false ){
            $_tmpName = str_replace("）",")",$payCompanyName);
            $_tmpName = str_replace("（","(",$_tmpName);
            array_push($name,$_tmpName);
        }

        return \Models\OilOperators::whereIn('company_name',$name)->first();
    }

    /*
     * 根据流水号取获取认领表判断认领状态和剩余金额
     */
    public function getBalanceByPayNo($transfer_amount, $pay_no)
    {
        $claimList = OilClaimMoney::getByPayNO($pay_no);
        if ($claimList->count() > 0) {
            $sumMoney = $claimList->sum('money');
            $balance = bcsub($transfer_amount,$sumMoney,2); //改为高精度减法
        } else {
            $balance = $transfer_amount;
        }

        return $balance;
    }

    public function mockAudit($chargeId)
    {
        $module = 'oil_account_money_charge';
        require_once $this->app->getModuleRoot() . $module . DIRECTORY_SEPARATOR . 'control.php';

        $module = new $module;
        /* 调用相应的方法。*/
        call_user_func_array([&$module, 'cardAuditForMock'], ['ids' => $chargeId]);
    }

    /*
     * 根据付款公司和付款帐号获取相关付款信息
     */
    public function getOrgByPaymentCompany()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['payment_company', 'account_no'], $params);

        $payCompany = \Models\OilPayCompany::getByPayCompanyName($params['payment_company']);

        if (!$payCompany) {
            throw new \RuntimeException('此付款记录未维护付款公司,请到机构维护中进行维护', 2);
        }

        $payCompanyMap = $noPayInfoOrgroot = [];
        foreach ($payCompany as $value) {
            //根据机构信息查看付款信息
            $payInfo = \Models\OilPayInfo::getByOrgrootAccountNo([
                'orgroot'    => $value->orgroot,
                'account_no' => $params['account_no']
            ]);

            if($payInfo){
                $orgcodes[] = $value->orgroot;
                $payCompanyMap[$value->orgroot] = ['pay_company_id' => $value->id,
                                                   'pay_info_id'    => $payInfo];
            }else{
                $noPayInfoOrgroot[] = $value->orgroot;
            }

//            if (!$payInfo) {
//                throw new \RuntimeException($value->orgroot . '此付款记录未维护付款信息,请到机构维护中进行维护', 2);
//            }
        }

        if(!$orgcodes){
            throw new \RuntimeException(implode(',',$noPayInfoOrgroot) . '此付款记录未维护付款信息,请到机构维护中进行维护', 2);
        }

//        Capsule::connection()->enableQueryLog();
        $sql = "select
id,orgcode,CONCAT_WS(' ',orgcode,org_name) as org_name
from
oil_org
where substr(orgcode from 1 for 6) in ('" . implode("','", $orgcodes) . "')
and is_del = 0
        ";

        if (isset($params['keyword']) && $params['keyword']) {
            $sql .= " and (orgcode like '%" . $params['keyword'] . "%' or org_name like '%" . $params['keyword'] . "%')";
        }
        $orgList = Capsule::connection()->select($sql);
//        $sql = Capsule::connection()->getQueryLog();

        if (!$orgList) {
            throw new \RuntimeException('此付款记录未匹配到机构,请到机构维护中进行维护', 2);
        }

        foreach ($orgList as &$val) {
            $val->pay_company_id = isset($payCompanyMap[substr($val->orgcode, 0, 6)]) ? $payCompanyMap[substr($val->orgcode, 0, 6)]['pay_company_id'] : NULL;
        }

        Response::json($orgList, 0);

    }

    /**
     * @title   认领充值用款检查付款公司
     * <AUTHOR>
     */
    public function getPaymentCompany()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['payment_company'], $params);

        $payCompany = \Models\OilPayCompany::getByPayCompanyName($params['payment_company']);

        $sql = "select
id,orgcode,CONCAT_WS(' ',orgcode,org_name) as org_name
from
oil_org
where is_del = 0
        ";

        if (isset($params['keyword']) && $params['keyword']) {
            $sql .= " and (orgcode like '%" . $params['keyword'] . "%' or org_name like '%" . $params['keyword'] . "%')";
        }

        $isHas = 1;
        if ( count($payCompany) == 0) {
            $sql .= " and 1 = 1 order by orgcode asc limit 100";
            $isHas = 0;
            $orgAll = Capsule::connection()->select($sql);
        }else{
            $isSelfOrg = [];
            $isAllOrg = [];
            foreach ($payCompany as $value) {
                if($value->receipt_mode == 2){
                    $isSelfOrg[] = $value->orgroot;
                }else{
                    $isAllOrg[] = $value->orgroot;
                }

                $payCompanyMap[$value->orgroot] = ['pay_company_id' => $value->id,"pay_company_name"=>$value->company_name];
            }
            if(count($isSelfOrg) > 0){
                $sqlSelf = $sql ." and orgcode in ('" . implode("','", $isSelfOrg) . "') order by orgcode asc";
                $orgSlef = Capsule::connection()->select($sqlSelf);
            }
            if(count($isAllOrg) > 0){
                $sqlAll =  $sql . " and substr(orgcode from 1 for 6) in ('" . implode("','", $isAllOrg) . "') order by orgcode asc";
                $orgAll = Capsule::connection()->select($sqlAll);
            }
            #$sql .= " and substr(orgcode from 1 for 6) in ('" . implode("','", $orgcodes) . "') order by orgcode asc";
            #$sql .= " and orgcode in ('" . implode("','", $orgcodes) . "') order by orgcode asc";
        }


        if(count($orgSlef) > 0 && count($orgAll) > 0){
            $orgList = array_merge($orgAll,$orgSlef);
        }elseif (count($orgAll) > 0 && count($orgSlef) == 0){
            $orgList = $orgAll;
        }elseif (count($orgAll) == 0 && count($orgSlef) > 0){
            $orgList = $orgSlef;
        }

        foreach ($orgList as &$val) {
            /*$val->pay_company_id = isset($payCompanyMap[substr($val->orgcode, 0, 6)]) ? $payCompanyMap[substr($val->orgcode, 0, 6)]['pay_company_id'] : NULL;
            $val->pay_company_name = isset($payCompanyMap[substr($val->orgcode, 0, 6)]) ? $payCompanyMap[substr($val->orgcode, 0, 6)]['pay_company_name'] : NULL;*/
            $val->pay_company_id = isset($payCompanyMap[$val->orgcode]) ? $payCompanyMap[$val->orgcode]['pay_company_id'] : NULL;
            $val->pay_company_name = isset($payCompanyMap[$val->orgcode]) ? $payCompanyMap[$val->orgcode]['pay_company_name'] : NULL;
        }
        Response::json(["isHas"=>$isHas,"orgList"=>$orgList], 0);
    }

    /**
     * 充值认领新增
     * @return mixed
     */
    public function createForOther($args = null)
    {
        global $app;
        if($args){
            $params = $args;
            if(!$app->myAdmin || !$app->myAdmin->id){
                $app->myAdmin = new stdClass();
                $app->myAdmin->id = 8888;
                $app->myAdmin->true_name = '系统自动';
            }
        }else{
            $params = helper::filterParams();
        }

        helper::argumentCheck(['receipt_company', 'org_id', 'money', 'transfer_amount', 'purpose_id',
                               'pay_no', 'paytime'], $params);

        $params['money'] = floatval(trim($params['money']));

        if ($params['purpose_id'] == 1) {
            throw new \RuntimeException('不能认领充值油卡', 2);
        }

        if($params['money'] <= 0){
            throw new \RuntimeException('认领金额不能小于0', 2);
        }

        $orgInfo = \Models\OilOrg::getById(['id' => $params['org_id']]);

        if (!$orgInfo) {
            throw new \RuntimeException('机构信息不存在');
        }

        $lock_name = 'claim_lock'.$params['pay_no'];
        $is_lock = \Framework\Cache::get($lock_name);
        $auto_claim_res = [];
        if(!$is_lock){
            Capsule::connection()->beginTransaction();
            try {

                //todo 根据付款公司及认领用途，确定认领金额去向
                $purposeInfo = \Models\OilClaimPurpose::getById(['id'=>$params['purpose_id']]);

                $bankInfo = \Models\OilBankRecords::getById(['id'=>$params['id']]);
                if(!$bankInfo){
                    throw new \RuntimeException('收款账单不存在', 20);
                    return false;
                }
                if($bankInfo->status == 20){
                    throw new \RuntimeException('收款账单已认领', 21);
                    return false;
                }

                Log::error("RPYNAM：".$bankInfo->RPYNAM."purpose:".$purposeInfo->name.",orgcode：".$orgInfo->orgcode,[],"claimMoney");
                if($bankInfo->RPYNAM == \Fuel\Defines\OrgStatus::CREDIT_COMPANY_NAME && $purposeInfo->name == \Fuel\Defines\OrgStatus::CREDIT_PURPOSE_NAME && $orgInfo->orgcode == \Fuel\Defines\OrgStatus::CREDIT_ORG_CODE) {
                    $operatorInfo = \Models\OilOperators::getById(['id' => $orgInfo->operators_id]);
                    if(!$operatorInfo){
                        //todo 运营商状态
                        throw new \RuntimeException('机构运营商不存在', 2);
                    }
                    //取收款供应商信息
                    $toOperator = $this->getOperatorByAccountNo($bankInfo->account_no);
                    if(!$toOperator){
                        throw new \RuntimeException('收款方运营商信息不存在', 21);
                    }

                    if ( $bankInfo->claim_object == \Fuel\Defines\ClaimMoneyStatus::CLAIM_OBJECT_UP ) {
                        throw new \RuntimeException('认领方为【上游】，无法进行下游认领', 21);
                    }

                    if($orgInfo->operators_id != $toOperator->id && $bankInfo->claim_object == \Fuel\Defines\ClaimMoneyStatus::CLAIM_OBJECT_DOWN){
                        throw new \RuntimeException('收款方与机构运营商不符，无法认领', 21);
                    }
                    $last_fee = $bankInfo->transfer_amount - $bankInfo->total_claimed;
                    if(bccomp($params['money'],$last_fee,2) == 1){
                        throw new \RuntimeException('认领金额超出认领额度', 22);
                        return false;
                    }
                    $this->addCashPool($bankInfo,$params['money'],$params,$orgInfo);
                    Capsule::connection()->commit();
                    Response::json([], 0, '认领成功');
                    exit;
                }

                //校验认领额度
                $balanceMoney = $this->getBalanceByPayNo($params['transfer_amount'], $params['pay_no']);
                if ($params['money'] > $balanceMoney) {
                    throw new \RuntimeException('认领金额超出认领额度', 2);
                }

                $claimService = (new \Fuel\Service\ClaimMoney());
                //如果是无车授信还款
                if($purposeInfo->name == \Fuel\Defines\OrgStatus::CREDIT_PURPOSE_REPAY){
                    $operatorInfo = \Models\OilOperators::getById(['id' => $orgInfo->operators_id]);
                    if(!$operatorInfo){
                        //todo 运营商状态
                        throw new \RuntimeException('机构运营商不存在', 2);
                    }
                    //取收款供应商信息
                    $toOperator = $this->getOperatorByAccountNo($bankInfo->account_no);
                    if(!$toOperator){
                        throw new \RuntimeException('收款方运营商信息不存在', 21);
                    }

                    if ( $bankInfo->claim_object == \Fuel\Defines\ClaimMoneyStatus::CLAIM_OBJECT_UP ) {
                        throw new \RuntimeException('认领方为【上游】，无法进行下游认领', 21);
                    }

                    if($orgInfo->operators_id != $toOperator->id && $bankInfo->claim_object == \Fuel\Defines\ClaimMoneyStatus::CLAIM_OBJECT_DOWN){
                        throw new \RuntimeException('收款方与机构运营商不符，无法认领', 21);
                    }
                    if(!isset($params['pay_company_id']) || empty($params['pay_company_id'])){
                        throw new \RuntimeException('无车授信还款请选择付款公司', 2);
                    }
                    $params = $claimService->creditRepay($bankInfo,$orgInfo,$params);
                }

                //如果是油品自授信还款
                if($purposeInfo->name == \Fuel\Defines\OrgStatus::CREDIT_PURPOSE_OWN_REPAY){
                    $operatorInfo = \Models\OilOperators::getById(['id' => $orgInfo->operators_id]);
                    if(!$operatorInfo){
                        //todo 运营商状态
                        throw new \RuntimeException('机构运营商不存在', 2);
                    }
                    $orgInfo->operators_name = $operatorInfo->company_name;//运营商名称
                    //取收款供应商信息
                    $toOperator = $this->getOperatorByAccountNo($bankInfo->account_no);
                    if(!$toOperator){
                        throw new \RuntimeException('收款方运营商信息不存在', 21);
                    }

                    if ( $bankInfo->claim_object == \Fuel\Defines\ClaimMoneyStatus::CLAIM_OBJECT_UP ) {
                        throw new \RuntimeException('认领方为【上游】，无法进行下游认领', 21);
                    }

                    //G7WALLET-5396
                    if($orgInfo->operators_id != $toOperator->id){
                        //throw new \RuntimeException('收款方与机构运营商不符，无法认领', 21);
                    }

//                    if(!isset($params['pay_company_id']) || empty($params['pay_company_id'])){
//                        throw new \RuntimeException('油品自授授信还款请选择付款公司', 2);
//                    }
                    $res = $claimService->ownCreditRepay($bankInfo,$orgInfo,$params,$toOperator->id);
                    $params = $res['params'];
                    $auto_claim_res = ['bill_ids'=>$res['bill_ids'],'credit_ids'=>$res['credit_ids']];
                    PayCompanyPushToCrm::push($params['payment_company']);
//                    $claimMoneyId = $res['claim_money_id'];
                }else{

                    $operatorInfo = \Models\OilOperators::getById(['id' => $orgInfo->operators_id]);
                    if(!$operatorInfo){
                        //todo 运营商状态
                        throw new \RuntimeException('机构运营商不存在', 2);
                    }

                    $params['orgcode'] = $orgInfo->orgcode;
                    $params['org_name'] = $orgInfo->org_name;
                    $params['status'] = 10; //默认认领成功
                    $params['creator_id'] = $this->app->myAdmin->id;
                    $params['creator_name'] = $this->app->myAdmin->true_name;
                    $params['createtime'] = helper::nowTime();
                    $params['operators_id'] = $orgInfo->operators_id;
                    $params['operators_name'] = $operatorInfo->company_name;

                    $data = OilClaimMoney::add($params);
                    PayCompanyPushToCrm::push($params['payment_company']);
//                    $claimMoneyId = $data->id;
                }

                $claimService->UpdateBankRecords($params['pay_no']);

                //释放锁
                \Framework\Cache::forget($lock_name);

                Capsule::connection()->commit();
            } catch (Exception $e) {
                //释放锁
                \Framework\Cache::forget($lock_name);

                Log::error('claim-exception' . strval($e), [], 'claimMoney');
                Capsule::connection()->rollBack();

                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }
        }else{
            throw new \RuntimeException('错误，有他人正在进行操作',2);
        }

        if($args){
            return $auto_claim_res;
        }else{
            Response::json($data, 0, '添加成功');
        }
    }

    /*
     * 获取认领用途列表
     */
    public function getPurposeMap()
    {
        $params = helper::filterParams();
        $data = \Models\OilClaimPurpose::getPurposeMap($params);
        Response::json($data, 0, '添加成功');
    }

    /*
     * 获取认领用途列表
     */
    public function getPurposeMapForSerach()
    {
        $params = helper::filterParams();
        $data = \Models\OilClaimPurpose::getPurposeMapForSerach($params);
        Response::json($data, 0, '添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilClaimMoney::edit($params);

        Response::json($data, 0, '编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilClaimMoney::remove($params);

        Response::json($data);
    }

    /**
     * 认领撤销
     */
    public function unAudit()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['ids'], $params);

        Capsule::connection()->beginTransaction();
        try {
            $claimInfo = OilClaimMoney::getByIdLock(['id' => $params['ids']]);

            if (!$claimInfo) {
                throw new \RuntimeException('认领单已不存在', 2);
            }

            if ($claimInfo->status == 20) {
                throw new \RuntimeException('认领单已撤销，无需再次撤销', 2);
            }

            if ($claimInfo->claim_object == \Fuel\Defines\ClaimMoneyStatus::CLAIM_OBJECT_UP) {
                (new \Fuel\Service\ClaimMoney())->claimBackForUpstream($claimInfo);
                //throw new \RuntimeException('上游认领单，无法撤销认领', 2);
                Capsule::connection()->commit();
                Response::json([], 0, '撤销成功');
                return false;
            }

            //增加运营商判断
            if($claimInfo->org_id){
                $orgInfo = OilOrg::getById(['id'=>$claimInfo->org_id]);
                if(!$orgInfo){
                    throw new \RuntimeException('机构不存在，无法撤销认领', 2);
                }
                if($claimInfo->operators_id != $orgInfo->operators_id){
                    throw new \RuntimeException('收款方与机构运营商不符，无法撤销认领', 2);
                }
            }

            $isPose = false;
            $purposeMap = \Models\OilClaimPurpose::getById(['id'=>$claimInfo->purpose_id]);
            if($claimInfo->purpose_id == 24 && $purposeMap->name == \Fuel\Defines\OrgStatus::CREDIT_PURPOSE_REPAY){
                throw new \RuntimeException('无车授信还款不支持撤销', 2);
            }
            if($purposeMap->name == \Fuel\Defines\OrgStatus::CREDIT_PURPOSE_OWN_REPAY){
                throw new \RuntimeException('油品自授信还款暂不支持撤销功能', 2);
            }
            if($purposeMap->id && $purposeMap->name == \Fuel\Defines\OrgStatus::CREDIT_PURPOSE_NAME){
                $isPose = true;
            }
            if($claimInfo->orgcode == \Fuel\Defines\OrgStatus::CREDIT_ORG_CODE && $claimInfo->payment_company == \Fuel\Defines\OrgStatus::CREDIT_COMPANY_NAME && $isPose){
                $this->revokeCashPool($claimInfo);
                Capsule::connection()->commit();
                Response::json([], 0, '撤销成功');
                return false;
            }
            //充值单撤销
            if ($claimInfo->purpose_id == 1 && $claimInfo->business_no) {
                //判断是否有认领转账
                $business_nos = explode(',',$claimInfo->business_no);
                if(count($business_nos) > 1){
                    //只有认领转账才会有这种情况
                    throw new \RuntimeException('转账认领不支持撤销', 2);
                }

                $chargeInfo = OilAccountMoneyCharge::getByNo($claimInfo->business_no);
                if ($chargeInfo) {
                    //模拟充值单销审
                    \Fuel\Service\AccountCharge::unAuditForClaim($chargeInfo->id);
                }
            }

            $data = $claimInfo->update([
                'status' => 20,
                'last_operator'=> $this->app->myAdmin->true_name,
                "updatetime" => \helper::nowTime(),
                "revoke_time" => \helper::nowTime()
            ]);

            (new \Fuel\Service\ClaimMoney())->UpdateBankRecords($claimInfo->pay_no);

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
        Response::json($data,0,'撤销成功');
    }

    /**
     * @title   获取收款认领单状态
     * <AUTHOR>
     */
    public function getStatus()
    {
        $info = \Fuel\Defines\ClaimMoneyStatus::getAll();
        $data = [];
        foreach ($info as $k => $v) {
            $tmp = [];
            $tmp['key'] = $k;
            $tmp['value'] = $v;
            $data[] = $tmp;
        }

        Response::json($data);
    }

    /**
     * @title   无车承运认领后，增加资金池金额
     * <AUTHOR>
     */
    public function addCashPool($info,$arrival_money,$claimArr,$orgInfo)
    {
        $orgCode = \Fuel\Defines\OrgStatus::getCreditOrgCode();
        $orgId = \Models\OilOrg::getInferior(["orgcode"=>$orgCode[0]]);
        $creditInfo = \Models\OilCreditAccount::getByOrgId(['org_id'=>$orgId[0]]);
        $creditAccount = $creditInfo[0];
        if(!$creditAccount->id){
            throw new \RuntimeException('结算无车承运账单，信用账户不存在', 2);
            return false;
        }

        Capsule::connection()->beginTransaction();

        try{
            $upArr['id'] = $creditAccount->id;
            $upArr['cash_pool'] = $creditAccount->cash_pool + $arrival_money;
            \Models\OilCreditAccount::edit($upArr);

            $carrierArr['type'] = 1;
            $carrierArr['transfer_fee'] = $arrival_money;
            $carrierArr['no'] = $info->REFNBR;
            $carrierArr['remark'] = "认领无车承运账单";
            \Models\OilCarrierAccountLog::add($carrierArr);


            $claimArr['orgcode'] = $orgInfo->orgcode;
            $claimArr['org_name'] = $orgInfo->org_name;
            $claimArr['status'] = 10; //默认认领成功
            $claimArr['creator_id'] = $this->app->myAdmin->id;
            $claimArr['creator_name'] = $this->app->myAdmin->true_name;
            $claimArr['createtime'] = helper::nowTime();

            OilClaimMoney::add($claimArr);

            Capsule::connection()->commit();
            $claimStatus = 20;
            if(bccomp( ( $arrival_money + $info->total_claimed) ,$info->transfer_amount,2) != 0 ){
                $claimStatus = 15;
            }
            //todo 更改收款中心状态已认领
            \Models\OilBankRecords::claimMoney($info->REFNBR,$arrival_money + $info->total_claimed,$claimStatus);
        }catch (\Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return true;
    }

    /**
     * @title   无车承运撤销认领，减少资金池金额
     * <AUTHOR>
     */
    public function revokeCashPool($params)
    {
        $orgCode = \Fuel\Defines\OrgStatus::getCreditOrgCode();
        $orgId = \Models\OilOrg::getInferior(["orgcode"=>$orgCode[0]]);
        $creditInfo = \Models\OilCreditAccount::getByOrgId(['org_id'=>$orgId[0]]);
        $creditAccount = $creditInfo[0];
        if(!$creditAccount->id){
            throw new \RuntimeException('结算无车承运账单，信用账户不存在', 2);
            return false;
        }

        $info = \Models\OilBankRecords::getByPayNo(['pay_no'=>$params['pay_no']]);
        if(!$info){
            throw new \RuntimeException('收款账单不存在', 2);
            return false;
        }
        if($info->status == 10){
            throw new \RuntimeException('收款账单未认领', 2);
            return false;
        }
        if(bccomp($creditAccount->cash_pool,$params->money,2) == -1){
            throw new \RuntimeException('资金池余额不足，撤销失败', 2);
            return false;
        }
        try{

            $upArr['id'] = $creditAccount->id;
            $upArr['cash_pool'] = $creditAccount->cash_pool - $params->money;
            \Models\OilCreditAccount::edit($upArr);

            $carrierArr['type'] = 2;
            $carrierArr['transfer_fee'] = $params->money;
            $carrierArr['no'] = $info->REFNBR;
            $carrierArr['remark'] = "撤销认领无车承运账单";
            \Models\OilCarrierAccountLog::add($carrierArr);

            $upClaim['id'] = $params->id;
            $upClaim['status'] = 20;
            $upClaim['last_operator'] = $this->app->myAdmin->true_name;
            $upClaim["updatetime"] = \helper::nowTime();
            $upClaim['revoke_time'] = \helper::nowTime();
            OilClaimMoney::edit($upClaim);

            Capsule::connection()->commit();
            //todo 更改收款中心状态已认领
            $claimStatus = 10;
            if(bccomp( ( $info->total_claimed - $params->money ) ,0 ,2) != 0 ){
                $claimStatus = 15;
            }
            \Models\OilBankRecords::claimMoney($info->REFNBR,$info->total_claimed - $params->money,$claimStatus);
        }catch (\Exception $e) {

            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return true;
    }

    /**
     * 认领上游来款
     */
    public function claimForSupplier()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['claim_fee','pay_no','purpose_id','account_level','account_no'], $params);
        if (in_array($params['purpose_id'], \Fuel\Defines\ClaimMoneyStatus::getNeedCompanyPurpose()) and
            empty($params['collect_company_id'])) {
            Response::json([],412001,"请选择收款公司");
        }
        $result = (new \Fuel\Service\ClaimMoney())->claimMoneyForUpstream($params);
        Response::json($result,0,"成功");
    }

    /**
     * @Notes:认领流水列表
     * @Interface getClaimList
     * @author: yuanzhi
     * @Time: 2023/11/13   5:00 PM
     */
    public function getClaimList()
    {
        $params = helper::filterParams();
        $createtimeBetween = array_get($params, 'createtimeGe', '').array_get($params, 'createtimeLe');
        $updateTimeBetween = array_get($params, 'updatetimeLe', '').array_get($params, 'updatetimeGe');

        if(empty($createtimeBetween) && empty($updateTimeBetween)
        ){
            throw new RuntimeException("认领时间区间与更新时间区间二选一", 305);
        }

        if(strlen($createtimeBetween) < 12 &&  strlen($updateTimeBetween) < 12
        ){
            throw new RuntimeException("认领时间区间与更新时间区间二选一", 305);
        }

        if(isset($params['updatetimeLe']) && isset($params['updatetimeGe'])) {
            if(strtotime($params['updatetimeLe']) - strtotime($params['updatetimeGe']) > 3600*24*180) {
                throw new RuntimeException("更新时间区间不能超过180天", 305);
            }
        }
        if(isset($params['createtimeGe']) && isset($params['createtimeLe'])) {
            if(strtotime($params['createtimeLe']) - strtotime($params['createtimeGe']) > 3600*24*180) {
                throw new RuntimeException("认领时间区间不能超过180天", 305);
            }
        }
        $result = (new \Fuel\Service\ClaimMoney())->getListByTimeWithOrgInfo($params);
        Response::json($result,0,"成功");
    }

    /**
     * 定时查询超额认领报警 超额
     */
    public function cronExcessClaim()
    {
        $start_time = date('Y-m-d',strtotime('-1 day')).' 23:00:00';
        $content = [];
        $end_time = date('Y-m-d H:i:s',strtotime('-1 hour'));
        $list = OilClaimMoney::where('status','=',10)
            ->where('createtime','>=',$start_time)
            ->where('createtime','<',$end_time)
            ->selectRaw('pay_no,sum(money) as claim_total,transfer_amount,receipt_company,payment_company,paytime')
            ->groupBy('pay_no')
            ->get();

        if($list->count() > 0){
            foreach ($list as $item){
                if($item->claim_total > $item->transfer_amount){
                    $_content = [
                        '收款方: '.$item->receipt_company,
                        '付款方: '.$item->payment_company,
                        '收款流水号: '.$item->pay_no,
                        '收款金额: '.$item->transfer_amount,
                        '收款时间: '.$item->paytime,
                        '认领总金额: '.$item->claim_total,
                        "—————————————————————————————————\n"
                    ];

                    $content = array_merge($content,$_content);
                }
            }

            if($content){
                $content = array_merge($content,["请及时查看原因并紧急修复处理"]);
                if(!in_array(API_ENV ,['prod','pro'])){
                    $chat_id = 'oc_2324b9d553f2e19e3bc8b63d1181b462';
                }else{
                    $chat_id = 'oc_b8aa1d67088cb04fda66d7d58a372c03';
                }
                $evn = '[环境：'.API_ENV."]";
                $apiParams = [
                    'title'    => '【紧急重要】超额认领异常！'.$evn,
                    'chat_id'  => $chat_id,
                    'msg_type' => 'card',
                    'content'  => implode("\n",$content),
                    'at'       => [
                        '李永华' => '<EMAIL>',
                        '田徐保' => '<EMAIL>',
                        '雷庆' => '<EMAIL>',
                        '付明乐' => '<EMAIL>',
                    ]
                ];
                (new \Framework\DingTalk\FeiShuNotify())->Send($apiParams);
            }
        }

    }

    /**
     * 根据收款认领单表的purpose_id调用getNeedCompanyPurpose函数判断是否上游退款类型收款
     * 如是则通过业务单号关联oil_account_settlement清分结算表查询认领的账户类型
     * 根据认领单的org_name通过账户类型查询供应商、服务区、主卡表相应记录
     * 并将相应表的id更新值收款认领单表的org_id中
     */
    public function initOrgIdByOrgName()
    {
        // 获取需要处理的purpose_id列表
        $needCompanyPurposeIds = \Fuel\Defines\ClaimMoneyStatus::getNeedCompanyPurpose();
        // 查找所有符合条件的认领单：purpose_id在需要处理的列表中，且org_id为空或为0
        $claimRecords = OilClaimMoney::whereIn('purpose_id', $needCompanyPurposeIds)
            ->where(function($query) {
                $query->whereNull('org_id')
                      ->orWhere('org_id', 0);
            })
            ->where('paytime', '>=', '2025-01-01 00:00:00')
            ->get();
        foreach ($claimRecords as $claim) {
            // 跳过没有业务单号的记录
            if (empty($claim->business_no)) {
                continue;
            }
            try {
                // 通过业务单号关联oil_account_settlement清分结算表查询认领的账户类型
                $settlementInfo = \Models\OilAccountSettlement::where('apply_no', $claim->business_no)->first();
                if (!$settlementInfo) {
                    Log::error("清分结算单不存在", [
                        'claim_id' => $claim->id,
                        'business_no' => $claim->business_no,
                    ], 'initClaimMoneyOrgId');
                    continue;
                }

                $accountType = $settlementInfo->account_type;
                $orgName = trim($claim->org_name);
                $orgId = null;
                $supplierId = null;
                $supplierName = null;
                // 根据账户类型查询相应表
                switch ($accountType) {
                    // 油站供应商
                    case 1:
                        $supplier = \Models\OilStationSupplier::where('supplier_name', $orgName)->first();
                        if ($supplier) {
                            $orgId = $supplier->id;
                        }
                        break;
                    // 服务区
                    case 2:
                        $area = \Models\OilStationArea::where('name', $orgName)->first();
                        if ($area) {
                            $orgId = $area->id;
                        }
                        break;
                    // 主卡
                    case 5:
                        $mainCard = \Models\OilCardMain::where('main_no', $orgName)->first();
                        if ($mainCard) {
                            $orgId = $mainCard->id;
                        }
                        break;
                }
                if (!$orgId) {
                    Log::error("收款认领单初始化org_id失败", [
                        'claim_id' => $claim->id,
                        'org_name' => $orgName,
                        'account_type' => $accountType,
                    ], 'initClaimMoneyOrgId');
                    continue;
                }
                // 更新收款认领单表的org_id
                OilClaimMoney::edit([
                    'id' => $claim->id,
                    'org_id' => $orgId,
                ]);
            } catch (Throwable $e) {
                Log::error("收款认领单初始化org_id失败", [
                    'claim_id' => $claim->id,
                    'exception' => $e->getMessage(),
                    'org_name' => $orgName,
                    'account_type' => $accountType,
                ], 'initClaimMoneyOrgId');
            }
        }
    }

    public function initSupplierIdAndNameByOrgName()
    {
        // 获取需要处理的purpose_id列表
        $needCompanyPurposeIds = \Fuel\Defines\ClaimMoneyStatus::getNeedCompanyPurpose();
        // 查找所有符合条件的认领单：purpose_id在需要处理的列表中，且org_id为空或为0
        $claimRecords = OilClaimMoney::whereIn('purpose_id', $needCompanyPurposeIds)
                                     ->where('supplier_id', '=', 0)
                                     ->where('paytime', '>=', '2025-01-01 00:00:00')
                                     ->get();
        foreach ($claimRecords as $claim) {
            // 跳过没有业务单号的记录
            if (empty($claim->business_no)) {
                continue;
            }
            try {
                // 通过业务单号关联oil_account_settlement清分结算表
                $settlementInfo = \Models\OilAccountSettlement::where('apply_no', $claim->business_no)->first();
                if (!$settlementInfo) {
                    Log::error("清分结算单不存在", [
                        'claim_id'    => $claim->id,
                        'business_no' => $claim->business_no,
                    ], 'initClaimMoneyOrgId');
                    continue;
                }

                $supplierAccount = \Models\OilSupplierAccount::where('id', $settlementInfo->account_id)->first();
                $supplierInfo = \Models\OilStationSupplier::getById([
                    'id' => $supplierAccount->supplier_id,
                ]);
                if (!$supplierAccount or !$supplierInfo) {
                    Log::error("收款认领单初始化供应商信息失败", [
                        'claim_id'   => $claim->id,
                        'account_id' => $settlementInfo->account_id,
                    ], 'initClaimMoneySupplierIdAndName');
                    continue;
                }
                OilClaimMoney::edit([
                    'id'            => $claim->id,
                    'supplier_id'   => $supplierAccount->supplier_id,
                    'supplier_name' => $supplierInfo->supplier_name,
                ]);
            } catch (Throwable $e) {
                Log::error("收款认领单初始化供应商信息失败", [
                    'claim_id'  => $claim->id,
                    'exception' => $e->getMessage(),
                ], 'initClaimMoneySupplierIdAndName');
            }
        }
    }

    public function initCollectCompanyId()
    {
        $sheet = $PHPExcel->getActiveSheet();

        // 获取总行数
        $highestRow = $sheet->getHighestRow();

        // 初始化计数器
        $totalRecords = 0;
        $updatedRecords = 0;
        $failedRecords = 0;
        $notFoundRecords = 0;

        // 开始数据库事务
        Capsule::connection()->beginTransaction();

        try {
            // 从第2行开始读取数据（假设第1行是标题行）
            for ($row = 2; $row <= $highestRow; $row++) {
                $businessNo = trim($sheet->getCell('A' . $row)->getValue());
                $collectCompanyId = trim($sheet->getCell('B' . $row)->getValue());
                $collectCompanyName = trim($sheet->getCell('C' . $row)->getValue());

                // 跳过空行
                if (empty($businessNo)) {
                    continue;
                }

                $totalRecords++;

                // 查找对应的认领单记录
                $claimRecord = OilClaimMoney::where('business_no', $businessNo)->first();

                if (!$claimRecord) {
                    echo "Record not found for business_no: {$businessNo}\n";
                    Log::error("update_claim_money_company: Record not found for business_no: {$businessNo}", [], 'update_claim_money');
                    $notFoundRecords++;
                    continue;
                }

                try {
                    // 更新记录
                    $updateResult = OilClaimMoney::where('id', $claimRecord->id)
                                                 ->update([
                                                     'collect_company_id' => $collectCompanyId,
                                                     'collect_company_name' => $collectCompanyName,
                                                     'updatetime' => date('Y-m-d H:i:s')
                                                 ]);

                    if ($updateResult) {
                        $updatedRecords++;
                        echo "Updated record for business_no: {$businessNo}\n";
                    } else {
                        $failedRecords++;
                        echo "Failed to update record for business_no: {$businessNo}\n";
                        Log::error("update_claim_money_company: Failed to update record for business_no: {$businessNo}", [], 'update_claim_money');
                    }
                } catch (Exception $e) {
                    $failedRecords++;
                    echo "Error updating record for business_no: {$businessNo} - " . $e->getMessage() . "\n";
                    Log::error("update_claim_money_company: Error updating record for business_no: {$businessNo} - " . $e->getMessage(), [], 'update_claim_money');
                }
            }

            // 提交事务
            Capsule::connection()->commit();

            // 输出结果统计
            echo "\nUpdate completed.\n";
            echo "Total records processed: {$totalRecords}\n";
            echo "Records updated successfully: {$updatedRecords}\n";
            echo "Records not found: {$notFoundRecords}\n";
            echo "Records failed to update: {$failedRecords}\n";

        } catch (Exception $e) {
            // 回滚事务
            Capsule::connection()->rollBack();
            echo "Error: " . $e->getMessage() . "\n";
            Log::error("update_claim_money_company: " . $e->getMessage(), [], 'update_claim_money');
        }
    }
}
