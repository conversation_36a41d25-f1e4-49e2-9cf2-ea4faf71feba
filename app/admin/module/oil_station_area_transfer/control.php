<?php
/**
 * oil_station_area_transfer Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/07/15
 * Time: 15:32:03
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilStationAreaTransfer;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_station_area_transfer extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilStationAreaTransfer::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => 'oil_station_area_transfer_' . date("YmdHis"),
            'sheetName' => 'oil_station_area_transfer',
            'title' => [
            'id'   =>  '自增ID',
'area_id'   =>  '服务区ID',
'from_supplier_id'   =>  '转出供应商',
'to_supplier_id'   =>  '转入供应商',
'origin_statistics_type'   =>  '服务区原始统计方式 10:油卡，20：记账',
'new_statistics_type'   =>  '服务区新统计方式 10:油卡，20：记账',
'creator'   =>  '创建人',
'createtime'   =>  '创建时间',
'updatetime'   =>  '更新时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilStationAreaTransfer::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilStationAreaTransfer::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilStationAreaTransfer::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilStationAreaTransfer::remove($params);

        Response::json($data);
    }

}