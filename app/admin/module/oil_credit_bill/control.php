<?php
/**
 * 授信账户账单 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/09/24
 * Time: 11:43:50
 */

use Fuel\Defines\CreditProvider;
use Fuel\Service\Credit;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilAccountAssign;
use Models\OilAccountMoneyCharge;
use Models\OilCardViceTrades;
use Models\OilCreditBill;
use Framework\Excel\ExcelWriter;
use Framework\Excel\ExcelReader;
use Fuel\Response;
use \Framework\Log;
use \Fuel\Defines\CreditBillStatus;
use \Models\OilCreditAccount;
use \Models\OilOrg;
use Symfony\Component\Translation\Translator;
use Models\OilCronMonitor;

class oil_credit_bill extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();
        $params['is_glp'] = 20;
        Log::info('账单列表查询参数--'.var_export($params,true),[],'CreditBill');
        $data = OilCreditBill::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            if(isset($params['is_charge']) && $params['is_charge'] == 1){
                if($data->count() > 0){
                    $ids = [];
                    foreach ($data as $item){
                        $ids[] = $item->id;
                    }

                    $detail = \Models\OilCreditBillDetails::getList(['_export'=>1,'id_in'=>$ids]);

                    $redirect_url = \Fuel\Service\ExportJobSrv::syncExportTask($params,"exportListForCharge",$data,$detail);
                    echo "<script>window.location.href = '/".strtolower(__CLASS__)."';window.open('".$redirect_url."')</script>";

//                    $this->exportListForCharge($data,$detail);
                }
            }else{
                $html = strtolower(__CLASS__);
                if (!empty($params['is_own']))
                {
                    $exportList = "exportListOwn";
                    $html = "oil_credit_bill_own";
                }
                else
                {
                    $exportList = "exportList";
                }
                $redirect_url = \Fuel\Service\ExportJobSrv::syncExportTask($params,$exportList,$data);
                echo "<script>window.location.href = '/".$html."';window.open('".$redirect_url."')</script>";

//                $this->exportList($data, !empty($params['is_own']));
            }
        } else {
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    public function exportList($data)
    {
        $exportData = [
            'fileName'  => '授信账户账单_' . date("YmdHis"),
            'sheetName' => '授信账户账单',
            'download'  => 1, //增加
            'title'     => [
                'order_no'             => '借款订单号',
                'bill_no'              => '借款账单号',
                'debt_money'           => '借贷金额（元）',
                'max_day'              => '最长期限（天）',
                'rate_year'            => '利率（年）',
                'interest_startdate'   => '初始起息日',
                'repayment_lastdate'   => '最晚还款日',
                '_status'               => '还款状态',
                'repay_total'          => '预计应还总额',
                'repay_principal'      => '预计应还本金',
                'repay_interest'       => '预计应还利息',
                'repaid_principal'     => '实际已还本金',
                'not_repaid_principal' => '剩余未还本金'
            ],
            'data'      => $data->toArray(),
        ];

        $url = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['order_no', 'bill_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });
        return $url;
    }

    public function exportListOwn($data)
    {
            $realData = $data->toArray();
            foreach ($realData as &$v) {
                $v['need_repaid_money'] = $v['debt_money'];
            }
            $exportData = [
                'fileName'  => '油品自授信账单_' . date("YmdHis"),
                'sheetName' => '油品自授信账单',
                'download'  => 1, //增加
                'title'     => [
                    'bill_no'              => '账单编号',
                    '_status'              => '还款状态',
                    'org_name'             => '所属机构',
                    'orgroot'              => '顶级机构',
                    'operator_name'        => '机构运营商',
                    'use_consume_money'    => '累计消费金额',
                    'oil_num'              => '累计消费升数',
                    'use_fanli_money'      => '累计使用返利',
                    'debt_money'           => '累计使用授信',
                    'need_repaid_money'    => '应还金额',
                    'repaid_total'         => '已结金额',
                    'not_repaid_principal' => '待结金额',
                    'can_repay_money'      => '可用还款金额',
                    'interest_startdate'   => '账期开始时间',
                    'repayment_lastdate'   => '账期结束时间',
                    'bill_time'            => '出账时间',
                    'proName'              => '授信产品',
                    'bill_way'             => '出账方式',
                    'createtime'           => '创建时间',
                    'updatetime'           => '更新时间',
                    'last_operator'        => '更新人',
                ],
                'data'      => $realData,
            ];

        $url = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['order_no', 'bill_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });
        return $url;
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    public function exportListForCharge($data,$detail)
    {
        $exportData = [
            'fileName'  => '授信账户账单_' . date("YmdHis"),
            'sheetName' => '授信账户账单',
            'download'  => 1, //增加
            'title'     => [
                'bill_no' => '账单编号',
                //'org_name'              => '还款机构',
                'operator_name' => '销售运营商',
                'pay_company_name' => '还款公司',
                'product_from' => '收款方',
                'debt_money' => '账单金额',
                'repaid_total' => '已结金额',
                'not_repaid_principal' => '待结金额',
                'bill_time' => '账单日',
                'proName' => '授信产品',
                'bill_way' => '出账方式',
                '_status' => '还款状态',
                'createtime' => '创建时间',
                'updatetime' => '更新时间'
            ],
            'data'      => $data->toArray(),
            'multiSheet' => [
                [
                    'sheetName' => '账单明细',
                    'title'     => [
                        'bill_no' => '对应授信账单',
                        'no_type' => '性质',
                        'no' => '单号/卡号',
                        'no_time' => '时间',
                        'money' => '金额',
                    ],
                    'data'      => $detail->toArray()
                ]
            ]
        ];

        $url = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['order_no', 'bill_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });
        return $url;
    }

    /**
     * 列表数据导出针对G7s端
     * @param  $data
     */
    public function exportForG7s()
    {
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . 'data' . DIRECTORY_SEPARATOR . 'oil_credit_bill';
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }
        $url = NULL;
        $params = helper::filterParams();
        $params['_export'] = 1;
        $data = OilCreditBill::getList($params);
        $exportData = [
            'filePath'  => $filePath,
            'fileName'  => '账单明细_' . date("YmdHis"),
            'sheetName' => '账单明细',
            'download'  => 1,
            'title'     => [
                'interest_startdate'      => '起息日',
                'debt_money'     => '借贷金额(元)',
                'rate_year'        => '利率(年)',
                'repayment_lastdate'        => '最晚还款日',
                'status'      => '还款状态',
                'totay_repay_total'     => '当前待还(元)',
                'not_repaid_principal'          => '当前待还本金(元)',
                'repay_interest'    => '当前待还利息(元)',
                'repay_other'     => '当前待还其他(元)',
                'repay_total'        => '预计应还总额(元)',
                'repay_principal' => '预计应还本金(元)',
                'should_repay_interest'    => '预计应还利息(元)',
                'repaid_total'              => '实际已还总额(元)',
                'repaid_principal'              => '实际已还本金(元)',
                'repaid_interest'              => '实际已还利息(元)',
                'settlement_time'              => '结清日期',
                'order_no'              => '借款单号',
                'bill_time'              => '账单生成时间',
            ],
            'data'      => $data->toArray(),
        ];

        $filePath = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['order_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });

        if($filePath){
            //上传阿里云
            $url = (new \commonModel())->fileUploadToOss($filePath);
        }else{
            throw new \RuntimeException('导出失败！',2);
        }

        Response::json(['url'=>$url]);
    }

    /**
     * 批量导入
     */
    public function batchImport()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['account_id'],$params);
        Log::info('$_FILES--'.var_export($_FILES,true),[],'zzg');
        //上传文件
        $upload = $this->file_upload($_FILES['userfile']);

        if (is_numeric($upload) && $upload < 0) {
            if ($upload == -9) {
                $msg = "未选择文件";
            } else if ($upload == -8) {
                $msg = "文件格式不正确";
            } else {
                $msg = "上传导入失败";
            }
            throw new \RuntimeException($msg, $upload);
        }

        $fileName = substr($_FILES['userfile']['name'],4,8);

        if(preg_match('/\d{8}/',$fileName)){
            $billTime = date('Y-m-d',strtotime($fileName));
        }else{
            throw new \RuntimeException('文件名非法',2);
        }

        $fieldMap = [
            'order_no'                 => '订单号',
            'bill_no'                  => '账单号',
            'debt_money'               => '保理金额',
            'rate_year'                => '利率（年）',
            'max_day'                  => '最长期限(天)',
            'interest_startdate'       => '初始起息日',
            'repayment_lastdate'       => '最晚还款日',
            ////////////////////////////////////
            'buyer'                    => '买方名',
            'saler'                    => '卖方名',
            //'inverse_blance_assign_no' => '预计收益',
            //////////////////////////////////////
            'repay_total'              => '预计应还总额',
            'repay_interest'           => '当前预计应还利息',
            'not_repaid_principal'     => '剩余未还本金',
            'bill_time'                => '账单生成时间',
            'status'                   => '状态',
            'repay_principal'          => '预计应还本金',
            'repaid_principal'         => '实际已还本金',
            'should_repay_interest'    => '预计收益',
            'repaid_total'             => '实际已还总额',
            'repaid_interest'          => '实际已还利息',
            'settlement_time'          => '结清日期',
        ];

        $excelParams = [
            'filePath'  => $upload,
            'fieldsMap' => array_flip($fieldMap)
        ];

        $result = ExcelReader::read($excelParams, function ($rowNum, $fieldName, $cellValue) {
            return $this->preImportCellValue($rowNum, $fieldName, $cellValue);
        });

        /*************************
         * 开启事务
         *************************/
        Capsule::connection()->beginTransaction();
        try {
            $data = array_values($result[0]);
            Log::info('账单导入参数--'.var_export($data,TRUE),[],'CreditBill');
            $nowTime = helper::nowTime();
            foreach ($data as $v){
                //若导入数据的账单号和订单号，在系统中已经存在，则需要将系统内的数据更新，否则新增一条账单数据
                $info = OilCreditBill::getSingleRecord([
                    'account_id'=>$params['account_id'],
                    'order_no'=>$v['order_no'],
                    'bill_no'=>$v['bill_no'],
                ]);
                $v['bill_time'] = $billTime;
                if($info){
                    $v['updatetime'] = $nowTime;
                    $v['last_operator'] = $this->app->myAdmin->true_name;
                    $v['repay_interest'] = str_replace(',','',$v['repay_interest']);
                    $info->update($v);

                    //to gos system
                    \Fuel\Service\BillToGos::updateToGos([$info->id]);
                }else{
                    $v['account_id'] = $params['account_id'];
                    $v['createtime'] = $nowTime;
                    $v['creator'] = $this->app->myAdmin->true_name;
                    $v['repay_interest'] = str_replace(',','',$v['repay_interest']);
                    OilCreditBill::add($v);
                }
            }


            /**********************
             * 提交事务
             **********************/
            Capsule::connection()->commit();
        } catch (Exception $e) {
            /*********************
             * 回滚事务
             *********************/
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json(TRUE,0,'导入成功');
    }

    /**
     * 单元格数据预处理
     * @param $rowNum
     * @param $fieldName
     * @param $cellValue
     * @return array|string
     */
    private function preImportCellValue($rowNum, $fieldName, $cellValue)
    {
        $data = $cellValue;
        $msg  = '';
        if ($rowNum > 1) {
            switch ($fieldName) {
                case 'status':
                    if ($cellValue) {
                        $status = CreditBillStatus::getAll();
                        if (!in_array($cellValue, $status)) {
                            $msg = '导入失败: 状态【' . $cellValue . '】必须是等待还款/部分还款/已还款';
                        } else {
                            $statusArr = array_flip($status);
                            $data     = $statusArr[ $cellValue ];
                        }
                    } else {
                        $msg = '导入失败: 状态不能为空';
                    }
                    break;
                default :
                    $data = $cellValue;
            }
        }

        if ($msg) {
            throw new \RuntimeException($msg, 2);
        }

        return $data;
    }

    //上传文件
    public function file_upload($file)
    {
        set_time_limit(0);
        if (empty($file) || empty($file['name'])) {
            //未选择文件
            return -9;
        }
        //判断文件类型
        if ($file['name']) {
            $ext = strtolower(trim(substr(strrchr($file['name'], '.'), 1)));
            if ($ext != "xls" && $ext != "xlsx") {
                //文件类型不正确
                throw new \RuntimeException('文件类型不正确', -8);
            }
            $dir = '../tmp/data';
            if (!is_dir($dir)) {
                helper::createDir($dir, 0777);
            }

            //文件上传
            $tmp_name = $file['tmp_name'];
            $newname = $dir . '/import_' . date('m-d-H-i-s') . '.' . $ext;

            if (@copy($tmp_name, $newname)) {
                @unlink($tmp_name);
            } else if (@move_uploaded_file($tmp_name, $newname)) {
            } else if (@rename($tmp_name, $newname)) {
            } else {
                //上传文件失败
                return -7;
            }
            @chmod($newname, 0777);

            return $newname;
        }
    }

    /**
     * @title   获取账单状态
     * <AUTHOR>
     */
    public function getBillStatus()
    {
        $info = CreditBillStatus::getAll();
        $data = [];
        foreach ($info as $k => $v) {
            $tmp          = [];
            $tmp['key']   = $k;
            $tmp['value'] = $v;
            $data[]       = $tmp;
        }

        Response::json($data);
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);

        $data = OilCreditBill::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['account_id'],$params);

        $params['creator'] = $this->app->myAdmin->true_name;
        $data = OilCreditBill::add($params);

        Response::json($data, 0, '添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        Log::info('授信账单修改参数--'.var_export($params,true),[],'CreditBill');
        helper::argumentCheck(['id','account_id'], $params);

        $params['last_operator'] = $this->app->myAdmin->true_name;
        $data = OilCreditBill::edit($params);

        Response::json($data, 0, '编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilCreditBill::remove($params);



        Response::json($data);
    }

    public function getBillData()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['account_id'],$params);


        $data = OilCreditBill::getBillData($params);
        Log::info('$data--'.var_export($data,TRUE),[],'zzg');
        $orgInfo = OilCreditAccount::getById(['id'=>$params['account_id']]);
        $data['org_name'] = isset($orgInfo->Org) ? $orgInfo->Org->org_name : '';
        $data['bill_time'] = isset($data['bill_time']) ? date('Y-m-d',strtotime($data['bill_time'])) : '';

        Response::json($data);
    }

    /**
     * @title 获取信用账单统计数据(G7S)
     * <AUTHOR>
     */
    public function getBillDataForG7s()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['current_orgcode'],$params);
        Log::info('账单统计数据参数--'.var_export($params,true),[],'CreditBill');

        $params['org_id_in'] = OilOrg::getByOrgcodeLike($params['current_orgcode']);

        if(isset($params['orgcode']) && $params['orgcode']){
            $params['org_id_in'] = OilOrg::getByOrgCodesFieldArr(explode(',',$params['orgcode']));
        }

        $data = OilCreditBill::getBillDataForG7s($params);

        Response::json($data);
    }

    /*
     * 还款工单
     */
    public function creditRepayList()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['current_orgcode'],$params);

        $orgInfo = OilOrg::getByOrgcode($params['current_orgcode']);
        if(!$orgInfo){
            throw new \RuntimeException('机构信息不存在');
        }
        $params['org_id'] = $orgInfo->id;

        $data = \Models\OilCreditRepay::getList($params);

        Response::json($data);
    }

    /*
     * 还款工单详情
     */
    public function creditRepayDetail()
    {
        $params = helper::filterParams();

        $data = \Models\OilCreditRepay::getDetail($params);

        Response::json($data);
    }

    /**
     * @title cron get bill list
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @throws \Fuel\Service\AccountCenter\AccountException
     * @returns
     * []
     * @returns
     */
    public function syncBillList()
    {
        $params = helper::filterParams();

        $total = 0;
        $errorTotal = 0;
        $max_third_id = OilCreditBill::max('third_id');
        $max_third_id = $max_third_id ? $max_third_id : 0;

        $max_third_id = $max_third_id - 100;
        for($page=1; $page < 10000; $page++) {
            $params['id'] = $max_third_id; //目前穿id不生效
            $res = (new \Fuel\Service\AccountCenter\AccountCreditService())->getBillList($params);

            $data = $this->formatCreditData($res);
            $total += count($data);
            Log::error('$params[\'id\']:'.$params['id'].' | $pageNo:'. $page . " | total:".$total, [], 'syncBillList');

            foreach ($data as $key=>$val) {
                $val['id'] = NULL;
                $max_third_id = $val['third_id'];
                try {
                    $checkExist = OilCreditBill::getByOrder($val['order_no']);
                    if ($checkExist && isset($checkExist->id) && $checkExist->id) {
                        $val['id'] = $checkExist->id;
                        OilCreditBill::editByOrderAndBillNo($val);
                    }else{
                        OilCreditBill::add($val);
                    }
                } catch (\Exception $e) {
                    $errorTotal += 1;
                    Log::error('syncBillList-Exception' . strval($e), $val, 'syncBillList');
                    throw new \RuntimeException($e->getMessage(),$e->getCode());
                }
            }

            if(count($res) < 50){
                Log::error('res-no-data' . var_export($params, TRUE), [], 'syncBillList');
                echo 'no data';
                break;
            }
        }

        echo 'maxId:'.$max_third_id.'finish | total:'.$total.' | $errorTotal:'.$errorTotal;
    }

    /*
     * cron执行同步账单记录
     */
    public function cronBillRecord()
    {
        $params = helper::filterParams();

        $data = Credit::cronBillRecord($params);

        echo 'over'.$data;

    }

    public function testBill()
    {
        $params = helper::filterParams();

        $params['id'] = isset($params['id']) && $params['id'] ? $params['id'] : 6467;
        //$params['startUpdateTime'] = isset($params['startUpdateTime']) && $params['startUpdateTime'] ? $params['startUpdateTime'] : '2018-05-23T20:08:07+08:00';
        $res = (new \Fuel\Service\AccountCenter\AccountCreditService())->getBillList($params);
        Response::json($res);exit;
    }

    /*
     * 一键结算
     */
    public function settleAction()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['ids'],$params);

        // 基础校验
        $idArr = explode(',', $params['ids']);

        if ($idArr) {
            $errorNum = 0;
            foreach ($idArr as $bill_id) {
                Capsule::connection()->beginTransaction();
                try {
                    // 1，更改账单状态
                    $bill_info = OilCreditBill::getByIdLock(['id'=>$bill_id]);
                    if(!$bill_info){
                        throw new RuntimeException('失败:账单已不存在', 2);
                    }

                    if($bill_info->status != 10){
                        throw new RuntimeException('失败:状态不允许操作', 2);
                    }

                    if($bill_info->not_repaid_principal > 0){
                        throw new RuntimeException('失败:有待结金额账单', 2);
                    }

                    if($bill_info->repaid_total != $bill_info->debt_money){
                        throw new RuntimeException('失败:已结和账单金额不一致', 2);
                    }

                    OilCreditBill::edit(['id'=>$bill_id,'status'=>CreditBillStatus::ALREADY_PAID]);

                    // 2，生成还款账单
                    $this->createCreditRepay($bill_info);

                    Capsule::connection()->commit();
                } catch (Exception $e) {
                    Log::error('Exception:'.$e->getMessage(),[],'settleAction');
                    Capsule::connection()->rollBack();
                    throw new RuntimeException($e->getMessage(),$e->getCode());
                    $errorNum++;
                }
            }

            $data = "总共" . count($idArr) . '条，失败' . $errorNum . '条';
        } else {
            throw new RuntimeException('参数错误', 2);
        }

        Response::json($data, 0, $data);

    }

    /**
     * 创建授信还款单
     */
    public function createCreditRepay($bill_info)
    {
        $credit_account_info = OilCreditAccount::getById(['id'=>$bill_info->account_id]);

        if(!$credit_account_info){
            throw new \RuntimeException('此授信账单对应的授信账户不存在',2);
        }

        if(strlen($credit_account_info->Org->orgcode) > 6){
            $orgroot = OilOrg::getByOrgcode(substr($credit_account_info->Org->orgcode,0,6));
            $root_org_id = $orgroot->id;
        }else{
            $root_org_id = $credit_account_info->Org->id;
        }

        $insertRepay['no'] = \Models\OilCreditRepay::createRepayNo();
        $insertRepay['no_type'] = 'HK';
        $insertRepay['org_id'] = $credit_account_info->Org->id;
        $insertRepay['root_org_id'] = $root_org_id;
        $insertRepay['operators_id'] = $credit_account_info->operators_id;
        $insertRepay['credit_provider_id'] = $credit_account_info->credit_provider_id;
        $insertRepay['credit_account_id'] = $credit_account_info->id;
        $insertRepay['apply_time'] = helper::nowTime();
        $insertRepay['repay_money'] = $bill_info->debt_money;
        $insertRepay['third_id'] = $bill_info->debt_money; //取账单的id作为third_id
        $insertRepay['billID'] = $bill_info->bill_no;
        $insertRepay['proCode'] = $bill_info->proCode;
        $insertRepay['proName'] = $bill_info->proName;
        $insertRepay['status'] = 1;
        $insertRepay['no_way'] = 10; //正常还款
        $insertRepay['creator_id'] = $this->app->myAdmin->id;
        $insertRepay['creator_name'] = $this->app->myAdmin->true_name;
        $insertRepay['createtime'] = \helper::nowTime();
        $insertRepay['pay_company_id'] = $credit_account_info->pay_company_id;
        $insertRepay['pay_company_name'] = $credit_account_info->pay_company_name;

        \Models\OilCreditRepay::add($insertRepay);

        //修改授信账户到最近还款时间
        $credit_account_info->update(['last_repay_time'=>helper::nowTime()]);
    }

    public function syncBillListForUpdateTime()
    {
        $params = helper::filterParams();

        $params['id'] = 0;
        $max_update = OilCreditBill::max('third_updatetime');
        $params['startUpdateTime'] = date('c', strtotime($max_update) - 3600);
        $params['endUpdateTime'] = date('c', strtotime('-2 min'));
        //$params['startUpdateTime'] = date('c', strtotime('2019-11-14 13:00:00'));

        for($page=1; $page < 10000; $page++) {
            Log::error('startUpdateTime::' . $params['startUpdateTime'], [$params], 'syncBillList');
            $res = (new \Fuel\Service\AccountCenter\AccountCreditService())->getBillList($params);
            Log::error('res-count::' . var_export(count($res), TRUE), [], 'syncBillList');
            Log::error('res-res::' . var_export($res, TRUE), [], 'syncBillList');

            if(count($res) > 0){
                $data = $this->formatCreditData($res);
                $num = count($data);
                foreach ($data as $key=>$val) {
                    $params['id'] = $val['third_id'];

                    try {
                        //$checkExist = OilCreditBill::getbyThirdId($val['third_id']);
                        $checkExist = OilCreditBill::getByOrder($val['order_no']);
                        if ($checkExist && isset($checkExist->id) && $checkExist->id) {
                            $val['id'] = $checkExist->id;
                            OilCreditBill::editByOrderAndBillNo($val);
                        } else {
                            OilCreditBill::add($val);
                        }
                    } catch (\Exception $e) {
                        //throw new \RuntimeException($e->getMessage(),$e->getCode());
                        Log::error('syncBillList-Error:' . strval($e), [], 'syncBillList');
                    }
                }
            }

            if(count($res) < 50){
                echo 'no data';
                break;
            }
        }

        echo '最大时间：'.$params['startUpdateTime'].'finish';
    }


    public function formatCreditData($data)
    {
        //todo 等待完善
        $ourCreditData = [];
        if($data)
        {
            foreach ($data as $v){
                $orgcodes[] = $v->orgCode;
            }
            $orgcodes = array_unique($orgcodes);

            //获取根据机构码获取授信账户的id
            $accountKeyVal = OilCreditAccount::getByOrgCodesMap($orgcodes);

            foreach ($data as $key=>$val)
            {
                $ourCreditData[$key]['account_id'] = isset($accountKeyVal[$val->orgCode]) ? $accountKeyVal[$val->orgCode] : NULL;
                $ourCreditData[$key]['order_no'] = $val->orderNumber; //普洛斯订单号
                $ourCreditData[$key]['bill_no'] = $val->billNumber; //普洛斯主账单号
                $ourCreditData[$key]['debt_money'] = $val->amount/100; //借贷金额（元）
                $ourCreditData[$key]['max_day'] = ''; //dueTime最长期限（天）/保理期限
                $ourCreditData[$key]['rate_year'] = $val->rate; //利率（年）
                $ourCreditData[$key]['interest_startdate'] = date('Y-m-d', strtotime($val->interestTime)); //开始计息日
                $ourCreditData[$key]['repayment_lastdate'] = date('Y-m-d', strtotime($val->endRepaymentTime)); //最晚还款日
                $ourCreditData[$key]['status'] = CreditBillStatus::getLocalType($val->status);
                $ourCreditData[$key]['repay_total'] = $val->estimateRepaymentAmount / 100; //账期预计应还总额
                $ourCreditData[$key]['repay_principal'] = $val->estimateBalance / 100; //账期预计应还本金
                $ourCreditData[$key]['repay_interest'] = $val->todayNormalInterest / 100; //当前预计应还利息
                $ourCreditData[$key]['should_repay_interest'] = $val->estimateInterest / 100; //账期预计应还利息（预计收益）
                $ourCreditData[$key]['repaid_total'] = $val->realityRepaymentAmount / 100; //实际已还总额
                $ourCreditData[$key]['repaid_interest'] = $val->realityInterest / 100; //实际已还利息
                $ourCreditData[$key]['repaid_principal'] = $val->realityBalance / 100; //实际还款本金
                $ourCreditData[$key]['not_repaid_principal'] = $val->surplusBalance / 100; //剩余未还本金
                $ourCreditData[$key]['bill_time'] = date('Y-m-d H:i:s', strtotime($val->createTime)); //账单生成时间
                $ourCreditData[$key]['settlement_time'] = $val->settleTime ? date('Y-m-d H:i:s', strtotime($val->settleTime)) : NULL; //结清日期
                $ourCreditData[$key]['third_createtime'] = date('Y-m-d H:i:s', strtotime($val->createTime)); //未知是什么时间
                $ourCreditData[$key]['third_updatetime'] = date('Y-m-d H:i:s', strtotime($val->updateTime));
                $ourCreditData[$key]['updatetime'] = \helper::nowTime(); //更新时间
                ///以下是新增字段//////////////////////////////////////////////////////////////////////////
                $ourCreditData[$key]['accountTime'] = date('Y-m-d H:i:s', strtotime($val->accountTime)); //账单核算日期
                $ourCreditData[$key]['buyerCompanyName'] = $val->buyerCompanyName; //买方名称
                $ourCreditData[$key]['dueTime'] = $val->dueTime; //保理期限 //todo 不是日期格式
                $ourCreditData[$key]['realityBreakInterest'] = $val->realityBreakInterest / 100; //实际还款罚息
                $ourCreditData[$key]['realityCompoundInterest'] = $val->realityCompoundInterest / 100; //实际还款复利
                $ourCreditData[$key]['realityGraceInterest'] = $val->realityGraceInterest / 100; //实际还款宽限期利息
                $ourCreditData[$key]['realityNormalInterest'] = $val->realityNormalInterest / 100; //实际已还正常利息
                $ourCreditData[$key]['repaymentBackUserName'] = $val->repaymentBackUserName; //还款账户名
                $ourCreditData[$key]['sellerCompanyName'] = $val->sellerCompanyName; //卖方名称
                $ourCreditData[$key]['repaymentTime'] = $val->repaymentTime ? date('Y-m-d H:i:s', strtotime($val->repaymentTime)) : NULL; //实际还款日
                $ourCreditData[$key]['serviceAmount'] = $val->serviceAmount / 100; //手续费
                $ourCreditData[$key]['todayBreakInterest'] = $val->todayBreakInterest / 100; //当前预计应还罚息
                $ourCreditData[$key]['todayCompoundInterest'] = $val->todayCompoundInterest / 100; //当前预计应还复利
                $ourCreditData[$key]['todayGraceInterest'] = $val->todayGraceInterest / 100; //当前预计应还宽限期利息
                $ourCreditData[$key]['todayNormalInterest'] = $val->todayNormalInterest / 100; //当前预计应还正常利息
                $ourCreditData[$key]['breakRate'] = $val->breakRate / 100; //违约期利率
                $ourCreditData[$key]['compoundRate'] = $val->compoundRate / 100; //复利利率
                $ourCreditData[$key]['gracePeriodRate'] = $val->gracePeriodRate / 100; //宽限期利率
                $ourCreditData[$key]['consumeStatus'] = $val->consumeStatus; //消费状态
                $ourCreditData[$key]['customerName'] = $val->customerName; //客户名称
                $ourCreditData[$key]['third_id'] = $val->id; //他们的自增id
                $ourCreditData[$key]['orgCode'] = $val->orgCode; //机构code
                $ourCreditData[$key]['overdueStatus'] = $val->overdueStatus; //逾期状态
                $ourCreditData[$key]['recentRepayTime'] = $val->recentRepayTime ? $val->recentRepayTime : NULL; //最近还款时间

            }
        }

        return $ourCreditData;
    }

    public function syncBillListToCsv()
    {
        $params = helper::filterParams();

        \Framework\Cache::put('FoosCreditBillMaxId',0);

        for($page=1; $page < 10000; $page++) {
            //增量id
            //$max_third_id = OilCreditBill::max('third_id');
            $max_third_id = \Framework\Cache::get('FoosCreditBillMaxId');

            $max_third_id = $max_third_id ? $max_third_id : 0;

            $params['id'] = $max_third_id + 1; //目前穿id不生效

            Log::error('startUpdateTime::' . $params['startUpdateTime'], [$params], 'syncBillList');
            $res = (new \Fuel\Service\AccountCenter\AccountCreditService())->getBillList($params);
            Log::error('res-count::' . var_export(count($res), TRUE), [], 'syncBillList');
//            Log::error('res-count::' . var_export($res, TRUE), [], 'syncBillList');

            if(count($res) == 0){
                echo 'no data';
                break;
            }

            $data = $this->formatCreditData($res);

            $contentArr = [
                'account_id'              => '账户id',
                'order_no'        => '普洛斯订单号',
                'bill_no'       => '普洛斯主账单号',
                'debt_money'       => '借贷金额（元）',
                'max_day'       => '保理期限',
                'rate_year'       => '利率（年）',
                'interest_startdate'       => '开始计息日',
                'repayment_lastdate'       => '最晚还款日',
                'status'       => '还款状态',
                'repay_total'       => '账期预计应还总额',
                'repay_principal'       => '账期预计应还本金',
                'repay_interest'       => '当前预计应还利息',
                'should_repay_interest'       => '账期预计应还利息（预计收益）',
                'repaid_total'       => '实际已还总额',
                'repaid_interest'       => '实际已还利息',
                'repaid_principal'       => '实际还款本金',
                'not_repaid_principal'       => '剩余未还本金',
                'bill_time'       => '账单生成时间',
                'settlement_time'       => '结清日期',
                'third_createtime'       => '结清日期',
                'third_updatetime'       => '更新时间',
                'updatetime'       => '本地更新时间',

                'accountTime'       => '账单核算日期',
                'buyerCompanyName'       => '买方名称',
                'dueTime'       => '保理期限',
                'realityBreakInterest'       => '实际还款罚息',
                'realityCompoundInterest'       => '实际还款复利',
                'realityGraceInterest'       => '实际还款宽限期利息',
                'realityNormalInterest'       => '实际已还正常利息',
                'repaymentBackUserName'       => '还款账户名',
                'sellerCompanyName'       => '卖方名称',
                'repaymentTime'       => '实际还款日',
                'serviceAmount'       => '手续费',

                'todayBreakInterest'       => '当前预计应还罚息',
                'todayCompoundInterest'       => '当前预计应还复利',
                'todayGraceInterest'       => '当前预计应还宽限期利息',
                'todayNormalInterest'       => '当前预计应还正常利息',
                'breakRate'       => '违约期利率',
                'compoundRate'       => '复利利率',
                'gracePeriodRate'       => '宽限期利率',
                'consumeStatus'       => '消费状态',
                'customerName'       => '客户名称',
                'third_id'       => '他们的自增id',
                'orgCode'       => '机构code',
                'overdueStatus'       => '逾期状态',
                'recentRepayTime'       => '最近还款时间',
            ];

            $filePath = APP_WWW_ROOT . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . 'check_policy' . DIRECTORY_SEPARATOR . 'creditBill_' . date("YmdH") . '.csv';

            if (!file_exists($filePath)) {
                file_put_contents($filePath, mb_convert_encoding(implode(',', array_values($contentArr)), 'GBK', 'UTF-8') . "\r\n", FILE_APPEND);
            }

            $num = count($data);
            foreach ($data as $key=>$val) {
                $val['order_no'] = "'".$val['order_no'];
                $val['bill_no'] = "'".$val['bill_no'];
                $val['buyerCompanyName'] = mb_convert_encoding($val['buyerCompanyName'],'GBK','UTF-8');
                $val['repaymentBackUserName'] = mb_convert_encoding($val['repaymentBackUserName'],'GBK','UTF-8');
                $val['sellerCompanyName'] = mb_convert_encoding($val['sellerCompanyName'],'GBK','UTF-8');
                $val['customerName'] = mb_convert_encoding($val['customerName'],'GBK','UTF-8');
                file_put_contents($filePath, implode(',', $val) . "\r\n", FILE_APPEND);

                if($key+1 == $num){
                    \Framework\Cache::put('FoosCreditBillMaxId',$val['third_id'],1000);
                }

//                try {
//                    //$checkExist = OilCreditBill::getbyThirdId($val['third_id']);
//                    $checkExist = OilCreditBill::getByOrderAndBillNo($val['order_no'], $val['bill_no']);
//                    if ($checkExist) {
//                        OilCreditBill::editByOrderAndBillNo($val);
//                    } else {
//                        OilCreditBill::add($val);
//                    }
//                } catch (\Exception $e) {
//                    //throw new \RuntimeException($e->getMessage(),$e->getCode());
//                    Log::error('syncBillList-Error:' . strval($e), [], 'syncBillList');
//                }
            }
        }

        echo 'maxId:'.$max_third_id.'filePath:'.$filePath.'finish';
    }

    /**
     * @title get Orgcode Credit Info
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @throws \Fuel\Service\AccountCenter\AccountException
     * @returns
     * []
     * @returns
     */
    public function getCreditInfo()
    {
        $params = helper::filterParams();
//        $params['orgCode'] = '200K40';

        $res = OilCreditBill::getCreditInfo($params);

        var_dump($res);exit;
    }

    public function exportCredits()
    {
        $data = OilCreditAccount::getAllCreditAccount();

        $allTotalRepay = [];
        if($data){
            foreach ($data as $org_name=>$orgCode){
                $creditInfo = OilCreditBill::getCreditInfo(['orgCode'=>$orgCode]);

                if($creditInfo){
                    $allTotalRepay[] = [
                        'org_name' => $org_name,
                        'orgcode' => $orgCode,
                        'totalClearingCapital' => $creditInfo->totalClearingCapital / 100,
                    ];
                }

            }
        }

        //export file
        $path = APP_ROOT . '/www/download/creditInfo';
        if (!file_exists($path)) {
            @mkdir($path);
        }

        $fileUrl = ExcelWriter::exportXls(
            [
                'filePath'  => $path,
                'fileName'  => date("YmdHis") . '_credit_repay_total',
                'fileExt'   => 'xlsx',
                'sheetName' => '机构累计已清分本金',
                'download'  => 1,
                'title'     => [
                    'orgcode'       => '机构编码',
                    'org_name'      => '机构名称',
                    'totalClearingCapital'  => '累计清分本金',
                ],
                'data'      => $allTotalRepay,
            ], function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['orgcode', 'orgname'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        }
        );

        echo $fileUrl;exit;
    }

    /**
     * @title 获取机构上个月月末以前的账单，全部为已还款状态
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getLastMonthByOrgcode()
    {
        $params = helper::filterParams();

        $res = OilCreditBill::getLastMonthByOrgcode($params);

        var_dump($res);exit;
    }

    /**
     * @title 账单按月统计
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getStatisticsByOrgCode()
    {
        $params = helper::filterParams();

        $data = OilCreditBill::getStatisticsByOrgCode($params);

        Response::json($data);
    }

    /**
     * 列表数据导出针对G7s端
     * @param  $data
     */
    public function getStatisticsByOrgCodeExport()
    {
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . 'data' . DIRECTORY_SEPARATOR . 'oil_credit_bill';
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }
        $url = NULL;
        $params = helper::filterParams();
        $params['_export'] = 1;
        $data = OilCreditBill::getStatisticsByOrgCode($params);
        $exportData = [
            'filePath'  => $filePath,
            'fileName'  => '授信账单_' . date("YmdHis"),
            'sheetName' => '授信账单',
            'download'  => 1,
            'title'     => [
                'org_name'      => '机构',
                'product_name'     => '信用产品',
                'month'        => '月份',
                'last_status'      => '还款状态',
                'repay_total'     => '当前待还',
                'updatetime'          => '更新时间',
            ],
            'data'      => $data['data'],
        ];

        $filePath = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], [])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });

        if($filePath){
            //上传阿里云
            $url = (new \commonModel())->fileUploadToOss($filePath);
        }else{
            throw new \RuntimeException('导出失败！',2);
        }

        Response::json(['url'=>\Fuel\Service\UploadService::getOssSignUrl($url)]);
    }

    public function toGosInit()
    {
        \Fuel\Service\BillToGos::init();
    }

    /**
     * @title 获取机构还款信息
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  orgCode
     * @returns
     * []
     * @returns
     */
    public function getOrgCreditInfo()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['orgCode'],$params);

        $orgInfo = OilOrg::getByOrgcode($params['orgCode']);
        $data = (new \Fuel\Service\AccountCenter\AccountCreditService())->getCreditInfo($params);
        $data->org_name = $orgInfo->org_name;

        Response::json($data);
    }

    /*
     * 获取成都账单信息
     */
    public function getRePayBill()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['orgCode'],$params);

        $params['companyCode'] = 'G7_DALIAN';
        $params['creditChannel'] = 'G7_FACTORING';

        $data = (new \Fuel\Service\AccountCenter\AccountService())->getRePayBill($params);

        Response::json($data);
    }

    /*
     * 获取成都还款账号
     */
    public function getRePayAccount()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['orgCode'],$params);

        $params['companyCode'] = 'G7_DALIAN';
        $params['creditChannel'] = 'G7_FACTORING';

        $data = (new \Fuel\Service\AccountCenter\AccountService())->getRePayAccount($params);

        Response::json($data);
    }

    /*
     * 获取成都还款流水
     */
    public function getRepayLogs()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['orgCode'],$params);

        $params['companyCode'] = 'G7_DALIAN';
        $params['creditChannel'] = 'G7_FACTORING';

        $data = (new \Fuel\Service\AccountCenter\AccountService())->getRepayLogs($params);

        Response::json($data);
    }

    /*
     * 生成自授信的账单
     */
    public function generateBill()
    {
        $data = Credit::generateBill();

        OilCronMonitor::edit(['event_name'=>'generateBill'],true);

        Response::json($data);
    }

    public function initGenerateBill()
    {
        $params = helper::filterParams();

        //Capsule::connection()->getPdo()->exec('TRUNCATE TABLE oil_credit_bill_details');
        //Capsule::connection()->getPdo()->exec("DELETE FROM oil_credit_bill WHERE proName = 'G7内部授信'");

        $data = Credit::initGenerateBill($params);

        Response::json($data);
    }

    public function getListForG7s()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['orgcode'],$params);

        $params['is_glp'] = 20;
        $data = OilCreditBill::getListForG7s($params);

        Response::json($data);
    }

    /**
     * 圆通类授信账单 G7S端支持导出
     * @throws PHPExcel_Exception
     */
    public function exportForOwnCreditBill()
    {
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . 'data' . DIRECTORY_SEPARATOR . 'oil_credit_bill';
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }
        $url = NULL;
        $params = helper::filterParams();
        helper::argumentCheck(['orgcode'], $params);
        $params['_export'] = 1;
        $params['is_glp'] = 20;
        $data = OilCreditBill::getListForG7s($params);
        if(count($data) == 0){
            throw new \RuntimeException('无数据导出',2);
        }
        $exportData = [
            'filePath'  => $filePath,
            'fileName'  => '账单列表_' . date("YmdHis"),
            'sheetName' => '账单列表',
            'download'  => 1,
            'title'     => [
                'bill_no'      => '账单编号',
                //'pay_company_name'     => '还款公司',
                'org_name'        => '所属机构',
                '_status'         => '还款状态',
                'repayment_cycle'        => '账单周期',
                'interest_startdate'      => '账单开始时间',
                'repayment_lastdate'     => '账单结束时间',
                'use_consume_money'     => '累计消费金额',
                'oil_num'               => '累计消费升数',
                'use_fanli_money'     => '累计使用返利金额',
                'debt_money'     => '累计使用授信金额',
                'not_repaid_principal'     => '应还款金额',
                'repaid_total'     => '已还款金额',
                'bill_time'     => '出账日期',
            ],
            'data'      => $data->toArray(),
        ];

        $filePath = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], [])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });

        if($filePath){
            //上传阿里云
            $url = (new \commonModel())->fileUploadToOss($filePath);
        }else{
            throw new \RuntimeException('导出失败！',2);
        }

        Response::json(['osspath'=>$url]);
    }

    public function getBillDetail()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['orgCode'],$params);

        $params['is_glp'] = 20;
        $data = OilCreditBill::getListForG7s($params);

        Response::json($data);
    }

    /**
     * 生成自授信消费出账方式客户账单
     * @return void
     * <AUTHOR> <<EMAIL>>
     * @since 2021/8/17 10:54 上午
     */
    public function generateConsumeBill()
    {
        $params = helper::filterParams();
        $validator = new Illuminate\Validation\Validator(new Translator('en'), $params, [
            'cycle' => 'in:day,week,custom,day_custom',
        ], [
            'cycle.in' => '出账周期不正确',
        ]);
        if ($validator->fails()) {

            Response::json([], 412, $validator->errors()->first());
        }
        Credit::generateConsumeBill($params);
        //运行监控
        OilCronMonitor::edit(['event_name'=>'generateConsumeBill'],true);
        echo 'ok';
    }

    public function exportCreditBillDetail()
    {
        $params = helper::filterParams();
        $validator = new Illuminate\Validation\Validator(new Translator('en'), $params, [
            'id' => 'required|numeric',
        ], [
            'id.required' => '账单ID不能为空',
            'id.numeric' => '账单ID不正确',
        ]);
        if ($validator->fails()) {

            Response::json([], 412, $validator->errors()->first());
        }
//        Credit::exportCreditBillDetail($params);

        $redirect_url = \Fuel\Service\ExportJobSrv::syncExportTask($params,"exportDetailCreditBill");
        echo "<script>window.location.href = '/".strtolower(__CLASS__)."';window.open('".$redirect_url."')</script>";

    }

    /**
     * 自授信账单-一键结清
     * @throws Exception
     */
    public function settleOwn()
    {
        $params = helper::filterParams();
        $validator = new Illuminate\Validation\Validator(new Translator('en'), $params, [
            'id' => 'required|numeric',
        ], [
            'id.required' => '账单ID不能为空',
            'id.numeric' => '账单ID不正确',
        ]);
        if ($validator->fails()) {

            Response::json([], 412, $validator->errors()->first());
        }
        $res = Credit::settleOwn($params);
        Response::json($res['data'] ?: [], $res['code'], $res['msg']);
    }

    public function exportDetailCreditBill(array $params)
    {
        $credit_bill_provider_data = OilCreditBill::getCreditBillAndProviderById($params['id'] ?: '');
        if (!$credit_bill_provider_data) {
            Response::json([], 412, "账单不存在");
        }
        $exportData = [];
        switch ($credit_bill_provider_data->bill_way) {
            case CreditProvider::BILL_WAY_ASSIGN:
                $data = OilAccountAssign::getByBillId($params['id'], false);
                if (!$data) {
                    Response::json([], 404, "无数据可导出");
                }
                $exportData = [
                    'fileName'  => '账单分配明细_' . date("YmdHis"),
                    'sheetName' => '账单分配明细',
                    'download' => 1,
                    'title'     => [
                        'no'            => '分配单号',
                        'money_total'   => '分配金额',
                        'actual_money'  => '到账金额',
                        'apply_time'    => '申请时间',
                        'last_operator' => '操作人',
                    ],
                    'data'      => $data->toArray(),
                ];
                break;
            case CreditProvider::BILL_WAY_CHARGE:
                $data = OilAccountMoneyCharge::getByBillId($params['id'], false);
                if (!$data) {
                    Response::json([], 404, "无数据可导出");
                }
                $exportData = [
                    'fileName'  => '账单充值明细_' . date("YmdHis"),
                    'sheetName' => '账单充值明细',
                    'download' => 1,
                    'title'     => [
                        'no'            => '充值单号',
                        'money'         => '充值金额',
                        'arrival_money' => '到账金额',
                        'app_time'      => '申请时间',
                        'last_operator' => '操作人',
                    ],
                    'data'      => $data->toArray(),
                ];
                break;
            case CreditProvider::BILL_WAY_TRADE:
                $data = OilCardViceTrades::getByBillId($params['id']);
                if (!$data) {
                    Response::json([], 404, "无数据可导出");
                }
                foreach ($data as $v) {
                    $v->api_id .= ' ';
                    $v->qz_drivertel .= ' ';
                    $v->actual_money = bcsub($v->trade_money, $v->use_fanli_money, 2);
                    $v->vice_no .= ' ';
                }
                $exportData = [
                    'fileName'  => '账单消费明细_' . date("YmdHis"),
                    'sheetName' => '账单消费明细',
                    'download' => 1,
                    'title'     => [
                        'api_id'          => '交易流水号',
                        'vice_no'         => '卡号',
                        'trade_money'     => '加油金额',
                        'use_fanli_money' => '返利使用金额',
                        'actual_money'    => '使用现金/授信',
                        'trade_place'     => '交易站点',
                        'oil_name'        => '油品名称',
                        'trade_num'       => '加油升数',
                        'org_name'        => '所属机构',
                        'qz_drivername'   => '司机姓名',
                        'qz_drivertel'    => '司机手机号',
                        'truck_no'        => '司机车牌号',
                        'createtime'      => '消费创建时间'
                    ],
                    'data'      => $data->toArray(),
                ];
                break;
            default:
                Response::json([], 500, "授信产品出账方式异常");
        }
        if (empty($params['_export'])) {
            Response::json([
                'data' => $exportData['data'],
                'total' => count($exportData['data']),
            ], 0, "成功");
        }
        $url = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['order_no', 'bill_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });
        return $url;
    }

    public function modifyCreditBillMoney()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['bill_id',"trade_ids"],$params);
        Log::error("修改账单金额:",[$params],"modify_bill_");
        Credit::modifyCredit($params);
        Response::json($params, 0, "success");
    }

}