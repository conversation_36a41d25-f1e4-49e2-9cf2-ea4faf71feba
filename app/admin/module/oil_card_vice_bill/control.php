<?php
/**
 * 卡流水（账单） Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/05/15
 * Time: 14:52:44
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCardViceBill;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_card_vice_bill extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilCardViceBill::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }
    
    /**
     * g7s单卡流水查询
     * 必须选择时间周期 （只能看近一年内的流水）
     */
    public function getCardBillListForG7s()
    {
        $params = helper::filterParams();
        \helper::argumentCheck(['card_no', 'trade_timeGte', 'trade_timeLte'], $params);
        
        // 分区只保留近一年数据，故只支持查一年内的流水
        if (strtotime("+1 year",strtotime($params['trade_timeGte'])) < time()) {
            throw new \RuntimeException('仅支持查询一年内的流水，更早时间的卡流水查询请联系客服', 2);
        }
        
        $data = OilCardViceBill::getCardBillListAndStat($params);
    
        Response::json($data);
    }
    
    /**
     * 微信卡流水查询
     */
    public function getWxCardBillList()
    {
        $params = helper::filterParams();
        \helper::argumentCheck(['card_no'], $params);
        
        // 仅能查看近一年流水
        $params['trade_timeGte'] = date('Y-m-d', '-1 year');
        $data = OilCardViceBill::getList($params);
        
        Response::json($data);
    }
    
    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '卡流水（账单）_' . date("YmdHis"),
            'sheetName' => '卡流水（账单）',
            'title' => [
            'id'   =>  '自增id',
'card_no'   =>  '卡号',
'res_type'   =>  '资源类型',
'res_id'   =>  '资源id',
'amount'   =>  '金额（）',
'pay_type'   =>  '付款方式',
'mobile'   =>  '手机号',
'trade_desc'   =>  '交易说明',
'trade_time'   =>  '交易时间',
'createtime'   =>  '创建时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilCardViceBill::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilCardViceBill::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilCardViceBill::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilCardViceBill::remove($params);

        Response::json($data);
    }
    
    /////////////////////////////////////////跑历史流水start////////////////////////////////////////
    /**
     * 处理历史流水数据
     */
    public function handleHistoryBill()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['month'], $params);
        $days = date('t', strtotime($params['month']. '-01')); // 每月天数
        $count =1;
        while($count <= $days) {
            var_dump($count);
            
            // 入参
            $preFix       = ($count < 10) ? '0' : '';
            $startTimeGte = $params['month'].'-'. $preFix.$count;
            $endTimeLte   = $startTimeGte.' 23:59:59';

            // 按天处理消费
            (new \Fuel\Service\CardViceBill())->handleCardViceTradesHistoryData($startTimeGte, $endTimeLte);
            
            // 按天处理分配
            (new \Fuel\Service\CardViceBill())->handleCardViceAssignHistoryData($startTimeGte, $endTimeLte);
            ++$count;
        }
        
        var_dump('cron cronHandleHistoryBill end!');
    }
    ////////////////////////////////////////////跑历史流水end//////////////////////////////////////////////
    
    public function testCreateTable()
    {
        (new \Fuel\Service\CardViceBill())->createTable();
        exit('success!!');
    }
    
    /**
     * 上线后补录5月剩余数据
     */
    public function handleMayData()
    {
        $count = 26;
        $days = 31;
        $month = '2020-05';
        while($count <= $days) {
            var_dump($count);
        
            // 入参
            $preFix       = ($count < 10) ? '0' : '';
            $startTimeGte = $month.'-'. $preFix.$count;
            $endTimeLte   = $startTimeGte.' 23:59:59';

            // 按天处理消费
            (new \Fuel\Service\CardViceBill())->handleCardViceTradesHistoryData($startTimeGte, $endTimeLte, true);
        
            // 按天处理分配
            (new \Fuel\Service\CardViceBill())->handleCardViceAssignHistoryData($startTimeGte, $endTimeLte, true);
            ++$count;
        }
        
        exit('script handleMayData over!');
    }
    
    /**
     * 删除共享卡分配和圈回记录
     */
    public function deleteShareCardAssignBill()
    {
        (new \Fuel\Service\CardViceBill())->deleteShareCardAssignBill();
        exit('script deleteShareCardBill end!');
    }
    
    public function addPartition()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['month'], $params);
        
        $data = (new \Fuel\Service\CardViceBill())->addPartition($params);
    
        Response::json($data,0,'分区添加成功');
    }
    
    /**
     * 修复卡流水交易时间
     * @param int $lastId
     */
    public function fixTradeTime($lastId=0)
    {
        var_dump("LastId:" . $lastId);
    
        //  余额列表
        $list = OilCardViceBill::select(Capsule::connection()->raw("oil_card_vice_bill.id as bill_id,oil_card_vice_trades.id as trade_id,oil_card_vice_trades.trade_time as trade_time"))
            ->where('oil_card_vice_bill.trade_time', '<', '1980-01-01')
            ->where('oil_card_vice_bill.res_type', '=', 10)
            ->where('oil_card_vice_bill.id', '>', $lastId)
            ->leftJoin('oil_card_vice_trades', 'oil_card_vice_trades.id','=','oil_card_vice_bill.res_id')
            ->orderBy('oil_card_vice_bill.id', 'asc')
            ->limit(500)
            ->get();
    
        $list = !$list ? [] : $list->toArray();
        if (count($list) == 0) {
            exit('异常卡流水数据处理完毕!');
        }
    
        $updateArr = [];
        foreach ($list as $key => $val) {
            $updateArr[$key] = [
                'id' => $val['bill_id'],
                'trade_time' => $val['trade_time'],
            ];
        }
        
        if (!empty($updateArr)) {
            OilCardViceBill::updateBatch('oil_card_vice_bill', $updateArr);
        }
    
        usleep(20);
        $this->fixTradeTime($lastId);
    }
    
    /**
     * 1号卡更名修改卡流水描述
     */
    public function fixTradeDesc()
    {
        $params = helper::filterParams();
    
        $data = (new \Fuel\Service\CardViceBill())->updateTradeDesc();
    
        Response::json($data,0,'1号卡更名修改卡流水修改完毕！');
    }
}