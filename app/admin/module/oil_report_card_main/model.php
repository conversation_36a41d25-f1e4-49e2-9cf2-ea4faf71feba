<?php
/**
 * 总账查询
 * <AUTHOR>
 */

class oil_report_card_mainModel extends model
{
	public function __construct(){
		parent::__construct();
	}
	/**
	 * 信息列表
	 * @param string $where
	 * @param string $fields
	 * <AUTHOR>
	 * @since 2015/10/29
	 */
	public function search($where='',$limit=''){
		$sql = 'SELECT %s FROM oil_report_card_main a LEFT JOIN oil_card_main b ON a.main_id=b.id';
		$fields = ' b.main_no,a.charge_total,a.fanli_money_total,a.fanli_jifen_total,a.assign_money_total,a.oil_num_total,a.oil_expend_total';
		$total = parent::findBySql($sql, $where, null, null, 'count(1) count');
		$datas = array();
		if ($total[0]->count > 0) {
			$datas = parent::findBySql($sql, $where, ' b.createtime DESC ', $limit, $fields);
		}
		$result = new stdClass();
		$result->total = $total[0]->count;
		$result->data = $datas;
		return $result;
	}

	public function add($record,$tabel) {
		$cId = parent::insert($record,$tabel);
		if($cId) {
			return $cId;
		}
		else {
			return 0;
		}
	}
	/**
	 * 清空表
	 */
	public function truncate()
	{
		$sql = "truncate table oil_report_card_main";
		return $this->exec($sql);
	}

	/**
	 * 获取总账数据
	 * @return mixed
	 */
	public function getAccountBillTotal()
	{
		$sql = "SELECT a.id main_id,b.charge_total,c.fanli_money_total,d.fanli_jifen_total,e.assign_money_total,f.oil_num_total,g.oil_expend_total
FROM oil_card_main a LEFT JOIN
(SELECT SUM(recharge_total) charge_total,card_main_id main_id FROM oil_card_main_recharge GROUP BY card_main_id) b ON a.id=b.main_id
LEFT JOIN
(SELECT SUM(money) fanli_money_total,main_id FROM oil_card_main_fanli_records GROUP BY main_id) c ON a.id=c.main_id
LEFT JOIN
(SELECT SUM(trade_jifen) fanli_jifen_total,main_no FROM oil_card_vice_trades GROUP BY main_no) d ON a.main_no=d.main_no
LEFT JOIN
(SELECT SUM(money) assign_money_total,main_id FROM (SELECT cc.assign_money money,dd.card_main_id main_id FROM oil_account_money_records aa
LEFT JOIN oil_account_assign bb ON aa.no=bb.no AND bb.status=1
LEFT JOIN oil_account_assign_details cc ON cc.assign_id=bb.id
LEFT JOIN oil_card_vice dd ON cc.vice_id=dd.id WHERE aa.no_type='FP' GROUP BY cc.id) a GROUP BY a.main_id) e ON a.id=e.main_id
LEFT JOIN
(SELECT SUM(trade_num) oil_num_total,main_no FROM oil_card_vice_trades WHERE trade_type='加油' GROUP BY main_no) f ON a.main_no=f.main_no
LEFT JOIN
(SELECT SUM(trade_money) oil_expend_total,main_no FROM oil_card_vice_trades WHERE trade_type='加油' AND consume_type=1 GROUP BY main_no) g ON a.main_no=g.main_no";
		$stmt = $this->query($sql);
		return $stmt->fetchAll();
	}

    /**
     * 总账查询--主卡每日数据生成（自动任务）
     * 每天8点15执行
     * <AUTHOR>
     * @since 2015/10/29
     */
    public function addNew($request,callable $callBack=null)
    {
        $data = $this->getAccountBillTotal();
        $addArr = array();
        $day_no = date('Ymd',time());
        foreach ($data as $k=>$v)
        {
            $addArr[$k]['day_no'] = $day_no;
            $addArr[$k]['main_id'] = $v->main_id;
            $addArr[$k]['charge_total'] = $v->charge_total ? $v->charge_total : 0;
            $addArr[$k]['fanli_money_total'] = $v->fanli_money_total ? $v->fanli_money_total : 0;
            $addArr[$k]['fanli_jifen_total'] = $v->fanli_jifen_total ? $v->fanli_jifen_total : 0;
            $addArr[$k]['assign_money_total'] = $v->assign_money_total ? $v->assign_money_total : 0;
            $addArr[$k]['oil_num_total'] = $v->oil_num_total ? $v->oil_num_total : 0;
            $addArr[$k]['oil_expend_total'] = $v->oil_expend_total ? $v->oil_expend_total : 0;
            $addArr[$k]['createtime'] = helper::nowTime();

            $condition = $addArr[$k]['charge_total'] > ***********.99 || $addArr[$k]['fanli_money_total'] > ***********.99
                || $addArr[$k]['fanli_jifen_total'] > ***********.99 || $addArr[$k]['assign_money_total'] > ***********.99;
            if($condition)
            {
                $tradeErr = array(
                    'module'     => '总账查询',
                    'data'       => '累计金额超出限制',
                    'details'    => '日期：'.$day_no.',主卡ID：'.$addArr[$k]['main_id'],
                    'createtime' => helper::nowTime(),
                );

                //添加错误日志
                $this->add($tradeErr, 'oil_error_logs');
            }
        }
        //先清空表
        $id = $this->truncate();
        //再添加总账记录
        foreach($addArr as $v)
        {
            $this->add($v,'oil_report_card_main');
        }

        if($callBack) {
            $progress = round( ($k+1)*100/count($data),2);
            $callBack($progress);
        }
    }

}
