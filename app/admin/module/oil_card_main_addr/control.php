<?php
/**
 * 卡务收件地址 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/05/22
 * Time: 15:41:19
 */

use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCardMainAddr;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_card_main_addr extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilCardMainAddr::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $this->exportList($data);
        } else {
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName'  => '卡务收件地址_' . date("YmdHis"),
            'sheetName' => '卡务收件地址',
            'title'     => [
                'id'          => '主键ID',
                'addr_flag'   => '地址标识',
                'addr_name'   => '收件人',
                'addr_mobile' => '联系电话',
                'address'     => '收件地址',
                'createtime'  => '创建时间',
                'updatetime'  => '最后修改时间',
            ],
            'data'      => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilCardMainAddr::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilCardMainAddr::add($params);

        Response::json($data, 0, '添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilCardMainAddr::edit($params);

        Response::json($data, 0, '编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilCardMainAddr::remove($params);

        Response::json($data);
    }

}