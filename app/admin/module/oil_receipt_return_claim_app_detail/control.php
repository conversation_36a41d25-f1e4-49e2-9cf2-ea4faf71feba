<?php
/**
 * 回票认领工单表 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/11/04
 * Time: 19:40:39
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilReceiptReturnClaimAppDetail;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_receipt_return_claim_app_detail extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilReceiptReturnClaimAppDetail::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
                'fileName' => '回票认领工单表_' . date("YmdHis"),
                'sheetName' => '回票认领工单表',
                'title' => [
                'id'   =>  '自增id',
                'claim_app_id'   =>  '回票认领工单id',
                'oil_type_id'   =>  '二级油品分类id',
                'oil_type_name'   =>  '二级油品分类名称',
                'receipt_money'   =>  '认领金额',
                'receipt_num'   =>  '认领数量',
                'creator_id'   =>  '创建人id',
                'creator_name'   =>  '创建人',
                'last_operator_id'   =>  '最后修改人id',
                'last_operator'   =>  '最后修改人',
                'createtime'   =>  '创建时间',
                'updatetime'   =>  '修改时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilReceiptReturnClaimAppDetail::getById($params);

        Response::json($data);
    }

    /**
     * 详情查询
     * @return object
     */
    public function getByClaimId()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['claim_id'], $params);
        $data = OilReceiptReturnClaimAppDetail::getByClaimId($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilReceiptReturnClaimAppDetail::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilReceiptReturnClaimAppDetail::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilReceiptReturnClaimAppDetail::remove($params);

        Response::json($data);
    }

}