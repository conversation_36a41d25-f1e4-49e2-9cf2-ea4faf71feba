<?php
/**
 * 发票库存流水 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/03/27
 * Time: 15:42:26
 */

use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilInvoiceStockRecords;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_invoice_stock_records extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['invoice_stock_id'], $params);

        $data = OilInvoiceStockRecords::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $this->exportList($data);
        } else {
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName'  => '发票库存流水_' . date("YmdHis"),
            'sheetName' => '发票库存流水',
            'title'     => [
                'invoice_stock_id' => '库存账户ID',
                'before_amount'    => '动前金额',
                'change_amount'    => '动账金额',
                'after_amount'     => '动后金额',
                'before_quantity'  => '动前数量',
                'change_quantity'  => '动账数量',
                'after_quantity'   => '动后数量',
                'remark'           => '备注',
                'operator_name'    => '操作人',
                'operate_time'     => '操作时间',
                'createtime'       => '创建时间',
                'updatetime'       => '更新时间',
            ],
            'data'      => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilInvoiceStockRecords::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilInvoiceStockRecords::add($params);

        Response::json($data, 0, '添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilInvoiceStockRecords::edit($params);

        Response::json($data, 0, '编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilInvoiceStockRecords::remove($params);

        Response::json($data);
    }

    /**
     * @title
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getByStockId()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['invoice_stock_id'], $params);

        $data = OilInvoiceStockRecords::getList($params);

        Response::json($data);
    }

}