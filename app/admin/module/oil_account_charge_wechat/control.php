<?php
/**
 * oil_account_charge_wechat Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/10/20
 * Time: 11:20:28
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilAccountChargeWechat;
use Framework\Excel\ExcelWriter;

class oil_account_charge_wechat extends baseControl
{

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilAccountChargeWechat::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Fuel\Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '{oil_account_charge_wechat}_' . date("YmdHis"),
            'sheetName' => '{oil_account_charge_wechat}',
            'title' => [
            
                'id'   =>  'id',

                'charge_id'   =>  '充值记录id',

                'transaction_id'   =>  '微信支付订单号',

                'result_code'   =>  'result_code',

                'is_subscribe'   =>  '是否关注公众账号',

                'trade_type'   =>  '交易类型 JSAPI、NATIVE、APP',

                'bank_type'   =>  '付款银行',

                'out_trade_no'   =>  '商户订单号（对应充值单号）',

                'total_fee'   =>  '订单总金额',

                'cash_fee'   =>  '现金支付金额',

                'cash_fee_type'   =>  'cash_fee_type',

                'settlement_total_fee'   =>  'settlement_total_fee',

                'fee_type'   =>  '货币种类',

                'openid'   =>  '用户标识',

                'attach'   =>  '商家数据包',

                'time_end'   =>  '支付完成时间',

                'original_data'   =>  '返回的原始数据',

                'status'   =>  '自定义状态',

                'createtime'   =>  '创建时间',

                'updatetime'   =>  '修改时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilAccountChargeWechat::getById($params);

        Fuel\Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilAccountChargeWechat::add($params);

        Fuel\Response::json($data);
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilAccountChargeWechat::edit($params);

        Fuel\Response::json($data);
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilAccountChargeWechat::remove($params);

        Fuel\Response::json($data);
    }

    public function getOpenid()
    {
        $openId = \Fuel\Service\weChatPay::GetOpenid();
        $openId = $openId ? $openId : '';
        $reDirectUrl = \Framework\Config::get('weChat.auth.backUrl').'?openId='.$openId;

        Header("Location: $reDirectUrl");
    }

}