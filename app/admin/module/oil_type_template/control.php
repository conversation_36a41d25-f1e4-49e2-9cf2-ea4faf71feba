<?php
/**
 * oil_type_template Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/10/24
 * Time: 12:38:14
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilTypeTemplate;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_type_template extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilTypeTemplate::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => 'oil_type_template_' . date("YmdHis"),
            'sheetName' => 'oil_type_template',
            'title' => [
                'id'   =>  'id',
                'name'   =>  '模板名称表',
                'status'   =>  '模板状态 1 在用  0停用 ',
                'creator'   =>  '创建人',
                'last_operator'   =>  '更新人',
                'createtime'   =>  '创建时间',
                'updatetime'   =>  '更新时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilTypeTemplate::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilTypeTemplate::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilTypeTemplate::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilTypeTemplate::remove($params);

        Response::json($data);
    }

}