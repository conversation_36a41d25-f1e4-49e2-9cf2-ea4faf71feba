<?php
/**
 *  资金账户查询
 * <AUTHOR>
 */
require_once APP_ROOT . DIRECTORY_SEPARATOR . 'Models' . DIRECTORY_SEPARATOR . 'OilCardMain.php';
use Fuel\Request\GasClient as GasClient;
use Models\OilAccountMoney as OilAccountMoney;
use Models\OilAccountMoneyRecords;
use Models\OilAccountJifenRecords;
use Models\OilOrg;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Response as Response;


class oil_account_money extends baseControl
{
    public $case_no;

    public function __construct()
    {
        parent::__construct();
    }
/////////////////手机加油接口方法////////////////////////
    /**
     * 获取机构庄户信息
     * <AUTHOR>
     * @since 2016/03/16
     */
    public function myAccountInfo()
    {
        $params = helper::filterParams();
        //验证
        helper::argumentCheck(['orgcode'], $params);

        //校验机构号是否有效
        $orgInfo = \Models\OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('机构号无效', 2);
        } else {
            $params['org_id'] = $orgInfo->id;
        }
        $data = OilAccountMoney::myAccountInfo($params);
        $data['currentMonthTradeMoney'] = \Models\OilCardViceTrades::getTradesMonth(['org_id' => $orgInfo->id, 'type' => 'money']);

        Response::json($data);
    }
/////////////////手机加油接口方法end////////////////////////

    /**
     * 列表查询
     */
    public function search()
    {
        $params = helper::filterParams();

        if (isset($params['orgcode']) && $params['orgcode']) {
            if (isset($params['org_flag']) && $params['org_flag']) {
                $params['org_id_list'] = OilOrg::getByOrgcodeLike($params['orgcode']);
            } else {
                $org = OilOrg::getByOrgcode($params['orgcode']);
                $params['org_id'] = $org->id;
            }
        }

        $data = Models\OilAccountMoney::getList($params);
        if (count($data) > 0) {
            foreach ($data as &$v) {
                $v->orgroot = substr($v->orgcode, 0, 6);
            }
            $orgRoots = Models\OilOrg::preOrgRoot($data, ['orgroot']);
            $orgRootInfo = Models\OilOrg::getByOrgCodesMap($orgRoots);
            $data = Models\OilOrg::convertOrgRoot2OrgName($data, $orgRootInfo, ['orgroot']);
        }

        if (isset($params['_export']) && $params['_export'] == 1) {
            foreach ($data as &$v) {
                $v = (array)$v;
            }
            $this->exportList($data);
        } else {
            Response::json($data);
        }
    }

    /**
     * @title 现金账户分页查询(接口专用)
     * <AUTHOR>
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = Models\OilAccountMoney::getList($params);

        Response::jsonToG7s($data);
    }

    /**
     * @title 获取单个账户现金余额和现金可用余额(接口专用)
     * <AUTHOR>
     */
    public function getAccountMoneyBalance()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['orgcode'], $params);

        $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
        if(!$orgInfo){
            throw new \RuntimeException('机构非法', 2);
        }

        $accountMoney = (new \Fuel\Service\AccountMoney())->setOrg($orgInfo->id);

        $accountMoneyInfo = $accountMoney->getAccountInfo();

        $accountMoneyBalance = $accountMoney->getAccountBalance();

        Response::jsonToG7s([
            'balance'=>$accountMoneyInfo->money,
            'use_balance'=>$accountMoneyBalance
        ]);
    }

    /**
     * 列表数据导出
     * @param $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName'  => '资金账户_' . date("YmdHis"),
            'sheetName' => '资金账户',
            'title'     => [
                'account_no'         => '帐号',
                'orgroot'            => '顶级机构',
                'orgcode'            => '机构编码',
                'org_name'           => '机构名称',
                'money'              => '现金余额',
                'charge_total'       => '累计充值',
                'total_transfer_in'  => '累计转入',
                'total_transfer_out' => '累计转出',
                'assign_total'       => '累计分配',
                'fanli_total'        => '累计现金返利',
                'vice_card_num'      => '副卡数量',
                'last_charge_time'   => '最后充值时间',
            ],
            'data'      => $data->toArray(),
        ];

        \Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['account_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });
    }


    //获取账户明细信息
    public function getAccountDetail()
    {
        $params = helper::filterParams();
        $wheresql = " 1 AND money_id=$params[money_id] ";
        $params['start_time'] ? $wheresql .= " AND createtime >= '$params[start_time] 00:00:00'" : '';
        $params['end_time'] ? $wheresql .= " AND createtime <= '$params[end_time] 23:59:59'" : '';
        $params['trade_type'] ? $wheresql .= " AND trade_type = $params[trade_type]" : '';
        $params['no_type'] ? $wheresql .= " AND no_type = '$params[no_type]'" : '';
        $params['no'] ? $wheresql .= " AND no = '$params[no]'" : '';

        //分页信息
        $limit = [$params['start'], $params['limit']];
        $data = $this->oil_account_money->getAccountDetail($wheresql, NULL, $limit);

        if (isset($data->data) && $data->data) {
            foreach ($data->data as $k => &$v) {
                $v->no_type_name = \Fuel\Defines\NoTypeStatus::getById($v->no_type);
            }
        }

        //输出结果
        echo json_encode($data);
    }

    /**
     * 获取单据类型
     */
    public function getNoTypeStatus()
    {
        $result = [];
        $data = \Fuel\Defines\NoTypeStatus::getAll();
        foreach ($data as $k => $v) {
            $tmp['id'] = $k;
            $tmp['name'] = $v;
            $result[] = $tmp;
        }

        Response::json($result);
    }

    /**
     * 记账卡账户明细查询
     * @param array $params
     * @return mixed
     */
    public function accountDetailsSearch(array $params)
    {
        if (empty($params['no_type'])) {
            $params['log_type_eq'] = '';
        } else if ($params['no_type'] == 'JY') {
            $params['log_type_eq'] = 11;//加油
        } else if ($params['no_type'] == 'JC') {
            $params['log_type_eq'] = 111;//加油撤销
        } else if ($params['no_type'] == 'TZ') {
            $params['log_type_eq'] = 12;//透支
        } else if ($params['no_type'] == 'TC') {
            $params['log_type_eq'] = 121;//透支撤销
        } else if ($params['no_type'] == 'CZ') {
            $params['log_type_eq'] = 2;//充值
        } else if ($params['no_type'] == 'FL') {
            $params['log_type_eq'] = 21;//返利
        } else if ($params['no_type'] == 'DF') {
            $params['log_type_eq'] = 22;//代扣服务费
        } else if ($params['no_type'] == 'FP') {
            $params['log_type_eq'] = 23;//分配
        } else {
            $params['log_type_eq'] = *****************;//其他类型：该值必须在撬装平台gas_history表中不存在
        }

        $data = GasClient::get(
            [
                'method' => 'gas.api.getOrgAccountLog',
                'data'   => $params
            ]
        );

        if (isset($params['_export']) && $params['_export'] == 1) {
            $logArr = $data;
        } else {
            $logArr = $data->result;
        }

        //数据整理
        foreach ($logArr as $v) {
            $v->account_no = '撬装账户[' . $v->account_no . ']';
            $v->account_remain = $v->balance;
            $v->no_type = $v->log_type;

            $v->income_money = sprintf("%.2f", $v->money);
            $v->spend_money = '';
            $v->remark_work = '';
        }

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $logArr;
        } else {
            $data->result = $logArr;
        }

        return $data;
    }

    /**
     * 获取账户操作记录
     */
    public function getAccountLog()
    {
        $params = helper::filterParams();

        $data = [];
        if (isset($params['account_type']) && $params['account_type'] == 1) {//现金账户
            $data = OilAccountMoneyRecords::getAccountRecords($params);
        } else if (isset($params['account_type']) && $params['account_type'] == 2) {//记账卡账户
            $data = $this->accountDetailsSearch($params);
        } else if (isset($params['account_type']) && $params['account_type'] == 3) {//积分账户
            $data = OilAccountJifenRecords::getAccountRecords($params);
        } else if (isset($params['account_type']) && $params['account_type'] == 4) {//授信账户
            $data = \Models\OilCreditAccountRecords::getAccountRecords($params);
        }

        Response::json($data);
    }

    public function getGasAccount()
    {
        $params = helper::filterParams();
        Framework\Log::debug('getGasAccount', $params);
        if (isset($params['orgcode']) && $params['orgcode']) {
            $orgCodes = explode(",", $params[',']);
            if (count($orgCodes) > 1) {
                if (isset($params['orgcodelk'])) {
                    unset($params['orgcodelk']);
                }
                if (isset($params['is_parent'])) {
                    unset($params['is_parent']);
                }
            }
        }
        $data = GasClient::post(
            [
                'method' => 'gas.api.GetIncomeList',
                'data'   => $params
            ]
        );
        Response::json($data);
    }

    /**
     * @title   平账探测
     * @desc
     * @version 1.0.0
     * <AUTHOR>
     * @since
     * @params
     * return void
     */
    public function accountChecking()
    {
        $data = OilAccountMoney::accountChecking();
        $html = '<style>.table-c table{border-right:1px solid #ddd;border-bottom:1px solid #ddd}
.table-c table td,.table-c table th{border-left:1px solid #ddd;border-top:1px solid #ddd;padding: 5px;}</style><h1>' . date("Y-m-d H:i:s") . '平账探测</h1>';
        if ($data) {
            $title = '异常！ - ';
            $html .= '<h2 style="color:red;">发现异常:(</h2>';
            $html .= '<div class="table-c"><table width="80%" border="0" cellspacing="0" cellpadding="0">
                <thead style="background-color:#f7f7f7;">
                    <tr>
                        <th>公司名称</th>
                        <th align="center">机构ID</th>
                        <th>账户详情</th>
                        <th align="right">差额</th>
                    </tr>
                </thead>';
            foreach ($data as $k => $v) {
                $html .= '<tr>';
                $html .= '<td>' . $v->orgcode . ' ' . $v->org_name . '</td>';
                $html .= '<td align="center">' . $v->org_id . '</td>';
                $html .= '<td><table width="80%" border="0" cellspacing="0" cellpadding="0"><thead style="background-color:#f7f7f7;"><tr><th width="16%">余额</th><th width="16%">累充</th><th width="16%">累返</th><th width="16%">累转入</th><th width="16%">累转出</th><th width="20%">累分</th></tr></thead>
                        <tr><td>' . $v->money . '</td><td>' . $v->charge_total . '</td><td>' . $v->fanli_total . '</td><td>' . $v->total_transfer_in . '</td><td>' . $v->total_transfer_out . '</td><td>' . $v->assign_total . '</td></tr>
                </table></td>';
                $html .= '<td align="right">' . $v->diff_money . '</td>';
                $html .= '</tr>';
            }
            $html .= '</table></div>';
        } else {
            $title = '正常！ - ';
            $html .= '<h2>未发现异常:)</h2>';
        }

        $mailList = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];
        \Framework\Mailer\MailSender::sendEmail($title . '油品平账探测', $html, $mailList, TRUE);

        Response::jsonToG7s($data);
    }

    public function accountCheckingOtherDataSources()
    {
        $assignOrder = \Models\OilAccountAssign::getOrgAssignTotalMoney();
        $chargeOrder = \Models\OilAccountMoneyCharge::getOrgChargeTotal();
        $fanLiCalculate = \Models\OilFanliCalculate::getOrgFanLiTotal();

        $transferOut = \Models\OilAccountMoneyTransfer::getOrgTransferOut();
        $transferIn = \Models\OilAccountMoneyTransfer::getOrgTransferIn();

        $accountRecordNotCharge = \Models\OilAccountMoneyRecords::getOrgNotChargeTotalMoney();
        $accountRecordCharge = \Models\OilAccountMoneyRecords::getOrgChargeTotalMoney();

        $account = OilAccountMoney::getAll();

        $data = [];
        $orgArr = [];

        if ($account) {
            foreach ($account as $v) {
                $data[$v->org_id]['account'] = (array)$v;
            }
        }

        if ($assignOrder) {
            foreach ($assignOrder as $v) {
                $data[$v->org_id]['order']['assign_total'] = $v->money;
            }
        }

        if ($chargeOrder) {
            foreach ($chargeOrder as $v) {
                if ($v->charge_type == 1) {
                    $data[$v->org_id]['order']['charge_total'] = $v->money;
                } elseif ($v->charge_type == 2) {
                    $data[$v->org_id]['order']['fanli_total'] = $v->money;
                }
            }
        }

        if ($fanLiCalculate) {
            foreach ($fanLiCalculate as $v) {
                if (isset($data[$v->org_id]['order']['fanli_total'])) {
                    $data[$v->org_id]['order']['fanli_total'] = $v->money + $data[$v->org_id]['order']['fanli_total'];
                } else {
                    $data[$v->org_id]['order']['fanli_total'] = $v->money;
                }
            }
        }

        if ($transferOut) {
            foreach ($transferOut as $v) {
                $data[$v->org_id]['order']['total_transfer_out'] = $v->money;
            }
        }

        if ($transferIn) {
            foreach ($transferIn as $v) {
                $data[$v->org_id]['order']['total_transfer_in'] = $v->money;
            }
        }

        if ($accountRecordNotCharge) {
            foreach ($accountRecordNotCharge as $v) {
                if ($v->no_type == 'FL') {
                    $data[$v->org_id]['log']['fanli_total'] = $v->money;
                } elseif ($v->no_type == 'FP') {
                    $data[$v->org_id]['log']['assign_total'] = $v->money;
                } elseif ($v->no_type == 'ZZ' && $v->trade_type == 1) {
                    $data[$v->org_id]['log']['total_transfer_in'] = $v->money;
                } elseif ($v->no_type == 'ZZ' && $v->trade_type == -1) {
                    $data[$v->org_id]['log']['total_transfer_out'] = $v->money;
                } elseif ($v->no_type == 'FL') {
                    if (isset($data[$v->org_id]['log']['fanli_total'])) {
                        $data[$v->org_id]['log']['fanli_total'] = $v->money + $data[$v->org_id]['log']['fanli_total'];
                    } else {
                        $data[$v->org_id]['log']['fanli_total'] = $v->money;
                    }
                }
            }
        }

        if ($accountRecordCharge) {
            foreach ($accountRecordCharge as $v) {
                if (abs($v->money) > 0) {
                    if ($v->charge_type == 1) {
                        $data[$v->org_id]['log']['charge_total'] = $v->money;
                    } elseif ($v->charge_type == 2) {
                        if (isset($data[$v->org_id]['log']['fanli_total'])) {
                            $data[$v->org_id]['log']['fanli_total'] = $v->money + $data[$v->org_id]['log']['fanli_total'];
                        } else {
                            $data[$v->org_id]['log']['fanli_total'] = $v->money;
                        }
                    }
                }
            }
        }

        $result = $this->preExportData($data, $orgArr);
        $exportData = [
            'fileName'  => 'account_checking' . date("YmdHis"),
            'filePath'  => APP_WWW_ROOT . DIRECTORY_SEPARATOR . 'download',
            'sheetName' => 'account_checking',
            'fileExt'   => 'xls',
            'download'  => 1,
            'title'     => [
                'org_name'           => '机构名称',
                'orgcode'            => '机构编码',
                'org_id'             => '机构ID',
                'type'               => '类型',
                'money'              => '余额',
                'charge_total'       => '累计充值',
                'assign_total'       => '累计分配',
                'fanli_total'        => '累计返利',
                'total_transfer_in'  => '累计转入',
                'total_transfer_out' => '累计转出',
                'diff_money'         => '差值',
                'charge_diff'        => '累充对比',
                'assign_diff'        => '累分对比',
                'fanli_diff'         => '累返对比',
                'transfer_in_diff'   => '累转入对比',
                'transfer_out_diff'  => '累转出对比',
            ],
            'data'      => $result,
        ];

        $exportFilePath = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['diff_money'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });

        $common = new commonModel();
        $url = $common->fileUploadToOss($exportFilePath);

        Response::jsonToG7s(['downloadUrl' => $url]);
    }

    private function preExportData($data, $orgArr)
    {
        $result = [];
        if ($data) {
            foreach ($data as $k => $v) {
                $_account = [];
                if (isset($v['account'])) {
                    $_account['org_name'] = $v['account']['org_name'];
                    $_account['org_id'] = $v['account']['org_id'];
                    $_account['orgcode'] = $v['account']['orgcode'];
                    $_account['type'] = '账户';
                    $_account['money'] = isset($v['account']['money']) ? $v['account']['money'] : '';
                    $_account['charge_total'] = isset($v['account']['charge_total']) ? $v['account']['charge_total'] : '';
                    $_account['assign_total'] = isset($v['account']['assign_total']) ? $v['account']['assign_total'] : '';
                    $_account['fanli_total'] = isset($v['account']['fanli_total']) ? $v['account']['fanli_total'] : '';
                    $_account['total_transfer_in'] = isset($v['account']['total_transfer_in']) ? $v['account']['total_transfer_in'] : '';
                    $_account['total_transfer_out'] = isset($v['account']['total_transfer_out']) ? $v['account']['total_transfer_out'] : '';
                    $_account['diff_money'] = $_account['charge_total'] + $_account['total_transfer_in'] + $_account['fanli_total'] - $_account['assign_total'] - $_account['total_transfer_out'] - $_account['money'];
                    $_account['diff_money'] = strval(round($_account['diff_money'], 2));
                }

                $_accountLog = [];
                if (isset($v['log'])) {
                    $_accountLog['org_name'] = $v['account']['org_name'];
                    $_accountLog['org_id'] = $v['account']['org_id'];
                    $_accountLog['orgcode'] = $v['account']['orgcode'];
                    $_accountLog['type'] = '流水';
                    $_accountLog['money'] = '0';
                    $_accountLog['charge_total'] = isset($v['log']['charge_total']) ? $v['log']['charge_total'] : '0';
                    $_accountLog['assign_total'] = isset($v['log']['assign_total']) ? $v['log']['assign_total'] : '0';
                    $_accountLog['assign_total'] = $_accountLog['assign_total'] > 0 ? "-" . $_accountLog['assign_total'] : abs($_accountLog['assign_total']);
                    $_accountLog['fanli_total'] = isset($v['log']['fanli_total']) ? $v['log']['fanli_total'] : '';
                    $_accountLog['total_transfer_in'] = isset($v['log']['total_transfer_in']) ? $v['log']['total_transfer_in'] : '0';
                    $_accountLog['total_transfer_out'] = isset($v['log']['total_transfer_out']) ? $v['log']['total_transfer_out'] : '0';
                    $_accountLog['total_transfer_out'] = $_accountLog['total_transfer_out'] > 0 ? "-" . $_accountLog['total_transfer_out'] : abs($_accountLog['total_transfer_out']);
                    $_accountLog['diff_money'] = '';
                }

                $_accountOrder = [];
                if (isset($v['order'])) {
                    $_accountOrder['org_name'] = $v['account']['org_name'];
                    $_accountOrder['org_id'] = $v['account']['org_id'];
                    $_accountOrder['orgcode'] = $v['account']['orgcode'];
                    $_accountOrder['type'] = '工单';
                    $_accountOrder['money'] = '';
                    $_accountOrder['charge_total'] = isset($v['order']['charge_total']) ? $v['order']['charge_total'] : '0';
                    $_accountOrder['assign_total'] = isset($v['order']['assign_total']) ? $v['order']['assign_total'] : '0';
                    $_accountOrder['fanli_total'] = isset($v['order']['fanli_total']) ? $v['order']['fanli_total'] : '0';
                    $_accountOrder['total_transfer_in'] = isset($v['order']['total_transfer_in']) ? $v['order']['total_transfer_in'] : '0';
                    $_accountOrder['total_transfer_out'] = isset($v['order']['total_transfer_out']) ? $v['order']['total_transfer_out'] : '0';
                    $_accountOrder['diff_money'] = '';
                   
                }

                if ($_account && $_accountLog) {
                    $_account['charge_diff'] = isset($_account['charge_total']) && isset($_accountLog['charge_total']) ? $_account['charge_total'] - $_accountLog['charge_total'] : '--';
                    $_account['assign_diff'] = isset($_account['assign_total']) && isset($_accountLog['assign_total']) ? $_account['assign_total'] - $_accountLog['assign_total'] : '--';
                    $_account['fanli_diff'] = isset($_account['fanli_total']) && isset($_accountLog['fanli_total']) ? $_account['fanli_total'] - $_accountLog['fanli_total'] : '--';
                    $_account['transfer_in'] = isset($_account['total_transfer_in']) && isset($_accountLog['total_transfer_in']) ? $_account['total_transfer_in'] - $_accountLog['total_transfer_in'] : '--';
                    $_account['transfer_out'] = isset($_account['total_transfer_out']) && isset($_accountLog['total_transfer_out']) ? $_account['total_transfer_out'] - $_accountLog['total_transfer_out'] : '--';
                }
                if ($_account && $_accountOrder) {
                    $_accountOrder['charge_diff'] = isset($_account['charge_total']) && isset($_accountOrder['charge_total']) ? $_account['charge_total'] - $_accountOrder['charge_total'] : '--';
                    $_accountOrder['assign_diff'] = isset($_account['assign_total']) && isset($_accountOrder['assign_total']) ? $_account['assign_total'] - $_accountOrder['assign_total'] : '--';
                    $_accountOrder['fanli_diff'] = isset($_account['fanli_total']) && isset($_accountOrder['fanli_total']) ? $_account['fanli_total'] - $_accountOrder['fanli_total'] : '--';
                    $_accountOrder['transfer_in_diff'] = isset($_account['total_transfer_in']) && isset($_accountOrder['total_transfer_in']) ? $_account['total_transfer_in'] - $_accountOrder['total_transfer_in'] : '--';
                    $_accountOrder['transfer_out_diff'] = isset($_account['total_transfer_out']) && isset($_accountOrder['total_transfer_out']) ? $_account['total_transfer_out'] - $_accountOrder['total_transfer_out'] : '--';
                }
                if ($_accountLog && $_accountOrder) {
                    $_accountLog['charge_diff'] = isset($_accountLog['charge_total']) && isset($_accountOrder['charge_total']) ? $_accountLog['charge_total'] - $_accountOrder['charge_total'] : '--';
                    $_accountLog['assign_diff'] = isset($_accountLog['assign_total']) && isset($_accountOrder['assign_total']) ? $_accountLog['assign_total'] - $_accountOrder['assign_total'] : '--';
                    $_accountLog['fanli_diff'] = isset($_accountLog['fanli_total']) && isset($_accountOrder['fanli_total']) ? $_accountLog['fanli_total'] - $_accountOrder['fanli_total'] : '--';
                    $_accountLog['transfer_in_diff'] = isset($_accountLog['total_transfer_in']) && isset($_accountOrder['total_transfer_in']) ? $_accountLog['total_transfer_in'] - $_accountOrder['total_transfer_in'] : '--';
                    $_accountLog['transfer_out_diff'] = isset($_accountLog['transfer_out']) && isset($_accountOrder['transfer_out']) ? $_accountLog['transfer_out'] - $_accountOrder['transfer_out'] : '--';
                }

                if($_account){
                    $result[] = $_account;
                }
                if($_accountLog){
                    $result[] = $_accountLog;
                }
                if($_accountOrder){
                     $result[] = $_accountOrder;
                }
            }
        }

        return $result;
    }

    public function init()
    {
        $data = \Fuel\Service\AccountToGos::init();

        Response::json($data);
    }

    /**
     * @title 初始化账户流水至Gos系统
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function initDataToGos()
    {
        \Fuel\Service\AccountRecordsToGos::initDataToGos();
    }

    /**
     * @title 获取机构的现金授信账户的可用金额
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     */
    public function getAccountUseMoney()
    {
        $params = helper::filterParams();

        //验证
        helper::argumentCheck(['orgcode'], $params);

        //校验机构号是否有效
        $orgInfo = \Models\OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('机构号无效', 2);
        } else {
            $params['org_id'] = $orgInfo->id;
        }
        //现金账户对用的云映像信息
        //$cashAccountInfo = OilOrg::getOrgOperator($orgInfo->id);
        $cashAccountName = 'G7油品账户';//$cashAccountInfo->operators_name;

        //授信账户对应的授信商信息
        $creditAccountInfo = \Models\OilCreditAccount::getByOrgId($params);

        $creditNoToName = [];
        if($creditAccountInfo)
        {
            foreach ($creditAccountInfo as $v)
            {
                $creditNoToName[$v->account_no] = $v->CreditProvider->name;
            }
        }

        $data = OilAccountMoney::getAccountUseMoney($params);

        if($data){
            foreach ($data as &$v){
                if($v->account_type == 10){
                    $v->account_name = $cashAccountName;
                    $v->subAccountType = \Fuel\Defines\AccountType::ACCOUNT_CENTER_CASH;
                    //txb 2018.7.9 重新修改 现金账户余额
                    $result = OilAccountMoney::myAccountInfo(['org_id'=> $orgInfo->id ]);

                    $v->balance = $result->money;
                    $frozenmoney = Fuel\Service\FrozenMoney::getUseMoney( $params['orgcode'] );
                    if ($frozenmoney && $frozenmoney > 0) {
                        $v->use_balance = $result->money - $frozenmoney;
                    }else{
                        $v->use_balance = $result->money;
                    }

                }elseif ($v->account_type == 20){

                    //\Framework\Log::error('5555',[],'getAccountUseMoney');
                    $credit_provider = \Models\OilCreditProvider::where('id',$v->credit_provider_id)->first();
                    //使用信用方的授信可用额度
                    if($credit_provider->name == '邦加油'){
                        $zbank_info = \Models\OilCreditAccount::checkZBank(['orgcode'=>$params['orgcode']]);

                        $creditInfo = $zbank_info['creditInfo'];

                        $v->subAccountType = $creditInfo->subAccountType;

                        if($zbank_info['code'] != 1){
                            $v->use_balance = 0;
                            $v->balance = 0;
                            $v->_status = '异常';
                        }else{
                            $v->use_balance = $creditInfo->restCreditAmount/100;
                            $v->balance = $creditInfo->restCreditAmount/100;
                            $v->_status = $zbank_info['status'];
                        }

                    }elseif($credit_provider->name == '动力宝'){
                        $creditInfo = (new \Fuel\Service\AccountCenter\AccountService())->getCreditBalanceByOrgCode(['orgCode'=>$params['orgcode']]);
                        \Framework\Log::error('666',[],'getAccountUseMoney');
                        $v->subAccountType = null;
                        if($creditInfo){
                            $v->use_balance = $creditInfo->restCreditAmount/100;
                            $v->balance = $creditInfo->restCreditAmount/100;
                            $v->subAccountType = $creditInfo->subAccountType;
                            if($creditInfo->creditGlpInfo && $creditInfo->creditGlpInfo->creditGlpStatus != 'NORMAL'){
                                if($creditInfo->creditGlpInfo->creditGlpStatus == 'OVERDUE'){
                                    $v->_status = '逾期';
                                }elseif($creditInfo->creditGlpInfo->creditGlpStatus == 'FROZEN'){
                                    $v->_status = '冻结';
                                }
                            }else{
                                $v->_status = '正常';
                            }
                        }else{
                            $v->_status = '无此账户';
                        }
                    }
                    $v->account_name = isset($creditNoToName[$v->account_no]) ? $creditNoToName[$v->account_no] : '';
                    \Framework\Log::error('777',[],'getAccountUseMoney');
                }
            }
        }

        Response::json($data);
    }


    public function getCashAccountNoList()
    {
        $params = helper::filterParams();

        //验证
        helper::argumentCheck(['orgcode'], $params);

        $data = OilAccountMoney::getCashAccountNoList($params);

        Response::json($data);
    }

    //查询机构账户和卡账户
    public function getAccount()
    {
        //$creditInfo = (new \Fuel\Service\AccountCenter\AccountService())->getZBankAccountInfo(['orgCode'=>'********']);
        //print_r($creditInfo);exit;
        $info = \Models\OilCreditAccount::checkZBank(['orgcode'=>'********'],true);
        print_r($info['creditInfo']);
        $res = \Models\OilCardVice::checkCardStatus(['vice_no' => '****************', "org_id" => '********'], $info['info']->subAccountID);
        print_r($res['info']);
        exit;
    }

    /**
     * @title  为gas提供壹号卡查询余额
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  vice_no
     * @param orgcode
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function getOrgAccountBalance()
    {

        $params = helper::filterParams();
        helper::argumentCheck(['orgcode', 'vice_no'], $params);
        $params['flag'] = $params['flag'] == 2 ? 2 : 1;
        \Framework\Log::error('$params'.var_export($params,TRUE),[],'getOrgAccountBalance');
        $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('机构非法', 2);
        }

        $cardInfo = \Models\OilCardVice::getByViceNoForLock(array('vice_no' => $params['vice_no']));
        if (!$cardInfo) {
            throw new \RuntimeException('卡不存在', 2);
        }
        if($cardInfo->status != '使用'){
            throw new \RuntimeException('卡非使用状态', 2);
        }
        $cardOrgInfo = OilOrg::getById(['id'=>$cardInfo->org_id]);
        if (strtoupper(substr($cardOrgInfo->orgcode,0,6)) != strtoupper(substr($params['orgcode'],0,6))) {
            throw new \RuntimeException('卡的所属机构不正确', 2);
        }

        if ( $cardInfo->card_from != \Fuel\Defines\CardFrom::GAS_CARD ) {
            throw new \RuntimeException('非壹号卡', 2);
        }

        //todo flag 要区分是否是卡机
        if($params['flag'] == 2 && in_array($cardInfo->oil_com,[\Fuel\Defines\OilCom::GAS_FIRST_ZBANK_CHARGE,\Fuel\Defines\OilCom::GAS_FIRST_ZBANK_TALLY])){
            throw new \RuntimeException('此设备暂不支持邦加油卡', 2);
        }

        //油站限制校验
        if(isset($params['station_id']) && $params['station_id']){
            $data = (new \GosSDK\Gos())
                ->setMethod('v1/stationLimit/checkStationByCardNoAndStation')
                ->setParams([
                    'card_no' => $params['vice_no'],
                    'station_id' => $params['station_id'],
                ])
                ->sync();
            if(!$data->is_available){
                throw new \RuntimeException('此卡已被限定使用，请司机联系发卡公司',2);
            }
        }

        //todo 以后启用
        /*if($cardInfo->status != '使用'){
            throw new \RuntimeException('卡状态异常', 2);
        }

        if( bccomp($params['trade_num'],$cardInfo->oil_top) > 0 ){
            throw new \RuntimeException('已超次限', 2);
        }

        //卡余额
        if( bccomp($cardInfo->card_remain,0) < 0 && in_array($cardInfo->oil_com,[\Fuel\Defines\OilCom::GAS_FIRST_ZBANK_CHARGE,\Fuel\Defines\OilCom::GAS_FIRST_CHARGE]) ){
            throw new \RuntimeException('卡余额为0', 2);
        }

        //需要校验1号卡,限额
        $limit = \Models\OilCardVice::getCardOilLimit($cardInfo->vice_no);
        //todo 获取卡的限额
        if(bccomp(($limit['day_top']+$params['trade_num']),$cardInfo->day_top) > 0){
            throw new \RuntimeException('已超日限', 2);
        }

        if(bccomp(($limit['month_top']+$params['trade_num']),$cardInfo->month_top) > 0){
            throw new \RuntimeException('已超月限', 2);
        }*/

        //众邦卡数据验证
        if (in_array($cardInfo->oil_com, \Fuel\Defines\OilCom::getZBankFirstList())) {
            //验证机构账户及卡账户
            $info = \Models\OilCreditAccount::checkZBank(['org_id'=>$orgInfo->id],true);
            if($info['status'] != "正常"){
                throw new \RuntimeException('机构账户异常', 2);
            }
            //$use_balance = $info['creditInfo']->restCreditAmount/100;
            $balance = $info['creditInfo']->restCreditAmount/100;

            if($cardInfo->oil_com == \Fuel\Defines\OilCom::GAS_FIRST_ZBANK_CHARGE) {
                //验证卡账户
                $res = \Models\OilCardVice::checkCardStatus(['vice_no' => $cardInfo->vice_no, "org_id" => $orgInfo->id], $info['info']->subAccountID);
                if ($res['status'] != '正常') {
                    throw new \RuntimeException('卡账户异常', 2);
                }
                //$cardRemain = $cardInfo->card_remain;
                //$use_balance = $res['info']->restCreditAmount/100;
                $balance = $cardInfo->card_remain;
            }

            //验证企业单日限额

            $result = [
                "is_async" => 1,
                'balance' => $balance
            ];
            \Framework\Log::error("getBalance:".json_encode($result),[$params],"getBalance_");
            Response::jsonToG7s($result);

        } else {
            $accountMoney = (new \Fuel\Service\AccountMoney())->setOrg($orgInfo->id);

            $accountMoneyInfo = $accountMoney->getAccountInfo();

            $accountMoneyBalance = $accountMoney->getAccountBalance();

            //todo 上线时要修改为以前的
            $balance = $accountMoneyBalance;
            if($cardInfo->oil_com == \Fuel\Defines\OilCom::GAS_FIRST_CHARGE){
                $balance = $cardInfo->card_remain;
            }
            $result = [
                "is_async" => 2,
                'balance' => $balance,
                'use_balance' => $balance
            ];
            \Framework\Log::error("getBalance:".json_encode($result),[$params],"getBalance_");
            Response::jsonToG7s($result);
        }
    }

    /**
     * @title  对比每个顶级机构返利及使用
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function compareOrgFanli()
    {
        //todo 获取每个顶级机构及子级机构的累计充值和累计计算返利
        //todo 获取每个顶级机构及子级机构的账户的返利剩余及返利扣减剩余及消费记录标记的使用返利
        $orgList = OilOrg::getAllOilOrg(["is_root"=>1]);
        $htmlLine = "";
        $diffOrg = array();
        foreach ($orgList as $val){
            //获取累计计算返利
            $orgIdArr = explode(",",$val['org_ids']);
            $calculateFanli = \Models\OilCardViceTrades::getFanliByMonth(["org_id_list"=>$orgIdArr],1);
            //获取累计充值返利
            $chargeFanli = \Models\OilAccountMoneyCharge::sumTotalCharge(["org_id_list"=>$orgIdArr,"status"=>1,"charge_type"=>2]);
            $addFanli = $calculateFanli + $chargeFanli;

            //获取机构的账户的返利剩余
            $fanliRemain = OilAccountMoney::getFanliRemain(["org_id_list"=>$orgIdArr]);
            //获取机构的消费标记使用返利
            $use_fanli_fee = \Models\OilCardViceTrades::getFanliByMonth(["org_id_list"=>$orgIdArr],3);
            $reduceFanli = $fanliRemain + $use_fanli_fee;

            \Framework\Log::error("机构：".$val['orgroot']."|计算返利:".$calculateFanli.",充值返利:".$chargeFanli."|返利剩余：".$fanliRemain.",返利标记".$use_fanli_fee,[],"orgCompareFanli_");

            if(bccomp($addFanli,$reduceFanli) != 0){
                $diffOrg[] = $val['orgroot'];
                $htmlLine .= "<br/>机构:".$val['orgroot']."&nbsp;&nbsp;返利入账:".$addFanli."元,&nbsp;&nbsp;返利消耗:".$reduceFanli."元<hr/>";
            }
        }
        \Framework\Log::error("机构不一致机构：".json_encode($diffOrg),[],"orgCompareFanli_");

        $this->sendEmail($htmlLine,count($diffOrg));
    }

    public function sendEmail($content,$orgNum)
    {
        $params['title'] = date("Y-m-d", time()) . "机构返利情况";
        $newContent = "不一致机构数量:".$orgNum."<br/><hr/>";
        $params['content'] = $newContent.$content;
        helper::argumentCheck(['title', 'content'], $params);

        $alarmEmailList = array(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        );
        \Framework\Mailer\MailSender::sendNow($params['title'], $params['content'], $alarmEmailList, true, false, []);
    }
}
