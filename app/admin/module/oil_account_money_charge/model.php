<?php
/**
 * 充值申请
 * <AUTHOR>
 */
require_once APP_ROOT . DIRECTORY_SEPARATOR . 'Models' . DIRECTORY_SEPARATOR . 'OilCardMain.php';
use Models\OilAccountMoneyCharge as OilAccountMoneyCharge;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Service\AccountTrades as AccountTrades;

class oil_account_money_chargeModel extends model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 信息列表
     * @param string $where
     * @param string $fields
     * @param string $limit
     * @return stdClass
     */
    public function search($where = '', $fields = '', $limit = '')
    {
        $sql = " SELECT %s FROM oil_account_money_charge a 
					LEFT JOIN oil_org b ON a.org_id=b.id 
					LEFT JOIN gsp_sys_users c ON a.creator_id=c.id
					left join oil_operators d on a.operator_id=d.id
				";
        !$fields ? $fields = " a.*,b.org_name,c.true_name,d.name " : '';
        //取记录总数
        $count_sql = 'SELECT COUNT(1) count FROM oil_account_money_charge a
					LEFT JOIN oil_org b ON a.org_id=b.id
					LEFT JOIN gsp_sys_users c ON a.creator_id=c.id
					WHERE ' . $where;
        $stmt = $this->query($count_sql);
        $total = $stmt->fetchColumn();
        //取记录集
        if ($total > 0) {
            $datas = parent::findBySql($sql, $where, ' a.no DESC ', $limit, $fields);
        }
        $result = new stdClass();
        $result->total = $total;
        $result->data = isset($datas) ? $datas : '';

        return $result;
    }

    /**
     * @param $record
     * @param $tabel
     * @return int
     */
    public function add($record, $tabel)
    {
        $cId = parent::insert($record, $tabel);
        if ($cId) {
            return $cId;
        } else {
            return 0;
        }
    }

    /**
     * @param array $ids
     * @param array $setarr
     * @param string $tb
     * @return int
     */
//    public function update($ids = [], $setarr = [], $tb = '')
//    {
//        return parent::update($setarr, $ids, $tb);
//    }

    /**
     * @param array $ids
     * @param array $setarr
     * @param string $tb
     * @return int
     */
    public function edit($ids = [], $setarr = [], $tb = '')
    {
        return parent::update($setarr, $ids, $tb);
    }

    /**
     * @param $sid
     * @param string $tb
     * @return mixed
     */
//    public function delete($sid, $tb = "oil_account_money_charge")
//    {
//        return parent::delete($sid, NULL, $tb);
//    }

    /**
     * @param $sid
     * @param string $tb
     * @return mixed
     */
    public function destroy($sid, $tb = "oil_account_money_charge")
    {
        return parent::delete($sid, NULL, $tb);
    }

    //批量删除
    public function bdel($ids)
    {
        $sql = " delete from oil_account_money_charge where id in(0,$ids) ";

        return $this->exec($sql);
    }

    /**
     * 更新资金账户数据
     * @param unknown $org_id
     * @param unknown $money
     * @return Ambigous <string, PDOStatement>
     */
    public function updateAccountMoney($org_id, $money, $fanli = 0, $flag)
    {
        $time = helper::nowTime();
        if ($flag == 1) {
            $sql = "UPDATE oil_account_money SET money=money+$money,charge_total=charge_total+$money,
				last_charge_time='$time',updatetime='$time' WHERE org_id=$org_id";
        } else {
            $sql = "UPDATE oil_account_money SET money=money-$money-$fanli,charge_total=charge_total-$money,cash_fanli_remain=cash_fanli_remain-$fanli,fanli_total=fanli_total-$fanli,
				last_charge_time='$time',updatetime='$time' WHERE org_id=$org_id";
        }

        return $this->exec($sql);
    }

    //审核
    public function cardAudit($id)
    {
        $sql = 'UPDATE oil_account_money_charge SET status=1 WHERE id=' . $id;

        return $this->exec($sql);
    }

    //驳回
    public function cardReject($id)
    {
        $sql = 'UPDATE oil_account_money_charge SET status=-1 WHERE id=' . $id;

        return $this->exec($sql);
    }

    //销审
    public function cardUnAudit($params)
    {
        if (!isset($params['id'])) {
            throw  new Exception('id缺失', '6');
        }
        $sql = 'UPDATE oil_account_money_charge SET ';
        foreach ($params as $k => $v) {
            if ($k != 'id')
                $sql .= "$k = '$v',";
        }
        $sql = rtrim($sql, ',');
        $sql = $sql . ' WHERE id=' . $params['id'];

        return $this->exec($sql);
    }

    public function getInfoById($where, $field = '*', $tb = 'oil_account_money_charge')
    {
        $sql = "SELECT $field FROM $tb WHERE $where";
        $stmt = $this->query($sql);

        return $stmt->fetchAll();
    }

    /**
     * 生成单号
     * @param $head
     * 编号 + 年月日 + 当日序号最大值+1
     * @example CZ15122400001
     * @return string
     */
    public function createNo($head = '')
    {
        $no = "";
        if (!empty($head)) {
            $no = $head . date('ymd');
            $sql = "SELECT %s FROM oil_account_money_charge";
            $fields = " MAX(no) as no ";
            $where = " no LIKE '$no%' ";
            $result = parent::findBySql($sql, $where, NULL, NULL, $fields);

            if ($result[0]->no) {
                $no .= sprintf("%05d", (substr($result[0]->no, -5) + 1));
            } else {
                $no .= '00001';
            }
        }

        return $no;
    }

    /**
     * 生成机构账号
     * @return string
     */
    public function createAccountNo()
    {
        $sql = "SELECT %s FROM oil_account_money";
        $fields = " MAX(account_no) as account_no ";
        $where = " account_no LIKE '108%' ";
        $result = parent::findBySql($sql, $where, NULL, NULL, $fields);

        if ($result[0]->account_no) {
            $account_no = $result[0]->account_no + 1;
        } else {
            $account_no = '********';
        }

        return $account_no;
    }

    /**
     * 获取副卡信息
     * @param $main_id
     * @return mixed
     */
    public function getViceInfo($main_id)
    {
        $sql = "SELECT id FROM oil_card_vice WHERE card_main_id=" . $main_id;
        $stmt = $this->query($sql);

        return $stmt->fetchAll();
    }

    /**
     * 审核前校验
     * @param $info
     */
	private function checkInputBeforeAudit($info)
    {
        if ($info->status == 1) {
            throw new \RuntimeException('该单号已审核',2);
        }

        if($info->charge_type == 1){
            $params['org_id'] = $info->org_id;
            $params['operator_id'] = $info->operator_id;
            $params['pay_type'] = $info->pay_type;
            if(!$info->pay_channel)
                throw new \RuntimeException('请维护渠道信息',2);
            $params['pay_channel'] = $info->pay_channel;
            if(!$info->pay_company_id)
                throw new \RuntimeException('请维护付款公司信息',2);
            $params['pay_company_id'] = $info->pay_company_id;

            //校验付款信息和付款公司是否停用
            OilAccountMoneyCharge::checkInput($params,$info);
        }
    }

    /**
     * 审核
     * @param null $ids
     * @return bool|null
     * @throws Exception
     */
    public function doCardAudit($ids = NULL,$isTrans = true)
    {
        if (!$ids) {
            throw new \RuntimeException('id不能为null', 2);
        }

        $res = NULL;

        //开启事务
        if($isTrans) {
            Capsule::connection()->beginTransaction();
        }
        try {
            $chargeInfo = OilAccountMoneyCharge::getByIdLock(['id'=>$ids]);

            //校验
            \Fuel\Service\AccountCharge::auditByValidate($chargeInfo);

            //判断微信转账逻辑
            if ($chargeInfo->pay_status != 0 && $chargeInfo->data_from == 4 && $chargeInfo->status == 0) {
                \Fuel\Service\AccountCharge::getWeiXinStat($chargeInfo);
            }

            //实例Service
            $accountMoneyService = (new \Fuel\Service\AccountMoney())->setOrg($chargeInfo->org_id);

            $accountMoneyInfo = $accountMoneyService->getAccountInfo();

            $orignalMoney = $accountMoneyInfo->money;

            //当充值为负数导致余额小于0无法操作时，提示 资金账号余额不足，审核失败
//            if ($chargeInfo->money < 0 && -(int)$chargeInfo->money > $accountMoneyInfo->money) {
//                throw new \RuntimeException('资金账号余额不足，审核失败', -1);
//            }
            if($chargeInfo->charge_type == 1){
                if ( !in_array($this->app->myAdmin->id,[1,1001705,1001285,1001692,1001389]) ) {
                    if ($chargeInfo->arrival_money < 0 && ($accountMoneyService->getAccountBalance() - $accountMoneyInfo->cash_fanli_remain - abs($chargeInfo->arrival_money) < 0)) {
                        throw new \RuntimeException('资金账户余额不足，无法进行负充值。如需特殊支持，请联系如玉、春丽。', -1);
                    }
                }else{
                    if ($chargeInfo->arrival_money < 0 && ($accountMoneyService->getAccountBalance() - abs($chargeInfo->arrival_money) < 0)) {
                        throw new \RuntimeException('资金账户余额不足，无法进行负充值。', -1);
                    }
                }
            }

            //负充值进行验证
            if($chargeInfo->arrival_money < 0 && $chargeInfo->charge_type == 2){
                if(($accountMoneyService->getAccountBalance() + $chargeInfo->arrival_money) < 0){
                    throw new \RuntimeException('返利账户资金不足，无法进行负返利充值', 2);
                }
                if($accountMoneyService->getAccountBalance() + $chargeInfo->arrival_money < 0){
                    throw new \RuntimeException('返利账户资金不足，无法进行负返利充值', 2);
                }

//                $money_fee = $accountMoneyInfo->money - $accountMoneyInfo->cash_fanli_remain;
//                if (bccomp($accountMoneyInfo->cash_fanli_remain, abs($chargeInfo->arrival_money),2 ) < -1 || bccomp($money_fee,0,2) < 0) {
//                    throw new \RuntimeException('返利余额不足,审核失败', 2);
//                }
            }

            //累计充值金额超出数据库字段限制时，提示 "累计金额超限，请联系技术人员"
            if($chargeInfo->arrival_money + $accountMoneyInfo->charge_total > ***********.99)
            {
                throw new \RuntimeException('累计金额超限，请联系技术人员',2);
            }
            $creditId = 0;
            if($chargeInfo->charge_type == 4){ //检查峰松授信额度
                $orgCode = \Fuel\Defines\OrgStatus::getCreditOrgCode();
                $orgId = \Models\OilOrg::getInferior(["orgcode"=>$orgCode[0]]);
                if(count($orgId) > 0) {
                    $creditInfo = \Models\OilCreditAccount::getByOrgIdOne(["org_id"=>$orgId[0]]);
                    if($creditInfo){
                        //使用各个基地的授信额度，与顶级机构额度无关
                        $creditId = $creditInfo->id;
                        /*$use_total = $creditInfo->credit_total - $creditInfo->used_total;
                        if(bccomp($chargeInfo->arrival_money,$use_total,2) == 1){
                            throw new \RuntimeException('授信账号余额不足，审核失败',2);
                        }*/
                    }else{
                        //throw new \RuntimeException('授信账户不存在',2);
                    }
                }else{
                    //throw new \RuntimeException('授信机构不存在',2);
                }
            }

            //充值逻辑
            $chargeResult = \Fuel\Service\AccountCharge::chargeMoney($chargeInfo,$accountMoneyInfo,$accountMoneyService);

            $updateArr = [
                'status'           => 1,
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name,
                'updatetime'       => helper::nowTime(),
            ];
            if($chargeInfo->audit_time === NULL){
                $updateArr['audit_time'] = helper::nowTime();
            }
            $chargeInfo->update($updateArr);

            /*if ($chargeInfo->data_from == 3) {
                $this->addByChargePood($chargeInfo);
            }*/
            if($chargeInfo->charge_type == 4 && $creditId > 0){ // 扣除峰松授信金额
                $upArr['id'] = $creditId;
                $upArr['used_total'] = $creditInfo->used_total + $chargeInfo->arrival_money;
                \Models\OilCreditAccount::edit($upArr);

                $record['money_id'] = $creditId;
                $record['org_id'] = $chargeInfo->org_id;
                $record['trade_type'] = -1;
                $record['money'] = -$chargeInfo->arrival_money;
                $record['after_money'] = $creditInfo->credit_total - $upArr['used_total'];
                $record['no_type'] = 'CZ';
                $record['no'] = $chargeInfo->no;
                $record['remark'] = '审核;';
                $record['operator_id'] = $this->app->myAdmin->id;
                $record['operator_name'] = $this->app->myAdmin->true_name;

                \Models\OilCreditAccountRecords::add($record);
            }

            //更新机构充值时间 //todo 是否需要区分类型
            if($chargeInfo->charge_type != 2) {
                \Models\OilOrg::updateChargesTime($chargeInfo->org_id);
            }

            $res = true;
            //事务提交
            if($isTrans) {
                Capsule::connection()->commit();
            }
        } catch (Exception $e) {
            \Framework\Log::error("充值单审核异常:".$ids,[$e->getMessage(),$e->getCode()],"chargeAudit_");
            //事务回滚
            if($isTrans) {
                Capsule::connection()->rollBack();
            }
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        //////////////////////第三方系统///////////////////////////////
        try {
            //向BOA添加交易流水
            /*$chargeInfo->money = $chargeInfo->money+$chargeInfo->fanli_charge;
            $chargeInfo->trade_type = 1;
            $chargeInfo->cash_fanli_remain = $chargeInfo->fanli_charge;

            $result = AccountTrades::addBoaMoneyTradesRecords($chargeInfo);
            if(!$result){
                throw new \RuntimeException('添加交易流水失败', 2);
            }*/

            //发送微信通知
            \Models\OilOrgWx::sendNotify([
                'org_id'       => $chargeInfo->org_id,
                'cash_type'    => '充值',
                'money'        => formatMoney($chargeInfo->money + $chargeInfo->fanli_charge),
                'blance_money' => formatMoney($orignalMoney + $chargeInfo->money + $chargeInfo->fanli_charge),
                'remark'       => $chargeResult['remark']
            ]);
            //小于5000块微信提醒
            if (intval($orignalMoney + $chargeInfo->money + $chargeInfo->fanli_charge) < \Fuel\Defines\AccountMoneyTip::$AccountMoneyLimit) {
                \Models\OilOrgWx::sendMoneyLackNotify([
                    'org_id'       => $chargeInfo->org_id,
                    'blance_money' => formatMoney($orignalMoney + $chargeInfo->money + $chargeInfo->fanli_charge),
                ]);
            }
        } catch (Exception $e) {
            //\Framework\Log::dataLog('sendNotify--'.strval($e),'wxDebug');
        }

        return $res;
    }

    public function doCardAudit1($ids = NULL)
    {
        if (!$ids) {
            throw new \RuntimeException('id不能为null', 2);
        }

        //开启事务
        Capsule::connection()->beginTransaction();

        $info = OilAccountMoneyCharge::getByIdLock(['id'=>$ids]);

        //审核前校验
        $this->checkInputBeforeAudit($info);
        //判断微信转账逻辑
        if ($info->pay_status != 0 && $info->data_from == 4 && $info->status == 0) {
            //发起微信查询，查询到账是否成功
            $wxRes = \Fuel\Service\weChatPay::orderDetail([
                'out_trade_no' => $info['no']
            ]);

            \Framework\Log::debug('wxQuery:' , (array)$wxRes, 'wxError');

            if ($wxRes['return_code'] != 'SUCCESS' && $wxRes['result_code'] != 'SUCCESS') {

                $msg = '尚未到账,不能通过审核';
                if ($wxRes['return_code'] != 'SUCCESS') {
                    $msg = $wxRes['return_msg'];
                } elseif ($wxRes['result_code'] != 'SUCCESS' && isset($wxRes['err_code_des'])) {
                    $msg = $wxRes['err_code_des'];
                }

                \Framework\Log::error('wxError:' , (array)$wxRes, 'wxError');
                throw new \RuntimeException($msg, 2);
            }

            if ($wxRes['trade_state'] != 'SUCCESS') {
                throw new \RuntimeException('尚未到账,不能通过审核', 2);
            } else {
                OilAccountMoneyCharge::edit(
                    [
                        'id'         => $ids,
                        'pay_status' => 0
                    ]
                );
            }
        }

        $accountMoneyInfo = \Models\OilAccountMoney::getByOrgId(['org_id' => $info->org_id]);

        //当充值为负数导致余额小于0无法操作时，提示 资金账号余额不足，审核失败
        if ($info->money < 0 && -(int)$info->money > $accountMoneyInfo->money) {
            throw new \RuntimeException('资金账号余额不足，审核失败', -1);
        }
        //累计充值金额超出数据库字段限制时，提示 "累计金额超限，请联系技术人员"
        if($info->arrival_money + $accountMoneyInfo->charge_total > ***********.99)
        {
            throw new \RuntimeException('累计金额超限，请联系技术人员',2);
        }

        $res = NULL;
        try {
            /********************* 处理资金账户数据 **********************/
            //如果对应机构没有资金账户数据，则为该机构建立资金账户
            if (!$accountMoneyInfo || !isset($accountMoneyInfo->org_id)) {
                $accountMoneyInfo = \Models\OilAccountMoney::add(
                    [
                        'account_no'       => \Models\OilAccountMoney::createAccountNo(),
                        'org_id'           => $info->org_id,
                        'charge_total'     => 0,
                        'money'            => 0,
                        'other_creator_id' => $info->other_creator_id,
                        'other_creator'    => $info->other_creator,
                        'createtime'       => helper::nowTime()
                    ]
                );
            }

            $orgInfo = \Models\OilOrg::getById(['id'=>$info->org_id]);
            //判断充值方式
            if($info->charge_type == 1){//常规
                $info->fanli_charge = 0.00;
            }else if($info->charge_type == 2){//返利
                $info->fanli_charge = $info->arrival_money;
                $info->arrival_money = 0.00;
            }else{//未知
                $info->fanli_charge = 0.00;
                $info->arrival_money = 0.00;
            }
            //以机构为主键，更新资金账户的现金余额、累计充值字段、最后充值时间字段
            \Models\OilAccountMoney::updateMoneyForChargeById(
                [
                    'id'    =>  $accountMoneyInfo->id,
                    'money'    =>  $info->arrival_money,
                    'fanli_charge' => $info->fanli_charge
                ]
            );

            //校验账户资金账号平账
            \Fuel\Service\AccountVerification::checkAccountEqual($info->org_id,$info['no']);

            //在流水记录中记录本次充值后的账户资金余额
            $total = $info->arrival_money+$info->fanli_charge;

            $remark = $info->remark ? $info->remark.',' : '';
            $remark .= $orgInfo->org_name.'现金充值'.$info->arrival_money.'元,'.'返利充值'.$info->fanli_charge.'元,合计充值:'.$total.'元';

            $addArr = [
                'org_id'      => $info->org_id,
                'money_id'    => $accountMoneyInfo->id,
                'money'       => $info->arrival_money + $info->fanli_charge,
                'after_money' => $accountMoneyInfo->money + $info->arrival_money + $info->fanli_charge,
                'no'          => $info->no,
                'no_type'     => 'CZ',
                'trade_type'  => 1,
                'remark'      => $remark,
                'remark_work' => $info->remark_work,
                'createtime'  => helper::nowTime()
            ];
            \Models\OilAccountMoneyRecords::add($addArr);
            //向BOA添加交易流水
            $info->money = $info->money+$info->fanli_charge;
            $info->trade_type = 1;
            $info->cash_fanli_remain = $info->fanli_charge;
            $result = AccountTrades::addBoaMoneyTradesRecords($info);
            if(!$result){
                throw new \RuntimeException('添加交易流水失败', 2);
            }

            $updateArr = [
                'id'               => $ids,
                'status'           => 1,
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name,
                'updatetime'       => helper::nowTime(),
            ];
            if($info->audit_time === NULL){
                $updateArr['audit_time'] = helper::nowTime();
            }
            OilAccountMoneyCharge::edit($updateArr);
            //判断data_from为3（手机加油）的时候直接把oil_assign_pond分配池的真实分配
            //todo 如果有使用返利的这个时候的关联操作
            //todo 如果有使用抵用券待做
            if ($info->data_from == 3) {
                $this->addByChargePood($info);
            }

            //发送微信通知
            try {
                \Models\OilOrgWx::sendNotify([
                    'org_id'       => $info->org_id,
                    'cash_type'    => '充值',
                    'money'        => formatMoney($info->money + $info->fanli_charge),
                    'blance_money' => formatMoney($accountMoneyInfo->money + $info->money + $info->fanli_charge),
                    'remark'       => $addArr['remark']
                ]);
                //小于5000块微信提醒
                if (intval($accountMoneyInfo->money + $info->money + $info->fanli_charge) < \Fuel\Defines\AccountMoneyTip::$AccountMoneyLimit) {
                    \Models\OilOrgWx::sendMoneyLackNotify([
                        'org_id'       => $info->org_id,
                        'blance_money' => formatMoney($accountMoneyInfo->money + $info->money + $info->fanli_charge),
                    ]);
                }
            } catch (Exception $e) {
//                \Framework\Log::dataLog('sendNotify--'.strval($e),'wxDebug');
            }
            //事务提交
            Capsule::connection()->commit();

            $res = true;
        } catch (Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $res;
    }

    /**
     * addByChargePood
     * @param $info
     * @return bool
     * <AUTHOR>
     * @since ${DATE}
     */
    private function addByChargePood($info)
    {
        $assignPond = \Models\OilAssignPond::getByChargeId(['charge_id' => $info->id]);
        if (!$assignPond) {
            throw new \RuntimeException('不存在该充值记录', 2);
        }

        //创建分配申请单
        $assignRes = \Models\OilAccountAssign::add(
            [
                'charge_id'      => $info->id,
                'no'             => $this->loadModel('common')->createOrderNo('FP'),
                'no_type'        => 'FP',
                'org_id'         => $info->org_id,
                'org_name'       => $info->from_orgname,
                'money_total'    => $info->arrival_money,
                'use_cash_fanli' => $info->fanli_money,
                'assign_num'     => count($assignPond),
                'data_from'      => $info->data_from,
                'creator_id'     => $info->creator_id,
                'apply_time'     => $info->app_time,
                'createtime'     => $info->createtime,
                'other_creator_id'   => $info->other_creator_id,
                'other_creator'   => $info->other_creator
            ]
        );

        //处理分配申请单明细
        if ($assignRes && $assignPond) {
            $this->addAssignDetail($assignPond, $assignRes);
        }

        return TRUE;
    }

    /**
     * 如果有充值缓冲池记录，则执行如下插入到分配明细
     * addAssignDetail
     * @param $assignPond
     * @param $assignRes
     * @return bool
     * <AUTHOR>
     * @since ${DATE}
     */
    private function addAssignDetail($assignPond, $assignRes)
    {
        foreach ($assignPond as $_v) {
            $cardInfo = \Models\OilCardVice::getById(['id' => $_v->vice_id]);
            \Models\OilAccountAssignDetails::add(
                [
                    'assign_id'        => $assignRes->id,
                    'org_id'           => $cardInfo->org_id,
                    'org_name'         => $cardInfo->org_name,
                    'vice_id'          => $cardInfo->id,
                    'truck_no'         => $cardInfo->truck_no,
                    'assign_money'     => $_v->money,
                    'assign_jifen'     => 0,
                    'creator_id'       => 1,
                    'createtime'       => $assignRes->createtime,
                    'other_creator_id' => 1, //todo 待定之前门户段要求这个必传手机端是否必须传
                    'other_creator'    => 1, //todo 待定
                ]
            );
        }

        return TRUE;
    }

    /**
     * 回调link支付操作
     */
    public function asyncChargeUpdateStatus(array $params)
    {
        $res = NULL;
        \Framework\Log::dataLog('g7PayArgs-----------' . var_export($params, TRUE), 'g7Pay');

        //如果充值成功->账户真实充值
        if (isset($params['merchantSn']) && $params['merchantSn']) {
            $info = OilAccountMoneyCharge::where('merchantSn', '=', $params['merchantSn'])->first();
            \Framework\Log::debug('search by merchantSn' , $info->toArray(), 'g7Pay');

            if (!$info) {
                \Framework\Log::debug('error--is--not exist', $params, 'g7Pay');
                throw new \RuntimeException('Error:merchantSn does not exist', 2);
            }

            if ($params['amount'] / 100 != $info->real_money) {
                \Framework\Log::debug('error--mnoey--not--eq---', $params, 'g7Pay');
                throw new \RuntimeException('Error:The amount of error is not equal to the true amount', 2);
            }

            if ($params['status'] == 0 && $info->stauts == 0) {
                //成功逻辑 模拟手动点击审核逻辑
                $this->cardUnAudit($info->id);
            }

            $res = $info->update(['pay_status' => $params['status']]);
        } else {
            \Framework\Log::error('error--merchantSn--is--empty', $params, 'g7Pay');
            throw new \RuntimeException('Error:merchantSn not allowed to empty', 2);
        }

        if ($res) {
            try {
                //通知 todo
                Framework\TruckAppNotify\Notify::send([
                    'uid'         => $info->other_creator_id,
                    'title'       => '充值成功',
                    'description' => '您油卡成功充值' . $info->real_money . '元，圈存后即可使用'
                ]);
            } catch (\Exception $e) {
                \Framework\Log::error('error--TruckAppNotify - '.$e->getMessage(), ['exception'=>strval($e)], 'g7Pay');
            }

        }

        \Framework\Log::debug('return update', (array)$res, 'g7Pay');

        return $res;
    }

    /**
     * updatePayStatusByMerchantSn
     * @param array $params
     * <AUTHOR>
     * @since ${DATE}
     */
    public function updatePayStatusByMerchantSn(array $params)
    {
        $info = OilAccountMoneyCharge::where('merchantSn', '=', $params['merchantSn'])->first();

        if (!$info) {
            \Framework\Log::dataLog('error--is--not exist' . var_export($params['merchantSn'], TRUE), 'g7Pay');
            throw new \RuntimeException('Error:merchantSn does not exist', 2);
        }

        $data = \Fuel\Service\Pay::queryPayStatus($params);

        \Framework\Log::debug('res--------', (array)$data, 'g7Pay');

        if ($data->status == 0 && $info->stauts == 0) {
            //成功逻辑 模拟手动点击审核逻辑
            $this->doCardAudit($info->id);
            //通知 todo
            Framework\TruckAppNotify\Notify::send([
                'uid'         => $info->other_creator_id,
                'title'       => '充值成功',
                'description' => '您油卡成功充值' . $info->real_money . '元，圈存后即可使用'
            ]);
        }

        return OilAccountMoneyCharge::where('merchantSn', '=', $params['merchantSn'])->update(['pay_status' => $data->status]);
    }

    /**
     * 运营商查询
     * @param  name
     * @return bool
     * <AUTHOR>
     * @since 2016-08-23
     */
    public function getOperators()
    {
        $sql = "SELECT id, concat(code, ' ', name) name, code, name name,company_name FROM oil_operators";

        $rangeWhere = dataRangeSqlStr('oil_operators');
        $rangeWhere && $sql .= " where $rangeWhere";
        $data = parent::queryBySql($sql);

        return $data;
    }
}
