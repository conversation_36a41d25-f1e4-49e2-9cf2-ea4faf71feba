<?php
/**
 * 欠票统计数据供应商视角数据快照表 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/06/28
 * Time: 10:56:45
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilOwingSupplierSnapshot;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_owing_supplier_snapshot extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilOwingSupplierSnapshot::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $redirect_url = \Fuel\Service\ExportJobSrv::syncExportTask($params,"exportList",$data);
            if( !empty($redirect_url) ) {
                echo "<script>window.location.href = '/" . strtolower(__CLASS__) . "';window.open('" . $redirect_url . "')</script>";
            }
            //$this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    public function exportList($data)
    {
        $exportData = [
            'fileName' => '供应商视角欠票快照表_' . date("YmdHis"),
            'sheetName' => '供应商视角欠票快照表',
            'download'  => 1,
            'title' => [
                'snapshot_data'   =>  '数据计算日期',
                'supplier_id'   =>  '供应商ID',
                'supplier_name'   =>  '供应商名称',
                'cooperation_type_value'   =>  '合作类型',
                'operator_name'   =>  '签约运营商',
                'receipt_statement_value'   =>  '回票统计方式',
                'receipt_claim_value'   =>  '回票认领',
                'oil_name'   =>  '油品名称',
                'owing_ticket_total'   =>  'a.欠票金额',
                'owing_ticket_const_total'   =>  'b.固定欠票金额',
                'owing_ticket_dynamic_total'   =>  'c.浮动欠票金额',
                'owing_ticket_num_total'   =>  'd.欠票总数量',
                'owing_ticket_price'   =>  'e.欠票单价折算',
                'oil_unit'   =>  '单位',
                'oil_consumption'   =>  'f.油品可用消费',
                'supplier_recharge'   =>  'g.供应商可用总充值',
                'supplier_receipt_return_total'   =>  'h.供应商累计回票',
                'supplier_total_rebate'   =>  'i.供应商累计返利',
                'oil_consumption_total'   =>  'j.油品累计消费',
                'oil_consumption_flowing_water'   =>  'k.油品累计消费总额',
                'oil_arrears_flowing_water'   =>  'l.累计欠票调整金额',
                'oil_adjustment_flowing_water'   =>  'm.油品累计消费流水差异调账',
                'receipt_money'   =>  'n.油品回票金额',
                'receipt_discount'   =>  'o.油品回票累计折扣',
                'receipt_num'   =>  'p.回票数量',
                'owing_day'   =>  '累计欠票天数',
                'first_owing_trade'   =>  '首笔欠票交易时间',
                'last_receipt_time'   =>  '最后回票开票时间',
                'last_return_time'   =>  '最后回票时间',
                'settlement_docker'   =>  '结算对接人',
                'network_docker'   =>  '网络负责人',
                'remark'   =>  '备注',
                'createtime'   =>  '创建时间',
                'updatetime'   =>  '更新时间'
            ],
            'data' => $data,
        ];

        $url = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
        return $url;
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilOwingSupplierSnapshot::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilOwingSupplierSnapshot::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilOwingSupplierSnapshot::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilOwingSupplierSnapshot::remove($params);

        Response::json($data);
    }

    /**
     * 生成供应商角度欠票
     */
    public function createSnapshot()
    {
        $params = helper::filterParams();
        if(!isset($params['end_time']) || empty($params['end_time'])){
            $params['end_time'] = date("Y-m-d H:i:s",time());
        }
        //$params['supplierId'] = "10667";
        $data = (new \Fuel\Service\OwingTicket\SupplierCompanyStatisticsService())->createSupplierSnapshot($params);
        Response::json($data);
    }
}