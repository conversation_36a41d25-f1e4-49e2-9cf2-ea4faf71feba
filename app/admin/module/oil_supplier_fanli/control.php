<?php
/**
 * 上游返利记录 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/06/18
 * Time: 20:47:55
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilSupplierFanli;
use Models\OilEntityChangeRecord;
use Models\OilStationSupplier;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

use Fuel\Defines\RebateFormula;

class oil_supplier_fanli extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();
        $params['is_del'] = 2;

        $data = \Fuel\Service\SupplierFanli::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $redirect_url = \Fuel\Service\ExportJobSrv::syncExportTask($params,"exportList",$data);
            echo "<script>window.location.href = '/".strtolower(__CLASS__)."';window.open('".$redirect_url."')</script>";
//            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     * @throws
     */
    public function exportList($data)
    {
        $exportData = [
            'fileName' => '上游返利记录_' . date("YmdHis"),
            'sheetName' => '上游返利记录',
            'download'  => 1, //增加
            'title' => [
                'id'                =>  '序号',
                'no'                =>  '上游返利单号',
                'status_txt'        =>  '审核状态',
                'supplier_id'       =>  '油站供应商ID',
                'supplier_name'     =>  '油站供应商名称',
                'cooperation_type_txt'     =>  '油站供应商合作类型',
                'settle_obj_txt'    =>  '核算主体',
                'area_code'         =>  '服务区/主卡编码',
                'area_name'         =>  '服务区/主卡名称',
                'rebate_form_txt'   =>  '返利类型',
                'fanli_fee'         =>  '返利金额',
                'service_fee'       =>  '服务费金额',
                'trade_starttime_txt'   =>  '消费开始时间',
                'trade_endtime_txt'     =>  '消费截止时间',
                'real_arrival_time'     =>  '上游实际到账时间',
                'fanli_type_txt'        =>  '返利形式',
                'arrive_type_txt'       =>  '返利到账类型',
                'operator_id'           =>  '签约主体ID',
                'operator_name'         =>  '签约主体',
                'collect_company_id'    =>  '收款公司ID',
                'collect_company_name'  =>  '收款公司',
                'createtime'            =>  '创建时间',
                'creator'               =>  '创建人',
                'updatetime'            =>  '最后修改时间',
                'last_operator'         =>  '最后修改人',
                'audit_time'            =>  '审核时间',
                'audit_operator'        =>  '审核人',
                'remark'                =>  '录入备注',
                'audit_remark'          =>  '审核备注'
            ],
            'data' => $data->toArray(),
        ];

        $url = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
        return $url;
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilSupplierFanli::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilSupplierFanli::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilSupplierFanli::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilSupplierFanli::remove($params);

        Response::json($data);
    }

    public function createOrUpdate()
    {
        $params = helper::filterParams();
        helper::argumentCheck(
            [
                'supplier_id',
                'fanli_type',
                'rebate_form',
                "arrive_type",
                "fanli_fee",
                "real_arrival_time",
                "operator_id",
                "collect_company_id"
            ],
            $params);

        if ($params['rebate_form'] == RebateFormula::CA_OBJ_TRADE) {
            helper::argumentCheck(["trade_starttime","trade_endtime"], $params);
        }

        if ($params['fanli_type'] != 20)
        {
            helper::argumentCheck(["service_fee"], $params);
        }

        $data = \Fuel\Service\SupplierFanli::inertSupplierFanli($params);
        Response::json($data,0,"成功");
    }

    public function batchUpdateData()
    {
        $params = helper::filterParams();
        helper::argumentCheck(["ids","flag"], $params);
        $data = \Fuel\Service\SupplierFanli::batchUpdate($params);
        Response::json($data);
    }

    public function countTotalData()
    {
        $params               = helper::filterParams();
        $params['_countdata'] = 1;
        $params['is_del'] = 2;
        $tmp                  = OilSupplierFanli::getList($params);
        $data                 = $tmp[0];
        $result               = [
            "total_count" => $data->total_count,
            "total_fanli" => number_format($data->totalFanli, 2, ".", ""),
            "total_service" => number_format($data->totalService, 2, ".", ""),
        ];
        echo json_encode($result);
        exit;
    }

    public function batchImport()
    {
        $params = helper::filterParams();

        if (isset($params['filePath']) && $params['filePath']) {
            $upload = $params['filePath'];
        } else {
            //上传文件
            $upload = $this->file_upload($_FILES['userfile']);
        }

        $fieldMap = [
            'supplier_id'       => '油站供应商ID',
            'supplier_name'     => '油站供应商名称',
            'settle'            => '核算主体',
            'area_code'         => '服务区编码',
            'area_name'         => '服务区名称',
            'rebate_form'       => '返利类型',
            'fanli_fee'         => '返利金额',
            'service_fee'       => '服务费金额',
            'trade_start'       => '消费开始时间',
            'trade_end'         => '消费结束时间',
            'recharge_no'       => '充值单号',
            'fanli_type'        => '返利形式',
            'arrive_type'       => '返利到账类型',
            'remark'            => '录入备注',
        ];

        $excelParams = [
            'filePath'  => $upload,
            'fieldsMap' => array_flip($fieldMap),
            'ignore'    => TRUE
        ];

        $result = \Framework\Excel\ExcelReader::read($excelParams);
        if(count($result[0]) == 0){
            throw new \RuntimeException("数据为空", 2);
        }
        $data = \Fuel\Service\SupplierFanli::batchImport($result);
        Response::json(null, 0, '导入成功');
    }

    public function file_upload($file)
    {
        set_time_limit(0);
        if (empty($file) || empty($file['name'])) {
            //未选择文件
            throw new \RuntimeException('未选择文件', 2);
        }
        //判断文件类型
        if ($file['name']) {
            $ext = strtolower(trim(substr(strrchr($file['name'], '.'), 1)));
            if ($ext != "xls" && $ext != "xlsx") {
                //文件类型不正确
                throw new \RuntimeException('文件类型不正确', 2);
            }
        }

        $dir = '../tmp/data';
        if (!is_dir($dir)) {
            helper::createDir($dir, 0777);
        }

        //文件上传
        $tmp_name = $file['tmp_name'];
        $newname  = $dir . '/import_' . date('m-d-H-i-s') . '.' . $ext;

        if (@copy($tmp_name, $newname)) {
            @unlink($tmp_name);
        } elseif (@move_uploaded_file($tmp_name, $newname)) {
        } elseif (@rename($tmp_name, $newname)) {
        } else {
            //上传文件失败
            return -7;
        }
        @chmod($newname, 0777);

        return $newname;
    }

    /**
     * 根据上游返利oil_supplier_fanli表的supplier_id加real_arrive_time结合oil_entity_change_record表中变更类型为运营商变更的记录
     * 匹配某个供应商在real_arrvie_time时的签约供应商是谁也就是res_from_id,把它更新到上游返利表
     * @return void
     */
    public function initOperatorBySupplierIdAndRealArriveTime()
    {
        $fanliRecords = OilSupplierFanli::whereNotNull('real_arrival_time')
                                        ->where('supplier_id', '>', 0)
                                        ->where('createtime', '>=', '2025-01-01 00:00:00')
                                        ->where('operator_id', 0)
                                        ->get();
        foreach ($fanliRecords as $record) {
            try {
                // 查询oil_entity_change_record表中与该供应商相关的运营商变更记录
                // 按照时间降序排序，找到在real_arrival_time之前最近的一条记录
                $changeRecord = \Models\OilEntityChangeRecord::where('supplier_id', $record->supplier_id)
                                                             ->where('classify', 30)
                                                             ->where('res_type', 60)
                                                             ->where('createtime', '<=', $record->real_arrival_time)
                                                             ->orderBy('createtime', 'desc')
                                                             ->first();
                if ($changeRecord) {
                    OilSupplierFanli::where('id', $record->id)
                                    ->update(['operator_id' => $changeRecord->res_to_id]);
                    continue;
                }
                $supplierInfo = OilStationSupplier::getById(['id'=>$record->supplier_id]);
                OilSupplierFanli::where('id', $record->id)
                                ->update(['operator_id' => $supplierInfo->operator_id]);
            } catch (\Exception $e) {
                \framework\Log::error(
                    "处理返利ID: {$record->id} 时出错: {$e->getMessage()}",
                    [
                        'supplier_id' => $record->supplier_id,
                        'real_arrival_time' => $record->real_arrival_time,
                    ],
                    "initOperatorBySupplierIdAndRealArriveTime"
                );
            }
        }
    }

    public function initCollectCompanyBySupplierIdAndRealArriveTime()
    {
        // 获取所有有supplier_id和real_arrival_time的上游返利记录
        $fanliRecords = \Models\OilSupplierFanli::whereNotNull('real_arrival_time')
                                        ->where('supplier_id', '>', 0)
                                        ->where('collect_company_id', 0)
                                        ->where('createtime', '>=', '2025-01-01 00:00:00')
                                        ->get();
        foreach ($fanliRecords as $record) {
            try {
                // 查询供应商在real_arrival_time时间点使用的收款公司
                // 查找在real_arrival_time时间点之前创建的最新收款公司关联记录
                $companyRelation = \Models\OilSupplierCompany::where('supplier_id', $record->supplier_id)
                                                    ->where('createtime', '<=', $record->real_arrival_time)
                                                    ->orderBy('createtime', 'desc')
                                                    ->first();

                if ($companyRelation) {
                    // 更新返利记录的collect_company_id字段
                    $record->collect_company_id = $companyRelation->collect_company_id;
                    $record->save();
                }
            } catch (\Exception $e) {
                \Framework\Log::error('初始化供应商收款公司失败: ' . $e->getMessage(), [
                    'supplier_id' => $record->supplier_id,
                    'fanli_id' => $record->id,
                    'real_arrival_time' => $record->real_arrival_time
                ], 'initCollectCompanyBySupplierIdAndRealArriveTime');
            }
        }
    }

    /**
     * 根据oil_supplier_fanli表的合作类型cooperation_type及核算方式settle_obj来识别用area_code去查询服务区、主卡、供应商表的id及名称写入到settle_object_id及settle_object_name中
     * @return void
     */
    public function initSettlementIdByCooperationTypeAndSettleObj()
    {
        // 查询需要更新的记录（settle_object_id为空或为0的记录）
        $fanliRecords = OilSupplierFanli::where('settle_object_id', 0)
                                        ->where('createtime', '>=', '2025-01-01 00:00:00')
                                        ->get();
        foreach ($fanliRecords as $record) {
            try {
                // 根据cooperation_type和settle_obj确定查询策略
                if ($record->cooperation_type == \Fuel\Defines\CooperationType::COOPERATION_TYPE_ZD or $record->cooperation_type == \Fuel\Defines\CooperationType::COOPERATION_TYPE_PT) { // 站点类型 (20)
                    if ($record->settle_obj == \Fuel\Defines\SupplierAccountConf::SETTLE_OBJ_AREA) { // 核算主体为服务区/主卡 (20)
                        $record->settle_object_id = $record->area_code;
                        $record->settle_object_name = $record->area_name;
                        $record->save();
                    }
                    if ($record->settle_obj == \Fuel\Defines\SupplierAccountConf::SETTLE_OBJ_SUPPLIER) { // 核算主体为供应商 (10)
                        $supplierInfo = \Models\OilStationSupplier::getById(['id' => $record->supplier_id]);
                        $record->settle_object_id = $supplierInfo->supplier_id;
                        $record->settle_object_name = $supplierInfo->supplier_name;
                        $record->save();
                    }
                }
                if ($record->cooperation_type == \Fuel\Defines\CooperationType::COOPERATION_TYPE_ZK) { // 主卡类型 (30)
                    // 查询主卡表
                    $cardInfo = \Models\OilCardMain::where('main_no', $record->area_code)->first();
                    $record->settle_object_id = $cardInfo->id;
                    $record->settle_object_name = $record->area_code;
                    $record->save();
                }
            } catch (\Exception $e) {
                \Framework\Log::error(
                    "处理返利ID: {$record->id} 时出错: {$e->getMessage()}",
                    [
                        'supplier_id'      => $record->supplier_id,
                        'cooperation_type' => $record->cooperation_type,
                        'settle_obj'       => $record->settle_obj,
                        'area_code'        => $record->area_code,
                    ],
                    "initSettlementIdByCooperationTypeAndSettleObj"
                );
            }
        }
    }

    public function initRealArriveTime()
    {
        $result = \Framework\Excel\ExcelReader::read([
            'filePath'  => "/data/web_data/web/收款认领单付款公司期初.xlsx",
            'fieldsMap' => array_flip([
                'no'                => '上游返利单号',
                'real_arrival_time' => '上游实际到账时间'
            ]),
            'ignore'    => true
        ]);
        foreach ($result as $row) {
            if (empty($row['no']) || empty($row['real_arrival_time'])) {
                throw new \RuntimeException("第{$row['no']}行数据不完整，上游返利单号和上游实际到账时间不能为空", 2);
            }
        }
        // 处理每一行数据
        foreach ($result as $row) {
            try {
                // 查找对应的返利记录
                $fanliRecord = OilSupplierFanli::where('no', $row['no'])
                                               ->first();
                if (!$fanliRecord) {
                    \Framework\Log::error(
                        "处理返利单号: {$row['no']} 时出错: 未找到对应的返利记录",
                        [
                            'no' => $row['no'],
                        ],
                        "initRealArriveTime"
                    );
                }
                $fanliRecord->real_arrival_time = $row['real_arrival_time'];
                $fanliRecord->save();
            } catch (Throwable $exception) {
                \framework\Log::error(
                    "处理返利单号: {$row['no']} 时出错",
                    [
                        'no'        => $row['no'],
                        'exception' => [
                            'file' => $exception->getFile(),
                            'line' => $exception->getLine(),
                            'msg'  => $exception->getMessage(),
                        ],
                    ],
                    "initRealArriveTime"
                );
            }
        }
    }
}