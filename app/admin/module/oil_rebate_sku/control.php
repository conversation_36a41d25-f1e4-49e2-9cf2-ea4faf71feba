<?php
/**
 * 返利政策sku Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/09/21
 * Time: 11:39:47
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilRebateSku;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_rebate_sku extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilRebateSku::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '返利政策sku_' . date("YmdHis"),
            'sheetName' => '返利政策sku',
            'title' => [
            'id'   =>  '自增ID',
'policy_id'   =>  '政策ID',
'weight'   =>  '权重',
'suit_oil'   =>  '适用油品',
'status'   =>  '1正常 2已删除',
'createtime'   =>  '创建时间',
'updatetime'   =>  '更新时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilRebateSku::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilRebateSku::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilRebateSku::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilRebateSku::remove($params);

        Response::json($data);
    }

}