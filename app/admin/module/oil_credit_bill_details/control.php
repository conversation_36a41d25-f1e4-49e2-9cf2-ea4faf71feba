<?php
/**
 * 授信账单明细表 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/06/09
 * Time: 10:07:57
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCreditBillDetails;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_credit_bill_details extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilCreditBillDetails::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表查询
     * @return array
     */
    public function getListForG7s()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['bill_id'], $params);

        $params['no_type'] = 'XF';
        $data = OilCreditBillDetails::getListForG7s($params);

        Response::json($data);
    }

    /**
     * G7S端支持导出
     * @throws PHPExcel_Exception
     */
    public function exportForG7s()
    {
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . 'data' . DIRECTORY_SEPARATOR . 'oil_credit_bill_details';
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }
        $url = NULL;
        $params = helper::filterParams();
        helper::argumentCheck(['bill_id'], $params);
        $params['_export'] = 1;
        $params['no_type'] = 'XF';
        $data = OilCreditBillDetails::getListForG7s($params);
        if(count($data) == 0){
            throw new \RuntimeException('无数据导出',2);
        }
        $exportData = [
            'filePath'  => $filePath,
            'fileName'  => '账单明细_' . date("YmdHis"),
            'sheetName' => '账单明细',
            'download'  => 1,
            'title'     => [
                'api_id'      => '交易流水号',
                'vice_no'     => '卡号',
                'trade_money'     => '加油金额',
                'use_fanli_money'        => '使用返利金额',
                'true_money'         => '使用现金/授信',
                'trade_place'        => '交易地点',
                'oil_name'      => '油品名称',
                'trade_num'     => '加油升数',
                'org_name'     => '所属机构',
                'qz_drivername'     => '司机姓名',
                'qz_drivertel'     => '司机手机号',
                'truck_no'     => '司机车牌号',
                'createtime'     => '消费创建时间',
            ],
            'data'      => $data,
        ];

        $filePath = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['vice_no'])) {

                $data['value'] .= "\t";
            }
            if (in_array($data['name'], ['api_id'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });

        if($filePath){
            //上传阿里云
            $url = (new \commonModel())->fileUploadToOss($filePath);

            $signUrl = \Fuel\Service\UploadService::getOssSignUrl($url);
        }else{
            throw new \RuntimeException('导出失败！',2);
        }

        Response::json(['osspath'=>$signUrl]);
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '授信账单明细表_' . date("YmdHis"),
            'sheetName' => '授信账单明细表',
            'title' => [
            'id'   =>  'id',
            'bill_id'   =>  '账单id',
            'bill_no'   =>  '账单编号',
            'orgcode'   =>  '机构code',
            'no_id'   =>  '单据主键id',
            'no_type'   =>  '单据类型 FP:分配，XF:消费，HK:还款',
            'no'   =>  '单据号',
            'no_time'   =>  '单据时间',
            'money'   =>  '金额',
            'createtime'   =>  'createtime',
            'updatetime'   =>  'updatetime'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilCreditBillDetails::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilCreditBillDetails::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilCreditBillDetails::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilCreditBillDetails::remove($params);

        Response::json($data);
    }

}