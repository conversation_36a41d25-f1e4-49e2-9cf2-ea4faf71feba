<?php
/**
 * oil_tags Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/03/01
 * Time: 15:49:59
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilTags;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_tags extends baseControl
{
    private $myAdmin;

    public function __construct()
    {
        parent::__construct();
        $this->myAdmin = $this->app->myAdmin;
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['orgcode'],$params);

        unset($params['role_id'],$params['user_id']); //暂时不开发对role_id和user_id进行检索
        $data = OilTags::getList($params);

        Response::json($data);
    }

    public function viceTagAdd()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['orgcode','vice_ids','tag_names','user_id','role_id'],$params);

        //1.如果有标签不存在，则先添加标签
        $params['tag_names'] = explode(',',$params['tag_names']);
        $tags = OilTags::getTags($params);
        $addTags = [];
        $nowTime = helper::nowTime();
        if($params['tag_names']){
            foreach ($params['tag_names'] as $v){
                if(!in_array($v,$tags)){
                    $tmp['orgcode'] = $params['orgcode'];
                    $tmp['user_id'] = $params['user_id'];
                    $tmp['role_id'] = $params['role_id'];
                    $tmp['tag_name'] = $v;
                    $tmp['createtime'] = $nowTime;
                    $tmp['updatetime'] = $nowTime;
                    $addTags[] = $tmp;
                }
            }
        }
        if($addTags){
            OilTags::batchInsert($addTags);
        }
        //2.添加副卡标签
        $tagIds = OilTags::getTagIds($params);
        $params['vice_ids'] = explode(',',$params['vice_ids']);
        \Models\OilViceTags::deleteByViceIds($params);
        $addViceTags = [];
        foreach ($tagIds as $tag_id){
            foreach ($params['vice_ids'] as $vice_id){
                $tmp['vice_id'] = $vice_id;
                $tmp['tag_id'] = $tag_id;
                $tmp['createtime'] = $nowTime;
                $tmp['updatetime'] = $nowTime;
                $addViceTags[] = $tmp;
            }
        }
        if($addViceTags){
            \Models\OilViceTags::batchInsert($addViceTags);
        }

        Response::json('操作成功');
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilTags::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['orgcode','role_id','user_id','tag_name'],$params);

        $data = OilTags::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id','orgcode','role_id','user_id','tag_name'], $params);
        $data = OilTags::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        //1.删除副卡标签
        \Models\OilViceTags::deleteByTagId(['tag_id'=>$params['id']]);
        //2.删除标签
        $data = OilTags::remove($params);

        Response::json($data);
    }

    public function test()
    {
        $data = \Models\OilViceTags::getViceNoByTagId([2,5,3]);

        var_dump($data);
    }

    /**
     * @title   初始化数据到gos
     * <AUTHOR>
     */
    public function batchAddToGos()
    {
        \Fuel\Service\TagsToGos::batchAddToGos();
    }
}