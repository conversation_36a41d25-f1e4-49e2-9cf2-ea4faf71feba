<?php

/**
 * 分配申请
 */

use Fuel\Defines\AccountBalanceAlarm;
use Fuel\Defines\CardViceBillConf;
use Fuel\Request\GasClient;
use Framework\Excel\ExcelReader;
use Fuel\Service\CardViceBill;
use Jobs\MarkTradeFanliJob;
use Models\OilAccountMoney;
use Models\OilAccountAssign;
use Models\OilAccountAssignDetails;
use Fuel\Response;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Request\OilAgentClient;
use Models\OilAccountAssignTask;
use Models\OilAccountAssignTaskDetail;
use Fuel\Service\AccountTrades;
use Fuel\Service\FrozenMoney as FrozenMoney;
use Fuel\Service\AccountAssignToGos;
use Framework\Log;
use Fuel\Service\AccountCenter\AssignService;
use Framework\Job;
use Fuel\Service\CardViceTradesToGos;
use Models\OilCardViceSubTrades;
use Models\OilCreditAccount;
use Models\OilRecycle;
use Models\OilCardViceAppLog;
use Fuel\Defines\OilCom;
use Fuel\Defines\AccountAssignStatus;
use Fuel\Service\AutoAssign;
use Fuel\Defines\OrgStatus;
use Fuel\Service\EWei;
use Fuel\Service\AccountAssign;
use Fuel\Defines\AccountType;
use Models\OilOrg;
use Fuel\Defines\CardFrom;
use Framework\DingTalk\DingTalkAlarm;
use Models\OilCardVice;
use Fuel\Service\AccountCenter\AccountService;
use Fuel\Service\AccountMoney;
use Models\OilCardAccount;
use Fuel\Service\WeChatTemplateMsg;
use Models\OilCreditProvider;
use Fuel\Export\AccountAssignExport;
use Models\OilCardViceTradesZBank;
use Models\OilConfigure;
use Models\OilAssignCompare;
use Models\OilAccountMoneyRecords;
use Models\OilCardMain;
use Fuel\Service\CardViceToGos;
use Fuel\Service\AccountCenter\TransferService;
use Fuel\Service\AccountGredit;
use Models\OilOrgWx;
use Fuel\Defines\AccountMoneyTip;
use Fuel\Defines\CheckAssignStatus;
use Fuel\Defines\DataFrom;
use Fuel\Defines\ConsumeType;
use Framework\Helper;
use Framework\Cache;
use Framework\Sms\NoticeSender;
use Models\OilAccountMoneyCharge;
use Models\OilCreditRepay;
use Models\OilMarkfanliremainLog;
use Models\OilCreditBill;
use Models\OilCardViceTrades;
use Fuel\Service\CardTradeService;
use Fuel\Defines\CardTradeConf;
use Fuel\Defines\NoTypeStatus;
use Fuel\Defines\SupplierAccountConf;

use Jobs\UpstreamSettleDataWriteJob;

use Fuel\Defines\OrgConf;
use Fuel\Defines\CardViceConf;
use Fuel\Service\Station;
use Models\OilSupplierAccount;
use Fuel\Service\SupplierAccount;
use Jobs\TradeToAssignJob;
use Models\OilCardViceTradesExt;
use Symfony\Component\Translation\Translator;

class oil_account_assign extends baseControl
{
    public $case_no;

    public function __construct()
    {
        parent::__construct();
        $this->case_no = '';
    }

    public function testCheckStatus()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['assign_id'], $params);
        $this->editCheckAssign($params['assign_id']);
        echo 'ok';
    }

    /**
     * 列表查询
     */
    public function search()
    {
        $params = \helper::filterParams();
        Log::info('查询参数--' . var_export($params, TRUE), [], 'accountAssign');

        if (isset($params['start_time']) && $params['start_time']) {
            preg_match('/\d{4}-\d{2}-\d{2}/', $params['start_time'], $start_time);
        } else {
            $params['start_time'] = date("Y-m-d", time());
        }

        if (isset($params['end_time']) && $params['end_time']) {
            preg_match('/\d{4}-\d{2}-\d{2}/', $params['end_time'], $end_time);
        }

        if (isset($params['com_start_time']) && $params['com_start_time']) {
            preg_match('/\d{4}-\d{2}-\d{2}/', $params['com_start_time'], $com_start_time);
        }

        if (isset($params['com_end_time']) && $params['com_end_time']) {
            preg_match('/\d{4}-\d{2}-\d{2}/', $params['com_end_time'], $com_end_time);
        }

        if (isset($params['u_start_time']) && $params['u_start_time']) {
            preg_match('/\d{4}-\d{2}-\d{2}/', $params['u_start_time'], $u_start_time);
        }

        if (isset($params['u_end_time']) && $params['u_end_time']) {
            preg_match('/\d{4}-\d{2}-\d{2}/', $params['u_end_time'], $u_end_time);
        }
        //todo 处理信用账户分配
        if (isset($params['account_type']) && !empty($params['account_type'])) {
            $typeList = explode(",", $params['account_type']);
            $typeIn   = [];
            $typeCode = [];
            foreach ($typeList as $item) {
                $flag = substr($item, 0, 2);
                if ($flag == 'G7') {
                    /*if(in_array($item,[AccountType::CREDIT_FREE,AccountType::CREDIT_GLP,AccountType::CREDIT_RATE])){
                        array_push($typeName,AccountType::creditTypeMap($item));
                    }*/
                    $code = substr($item, 3);
                    array_push($typeCode, $code);
                } else {
                    array_push($typeIn, $item);
                }
            }
            if (count($typeCode) > 0) {
                $accountList = OilCreditAccount::getCreditAccountNoByName($typeCode);
                if (count($accountList) > 0) {
                    $params['account_noOrIn'] = array_keys($accountList->toArray());
                }
            }
            if (count($typeIn) > 0) {
                $params['account_typeIn'] = $typeIn;
            }
            unset($params['account_type']);
        }

        if (isset($params['account_no']) && substr($params['account_no'], 0, 3) == '106') {
            $params['account_type'] = 50;
            unset($params['account_no']);
        }

        if (isset($params['account_nos']) && $params['account_nos']) {
            $params['account_type'] = 20;
            $params['account_noIn'] = explode(",", $params['account_nos']);
            unset($params['account_nos']);
        }

        //参数处理
        if (isset($params['orgcode']) && preg_match('/(.*)\s/', $params['orgcode'])) {
            preg_match('/(.*)\s/', $params['orgcode'], $arr);
            $params['orgcode'] = $arr[1];
        }

        if (isset($params['_export']) && $params['_export'] == 1) {

            $task = (new \Jobs\ExportAccountAssignJob($params))
                ->setTaskName('分配申请')
                ->setUserInfo($this->app->myAdmin)
                ->onQueue('default')
                ->dispatch();

            Response::json(["redirect_url"=>$task->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');

//            $params['excelType'] = 'csv';
//            $data                = (new AccountAssignExport())->export($params);
//
//            if ($data == 'async') {
//                Response::json(null, 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
//            } else {
//                Response::json(['url' => $data]);
//            }
        } else {
            $data = Models\OilAccountAssign::getList($params);
            $data = OilOrg::handleData($data);
            Response::json($data);
        }
    }


    public function deleteCreditAssign()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['id'], $params);
        $assignInfo = OilAccountAssign::getById(['id' => $params['id']]);
        Capsule::connection()->beginTransaction();

        $id = null;
        try {
            if ($assignInfo) {
                (new AssignService())->doFailById($assignInfo->id);

                $id = $assignInfo->id;
                //删除加入回收站
                $recycle                = [];
                $recycle['table_name']  = 'oil_account_assign';
                $recycle['pk']          = $assignInfo->id;
                $recycle['org_id']      = $assignInfo->org_id;
                $recycle['no']          = $assignInfo->no;
                $recycle['sn']          = $assignInfo->sn;
                $recycle['data']        = json_encode($assignInfo);
                $recycle['operator_id'] = $this->app->myAdmin->id;
                $recycle['createtime']  = \helper::nowTime();
                $recycles               = OilRecycle::add($recycle);
                if ($recycles) {
                    OilAccountAssignDetails::removeByAssignId(['assignIds' => [$assignInfo->id]]);
                    OilAccountAssign::remove(['ids' => [$assignInfo->id]]);
                }
            }
            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            throw new RuntimeException($e->getMessage(), 2);
        }

        //push至Gos系统
        AccountAssignToGos::remove([$id]);

        Response::json(null, 0, '删除成功');
    }

    //测试卡余额查询接口
    public function test2()
    {
        $decuctionList = Models\OilCardDeductionAmount::getCardAmountMap();
        var_dump($decuctionList);exit;
        $consumeTime = '2019-10-25 13:40:57';
        $short_no    = '****************';
        $money       = '5953.64';
        $mobile      = '***********';
        $content     = '尊敬的客户您好，您于' . $consumeTime . '使用尾号' . $short_no . '消费' . $money . '元，该笔交易已被撤销。如遇疑问详询客服电话************转2。';

        $sendData['content'] = $content;
        $sendData['mobiles'] = $mobile;
        $sendData['path']    = '/message/general';
        $res                 = \Framework\Sms\NewSms::send($sendData);

        var_dump($res);
        exit;

        ///////////////////////////////////////
        $consumeTime = '2019-10-25 13:41:45';
        $short_no    = '6211031499198161';
        $money       = '1079.01';
        $mobile      = '13317321153';
        $content     = '尊敬的客户您好，您于' . $consumeTime . '使用尾号' . $short_no . '消费' . $money . '元，该笔交易已被撤销。如遇疑问详询客服电话************转2。';

        $sendData['content'] = $content;
        $sendData['mobiles'] = $mobile;
        $sendData['path']    = '/message/general';
        $res                 = \Framework\Sms\NewSms::send($sendData);

        $consumeTime = '2019-10-25 13:41:19';
        $short_no    = '6211029635024838';
        $money       = '1809';
        $mobile      = '13833039303';
        $content     = '尊敬的客户您好，您于' . $consumeTime . '使用尾号' . $short_no . '消费' . $money . '元，该笔交易已被撤销。如遇疑问详询客服电话************转2。';

        $consumeTime = '2019-10-25 13:41:29';
        $short_no    = '6211053555034478';
        $money       = '1903.3';
        $mobile      = '15769519495';
        $content     = '尊敬的客户您好，您于' . $consumeTime . '使用尾号' . $short_no . '消费' . $money . '元，该笔交易已被撤销。如遇疑问详询客服电话************转2。';

//        $consumeTime = '2019-10-25 13:41:29';
//        $short_no = '6211053555034478';
//        $money = '1903.3';
//        $mobile = '15769519495';
//        $content = '尊敬的客户您好，您于' . $consumeTime . '使用卡号' . $short_no . '的1号卡的消费'.$money.'元，该笔交易已被撤销。如遇疑问详询客服电话************转2。';

        $consumeTime = '2019-10-25 13:42:26';
        $short_no    = '****************';
        $money       = '2150';
        $mobile      = '***********';
        $content     = '尊敬的客户您好，您于' . $consumeTime . '使用尾号' . $short_no . '消费' . $money . '元，该笔交易已被撤销。如遇疑问详询客服电话************转2。';


        var_dump($res);
        exit;

        $res = (new AccountService())->revokeCardConsume([
            'extID'       => '********-ca08-11e9-b694-00163e000703',
            'billID'      => '1166906836222353411',
            'companyCode' => 'G7_DALIAN',
            'totalAmount' => 1016,
            'comment'     => 'comment',
        ]);

        var_dump($res);
        exit;

//        $data = \Fuel\Service\CardVice::getCardList('***********',null,'c0c84328a597c42595bf14c967e0b010');
//
//        var_dump($data->toArray());exit;
//
//        $consume = (new AccountService())->getCardConsume(['extID'=>'775d833c-bb15-11e9-8b8a-00163e000703']);
//        var_dump('消费查询',$consume);exit;

        $creditInfo = (new AccountService())->getAccountInfoByProductCode(['subAccountID' => '1159297459281862661', 'one' => 1], 'OIL_G7_45');
        var_dump('机构授信帐号', $creditInfo);
        $cardInfo = (new AccountService())->getAccountInfoByProductCode(['subAccountID' => '1158550237451788296', 'one' => 1], 'OIL_G7_45');
        var_dump('卡授信额度', $cardInfo);
        exit;
        $orginfo = (new AccountService())->getAccountInfoByProductCode(['orgCode' => '200133', 'accountUniqueIdentifier' => 'OIL_G7_45'], 'OIL_G7_45');

        var_dump('机构授信账户', $orginfo);
        exit;
        $_data = OilAccountAssignDetails::whereIn('id', [51420, 51421, 51422])->get();
        if ($_data) {
            $updateArr = [];
            foreach ($_data as $detail) {
                $tmp = [];
                if (!$detail->callback_time) {
                    $tmp['callback_time']     = \helper::nowTime();
                    $tmp['callback_time_end'] = \helper::nowTime();
                } else {
                    $tmp['callback_time_end'] = \helper::nowTime();
                }
                $tmp['status']         = 10;
                $tmp['assign_message'] = '成功';
                $tmp['where']          = 'id = ' . $detail->id;

                $updateArr[] = $tmp;
            }
            var_dump($updateArr);
            OilAccountAssignDetails::batchEditByPdo($updateArr);
        }
        exit;
        echo date('Y-m-d', strtotime($value['callback_time']) - 5 * 60);
        exit;
        $diff = array_diff(['****************'], []);
        if ($diff && count($diff) > 0) {
            throw new RuntimeException('子账户账号：' . implode(',', $diff) . '不存在', 2);
        }
        echo 222;
        exit;
        foreach ($times as $v) {
            echo date('Y-m-d H:i:s', strtotime($v)) . '</br>';
        }
        exit;
        echo date('Y-m-d H:i:s', strtotime("9/5/2018 00:30:37"));
        exit;
//        $arr = [1,2,3,4,5,6];
//        var_dump(array_chunk($arr,100));exit;
        $res = (new AssignService())->doFail([
            'totalAmount' => 544198, //1400000,200000
            'billID'      => '1026750040481382407' //996415530002968581,996415750648524803
        ]);
        var_dump($res);
        exit;
        // (new \Fuel\Service\AccountCenter\ReChargeService())->mockInit([]);
//        $subCardList = [a
//          '1000113200015378975',
//          '1000113200015378976',
//        ];
//        $data = OilAgentClient::post(
//            [
//                'method'=>'crawl-provider.fuelCardService.query',
//                'data'=>[
//                    'cardtype'=>'zsh',
//                    'account'=>'zqxjs007',
//                    'level'=>1,
//                    'callback'=>'',
//                    'params'=>json_encode([
//                        'taskType'=>'zshBalanceCrawler',
//                        'account'=>'zqxjs007',
//                        'password'=>'zqxjs0007',
//                        'parentcard'=>'1000113200015370818',
//                        'cardList'=> $subCardList,
//                    ]),
//                ]
//            ]
//        );

        $subCardList = [
            ['****************' => 0.06],
            ['****************' => 0.05],
        ];
        $data        = OilAgentClient::post(
            [
                'method' => 'crawl-provider.fuelCardService.assign',
                'data'   => [
                    'cardtype'    => 'zsy',
                    'tasktype'    => 'zsyCharge',
                    'account'     => 'zqx_4001',
                    'password'    => 'zqxht007',
                    'parentcard'  => '****************',
                    'subcardlist' => json_encode($subCardList),
                    'level'       => 1,
                    'jobnum'      => '',
                ],
            ]
        );
        var_dump($data);
    }


    public function doFailByBillID()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['billId', 'totalAmount'], $params);
        $res = (new AssignService())->doFail([
            'totalAmount' => intval($params['totalAmount']), //1400000,200000
            'billID'      => $params['billId'] //996415530002968581,996415750648524803
        ]);
        var_dump($res);
        exit;
    }

    /**
     * 回调函数
     */
    public function responseResult()
    {
        $params = \helper::filterParams();
//        $params = array (
//            'result' => 0,
//            'responseResult' => '{"data":[{"cardNo":"****************","msg":"子卡备付金余额等于0","assignAmount":"0.01"},{"cardNo":"****************","msg":"子卡备付金余额等于0","assignAmount":"0.01"}],"result":0,"message":"成功"}',
//            'taskType' => 'zsyPositiveDis',
//            'time' => '2018-10-16 15:41:30',
//            'message' => '成功',
//            'taskId' => 'c68932a12c224843b40428a654219d2b', );

        Log::error('params-:' . var_export($params, TRUE), [], 'reponseResult');

        if (!isset($params['result'])) {
            Log::dataLog('oil_account_assign | responseResult | result 缺失', 'gasAgent-callBack');
            throw new RuntimeException('result 缺失', 3);
        }
        if ($params['result'] === '0' || $params['result'] === 0) {
            $status = 10;
        } elseif ($params['result'] == 8) {
            $status = 8;
        } else {
            $status = -10;
        }

        if ($params['result'] === '6' || $params['result'] === 6 || $params['result'] === '8' || $params['result'] === 8) {
            $responseResult = json_decode($params['responseResult'], TRUE);
            if (isset($responseResult['data']) && $responseResult['data']) {
                $status = 10;
            }
            Log::error('result:6--' . var_export($params, TRUE), [], 'reponseResult_faild6');
        }

        $params['status'] = $status;
        $amountType       = $desc_msg = '';
        switch ($params['taskType']) {
            case 'zsyPositiveDis':
                $amountType = 'assignAmount';
                $desc_msg   = '实际分配';
                break;
            case 'zsyNegativeDis':
                $amountType = 'turnAmount';
                $desc_msg   = '实际圈回';
                break;
            case 'zshCashPositiveDis':
                $amountType = 'assignAmount';
                $desc_msg   = '实际分配';
                break;
            case 'zshCashNegativeDis':
                $amountType = 'turnAmount';
                $desc_msg   = '实际圈回';
                break;
            case 'zshIntegralPositiveDis':
                $amountType = 'assignIntegral';
                $desc_msg   = '实际分配';
                break;
            case 'zshIntegralNegativeDis':
                $amountType = 'turnIntegral';
                $desc_msg   = '实际圈回';
                break;
        }
        $params['desc_msg']   = $desc_msg;
        $params['amountType'] = $amountType;

        //通过任务ID查出assign_id及assign_detail_id
        $assignInfo                  = OilAccountAssignTask::where('taskId', $params['taskId'])->first();
        $assignDetailInfo            = OilAccountAssignTaskDetail::select(Capsule::connection()->raw('GROUP_CONCAT(assign_detail_id) ids'))->where('taskId', $params['taskId'])->first();
        $params['assign_id']         = $assignInfo->assign_id;
        $params['assign_detail_ids'] = explode(',', $assignDetailInfo->ids);

        try {
            $autoAssign = new AutoAssign();
            $autoAssign->updateTaskStatus(//更新任务状态
                function () use ($params) {
                    OilAccountAssignTask::where('taskId', $params['taskId'])->update(['status' => $params['status'], 'message' => $params['result'] . '--' . htmlspecialchars($params['message'])]);
                    OilAccountAssignTaskDetail::where('taskId', $params['taskId'])->update(['master_status' => $params['status'],'master_message'=>$params['result'] . '--' . htmlspecialchars($params['message'])]);
                }
            )->updateAssignOrderStatus(//更新分配单状态
                function () use ($params) {

                    Log::error(var_export($params, TRUE), [], 'reponseResult');
                    $proxyData = json_decode($params['responseResult'], TRUE);

                    $distributeId = isset($proxyData['distributeId']) && $proxyData['distributeId'] ? $proxyData['distributeId'] : null;

                    $_data = OilAccountAssignDetails::whereIn('id', $params['assign_detail_ids'])->get();

                    $assignDetailFailed = [];
                    if ($_data) {
                        $updateArr = $updateBalance = $reSendAssign = [];
                        foreach ($_data as $detail) {
                            $tmp = $balanceInfo =[];
                            //$_status = -10;
                            $check_assign = -10;
                            $_msg         = "";
                            if ($params['result'] === '0' || $params['result'] === 0 || $params['result'] === '6' || $params['result'] === 6 || $params['result'] === '8' || $params['result'] === 8) {
                                if (isset($proxyData['data']) && $proxyData['data']) {
                                    foreach ($proxyData['data'] as $key => $val) {
                                        if ($detail->vice_no == $val['cardNo']) {
                                            $_msg = $val['msg'] . "," . $params['desc_msg'] . "：" . $val[$params['amountType']];
                                            Log::error('diff' . var_export(bccomp(abs($detail->assign_money) * 100, $val[$params['amountType']] * 100), TRUE), [], 'reponseResult');
                                            if ($val['status'] == 0) {
                                                //G7WALLET-6131备付金逻辑调整
                                                if(isset($val['reserveRemain']) && $val['reserveRemain']){
                                                    $balanceInfo['reserveRemain'] = $val['reserveRemain'];
                                                }
                                                if(isset($val['cardRemain']) && $val['cardRemain']){
                                                    $balanceInfo['cardRemain'] = $val['cardRemain'];
                                                }
                                                if(isset($val['cardIntegralRemain']) && $val['cardIntegralRemain']){
                                                    $balanceInfo['cardIntegralRemain'] = $val['cardIntegralRemain'];
                                                }
                                                if(isset($val['reserveIntegralRemain']) && $val['reserveIntegralRemain']){
                                                    $balanceInfo['reserveIntegralRemain'] = $val['reserveIntegralRemain'];
                                                }
                                                if($balanceInfo){
                                                    $updateBalance[$val['cardNo']] = $balanceInfo;
                                                }

                                                //成功
                                                //中石化分配
                                                if (in_array($params['taskType'], ['zshCashPositiveDis', 'zshCashNegativeDis'])) {
                                                    if (bccomp($detail->assign_money * 100, $val[$params['amountType']] * 100) == 0) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 10;
                                                        unset($proxyData['data'][$key]);
                                                    } else {
                                                        $params['status'] = -10;
                                                        $check_assign     = -10;
                                                    }
                                                } elseif (in_array($params['taskType'], ['zshIntegralPositiveDis', 'zshIntegralNegativeDis'])) {
                                                    //积分分配
                                                    if (bccomp($detail->assign_jifen * 100, $val[$params['amountType']] * 100) == 0) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 10;
                                                        unset($proxyData['data'][$key]);
                                                    } else {
                                                        $params['status'] = -10;
                                                        $check_assign     = -10;
                                                    }
                                                } elseif (in_array($params['taskType'], ['zsyNegativeDis'])) {
                                                    //中石油圈回
                                                    if (bccomp(abs($detail->assign_money) * 100, $val[$params['amountType']] * 100) == 0) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 10;
                                                        unset($proxyData['data'][$key]);
                                                    } else {
                                                        $params['status'] = -8;
                                                        $check_assign     = -10;
                                                    }
                                                } else {
                                                    if (bccomp(abs($detail->assign_money) * 100, $val[$params['amountType']] * 100) == 0) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 10;
                                                        unset($proxyData['data'][$key]);
                                                    } else {
                                                        $params['status'] = -10;
                                                        $check_assign     = -10;
                                                    }
                                                }
                                            } elseif ($val['status'] == -1) {
                                                //失败
                                                $params['status'] = -10;
                                                $check_assign     = 0;
                                                //$_msg = $val['msg'].'失败，等待校验';
                                                $_msg = $val['msg'];
                                                //$reSendAssign[] = $detail->assign_id;
                                                $assignDetailFailed[$detail->id] = [
                                                    'reason' => $val['msg'],
                                                ];
                                            } elseif ($val['status'] == 99) {
                                                //不确定，待发起校验
                                                $params['status'] = 10;
                                                $_msg             = $val['msg'] . '等待校验';
                                                $check_assign     = 0;
                                            } else {
                                                $params['status'] = -10;
                                                $check_assign     = -10;
                                            }
                                        }
                                        if (strpos($val['msg'], '实际差值：0.00') !== FALSE) {
                                            $_msg = "分配后余额无变化，请登陆主站核查,并尝试重新下发分配任务";
                                        } elseif (strpos($val['msg'], '实际差值') !== FALSE) {
                                            $_msg = "分配后余额有变化，请登陆主站核查";
                                        } elseif (strpos($val['msg'], '数据获取失败') !== FALSE) {
                                            $_msg = "无预分配订单，请登陆主站核查";
                                        }
                                    }
                                } else {
                                    $_msg = $params['result'] . '--' . htmlspecialchars($params['message']);
                                    $assignDetailFailed[$detail->id] = [
                                        'reason' => $params['message'],
                                    ];
                                    if (strpos($params['message'], '需人工校验') !== FALSE) {
                                        $_msg = "需人工校验,请登陆主站核查";
                                    }
                                }
                            } else {
                                $_msg = $params['result'] . '--' . htmlspecialchars($params['message']) . '-result值超出约定';
                                $assignDetailFailed[$detail->id] = [
                                    'reason' => $params['result'] . '--' . $params['message'] . '-result值超出约定',
                                ];
                            }

                            if (!$detail->callback_time) {
                                $tmp['callback_time']     = \helper::nowTime();
                                $tmp['callback_time_end'] = \helper::nowTime();
                            } else {
                                $tmp['callback_time_end'] = \helper::nowTime();
                            }

                            if ($distributeId) {
                                $tmp['distributeId'] = $distributeId;
                            }

                            $tmp['status']         = $params['status'];
                            //1216更改
                            if ($params['message'] == '分配失败' && in_array($params['taskType'],['zshCashPositiveDis','zshCashNegativeDis','zshIntegralPositiveDis','zshIntegralNegativeDis'])) {
                                $_msg = $params['result']."--主站订单失败，请等待15分钟后，登陆主站查询此单完成情况";
                            }
                            $tmp['assign_message'] = $_msg;
//                            if(in_array($params['taskType'],['zsyNegativeDis','zsyPositiveDis'])){
//                                $tmp['check_assign'] = $check_assign;
//                            }
                            $tmp['check_assign'] = $check_assign;
                            $tmp['where']        = 'id = ' . $detail->id;

                            $updateArr[] = $tmp;

                            //任务明细状态更改
                            $taskDetails[] = [
                                'where' => "assign_detail_id = ".$detail->id." and taskId = '".$params['taskId']."' ",
                                'status' => $params['status'],
                                'message' => $_msg
                                ];

                        }

                        Log::error('$updateArr' . var_export($updateArr, TRUE), [], 'reponseResult');
                        OilAccountAssignTaskDetail::batchEditByPdo($taskDetails);

                        OilAccountAssignDetails::batchEditByPdo($updateArr);

                        if($updateBalance){
                            Log::error('updateBalance'.var_export($updateBalance, TRUE), [], 'reponseResult');
                            $balanceUpdateSql = $_tmp_sql = [];
                            foreach ($updateBalance as  $vice_no => $item){
                                $_tmp_sql['where'] = "vice_no = '".$vice_no."'";
                                if(isset($item['reserveRemain']) && $item['reserveRemain']){
                                    $_tmp_sql['reserve_remain'] = $item['reserveRemain'];
                                }
                                if(isset($item['cardRemain']) && $item['cardRemain']){
                                    $_tmp_sql['card_remain'] = $item['cardRemain'];
                                }
                                if(isset($item['cardIntegralRemain']) && $item['cardIntegralRemain']){
                                    $_tmp_sql['point_remain'] = $item['cardIntegralRemain'];
                                }
                                if(isset($item['reserveIntegralRemain']) && $item['reserveIntegralRemain']){
                                    $_tmp_sql['point_reserve_total'] = $item['reserveIntegralRemain'];
                                }

                                $balanceUpdateSql[] = $_tmp_sql;
                            }
                            Log::error('balanceUpdateSql'.var_export($balanceUpdateSql, TRUE), [], 'reponseResult');
                            OilCardVice::batchEditByPdo('oil_card_vice',$balanceUpdateSql);
                        }

//                        //分配失败-1的这种情况实行重新下发分配动作
//                        if($reSendAssign && in_array($params['taskType'],['zsyPositiveDis'])){
//                            foreach(array_unique($reSendAssign) as $re_send_assign_id){
//                                try{
//                                    $sendCount = Cache::get('reSend'.$re_send_assign_id) ? Cache::get('reSend'.$re_send_assign_id) : 1;
//                                    if($sendCount <= 5){
//                                        OilAccountAssign::autoAssignTask(['assign_id'=>$re_send_assign_id,'key_type'=>'autoAssign']);
//                                        Log::error('reSend:assign_id:'.$re_send_assign_id.'执行次数'.$sendCount.'taskParams'.var_export($params,TRUE),[],'reSendTask');
//                                        Cache::put('reSend'.$re_send_assign_id,$sendCount+1,86400);
//                                    }
//                                }catch (Exception $e){
//                                    Log::error('reSend:Exception:'.strval($e),[],'reSendTask');
//                                }
//                            }
//                        }
                    }

                    $status = OilAccountAssign::getAssignNoStatus($params['assign_id'], FALSE);

                    Log::error(var_export($status, TRUE), [], 'reponseResult');

                    if ($status != 20) {
                        $info = OilAccountAssign::getById(['id' => $params['assign_id']]);
                        if ($info->status != 1) {//未审核时才允许改分配单主状态
                            OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
                            //添加工单日志
                            OilCardViceAppLog::add([
                                'type'             => 2,
                                'app_id'           => $params['assign_id'],
                                'status'           => $status,
                                'status_name'      => AccountAssignStatus::getById($status),
                                'last_operator'    => '系统操作',
                                'last_operator_id' => 1,
                                'createtime'       => \helper::nowTime(),
                                'updatetime'       => \helper::nowTime(),
                            ]);

                            if ($status == -10) {
                                $message = '【自动分配】亲，工单' . $info->no . '自动分配失败，请及时处理哦～';
                                AutoAssign::sendNotify($message);
                            }
                            if (($status == -10 or $status == -20 or $status == -8 or $status == 8) and
                                $assignDetailFailed) {
                                try {
                                    (new DingTalkAlarm())->sendDingTalkByConfig(
                                        "fossoilBankGroup",
                                        "",
                                        OilAccountAssign::getAssignAndDetailAlarmInfo(
                                            $params['assign_id'],
                                            $assignDetailFailed
                                        ), [], false, 'post'
                                    );
                                    //肇庆飞书提醒
                                    if($info->gms_order_id){
                                        Station::sendAssignAlerm(json_encode($assignDetailFailed,JSON_UNESCAPED_UNICODE),$info->gms_order_id);
                                    }
                                } catch (Exception $e) {
                                    Log::error("分配失败飞书预警发生异常", [
                                        'assign_id' => $params['assign_id'],
                                        'error'         => $e->getMessage(),
                                        'assign_detail_failed' => $assignDetailFailed
                                    ],  'sendDingTalkByConfig');
                                }
                            }

                            //自动校验通过审核逻辑
                            if ($status == 10) {
                                //获取分配单下所有校验状态
                                $checkStatus = $this->editCheckAssign($params['assign_id']);

                                //根据校验状态判断是否自动审核
                                //1首先判断总开关配置是否开启
                                $info    = OilAccountAssign::getById(['id' => $params['assign_id']]);
                                $is_open = OilConfigure::getBySysKey('auto_assign_switch');
                                if ($checkStatus && $checkStatus == 10 && intval($is_open) == 2) {
                                    $_POST['id']          = $params['assign_id'];
                                    $_POST['isAutoAudit'] = 1;
                                    try {
                                        $this->auditBy();
                                    } catch (Exception $e) {
                                        Log::error($e->getCode() . '--' . $e->getMessage(), [], 'assignAudit');
                                        $message = '【自动审核】亲，工单' . $info->no . '自动审核失败，请及时处理哦～';
                                        AutoAssign::sendNotify($message);
                                    }
                                }
                            }
                        }
                    }

                    Log::error('oil_account_assign | responseResult | 分配回调完成', [], 'reponseResult');
                }
            );
        } catch (Exception $e) {

            Log::dataLog('oil_account_assign | responseResult | 分配回调异常 | $e:' . strval($e), 'gasAgent-callBack');

            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return TRUE;
    }

    /**
     * 回调函数
     */
    public function responseResult2()
    {
        $params = \helper::filterParams();
        $params = array (
            'result' => 0,
            'responseResult' => '{"data":[{"cardNo":"****************","msg":"子卡备付金余额等于0","assignAmount":"0.6"},{"cardNo":"****************","msg":"子卡备付金余额等于0","assignAmount":"0.2"}],"result":0,"message":"成功"}',
            'taskType' => 'zsyPositiveDis',
            'time' => '2018-10-16 15:41:30',
            'message' => '成功',
            'taskId' => '054dc5fdf9d54536b901c7faec987cb0', );

        Log::error('params-:' . var_export($params, TRUE), [], 'reponseResult');

        if (!isset($params['result'])) {
            Log::dataLog('oil_account_assign | responseResult | result 缺失', 'gasAgent-callBack');
            throw new RuntimeException('result 缺失', 3);
        }
        if ($params['result'] === '0' || $params['result'] === 0) {
            $status = 10;
        } elseif ($params['result'] == 8) {
            $status = 8;
        } else {
            $status = -10;
        }

        if ($params['result'] === '6' || $params['result'] === 6 || $params['result'] === '8' || $params['result'] === 8) {
            $responseResult = json_decode($params['responseResult'], TRUE);
            if (isset($responseResult['data']) && $responseResult['data']) {
                $status = 10;
            }
            Log::error('result:6--' . var_export($params, TRUE), [], 'reponseResult_faild6');
        }

        $params['status'] = $status;
        $amountType       = $desc_msg = '';
        switch ($params['taskType']) {
            case 'zsyPositiveDis':
                $amountType = 'assignAmount';
                $desc_msg   = '实际分配';
                break;
            case 'zsyNegativeDis':
                $amountType = 'turnAmount';
                $desc_msg   = '实际圈回';
                break;
            case 'zshCashPositiveDis':
                $amountType = 'assignAmount';
                $desc_msg   = '实际分配';
                break;
            case 'zshCashNegativeDis':
                $amountType = 'turnAmount';
                $desc_msg   = '实际圈回';
                break;
            case 'zshIntegralPositiveDis':
                $amountType = 'assignIntegral';
                $desc_msg   = '实际分配';
                break;
            case 'zshIntegralNegativeDis':
                $amountType = 'turnIntegral';
                $desc_msg   = '实际圈回';
                break;
        }
        $params['desc_msg']   = $desc_msg;
        $params['amountType'] = $amountType;

        //通过任务ID查出assign_id及assign_detail_id
        $assignInfo                  = OilAccountAssignTask::where('taskId', $params['taskId'])->first();
        $assignDetailInfo            = OilAccountAssignTaskDetail::select(Capsule::connection()->raw('GROUP_CONCAT(assign_detail_id) ids'))->where('taskId', $params['taskId'])->first();
        $params['assign_id']         = $assignInfo->assign_id;
        $params['assign_detail_ids'] = explode(',', $assignDetailInfo->ids);

        try {
            $autoAssign = new AutoAssign();
            $autoAssign->updateTaskStatus(//更新任务状态
                function () use ($params) {
                    OilAccountAssignTask::where('taskId', $params['taskId'])->update(['status' => $params['status'], 'message' => $params['result'] . '--' . htmlspecialchars($params['message'])]);
                    OilAccountAssignTaskDetail::where('taskId', $params['taskId'])->update(['master_status' => $params['status'],'master_message'=>$params['result'] . '--' . htmlspecialchars($params['message'])]);
                }
            )->updateAssignOrderStatus(//更新分配单状态
                function () use ($params) {

                    Log::error(var_export($params, TRUE), [], 'reponseResult');
                    $proxyData = json_decode($params['responseResult'], TRUE);

                    $distributeId = isset($proxyData['distributeId']) && $proxyData['distributeId'] ? $proxyData['distributeId'] : null;

                    $_data = OilAccountAssignDetails::whereIn('id', $params['assign_detail_ids'])->get();

                    if ($_data) {
                        $updateArr = $reSendAssign = [];
                        foreach ($_data as $detail) {
                            $tmp = [];
                            //$_status = -10;
                            $check_assign = -10;
                            $_msg         = "";
                            if ($params['result'] === '0' || $params['result'] === 0 || $params['result'] === '6' || $params['result'] === 6 || $params['result'] === '8' || $params['result'] === 8) {
                                if (isset($proxyData['data']) && $proxyData['data']) {
                                    foreach ($proxyData['data'] as $key => $val) {
                                        if ($detail->vice_no == $val['cardNo']) {
                                            $_msg = $val['msg'] . "," . $params['desc_msg'] . "：" . $val[$params['amountType']];
                                            Log::error('diff' . var_export(bccomp(abs($detail->assign_money) * 100, $val[$params['amountType']] * 100), TRUE), [], 'reponseResult');
                                            if ($val['status'] == 0) {
                                                //成功
                                                //中石化分配
                                                if (in_array($params['taskType'], ['zshCashPositiveDis', 'zshCashNegativeDis'])) {
                                                    if (bccomp($detail->assign_money * 100, $val[$params['amountType']] * 100) == 0) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 10;
                                                        unset($proxyData['data'][$key]);
                                                    } else {
                                                        $params['status'] = -10;
                                                        $check_assign     = -10;
                                                    }
                                                } elseif (in_array($params['taskType'], ['zshIntegralPositiveDis', 'zshIntegralNegativeDis'])) {
                                                    //积分分配
                                                    if (bccomp($detail->assign_jifen * 100, $val[$params['amountType']] * 100) == 0) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 10;
                                                        unset($proxyData['data'][$key]);
                                                    } else {
                                                        $params['status'] = -10;
                                                        $check_assign     = -10;
                                                    }
                                                } elseif (in_array($params['taskType'], ['zsyNegativeDis'])) {
                                                    //中石油圈回
                                                    if (bccomp(abs($detail->assign_money) * 100, $val[$params['amountType']] * 100) == 0) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 10;
                                                        unset($proxyData['data'][$key]);
                                                    } else {
                                                        $params['status'] = -8;
                                                        $check_assign     = -10;
                                                    }
                                                } else {
                                                    if (bccomp(abs($detail->assign_money) * 100, $val[$params['amountType']] * 100) == 0) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 10;
                                                        unset($proxyData['data'][$key]);
                                                    } else {
                                                        $params['status'] = -10;
                                                        $check_assign     = -10;
                                                    }
                                                }
                                            } elseif ($val['status'] == -1) {
                                                //失败
                                                $params['status'] = -10;
                                                $check_assign     = 0;
                                                //$_msg = $val['msg'].'失败，等待校验';
                                                $_msg = $val['msg'];
                                                //$reSendAssign[] = $detail->assign_id;
                                            } elseif ($val['status'] == 99) {
                                                //不确定，待发起校验
                                                $params['status'] = 10;
                                                $_msg             = $val['msg'] . '等待校验';
                                                $check_assign     = 0;
                                            } else {
                                                $params['status'] = -10;
                                                $check_assign     = -10;
                                            }
                                        }
                                        if (strpos($val['msg'], '实际差值：0.00') !== FALSE) {
                                            $_msg = "分配后余额无变化，请登陆主站核查,并尝试重新下发分配任务";
                                        } elseif (strpos($val['msg'], '实际差值') !== FALSE) {
                                            $_msg = "分配后余额有变化，请登陆主站核查";
                                        } elseif (strpos($val['msg'], '数据获取失败') !== FALSE) {
                                            $_msg = "无预分配订单，请登陆主站核查";
                                        }
                                    }
                                } else {
                                    $_msg = $params['result'] . '--' . htmlspecialchars($params['message']);
                                    if (strpos($params['message'], '需人工校验') !== FALSE) {
                                        $_msg = "需人工校验,请登陆主站核查";
                                    }
                                }
                            } else {
                                $_msg = $params['result'] . '--' . htmlspecialchars($params['message']) . '-result值超出约定';
                            }

                            if (!$detail->callback_time) {
                                $tmp['callback_time']     = \helper::nowTime();
                                $tmp['callback_time_end'] = \helper::nowTime();
                            } else {
                                $tmp['callback_time_end'] = \helper::nowTime();
                            }

                            if ($distributeId) {
                                $tmp['distributeId'] = $distributeId;
                            }

                            $tmp['status']         = $params['status'];
                            //1216更改
                            if ($params['message'] == '分配失败' && in_array($params['taskType'],['zshCashPositiveDis','zshCashNegativeDis','zshIntegralPositiveDis','zshIntegralNegativeDis'])) {
                                $_msg = $params['result']."--主站订单失败，请等待15分钟后，登陆主站查询此单完成情况";
                            }
                            $tmp['assign_message'] = $_msg;
//                            if(in_array($params['taskType'],['zsyNegativeDis','zsyPositiveDis'])){
//                                $tmp['check_assign'] = $check_assign;
//                            }
                            $tmp['check_assign'] = $check_assign;
                            $tmp['where']        = 'id = ' . $detail->id;

                            $updateArr[] = $tmp;

                            //任务明细状态更改
                            $taskDetails[] = [
                                'where' => "assign_detail_id = ".$detail->id." and taskId = '".$params['taskId']."' ",
                                'status' => $params['status'],
                                'message' => $_msg
                            ];

                        }

                        Log::error('$updateArr' . var_export($updateArr, TRUE), [], 'reponseResult');

                        OilAccountAssignDetails::batchEditByPdo($updateArr);

                        //更新分配回调任务明细的结果message和status状态
                        OilAccountAssignTaskDetail::batchEditByPdo($taskDetails);

//                        //分配失败-1的这种情况实行重新下发分配动作
//                        if($reSendAssign && in_array($params['taskType'],['zsyPositiveDis'])){
//                            foreach(array_unique($reSendAssign) as $re_send_assign_id){
//                                try{
//                                    $sendCount = Cache::get('reSend'.$re_send_assign_id) ? Cache::get('reSend'.$re_send_assign_id) : 1;
//                                    if($sendCount <= 5){
//                                        OilAccountAssign::autoAssignTask(['assign_id'=>$re_send_assign_id,'key_type'=>'autoAssign']);
//                                        Log::error('reSend:assign_id:'.$re_send_assign_id.'执行次数'.$sendCount.'taskParams'.var_export($params,TRUE),[],'reSendTask');
//                                        Cache::put('reSend'.$re_send_assign_id,$sendCount+1,86400);
//                                    }
//                                }catch (Exception $e){
//                                    Log::error('reSend:Exception:'.strval($e),[],'reSendTask');
//                                }
//                            }
//                        }
                    }

                    $status = OilAccountAssign::getAssignNoStatus($params['assign_id'], FALSE);

                    Log::error(var_export($status, TRUE), [], 'reponseResult');

                    if ($status != 20) {
                        $info = OilAccountAssign::getById(['id' => $params['assign_id']]);
                        if ($info->status != 1) {//未审核时才允许改分配单主状态
                            OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
                            //添加工单日志
                            OilCardViceAppLog::add([
                                'type'             => 2,
                                'app_id'           => $params['assign_id'],
                                'status'           => $status,
                                'status_name'      => AccountAssignStatus::getById($status),
                                'last_operator'    => '系统操作',
                                'last_operator_id' => 1,
                                'createtime'       => \helper::nowTime(),
                                'updatetime'       => \helper::nowTime(),
                            ]);

                            if ($status == -10) {
                                $message = '【自动分配】亲，工单' . $info->no . '自动分配失败，请及时处理哦～';
                                AutoAssign::sendNotify($message);
                            }

                            //自动校验通过审核逻辑
                            if ($status == 10) {
                                //获取分配单下所有校验状态
                                $checkStatus = $this->editCheckAssign($params['assign_id']);

                                //根据校验状态判断是否自动审核
                                //1首先判断总开关配置是否开启
                                $info    = OilAccountAssign::getById(['id' => $params['assign_id']]);
                                $is_open = OilConfigure::getBySysKey('auto_assign_switch');
                                if ($checkStatus && $checkStatus == 10 && intval($is_open) == 2) {
                                    $_POST['id']          = $params['assign_id'];
                                    $_POST['isAutoAudit'] = 1;
                                    try {
                                        $this->auditBy();
                                    } catch (Exception $e) {
                                        Log::error($e->getCode() . '--' . $e->getMessage(), [], 'assignAudit');
                                        $message = '【自动审核】亲，工单' . $info->no . '自动审核失败，请及时处理哦～';
                                        AutoAssign::sendNotify($message);
                                    }
                                }
                            }
                        }
                    }

                    Log::error('oil_account_assign | responseResult | 分配回调完成', [], 'reponseResult');
                }
            );
        } catch (Exception $e) {

            Log::dataLog('oil_account_assign | responseResult | 分配回调异常 | $e:' . strval($e), 'gasAgent-callBack');

            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return TRUE;
    }

    /**
     * 回调函数
     */
    public function responseResult0114()
    {
        $params = \helper::filterParams();
//        $params = array (
//            'result' => 0,
//            'responseResult' => '{"data":[{"cardNo":"****************","msg":"子卡备付金余额等于0","assignAmount":"0.01"},{"cardNo":"****************","msg":"子卡备付金余额等于0","assignAmount":"0.01"}],"result":0,"message":"成功"}',
//            'taskType' => 'zsyPositiveDis',
//            'time' => '2018-10-16 15:41:30',
//            'message' => '成功',
//            'taskId' => 'c68932a12c224843b40428a654219d2b', );

        Log::error('params-:' . var_export($params, TRUE), [], 'reponseResult');

        if (!isset($params['result'])) {
            Log::dataLog('oil_account_assign | responseResult | result 缺失', 'gasAgent-callBack');
            throw new RuntimeException('result 缺失', 3);
        }
        if ($params['result'] === '0' || $params['result'] === 0) {
            $status = 10;
        } elseif ($params['result'] == 8) {
            $status = 8;
        } else {
            $status = -10;
        }

        if ($params['result'] === '6' || $params['result'] === 6 || $params['result'] === '8' || $params['result'] === 8) {
            $responseResult = json_decode($params['responseResult'], TRUE);
            if (isset($responseResult['data']) && $responseResult['data']) {
                $status = 10;
            }
            Log::error('result:6--' . var_export($params, TRUE), [], 'reponseResult_faild6');
        }

        $params['status'] = $status;
        $amountType       = $desc_msg = '';
        switch ($params['taskType']) {
            case 'zsyPositiveDis':
                $amountType = 'assignAmount';
                $desc_msg   = '实际分配';
                break;
            case 'zsyNegativeDis':
                $amountType = 'turnAmount';
                $desc_msg   = '实际圈回';
                break;
            case 'zshCashPositiveDis':
                $amountType = 'assignAmount';
                $desc_msg   = '实际分配';
                break;
            case 'zshCashNegativeDis':
                $amountType = 'turnAmount';
                $desc_msg   = '实际圈回';
                break;
            case 'zshIntegralPositiveDis':
                $amountType = 'assignIntegral';
                $desc_msg   = '实际分配';
                break;
            case 'zshIntegralNegativeDis':
                $amountType = 'turnIntegral';
                $desc_msg   = '实际圈回';
                break;
        }
        $params['desc_msg']   = $desc_msg;
        $params['amountType'] = $amountType;

        //通过任务ID查出assign_id及assign_detail_id
        $assignInfo                  = OilAccountAssignTask::where('taskId', $params['taskId'])->first();
        $assignDetailInfo            = OilAccountAssignTaskDetail::select(Capsule::connection()->raw('GROUP_CONCAT(assign_detail_id) ids'))->where('taskId', $params['taskId'])->first();
        $params['assign_id']         = $assignInfo->assign_id;
        $params['assign_detail_ids'] = explode(',', $assignDetailInfo->ids);

        try {
            $autoAssign = new AutoAssign();
            $autoAssign->updateTaskStatus(//更新任务状态
                function () use ($params) {
                    OilAccountAssignTask::where('taskId', $params['taskId'])->update(['status' => $params['status'], 'message' => $params['result'] . '--' . $params['message']]);
                    OilAccountAssignTaskDetail::where('taskId', $params['taskId'])->update(['status' => $params['status']]);
                }
            )->updateAssignOrderStatus(//更新分配单状态
                function () use ($params) {

                    Log::error(var_export($params, TRUE), [], 'reponseResult');
                    $proxyData = json_decode($params['responseResult'], TRUE);

                    $distributeId = isset($proxyData['distributeId']) && $proxyData['distributeId'] ? $proxyData['distributeId'] : null;

                    $_data = OilAccountAssignDetails::whereIn('id', $params['assign_detail_ids'])->get();

                    if ($_data) {
                        $updateArr = $reSendAssign = [];
                        foreach ($_data as $detail) {
                            $tmp = [];
                            //$_status = -10;
                            $check_assign = -10;
                            $_msg         = "";
                            if ($params['result'] === '0' || $params['result'] === 0 || $params['result'] === '6' || $params['result'] === 6 || $params['result'] === '8' || $params['result'] === 8) {
                                if (isset($proxyData['data']) && $proxyData['data']) {
                                    foreach ($proxyData['data'] as $key => $val) {
                                        if ($detail->vice_no == $val['cardNo']) {
                                            $_msg = $val['msg'] . "," . $params['desc_msg'] . "：" . $val[$params['amountType']];
                                            Log::error('diff' . var_export(bccomp(abs($detail->assign_money) * 100, $val[$params['amountType']] * 100), TRUE), [], 'reponseResult');
                                            if (abs($val[$params['amountType']]) != 0) {
                                                if ($val[$params['amountType']] == -1) {
                                                    $_msg             = $val['msg'] . "," . $params['desc_msg'] . "：失败请核查";
                                                    $params['status'] = -10;
                                                    if (strpos($val['msg'], '实际差值：0.00') !== FALSE) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 0;
                                                        $reSendAssign[]   = $detail->assign_id;
                                                        $_msg             = "分配后余额无变化，请登陆主站核查,并尝试重新下发分配任务";
                                                    } elseif (strpos($val['msg'], '实际差值') !== FALSE) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 0;
                                                        $_msg             = "分配后余额有变化，请登陆主站核查";
                                                    } elseif (strpos($val['msg'], '数据获取失败') !== FALSE) {
                                                        $params['status'] = 10;
                                                        $check_assign     = 0;
                                                        $_msg             = "无预分配订单，请登陆主站核查";
                                                    }
                                                } else {
                                                    //中石化分配
                                                    if (in_array($params['taskType'], ['zshCashPositiveDis', 'zshCashNegativeDis'])) {
                                                        if (bccomp($detail->assign_money * 100, $val[$params['amountType']] * 100) == 0) {
                                                            //$_status = 10;
                                                            $check_assign = 10;
                                                            unset($proxyData['data'][$key]);
                                                        } else {
                                                            //$_status = -10;
                                                            $check_assign = -10;
                                                        }
                                                    } elseif (in_array($params['taskType'], ['zshIntegralPositiveDis', 'zshIntegralNegativeDis'])) {
                                                        //积分分配
                                                        if (bccomp($detail->assign_jifen * 100, $val[$params['amountType']] * 100) == 0) {
                                                            //$_status = 10;
                                                            $check_assign = 10;
                                                            unset($proxyData['data'][$key]);
                                                        } else {
                                                            //$_status = -10;
                                                            $check_assign = -10;
                                                        }
                                                    } elseif (in_array($params['taskType'], ['zsyNegativeDis'])) {
                                                        //中石油圈回
                                                        if (bccomp(abs($detail->assign_money) * 100, $val[$params['amountType']] * 100) == 0) {
                                                            //$_status = 10;
                                                            $check_assign = 10;
                                                            unset($proxyData['data'][$key]);
                                                        } else {
                                                            $params['status'] = -8;
                                                            $check_assign     = -10;
                                                        }
                                                    } else {
                                                        if (bccomp(abs($detail->assign_money) * 100, $val[$params['amountType']] * 100) == 0) {
                                                            //$_status = 10;
                                                            $check_assign = 10;
                                                            unset($proxyData['data'][$key]);
                                                        } else {
                                                            //$_status = -10;
                                                            $check_assign = -10;
                                                        }
                                                    }
                                                }

                                            } else {
                                                $_status          = -10;
                                                $params['status'] = -10;
                                                $check_assign     = -10;
                                            }
                                        }
                                    }
                                } else {
                                    $_msg = $params['result'] . '--' . $params['message'];
                                    if (strpos($params['message'], '需人工校验') !== FALSE) {
                                        $_msg = "需人工校验,请登陆主站核查";
                                    }
                                }
                            } else {
                                $_msg = $params['result'] . '--' . $params['message'];
                            }

                            if (!$detail->callback_time) {
                                $tmp['callback_time']     = \helper::nowTime();
                                $tmp['callback_time_end'] = \helper::nowTime();
                            } else {
                                $tmp['callback_time_end'] = \helper::nowTime();
                            }

                            if ($distributeId) {
                                $tmp['distributeId'] = $distributeId;
                            }

                            $tmp['status']         = $params['status'];
                            $tmp['assign_message'] = $_msg;
                            if (in_array($params['taskType'], ['zsyNegativeDis', 'zsyPositiveDis'])) {
                                $tmp['check_assign'] = $check_assign;
                            }
                            $tmp['where'] = 'id = ' . $detail->id;

                            $updateArr[] = $tmp;

                        }

                        Log::error('$updateArr' . var_export($updateArr, TRUE), [], 'reponseResult');
                        OilAccountAssignDetails::batchEditByPdo($updateArr);

//                        //分配失败-1的这种情况实行重新下发分配动作
//                        if($reSendAssign && in_array($params['taskType'],['zsyPositiveDis'])){
//                            foreach(array_unique($reSendAssign) as $re_send_assign_id){
//                                try{
//                                    $sendCount = Cache::get('reSend'.$re_send_assign_id) ? Cache::get('reSend'.$re_send_assign_id) : 1;
//                                    if($sendCount <= 5){
//                                        OilAccountAssign::autoAssignTask(['assign_id'=>$re_send_assign_id,'key_type'=>'autoAssign']);
//                                        Log::error('reSend:assign_id:'.$re_send_assign_id.'执行次数'.$sendCount.'taskParams'.var_export($params,TRUE),[],'reSendTask');
//                                        Cache::put('reSend'.$re_send_assign_id,$sendCount+1,86400);
//                                    }
//                                }catch (Exception $e){
//                                    Log::error('reSend:Exception:'.strval($e),[],'reSendTask');
//                                }
//                            }
//                        }
                    }

                    $status = OilAccountAssign::getAssignNoStatus($params['assign_id'], FALSE);

                    Log::error(var_export($status, TRUE), [], 'reponseResult');

                    if ($status != 20) {
                        $info = OilAccountAssign::getById(['id' => $params['assign_id']]);
                        if ($info->status != 1) {//未审核时才允许改分配单主状态
                            OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
                            //添加工单日志
                            OilCardViceAppLog::add([
                                'type'             => 2,
                                'app_id'           => $params['assign_id'],
                                'status'           => $status,
                                'status_name'      => AccountAssignStatus::getById($status),
                                'last_operator'    => '系统操作',
                                'last_operator_id' => 1,
                                'createtime'       => \helper::nowTime(),
                                'updatetime'       => \helper::nowTime(),
                            ]);

                            if ($status == -10) {
                                $message = '【自动分配】亲，工单' . $info->no . '自动分配失败，请及时处理哦～';
                                AutoAssign::sendNotify($message);
                            }

                            //自动校验通过审核逻辑
                            if ($status == 10) {
                                //获取分配单下所有校验状态
                                $checkStatus = $this->editCheckAssign($params['assign_id']);

                                //根据校验状态判断是否自动审核
                                //1首先判断总开关配置是否开启
                                $info    = OilAccountAssign::getById(['id' => $params['assign_id']]);
                                $is_open = OilConfigure::getBySysKey('auto_assign_switch');
                                if ($checkStatus && $checkStatus == 10 && intval($is_open) == 2) {
                                    $_POST['id']          = $params['assign_id'];
                                    $_POST['isAutoAudit'] = 1;
                                    try {
                                        $this->auditBy();
                                    } catch (Exception $e) {
                                        Log::error($e->getCode() . '--' . $e->getMessage(), [], 'assignAudit');
                                        $message = '【自动审核】亲，工单' . $info->no . '自动审核失败，请及时处理哦～';
                                        AutoAssign::sendNotify($message);
                                    }
                                }
                            }
                        }
                    }

                    Log::error('oil_account_assign | responseResult | 分配回调完成', [], 'reponseResult');
                }
            );
        } catch (Exception $e) {

            Log::dataLog('oil_account_assign | responseResult | 分配回调异常 | $e:' . strval($e), 'gasAgent-callBack');

            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return TRUE;
    }

    /**
     * 回调函数
     */
    public function responseResultOld()
    {
        //[{"cardNo":"****************","msg":"子卡备付金余额等于0","turnAmount":"0.01"},"cardNo":"****************","msg":"子卡备付金余额等于0","turnAmount":"0.02"}]
        //[{"cardNo":"****************","msg":"子卡备付金余额等于0","turnAmount":"0.02"}]
        $params = \helper::filterParams();
//        $params = array (
//            'result' => 0,
//            'responseResult' => '{"data":[{"cardNo":"****************","msg":"子卡备付金余额等于0","turnAmount":"0.01"},{"cardNo":"****************","msg":"子卡备付金余额等于0","turnAmount":"0.01"}],"result":0,"message":"成功"}',
//            'taskType' => 'zsyDistribute',
//            'time' => '2018-10-16 15:41:30',
//            'message' => '成功',
//            'taskId' => 'c68932a12c224843b40428a654219d2b', );

        Log::error('params-:' . var_export($params, TRUE), [], 'reponseResult');

        if (!isset($params['result'])) {
            Log::dataLog('oil_account_assign | responseResult | result 缺失', 'gasAgent-callBack');
            throw new RuntimeException('result 缺失', 3);
        }
        if ($params['result'] === '0' || $params['result'] === 0) {
            $status = 10;
        } elseif ($params['result'] == 8) {
            $status = 8;
        } else {
            $status = -10;
        }
        $params['status'] = $status;
        //通过任务ID查出assign_id及assign_detail_id
        $assignInfo                  = OilAccountAssignTask::where('taskId', $params['taskId'])->first();
        $assignDetailInfo            = OilAccountAssignTaskDetail::select(Capsule::connection()->raw('GROUP_CONCAT(assign_detail_id) ids'))->where('taskId', $params['taskId'])->first();
        $params['assign_id']         = $assignInfo->assign_id;
        $params['assign_detail_ids'] = explode(',', $assignDetailInfo->ids);

        if ($params['taskType'] == 'zsyDistribute') {
            //中石油的圈回特殊如果分配的和turnAmount不一致
            try {
                $autoAssign = new AutoAssign();
                $autoAssign->updateTaskStatus(//更新任务状态
                    function () use ($params) {
                        OilAccountAssignTask::where('taskId', $params['taskId'])->update(['status' => $params['status'], 'message' => $params['result'] . '--' . $params['message']]);
                        OilAccountAssignTaskDetail::where('taskId', $params['taskId'])->update(['status' => $params['status']]);
                    }
                )->updateAssignOrderStatus(//更新分配单状态
                    function () use ($params) {

                        Log::error(var_export($params, TRUE), [], 'debugAssignCallBack');

                        Log::error(var_export([
                            'status'         => $params['status'],
                            'assign_message' => $params['result'] . '--' . $params['message'],
                            'callback_time'  => \helper::nowTime()
                        ], TRUE), [], 'debugAssignCallBack');

                        $proxyData = json_decode($params['responseResult']);

                        $_data = OilAccountAssignDetails::whereIn('id', $params['assign_detail_ids'])->get();

                        if ($_data) {
                            $updateArr = [];
                            foreach ($_data as $detail) {
                                $tmp          = [];
                                $_status      = -10;
                                $check_assign = -10;
                                $_msg         = "";
                                foreach ($proxyData->data as $key => $val) {
                                    if ($detail->vice_no == $val->cardNo) {
                                        $_msg = $val->msg . ",实际圈回：" . $val->turnAmount;
                                        Log::error('diff' . var_export(bccomp(abs($detail->assign_money) * 100, $val->turnAmount * 100), TRUE), [], 'reponseResult');
                                        if ($val->turnAmount > 0) {
                                            if (bccomp(abs($detail->assign_money) * 100, $val->turnAmount * 100) == 0) {
                                                $_status      = 10;
                                                $check_assign = 10;
                                                unset($proxyData->data[$key]);
                                            } else {
                                                $_status      = -8;
                                                $check_assign = -10;
                                            }
                                        } else {
                                            $_status      = -10;
                                            $check_assign = -10;
                                        }

                                    }
                                }

                                if (!$detail->callback_time) {
                                    $tmp['callback_time']     = \helper::nowTime();
                                    $tmp['callback_time_end'] = \helper::nowTime();
                                } else {
                                    $tmp['callback_time_end'] = \helper::nowTime();
                                }

                                $tmp['status']         = $_status;
                                $tmp['assign_message'] = $_msg;
                                $tmp['check_assign']   = $check_assign;
                                $tmp['where']          = 'id = ' . $detail->id;

                                $updateArr[] = $tmp;
                            }
                            Log::error('$updateArr' . var_export($updateArr, TRUE), [], 'reponseResult');
                            OilAccountAssignDetails::batchEditByPdo($updateArr);
                        }

                        Log::error(var_export($_data, TRUE), [], 'debugAssignCallBack');

                        $status = OilAccountAssign::getAssignNoStatus($params['assign_id'], FALSE);

                        Log::error(var_export($status, TRUE), [], 'debugAssignCallBack');

                        if ($status != 20) {
                            $info = OilAccountAssign::getById(['id' => $params['assign_id']]);
                            if ($info->status != 1) {//未审核时才允许改分配单主状态
                                OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
                                //添加工单日志
                                OilCardViceAppLog::add([
                                    'type'             => 2,
                                    'app_id'           => $params['assign_id'],
                                    'status'           => $status,
                                    'status_name'      => AccountAssignStatus::getById($status),
                                    'last_operator'    => '系统操作',
                                    'last_operator_id' => 1,
                                    'createtime'       => \helper::nowTime(),
                                    'updatetime'       => \helper::nowTime(),
                                ]);

                                if ($status == -10) {
                                    $message = '【自动分配】亲，工单' . $info->no . '自动分配失败，请及时处理哦～';
                                    AutoAssign::sendNotify($message);
                                }

                                //自动校验通过审核逻辑
                                if ($status == 10) {
                                    //获取分配单下所有校验状态
                                    $checkStatus = $this->editCheckAssign($params['assign_id']);

                                    //根据校验状态判断是否自动审核
                                    //1首先判断总开关配置是否开启
                                    $info    = OilAccountAssign::getById(['id' => $params['assign_id']]);
                                    $is_open = OilConfigure::getBySysKey('auto_assign_switch');
                                    if ($checkStatus && $checkStatus == 10 && intval($is_open) == 2) {
                                        $_POST['id']          = $params['assign_id'];
                                        $_POST['isAutoAudit'] = 1;
                                        try {
                                            $this->auditBy();
                                        } catch (Exception $e) {
                                            Log::error($e->getCode() . '--' . $e->getMessage(), [], 'assignAudit');
                                            $message = '【自动审核】亲，工单' . $info->no . '自动审核失败，请及时处理哦～';
                                            AutoAssign::sendNotify($message);
                                        }
                                    }
                                }
                            }
                        }

                        Log::dataLog('oil_account_assign | responseResult | 分配回调完成', 'gasAgent-callBack');
                    }
                );
            } catch (Exception $e) {

                Log::dataLog('oil_account_assign | responseResult | 分配回调异常 | $e:' . strval($e), 'gasAgent-callBack');

                throw new RuntimeException($e->getMessage(), $e->getCode());
            }
        } else {
            try {
                $autoAssign = new AutoAssign();
                $autoAssign->updateTaskStatus(//更新任务状态
                    function () use ($params) {
                        OilAccountAssignTask::where('taskId', $params['taskId'])->update(['status' => $params['status'], 'message' => $params['result'] . '--' . $params['message']]);
                        OilAccountAssignTaskDetail::where('taskId', $params['taskId'])->update(['status' => $params['status']]);
                    }
                )->updateAssignOrderStatus(//更新分配单状态
                    function () use ($params) {

                        Log::error(var_export($params, TRUE), [], 'debugAssignCallBack');

                        Log::error(var_export([
                            'status'         => $params['status'],
                            'assign_message' => $params['result'] . '--' . $params['message'],
                            'callback_time'  => \helper::nowTime()
                        ], TRUE), [], 'debugAssignCallBack');

                        $_data = OilAccountAssignDetails::whereIn('id', $params['assign_detail_ids'])->get();
                        if ($_data) {
                            $updateArr = [];
                            foreach ($_data as $detail) {
                                $tmp = [];
                                if (!$detail->callback_time) {
                                    $tmp['callback_time']     = \helper::nowTime();
                                    $tmp['callback_time_end'] = \helper::nowTime();
                                } else {
                                    $tmp['callback_time_end'] = \helper::nowTime();
                                }
                                $tmp['status']         = $params['status'];
                                $tmp['assign_message'] = $params['result'] . '--' . $params['message'];
                                $tmp['where']          = 'id = ' . $detail->id;

                                $updateArr[] = $tmp;
                            }
                            OilAccountAssignDetails::batchEditByPdo($updateArr);
                        }

                        Log::error(var_export($_data, TRUE), [], 'debugAssignCallBack');

                        $status = OilAccountAssign::getAssignNoStatus($params['assign_id'], FALSE);

                        Log::error(var_export($status, TRUE), [], 'debugAssignCallBack');

                        if ($status != 20) {
                            $info = OilAccountAssign::getById(['id' => $params['assign_id']]);
                            if ($info->status != 1) {//未审核时才允许改分配单主状态
                                OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
                                //添加工单日志
                                OilCardViceAppLog::add([
                                    'type'             => 2,
                                    'app_id'           => $params['assign_id'],
                                    'status'           => $status,
                                    'status_name'      => AccountAssignStatus::getById($status),
                                    'last_operator'    => '系统操作',
                                    'last_operator_id' => 1,
                                    'createtime'       => \helper::nowTime(),
                                    'updatetime'       => \helper::nowTime(),
                                ]);

                                if ($status == -10) {
                                    $message = '【自动分配】亲，工单' . $info->no . '自动分配失败，请及时处理哦～';
                                    AutoAssign::sendNotify($message);
                                }

                            }
                        }

                        Log::dataLog('oil_account_assign | responseResult | 分配回调完成', 'gasAgent-callBack');
                    }
                );
            } catch (Exception $e) {

                Log::dataLog('oil_account_assign | responseResult | 分配回调异常 | $e:' . strval($e), 'gasAgent-callBack');

                throw new RuntimeException($e->getMessage(), $e->getCode());
            }

        }

        return TRUE;
    }

    /**
     * 回调函数
     */
    public function responseResultBak()
    {
        $params = \helper::filterParams();

        Log::dataLog('args----------' . var_export($params, TRUE), 'reponseResult');

        if (!isset($params['result'])) {
            Log::dataLog('oil_account_assign | responseResult | result 缺失', 'gasAgent-callBack');
            throw new RuntimeException('result 缺失', 3);
        }
        if ($params['result'] === '0' || $params['result'] === 0) {
            $status = 10;
        } elseif ($params['result'] == 8) {
            $status = 8;
        } else {
            $status = -10;
        }
        $params['status'] = $status;
        //通过任务ID查出assign_id及assign_detail_id
        $assignInfo                  = OilAccountAssignTask::where('taskId', $params['taskId'])->first();
        $assignDetailInfo            = OilAccountAssignTaskDetail::select(Capsule::connection()->raw('GROUP_CONCAT(assign_detail_id) ids'))->where('taskId', $params['taskId'])->first();
        $params['assign_id']         = $assignInfo->assign_id;
        $params['assign_detail_ids'] = explode(',', $assignDetailInfo->ids);

        try {
            $autoAssign = new AutoAssign();
            $autoAssign->updateTaskStatus(//更新任务状态
                function () use ($params) {
                    OilAccountAssignTask::where('taskId', $params['taskId'])->update(['status' => $params['status'], 'message' => $params['result'] . '--' . $params['message']]);
                    OilAccountAssignTaskDetail::where('taskId', $params['taskId'])->update(['status' => $params['status']]);
                }
            )->updateAssignOrderStatus(//更新分配单状态
                function () use ($params) {

                    Log::error(var_export($params, TRUE), [], 'debugAssignCallBack');

                    Log::error(var_export([
                        'status'         => $params['status'],
                        'assign_message' => $params['result'] . '--' . $params['message'],
                        'callback_time'  => \helper::nowTime()
                    ], TRUE), [], 'debugAssignCallBack');

                    $_data = OilAccountAssignDetails::whereIn('id', $params['assign_detail_ids'])->update([
                        'status'         => $params['status'],
                        'assign_message' => $params['result'] . '--' . $params['message'],
                        'callback_time'  => \helper::nowTime()
                    ]);

                    Log::error(var_export($_data, TRUE), [], 'debugAssignCallBack');

                    $status = OilAccountAssign::getAssignNoStatus($params['assign_id'], FALSE);

                    Log::error(var_export($status, TRUE), [], 'debugAssignCallBack');

                    if ($status != 20) {
                        $info = OilAccountAssign::getById(['id' => $params['assign_id']]);
                        if ($info->status != 1) {//未审核时才允许改分配单主状态
                            OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
                            //添加工单日志
                            OilCardViceAppLog::add([
                                'type'             => 2,
                                'app_id'           => $params['assign_id'],
                                'status'           => $status,
                                'status_name'      => AccountAssignStatus::getById($status),
                                'last_operator'    => '系统操作',
                                'last_operator_id' => 1,
                                'createtime'       => \helper::nowTime(),
                                'updatetime'       => \helper::nowTime(),
                            ]);

                            if ($status == -10) {
                                $message = '【自动分配】亲，工单' . $info->no . '自动分配失败，请及时处理哦～';
                                AutoAssign::sendNotify($message);
                            }

                            /********************推送到易维系统************************/
//                            $assignInfo = OilAccountAssign::getById(['id' => $params['assign_id']]);
//                            $pms['tb_no'] = $assignInfo->no;
//                            $pms['ew_status'] = 'open';
//                            $pms['tb_name'] = 'oil_account_assign';
//                            $pms['content'] = AccountAssignStatus::getById($status);//'自动分配状态回写';
//                            (new EWei)->editWorkOrder($pms);
                        }
                    }

                    Log::dataLog('oil_account_assign | responseResult | 分配回调完成', 'gasAgent-callBack');
                }
            );
        } catch (Exception $e) {

            Log::dataLog('oil_account_assign | responseResult | 分配回调异常 | $e:' . strval($e), 'gasAgent-callBack');

            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return TRUE;
    }

    public function tempActionQuery()
    {
        $data = OilAccountAssign::where('apply_time', '<', '2018-11-09 17:00:00')->where('status', 20)->get();
        if ($data) {
            foreach ($data as $val) {
                try {
                    $_POST['assign_id'] = $val->id;
                    echo $this->assignRealTimeQuery2();
                } catch (Exception $e) {

                }

                sleep(2);
            }
        }
        echo "finish";
    }

    /**
     * 分配实时查询
     */
    public function assignRealTimeQuery2()
    {
        $params = \helper::filterParams();

        \helper::argumentCheck(['assign_id'], $params);
        $assignIinfo = OilAccountAssign::getByIdLock(['id' => $params['assign_id']]);
        if ($assignIinfo) {
            if (in_array($assignIinfo->status, [0, 1, 15, 10, 8])) {
                throw new RuntimeException('当前状态下无需点击分配实时查询', 2);
            }
        } else {
            throw new RuntimeException('无此分配单信息', 2);
        }
        //1，获取此分配单无taskid的和无回调的任务根据流水号得到任务的执行状态
        $autoAssign = new AutoAssign();
        $taskState  = $autoAssign::taskStageSer($params['assign_id']);

        //2，修改任务及任务明细状态更新分配单状态
        if ($taskState) {
            try {
                foreach ($taskState as $key => $val) {
                    $autoAssign->updateTaskStatus(//更新任务状态
                        function () use ($params, $key, $val) {
                            $taskDetailsArr = [];
                            if (isset($val['taskId'])) {
                                $taskDetailsArr = [
                                    'status' => $val['status'],
                                    'taskId' => $val['taskId'],
                                ];
                                $udata          = ['status' => $val['status'], 'message' => $val['status'] . '--' . $val['message'], 'taskId' => $val['taskId']];
                            } else {
                                $taskDetailsArr = [
                                    'status' => $val['status'],
                                ];
                                $udata          = ['status' => $val['status'], 'message' => $val['status'] . '--' . $val['message']];
                            }

                            OilAccountAssignTask::where('id', $key)->update($udata);
                            OilAccountAssignTaskDetail::where('task_id', $key)->update($taskDetailsArr);
                        }
                    )->updateAssignOrderStatus(//更新分配单状态
                        function () use ($params, $key, $val) {
                            Log::info('$key--' . var_export($key, TRUE), [], 'taskStageSer');
                            $assign_detail_ids = OilAccountAssignTaskDetail::where('task_id', $key)->pluck('assign_detail_id');//此时的task_id是task表的主键关联id
                            Log::info('$assign_detail_ids--' . var_export($assign_detail_ids, TRUE), [], 'taskStageSer');
                            OilAccountAssignDetails::whereIn('id', $assign_detail_ids)->update([
                                'status'         => $val['status'] == 0 ? 20 : $val['status'],
                                'assign_message' => 'q-' . $val['status'] . '--' . $val['message'], //前缀q代表是分配实时查询影响的分配状态
                                'callback_time'  => \helper::nowTime()
                            ]);

                            $status = OilAccountAssign::getAssignNoStatus($params['assign_id'], FALSE);

                            if ($status != 20) {
                                $info = OilAccountAssign::getById(['id' => $params['assign_id']]);
                                if ($info->status != 1) {//未审核时才允许改分配单主状态
                                    OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
                                    //添加工单日志
                                    OilCardViceAppLog::add([
                                        'type'        => 2,
                                        'app_id'      => $params['assign_id'],
                                        'status'      => $status,
                                        'status_name' => AccountAssignStatus::getById($status),
                                        'createtime'  => \helper::nowTime(),
                                        'updatetime'  => \helper::nowTime(),
                                    ]);

                                    /********************推送到易维系统************************/
                                    $assignInfo       = OilAccountAssign::getById(['id' => $params['assign_id']]);
                                    $pms['tb_no']     = $assignInfo->no;
                                    $pms['ew_status'] = 'open';
                                    $pms['tb_name']   = 'oil_account_assign';
                                    $pms['content']   = AccountAssignStatus::getById($status);//'自动分配状态回写';
                                    (new EWei)->editWorkOrder($pms);
                                }
                            }

                            Log::dataLog('oil_account_assign | responseResult | 分配回调完成', 'gasAgent-callBack');
                        }
                    );
                }
            } catch (Exception $e) {

                Log::dataLog('oil_account_assign | responseResult | 分配回调异常 | $e:' . strval($e), 'gasAgent-callBack');

                throw new RuntimeException($e->getMessage(), $e->getCode());
            }
        }

        return $params['assign_id'];
    }

    /**
     * 分配实时查询
     */
    public function assignRealTimeQuery()
    {
        $params = \helper::filterParams();

        \helper::argumentCheck(['assign_id'], $params);
        $assignIinfo = OilAccountAssign::getByIdLock(['id' => $params['assign_id']]);
        if ($assignIinfo) {
            if (in_array($assignIinfo->status, [0, 1, 15, 10, 8])) {
                throw new RuntimeException('当前状态下无需点击分配实时查询', 2);
            }
        } else {
            throw new RuntimeException('无此分配单信息', 2);
        }
        //1，获取此分配单无taskid的和无回调的任务根据流水号得到任务的执行状态
        $autoAssign = new AutoAssign();
        $taskState  = $autoAssign::taskStageSer($params['assign_id']);

        //2，修改任务及任务明细状态更新分配单状态
        if ($taskState) {
            try {
                foreach ($taskState as $key => $val) {
                    $autoAssign->updateTaskStatus(//更新任务状态
                        function () use ($params, $key, $val) {
                            $taskDetailsArr = [];
                            if (isset($val['taskId'])) {
                                $taskDetailsArr = [
                                    'status' => $val['status'],
                                    'taskId' => $val['taskId'],
                                ];
                                $udata          = ['status' => $val['status'], 'message' => $val['status'] . '--' . $val['message'], 'taskId' => $val['taskId']];
                            } else {
                                $taskDetailsArr = [
                                    'status' => $val['status'],
                                ];
                                $udata          = ['status' => $val['status'], 'message' => $val['status'] . '--' . $val['message']];
                            }

                            OilAccountAssignTask::where('id', $key)->update($udata);
                            OilAccountAssignTaskDetail::where('task_id', $key)->update($taskDetailsArr);
                        }
                    )->updateAssignOrderStatus(//更新分配单状态
                        function () use ($params, $key, $val) {
                            Log::info('$key--' . var_export($key, TRUE), [], 'taskStageSer');
                            $assign_detail_ids = OilAccountAssignTaskDetail::where('task_id', $key)->pluck('assign_detail_id');//此时的task_id是task表的主键关联id
                            Log::info('$assign_detail_ids--' . var_export($assign_detail_ids, TRUE), [], 'taskStageSer');
                            OilAccountAssignDetails::whereIn('id', $assign_detail_ids)->update([
                                'status'         => $val['status'] == 0 ? 20 : $val['status'],
                                'assign_message' => 'q-' . $val['status'] . '--' . $val['message'], //前缀q代表是分配实时查询影响的分配状态
                                'callback_time'  => \helper::nowTime()
                            ]);

                            $status = OilAccountAssign::getAssignNoStatus($params['assign_id'], FALSE);

                            if ($status != 20) {
                                $info = OilAccountAssign::getById(['id' => $params['assign_id']]);
                                if ($info->status != 1) {//未审核时才允许改分配单主状态
                                    OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
                                    //添加工单日志
                                    OilCardViceAppLog::add([
                                        'type'        => 2,
                                        'app_id'      => $params['assign_id'],
                                        'status'      => $status,
                                        'status_name' => AccountAssignStatus::getById($status),
                                        'createtime'  => \helper::nowTime(),
                                        'updatetime'  => \helper::nowTime(),
                                    ]);

                                    /********************推送到易维系统************************/
                                    $assignInfo       = OilAccountAssign::getById(['id' => $params['assign_id']]);
                                    $pms['tb_no']     = $assignInfo->no;
                                    $pms['ew_status'] = 'open';
                                    $pms['tb_name']   = 'oil_account_assign';
                                    $pms['content']   = AccountAssignStatus::getById($status);//'自动分配状态回写';
                                    (new EWei)->editWorkOrder($pms);
                                }
                            }

                            Log::dataLog('oil_account_assign | responseResult | 分配回调完成', 'gasAgent-callBack');
                        }
                    );
                }
            } catch (Exception $e) {

                Log::dataLog('oil_account_assign | responseResult | 分配回调异常 | $e:' . strval($e), 'gasAgent-callBack');

                throw new RuntimeException($e->getMessage(), $e->getCode());
            }
        }

        Response::json(null, 0, '实时查询状态成功');
    }

    public function res()
    {
        EWei::getEWeiInfoForTest();
    }

    /**
     * 删除超时易维工单
     *
     * <AUTHOR>
     */
    public function deleteTimeOutEWeiWorkOrder()
    {
        EWei::deleteTimeOutWorkOrder();
    }

    ////////////////////////分配记录同步接口/////////////////////////
    public function synGspAccountAssign()
    {
        $params = \helper::filterParams();

        //验证
        \helper::argumentCheck(['timeGe', 'limit', 'page'], $params);

        $data = OilAccountAssign::getList($params);

        Response::json($data);
    }

    /**
     * 数据删除
     *
     * <AUTHOR>
     * @since  2015/09/29
     */
    public function delete()
    {
        $params = \helper::filterParams();
        $id     = intval($params['id']);

        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            //审核之后的单号不能重复审核
            $data = OilAccountAssign::getByIdByLock(['id' => $id]);

            if (!$data) {
                throw new RuntimeException('该单号已删除', 2);
            }

            if ($data->provider_flag == 2) {
                //撬装充值卡的审核
                AccountAssign::delAssign($id);
            } else {
                //校验：已审核的不能进行删除
                if ($data->status == 1) {
                    throw new RuntimeException('该单号已审核，不能删除', 2);
                } elseif ($data->status == 20) {
                    throw new RuntimeException('该单号正在主站分配中，不能删除', 2);
                }

                //校验：若分配明细中有自动分配中和自动分配成功的单子，则不能删除
                $details = OilAccountAssignDetails::checkAssignDetail(['assign_id' => $id]);
            }

            /***************************
             * START处理逻辑
             ***************************/

            //push至G7Pay
            (new AssignService())->doFailById($data->id);

            //删除加入回收站
            $data->details          = $details;
            $recycle                = [];
            $recycle['table_name']  = 'oil_account_assign';
            $recycle['pk']          = $data->id;
            $recycle['org_id']      = $data->org_id;
            $recycle['no']          = $data->no;
            $recycle['sn']          = $data->sn;
            $recycle['data']        = json_encode($data);
            $recycle['operator_id'] = $this->app->myAdmin->id;
            $recycle['createtime']  = \helper::nowTime();
            $recycles               = OilRecycle::add($recycle);
            if ($recycles) {
                OilAccountAssignDetails::removeByAssignId(['assignIds' => [$id]]);
                OilAccountAssign::remove(['ids' => [$id]]);
            }

            //事务提交
            Capsule::connection()->commit();
        } catch (Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            throw new RuntimeException('删除失败:' . $e->getMessage(), 2);
        }

        //push至Gos系统
        AccountAssignToGos::remove([$id]);

        Response::json(null, 0, '删除成功');
    }

    /**
     * 根据单号数据删除
     *
     * <AUTHOR>
     * @since  2015/09/29
     */
    public function deleteByNo()
    {
        $params = \helper::filterParams();
        $no     = trim($params['no']);

        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            //审核之后的单号不能重复审核
            $data = OilAccountAssign::getByNoForLock($no);

            if (!$data) {
                throw new RuntimeException('该单号已删除', 2);
            }

            if ($data->provider_flag == 2) {
                //撬装充值卡的审核
                AccountAssign::delAssign($data->id);
            } else {
                //校验：已审核的不能进行删除
                if ($data->status == 1) {
                    throw new RuntimeException('该单号已审核，不能删除', 2);
                } elseif ($data->status == 20) {
                    throw new RuntimeException('该单号正在主站分配中，不能删除', 2);
                }

                //校验：若分配明细中有自动分配中和自动分配成功的单子，则不能删除
                $details = OilAccountAssignDetails::getAssignDetailByAssignId(['assign_id' => $data->id]);
            }

            /***************************
             * START处理逻辑
             ***************************/

            //push至G7Pay
            (new AssignService())->doFailById($data->id);

            //删除加入回收站
            $data->details          = $details;
            $recycle                = [];
            $recycle['table_name']  = 'oil_account_assign';
            $recycle['pk']          = $data->id;
            $recycle['org_id']      = $data->org_id;
            $recycle['no']          = $data->no;
            $recycle['sn']          = $data->sn;
            $recycle['data']        = json_encode($data);
            $recycle['operator_id'] = $this->app->myAdmin->id;
            $recycle['createtime']  = \helper::nowTime();
            $recycles               = OilRecycle::add($recycle);
            if ($recycles) {
                OilAccountAssignDetails::removeByAssignId(['assignIds' => [$data->id]]);
                OilAccountAssign::remove(['ids' => [$data->id]]);
            }

            //事务提交
            Capsule::connection()->commit();
        } catch (Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            throw new RuntimeException('删除失败:' . $e->getMessage(), 2);
        }

        //push至Gos系统
        AccountAssignToGos::remove([$data->id]);

        Response::json(null, 0, '删除成功');
    }

    /*
     * 圈回删除
     */
    public function deleteForQH()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['no'], $params);

        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            //审核之后的单号不能重复审核
            $data = OilAccountAssign::getByNoForLock($params['no']);

            if (!$data) {
                throw new RuntimeException('该单号已删除', 2);
            }

            //校验：若分配明细中有自动分配中和自动分配成功的单子，则不能删除
            $details = OilAccountAssignDetails::getAssignDetailByAssignId(['assign_id' => $data->id]);
            /***************************
             * START处理逻辑
             ***************************/

            //删除加入回收站
            $data->details          = $details;
            $recycle                = [];
            $recycle['table_name']  = 'oil_account_assign';
            $recycle['pk']          = $data->id;
            $recycle['org_id']      = $data->org_id;
            $recycle['no']          = $data->no;
            $recycle['sn']          = $data->sn;
            $recycle['data']        = json_encode($data);
            $recycle['operator_id'] = $this->app->myAdmin->id;
            $recycle['createtime']  = \helper::nowTime();
            $recycles               = OilRecycle::add($recycle);
            if ($recycles) {
                OilAccountAssignDetails::removeByAssignId(['assignIds' => [$data->id]]);
                OilAccountAssign::remove(['ids' => [$data->id]]);
            }

            //事务提交
            Capsule::connection()->commit();
        } catch (Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            throw new RuntimeException('删除失败:' . $e->getMessage(), 2);
        }


        Response::json(null, 0, '删除成功');
    }

    /**
     * @title 针对授信分配异常的单子进行删除操作
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function deleteCredit()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['no'], $params);

        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            //审核之后的单号不能重复审核
            $data = OilAccountAssign::getByNoForLock($params['no']);

            if (!$data) {
                throw new RuntimeException('该单号已删除', 2);
            }

            //校验：若分配明细中有自动分配中和自动分配成功的单子，则不能删除
            $details = OilAccountAssignDetails::getAssignDetailByAssignId(['assign_id' => $data->id]);
            /***************************
             * START处理逻辑
             ***************************/

            //push至G7Pay
            (new AssignService())->doFailById($data->id);

            //删除加入回收站
            $data->details          = $details;
            $recycle                = [];
            $recycle['table_name']  = 'oil_account_assign';
            $recycle['pk']          = $data->id;
            $recycle['org_id']      = $data->org_id;
            $recycle['no']          = $data->no;
            $recycle['sn']          = $data->sn;
            $recycle['data']        = json_encode($data);
            $recycle['operator_id'] = $this->app->myAdmin->id;
            $recycle['createtime']  = \helper::nowTime();
            $recycles               = OilRecycle::add($recycle);
            if ($recycles) {
                OilAccountAssignDetails::removeByAssignId(['assignIds' => [$data->id]]);
                OilAccountAssign::remove(['ids' => [$data->id]]);
            }

            //事务提交
            Capsule::connection()->commit();
        } catch (Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            throw new RuntimeException('删除失败:' . $e->getMessage(), 2);
        }

        //push至Gos系统
        AccountAssignToGos::remove([$data->id]);

        Response::json(null, 0, '删除成功');
    }

    /**
     * 撬装分配数据删除
     *
     * <AUTHOR>
     * @since  2015/09/29
     */
    public function gasDistributionDelete()
    {
        $params = \helper::filterParams();
        $id     = intval($params['id']);
        $where  = " assign_type = 30";
        $where  .= " AND id = $id";
        $data   = $this->oil_account_assign->getInfoById($where, '*', 'oil_account_assign');

        if (empty($data)) {
            echo json_encode(['success' => TRUE, 'msg' => '该单号已删除']);
            exit;
        }
        try {
            //开启事务
            $this->dbh->beginTransaction();
            //删除加入回收站
            $recycle               = [];
            $recycle['table_name'] = 'oil_account_assign';
            $recycle['pk']         = $data[0]->id;
            $recycle['org_id']     = $data[0]->org_id;
            $recycle['no']         = $data[0]->no;
            $recycle['data']       = json_encode($data);
            $recycle['createtime'] = \helper::nowTime();
            $recycles              = $this->oil_account_assign->add($recycle, 'oil_recycle');;
            if ($recycles) {
                $this->oil_account_assign->destroy(['assign_id' => $id], 'oil_account_assign_details');
                $this->oil_account_assign->destroy(['id' => $id]);
            }

            $info = ['success' => TRUE, 'msg' => '数据删除成功'];

            //事务提交
            $this->dbh->commit();
            echo json_encode($info);
        } catch (Exception $e) {
            //事务回滚
            $this->dbh->rollBack();
            echo json_encode(['success' => FALSE, 'msg' => $e->getCode() == '1' ? $e->getMessage() : '删除失败']);
        }
    }

    /**
     * 撬装创建分配工单
     */
    public function gasDistributionAdd()
    {
        $params = \helper::filterParams();

        \helper::argumentCheck(['no', 'orgcode', 'money', 'creater', 'createtime', 'changetime', 'checkman', 'list'], $params);
        if (isset($params['orgcode']) && !empty($params['orgcode'])) {
            $orginfo = OilOrg::getByOrgcode($params['orgcode']);
            if (empty($orginfo) || !$orginfo->id) {
                throw new RuntimeException('机构不存在', 2);
            } else {
                $params['org_id']   = $orginfo->id;
                $params['org_name'] = $orginfo->org_name;
            }
        }

        Capsule::connection()->beginTransaction();
        try {
            $checkNoExist = OilAccountAssign::getByNoForLock($params['no']);
            if ($checkNoExist) {
                Log::info('RuntimeException', [$checkNoExist], 'gasDistributionAdd');
                throw new RuntimeException('单号已存在', 2);
            }
            $addArr         = [
                'no'              => $params['no'],
                'no_type'         => 'FP',
                'org_id'          => $params['org_id'],
                'org_name'        => $params['org_name'],
                'money_total'     => $params['money'],
                'assign_num'      => count($params['list']),
                'account_type'    => AccountType::GAS,
                'account_no'      => $orginfo->orgcode,
                'data_from'       => 1,
                'apply_time'      => $params['createtime'],
                'createtime'      => \helper::nowTime(),
                'last_operator'   => $params['checkman'],
                'complete_person' => $params['checkman'],
                'updatetime'      => \helper::nowTime(),
                'provider_flag'   => 2,
                'status'          => 1,
                'assign_type'     => Fuel\Defines\AccountAssignStatus::GAS,
            ];
            $addRegularInfo = OilAccountAssign::add($addArr);
            if (!$addRegularInfo)
                throw new RuntimeException('添加失败', 2);

            //添加工单日志
            OilCardViceAppLog::add([
                'type'             => 2,
                'app_id'           => $addRegularInfo->id,
                'status'           => $addArr['status'],
                'status_name'      => 1,
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => \helper::nowTime(),
                'updatetime'       => \helper::nowTime(),
            ]);
            foreach ($params['list'] as $value) {
                $value = (array)$value;
                if (isset($value['card_no']) && !empty($value['card_no'])) {
                    $ius            = [];
                    $ius['vice_no'] = $value['card_no'];
                    $ius['orgcode'] = substr($params['orgcode'], 0, 6);

                    $getCardInfo = OilCardVice::getCardInfo($ius);
                    if (!$getCardInfo) {
                        throw new RuntimeException('子账户账号不存在', 2);
                    }
                } else {
                    throw new RuntimeException('子账户账号不能为空', 2);
                }
                if (!isset($value['money']) || empty($value['money'])) {
                    throw new RuntimeException('分配金额不能为空', 2);
                }
                $detailInfo = [
                    'assign_id'       => $addRegularInfo->id,
                    'org_id'          => $getCardInfo->org_id,
                    'org_name'        => $getCardInfo->org_name,
                    'vice_id'         => $getCardInfo->id,
                    'assign_money'    => $value['money'],
                    'truck_no'        => $getCardInfo->truck_no,
                    'truck_no_custom' => isset($value['truck_no']) && !empty($vale['truck_no']) ? $value['truck_no'] : '',
                    'status'          => 1,
                    'createtime'      => \helper::nowTime(),
                    'updatetime'      => \helper::nowTime(),
                ];
                OilAccountAssignDetails::add($detailInfo);
            }
            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollback();
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json($addRegularInfo->id, 0, '添加成功');

    }

    /*
     * 批量审核
     * author tim
     */
    public function batchAudit()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['ids'], $params);

        $idArr = explode(',', $params['ids']);

        $statusArr = OilAccountAssign::whereIn('id', $idArr)->pluck('status')->toArray();

        if ($statusArr) {
            foreach ($statusArr as $status) {
                if ($status != 10) {
                    throw new RuntimeException('非主站分配完成状态禁止使用批量通过', 2);
                }
            }
        }

        if ($idArr) {
            $errorNum = 0;
            foreach ($idArr as $assign_id) {
                try {
                    $this->auditBy($assign_id);
                } catch (Exception $e) {
                    $errorNum++;
                }
            }

            $data = "总共" . count($idArr) . '条，失败' . $errorNum . '条';
        } else {
            throw new RuntimeException('参数错误', 2);
        }

        Response::json($data, 0, $data);
    }


    /**
     * @title   分配单审核
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function auditBy($assign_id = null)
    {
        global $timeStart;
        if ($assign_id) {
            $params['id'] = $assign_id;
            Log::error("审核分配申请单,auditBy,Params" . var_export($params, TRUE), [], "assignAuditBy_");
        } else {
            $params = \helper::filterParams();
        }
        \helper::argumentCheck(['id'], $params);
        Log::error("111", [$params], "catTime");
        global $app;
        if (!isset($app->myAdmin) || $assign_id > 0) {
            $app->myAdmin            = new stdClass();
            $app->myAdmin->id        = 8888;
            $app->myAdmin->true_name = '系统自动';
        }
        //$startTime = _getTime();
        $status = FALSE;
        Capsule::connection()->beginTransaction();
        try {
            $accountAssignInfo = OilAccountAssign::getByIdByLock(['id' => $params['id']]);

//            $isAudit = false;
//            if(isset($accountAssignInfo->org_id) && !empty($accountAssignInfo->org_id) && bccomp($accountAssignInfo->money_total,0,2) >= 0){
//                //用户审核来源
//                if((isset($params['isAutoAudit']) && $params['isAutoAudit'] == 1 ) || $accountAssignInfo->data_from == 1 ){
//                    $isAudit = false;
//                }else{
//                    $isAudit = AccountAssign::checkAssignAuth($accountAssignInfo->org_id,false,true);
//                }
//            }
//            if($isAudit){
//                throw new RuntimeException('该机构为客户自审核模式', 2);
//            }
//            Log::error("222", [], "catTime");
            if (!$accountAssignInfo) {
                throw new RuntimeException('此工单不存在', 2);
            }

            if ($accountAssignInfo->status == -1) {
                throw new RuntimeException('该单号已驳回,不能通过审核!', 2);
            }

            if ($accountAssignInfo->status == 1) {
                throw new RuntimeException('该单号通过,不能重复通过审核!', 2);
            }

            /*($accountAssignInfo->assign_type == CardFrom::CUSTOMER_CARD &&
                !in_array($accountAssignInfo->account_type, [10, 20, 30]) )*/
            if ($accountAssignInfo->no_type == AccountAssignStatus::MAIN_TURN_NO_TYPE) {
                $status = FALSE;
                OilAccountAssign::edit([
                    'id'               => $params['id'],
                    "status"           => 1,
                    "last_operator_id" => $app->myAdmin->id,
                    "last_operator"    => $app->myAdmin->true_name,
                    'complete_person'  => $app->myAdmin->true_name,//完成人
                    'complete_time'    => \helper::nowTime(),//完成时间
                    "updatetime"       => \helper::nowTime(),
                ]);

            } else {
                //之前的条件不符合
                if ($accountAssignInfo->account_type == AccountType::NONE) {
                    $updateCardArr = $this->handleCustomerCardAction($accountAssignInfo); //托管账户分配审核
                } elseif (in_array($accountAssignInfo->account_type, ['10', '50', '30'])) {
                    $updateCardRes = $this->handleAccountMoney($params, $accountAssignInfo); //资金账户分配审核
                    $updateCardArr = $updateCardRes['updateCardArr'];
                    $originalMoney = $updateCardRes['originalMoney'];
                } elseif ($accountAssignInfo->account_type == '20') {
                    $updateCardArr = $this->handleAccountCredit($params, $accountAssignInfo); //授信账户分配审核
                } else {
                    throw new RuntimeException('非法的账户类型', 2);
                }
                Log::error("333", [], "catTime");
                $status = TRUE;

                if ($accountAssignInfo->billID) {
                    $data = (new AssignService())->getList(['billID' => $accountAssignInfo->billID]);
                    if ($data && $data->data[0]->state != 'SUCCESS') {
                        //push至G7Pay
                        $g7Res = (new AssignService())->doSuccessById($params['id']);
                        if ($accountAssignInfo->account_type == 20) {
                            Log::error("assign_id:" . $params['id'] . ",order_no:" . $g7Res[0]->glpOrderNo, [], 'txxx_');
                            OilAccountAssign::edit([
                                'id'           => $params['id'],
                                "glp_order_no" => $g7Res[0]->glpOrderNo ? $g7Res[0]->glpOrderNo : null,
                                "updatetime"   => \helper::nowTime(),
                            ]);
                        }
                    } else {
                        Log::error('assignAssignError' . var_export($accountAssignInfo, TRUE), [], 'doSuccessByIdError');
                    }
                }
            }

            $res = [];
            //检查是否是众邦卡，是直接分配转账
            if ($accountAssignInfo->account_type == '20') {
                $res = $this->checkIsTransfer($params['id'], $accountAssignInfo);
            }

            //卡截留记录相关分配单记录
            if(!isset($params['card_consume']) && $accountAssignInfo->assign_type == \Fuel\Defines\AssignType::MANUAL_DEDUCTION_ASSIGN){
                Log::error('begin',[],'timyyy');
                $detailsInfo = OilAccountAssignDetails::getAssignDetailByAssignId(['assign_id' => $accountAssignInfo->id]);
                Log::error('begin',[$detailsInfo],'timyyy');
                $deductionList = Models\OilCardDeductionAmount::getCardAmountMapForEdit();
                Log::error('$deductionList',[$deductionList],'timyyy');
                foreach ($detailsInfo as $val){
                    Log::error('keys',[array_keys($deductionList)],'timyyy');
                    Log::error('vice_no',[$val['vice_no']],'timyyy');
                    if(in_array($val['vice_no'],array_keys($deductionList)))
                    {
                        Log::error('edit',[$deductionList[$val['vice_no']]],'timyyy');
                        $editRes = \Models\OilCardDeductionAmount::edit(['id'=>$deductionList[$val['vice_no']]['id'],'remark'=>$deductionList[$val['vice_no']]['remark'].$accountAssignInfo->no.';']);
                        Log::error('edit',[$editRes],'timyyy');
                    }
                }
            }

            //祥辉圈回增加返利充值逻辑
            if($accountAssignInfo->provider_flag == 2 && $accountAssignInfo->service_money < 0){
                //判断机构是否属于名单内
                $org_info = OilOrg::getById(['id'=>$accountAssignInfo->org_id]);
                $specialList = AccountAssignStatus::getSpecialList();
                if (count($specialList) > 0 && isset($specialList['SPECIAL_ORGLIST']) && count($specialList['SPECIAL_ORGLIST']) > 0) {
                    $nowOrgRoot = substr($org_info->orgcode, 0, 6);
                    if (in_array($nowOrgRoot, $specialList['SPECIAL_ORGLIST'])) {
                        //返利充值
                        $chargeRes = OilAccountMoneyCharge::assignNegativeChangeCharge(['service_money'=>abs($accountAssignInfo->service_money),'pay_no'=>$accountAssignInfo->no],$org_info);
                        //模拟mock充值审核
                        $this->mockAuditCharge($chargeRes->id);
                        //推送gos
                        \Fuel\Service\AccountMoneyChargeToGos::sendBatchCreateTask([$chargeRes->id],'sync');
                    }
                }
            }

            Log::error("444", [], "catTime");
            if ((_getTime() - $timeStart) > 12 && isset($params['card_consume'])) {
                Log::error("执行超时,请稍后在试", [], "catTime");
                throw new \RuntimeException('执行超时,请稍后在试！', 1103);
            }

            //主站圈回单子不入卡流水表
            if( $accountAssignInfo->no_type != AccountAssignStatus::MAIN_TURN_NO_TYPE) {
                $this->addCardStream($accountAssignInfo, true, $res);

                //主卡计算动账
                $this->setAssignMainCardAccount($params['id']);
            }

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Log::info('audit:', strval($e), 'assignAudit');
            Capsule::connection()->rollBack();
            throw new RuntimeException($e->getMessage(), $e->getCode() > 0 ? $e->getCode() : 2);
        }

        if ($status) {
            //push到Gos系统
            AccountAssignToGos::sendBatchUpdateTask([$params['id']]);
            if ($updateCardArr) {
                CardViceToGos::batchUpdateToGosByViceIds($updateCardArr);
            }
        }
        Log::error("555", [], "catTime");
        Log::info('sql--' . var_export(Capsule::connection()->getQueryLog(), TRUE));

        Log::error("666-end", [], "catTime");
        $returnData = ['success' => TRUE, 'msg' => '审核成功'];

        $this->addSupplierAccountLog($accountAssignInfo->id);

        //G7WALLET-6341分配针对返利逻辑处理
        if (in_array($accountAssignInfo->account_type, ['10', '50', '30'])){
            $org_info = OilOrg::getById(['id'=>$accountAssignInfo->org_id]);
            if(OrgStatus::isUseNewRebateMark($org_info->orgcode)){
                \Fuel\Service\Assign::assignUseRebate($params,$accountAssignInfo,$originalMoney);
            }
        }
        if ((isset($params['isAutoAudit']) && $params['isAutoAudit']) || $assign_id > 0) {
            return 1;
        } else {
            Response::json($returnData, 0, $returnData['msg']);
        }
    }

    /**
     * @param $chargeId
     */
    public function addSupplierAccountLog($assign_id = 0)
    {
        if( empty($assign_id) ){
            return false;
        }
        $assignDetail = OilAccountAssignDetails::getFieldByFilter(['assign_id'=>$assign_id]);
        if(count($assignDetail) <= 0){
            return  false;
        }

        $viceList = OilCardVice::getTotal(['idList'=>$assignDetail->toArray(),
            "propertyIn"=>[\Fuel\Defines\CardViceConf::CARD_PROPERTY_STATION_CUSTODY,\Fuel\Defines\CardViceConf::CARD_PROPERTY_MAIN_STATION]]);

        if(count($viceList) > 0) {
            //写入上游申请单流程
            $task = (new \Jobs\SupplierAssignChargeJob(['assign_id' => $assign_id]))
                ->setTaskName('异步写上游充值单-分配')
                ->onQueue('addSupplierAssignChargeJob')
                ->setTries(3)
                ->dispatch();
            \Framework\Log::error(__METHOD__, [$task], "SupplierAssignChargeJob");
        }
    }

    /*
     * 模拟返利充值审核
     */
    public function mockAuditCharge($chargeId)
    {
        $module = 'oil_account_money_charge';
        require_once $this->app->getModuleRoot() . $module . DIRECTORY_SEPARATOR . 'control.php';
        $_POST['ids'] = $chargeId;
        $module = new $module;
        /* 调用相应的方法。*/
        call_user_func_array([&$module, 'cardAuditForMock'], ['ids' => $chargeId]);
    }

    //分配，转账接口
    public function transter()
    {
        $params            = \helper::filterParams();
        $accountAssignInfo = OilAccountAssign::getByIdByLock(['id' => $params['id']]);
        //转账
        //$this->checkIsTransfer($params['id'],$accountAssignInfo);
        //撤销转账
        $this->revokeTransfer($params['id']);
        Response::json([], 0, '成功');
    }

    //检查是否众邦卡及直接转账
    public function checkIsTransfer($assignId, $assignInfo)
    {
        if($assignInfo->money_total < 0){
            //校验机构授信账户
            $creditAccout = OilCreditAccount::checkCreditAccountForNegative($assignInfo->account_no, 'auditByTrans_');
        }else{
            //校验机构授信账户
            $creditAccout = OilCreditAccount::checkCreditAccount($assignInfo->account_no, 'auditByTrans_');
        }

        $creditRes = [];
        Log::error('$creditAccout' . var_export($creditAccout['credit_account_info']->toArray(), TRUE), [], 'checkIsTransfer');
        Log::error('$creditAccout' . var_export($creditAccout['creditInfo'], TRUE), [], 'checkIsTransfer');
        //判断是否需要请求授信转账
        if ($creditAccout['credit_account_info']->bill_way == 10 && $creditAccout['credit_account_info']->is_own == 0) {
            $detail    = OilAccountAssignDetails::getAssignDetails(['assign_id' => $assignId]);
            $paramData = [];
            foreach ($detail as $item) {
                $cardAccount      = OilCardAccount::getCardAccountByAccountNO(['vice_id' => $item['vice_id'], 'common_account_no' => $assignInfo->account_no]);
                $cardCreditaccountInfo = (new AccountService())->getZBankAccountInfo(['subAccountID' => $cardAccount->cardSubAccountID, "one" => 1]);
                $balance = $cardCreditaccountInfo->restCreditAmount;
                Log::error('cardAccount:'.var_export($cardAccount,true), [], 'checkIsTransfer');
                $record['amount'] = intval($item['assign_money'] * 100);
                $record['extID']  = Helper::uuid();
                //圈回时，卡账户与机构账户互调
                Log::error('amount' . var_export($record['amount'], TRUE), [], 'checkIsTransfer');
                if (bccomp($record['amount'], 0,2) > 0) {
                    $orgCreditAccount = $creditAccout['creditInfo']->restCreditAmount / 100;
                    if($orgCreditAccount <= 0){
                        throw new \RuntimeException('机构授信账户余额不足', 2);
                    }

                    if(bccomp($item['assign_money'],$orgCreditAccount,2) > 0){
                        throw new \RuntimeException('授信额度不足,无法分配;子账户账号:'.$item->vice_no, 3520);
                    }
                    $lastBalance = ($balance + abs($record['amount']))/100;
                    $record['inSubAccountID']  = strval($cardAccount->cardSubAccountID);
                    $record['outSubAccountID'] = strval($creditAccout['credit_account_info']->subAccountID);
                } else {
                    Log::error('amount' . var_export($record['amount'], TRUE), [$balance], 'checkIsTransfer');
                    if(bccomp(abs($record['amount']),$balance,2) > 0){
                        Log::error('授信额度不足,无法撤回;子账户账号:'.$item->vice_no, [], 'checkIsTransfer');
                        throw new \RuntimeException('授信额度不足,无法撤回;子账户账号:'.$item->vice_no, 3520);
                    }
                    $record['inSubAccountID']  = strval($creditAccout['credit_account_info']->subAccountID);
                    $record['outSubAccountID'] = strval($cardAccount->cardSubAccountID);
                    $lastBalance = ($balance - abs($record['amount']))/100;
                }
                $creditRes[$item['vice_id']] = ['subAccountID'=>$cardAccount->cardSubAccountID,"creditName" => $creditAccout['credit_account_info']->name,"balance"=>$lastBalance];
                $record['amount'] = abs($record['amount']);
                $paramData[]      = $record;

                $map[$record['extID']] = $item['id'];
            }
            if ($paramData) {
                foreach ($map as $exiId => $oneId) {
                    OilAccountAssignDetails::edit(["id" => $oneId, 'extID' => $exiId]);
                }
                Log::error('$paramData' . var_export($paramData, TRUE), [], 'checkIsTransfer');
                $result = (new TransferService())->transferDirectZbank(['transferDetail' => $paramData]);
                Log::error("分配审核，转账结果" . var_export($result, TRUE), [$paramData], "checkIsTransfer");
                //需要处理返回值
                if ($result) {
                    foreach ($result->body as $res) {
                        if (array_key_exists($res->extID, $map)) {
                            OilAccountAssignDetails::edit(["id" => $map[$res->extID], 'extID' => $res->extID, "billID" => $res->billID]);
                        }
                    }
                } else {
                    throw new RuntimeException('授信转账异常', 2);
                }
            }
        }

        return $creditRes;
    }

    //入库卡流水及发送模板消息
    public function addCardStream($accountAssignInfo,$isAudit = false,$reditRes = [])
    {
        $detailArr = OilAccountAssignDetails::getAssignDetailByAssignId(['assign_id' => $accountAssignInfo->id]);
        foreach ($detailArr as $_val) {
            $logItem[$_val['vice_id']] = ["id"=>$_val['id'],"assign_time"=>$accountAssignInfo->complete_time,"use_fanli_remain"=>$_val['use_fanli_remain'],"account_type"=>$accountAssignInfo->account_type];
            //针对祥辉物流,更改模板消息备注
            $assignMap[$_val['vice_id']] = ['actual_money'=>$_val['actual_money'],'fee'=>$_val['service_money'],"assign_money"=>$_val['assign_money']];
            if(bccomp($_val['service_money'],0,2) > 0){
                $remarkMap[$_val['vice_id']] = "分配金额".$_val['assign_money']."元，车队管理费".$_val['service_money']."元，实际到账".$_val['actual_money']."元";
            }else{
                //$assignMap[$_val['vice_id']] = $_val['assign_money'];
                $remarkMap[$_val['vice_id']] = $_val['remark_work'];
            }
            $vice_ids[]                  = $_val['vice_id'];
        }
        $viceInfo = OilCardVice::getByIds($vice_ids);
        //txb 2018.8.10 分配后同步一号充值卡卡余额数据
        foreach ($viceInfo as $_key => $_card) {
            //圈回时发送模板消息
            $item['vice_no']       = $_card->vice_no;
            $item['card_remain']   = $_card->card_remain;
            $item['assign_amount'] = $assignMap[$_card->id]['actual_money'];
            if($accountAssignInfo->provider_flag == 2 && $isAudit) {
                if (in_array($_card->oil_com, OilCom::getFirstList())) {
                    if (bccomp($item['assign_amount'], 0,2) < 0) {
                        AccountAssign::sendTemplateMsg($item); //圈回通知发送
                    } else {
                        $item['remark_work'] = $remarkMap[$_card->id];
                        WeChatTemplateMsg::assign($item);
                    }
                }
            }
            $_dataItem = [];
            if($isAudit) {
                //入库卡流水表
                $_dataItem['card_no'] = $_card->vice_no;
                if(in_array($_card->oil_com,OilCom::getAllFirstList())){
                    $txt = $_card->oil_com == OilCom::GAS_FIRST_TALLY ? "共享账户-" : "充值账户-";
                }else{
                    $txt = '实体卡-';
                }
                if (bccomp($item['assign_amount'], 0,2) < 0) {
                    $_dataItem['res_type'] = CardViceBillConf::RES_TYPE_QH;
                    $_dataItem['trade_desc'] = $txt."圈回";
                } else {
                    $_dataItem['trade_desc'] = $txt."分配";
                    if($assignMap[$_card->id]['fee'] > 0 ){
                        $_dataItem['trade_desc'] = $txt."分配"."（分配".$assignMap[$_card->id]['assign_money']."元，管理费".$assignMap[$_card->id]['fee']."元）";
                    }
                    $_dataItem['res_type'] = CardViceBillConf::RES_TYPE_FP;
                }
                $_dataItem['res_id'] = $logItem[$_card->id]['id'];
                $_dataItem['amount'] = $item['assign_amount'];
                $pay_type = $logItem[$_card->id]['account_type'] == 20 ? CardViceBillConf::PAY_TYPE_CREDIT_ACCOUNT : CardViceBillConf::PAY_TYPE_CASH_ACCOUNT;
                $_dataItem['pay_type'] = $pay_type;
                $_dataItem['mobile'] = $_card->driver_tel;
                $_dataItem['trade_time'] = $logItem[$_card->id]['assign_time'];
                $_dataItem['use_fanli'] = $logItem[$_card->id]['use_fanli_remain'];
                $_dataItem['oil_com'] = $_card->oil_com;
                if( $accountAssignInfo->account_type == '20'){
                    $_dataItem['account_name'] = isset($reditRes[$_card->id]) && isset($reditRes[$_card->id]['creditName']) ? $reditRes[$_card->id]['creditName'] : "";
                    $_dataItem['account_no'] = isset($reditRes[$_card->id]) && isset($reditRes[$_card->id]['subAccountID']) ? $reditRes[$_card->id]['subAccountID'] : "";
                }else{
                    $cardAccount = OilCardAccount::getByViceId($_card->id, 'CASH');
                    $_dataItem['account_name'] = "";
                    $_dataItem['account_no'] = $cardAccount && $cardAccount->cardSubAccountID ? $cardAccount->cardSubAccountID : "";
                }
                if( !in_array($_card->oil_com,[OilCom::GAS_FIRST_TALLY,OilCom::GAS_FIRST_ZBANK_TALLY]) ) {

                    if ($_card->card_level == CardViceConf::CARD_LEVEL_CAR &&
                        !in_array($_dataItem['res_type'], [CardViceBillConf::RES_TYPE_XF, CardViceBillConf::RES_TYPE_XFCX])) {
                        $_dataItem['mobile'] = '';
                    }

                    (new CardViceBill())->generateBill($_dataItem);
                }
            }else{
                $_dataItem['res_id'] = $logItem[$_card->id]['id'];
                $_dataItem['res_type'] = CardViceBillConf::RES_TYPE_FP;
                (new CardViceBill())->cancelBill($_dataItem);
            }
        }
        return true;
    }

    public function checkIsTransfer_bak($assignId, $assignInfo)
    {
        $isDirect = 0;
        try {
            //$creditAccout = OilCreditAccount::getSingleRecord(['org_id' => $assignInfo->org_id]);
            $creditAccout = OilCreditAccount::checkZBank(['org_id' => $assignInfo->org_id]);
            $detail       = OilAccountAssignDetails::getAssignDetails(['assign_id' => $assignId]);
            $isDirect     = 0;
            $paramData    = [];
            foreach ($detail as $item) {
                //由于众邦卡，不能组合匹配，判断一个就行
                if ($item['card_vice']['oil_com'] == OilCom::GAS_FIRST_ZBANK_CHARGE) {
                    $isDirect = 1;
                }

                $cardAccount      = OilCardAccount::getCardAccount(['vice_id' => $item['vice_id']]);
                $record['amount'] = $item['assign_money'] * 100;
                $record['extID']  = Helper::uuid();
                //圈回时，卡账户与机构账户互调
                if (bccomp($record['amount'], 0,2) > 0) {
                    $record['inSubAccountID']  = $cardAccount->cardSubAccountID;
                    $record['outSubAccountID'] = $creditAccout['info']->subAccountID;
                } else {
                    $record['inSubAccountID']  = $creditAccout->subAccountID;
                    $record['outSubAccountID'] = $cardAccount['info']->cardSubAccountID;
                }
                $record['amount'] = abs($record['amount']);
                $paramData[]      = $record;

                $map[$record['extID']] = $item['id'];
            }
            if ($isDirect && $creditAccout['code'] == 1) {
                if (count($creditAccout) < 0 || $creditAccout->cardSubAccountID) {
                    throw new RuntimeException('信用账户不存在', 2);
                }

                foreach ($map as $exiId => $oneId) {
                    OilAccountAssignDetails::edit(["id" => $oneId, 'extID' => $exiId]);
                }

                $result = (new TransferService())->transferDirectZbank(['transferDetail' => $paramData]);
                Log::error("分配审核，转账结果" . var_export($result, TRUE), [$paramData], "auditByTrans_");
                //todo 需要处理返回值
                if ($result) {
                    foreach ($result->body as $res) {
                        if (array_key_exists($res->extID, $map)) {
                            OilAccountAssignDetails::edit(["id" => $map[$res->extID], 'extID' => $res->extID, "billID" => $res->billID]);
                        }
                    }
                } else {
                    return FALSE;
                }
            } else {
                return TRUE;
                //throw new RuntimeException('该充值单非众邦充值卡', 2);
            }
        } catch (Exception $e) {
            Log::error("请求g7pay转账失败:" . $e->getMessage(), $e->getCode());
            return TRUE;
            //throw new RuntimeException($e->getMessage(), $e->getCode());
        }
        return TRUE;
    }

    //撤销众邦转账单
    public function revokeTransfer($assignId)
    {
        try {
            $detail = OilAccountAssignDetails::getAssignDetails(['assign_id' => $assignId]);

            foreach ($detail as $item) {
                if ($item['card_vice']['oil_com'] == OilCom::GAS_FIRST_ZBANK_CHARGE) {

                    $record['amount'] = $item['assign_money'] * 100;
                    $record['billID'] = $item['billID'];
                    //$paramData[] = $record;
                    $result = (new TransferService())->transferRevokeZbank($record);
                    print_r($result);
                } else {
                    continue;
                }
            }
        } catch (Exception $e) {
            //return false;
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    //驳回
    public function assignReject()
    {
        $params = \helper::filterParams();
        $ids    = $params['ids'];

        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            //驳回之后的单号不能重复驳回
            $info = OilAccountAssign::getByIdLock(['id' => $ids]);
            //审核之后的单号不能驳回
            if ($info->status == 1) {
                throw new RuntimeException('该单号已审核，不能驳回', 2);
            }
            if ($info->status == -1) {
                throw new RuntimeException('该单号已驳回', 2);
            }
            $result = OilAccountAssign::edit(['id' => $ids, 'status' => -1, 'last_operator_id' => $this->app->myAdmin->id, 'last_operator' => $this->app->myAdmin->true_name, 'updatetime' => \helper::nowTime()]);

            if ($result) {
                //添加工单日志
                OilCardViceAppLog::add([
                    'type'             => 2,
                    'app_id'           => $ids,
                    'status'           => -1,
                    'status_name'      => '已驳回',
                    'last_operator'    => $this->app->myAdmin->true_name,
                    'last_operator_id' => $this->app->myAdmin->id,
                    'createtime'       => \helper::nowTime(),
                    'updatetime'       => \helper::nowTime()
                ]);

            } else {
                throw new RuntimeException('驳回失败', 2);
            }

            //提交事务
            Capsule::connection()->commit();
        } catch (Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        //push至Gos系统
        AccountAssignToGos::sendBatchUpdateTask([$params['ids']]);

        Response::json([], 0, '驳回成功');
    }

    /**
     * @title   客户卡销审逻辑 不对现金账户操作，直接通过审核
     * @desc
     * @version
     * <AUTHOR>
     */
    public function handleCustomerCardActionUnAudit($accountAssignInfo)
    {
        $updateCardArr = [];
        Capsule::connection()->beginTransaction();
        try {
            // 更新副卡的备付金余额和积分备付金
            /*if (!AccountAssign::updateViceReserve($accountAssignInfo->id, $isUnAudit = TRUE)) {
                throw new RuntimeException('销审失败：更新备付金失败', 1);
            }*/
            $updateCardArr = AccountAssign::updateViceReserve($accountAssignInfo->id, $isUnAudit = TRUE);

            //修改工单
            $status = OilAccountAssign::getAssignNoStatus($accountAssignInfo->id);
            $accountAssignInfo->update(
                [
                    'status'           => $status,
                    'last_operator_id' => $this->app->myAdmin->id,
                    'last_operator'    => $this->app->myAdmin->true_name,
                    'complete_person'  => $this->app->myAdmin->true_name,//完成人
                    'complete_time'    => \helper::nowTime(),//完成时间
                    'updatetime'       => \helper::nowTime(),
                ]
            );

            //添加工单日志
            OilCardViceAppLog::add([
                'type'             => 2,
                'app_id'           => $accountAssignInfo->id,
                'status'           => $status,
                'status_name'      => AccountAssignStatus::getById($status),
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => \helper::nowTime(),
                'updatetime'       => \helper::nowTime(),
            ]);
            Capsule::connection()->commit();
        } catch (Exception $e) {
            Log::info('audit:', strval($e), 'assignAudit');
            Capsule::connection()->rollBack();
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return $updateCardArr;

    }

    /**
     * @title   客户卡销审逻辑 不对现金账户操作，直接通过销审
     * @desc
     * @version
     * <AUTHOR>
     */
    public function handleCustomerCardAction($accountAssignInfo)
    {
        // 更新副卡的备付金余额和积分备付金
        /*if (!AccountAssign::updateViceReserve($accountAssignInfo->id)) {
            throw new RuntimeException('审核失败：更新备付金失败', 1);
        }*/
        $updateCardArr = AccountAssign::updateViceReserve($accountAssignInfo->id);

        //修改工单
        $accountAssignInfo->update(
            [
                'status'           => 1,
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name,
                'complete_person'  => $this->app->myAdmin->true_name,//完成人
                'complete_time'    => \helper::nowTime(),//完成时间
                'updatetime'       => \helper::nowTime(),
            ]
        );

        //添加工单日志
        OilCardViceAppLog::add([
            'type'             => 2,
            'app_id'           => $accountAssignInfo->id,
            'status'           => 1,
            'status_name'      => AccountAssignStatus::getById(1),
            'last_operator'    => $this->app->myAdmin->true_name,
            'last_operator_id' => $this->app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'updatetime'       => \helper::nowTime(),
        ]);

        return $updateCardArr;

    }

    /**
     * @title  审核操作：处理授信账户
     * @param array $params
     * @return mixed
     * <AUTHOR>
     */
    private function handleAccountCredit(array $params, $accountAssignInfo)
    {
        Log::error("handleAccountCredit-1111", [], "catTime");
        $id            = intval($params['id']);
        $updateCardArr = [];

        //分配单信息
        $money_total = $accountAssignInfo->money_total;

        //校验授信账户是否停用
        $info = OilCreditAccount::getByAccountNo($accountAssignInfo->account_no);
        Log::error("handleAccountCredit-2222", [], "catTime");
        Log::error("handleAccountCredit:begin" . var_export($params, TRUE), [$info], "creditAssign_");
        if ($info->status == 20) {
            throw new RuntimeException('授信账户[' . $accountAssignInfo->account_no . ']已停用', 2);
        }

        //账户Service
        $accountService = (new AccountGredit())->setAccount($accountAssignInfo->account_no);
        Log::error("handleAccountCredit-3333", [], "catTime");
        //账户信息
        $accountInfo = $accountService->getCreditAccountInfo();
        Log::error("handleAccountCredit-4444", [], "catTime");
        $originalMoney = $accountInfo->credit_blance;

        //校验
        AccountAssign::auditByValidate($accountAssignInfo, $accountService);
        Log::error("handleAccountCredit-5555", [], "catTime");
        $orgInfo = OilOrg::getById(['id' => $accountAssignInfo->org_id]);
        Log::error("handleAccountCredit-6666", [], "catTime");
//        if ($accountAssignInfo->provider_flag == 2 && !isset($params['isAutoAudit'])) {
//            //撬装充值卡的审核
//            AccountAssign::gasAssign($id, $orgInfo, $isUnAudit = FALSE,$accountAssignInfo);
//        }

        Log::error("ppp:" . \GuzzleHttp\json_encode($params), [], "assignZBank_");
        if ($accountAssignInfo->provider_flag == 2 && !isset($params['isAutoAudit'])) {
            Log::error("pppFirst:" . \GuzzleHttp\json_encode($params), [], "assignZBank_");
            //撬装充值卡的审核
            AccountAssign::gasAssign($id, $orgInfo, $isUnAudit = FALSE, $accountAssignInfo);
            $updateCardArr = AccountAssign::updateViceReserve($id);
        } else {
            Log::error("handleAccountCredit-7777", [], "catTime");
            Log::error("pppNoFirst:" . \GuzzleHttp\json_encode($params), [], "assignZBank_");
            //如果分配明细中有资金变动，则添加资金账户流水
            if (OilAccountAssignDetails::checkAccountChange($id)) {
                //分配处理
                AccountAssign::creditAssign(
                    [
                        'accountAssignInfo' => $accountAssignInfo,
                        'accountInfo'       => $accountInfo,
                        'accountService'    => $accountService,
                    ]
                );
            }

            Log::error("handleAccountCredit-8888", [], "catTime");
            //如果分配明细中有积分变动，则添加积分账户流水
            if (OilAccountAssignDetails::checkAccountJifenChange($id)) {
                AccountAssign::jifenAssign($id, $accountAssignInfo);
            }
            Log::error("handleAccountCredit-9999", [], "catTime");
            // 更新副卡的备付金余额和积分备付金
            /*if (!AccountAssign::updateViceReserve($id)) {
                throw new RuntimeException('审核失败：更新备付金失败', 1);
            }*/

            Log::error("handleAccountCredit-00001", [], "catTime");
            $creditAccountInfo = OilCreditAccount::getByAccountNoWithProvider($accountAssignInfo->account_no);
            Log::error("handleAccountCredit-00002", [], "catTime");
            if ($creditAccountInfo && $creditAccountInfo->CreditProvider->bill_way == 20) {
                Log::error("updateViceReserve-00001", [1], "catTime");
                $updateCardArr = AccountAssign::updateViceReserve($id);
                Log::error("updateViceReserve-00002", [2], "catTime");
            } else {
                $updateCardArr = [];
            }

            //更新副卡的授信账户余额
            if (!isset($params['card_consume'])) {
                $updateCardArr = AccountAssign::updateCreditViceBalance($id, $isUnAudit = FALSE, $accountAssignInfo->account_no);
                if (!$updateCardArr) {
                    throw new RuntimeException('审核失败：更新备付金失败', 1);
                }
            }
            Log::error("handleAccountCredit-9999", [], "catTime");
        }

        Log::error("handleAccountCredit:End" . var_export($params, TRUE), [], "creditAssign_");

        //修改工单
        $accountAssignInfo->update(
            [
                'status'           => 1,
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name,
                'complete_person'  => $this->app->myAdmin->true_name,//完成人
                'complete_time'    => \helper::nowTime(),//完成时间
                'updatetime'       => \helper::nowTime(),
            ]
        );

        Log::error("handleAccountCredit-101010", [], "catTime");

        //修改授信账户最新分配时间
        OilCreditAccount::updateByAccountNo(['account_no' => $accountAssignInfo->account_no, 'last_assign_time' => date("Y-m-d H:i:s")]);

        Log::error("handleAccountCredit-121212", [], "catTime");

        //添加工单日志
        OilCardViceAppLog::add([
            'type'             => 2,
            'app_id'           => $id,
            'status'           => 1,
            'status_name'      => AccountAssignStatus::getById(1),
            'last_operator'    => $this->app->myAdmin->true_name,
            'last_operator_id' => $this->app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'updatetime'       => \helper::nowTime(),
        ]);

        Log::error("handleAccountCredit-131313", [], "catTime");
        /********************外部系统************************/
        if ($accountAssignInfo->money_total) {
            //发送微信通知
            try {
                $creditInfo = (new AccountService())
                    ->getCreditBalanceByOrgCode(['orgCode' => $orgInfo->orgcode]);

                if ($creditInfo) {
                    $providerInfo = OilCreditProvider::getById(['id' => $accountInfo->credit_provider_id]);
                    $afterMoney   = $creditInfo->restCreditAmount / 100;
                    OilOrgWx::sendNotify([
                        'org_id'       => $accountAssignInfo->org_id,
                        'cash_type'    => '分配',
                        'money'        => formatMoney($money_total),
                        'blance_money' => $afterMoney,
                        'remark'       => $accountAssignInfo->remark_work,
                        'account_type' => '信用账户:' . $providerInfo->name,
                    ]);
                }
            } catch (Exception $e) {
                Log::dataLog('sendNotify--' . strval($e), 'wxDebug');
            }
        }
        Log::error("handleAccountCredit-141414", [], "catTime");

        return $updateCardArr;
    }

    /**
     * @title  审核操作：处理资金账户
     * @param array $params
     * @return mixed
     * <AUTHOR>
     */
    private function handleAccountMoney(array $params, $accountAssignInfo)
    {
        $id            = intval($params['id']);
        $updateCardArr = [];

        //分配单信息
        $money_total = $accountAssignInfo->money_total;

        //账户Service
        $accountMoneyService = (new AccountMoney())
            ->setOrg($accountAssignInfo->org_id);

        //现金账户信息
        $accountMoneyInfo = $accountMoneyService->getAccountInfo();
        $originalMoney    = $accountMoneyInfo->money;//分配前资金账户余额

        $orgInfo = OilOrg::getById(['id' => $accountAssignInfo->org_id]);

        //校验
        AccountAssign::auditByValidate($accountAssignInfo, $accountMoneyService);

        Log::error("现金分配Begin,params" . var_export($params, TRUE), [], "assignMoney_");
        if ($accountAssignInfo->provider_flag == 2 && !isset($params['isAutoAudit'])) {
            //撬装充值卡的审核
            AccountAssign::gasAssign($id, $orgInfo, $isUnAudit = FALSE, $accountAssignInfo);
        }

        $use_cash_fanli = 0;
        //如果分配明细中有资金变动，则添加资金账户流水
        if (OilAccountAssignDetails::checkAccountChange($id)) {
            //现金分配处理
            $use_cash_fanli = AccountAssign::moneyAssign(
                [
                    'accountAssignInfo'   => $accountAssignInfo,
                    'accountMoneyInfo'    => $accountMoneyInfo,
                    'accountMoneyService' => $accountMoneyService,
                ]
            );
        }
        //如果分配明细中有积分变动，则添加积分账户流水
        if (OilAccountAssignDetails::checkAccountJifenChange($id)) {
            AccountAssign::jifenAssign($id, $accountAssignInfo);
        }
        if ($accountAssignInfo->provider_flag != 3) {
            // 更新副卡的备付金余额和积分备付金
            $updateCardArr = AccountAssign::updateViceReserve($id);
        }

        //修改工单
        $accountAssignInfo->update(
            [
                //'use_cash_fanli'   => $use_cash_fanli, //新的逻辑是创建分配单已经返利计算好，所以这块 销申的时候不清零所以注释
                'status'           => 1,
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name,
                'complete_person'  => $this->app->myAdmin->true_name,//完成人
                'complete_time'    => \helper::nowTime(),//完成时间
                'updatetime'       => \helper::nowTime(),
            ]
        );

        Log::error("现金分配End,params" . var_export($params, TRUE), [], "assignMoney_");
        //添加工单日志
        OilCardViceAppLog::add([
            'type'             => 2,
            'app_id'           => $id,
            'status'           => 1,
            'status_name'      => AccountAssignStatus::getById(1),
            'last_operator'    => $this->app->myAdmin->true_name,
            'last_operator_id' => $this->app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'updatetime'       => \helper::nowTime(),
        ]);
        /********************外部系统************************/
        if ($accountAssignInfo->money_total) {

            global $app;
            if ( $app->config->scheduler->switch == 1 ) {
                $moneyParams['originalMoney'] = $originalMoney;
                $moneyParams['accountAssignInfo'] = $accountAssignInfo;
                $moneyParams['money_total'] = $money_total;
                (new \Jobs\SendWxTemplateNoticeJob($moneyParams))
                    ->setTaskName('发送微信通知')
                    ->onQueue('default')
                    ->setTries(3)
                    ->dispatch();
            }else {
                (new Job())
                    ->setTaskName('发送微信通知')
                    ->pushTask(function () use ($originalMoney, $accountAssignInfo, $money_total) {
                        require_once APP_MODULE_ROOT . '/oil_account_assign/control.php';

                        //发送微信通知
                        $afterMoney = formatMoney($originalMoney - $accountAssignInfo->money_total);//分配后资金账户余额
                        try {
                            OilOrgWx::sendNotify([
                                'org_id'       => $accountAssignInfo->org_id,
                                'cash_type'    => '分配',
                                'money'        => formatMoney($money_total),
                                'blance_money' => $afterMoney,
                                'remark'       => $accountAssignInfo->remark_work,
                            ]);
                            //小于5000块微信提醒
                            if (intval($afterMoney) < AccountMoneyTip::$AccountMoneyLimit) {
                                OilOrgWx::sendMoneyLackNotify([
                                    'org_id'       => $accountAssignInfo->org_id,
                                    'blance_money' => $afterMoney,
                                ]);
                            }
                        } catch (Exception $e) {
                            Log::dataLog('sendNotify--' . strval($e), 'wxDebug');
                        }
                    })
                    ->exec();
            }
        }

        return ['updateCardArr'=>$updateCardArr,'originalMoney'=>$originalMoney];
    }

    /**
     * @title 销审
     */
    public function unAudit($assign_id = 0, $isReturn = FALSE, $is_delete = FALSE)
    {
        if ($isReturn) {
            $params['id'] = $assign_id;
        } else {
            $params = \helper::filterParams();

            \helper::argumentCheck(['id', 'account_type'], $params);
        }

        Log::info('params--' . var_export($params, TRUE));

        Capsule::connection()->beginTransaction();
        try {
            //分配单信息
            $accountAssignInfo = OilAccountAssign::getByIdByLock(['id' => $params['id']]);

//            if (in_array($accountAssignInfo->provider_flag, [2, 3]) && $accountAssignInfo->assign_type == 30 && !$isReturn) {
//                throw new RuntimeException('操作失败：1号卡分配单禁止销审', 2);
//            }

            /*($accountAssignInfo->assign_type == CardFrom::CUSTOMER_CARD &&
                !in_array($accountAssignInfo->account_type, [10, 20, 30]) )*/
            //之前的条件不符合特留注释
            if ($accountAssignInfo->no_type == AccountAssignStatus::MAIN_TURN_NO_TYPE) {
                $status = OilAccountAssign::getAssignNoStatus($params['id']);
                OilAccountAssign::edit([
                    'id'               => $params['id'],
                    "status"           => $status,
                    "last_operator_id" => $this->app->myAdmin->id,
                    "last_operator"    => $this->app->myAdmin->true_name,
                    "updatetime"       => \helper::nowTime(),
                ]);
                $updateCardArr = [];

            } else {

                if ($accountAssignInfo->account_type == AccountType::NONE) {
                    $updateCardArr = (new \oil_account_assign())->handleCustomerCardActionUnAudit($accountAssignInfo);
                } elseif (in_array($accountAssignInfo->account_type, ['10', '50', '30'])) {
                    $updateCardArr = (new \oil_account_assign())->handleAccountMoneyForUnAudit($params, $accountAssignInfo);
                } elseif ($accountAssignInfo->account_type == '20' && !$is_delete) {
                    throw new RuntimeException('操作失败：资金来源为授信账户的分配单禁止销审', 2);
                }

                //push至G7Pay
                (new AssignService())->revokeById($params['id']);

            }

            //祥辉圈回增加返利充值逻辑
            if($accountAssignInfo->provider_flag == 2 && $accountAssignInfo->service_money < 0){
                //判断机构是否属于名单内
                $org_info = OilOrg::getById(['id'=>$accountAssignInfo->org_id]);
                $specialList = AccountAssignStatus::getSpecialList();
                if (count($specialList) > 0 && isset($specialList['SPECIAL_ORGLIST']) && count($specialList['SPECIAL_ORGLIST']) > 0) {
                    $nowOrgRoot = substr($org_info->orgcode, 0, 6);
                    if (in_array($nowOrgRoot, $specialList['SPECIAL_ORGLIST'])) {
                        //负返利充值
                        $chargeRes = OilAccountMoneyCharge::assignNegativeChangeCharge(['service_money'=>$accountAssignInfo->service_money,'pay_no'=>$accountAssignInfo->no],$org_info);
                        //模拟mock充值审核
                        $this->mockAuditCharge($chargeRes->id);
                    }
                }
            }

            //调用卡流水接口
            $this->addCardStream($accountAssignInfo,false);

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            throw new RuntimeException($e->getMessage(), $e->getCode() > 0 ? $e->getCode() : 2);
        }

        //push到Gos系统
        AccountAssignToGos::sendBatchUpdateTask([$params['id']]);

        if ($updateCardArr) {
            CardViceToGos::batchUpdateToGosByViceIds($updateCardArr);
        }

        Log::info('sql--' . var_export(Capsule::connection()->getQueryLog(), TRUE));
        $returnData = ['success' => TRUE, 'msg' => '销审成功'];
        if ($isReturn) {
            return 1;
        } else {
            Response::json($returnData, 0, $returnData['msg']);
        }
    }

    private function handleAccountCreditForUnAudit(array $params)
    {
        $id = intval($params['id']);

        Capsule::connection()->beginTransaction();
        try {
            //分配单信息
            $accountAssignInfo = OilAccountAssign::getByIdByLock(['id' => $id]);

            //账户Service
            $accountService = (new AccountGredit())->setAccount($accountAssignInfo->account_no);

            //现金账户信息
            $accountInfo = $accountService->getCreditAccountInfo();


            $orgInfo = OilOrg::getById(['id' => $accountAssignInfo->org_id]);

            //撬装充值卡的销审
            if ($accountAssignInfo->provider_flag == 2) {
                //撬装充值卡的销审
                AccountAssign::gasAssign($id, $orgInfo, $isUnAudit = TRUE, $accountAssignInfo);
            } else {
                //如果分配明细中有资金变动，则添加资金账户流水
                if (OilAccountAssignDetails::checkAccountChange($id)) {
                    //分配处理
                    AccountAssign::creditAssign(
                        [
                            'accountAssignInfo' => $accountAssignInfo,
                            'accountInfo'       => $accountInfo,
                            'accountService'    => $accountService,
                        ],
                        TRUE
                    );
                }

                //如果分配明细中有积分变动，则添加积分账户流水
                if (OilAccountAssignDetails::checkAccountJifenChange($id)) {
                    AccountAssign::jifenAssign($id, $accountAssignInfo);
                }

                // 更新副卡的备付金余额和积分备付金
                if (!AccountAssign::updateViceReserve($id)) {
                    throw new RuntimeException('审核失败：更新备付金失败', 1);
                }

            }

            $status = OilAccountAssign::getAssignNoStatus($accountAssignInfo->id);
            //修改工单
            $accountAssignInfo->update(
                [
                    'status'           => $status,
                    'last_operator_id' => $this->app->myAdmin->id,
                    'last_operator'    => $this->app->myAdmin->true_name,
                    'complete_person'  => $this->app->myAdmin->true_name,//完成人
                    'complete_time'    => \helper::nowTime(),//完成时间
                    'updatetime'       => \helper::nowTime(),
                ]
            );

            //添加工单日志
            OilCardViceAppLog::add([
                'type'             => 2,
                'app_id'           => $id,
                'status'           => $status,
                'status_name'      => AccountAssignStatus::getById($status),
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => \helper::nowTime(),
                'updatetime'       => \helper::nowTime(),
            ]);

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Log::info('audit:', strval($e), 'unauditForAssign');
            Capsule::connection()->rollBack();
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return $accountAssignInfo->no;
    }

    /**
     * @title  销审操作：资金账户处理
     * @param array $params
     * @param array $accountAssignInfo
     * @return array
     * <AUTHOR>
     */
    public function handleAccountMoneyForUnAudit(array $params, $accountAssignInfo)
    {
        $id            = intval($params['id']);
        $fanliMoney    = null;
        $updateCardArr = [];

        //开启事务
        Capsule::connection()->beginTransaction();
        try {

            //账户Service
            $accountMoneyService = (new AccountMoney())->setOrg($accountAssignInfo->org_id);

            //现金账户信息
            $accountMoneyInfo = $accountMoneyService->getAccountInfo();

            //校验
            AccountAssign::unAuditByValidate($accountAssignInfo);

            $orgInfo = OilOrg::getById(['id' => $accountAssignInfo->org_id]);

            $money_fenpei = [];
            $jifen_fenpei = [];

            //撬装充值卡的销审
            if ($accountAssignInfo->provider_flag == 2) {
                //撬装充值卡的销审
                AccountAssign::gasAssign($id, $orgInfo, $isUnAudit = TRUE, $accountAssignInfo);
            }

            //如果分配明细中有资金变动，则产生资金账户流水
            if (OilAccountAssignDetails::checkAccountChange($id)) {
                //现金分配处理
                $use_cash_fanli = AccountAssign::moneyAssign(
                    [
                        'accountAssignInfo'   => $accountAssignInfo,
                        'accountMoneyInfo'    => $accountMoneyInfo,
                        'accountMoneyService' => $accountMoneyService,
                    ]
                    , $isUnAudit = TRUE);

            }

            //如果分配明细中有积分变动，则添加积分账户流水
            if (OilAccountAssignDetails::checkAccountJifenChange($id)) {
                AccountAssign::jifenAssign($id, $accountAssignInfo, $isUnAudit = TRUE);
            }

            // 更新副卡的备付金余额和积分备付金
            /*if (!AccountAssign::updateViceReserve($id, $isUnAudit = TRUE)) { //todo 判断正负是否正确
                throw new RuntimeException('销审失败：更新备付金失败', 1);
            }*/
            $updateCardArr = AccountAssign::updateViceReserve($id, $isUnAudit = TRUE);

            //修改工单
            $status = OilAccountAssign::getAssignNoStatus($id);
            if (in_array($accountAssignInfo->provider_flag, [2, 3]) && $accountAssignInfo->assign_type == 30) {
                $status = -1; //一号卡直接驳回
            }
            $sn = Helper::uuid();
            $accountAssignInfo->update(
                [
                    'sn'               => $sn,
                    //                        'use_cash_fanli'   => $use_cash_fanli,//销审后使用返利变更为0了
                    'status'           => $accountAssignInfo->provider_flag == 2 ? 0 : $status,
                    'last_operator_id' => $this->app->myAdmin->id,
                    'last_operator'    => $this->app->myAdmin->true_name,
                    'complete_person'  => $this->app->myAdmin->true_name,//完成人
                    'complete_time'    => \helper::nowTime(),//完成时间
                    'updatetime'       => \helper::nowTime(),
                ]
            );

            //添加工单日志
            OilCardViceAppLog::add([
                'sn'               => $sn,
                'type'             => 2,
                'app_id'           => $id,
                'status'           => $accountAssignInfo->provider_flag == 2 ? 0 : $status,
                'status_name'      => AccountAssignStatus::getById($status),
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => \helper::nowTime(),
                'updatetime'       => \helper::nowTime(),
            ]);


            //事务提交
            Capsule::connection()->commit();
        } catch (Exception $e) {
            //事务回滚
            Capsule::connection()->rollBack();
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return $updateCardArr;
    }

    /**
     * 验证副卡号是否属于所选的机构
     *
     * @param $org_id
     * @param $details
     * @return bool
     * <AUTHOR>
     * @since  2015/09/29
     */
    public function checkIsBelongOrg($org_id, $details)
    {
        $vices = [];
        if ($details) {
            $detailArrs = explode('|', $details);
            foreach ($detailArrs as $val) {
                $detailArr = explode('#', $val);
                $vices[]   = $detailArr[0];
            }

            $viceStr = join(',', array_unique($vices));
            $res     = $this->oil_account_assign->checkIsBelongOrg($org_id, $viceStr);
            if ($res->count > 0) {
                echo json_encode(['success' => FALSE, 'msg' => $res->vice_nos . '副卡不能保存，请检查副卡所属机构']);
                die;
            } else {
                return TRUE;
            }
        } else {
            return TRUE;
        }
    }

    /**
     * 重构分配逻辑
     */
    public function add($params = null)
    {

        if (!$params) {
            $data = \helper::filterParams();
        } else {
            $data = $params;
        }
        Log::info('添加参数--' . var_export($data, TRUE), [], 'accountAssign');
        $data['data_from'] = isset($data['data_from']) ? $data['data_from'] : 1;

//        $assignSn = md5($data['assign_sn']);
//        $sn = Cache::get($assignSn);
//        if($sn){
//            throw new RuntimeException('分配流水号重复,创建分配单失败',2);
//        }else{
//            Cache::put($assignSn,1,300);
//        }

        Capsule::connection()->beginTransaction();
        try {

            $data = (new Fuel\Service\Assign($data))
                ->formatParams()
                ->splitOrder()
                ->validate()
                ->save();

            //G7WALLET-6056
            //push至G7Pay
            /*foreach ($data['assignIds'] as $id) {
                (new AssignService())->createById($id);
                //add work log
                $this->addWorkLog($id);
            }*/
            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollback();
            Log::error('分配异常--msg:' . $e->getMessage() . '原始参数：', [$params], 'Assign_Log');
            Log::error(__METHOD__ . strval($e), [], 'G7PayAbstractERROR');

            Log::error('msg', [$e->getMessage(), $e->getCode()], 'apiErrorLog_');

            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
//        Cache::forget($assignSn);


        if (isset($data['sendGasParams']) && $data['sendGasParams']) {
            try {
                Log::error('create_params:' . var_export($data['sendGasParams'], TRUE), [], 'sendGas');
                //调用gas油卡充值接
                $receiptData = GasClient::post(
                    [
                        'method' => 'gas.api.assignMoney',
                        'data'   => $data['sendGasParams']
                    ]
                );
                Log::error('return:' . var_export($receiptData, TRUE), [], 'sendGas');
                if (!$receiptData || !$receiptData->code == 0) {
                    throw new RuntimeException($receiptData->msg, 2);
                }
            } catch (Exception $e) {
                //修改工单状态为主站分配失败
                OilAccountAssign::where('id', $data['sendGasParams']['id'])->update(
                    ['status' => -10, 'remark' => $e->getMessage()]
                );
                throw new RuntimeException($e->getMessage(), $e->getCode());
            }
        }

        //判断如果是1号撬装卡就直接审核通过
        $this->auditForGas($data['assignIds']);


        //push到Gos系统
        AccountAssignToGos::sendBatchCreateTask($data['assignIds'], 'sync');

        if ($data) {
            Response::json(TRUE, 0, '添加成功');
        } else {
            Response::json(TRUE, 2, '添加失败');
        };

    }

    public function reSendGasAssign()
    {
        $data = \helper::filterParams();
        \helper::argumentCheck(['no'], $data);

        $gasData    = [];
        $assignInfo = OilAccountAssign::where('no', $data['no'])->first();

        $orgInfo = OilOrg::getById(['id' => $assignInfo->org_id]);

        $assignDetails = OilAccountAssignDetails::where('assign_id', $assignInfo->id)->get();
        if ($assignDetails) {
            foreach ($assignDetails as $v) {
                $gasData[] = [
                    'vice_no'       => $v->vice_no,
                    'assign_amount' => $v->assign_money,
                    'service_money'   => $v->service_money > 0 ? $v->service_money : 0,
                    'actual_money'    => isset($v->actual_money) && $v->actual_money ? $v->actual_money : 0,
                ];
            }

            if ($gasData) {
                try {
                    //调用gas油卡充值接
                    $receiptData = GasClient::post(
                        [
                            'method' => 'gas.api.assignMoney',
                            'data'   => [
                                'orgcode'          => $orgInfo->orgcode,
                                'details'          => $gasData,
                                'id'               => $assignInfo->id,
                                'no'               => $assignInfo->no,
                                'assign_total'     => $assignInfo->money_total,
                                'assign_num'       => count($gasData),
                                'other_creator_id' => $assignInfo->other_creator_id,
                                'unit'             => 1,
                                'other_creator'    => $assignInfo->other_creator,
                                'check_type'       => 1 //1审核 2销审
                            ]
                        ]
                    );
                } catch (Exception $e) {
                    throw new RuntimeException($e->getMessage(), $e->getCode());
                }

            }
        }

        return $receiptData;

    }

    /**
     * 校验卡号最后负分配的记录
     */
    public function checkLastAssign($isReturn = FALSE, $details = "")
    {
        if ($isReturn) {
            $data = $details;
        } else {
            $params = \helper::filterParams();
            \helper::argumentCheck(['details'], $params);
            $data = $params['details'];
        }
        //$data = "1000111109018397311#-10##京A33221##待审核#使用|****************#-20##京B12345##待审核#使用";
        $tmp = explode("|", $data);

        foreach ($tmp as $_key => $_val) {
            $vice = explode("#", $_val);
            if ($vice[1] < 0) {
                $viceNos[] = $vice[0];
            }
        }
        $res     = 1;
        $vice_No = "";
        if (count($viceNos) > 0) {
            $info = OilAccountAssignDetails::getViceInfo($viceNos, "vice_no", "createtime");
            Log::error("卡号最后圈回时间：" . json_encode($info), [], "NoApiAssign_");
            if (count($info) > 0) {
                foreach ($info as $_key => $item) {
                    if (time() - strtotime($item) < 24 * 60 * 60) {
                        $res     = -1;
                        $vice_No = (string)$_key;
                    }
                }
            }
        }
        if ($isReturn) {
            return $res;
        } else {
            Response::json($vice_No, $res, "成功");
        }
    }

    /*
     * 判断如果是撬装的卡自动审核
     */
    public function auditForGas(array $ids, $card_consume = null,$balance=0 )
    {
        $assignMap = OilAccountAssign::getByIdList($ids);

        if ($assignMap) {
            foreach ($assignMap as $assignInfo) {
                if ($assignInfo['assign_type'] == CardFrom::GAS_CARD && $assignInfo['status'] != 1) {
                    $_POST['id']          = $assignInfo['id'];
                    $_POST['isAutoAudit'] = 1;
                    if ($card_consume) {
                        $_POST['card_consume'] = 1;
                    }
                    try {
                        Log::error("申请撬装分配单,POST" . var_export($_POST, TRUE), [], "auditGas_");
                        $this->auditBy();

                        if ($card_consume && $assignInfo['trades_sn']) {
                            $zbankInfo = OilCardViceTradesZBank::where('trade_api_id', $assignInfo['trades_sn'])->first();
                            if ($zbankInfo) {
                                $zbankInfo->update(['is_pay' => 1,'balance'=>$balance]);
                            }
                        }
                    } catch (Exception $e) {
                        Log::error($e->getCode() . '--' . $e->getMessage(), [], 'assignAudit');
                        $message = '【自动审核】亲，工单' . $assignInfo->no . '自动审核失败，请及时处理哦～';
                        //AutoAssign::sendNotify($message);


                        /*//修改工单状态为主站分配失败
                        $editData = ['remark' => $e->getMessage()];
                        if ($e->getCode() == 1103 || $e->getCode() == 3520) {
                            $editData['status'] = -1;
                        }
                        OilAccountAssign::where('id', $assignInfo['id'])->update(
                            $editData
                        );*/

                        if ($card_consume && $assignInfo['trades_sn']) {
                            // todo 定义一个一身和的特殊code码，更改订单成功，返回幂等结果
                            $zbankInfo = OilCardViceTradesZBank::where('trade_api_id', $assignInfo['trades_sn'])->first();
                            if ($zbankInfo) {
                                $zbankInfo->update(['is_pay' => 3]);
                            }
                        }

                        if ($card_consume) {
                            //发送报警
                            try {
                                (new DingTalkAlarm())->alarmToGroup('共享卡卡扣款异常', '原因：' . $e->getMessage() . PHP_EOL . 'trade_api_id:' . $assignInfo['trades_sn'], [], TRUE, TRUE);
                            } catch (\Exception $e) {

                            }
                            throw new \RuntimeException($e->getMessage(), $e->getCode());
                        }
                        $message = '【自动审核】亲，工单' . $assignInfo->no . '自动审核失败，请及时处理哦～';
                        AutoAssign::sendNotify($message);
                        throw new \RuntimeException($e->getMessage(), $e->getCode());
                    }
                }
            }
        }
    }

    /**
     * 主站圈回
     * author:mike
     */
    public function add_turnback($params = null)
    {

        if (!$params) {
            $data = \helper::filterParams();
        } else {
            $data = $params;
        }
        Log::info('添加参数--' . var_export($data, TRUE), [], 'accountBackAssign');
        $data['data_from'] = isset($data['data_from']) ? $data['data_from'] : 1;

        $data = $this->mainCommon($data);

        Capsule::connection()->beginTransaction();
        try {
            $data = (new Fuel\Service\Assign($data))
                ->formatParams()
                ->splitOrder()
                ->validatevalidateTurnBack()
                ->saveTurnBack();

            //push至G7Pay
            foreach ($data['assignIds'] as $id) {
                //add work log
                $this->addWorkLog($id);
            }

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollback();
            Log::error(__METHOD__ . strval($e), [], 'G7PayAbstractERROR');

            Log::error('msg', [$e->getMessage(), $e->getCode()], 'apiErrorLog_');

            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json(TRUE, 0, '添加成功');
    }

    /**
     * 主站圈回 公用数据
     * author mike
     */
    private function mainCommon($data)
    {
        if (isset($data['money_total_main'])) {
            $data['money_total'] = $data['money_total_main'];
        }
        if (isset($data['jifen_total_main'])) {
            $data['jifen_total'] = $data['jifen_total_main'];
        }
        if (isset($data['assign_num_main'])) {
            $data['assign_num'] = $data['assign_num_main'];
        }
        if (isset($data['remark_main'])) {
            $data['remark'] = $data['remark_main'];
        }
        if (isset($data['remark_work_main'])) {
            $data['remark_work'] = $data['remark_work_main'];
        }

        $data['orgcode']     = AccountAssignStatus::MAIN_TURN_ORG_ID;
        $orgInfo             = OilOrg::getByOrgcode($data['orgcode']);
        $data['a_org_id']    = $orgInfo->id;
        $data['is_turnback'] = 1;

        $data['account_no'] = '0';
        return $data;
    }

    /**
     * 添加workLog
     *
     * @param array $params
     * @return bool
     * @throws Exception
     */
    private function addWorkLog($assign_id)
    {
        $info = OilAccountAssign::getById(['id' => $assign_id]);
        if ($info) {
            OilCardViceAppLog::add([
                'type'             => 2,
                'app_id'           => $info->id,
                'sn'               => $info->sn,
                'status'           => 0,
                'status_name'      => AccountAssignStatus::getById(0),
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => \helper::nowTime(),
                'updatetime'       => \helper::nowTime(),
            ]);
        }
    }

    /**
     * 执行分配单保存
     *
     * @param array $params
     * @return bool
     * @throws Exception
     */
    private function _saveAssign(array $params)
    {
        \helper::argumentCheck(['assign_type', 'account_no', 'account_type'], $params);

        $addArr         = [
            'no'               => $params['no'],
            'no_type'          => 'FP',
            'org_id'           => $params['org_id'],
            'org_name'         => $params['org_name'],
            'account_type'     => $params['account_type'],
            'account_no'       => $params['account_no'],
            'money_total'      => $params['money_total'],
            'jifen_total'      => $params['jifen_total'],
            'assign_num'       => count($params['assignDetails']),
            'data_from'        => $params['data_from'],
            'remark'           => $params['remark'],
            'remark_work'      => $params['remark_work'],
            'creator_id'       => $params['creator_id'],
            'apply_time'       => $params['apply_time'],
            'createtime'       => \helper::nowTime(),
            'last_operator_id' => $this->app->myAdmin->id,
            'last_operator'    => $this->app->myAdmin->true_name,
            'other_creator_id' => isset($params['other_creator_id']) && $params['other_creator_id'] ?
                $params['other_creator_id'] : '',
            'other_creator'    => isset($params['other_creator']) && $params['other_creator'] ?
                $params['other_creator'] : '',
            'updatetime'       => \helper::nowTime(),
            'provider_flag'    => isset($params['provider_flag']) ? $params['provider_flag'] : 0,
            'status'           => isset($params['status']) ? $params['status'] : 15,
            'assign_type'      => isset($params['assign_type']) ? $params['assign_type'] : null,
        ];
        $addRegularInfo = OilAccountAssign::add($addArr);

        $status = Fuel\Defines\AccountAssignStatus::getAll();

        //添加工单日志
        OilCardViceAppLog::add([
            'type'             => 2,
            'app_id'           => $addRegularInfo->id,
            'status'           => $addArr['status'],
            'status_name'      => $status[$addArr['status']],
            'last_operator'    => $this->app->myAdmin->true_name,
            'last_operator_id' => $this->app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'updatetime'       => \helper::nowTime(),
        ]);

        $status = array_flip($status);
        foreach ($params['assignDetails'] as $value) {
            $cardOrgInfo = OilCardVice::getOrgByViceId(['id' => $value[0]]);
            $detailInfo  = [
                'assign_id'        => $addRegularInfo->id,
                'org_id'           => $cardOrgInfo->id,
                'org_name'         => $cardOrgInfo->org_name,
                'vice_id'          => $value[0],
                'assign_money'     => $value[1],
                'assign_jifen'     => $value[2],
                'truck_no'         => $value[3],
                'truck_no_custom'  => $value[4],
                'status'           => $status[$value[5]] ? $status[$value[5]] : 0,
                'creator_id'       => $this->app->myAdmin->id,
                'createtime'       => \helper::nowTime(),
                'last_operator_id' => $this->app->myAdmin->id,
                'last_operator'    => $this->app->myAdmin->true_name,
                'updatetime'       => \helper::nowTime(),
            ];
            OilAccountAssignDetails::add($detailInfo);
        }

        return $addRegularInfo;
    }

    /*
     * 批量下发
     * author tim
     */
    public function batchAssign()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['ids'], $params);

        $idArr = explode(',', $params['ids']);

        $statusArr = OilAccountAssign::whereIn('id', $idArr)->pluck('status')->toArray();

        if ($statusArr) {
            foreach ($statusArr as $status) {
                if ($status != 15) {
                    throw new RuntimeException('非主站待分配状态禁止使用批量下发', 2);
                }
            }
        }

        if ($idArr) {
            $errorNum = 0;
            foreach ($idArr as $assign_id) {
                try {
                    $this->manualAssign($assign_id);
                } catch (Exception $e) {
                    $errorNum++;
                }
            }

            $data = "总共" . count($idArr) . '条，失败' . $errorNum . '条';
        } else {
            throw new RuntimeException('参数错误', 2);
        }

        Response::json($data, 0, $data);
    }

    /*
     * 判断明细里是否有500错误
     */
    public function chekeFaild()
    {
        $params = \helper::filterParams();

        $have500 = FALSE;
        if (!isset($params['assign_id']) || intval($params['assign_id']) == 0) {
            throw new RuntimeException('分配单ID不能为空', 2);
        }

        $detailInfo = OilAccountAssignDetails::getFaildAssign($params['assign_id']);

        if ($detailInfo > 0) {
            $have500 = TRUE;
        }

        Response::json($have500);
    }

    /**
     * 手动分配
     *
     * <AUTHOR> Du
     * @since  2015/5/26
     */
    public function manualAssign($assign_id = null,$isAutoAudit=false)
    {
        if ($assign_id) {
            $params['assign_id'] = $assign_id;
        } else {
            $params = \helper::filterParams();
        }

        \helper::argumentCheck(['assign_id'], $params);

//        $isAudit = false;
//        if(isset($accountAssignInfo->org_id) && !empty($accountAssignInfo->org_id) && bccomp($accountAssignInfo->money_total,0,2) >= 0){
//            //用户审核来源
//            if($isAutoAudit == 1 || $accountAssignInfo->data_from == 1 ){
//                $isAudit = false;
//            }else{
//                $isAudit = AccountAssign::checkAssignAuth($accountAssignInfo->org_id,false,true);
//            }
//        }
//        if($isAudit){
//            throw new RuntimeException('该机构分配单需要审核', 2);
//        }

        //新增针对天津石桥卡判断是否自动下发 todo 删除
        $assignDetails = OilAccountAssignDetails::getByAssignId($params['assign_id']);
        if ($assignDetails) {
            $filterCards = ['****************'];
            foreach ($assignDetails as $v) {
                if (in_array($v->vice_no, $filterCards)) {
                    throw new RuntimeException('因此卡1000114100060450623上次已多分，不能下发分配任务，请特殊处理', 2);
                }
            }
        }

        OilAccountAssign::autoAssignTask($params);

        //push到Gos系统
        AccountAssignToGos::sendBatchUpdateTask([$assign_id]);

        if ($assign_id) {
            return TRUE;
        } else {
            Response::json(null, 0, '分配下发成功');
        }

    }

    public function checkStatus($assign_id, $status)
    {
        $flag = OilAccountAssign::Filter(['id' => $assign_id, 'status' => $status])->first();

        return $flag;
    }

    /**
     * 卡分配编辑
     *
     * <AUTHOR>
     * @since  2015/9/18
     */
    public function update()
    {
        $params = \helper::filterParams();

        $params['is_update'] = TRUE;//修改分配单操作标记
        $params['data_from'] = DataFrom::GSP;

        Log::error("update-params:".var_export($params,true),[],"accountAssign");
        Capsule::connection()->beginTransaction();
        try {
            $assignInfo = OilAccountAssign::getByIdLock(['id' => $params['id']]);
            if (in_array($assignInfo->status, AccountAssignStatus::$mayUpdateStatus)) {
                throw new RuntimeException('工单状态已变更，无法修改', 2);
            }
            if( !isset($assignInfo->true_name) || empty($assignInfo->true_name) || !isset($assignInfo->id) || empty($assignInfo->id) ) {
                global $app;
                $assignInfo->last_operator_id = $app->myAdmin->id;
                $assignInfo->last_operator = $app->myAdmin->true_name;
            }
            $data = (new Fuel\Service\Assign($params))
                ->formatParams()
                ->setAssignInfoUpdate($assignInfo)
                ->splitOrder()
                ->validate()
                ->save();

            //push至G7Pay(确认失败)
            (new AssignService())->doFailById($params['id']);
            //push至G7Pay(新建预分配)
            if ($data['assignIds']) {
                foreach ($data['assignIds'] as $id) {
                    (new AssignService())->createById($id);
                }
            }

            //删除原单
            OilAccountAssignDetails::where('assign_id', '=', $params['id'])->delete();
            OilAccountAssign::remove(['ids' => $params['id']]);

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollback();
            throw new RuntimeException($e->getMessage(), $e->getCode() ? $e->getCode() : 2);
        }

        $ids = !is_array($params['id']) ? explode(",", $params['id']) : $params['id'];
        //push到Gos系统
        AccountAssignToGos::remove($ids);
        AccountAssignToGos::sendBatchCreateTask($data['assignIds']);

        if ($data) {
            Response::json(null, 0, '更改成功');
        }
    }

    /**
     * 主站分配 卡编辑
     *
     * <AUTHOR>
     * @since  2018/7/5
     */
    public function update_turnback()
    {
        $params = \helper::filterParams();

        $params = $this->mainCommon($params);

        $params['is_update'] = TRUE;//修改分配单操作标记
        Log::error("update-params:".var_export($params,true),[],"accountAssign-back");

        Capsule::connection()->beginTransaction();
        try {
            $assignInfo = OilAccountAssign::getByIdLock(['id' => $params['id']]);
            if (in_array($assignInfo->status, AccountAssignStatus::$mayUpdateStatus)) {
                throw new RuntimeException('工单状态已变更，无法修改', 2);
            }
            if( !isset($assignInfo->true_name) || empty($assignInfo->true_name) || !isset($assignInfo->id) || empty($assignInfo->id) ) {
                global $app;
                $assignInfo->last_operator_id = $app->myAdmin->id;
                $assignInfo->last_operator = $app->myAdmin->true_name;
            }
            $data = (new Fuel\Service\Assign($params))
                ->formatParams()
                ->setAssignInfoUpdate($assignInfo)
                ->splitOrder()
                ->validatevalidateTurnBack()
                ->saveTurnBack();

            //删除原单
            OilAccountAssignDetails::where('assign_id', '=', $params['id'])->delete();
            OilAccountAssign::remove(['ids' => $params['id']]);

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollback();
            throw new RuntimeException($e->getMessage(), $e->getCode() ? $e->getCode() : 2);
        }

        if ($data) {
            Response::json(null, 0, '更改成功');
        }
    }

    /**
     * 只对备注进行修改
     */
    public function updateForRemark()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['id'], $params);
        $assign_no = OilAccountAssign::getById(['id' => $params['id']]);

        if (isset($params['remark_main'])) {
            $params['remark'] = $params['remark_main'];
        }
        if (isset($params['remark_work_main'])) {
            $params['remark_work'] = $params['remark_work_main'];
        }
        global $app;

        $updateArr = [
            'id'               => $params['id'],
            'remark'           => $params['remark'],
            'remark_work'      => $params['remark_work'],
            'last_operator_id' => $app->myAdmin->id,
            'last_operator'    => $app->myAdmin->true_name,
        ];

        //针对部分圈回的允许修改金额
        if(isset($params['money_total']) && $assign_no->status == AccountAssignStatus::ASSIGN_BACK_PART)
        {
            $updateArr['money_total'] = $params['money_total'];
            $updateArr['actual_money'] = $updateArr['money_total'];
        }

        Capsule::connection()->beginTransaction();
        try {
            $assignInfo = OilAccountAssign::edit($updateArr);

            //如果更改了金额
            if(isset($params['money_total']) && $assign_no->status == AccountAssignStatus::ASSIGN_BACK_PART){
                $detailsArr = explode('|', $params['details']);
                foreach ($detailsArr as $value){
                    $info = $info = explode('#', $value);
                    $actual_money = $info[1];
                    OilAccountAssignDetails::editByCondition(['assign_money'=>$info[1],'actual_money'=>$actual_money],['vice_no'=>$info[0],'assign_id'=>$params['id']]);
                }
            }

            if ($assignInfo) {
                //更改账户流水根据工单最新一条的备注
                $record = OilAccountMoneyRecords::where('no', '=', $assign_no->no)->orderBy('createtime', 'desc')->first();

                if ($record->id) {
                    $record->update(['remark' => $params['remark'], 'remark_work' => $params['remark_work']]);
                }
            }

            Capsule::connection()->commit();//事务提交
        } catch (Exception $e) {
            Capsule::connection()->rollback();
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        //push至Gos系统
        AccountAssignToGos::sendBatchUpdateTask([$params['id']]);

        Response::json(TRUE, 0, '更改成功');
    }

    /**
     * 通过机构和副卡号获取主卡及副卡的相关信息
     *
     * <AUTHOR>
     * @since  2015/09/29
     */
    public function getInfoByOrgViceNo()
    {
        $params  = \helper::filterParams();
        $vice_no = trim($params['vice_no']);
        $orgcode = trim($params['orgcode']);
        $org_id  = intval($params['org_id']);

        $data = $this->oil_account_assign->getInfoByOrgViceNo($vice_no, $orgcode, $org_id);

        if (!empty($data)) {
            $oil_com       = $this->loadModel('oil_card_main')->getOilCom();
            $data->oil_com = $oil_com[$data->oil_com]['oil_com'];
        }
        echo json_encode($data);
    }

    /**
     * 主站圈回时副卡号获取主卡及副卡的相关信息
     *
     * <AUTHOR>
     * @since  2015/09/29
     */
    public function getInfoByViceNo()
    {
        $params  = \helper::filterParams();
        $vice_no = trim($params['vice_no']);

        $assignInfo = (new OilCardVice)->getInfoByViceNo(['vice_no' => $vice_no]);

        $data = $this->oil_account_assign->getInfoByViceNo($vice_no, $assignInfo->org_id);

        if (!empty($data)) {
            $oil_com       = $this->loadModel('oil_card_main')->getOilCom();
            $data->oil_com = $oil_com[$data->oil_com]['oil_com'];
        }
        echo json_encode($data);
    }

    /**
     * 获取机构的可用账户余额
     *
     * <AUTHOR>
     * @since  2015/09/29
     */
    public function getOrgMoney()
    {
        $params = \helper::filterParams();
        $org_id = intval($params['org_id']);
        //可用余额
        $used_money = FrozenMoney::accountBalance($org_id);

//        $accountInfo = \Models\OilAccountMoney::where('org_id', '=', $org_id)->first();
//        $assignTotal = OilAccountAssign::where('org_id', '=', $org_id)->where('status', '=', 0)->sum('money_total');
//        $gasAssignTotal = \Models\OilGasAssignApp::where('from_org_id', '=', $org_id)->where('status', '=', 0)->sum('money');
        Response::json($used_money);
    }

    /**
     * 获取分配详情数据
     *
     * <AUTHOR>
     * @since  2015/09/29
     */
    public function getDetailInfo()
    {
        $params    = \helper::filterParams();
        $assign_id = intval($params['assign_id']);

        //实时任务状态查询
//        AutoAssign::taskStage($assign_id);

        $params['start'] = isset($params['start']) && $params['start'] ? $params['start'] : 0;
        $params['limit'] = isset($params['limit']) && $params['limit'] ? $params['limit'] : 2000;
        $limit           = [$params['start'], $params['limit']];
        $info            = $this->oil_account_assign->getDetailInfo($assign_id, $limit);
        if (isset($info->data) && !empty($info->data)) {
            $oil_com = $this->loadModel('oil_card_main')->getOilCom();
            $status  = Fuel\Defines\AccountAssignStatus::getAll();
            foreach ($info->data as $rec) {
                $rec->oil_com = $oil_com[$rec->oil_com]['oil_com'];
                if($rec->status == -1){
                    $rec->status = $rec->reject_flag == 1 ? "客户驳回" : "扣款失败";
                }else {
                    $rec->status = $rec->status == 8 ? '' : $status[$rec->status];
                }
            }
        }

        foreach ($info->data as $key=>$item)
        {
            $info->data[$key] = (array)$item;
        }

        $type = [
            'truck_no' => 13,
            'truck_no_custom' => 13,
        ];
        if(!isset($params['u']) || $params['u'] != '1'){
            $info->data = \Fuel\Service\MaskingService::maskingData($info->data,$type);
        }

        echo json_encode($info);
    }

    /**
     * 自动分配撤销
     */
    public function cancelTask()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['assign_id'], $params);

        Log::info('$params--' . var_export($params, TRUE), [], 'cancelTask');
        $taskId = OilAccountAssignTask::getTaskIds($params['assign_id']);
        Log::info('$taskId--' . var_export($taskId, TRUE), [], 'cancelTask');
        if ($taskId) {
            $result    = [];
            $updateArr = [];
            foreach ($taskId as $v) {
                //发起撤销请求
                $data = OilAgentClient::post([
                    'method' => 'crawl-provider.fuelCardService.cancleTask',
                    'data'   => [
                        'taskId' => $v,
                    ],
                ]);
                Log::info('$data--' . var_export($data, TRUE), [], 'cancelTask');

                if ($data->status == 1) {//撤销成功
                    $result[] = 10;

                    //修改任务状态
                    OilAccountAssignTask::edit(['taskId' => $v, 'status' => 15]);
                    OilAccountAssignTaskDetail::updateByTaskId(['taskId' => $v, 'status' => 15]);
                    //修改分配明细的状态
                    $tmp           = [];
                    $tmp['status'] = 15;//主站待分配
                    $detailIds     = OilAccountAssignTaskDetail::getAssignDetailIds($v);
                    $tmp['where']  = 'id in (' . implode(',', $detailIds) . ')';
                    $updateArr[]   = $tmp;
                } else {//撤销失败
                    $result[] = -10;
                }
            }
            Log::info('$updateArr--' . var_export($updateArr, TRUE), [], 'cancelTask');
            //更新分配明细中的主站分配状态
            OilAccountAssignDetails::batchEditByPdo($updateArr);

            //获取分配单的状态
            $status = OilAccountAssign::getAssignNoStatus($params['assign_id'], FALSE);
            if ($status != 20) {
                //修改分配单状态
                OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
            }

            Log::info('$result--' . var_export($result, TRUE), [], 'cancelTask');

            if (in_array(10, $result) && in_array(-10, $result)) {
                Response::json(null, 8, '撤销部分成功');
            } elseif (in_array(10, $result)) {
                Response::json(null, 8, '撤销成功');
            } elseif (in_array(-10, $result)) {
                Response::json(null, 8, '撤销失败');
            }
        } else {
            Response::json(null, 0, '没有任务可撤销');
        }
    }

    /**
     * 异常解锁
     */
    public function unlockExcept()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['assign_id'], $params);

        $status = -10;

        Capsule::connection()->beginTransaction();
        try {
            //修改分配单状态
            OilAccountAssign::edit(['id' => $params['assign_id'], 'status' => $status]);
            OilAccountAssignDetails::where('assign_id', $params['assign_id'])->where('status', -20)->update(['status' => $status]);

            //添加工单日志
            OilCardViceAppLog::add([
                'type'             => 2,
                'app_id'           => $params['assign_id'],
                'status'           => $status,
                'status_name'      => '主站分配失败-异常解锁',
                'last_operator'    => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime'       => \helper::nowTime(),
                'updatetime'       => \helper::nowTime(),
            ]);

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();

            Log::critical('异常解锁', [strval($e)], 'unlockExcept');
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json(null, 0, '异常解锁成功');
    }

    /**
     * 批量导入
     */
    public function import()
    {
        $params = \helper::filterParams();
        //上传文件
        $upload = $this->file_upload($_FILES['userfile']);
        if (is_numeric($upload) && $upload < 0) {
            if ($upload == -9) {
                $msg = "未选择文件";
            } elseif ($upload == -8) {
                $msg = "文件格式不正确";
            } else {
                $msg = "上传导入失败";
            }
            throw new RuntimeException($msg, $upload);
        }

        $fieldMap    = [
            'index'           => '序号',
            'vice_no'         => '卡号',
            'truck_no_custom' => '自录入车牌号',
            'assign_money'    => '分配金额',
            'assign_jifen'    => '分配积分',
        ];
        $excelParams = [
            'filePath'  => $upload,
            'fieldsMap' => array_flip($fieldMap),
        ];

        $result = ExcelReader::read($excelParams, function ($rowNum, $fieldName, $cellValue) use ($params) {
            return $this->preImportCellValue($rowNum, $fieldName, $cellValue, $params);
        }, function ($rowData) {
            $this->checkRowData($rowData);
        });
        $data   = [];
        if ($result[0]) {
            foreach ($result[0] as $v) {
                if ($v['vice_no']) {
                    $info   = OilCardVice::getViceInfo(['vice_no' => $v['vice_no'], 'orgcode' => $params['orgcode']]);
                    $oilCom = OilCom::getById($info->oil_com);
                    $data[] = [
                        'index'           => $v['index'],
                        'org_name'        => $info->org_name,
                        'truck_no'        => $info->truck_no,
                        'fanli_region'    => $info->province,
                        'main_no'         => $info->main_no,
                        'jifen'           => $info->jifen,
                        'vice_id'         => $info->id,
                        'vice_status'     => $info->vice_status,
                        'oil_com'         => $oilCom['name'],
                        'vice_no'         => $v['vice_no'],
                        'truck_no_custom' => $v['truck_no_custom'],
                        'assign_money'    => floatval($v['assign_money']),
                        'assign_jifen'    => floatval($v['assign_jifen']),
                    ];
                }
            }
        }

        Response::json($data);
    }

    /**
     * 校验导入的行数据
     *
     * @param $rowData
     */
    private function checkRowData($rowData)
    {
        if ($rowData['vice_no'] || $rowData['truck_no_custom'] || $rowData['assign_money'] || $rowData['assign_jifen']) {
            if (!$rowData['vice_no'])
                throw new RuntimeException('导入失败: 卡号必填');
            if (!$rowData['assign_money'] && !$rowData['assign_jifen'])
                throw new RuntimeException('导入失败: 卡号【' . $rowData['vice_no'] . '】的分配金额和分配积分不能均为空');
        }

    }

    /**
     * 单元格数据预处理
     *
     * @param $rowNum
     * @param $fieldName
     * @param $cellValue
     * @param $params
     * @return array|string
     */
    private function preImportCellValue($rowNum, $fieldName, $cellValue, $params)
    {
        $data = $cellValue;
        $msg  = '';
        if ($rowNum > 1) {
            if ($cellValue) {
                switch ($fieldName) {
                    case 'vice_no':
                        if (!preg_match('/^(\d{16}|\d{19}|\d{20})$/', $cellValue)) {
                            $msg = '导入失败: 卡号【' . $cellValue . '】格式不正确';
                        } else {
                            $orgIds = OilOrg::getByOrgcodeLike($params['orgcode']);
                            $flag   = OilCardVice::getByViceNo(['vice_no' => $cellValue, 'org_id_lk' => $orgIds, 'oil_comIn' => OilCom::getImportOilcom()]);
                            Log::info('$params', [$flag, 22], 'aaaaa');
                            if (!$flag) {
                                $msg = '导入失败: 机构【' . $params['orgname'] . '】下无卡号【' . $cellValue . '】或不支持撬装卡分配';
                            }
                        }
                        break;
                    case 'assign_money':
                        if (!preg_match('/^(-{1})?[0-9]+(.[0-9]{1,2})?$/', $cellValue)) {
                            $msg = '导入失败: 分配金额【' . $cellValue . '】格式不正确';
                        }
                        break;
                    case 'assign_jifen':
                        if (!preg_match('/^(-{1})?[0-9]+(.[0-9]{1,2})?$/', $cellValue)) {
                            $msg = '导入失败: 分配积分【' . $cellValue . '】格式不正确';
                        }
                        break;
                    default :
                        $data = $cellValue;
                }
            }
        }

        if ($msg) {
            throw new RuntimeException($msg, 2);
        }

        return $data;
    }

    /**
     * 导入文件上传
     *
     * <AUTHOR>
     * @since  2015/10/14
     */
    public function file_upload($file)
    {
        set_time_limit(0);
        if (empty($file) || empty($file['name'])) {
            //未选择文件
            return -9;
        }

        if ($file['name']) {
            $ext = strtolower(trim(substr(strrchr($file['name'], '.'), 1)));
            if ($ext != "xlsx") {
                //文件类型不正确
                return -8;
            }
        }

        $dir = '../tmp/data';
        //$dir = $this->app->getCacheRoot().'./data';
        if (!is_dir($dir)) {
            \helper::createDir($dir, 0777);
        }

        //文件上传
        $tmp_name = $file['tmp_name'];
        $newname  = $dir . '/import_' . date('m-d-H-i-s') . '.' . $ext;

        if (@copy($tmp_name, $newname)) {
            @unlink($tmp_name);
        } elseif (@move_uploaded_file($tmp_name, $newname)) {
        } elseif (@rename($tmp_name, $newname)) {
        } else {
            //上传文件失败
            return -7;
        }
        @chmod($newname, 0777);

        return $newname;
    }

    /**
     * 导出
     *
     * <AUTHOR>
     * @since  2015/11/23
     */
    public function export()
    {
        $params = \helper::filterParams();
        $ids    = str_replace(['[', ']'], '', $params['ids']);
        if ($ids) {
            if ($params['is_count']) {
                $total = $this->oil_account_assign->getDetailsByIds($ids, '', TRUE);
                echo $total;
                die;
            } else {
                //分页信息
                $page     = intval($params['page']);
                $pageSize = 5000;
                if (empty($page)) {
                    $limit = " LIMIT 0,$pageSize ";
                } else {
                    $pageStart = ($page - 1) * $pageSize;
                    $limit     = " LIMIT $pageStart,$pageSize ";
                }

                $result = $this->oil_account_assign->getDetailsByIds($ids, $limit);

                $data[] = ['卡号', '车牌号', '分配金额', '分配积分', '单号', '付款机构', '油卡类型', '积分可用地区', '主卡号'];

                $oil_com = $this->loadModel('oil_card_main')->getOilCom();

                if ($result) {
                    foreach ($result as $val) {
                        $data[] = [
                            $val->vice_no ? '="' . $val->vice_no . '"' : '',
                            $val->truck_no,
                            $val->assign_money,
                            $val->assign_jifen,
                            $val->no,
                            $val->org_name,
                            $oil_com[$val->oil_com]['oil_com'],
                            $val->province,
                            $val->main_no ? '="' . $val->main_no . '"' : '',
                        ];
                    }

                    include_once $this->modulePath . "lib/export.php";
                    $csv       = new CSV_Writer(helper::charseticonv($data));
                    $filenames = date('Y/m/d/H:i:s') . "分配申请详情-" . $page;
                    $csv->headers($filenames);
                    $csv->output();
                }
            }
        }
    }

    /**
     *导出主站分配模板
     **/
    public function exportTpl()
    {
        $params = \helper::filterParams();
        $data   = json_decode($params['ids'], TRUE);
        $number = json_decode($params['no'], TRUE);
        $result = [];
        foreach ($data as $key => $info) {
            $result[$info['main_no']][] = $info;
        }

        include_once $this->modulePath . "lib/export.php";
        foreach ($result as $key => $value) {
            $objPHPExcel = new PHPExcel();
            $i           = 1; //自增变量，用来控制行，因为标头占的第一行，所以这里从第二行开始
            $objPHPExcel->getActiveSheet()->setCellValue('a' . $i, "校验码");
            $objPHPExcel->getActiveSheet()->setCellValue('b' . $i, "副卡");
            $objPHPExcel->getActiveSheet()->setCellValue('c' . $i, "持卡人");
            $objPHPExcel->getActiveSheet()->setCellValue('d' . $i, "分配金额");
            $objPHPExcel->getActiveSheet()->setCellValue('e' . $i, "分配积分");
            $i++;
            foreach ($value as $content) {
                $pin          = $content['pin'];
                $vice_no      = $content['vice_no'];
                $card_owner   = $content['card_owner'];
                $assign_money = floatval($content['assign_money']) ? $content['assign_money'] : '';
                $assign_jifen = floatval($content['assign_jifen']) > 0 ? $content['assign_jifen'] : '';
                $objPHPExcel->getActiveSheet()->setCellValue('a' . $i, "$pin");

                $objPHPExcel->getActiveSheet()->setCellValueExplicit('b' . $i, $vice_no, PHPExcel_Cell_DataType::TYPE_STRING);
                $objPHPExcel->getActiveSheet()->getStyle('b' . $i)->getNumberFormat()->setFormatCode("@");

//                $objPHPExcel->getActiveSheet()->setCellValue('b' . $i, chunk_split("$vice_no", 30));
                $objPHPExcel->getActiveSheet()->setCellValue('c' . $i, "$card_owner");
                $objPHPExcel->getActiveSheet()->setCellValue('d' . $i, "$assign_money");
                $objPHPExcel->getActiveSheet()->setCellValue('e' . $i, "$assign_jifen");
                $i++;
            }
            $objActSheet = $objPHPExcel->getActiveSheet();
            $objActSheet->setTitle(strval($key));
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename  = $_SERVER['DOCUMENT_ROOT'] . '/download/' . $number . '_' . $key . '.xlsx';
            $objWriter->save($filename);//保存生成
            $file[] = $filename;
        }

        if (count($file) > 1) {
            $zip      = new ZipArchive();
            $filename = $this->app->getAppRoot() . 'www' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . $number . '.zip';
            echo $filename;
            if ($zip->open($filename, ZIPARCHIVE::CREATE) !== TRUE) {
                exit("文件打开失败!");
            }
            //将文件添加到压缩文件中
            foreach ($file as $path) {
                $file_info_arr = pathinfo($path);
                $zip->addFile($path, $file_info_arr['basename']);
            }
            echo "文件数 : " . $zip->numFiles;
            $zip->close();
        } else {
            $filename = $file[0];
        }

        //下载压缩文件
        $example_name = basename($filename);  //获取文件名
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename=' . mb_convert_encoding($example_name, "gb2312", "utf-8"));  //转换文件名的编码
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
    }

    /**
     *检查校验码是否存在
     **/
    function checkPinExist($data)
    {
        foreach ($data as $value) {
            if ($value['pin'] == '') {
                $result = '校验码不能为空!!!';
                $data   = ['success' => FALSE, 'msg' => $result];
                echo json_encode($data);
                exit;
            }
            if ($value['card_owner'] == '') {
                $result = '持卡人不能为空!!!';
                $data   = ['success' => FALSE, 'msg' => $result];
                echo json_encode($data);
                exit;
            }
        }
        $result = '确认导出!!!';
        $data   = ['success' => TRUE, 'msg' => $result];
        echo json_encode($data);


    }


    function exporttplrequest()
    {
        $params = \helper::filterParams();
        $data   = json_decode($params['data'], TRUE);
        if (!$data) {
            $data = ['success' => FALSE, 'msg' => '未发现非主站分配成功的卡'];
            echo json_encode($data);
            exit;
        }
        $this->checkPinExist($data);
    }

    /**
     * 查询主站分配记录 old method
     */
    public function checkAssign_bak()
    {
        $taskId = null;
        $params = [];
        Log::error('begin-', [], 'checkAssignDebug');

        $checkAssignData = OilAccountAssign::getCheckAssignData();
        if ($checkAssignData) {
            foreach ($checkAssignData as $assignId => $value) {
                if ($value) {
                    foreach ($value as $cardMain => $taskParams) {
                        $cardType        = $taskParams['cardType'];
                        $assignDetailIds = $taskParams['assignDetailIds'];
                        unset($taskParams['cardType'], $taskParams['assignDetailIds']);

                        $params[] = $taskParams;
                        try {
                            $apiParams = [
                                'method' => 'crawl-provider.fuelCardService.query',
                                'data'   => [
                                    'cardtype' => $cardType,
                                    'account'  => $taskParams['account'],
                                    'level'    => 1,
                                    'callback' => '',
                                    'params'   => json_encode($taskParams),
                                ],
                            ];
                            Log::error('发送参数:$apiParams-' . var_export($apiParams, TRUE), [], 'checkAssignDebug');

                            $taskId = OilAgentClient::post($apiParams);

                            Log::error('发送后结果:taskId-' . var_export($taskId, TRUE), [$taskId], 'checkAssignDebug');

                            if ($taskId) {
                                Log::error('OilAccountAssignDetails::editByIds-' . var_export(['idList' => $assignDetailIds, 'check_assign' => 5, 'task_id' => $taskId], TRUE), [$taskId], 'checkAssignDebug');

                                OilAccountAssignDetails::editByIds(['idList' => $assignDetailIds, 'check_assign' => 5, 'task_id' => $taskId]);

                                Log::error('OilAccountAssign::edit-' . var_export(['id' => $assignId, 'check_assign' => 5], TRUE), [$taskId], 'checkAssignDebug');

                                OilAccountAssign::edit(['id' => $assignId, 'check_assign' => 5]);
                            }
                        } catch (Exception $e) {
                            Log::error('checkAssignError:' . strval($e), [$value], 'checkAssignDebug');
                        }
                    }
                }

            }
        }

        Response::json(['taskId' => $taskId, 'params' => $params]);
    }

    /**
     * 查询主站分配记录 new method
     */
    public function checkAssign()
    {
        $taskId = null;
        $params = [];
        Log::error('begin-', [], 'checkAssignDebug');

        $checkAssign     = OilAccountAssign::getCheckAssignData();
        $checkAssignData = $checkAssign['checkData'];
        if ($checkAssignData) {
            foreach ($checkAssignData as $cardMain => $taskParams) {
                $sliceSize = $taskParams['cardType'] == 'zsy' ? 15 : 5; //中石油支持一次查询15张副卡， 中石化5张

                $cardListSlice        = array_chunk(array_unique($taskParams['cardList']), $sliceSize);
                $assignDetailIdsSlice = array_chunk(array_unique($taskParams['assignDetailIds']), $sliceSize);
                $cardType             = $taskParams['cardType'];
                unset($taskParams['cardType'], $taskParams['assignDetailIds']);
                foreach ($cardListSlice as $key => $cardList) {
                    $taskParams['cardList'] = $cardList;
                    $assignDetailIds        = $assignDetailIdsSlice[$key];
                    $params[]               = $taskParams;
                    try {
                        $apiParams = [
                            'method' => 'crawl-provider.fuelCardService.query',
                            'data'   => [
                                'cardtype' => $cardType,
                                'account'  => $taskParams['account'],
                                'level'    => 1,
                                'callback' => '',
                                'params'   => json_encode($taskParams),
                            ],
                        ];
                        Log::error('发送参数:$apiParams-' . var_export($apiParams, TRUE), [], 'checkAssignDebug');

                        $taskId = OilAgentClient::post($apiParams);

                        Log::error('发送后结果:taskId-' . var_export($taskId, TRUE), [$taskId], 'checkAssignDebug');

                        if ($taskId) {
                            Log::error('OilAccountAssignDetails::editByIds-' . var_export(['idList' => $assignDetailIds, 'check_assign' => 5, 'task_id' => $taskId], TRUE), [$taskId], 'checkAssignDebug');

                            OilAccountAssignDetails::editByIds(['idList' => $assignDetailIds, 'check_assign' => 5, 'task_id' => $taskId]);

                            Log::error('OilAccountAssign::edit-' . var_export(['id' => $checkAssign['assignIds'], 'check_assign' => 5], TRUE), [$taskId], 'checkAssignDebug');
                        }
                    } catch (Exception $e) {
                        Log::error('checkAssignError:' . strval($e), [$taskParams], 'checkAssignDebug');
                        //todo dingding note
                    }
                }

            }

            //修改工单的check_assign = 5
            OilAccountAssign::whereIn('id', $checkAssign['assignIds'])->update(['check_assign' => 5]);
        }

        Response::json(['taskId' => $taskId, 'params' => $params]);
    }

    /**
     * @title 发起实时对账,请求成都接口
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function pushAssignToDsp()
    {
        echo date('Y-m-d H:i:s') . ':';
        $params = \helper::filterParams();
//        $yesterday = strtotime("-100 day",time());
//        $start_time = date("Y-m-d ",$yesterday)." 00:00:00";
//        $end_time = date( "Y-m-d H:i:s",strtotime("+1 day",strtotime($start_time)) );
//
//        //测试
//        $start_time = '2018-01-11 00:00:00';
//        $end_time = '2018-09-12 00:00:00';
        //dev
        //$start_time = isset($params['start_time']) ? $params['start_time'] : date("Y-m-d ",time())." 00:00:00";
        $start_time = isset($params['start_time']) ? $params['start_time'] : date("Y-m-d ", strtotime("-1 day", time())) . " 00:00:00";
        $end_time   = isset($params['end_time']) ? $params['end_time'] : date('Y-m-d H:i:s');

        Cache::forever('query_begin_time', $start_time);
        Cache::forever('query_end_time', $end_time);

        $assignData = OilAccountAssign::getCompareData($start_time, $end_time);

        $checkAssignData = $assignData['checkData'];
        if ($checkAssignData) {
            $cardMainIds = [];
            foreach ($checkAssignData as $cardMain => $taskParams) {
                //$sliceSize = $taskParams['cardType'] == 'zsy' ? 1000 : 800; //中石油支持一次查询15张副卡， 中石化5张

                //$cardListSlice = array_chunk(array_unique($taskParams['cardList']),$sliceSize);
                $cardType = $taskParams['cardType'];
                unset($taskParams['cardType'], $taskParams['assignDetailIds']);
                //foreach ($cardListSlice as $key=>$cardList){
                //$taskParams['cardList'] = $cardList;
                $params[] = $taskParams;
                try {
                    $cardList = $taskParams['cardList'];
                    unset($taskParams['cardList']);
                    $apiParams = [
                        'method' => 'crawl-provider.fuelCardService.query',
                        'data'   => [
                            'cardtype' => $cardType,
                            'account'  => $taskParams['account'],
                            'level'    => 1,
                            'callback' => '',
                            'params'   => json_encode($taskParams),
                        ],
                    ];

                    Log::error('发送参数:$apiParams-' . var_export($apiParams, TRUE), [], 'pushAssignToDsp');

                    $taskId = OilAgentClient::post($apiParams);

                    //$taskId = Helper::uuid();
                    echo $taskId . '|';
                    $insertData = [];
                    //if ($taskId) {
                    $insertData['task_id'] = $taskId ? $taskId : null;
                    $insertData['main_no'] = $taskParams['parentcard'];
                    foreach ($cardList as $vice_no) {
                        $insertData['vice_no'] = $vice_no;
                        OilAssignCompare::add($insertData);
                    }

                    Log::error('发送后结果:taskId-' . var_export($taskId, TRUE), [$taskId], 'pushAssignToDsp');
                    //}
                } catch (Exception $e) {
                    Log::error('checkAssignError:' . strval($e), [$apiParams], 'pushAssignToDsp');
                }
            }
        }


        //15天挂失卡的分配记录
        Log::error('发送loss-start', [], 'pushAssignToDsp');
        $lossData = OilAccountAssign::getMainByCardLoss($start_time, $end_time, $cardMainIds);

        $checkLossData = $lossData['checkData'];
        if ($checkLossData) {
            foreach ($checkLossData as $cardMain => $taskParams) {
                //$sliceSize = $taskParams['cardType'] == 'zsy' ? 1000 : 800; //中石油支持一次查询15张副卡， 中石化5张

                //$cardListSlice = array_chunk(array_unique($taskParams['cardList']),$sliceSize);
                $cardType = $taskParams['cardType'];
                unset($taskParams['cardType'], $taskParams['assignDetailIds']);
                //foreach ($cardListSlice as $key=>$cardList){
                //$taskParams['cardList'] = $cardList;
                $params[] = $taskParams;
                try {
                    $cardList = $taskParams['cardList'];
                    unset($taskParams['cardList']);
                    $apiParams = [
                        'method' => 'crawl-provider.fuelCardService.query',
                        'data'   => [
                            'cardtype' => $cardType,
                            'account'  => $taskParams['account'],
                            'level'    => 1,
                            'callback' => '',
                            'params'   => json_encode($taskParams),
                        ],
                    ];

                    Log::error('发送loss参数:$apiParams-' . var_export($apiParams, TRUE), [], 'pushAssignToDsp');

                    $taskId = OilAgentClient::post($apiParams);

                    //$taskId = Helper::uuid();
                    echo $taskId . '|';
                    $insertData = [];
                    //if ($taskId) {
                    $insertData['task_id'] = $taskId ? $taskId : null;
                    $insertData['main_no'] = $taskParams['parentcard'];
                    foreach ($cardList as $vice_no) {
                        $insertData['vice_no'] = $vice_no;
                        OilAssignCompare::add($insertData);
                    }

                    Log::error('loss结果:taskId-' . var_export($taskId, TRUE), [$taskId], 'pushAssignToDsp');
                    //}
                } catch (Exception $e) {
                    Log::error('lossError:' . strval($e), [$apiParams], 'pushAssignToDsp');
                }
            }
        }
    }

    /**
     * 自动分配流水查询回调处理
     */
    public function checkAssignCallback()
    {
        $params = \helper::filterParams();
//        $params = [
//            'result' => 0,   'responseResult' => '{"result":0,"data":"","message":"成功"}',   'taskType' => 'zsyAssignTemporaryCrawler',   'time' => '2018-12-18 17:32:02',   'message' => '成功',   'taskId' => '2e5a1ce177a14a50ac56557f717698fa',
//        ];
        Log::info('ApiParams：--' . var_export($params, TRUE), [], 'checkAssignCallback_');
        $_callbackData = json_decode($params['responseResult'], TRUE);
        Log::info('ApiParams：-responseResult-' . var_export($_callbackData, TRUE), 'checkAssignCallback_');

        if ($_callbackData && isset($_callbackData['data'])) {
            $callbackData = $_callbackData;
        } else {
            Log::dataLog('ERROR | oil_account_assign | checkAssignCallback | $callbackData中无data:' . var_export
                ($params, TRUE), 'gasAgent-callBack');
            throw new RuntimeException('responseResult中无data', 5);
        }

        if ($callbackData) {
            $assignDetails = OilAccountAssign::getCallbackDetails(['taskId' => $params['taskId']]);
            //Log::info('$params-taskId' . var_export($params['taskId'], TRUE),[], 'txb');
            //Log::info('$params-callbackData' . var_export($callbackData, TRUE),[], 'txb');
            //Log::info('$assignDetails' . var_export($assignDetails, TRUE),[], 'txb');
            if (isset($callbackData['data']) && $callbackData['data']) {
                $callbackData = $callbackData['data'];

                if ($assignDetails) {
                    $reSendAssign = [];
                    foreach ($assignDetails as $details) {
                        $count = $checkTotal = 0;
                        $index = null;
                        foreach ($callbackData as $key => $callback) {
                            $assignMoney = $details['assign_money'];
                            if ($details['card_vice']['oil_com'] == 2 && $assignMoney < 0 && $callback['traName'] == "单位汇总(出)") {
                                $assignMoney = abs($assignMoney);
                            }

                            if ($details['card_vice']['vice_no'] == $callback['cardNo'] && (bccomp($assignMoney * 100, $callback['amount'] * 100) == 0)
                                && ($callback['opeTime'] / 1000 > strtotime($details['callback_time']) - 8 * 60)
                                && ($callback['opeTime'] / 1000 < strtotime($details['callback_time_end']) + 8 * 60)
                            ) {
                                //Log::info('vice_no:' . $callback['cardNo'].' money:'.$callback['amount'].'is same', [],'txb');
                                $count++;
                                $index = $key;
                            }

                            //返回的能匹配到卡说明这张卡有分配
                            if ($details['card_vice']['vice_no'] == $callback['cardNo']) {
                                $checkTotal++;
                            }

                        }
                        if ($count == 1) {
                            unset($callbackData[$index]);
                            //Log::info('index:'.$index, [],'txb');
                            //Log::info('check_assign:10,vice_no:' . $details['card_vice']['vice_no'].'details_id:'.$details['id'], [],'txb');
                            OilAccountAssignDetails::edit(['id' => $details['id'], 'check_assign' => 10]);//校验成功
                        } else {
                            //Log::info('check_assign:-10,vice_no:' . $details['card_vice']['vice_no'].'details_id:'.$details['id'], [],'txb');
                            OilAccountAssignDetails::edit(['id' => $details['id'], 'check_assign' => -10]);//校验失败
                        }

                        //if($checkTotal == 0 && strpos($details['assign_message'],'余额无变化') !== false){
                        if ($checkTotal == 0) {
                            if ($details['check_num'] == 0) {
                                //分配中改为待校验，等待下一轮的校验
                                OilAccountAssignDetails::edit(['id' => $details['id'], 'check_assign' => 0]);
                            } else {
                                OilAccountAssignDetails::edit(['id' => $details['id'], 'status' => -10, 'check_assign' => -10]);//为了系统重新下发
                                $reSendAssign[] = $details['assign_id'];
                            }
                        }

                        //更新工单校验状态
                        $checkStatus = $this->editCheckAssign($details['assign_id']);

                        //根据校验状态判断是否自动审核
                        //1首先判断总开关配置是否开启
                        $assignInfo = OilAccountAssign::getById(['id' => $details['assign_id']]);
                        $is_open    = OilConfigure::getBySysKey('auto_assign_switch');
                        if ($checkStatus && $checkStatus == 10 && intval($is_open) == 2) {
                            $_POST['id']          = $details['assign_id'];
                            $_POST['isAutoAudit'] = 1;
                            try {
                                $this->auditBy();
                            } catch (Exception $e) {
                                Log::error($e->getCode() . '--' . $e->getMessage(), [], 'assignAudit');
                                $message = '【自动审核】亲，工单' . $assignInfo->no . '自动审核失败，请及时处理哦～';
                                AutoAssign::sendNotify($message);
                            }
                        } else {
                            if ($assignDetails) {
                                //如果是客户提交单子通知客服
                                NoticeSender::sendAll([
                                    'message' => '【分配单】'
                                        . '，单号：' . $assignInfo->no . '，未能自动分配，'
                                        . '，卡数：' . $assignInfo->assign_num
                                        . '，现金：' . ($assignInfo->money_total - $assignInfo->use_cash_fanli)
                                        . '，返利：' . $assignInfo->use_cash_fanli
                                        . '，积分：' . $assignInfo->jifen_total
                                ]);
                            }

                        }

                        $detailsIds[] = $details['id'];
                    }

                    //分配失败更改工单状态
                    if ($reSendAssign) {
                        foreach (array_unique($reSendAssign) as $re_send_assign_id) {

                            $status = OilAccountAssign::getAssignNoStatus($re_send_assign_id, FALSE);
                            //OilAccountAssign::edit(['id' => $re_send_assign_id, 'status' => $status]);
                            OilAccountAssign::where('id',$re_send_assign_id)
                                ->where('status','!=',1)
                                ->update(['status'=>$status]);

                        }
                    }

                    //分配失败-1的这种情况实行重新下发分配动作
//                    if($reSendAssign && in_array($params['taskType'],['zsyAssignTemporaryCrawler'])){
//                        foreach(array_unique($reSendAssign) as $re_send_assign_id){
//                            try{
//                                $sendCount = Cache::get('reSend'.$re_send_assign_id) ? Cache::get('reSend'.$re_send_assign_id) : 1;
//                                if($sendCount <= 5){
//                                    OilAccountAssign::autoAssignTask(['assign_id'=>$re_send_assign_id,'key_type'=>'autoAssign']);
//                                    Log::error('reSend:assign_id:'.$re_send_assign_id.'执行次数'.$sendCount.'taskParams'.var_export($params,TRUE),[],'reSendTask');
//                                    Cache::put('reSend'.$re_send_assign_id,$sendCount+1,86400);
//                                }
//                            }catch (Exception $e){
//                                Log::error('reSend:Exception:'.strval($e),[],'reSendTask');
//                            }
//                        }
//                    }

                    //更改明细回调次数
                    OilAccountAssignDetails::whereIn('id', $detailsIds)->increment("check_num");

                } else {
                    Log::info('taskId details_id is null', [], 'txb');
                }
                //如果查出的data为空了，说明他们没有查出数据来
            } else {
                if ($assignDetails) {
                    foreach ($assignDetails as $details) {
//                        if(strpos($details['assign_message'],'余额无变化') !== false){
//                            $reSendAssign[] = $details['assign_id'];
//                            OilAccountAssignDetails::edit(['id' => $details['id'], 'status'=>-10, 'check_assign' => 0]);//为了系统重新下发
//                        }else{
//                            OilAccountAssignDetails::edit(['id' => $details['id'], 'check_assign' => 20]);//校验数据为空
//                        }

                        if ($details['check_num'] == 0) {
                            //分配中改为待校验，等待下一轮的校验
                            OilAccountAssignDetails::edit(['id' => $details['id'], 'check_assign' => 0]);
                        } else {
                            //取消20校验无数据的状态
                            $reSendAssign[] = $details['assign_id'];
                            if (in_array($params['taskType'], ['zsyAssignTemporaryCrawler'])) {
                                OilAccountAssignDetails::edit(['id' => $details['id'], 'status' => -10, 'check_assign' => -10, 'assign_message' => '分配失败，请重新分配']);//为了系统重新下发
                            } else {
                                OilAccountAssignDetails::edit(['id' => $details['id'], 'status' => -10, 'check_assign' => -10, 'assign_message' => '分配失败，请重新分配']);//为了系统重新下发
                            }
                        }

                        //更新工单校验状态
                        $checkStatus = $this->editCheckAssign($details['assign_id']);

                        //根据校验状态判断是否自动审核
                        //1首先判断总开关配置是否开启
                        $assignInfo = OilAccountAssign::getById(['id' => $details['assign_id']]);
                        $is_open    = OilConfigure::getBySysKey('auto_assign_switch');
                        if ($checkStatus && $checkStatus == 10 && intval($is_open) == 2) {
                            $_POST['id']          = $details['assign_id'];
                            $_POST['isAutoAudit'] = 1;
                            try {
                                $this->auditBy();
                            } catch (Exception $e) {
                                Log::error($e->getCode() . '--' . $e->getMessage(), [], 'assignAudit');
                                $message = '【自动审核】亲，工单' . $assignInfo->no . '自动审核失败，请及时处理哦～';
                                AutoAssign::sendNotify($message);
                            }
                        } else {
                            if ($assignDetails) {
                                //如果是客户提交单子通知客服
                                NoticeSender::sendAll([
                                    'message' => '【分配单】'
                                        . '，单号：' . $assignInfo->no . '，未能自动分配，'
                                        . '，卡数：' . $assignInfo->assign_num
                                        . '，现金：' . ($assignInfo->money_total - $assignInfo->use_cash_fanli)
                                        . '，返利：' . $assignInfo->use_cash_fanli
                                        . '，积分：' . $assignInfo->jifen_total
                                ]);
                            }

                        }
                        //明细id
                        $detailsIds[] = $details['id'];
                    }

                    //分配失败更改工单状态
                    if ($reSendAssign) {
                        foreach (array_unique($reSendAssign) as $re_send_assign_id) {

                            $status = OilAccountAssign::getAssignNoStatus($re_send_assign_id, FALSE);
                            //OilAccountAssign::edit(['id' => $re_send_assign_id, 'status' => $status]);
                            OilAccountAssign::where('id',$re_send_assign_id)
                                ->where('status','!=',1)
                                ->update(['status'=>$status]);

                        }
                    }

                    //分配失败-1的这种情况实行重新下发分配动作
//                    if($reSendAssign && in_array($params['taskType'],['zsyAssignTemporaryCrawler'])){
//                        foreach(array_unique($reSendAssign) as $re_send_assign_id){
//                            try{
//                                $sendCount = Cache::get('reSend'.$re_send_assign_id) ? Cache::get('reSend'.$re_send_assign_id) : 1;
//                                //echo $sendCount;
//                                if($sendCount <= 5){
//                                    if($sendCount == 5){
//                                        //执行第5次的时候，分配单的状态变成分配失败
//                                        OilAccountAssign::where('id',$re_send_assign_id)->update(['status'=> -10,'remark'=>'自动执行第5次分配改为分配失败']);
//                                    }else{
//                                        OilAccountAssign::autoAssignTask(['assign_id'=>$re_send_assign_id,'key_type'=>'autoAssign']);
//                                    }
//                                    Log::error('reSend:assign_id:'.$re_send_assign_id.'执行次数'.$sendCount.'taskParams'.var_export($params,TRUE),[],'reSendTask');
//                                    Cache::put('reSend'.$re_send_assign_id,$sendCount+1,86400);
//                                }
//                            }catch (Exception $e){
//                                Log::error('reSend:Exception:'.strval($e),[],'reSendTask');
//                            }
//                        }
//                    }

                    //更改明细回调次数
                    OilAccountAssignDetails::whereIn('id', $detailsIds)->increment("check_num");
                }
                throw new RuntimeException('responseResult中data为空', 5);
                //return;
            }

        }
    }

    /**
     * 自动分配流水查询回调处理
     */
    public function testCallBack()
    {
//        $params = \helper::filterParams();
        $params = [
            'result'         => 0,
            'responseResult' => '{"data":[{"accountType":"备用金","amount":239.0,"cardHolder":"","cardIntegral":0.0,"cardNo":"****************","cardType":"zsy","dateCreated":*************,"lastUpdated":*************,"loyaltyAndBalance":319.0,"nodeTag":"重庆渝中销售分公司网上充值点","opeTime":*************,"parentCard":"****************","traName":"单位分配(进)","tradeStatus":"正常"},{"accountType":"备用金","amount":1000.0,"cardHolder":"","cardIntegral":0.0,"cardNo":"****************","cardType":"zsy","dateCreated":*************,"lastUpdated":*************,"loyaltyAndBalance":1000.0,"nodeTag":"重庆渝中销售分公司网上充值点","opeTime":*************,"parentCard":"****************","traName":"单位分配(进)","tradeStatus":"正常"},{"accountType":"备用金","amount":800.0,"cardHolder":"","cardIntegral":0.0,"cardNo":"****************","cardType":"zsy","dateCreated":*************,"lastUpdated":*************,"loyaltyAndBalance":23000.0,"nodeTag":"重庆渝中销售分公司网上充值点","opeTime":*************,"parentCard":"****************","traName":"单位分配(进)","tradeStatus":"正常"},{"accountType":"备用金","amount":500.0,"cardHolder":"","cardIntegral":0.0,"cardNo":"****************","cardType":"zsy","dateCreated":*************,"lastUpdated":*************,"loyaltyAndBalance":500.0,"nodeTag":"重庆渝中销售分公司网上充值点","opeTime":*************,"parentCard":"****************","traName":"单位分配(>进)","tradeStatus":"正常"},{"accountType":"备用金","amount":2000.0,"cardHolder":"","cardIntegral":0.0,"cardNo":"****************","cardType":"zsy","dateCreated":*************,"lastUpdated":*************,"loyaltyAndBalance":2000.0,"nodeTag":"重庆渝中销售分公司网上充值点","opeTime":*************,"parentCard":"****************","traName":"单位分配(进)","tradeStatus":"正常"}],"result":0,"message":"成功"}',
            'taskType'       => 'zsyAssignTemporaryCrawler',
            'time'           => '2018-07-31 11:03:53',
            'message'        => '成功',
            'taskId'         => '3b6269cf565d4e2fa983654f759c5869'
        ];
        Log::info('ApiParams：--' . var_export($params, TRUE), [], 'checkAssignCallback_');
        $_callbackData = json_decode($params['responseResult'], TRUE);
        Log::info('ApiParams：-responseResult-' . var_export($_callbackData, TRUE), 'checkAssignCallback_');

        if ($_callbackData && isset($_callbackData['data'])) {
            $callbackData = $_callbackData;
        } else {
            Log::dataLog('ERROR | oil_account_assign | checkAssignCallback | $callbackData中无data:' . var_export
                ($params, TRUE), 'gasAgent-callBack');
            throw new RuntimeException('responseResult中无data', 5);
        }

        if ($callbackData) {
            $assignDetails = OilAccountAssign::getCallbackDetails(['taskId' => $params['taskId']]);

            if (isset($callbackData['data']) && $callbackData['data']) {
                $callbackData = $callbackData['data'];

                if ($assignDetails) {
                    foreach ($assignDetails as $details) {
                        $count = 0;
                        $index = null;
                        foreach ($callbackData as $key => $callback) {
                            $assignMoney = $details['assign_money'];
                            if ($details['card_vice']['oil_com'] == 2 && $assignMoney < 0 && $callback['traName'] == "单位汇总(出)") {
                                $assignMoney = abs($assignMoney);
                            }

                            if ($details['card_vice']['vice_no'] == $callback['cardNo'] && $assignMoney == $callback['amount']
                                && ($callback['opeTime'] / 1000 > strtotime($details['callback_time']) - 8 * 60)
                                && ($callback['opeTime'] / 1000 < strtotime($details['callback_time']) + 8 * 60)
                            ) {
                                $count++;
                                $index = $key;
                            }
                        }
                        if ($count == 1) {
                            unset($callbackData[$index]);
                            var_dump('10', $details['card_vice']['vice_no'], $index, $details['id']);
                            //OilAccountAssignDetails::edit(['id' => $details['id'], 'check_assign' => 10]);//校验成功
                        } else {
                            var_dump('-10', $index, $details['card_vice']['vice_no'], $details['id']);
                            //OilAccountAssignDetails::edit(['id' => $details['id'], 'check_assign' => -10]);//校验失败
                        }

                        //更新工单校验状态
                        //$checkStatus = $this->editCheckAssign($details['assign_id']);
                        //var_dump('-$checkStatus',$checkStatus);exit;

                    }
                    exit;
                }
                //如果查出的data为空了，说明他们没有查出数据来
            } else {
                throw new RuntimeException('responseResult中data为空', 5);
                //return;
            }

        }
    }

    /**
     * 更新工单校验状态
     *
     * @param $assign_id
     */
    private function editCheckAssign($assign_id)
    {
        $assignInfo  = OilAccountAssign::getById(['id' => $assign_id]);
        $no          = $assignInfo->no;
        $arr         = [];
        $detailsInfo = OilAccountAssignDetails::where('assign_id', '=', $assign_id)->select('check_assign', 'id')->get()->toArray();

        if ($detailsInfo) {
            foreach ($detailsInfo as $info) {
                $arr[] = $info['check_assign'];
            }
        }

        $arr = array_unique($arr);

        $checkStatus = null;
        Log::dataLog('detail_status_arr:' . var_export($arr, TRUE) . '|assign_id:' . $assign_id, 'checkStatus');
        if (in_array(-10, $arr)) {
            Log::dataLog('fail', 'checkStatus');
            $checkStatus = -10;
        } elseif (in_array(20, $arr)) {
            Log::dataLog('no data', 'checkStatus');
            $checkStatus = 20;
        } elseif (in_array(5, $arr)) {
            Log::dataLog('checking', 'checkStatus');
            $checkStatus = 5;
        } elseif (in_array(0, $arr)) {
            Log::dataLog('not check', 'checkStatus');
            $checkStatus = 0;
        } elseif (in_array(10, $arr)) {
            Log::dataLog('seccess', 'checkStatus');
            $checkStatus = 10;
        }

        OilAccountAssign::edit(['id' => $assign_id, 'check_assign' => $checkStatus]);
        $this->checkAssignCallBackToEWei($no, $checkStatus);
//        else {
//            Log::dataLog('seccess', 'checkStatus');
//            OilAccountAssign::edit(['id' => $assign_id, 'check_assign' => 10]);
//        }
        return $checkStatus;
    }

    /**
     * 自动审核校验状态到易维
     *
     * @param $no
     * @param $status
     */
    private function checkAssignCallBackToEWei($no, $status)
    {
        /********************推送到易维系统************************/
        $pms['tb_no']     = $no;
        $pms['ew_status'] = 'open';
        $pms['tb_name']   = 'oil_account_assign';
        $pms['content']   = '自动校验 - ' . CheckAssignStatus::getById($status);//'自动分配状态回写';
        (new EWei)->editWorkOrder($pms);
    }

    /**
     * 工单概要统计
     */
    public function accountAssignStats()
    {
        $params = \helper::filterParams();

        $data = OilAccountAssign::accountAssignStats($params);

        Response::json($data);
    }

    /**
     * 卡分配概要统计
     */
    public function accountAssignDetailStats()
    {
        $params = \helper::filterParams();

        $data = OilAccountAssignDetails::accountAssignDetailStats($params);

        Response::json($data);
    }

    /**
     *
     */
    public function accountAssignCheckStats()
    {
        $params = \helper::filterParams();

        $data = OilAccountAssignDetails::accountAssignDetailCheckStats($params);

        Response::json($data);
    }

    /**
     * @api statsView
     * 按分配单统计分配情况
     */
    public function statsViewAccountAssign()
    {
        $params = \helper::filterParams();
        $data   = [];
        $_data  = OilAccountAssign::accountAssignStats($params);

        $allStatus = AccountAssignStatus::getAll();
        foreach ($allStatus as $k => $v) {
            $data[$k] = [
                'group'  => 'accountAssign_' . $k,
                'domain' => 'zqx',
                'key'    => $v,
                'op'     => 'sum',
                'sum'    => 0,
            ];
        }

        $total = 0;
        if (count($_data) > 0) {
            foreach ($_data as $v) {
                $total             += $v->total;
                $data[$v->_status] = [
                    'group'  => 'accountAssign_' . $v->_status,
                    'domain' => 'zqx',
                    'key'    => AccountAssignStatus::getById($v->_status),
                    'op'     => 'sum',
                    'sum'    => $v->total,
                ];
            }

            $data['total'] = [
                'group'  => 'accountAssign_total',
                'domain' => 'zqx',
                'key'    => '分配单总数',
                'op'     => 'sum',
                'sum'    => $total,
            ];
        }

        Response::json(array_values($data));
    }

    /**
     * @api statsView
     * 按分配单统计分配情况
     */
    public function statsViewAccountAssignDetail()
    {
        $params = \helper::filterParams();
        $data   = [];
        $_data  = OilAccountAssignDetails::accountAssignDetailStats($params);

        $oilCom    = OilCom::getAutoAssignType();
        $allStatus = AccountAssignStatus::getAll();
        foreach ($oilCom as $v) {
            $_oilCom = OilCom::getById($v);
            foreach ($allStatus as $a => $b) {
                $data[$v . '_' . $a] = [
                    'group'  => 'accountAssignDetail_' . $v . '_' . $a,
                    'domain' => 'zqx',
                    'key'    => $_oilCom['name'] . $b,
                    'op'     => 'sum',
                    'sum'    => 0,
                ];
            }
        }

        $total     = 0;
        $zsh_total = 0;
        $zsy_total = 0;
        if (count($_data) > 0) {
            foreach ($_data as $v) {
                $total   += $v->total;
                $_oilCom = OilCom::getById($v->_oil_com);
                if ($v->_oil_com == 1) {
                    $zsh_total += intval($v->total);
                } elseif ($v->_oil_com == 2) {
                    $zsy_total += intval($v->total);
                }
                $data[$v->_oil_com . '_' . $v->_status] = [
                    'group'  => 'accountAssignDetail_' . $v->_oil_com . '_' . $v->_status,
                    'domain' => 'zqx',
                    'key'    => $_oilCom['name'] . AccountAssignStatus::getById($v->_status),
                    'op'     => 'sum',
                    'sum'    => $v->total,
                ];
            }

            $data['total_1'] = [
                'group'  => 'accountAssignDetail_1',
                'domain' => 'zqx',
                'key'    => '中石化分配卡总数',
                'op'     => 'sum',
                'sum'    => $zsh_total,
            ];

            $data['total_2'] = [
                'group'  => 'accountAssignDetail_2',
                'domain' => 'zqx',
                'key'    => '中石油分配卡总数',
                'op'     => 'sum',
                'sum'    => $zsy_total,
            ];

            $data['total_all'] = [
                'group'  => 'accountAssignDetail_total',
                'domain' => 'zqx',
                'key'    => '分配卡总数',
                'op'     => 'sum',
                'sum'    => $total,
            ];
        }

        Response::json(array_values($data));
    }


    /**
     * 按卡统计分配金额
     */
    public function assignMoneyDetailStats()
    {
        $params = \helper::filterParams();
        $data   = [];
        $record = OilAccountAssignDetails::assignMoneyDetailStats($params);

        $allOilCom = OilCom::getFanLiCalculate();
        foreach ($allOilCom as $v) {
            $oilCom          = OilCom::getById($v);
            $oil_com['name'] = isset($oilCom['name']) ? $oilCom['name'] : '--';
            $data[$v]        = [
                'group'  => 'assignMoneyDetail_' . $v,
                'domain' => 'zqx',
                'key'    => $oil_com['name'],
                'op'     => 'sum',
                'sum'    => 0,
            ];
        }

        $data['4']     = [
            'group'  => 'assignMoneyDetail_4',
            'domain' => 'zqx',
            'key'    => '撬装卡',
            'op'     => 'sum',
            'sum'    => 0,
        ];
        $data['total'] = [
            'group'  => 'assignMoneyDetail_total',
            'domain' => 'zqx',
            'key'    => '全部卡',
            'op'     => 'sum',
            'sum'    => 0,
        ];

        if ($record) {
            $moneyTotal = 0;
            foreach ($record as $v) {
                $v->_oil_com = $v->oil_com;
                $moneyTotal  += $v->moneyTotal;
                $_moneyTotal = $v->moneyTotal;

                if (in_array($v->_oil_com, OilCom::getSkidType())) {
                    $v->_oil_com = 4;
                    $v->oil_com  = '撬装卡';
                    $_moneyTotal += $v->moneyTotal;
                } else {
                    $oilCom     = OilCom::getById($v->_oil_com);
                    $v->oil_com = isset($oilCom['name']) ? $oilCom['name'] : '--';
                }


                $data[$v->_oil_com] = [
                    'group'  => 'assignMoneyDetail_' . $v->_oil_com,
                    'domain' => 'zqx',
                    'key'    => $v->oil_com,
                    'op'     => 'sum',
                    'sum'    => $_moneyTotal,
                ];
            }

            $data['total'] = [
                'group'  => 'assignMoneyDetail_total',
                'domain' => 'zqx',
                'key'    => '全部卡',
                'op'     => 'sum',
                'sum'    => $moneyTotal,
            ];
        }

        Response::json(array_values($data));
    }

    /**
     * 总分配统计
     */
    public function assignMoneyStats()
    {
        $params = \helper::filterParams();
        $record = OilAccountAssign::assignMoneyStats($params);
        $charge = OilAccountMoneyCharge::chargeMoneyStats($params);

        $data = [
            [
                'group'  => 'accountMoney_assignTotal',
                'domain' => 'zqx',
                'key'    => '总分配金额',
                'op'     => 'sum',
                'sum'    => $record,
            ],
            [
                'group'  => 'accountMoney_chargeTotal',
                'domain' => 'zqx',
                'key'    => '总充值金额',
                'op'     => 'sum',
                'sum'    => $charge,
            ],
        ];

        Response::json($data);
    }

    /**
     * @title  获取扣款账户信息
     * @level  1
     * <AUTHOR>
     */
    public function getDebitAccount()
    {
        $params = \helper::filterParams();

        if (isset($params['orgcode']) && $params['orgcode']) {
            $orgInfo          = OilOrg::getByOrgcode($params['orgcode']);
            $params['org_id'] = $orgInfo->id;
        }

        if (isset($params['org_id']) && $params['org_id']) {
            $accountInfo = $this->getAccountInfoByOrgId($params);
        } else {
            $accountInfo = $this->getAccountInfo(['local' => 1]);
        }

        Response::json($accountInfo);
    }

    private function getAccountInfo($params)
    {
        $accountInfo = [];

        //查出资金账户信息
        $accountMoney = OilAccountMoney::getAccountInfo();
        if ($accountMoney) {
            foreach ($accountMoney as $v) {
                $tmp               = [];
                $tmp['account_no'] = $v->account_no;
                $tmp['name']       = '现金账户(' . $v->account_no . ')';
                $tmp['type']       = '20';
                $accountInfo[]     = $tmp;
            }
        }
        $accountInfo[] = [
            'account_no' => '106',
            'name'       => '积分账户',
            'type'       => '50',
        ];

        //查出授信账户信息
//        $creditAccount = OilCreditAccount::getList(['limit' => 10000]);
//        if ($creditAccount) {
//            foreach ($creditAccount as $v) {
//                $tmp = [];
//                $tmp['account_no'] = $v->account_no;
//                $tmp['name'] = '信用账户(' . $v->account_no . ')';
//                $tmp['type'] = '20';
//                $accountInfo[] = $tmp;
//            }
//        }
        $accountInfo[] = [
            'account_no' => '308',
            'name'       => '托管主卡账户',
            'type'       => '40',
        ];

        return $accountInfo;
    }

    private function getAccountInfoByOrgId(array $params)
    {
        \helper::argumentCheck(['org_id'], $params);

        $accountInfo = [];

        //查出资金账户信息
        $accountMoney = OilAccountMoney::getByOrgId($params);
        if (!$accountMoney) {
            throw new RuntimeException('无资金账户信息', 2);
        }

        $tmp               = [];
        $tmp['account_no'] = $accountMoney->account_no;
        $tmp['name']       = '现金账户';
        $tmp['type']       = '10';
        $accountInfo[]     = $tmp;

        if (isset($params['flag']) && $params['flag'] == 'zqx') {

            $accountInfo[] = [
                'account_no' => '106',
                'name'       => '积分账户',
                'type'       => '50',
            ];
            if (OilCardMain::getByOrgId($params)) {//如果存在托管主卡账户
                $accountInfo[] = [
                    'account_no' => '308',
                    'name'       => '托管主卡账户',
                    'type'       => '40',
                ];
            }
        }

        if (!isset($params['action']) || $params['action'] != 'addOrUpdate') {//zqx禁止添加或编辑授信账户的分配单
            //峰松授信不显示到G7s端
            if ($params['orgcode'] == OrgStatus::CREDIT_ORG_CODE) {
                $creditAccount = new \stdClass();
            } else {
                //查出授信账户信息
                $creditAccount = OilCreditAccount::getByOrgId($params);
            }

            if ($creditAccount) {
                foreach ($creditAccount as $v) {
                    if ($v->status == 10) {
                        if (isset($params['bill_way']) && $params['bill_way']) {
                            if (isset($v->CreditProvider) && $v->CreditProvider->status == 10 && $v->CreditProvider->bill_way == $params['bill_way']) {
                                $tmp               = [];
                                $tmp['account_no'] = $v->account_no;
                                $tmp['name']       = '信用账户(' . $v->CreditProvider->name . ')';
                                $tmp['type']       = '20';
                                $accountInfo[]     = $tmp;
                            }
                        } else {
                            if (isset($v->CreditProvider) && $v->CreditProvider->status == 10) {
                                $tmp               = [];
                                $tmp['account_no'] = $v->account_no;
                                $tmp['name']       = '信用账户(' . $v->CreditProvider->name . ')';
                                $tmp['type']       = '20';
                                $accountInfo[]     = $tmp;
                            }
                        }

                    }
                }
            }
        }

        return $accountInfo;
    }

    /**
     * @title  获取授信账户可分配金额
     * <AUTHOR>
     */
    public function getCreditAcountBalance0()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['account_no'], $params);

        //可用余额
        $used_money = (new AccountGredit())->setAccount($params['account_no'])->getAccountBalance();

        Response::json(['use_money' => $used_money]);
    }

    /**
     * @title  获取授信账户可分配金额
     * <AUTHOR>
     */
    public function getCreditAcountBalance()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['orgCode'], $params);

        //可用余额
        $accountInfo = (new AccountService())->getCreditBalanceByOrgCode(['orgCode' => $params['orgCode']]);

        Response::json(['use_money' => $accountInfo ? $accountInfo->restCreditAmount : 0]);
    }

    /**
     * @title   初始化数据到gos
     * <AUTHOR>
     */
    public function batchAddToGos()
    {
        AccountAssignToGos::init();
    }

    /**
     * @title 获取分配类型
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getAccountType()
    {

        $info = AccountType::getAll();
        //查询授信产品
        $list = OilCreditProvider::getProductList();
        if (count($list) > 0) {
            $arr = $list->toArray();
            foreach ($arr as $_k => $_v) {
                $arr["G7_" . $_k] = $_v;
                unset($arr[$_k]);
            }
            $info = $info + $arr;
        }
        $data = [];
        foreach ($info as $k => $v) {
            $tmp          = [];
            $tmp['key']   = $k;
            $tmp['value'] = $v;
            $data[]       = $tmp;
        }

        Response::json($data);
    }

    /**
     * @title   回写sn流水号
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function reWriteSn()
    {
        try {
            $total = OilAccountAssign::whereNull('sn')->count();
            if ($total > 0) {
                $pageSize  = 1000;
                $totalPage = ceil($total / $pageSize);
                for ($i = 1; $i <= $totalPage; $i++) {
                    $records = OilAccountAssign::whereNull('sn')->orderBy('id', 'asc')->skip(($i - 1) * $pageSize)->take($pageSize)->get();
                    if ($records) {
                        foreach ($records as $v) {
                            //$v->update(['sn'=>Helper::uuid()]);
                            $batchInsertSqlArr[] = "update oil_account_assign set sn = '" . Helper::uuid() . "' where id = " . $v->id . " ";
                        }
                    }
                    $batchInsertSql = implode(";", $batchInsertSqlArr);

                    Capsule::connection()->getPdo()->exec($batchInsertSql);
                }
            }
        } catch (Exception $e) {
            throw new RuntimeException(strval($e), $e->getCode());
        }

    }

    public function getSn()
    {
        $sn = Helper::uuid();

        Response::json($sn);
    }

    /*
     * 统计用信信息
     */
    public function statsViewUseCreditAssign()
    {
        $data   = [];
        $params = \helper::filterParams();

        $_data = OilAccountAssign::getStatsData($params);

        $app_total = $wechat_total = $web_total = $gsp_total = 0;
        $app_num   = $wechat_num = $web_num = $gsp_num = 0;

        if ($_data['list']) {
            foreach ($_data['list'] as $v) {
                if ($v['data_from'] == DataFrom::APP) {
                    $app_total += $v['money_total'];
                    $app_num   += 1;
                }

                if ($v['data_from'] == DataFrom::WeChat) {
                    $wechat_total += $v['money_total'];
                    $wechat_num   += 1;
                }

                if ($v['data_from'] == DataFrom::WEB) {
                    $web_total += $v['money_total'];
                    $web_num   += 1;
                }

                if ($v['data_from'] == DataFrom::GSP) {
                    $gsp_total += $v['money_total'];
                    $gsp_num   += 1;
                }
            }
        }

        //今日用信总额(app)
        $data['total_0'] = [
            'group'  => 'creditAssign_total_app',
            'domain' => 'zqx',
            'key'    => '今日用信总额（app）',
            'op'     => 'sum',
            'sum'    => sprintf("%.2f", $app_total),
        ];

        //今日用信总额(微信)
        $data['total_1'] = [
            'group'  => 'creditAssign_total_wechat',
            'domain' => 'zqx',
            'key'    => '今日用信总额（wechat）',
            'op'     => 'sum',
            'sum'    => sprintf("%.2f", $wechat_total),
        ];

        //今日用信总额(web)
        $data['total_2'] = [
            'group'  => 'creditAssign_total_web',
            'domain' => 'zqx',
            'key'    => '今日用信总额（web）',
            'op'     => 'sum',
            'sum'    => sprintf("%.2f", $web_total),
        ];

        //今日用信总额(gsp)
        $data['total_3'] = [
            'group'  => 'creditAssign_total_gsp',
            'domain' => 'zqx',
            'key'    => '今日用信总额（gsp）',
            'op'     => 'sum',
            'sum'    => sprintf("%.2f", $gsp_total),
        ];

        //今日用信总额
        $data['total_4'] = [
            'group'  => 'creditAssign_total',
            'domain' => 'zqx',
            'key'    => '今日用信总额',
            'op'     => 'sum',
            'sum'    => $_data['credit_money_total'],
        ];

        ///////////////////////////////

        //获取今日用信工单数
        $data['total_5'] = [
            'group'  => 'creditAssign_num_app',
            'domain' => 'zqx',
            'key'    => '今日用信工单数(app)',
            'op'     => 'sum',
            'sum'    => $app_num,
        ];

        //获取今日用信工单数
        $data['total_6'] = [
            'group'  => 'creditAssign_num_wechat',
            'domain' => 'zqx',
            'key'    => '今日用信工单数(wechat)',
            'op'     => 'sum',
            'sum'    => $wechat_num,
        ];

        //获取今日用信工单数
        $data['total_7'] = [
            'group'  => 'creditAssign_num_web',
            'domain' => 'zqx',
            'key'    => '今日用信工单数(web)',
            'op'     => 'sum',
            'sum'    => $web_num,
        ];

        //获取今日用信工单数
        $data['total_8'] = [
            'group'  => 'creditAssign_num_gsp',
            'domain' => 'zqx',
            'key'    => '今日用信工单数(gsp)',
            'op'     => 'sum',
            'sum'    => $gsp_num,
        ];

        //获取今日用信工单数
        $data['total_9'] = [
            'group'  => 'creditAssign_num',
            'domain' => 'zqx',
            'key'    => '今日用信工单数',
            'op'     => 'sum',
            'sum'    => $_data['credit_num'],
        ];

        //今日用信客户数
        $data['total_10'] = [
            'group'  => 'creditUserToday_total',
            'domain' => 'zqx',
            'key'    => '今日用信客户数',
            'op'     => 'sum',
            'sum'    => $_data['user_num'],
        ];

        Response::json(array_values($data));
    }

    /*
     * 临时方法
     */
    public function amendTempUseFanli()
    {
        $array = [1, 2, 4, 6, 8];
        $k     = array_slice($array, 0, 2);
        print_r($k);
        exit;
        $total_amount = 240;
        $data         = OilAccountAssign::leftJoin('oil_org', 'oil_org.id', '=', 'oil_account_assign.org_id')
            ->where('oil_org.orgcode', 'like', '200I1A%')
            ->where('oil_account_assign.use_cash_fanli', '>', 0)
            ->select('oil_account_assign.id', 'oil_account_assign.use_cash_fanli')
            ->orderBy('oil_account_assign.createtime', 'asc')
            ->get();
        if ($data) {
            foreach ($data as $assign) {
                $total_amount = $total_amount - $assign->use_cash_fanli;
                if ($total_amount > 0) {
                    //OilAccountAssign::edit(['id'=>$assign->id,'use_cash_fanli'=>0]);
                    var_dump(['id' => $assign->id, 'use_cash_fanli' => 0]);
                } elseif ($total_amount == 0) {
                    //OilAccountAssign::edit(['id'=>$assign->id,'use_cash_fanli'=>0]);
                    var_dump(['id' => $assign->id, 'use_cash_fanli' => 0]);
                    break;
                } else {
                    //OilAccountAssign::edit(['id'=>$assign->id,'use_cash_fanli'=>0]);
                    var_dump(['id' => $assign->id, 'use_cash_fanli' => abs($total_amount)]);
                    break;
                }
            }
        }

        exit;
    }

    /*
     * 临时方法：用机构每月的所有返利正推逐条填写分配单中的使用返利
     */
    public function tempEditUseFanli()
    {
        $sql      = "(SELECT
	a.orgcode,
	a.total_fanli,
	a.createtime,
	ifNull(b.total_charge_fanli,0) as total_charge_fanli,
  (a.total_fanli + ifNull(b.total_charge_fanli,0) ) as sum_fanli
FROM
	(
		SELECT
			LEFT (c.orgcode, 6) AS orgcode,
			sum(a.fanli_money) AS 'total_fanli',
			DATE_FORMAT(a.audit_time, '%Y-%m') AS createtime
		FROM
			oil_fanli_calculate a
		LEFT JOIN oil_fanli_policy b ON b.id = a.policy_id
		LEFT JOIN oil_org c ON b.org_id = c.id
		WHERE
			a. STATUS = 1
		AND c.is_del = 0
		GROUP BY
			LEFT (c.orgcode, 6),
			DATE_FORMAT(a.audit_time, '%Y-%m')
	) a
LEFT JOIN (
	SELECT
		LEFT (b.orgcode, 6) AS orgcode,
		sum(a.arrival_money) AS 'total_charge_fanli',
		DATE_FORMAT(a.createtime, '%Y-%m') AS createtime
	FROM
		oil_account_money_charge a
	LEFT JOIN oil_org b ON a.org_id = b.id
	WHERE
		a.charge_type = 2
	AND a. STATUS = 1
	GROUP BY
		LEFT (b.orgcode, 6),
		DATE_FORMAT(a.createtime, '%Y-%m')
) b ON a.orgcode = b.orgcode
AND a.createtime = b.createtime
WHERE a.total_fanli + ifNull(b.total_charge_fanli,0)  > 0
)  
UNION
(
		SELECT
		LEFT (b.orgcode, 6) AS orgcode,
		0 as total_fanli,
		DATE_FORMAT(a.createtime, '%Y-%m') AS createtime,
		sum(a.arrival_money) AS 'total_charge_fanli',
		sum(a.arrival_money) as sum_fanli
	FROM
		oil_account_money_charge a
	LEFT JOIN oil_org b ON a.org_id = b.id
	WHERE
		a.charge_type = 2
	AND a. STATUS = 1
AND b.orgcode in ('200FES','200JWG','200NG6','200PHB','200S1D','200T9G','200XO0','200XOV','2010B5','201E62','201GF5','201GF6','201KMM','201LBN','201LP2')
	GROUP BY
		LEFT (b.orgcode, 6),
		DATE_FORMAT(a.createtime, '%Y-%m')
)";
        $initData = Capsule::connection()->select($sql);
//var_dump($initData);exit;
        $balanceAmount = [];
        if ($initData) {
            foreach ($initData as $val) {
                $month        = $val->createtime;
                $orgcode      = $val->orgcode;
                $total_amount = $val->sum_fanli;
                var_dump('orgcode:' . $orgcode . ',月份：' . $month . ',处理start');
                Log::error('orgcode:' . $orgcode . ',月份：' . $month . ',处理start', [], 'fanliInit');

                var_dump('orgcode:' . $orgcode . ',月份：' . $month . ',总返利：' . $total_amount);
                Log::error('orgcode:' . $orgcode . ',月份：' . $month . ',总返利：' . $total_amount, [], 'fanliInit');
                $data = OilAccountAssign::leftJoin('oil_org', 'oil_org.id', '=', 'oil_account_assign.org_id')
                    ->where('oil_org.orgcode', 'like', $orgcode . '%')
                    ->where('oil_account_assign.createtime', 'like', $month . '%')
                    ->where('oil_account_assign.money_total', '>', 0)
                    ->where('oil_account_assign.status', 1)
                    ->select('oil_account_assign.id', 'oil_account_assign.use_cash_fanli', 'oil_account_assign.money_total')
                    ->orderBy('oil_account_assign.createtime', 'asc')
                    ->get();

                if ($data) {
                    foreach ($data as $assign) {
                        if ($assign->money_total > 0 && $total_amount > 0) {
                            if ($assign->money_total > $total_amount) {
                                //OilAccountAssign::edit(['id'=>$assign->id,'use_cash_fanli'=>$total_amount]);
                                var_dump('orgcode:' . $orgcode . ',月份：' . $month . ',分配单id：' . $assign->id . ',使用返利' . $total_amount);
                                Log::error('orgcode:' . $orgcode . ',月份：' . $month . ',分配单id：' . $assign->id . ',使用返利' . $total_amount, [], 'fanliInit');
                                $total_amount = 0;
                                break;
                            } else {
                                //OilAccountAssign::edit(['id'=>$assign->id,'use_cash_fanli'=>$assign->money_total]);
                                var_dump('orgcode:' . $orgcode . ',月份：' . $month . ',分配单id：' . $assign->id . ',使用返利' . $assign->money_total);
                                Log::error('orgcode:' . $orgcode . ',月份：' . $month . ',分配单id：' . $assign->id . ',使用返利' . $assign->money_total, [], 'fanliInit');
                                $total_amount = $total_amount - $assign->money_total;
                            }
                        }
                    }
                } else {
                    var_dump('orgcode:' . $orgcode . ',月份：' . $month . ',没有分配记录，剩余返利：' . $total_amount);
                    Log::error('orgcode:' . $orgcode . ',月份：' . $month . ',没有分配记录，剩余返利：' . $total_amount, [], 'fanliInit');
                }

                if ($total_amount > 0) {
                    $balanceAmount[$orgcode][] = $total_amount;
                    Log::error('orgcode:' . $orgcode . ',月份：' . $month . ',剩余返利：' . $total_amount, [], 'fanliNotZero');
                }
                var_dump('orgcode:' . $orgcode . ',月份：' . $month . ',剩余返利：' . $total_amount);
                Log::error('orgcode:' . $orgcode . ',月份：' . $month . ',剩余返利：' . $total_amount, [], 'fanliInit');

                var_dump('orgcode:' . $orgcode . ',月份：' . $month . ',处理end');
                Log::error('orgcode:' . $orgcode . ',月份：' . $month . ',处理end', [], 'fanliInit');
                //exit;
            }
        }
        Log::error('有余额的机构信息:', [$balanceAmount], 'fanliNotZeroJson');
        var_dump('全部over');
        Log::error('全部over', [], 'fanliInit');
        die('finish');
    }

    public function editFanliBalance()
    {
        $dataJson = '{"200021":["14.76"],"2001LF":["129.75"],"2001U0":["2568.70"],"200575":["349.58"],"2006KE":["73.64","31.20"],"2006UK":["191.88","30.68","16.80","1.20","21.96"],"2007SI":["1044.90","56.20"],"200AYY":["127.78","133.39"],"200FGW":["12.97"],"200FJ0":["161.62"],"200FUW":["13.59"],"200GFS":["89.53","5.56"],"200GI1":["87.62","179.80","8.00"],"200GTQ":["300.38","4.28"],"200GYU":["203.26"],"200H74":["17.00"],"200HOC":["209.54","1.75","2.35"],"200HWI":["271.82"],"200HYB":["285.48","66.74"],"200I15":["363.19","20.00"],"200I1A":[7.16],"200I6G":["66.15"],"200IA2":["650.48"],"200ID2":["308.62"],"200IF8":["94.00","0.04","73.02"],"200IGV":["8.74","4.51","2.02","0.69"],"200IQP":["61.86"],"200J0X":["290.71","61.14"],"200J4C":["35.45"],"200J9N":["7.11","57.14"],"200JCV":[620.27],"200JM2":["18.60"],"200JQB":["2306.28","1434.44"],"200JQK":["681.64"],"200JUC":["19.77","3.00"],"200KL4":[172.18,"213.35","5.00"],"200LDD":["169.02","22.08"],"200LGI":["1028.97"],"200LKJ":[0.19000000000005457,"269.82"],"200M1T":["174.11","625.67"],"200MI2":["25.61"],"200MP2":["1768.24"],"200MWK":["121.21","75.04","301.24"],"200N3H":["195.35"],"200NVV":[270.07,"10.25","91.16","2.89","5.72","16.10"],"200NY7":["74.41","2565.25","1305.96","244.80","1.09","0.16"],"200NYC":[2935.54,"18.52"],"200NZ7":["16.61","56.38"],"200O37":["841.14","32.25","2.49","86.59","201.24","150.77"],"200OND":[200,"73.15","13.19","38.20","16.27"],"200OQJ":["328.81","100.08"],"200P0I":["211.43","3.32"],"200P9I":["708.66"],"200Q1E":["304.85"],"200QAZ":["77.46","0.75"],"200QB8":["304.55","176.03"],"200QJ8":["2.25"],"200QNA":["78.45","357.32"],"200QV3":["1886.49"],"200QXR":["108.00","0.47"],"200R2D":["1344.32"],"200R5R":["96.05"],"200RHL":[926.22],"200S2E":["42.03"],"200S3K":["126.77"],"200SGJ":["969.31","2712.78","144.59","6.03","112.67","56.05","45.28","29.05","1.77"],"200SLK":["4322.95","35.87"],"200SPV":["110.43"],"200TEA":["274.72"],"200TFT":["32.58"],"200TL3":["124.98"],"200TS6":["398.23"],"200U8X":["1162.68","316.78"],"200V12":["221.28","36.53"],"200VEC":["1273.17","235.83","81.49"],"200VGX":["20.29","115.96","11.27","2.27"],"200VN8":["73.19"],"200W25":["1569.41","69.53","16.04"],"200W4T":["270.15","221.61","409.44","188.51","196.99","90.37","17.00","6.66"],"200WDP":["370.37"],"200WTS":["13.00"],"200Y50":["98.44"],"200ZEO":["169.59"],"200ZXX":[28.589999999999918,"152.07","6.30"],"20104D":["366.30","2386.20","1750.54","284.28"],"201295":["603.24","25.68"],"2012BW":["206.58","22.10","117.78"],"2012YR":["315.20","78.76"],"20141D":["72.26"],"2015GF":["510.60","50.07"],"2015PL":["275.02","53.99"],"2016CF":["2981.24","35.70"],"2016HM":["10302.05","1448.02","408.92"],"2016HN":["404.62","237.22","190.74","150.49","104.83"],"2016NQ":["455.91"],"20170P":["58.69","55.22"],"20170R":["11.87"],"2017BC":["129.31","108.00"],"2017EJ":["272.00","308.02","128.20","54.35","75.00","2.39","103.10"],"2017ER":["796.87"],"2017Q4":["217.33"],"2017RI":[179.64999999999998,"129.55"],"20183O":["774.65"],"2018LM":["73.27"],"20192W":["77.21"],"20194O":["1.58"],"201A9G":["14.91"],"201ADL":["435.08","18.07"],"201AEO":["1329.83"],"201AFJ":["12.67"],"201AO3":["670.33","76.71"],"201AV8":["124.10","9.80","8.50"],"201AZJ":["352.18","8.67"],"201B19":[9.629999999999995],"201B24":["523.98","52.13","0.43"],"201B29":["4.89","10.00"],"201B3I":["1615.78","115.25"],"201B3W":["24.01","78.34","138.60","125.20"],"201B7J":["122.96"],"201B9O":["199.73"],"201BE9":["37.55"],"201BHP":["205.71","696.82"],"201BHQ":["41.27","19.69"],"201BUC":["237.13"],"201BUT":["107.87","60.00"],"201BVR":["0.80","77.53","85.31"],"201C5W":["98.28"],"201CAT":["36.87"],"201CD1":["92.74"],"201CIF":["6.00"],"201CQX":["194.54","57.91"],"201CQZ":["1.32"],"201CR0":["50.00"],"201CWJ":["402.11","9.02"],"201CWK":["144.74"],"201CZ2":["274.32"],"201DC9":["6020.59","1363.43","58.09"],"201DQD":["31.60","7.09"],"201DVJ":["96.77"],"201F68":["23.58"],"201FEA":["92.62"],"201GAG":["176.30"],"201GF7":["98.05"],"201GK7":["9638.65","415.65","41.00"],"201GPU":[371.48,"510.05"],"201H92":["1581.22"],"201I07":["40.44"],"201IAC":["490.32"],"201IQ0":["242.02"],"201JAD":["10292.26"],"201JI7":["88.07"],"201JI9":["9.37"],"201JNA":["72.41"],"201JQJ":["24.14"],"201JWY":["128.60"],"201JZD":["53.26","150.04","7.00"],"201JZE":["201.88","73.34"],"201K45":["506.30","579.26"],"201KUQ":["934.80"],"201L5N":["5.28"]}';
        $data     = json_decode($dataJson, TRUE);

        if ($data) {

            foreach ($data as $orgcode => $v) {
                //取前两个月的与余额相加作为机构的新的返利余额
                $fanli_balance = array_sum(array_slice($v, 0, 2));

                //得到机构现金账户信息
                $accountInfo = OilAccountMoney::leftJoin('oil_org', 'oil_org.id', '=', 'oil_account_money.org_id')
                    ->select('oil_account_money.money', 'oil_account_money.id', 'oil_org.orgcode')
                    ->where('oil_org.orgcode', $orgcode)->first();

                if ($accountInfo) {
                    if ($accountInfo->money > $fanli_balance) {
                        //修改现金账户的返利余额
                        OilAccountMoney::edit(['id' => $accountInfo->id, 'cash_fanli_remain' => $fanli_balance]);
                        var_dump('orgcode:' . $orgcode . ',的返利余额已更改为:' . $fanli_balance . '现金账户id:' . $accountInfo->id);
                        Log::error('orgcode:' . $orgcode . ',的返利余额已更改为:' . $fanli_balance . '现金账户id:' . $accountInfo->id, [], 'editFanliBalance');
                    } else {
                        var_dump('orgcode:' . $orgcode . ',的现金余额:' . $accountInfo->money . '小于这次的返利余额:' . $fanli_balance);
                        Log::error('orgcode:' . $orgcode . ',的现金余额:' . $accountInfo->money . '小于这次的返利余额:' . $fanli_balance, [], 'editFanliBalance');
                    }
                } else {
                    var_dump('orgcode:' . $orgcode . ',不存在现金帐号');
                    Log::error('orgcode:' . $orgcode . ',不存在现金帐号', [], 'editFanliBalance');
                }
            }
        }

        var_dump('全部over');
        Log::error('全部over', [], 'editFanliBalance');
        die('finish');
    }

    /**
     * @title  定时检查分配工单状态（1号共享卡）
     * @desc
     * @param array $details
     * @param int $dataFrom
     * @return array
     * @returns
     * array
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
     * @package  Fuel\Service
     */
    public function cronAlarmAssign()
    {
        $list = OilAccountAssign::getAssignStatus();

        Log::debug("更改状态的壹号卡" . json_encode($list), [], 'AssignSheet_');
        if (count($list) == 0) {
            Response::json([], 0, "暂无更改状态的G7能源账户");
            return TRUE;
        }

        $assign_no = [];
        foreach ($list as $_key => $_val) {
            if (time() - strtotime($_val->apply_time) > 5 * 60) {
                $assign_no[] = $_val->no;
            }
        }

        if (count($assign_no) > 0) {
            $str_no  = implode(",", $assign_no);
            $content = "";
            $content .= "* 探测环境：" . $this->config->api_env . "\n";
            $content .= "* 探测时间：" . \helper::nowTime() . "\n";
            $content .= "* 项目：探测共享账户分配单状态\n";
            $content .= "* 结果：发现了，" . count($assign_no) . "条记录\n";
            $content .= "* 分配单号：" . $str_no . "\n";

            try {
                (new DingTalkAlarm())->sendDingTalkByConfig(
                    'fossWarnGroup',
                    "未审核共享账户分配单",
                    $content
                );
            } catch (Exception $exception) {
                Log::error(__METHOD__ . "#" . __FUNCTION__, [strval($exception)], 'cronAlarmAssign');
            }

        }
        echo "success";
    }

    /**
     * @title  定时设置分配工单状态（1号共享卡）
     * @desc
     * @param array $details
     * @param int $dataFrom
     * @return array
     * @returns
     * array
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
     * @package  Fuel\Service
     */
    public function cronAssignStatus()
    {
        $condition['oil_account_assign.status'] = AccountAssignStatus::ASSIGNED;
        $list                                   = OilAccountAssign::getAssignStatus($condition);

        Log::debug("更改状态的壹号卡" . json_encode($list), [], 'AssignSheet_');
        if (count($list) == 0) {
            Response::json([], 0, "暂无更改状态的壹号卡");
            return TRUE;
        }

        foreach ($list as $_key => $_val) {
            try {
                Log::debug("获取机构余额：" . $_val->org_id, [], 'AssignSheet_');
                $accountMoney = (new AccountMoney())->setOrg($_val->org_id);
                //得到机构的可用余额
                $accountMoneyBalance = $accountMoney->getAccountBalance();
                //由于可用余额已经减去分配单金额 在加回去进行判定
                $orginmoney = $accountMoneyBalance + $_val->money_total;
                Log::debug("更改状态的壹号卡金额" . $accountMoneyBalance . ",比较使用的金额:" . $orginmoney . ",分配金额:" . $_val->money_total, [], 'AssignSheet_');
                if (bccomp($orginmoney, $_val->money_total, 2) >= 0) {
                    $res = $this->auditBy($_val->id);
                    Log::debug("定时任务审核通过结果" . json_encode($res), [], 'AssignSheet_');
                } else {
                    continue;
                }
            } catch (Exception $e) {
                Log::error('cronAssignStatus_error:' . strval($e), [], 'cronAssignStatusError');
            }

        }
        echo "success";
    }

    /*
     * 取消交易 以后需要废弃 deleteAssign
     */
    public function cancelConsume()
    {
        global $app;

        $start = microtime(true);
        $params = \helper::filterParams();
        Log::error('params-' . var_export($params, TRUE), [], 'cancelConsume');
        Log::error('params-' . var_export($params, TRUE), ["start"=>$start], 'Consume_');
        //\helper::argumentCheck(['stream_no'], $params);

        if( (!isset($params['stream_no']) || !$params['stream_no']) && (!isset($params['trade_id']) || !$params['trade_id'])){
            throw new \RuntimeException('stream_no和trade_id不能同时为空',2);
        }
        $is_cash = FALSE;
        $cashBalance = NULL;
        $orderInfo = NULL;
        $from = [];
        Capsule::connection()->beginTransaction();
        try {
            //根据stream_no查询
            if(isset($params['stream_no']) && $params['stream_no']){
                $orderInfo = OilCardViceTradesZBank::lockForUpdate()->where('stream_no', (string)$params['stream_no'])->first();
            }

            if(!$orderInfo && isset($params['trade_id']) && $params['trade_id']){
                $orderInfo = OilCardViceTradesZBank::lockForUpdate()->where('trade_api_id', (string)$params['trade_id'])->first();
            }

            if (!$orderInfo) {
                Log::error('此订单不存在' . $params['stream_no'], [$orderInfo], 'cancelConsume');
                Response::json(TRUE, 1120, '此订单不存在' . $params['stream_no']);
            }

            $from_one = $orderInfo->data_from;
            if($orderInfo->is_pay == 4){
                Log::error('此订单已撤销过' . $params['stream_no'], [$orderInfo], 'cancelConsume');
                Response::json(TRUE, 0, '此订单已撤销过' . $params['stream_no']);
            }

            if($orderInfo->is_pay == 2){
                Log::error('此订单正在支付中不可撤销' . $params['stream_no'], [$orderInfo], 'cancelConsume');
                throw new \RuntimeException('此订单正在支付中不可撤销',1121);
            }

            if($orderInfo->is_pay == 3){
                Log::error('此订单支付失败，不允许撤销！' . $params['stream_no'], [$orderInfo], 'cancelConsume');
                throw new \RuntimeException('此订单支付失败，不允许撤销！',1122);
            }

            $lastBalance = 0;
            //卡信息
            $cardInfo = OilCardVice::lockForUpdate()->where('vice_no', $orderInfo->vice_no)->first();
            if (!$cardInfo) {
                Log::error('卡号不存在或者卡状态非使用状态' . $orderInfo->vice_no, [], 'cancelConsume');
                throw new \RuntimeException('子账户账号不存在或者非使用状态', 2);
            }

            if (in_array($cardInfo->oil_com, \Fuel\Defines\OilCom::getZBankFirstList())) {
                Log::error('该卡种不支持撤销' . $cardInfo->oil_com, [], 'cancelConsume');
                throw new \RuntimeException('该账户类型不支持撤销', 2);
            }

            if ( in_array($cardInfo->oil_com,[OilCom::GAS_FIRST_CHARGE,OilCom::FORTUNE_CARD]) ) {
                //卡账户
                $cardAccountInfo = OilCardAccount::where('cardSubAccountID', $orderInfo->deduction_account_no)->first();
                if (!$cardAccountInfo) {
                    throw new \RuntimeException($orderInfo->deduction_account_no . "账户不存在", 2);
                }

                if ($cardAccountInfo->subAccountType == 'CASH') {
                    Log::error('充值卡现金取消交易', [], 'cancelConsume');
                    $lastBalance = $cashBalance = $cardInfo->card_remain + $orderInfo->trade_money;
                    $cardInfo->update(['card_remain' => $cashBalance ]);
                    $is_cash = TRUE;
                } elseif ($cardAccountInfo->subAccountType == 'CREDIT') {
                    Log::error('充值卡授信取消交易' , [], 'cancelConsume');
                    Log::error('请求帐户中心撤销开始' , [], 'cancelConsume');
                    (new AccountService())->revokeCardConsume([
                        'extID'       => $orderInfo->extID,
                        'billID'      => $orderInfo->billID,
                        'companyCode' => 'G7_DALIAN',
                        'totalAmount' => round($orderInfo->trade_money * 100, 0),
                    ]);
                    Log::error('请求帐户中心撤销结束' , [], 'cancelConsume');
                    //修改card_account里的卡余额
                    $lastBalance = $balance = $cardAccountInfo->amount + $orderInfo->trade_money;
                    $cardAccountInfo->update(['amount' => $balance]);
                }
            } elseif ($cardInfo->oil_com == OilCom::GAS_FIRST_TALLY) {
                $assignId = null;
                //共享卡机构账户取消交易
                if (substr($orderInfo->deduction_account_no, 0, 3) == '208') {
                    $creditInfo = OilCreditAccount::getByAccountNoWithProvider($orderInfo->deduction_account_no);
                    if ($creditInfo->is_own == 1) {
                        Log::error('共享卡自授信取消交易', [], 'cancelConsume');
                        // 自授信撤销走账户流水撤销
                        $accountCase = $params['document_type'] == CardTradeConf::DOCUMENT_TYPE_RESERVE_CANCEL ? 3 : 2;
                        $resCancelSelfCredit = (new CardTradeService)->cancelOrgSelfCreditTrade($orderInfo, $cardInfo,$accountCase);
                        $lastBalance = $resCancelSelfCredit['balance'];
                    } else {
                        Log::error('共享卡授信取消交易', [], 'cancelConsume');
                        Log::error('请求帐户中心撤销开始' , [], 'cancelConsume');
                        // ******** 三方授信撤销暂停
                        throw new \RuntimeException('该账户类型不支持撤销!', 2);
                        (new AccountService())->revokeCardConsume([
                            'extID'       => $orderInfo->extID,
                            'billID'      => $orderInfo->billID,
                            'companyCode' => 'G7_DALIAN',
                            'totalAmount' => round($orderInfo->trade_money * 100, 0),
                        ]);
                        Log::error('请求帐户中心撤销结束' , [], 'cancelConsume');
                    }
                } else {
                    Log::error('共享卡现金取消交易', [], 'cancelConsume');
                    $is_cash = TRUE;

                    // 共享卡去掉分配后，会有一部分交易撤销需要走分配单销审
                    $accountMoneyRecordInfo = OilAccountMoneyRecords::getByNo(['no'=>NoTypeStatus::NO_TYPE_XF.$orderInfo['id']]);
                    if (empty($accountMoneyRecordInfo)) {
                        $assignInfo = OilAccountAssign::where('trades_sn', $orderInfo->trade_api_id)->first();

                        if ($assignInfo && $assignInfo->status == AccountAssignStatus::AUDITED) {
                            try {
                                $res = $this->unAudit($assignInfo->id, TRUE, $is_delete = TRUE);
                                Log::error("销审结果", [$res], 'cancelConsume');
                            } catch (Exception $e) {
                                Log::error(__METHOD__ . strval($e), [], 'cancelConsume');
                                throw new RuntimeException($e->getMessage(), $e->getCode());
                            }

                            $condition_detail['oil_account_assign.id'] = $assignInfo->id;
                            $details = OilAccountAssign::getAssignDetails($condition_detail);
                            Log::error("分配单详情：" . json_encode($details), [], "cancelConsume");
                            //删除加入回收站
                            $recycle = [];
                            $recycle['table_name'] = 'oil_account_assign';
                            $recycle['pk'] = $assignInfo->id;
                            $recycle['org_id'] = $assignInfo->org_id;
                            $recycle['no'] = $assignInfo->no;
                            $recycle['sn'] = $assignInfo->sn;
                            $recycle['data'] = json_encode($details[0]);
                            $recycle['operator_id'] = $this->app->myAdmin->id;
                            $recycle['createtime'] = \helper::nowTime();
                            Log::error("删除分配单添加回收站", [$recycle], 'cancelConsume');
                            OilRecycle::add($recycle);

                            // 删除分配单及详情
                            OilAccountAssignDetails::removeByAssignId(["assignIds" => $assignInfo->id]);
                            OilAccountAssign::remove(["ids" => $assignInfo->id]);

                            $assignId = $assignInfo->id;

                            //获取最后的现金余额
                            $lastBalance  = FrozenMoney::accountBalance($assignInfo->org_id);
                            Log::error("lastBalance", [$lastBalance], 'cancelConsume');
                        }
                    } else {
                        // 校验该单是否撤销过（生成过资金账户流水）
                        $checkAccountCancelRecords = OilAccountMoneyRecords::getByNo(['no'=>NoTypeStatus::NO_TYPE_XFCX.$orderInfo['id']]);
                        if (!empty($checkAccountCancelRecords)) {
                            throw new \RuntimeException('单号'.$orderInfo['stream_no'] . "已撤销过，请勿重复操作！", 2);
                        }
                        // 走资金账户流水撤销
                        $ret = (new CardTradeService())->cancelOrgCashTrade($orderInfo, $cardInfo);
                        $orderInfo   = $ret['order_info'];
                        $lastBalance = $ret['balance'];
                    }

                }
            }else {
                throw new RuntimeException('非法卡类型',2);
            }

            if ($orderInfo->is_pay != 4) {
                $orderInfo->update(['is_pay' => 4,'balance'=>$lastBalance]); //订单新增撤销状态
            }

            $cancel_trade_id = "";
            // 增加生成负流水
            $tradeInfo = OilCardViceTrades::where('api_id',$orderInfo->trade_api_id)->first();
            if($tradeInfo){

                \Fuel\Service\OrgChangeOperatorService::setTradeCancel($tradeInfo);

                $ext_is_final = 100;
                //G7WALLET-4044 产品要求，直接放开
                if( in_array($tradeInfo->is_open_invoice,[10,20]) ){
                    if( \Fuel\Service\OrgChangeOperatorService::getChangeLog($tradeInfo) == 1 ) {
                        $ext_is_final = 310;
                    }else {
                        //G7WALLET-6227
                        $orgInfo = OilOrg::getById(['id'=>$tradeInfo->org_id]);
                        if($orgInfo && isset($orgInfo->is_receipt_white) && $orgInfo->is_receipt_white == 2){
                            (new CardTradeService)->receiptTradeReject($tradeInfo);
                        }   
                    }
                }

                //删除消费记录 备注：仅标用一体时zbank里use_fanli_money才有值
                //G7WALLET-5241
                if (isset($orderInfo->use_fanli_money) && $orderInfo->use_fanli_money == 0) {
                    $this->delTradesInfo($tradeInfo);
                }

                $cancel_no = 'D_' . $tradeInfo->id;
                $tradeInfo->update(['cancel_sn' => $cancel_no]);
                $insertTrade                    = $tradeInfo->toArray();
                $insertTrade['cancel_sn']       = $cancel_no;
                $insertTrade['trade_money']     = -1 * $insertTrade['trade_money'];
                $insertTrade['trade_num']       = -1 * $insertTrade['trade_num'];
                $insertTrade['total_money']     = -1 * $insertTrade['total_money'];
                $insertTrade['service_money']   = -1 * $insertTrade['service_money'];
                $insertTrade['use_fanli_money'] = -1 * $insertTrade['use_fanli_money'];
                $insertTrade['fanli_money']     = -1 * $insertTrade['fanli_money'];
                $insertTrade['fanli_jifen']     = -1 * $insertTrade['fanli_jifen'];
                $insertTrade['receipt_remain']  = -1 * $insertTrade['receipt_remain'];
                $insertTrade['api_id']          = 'd_' . $orderInfo->id; //这里的api_id存的是zbank表里删除记录的id
                $insertTrade['createtime']      = \helper::nowTime();
                // 应付金额改成负值
                $insertTrade['xpcode_pay_money'] = -1 * $insertTrade['xpcode_pay_money'];

                //G7WALLET-4874
                if($insertTrade['trade_type'] == CardTradeConf::RESERVE_TRADE_TYPE){
                    $insertTrade['trade_type'] = CardTradeConf::RESERVE_CANCEL_TRADE_TYPE;
                }


                if($lastBalance > 0){
                    $insertTrade['balance']         = $lastBalance;
                }

                unset($insertTrade['id']);
                Log::error('增加负消费', [$insertTrade], "txb_delete");
                $newInsert  = OilCardViceTrades::add($insertTrade);

                $cancel_trade_id = $newInsert->id;
                //消费扩展表新增消费数据
                $from[$insertTrade['api_id']] = $from_one;
                $tradeArr = $newInsert->toArray();
                $tradeArr['ext_is_final'] = $ext_is_final;
                $tradeArr['original_order_id'] = $params['original_order_id'] ?: '';
                $tradeArr['document_type'] = $params['document_type'] ?: 0;
                \Models\OilCardViceTradesExt::addTradesExt($tradeArr,$from);

                //G7WALLET-6403
                $subList = \Models\OilCardViceSubTrades::getList(['_export'=>1,"trades_id"=>$tradeInfo->id]);
                if(count($subList) > 0){
                    $sub_arr = $ids = [];
                    foreach ($subList as $_val){
                        $ids[] = $_val->id;
                        $sub_item = [];
                        $sub_item['trades_id'] = $newInsert->id;
                        $sub_item['cancel_sn'] = $newInsert->cancel_sn;
                        $sub_item['company_id'] = $_val['company_id'];
                        $sub_item['company_name'] = $_val['company_name'];
                        $sub_item['trade_money'] = $_val['trade_money']*-1;
                        $sub_item['gun_money'] = $_val['gun_money']*-1;
                        $sub_item['createtime'] = \helper::nowTime();
                        $sub_item['updatetime'] = \helper::nowTime();
                        $sub_arr[] = $sub_item;
                    }
                    OilCardViceSubTrades::batchAdd($sub_arr);
                    OilCardViceSubTrades::batchEdit(['idIn'=>$ids],["cancel_sn"=>$newInsert->cancel_sn,"updatetime"=>\helper::nowTime()]);
                }

                $tradeRes[] = $newInsert->id;
            } else {
                $content = '消费撤销未查询到原消费流水：stream_no' . $params['stream_no'] . '-third_trade_id' . $params['trade_id'];
                (new DingTalkAlarm())->alarmToGroup('消费撤销告警!', $content, [], TRUE, TRUE);
            }

            Capsule::connection()->commit();
        }catch (Exception $e){
            Capsule::connection()->rollBack();
            Log::error("Exception：" . $e->getMessage(), [], "cancelConsume");
            throw new \RuntimeException($e->getMessage(),2);
        }

        Log::error('params-commit' . var_export($params, TRUE), ["use_time"=>microtime(true) - $start], 'Consume_');

        CardViceToGos::batchUpdateToGos([$cardInfo->vice_no], "async");

        //通知gos系统
        if($assignId){
            AccountAssignToGos::remove([$assignId],"async");
        }

        //send gos
        if ($tradeRes) {
            //更新原订单
            CardViceTradesToGos::sendBatchUpdateTaskByQuene([$tradeInfo->id]);
            CardViceTradesToGos::sendBatchCreateTask($tradeRes);

            $_params = $newInsert->toArray();
            //取原交易的price_id
            $rebateInfo = \Models\OilCardViceTradeRebate::where('trade_id',$tradeInfo->id)->first();
            if($rebateInfo){
                $_params['price_id'] = $rebateInfo->gms_price_id;
            }

            //支持广州肇庆模式
            if($_params['station_code'] && OilCom::isElectronicCardTrade($_params['oil_com'])){
                Log::error('肇庆模式加油-撤销', [$_params], 'TradeToAssignJob');
                $vice_info = Station::getAssignCardByStationCode(['station_code'=>$_params['station_code'],'gms_order_id'=>$tradeInfo->api_id]);
                if($vice_info){
                    Log::error('肇庆-vice_info', $vice_info, 'TradeToAssignJob');
                    $sourceExt = OilCardViceTradesExt::getOneInfo(['trades_id' => $tradeInfo->id]);
                    $real_oil_num = $sourceExt->real_oil_num;
                    //异步下发分配任务
                    (new TradeToAssignJob([
                        'supplier_id' => $vice_info['supplier_id'],
                        'supplier_name' => $vice_info['supplier_name'],
                        'station_code' => $_params['station_code'],
                        'station_name' => $_params['trade_place'],
                        'vice_no' => $vice_info['vice_no'],
                        'api_id' => $tradeInfo->api_id, //取原订单的api_id
                        'trade_money'=>$_params['trade_money'],
                        'trade_price'=>$_params['trade_price'],
                        'trade_num'=>$_params['trade_num'],
                        'xpcode_pay_price' => $_params['xpcode_pay_price'],
                        'xpcode_pay_money' => abs($_params['xpcode_pay_money']),
                        'oil_name' => $_params['oil_name'],
                        'provice_code' => $_params['trade_place_provice_code'],
                        'trade_place' => $_params['trade_place'],
                        'is_cancel' => 2, //1正消费，2负消费
                        'area_code' => $vice_info['area_code'],
                        'ext' => ['real_oil_num' => $real_oil_num],
                    ]))->setTaskName(TradeToAssignJob::TITLE)
                    ->onQueue(TradeToAssignJob::QUEUE)
                    ->setTries(2)
                    ->dispatch();
                }
            }

            //G7WALLET-6201
            //异步标记返利
            (new MarkTradeFanliJob(['trade'=>$_params,'flag'=>2]))
                ->setTaskName("异步标记消费记录使用返利")
                ->onQueue("markTradeUseFanli")
                ->setTries(3)
                ->dispatch();

            $task = (new UpstreamSettleDataWriteJob($_params))
                ->setTaskName(UpstreamSettleDataWriteJob::TITLE)
                ->onQueue(UpstreamSettleDataWriteJob::QUEUE)
                ->setTries(2)
                ->dispatch();
            Log::error(__METHOD__ . ' 取消订单 异步写上游结算数据 派发', [$task], 'cancelConsume');
        }

        Log::error('tradeRes:=' . var_export($params, TRUE), ["use_time"=>microtime(true) - $start], 'Consume_');

        if($orderInfo->order_type == CardTradeConf::TRADE_ORDER){
            //加油撤销时,给用户发送模板消息
            $item['station_name'] = $tradeInfo->trade_place;
            $item['trade_money']  = $tradeInfo->trade_money . "元";
            $item['vice_no']      = $tradeInfo->vice_no;
            $item['remark']       = $params['remark'] ? $params['remark'] : '';
            WeChatTemplateMsg::cancelTrade($item);
        }

        //新增司机发送短信
        if ($tradeInfo && $tradeInfo->qz_drivertel && $cardInfo->card_level == 2 && $is_cash) {
            $consumeTime = date('m月d日H点i分', strtotime($tradeInfo->fetch_time));
            $revokeTime  = date('m月d日H点i分', time());
            $short_no    = substr($tradeInfo->vice_no, -6);
            $station     = $tradeInfo->trade_place;
            $oil_name    = $tradeInfo->oil_name;
            $trade_num   = $tradeInfo->trade_num;
            try{
                \Fuel\Service\CardVice::sendDriverConsumeTip([
                    'orgcode'    => $cardInfo->Org->orgcode,
                    'driver_tel' => $tradeInfo->qz_drivertel,
                    'content'    => '尊敬的客户您好，您于' . $consumeTime . '使用尾号' . $short_no . '的G7能源账户，在' . $station . ' 加【' . $oil_name . '】' . $trade_num . '升,' . $tradeInfo->trade_money . '元，该笔交易在' . $revokeTime . '已被撤销。如遇疑问详询客服电话************转2。'
                ]);
            }catch (Exception $e){
                Log::error("sms-Exception：" . $e->getMessage(), [], "cancelConsume");
            }

        }

        Log::error("return -=-result：", [$params], "cancelConsume");
        Log::error('return-' . var_export($params, TRUE), ["use_time"=>round(microtime(true) - $start,3)], 'Consume_');
        Response::json($params, 0, '成功');
    }

    /**
     * @title  为gas撤销分配单
     * @desc
     * @param array $details
     * @param int $dataFrom
     * @return array
     * @returns
     * array
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
     * @package  Fuel\Service
     * @deprecated  已不再使用，可以删除 跟@田徐保确认于2021-12-25 16:28
     */
    public function deleteAssign()
    {
        $params = \helper::filterParams();
        Log::error(json_encode($params), [], "txb_delete");
        //todo 线上与测试处理不同

        $oilCom = 21;
        global $app;
        if ($app->config->gas->is_expire == 0) {
            \helper::argumentCheck(['trade_id'], $params);
        } else {
            //\helper::argumentCheck(['trade_id','card_no',"trade_money"], $params);
            \helper::argumentCheck(['trade_id'], $params);
            //todo 查询消费记录
            $tradeInfo = OilCardViceTrades::getByApiId(['api_id' => $params['trade_id']]);
            if (count($tradeInfo) < 0 || !$tradeInfo) {
                Log::error('消费记录不存在,third_id:' . $params['trade_id'], [], "txb_delete");
                throw new RuntimeException('消费记录不存在,third_id:' . $params['trade_id'], 2);
            }
            $cardInfo = OilCardVice::getByViceNo(['vice_no' => $tradeInfo->vice_no]);
            if (count($cardInfo) <= 0) {
                Log::error("卡号不存在", [], "txb_delete");
                throw new RuntimeException('子账户账号' . $cardInfo->vice_no . "不存在", 2);
            }
            if (in_array($cardInfo->oil_com, OilCom::getZBankFirstList())) {
                Log::error('众邦卡不允许，撤销,卡号', [], "txb_delete");
                throw new RuntimeException('众邦账户不允许，撤销,子账户账号' . $cardInfo->vice_no, 2);
            }
            $oilCom = $cardInfo->oil_com;

            $hasInfo = OilCardViceTradesZBank::getByTradeApiId(['trade_id' => $params['trade_id'], 'money_type' => 2]);
            if ($hasInfo || count($hasInfo) > 0) {
                Log::error('卡号' . $cardInfo->vice_no . "，请勿重复撤销", [], "txb_delete");
                //throw new RuntimeException('卡号' . $cardInfo->vice_no . "，请勿重复撤销", 2);
                Response::json($params, 0, "成功");
            }

        }
        //todo 需要处理1号充值卡撤销
        //todo 需要把扣的钱，充值到卡的余额上
        $is_cash = FALSE;
        //todo 线上和测试不同，充值卡直接扣钱
        if ($oilCom == OilCom::GAS_FIRST_CHARGE) {
            Log::error('充值卡撤销', [], "txb_delete");
            $tradeRes = [];
            Capsule::connection()->beginTransaction();
            try {
                $balance = $cardInfo->card_remain + $tradeInfo->trade_money;
                if (!empty($tradeInfo->account_no)) {
                    //卡账户
                    $cardAccountInfo = OilCardAccount::where('cardSubAccountID', $tradeInfo->account_no)->first();
                    if (!$cardAccountInfo) {
                        throw new RuntimeException($tradeInfo->account_no . "账户不存在", 2);
                    }

                    Log::error($cardAccountInfo->subAccountType . '撤销', [], "txb_delete");
                    if ($cardAccountInfo->subAccountType == 'CASH') {
                        $upCard['id']          = $cardInfo->id;
                        $upCard['card_remain'] = $balance;
                        OilCardVice::edit($upCard);
                        $is_cash = TRUE;
                    } elseif ($cardAccountInfo->subAccountType == 'CREDIT') {
                        //临时抛出异常，暂时不允许撤销
                        //throw new RuntimeException('用信消费目前不允许撤销，如有疑问请联系油站运营人员', 2);
                        //todo 请求账户中心撤销
                        $consumerInfo = OilCardViceTradesZBank::getByTradeApiId(['trade_id' => $params['trade_id'], 'money_type' => 1]);

                        $res = (new AccountService())->revokeCardConsume([
                            'extID'       => $consumerInfo->extID,
                            'billID'      => $consumerInfo->billID,
                            'companyCode' => 'G7_DALIAN',
                            'totalAmount' => round($consumerInfo->trade_money * 100, 0),
                            //'comment' => 'comment',
                        ]);

                        //修改card_account里的卡余额
                        $balance = $cardAccountInfo->amount + $tradeInfo->trade_money;
                        $cardAccountInfo->update(['amount' => $balance]); //待验证
                    }
                } else {
                    $upCard['id']          = $cardInfo->id;
                    $upCard['card_remain'] = $balance;
                    OilCardVice::edit($upCard);
                    $is_cash = TRUE;
                }

                //增加扣款日志
                $addItem['vice_no']      = $tradeInfo->vice_no;
                $addItem['org_id']       = $cardInfo->org_id;
                $addItem['oil_com']      = $oilCom;
                $addItem['is_pay']       = 1;
                $addItem['trade_money']  = $tradeInfo->trade_money;
                $addItem['trade_api_id'] = $params['trade_id'];
                $addItem['money_type']   = 2;
                Log::error('增加扣款日志', [$addItem], "txb_delete");
                $res = OilCardViceTradesZBank::add($addItem);

                //删除消费记录
                $this->delTradesInfo($tradeInfo);

                //不进行删除，增加一笔负消费 @todo 2019-08-27
                $cancel_no = 'D_' . $tradeInfo->id;
                $tradeInfo->update(['cancel_sn' => $cancel_no]);
                $insertTrade                    = $tradeInfo->toArray();
                $insertTrade['cancel_sn']       = $cancel_no;
                $insertTrade['trade_money']     = -1 * $insertTrade['trade_money'];
                $insertTrade['trade_num']       = -1 * $insertTrade['trade_num'];
                $insertTrade['total_money']     = -1 * $insertTrade['total_money'];
                $insertTrade['service_money']   = -1 * $insertTrade['service_money'];
                $insertTrade['use_fanli_money'] = -1 * $insertTrade['use_fanli_money'];
                $insertTrade['receipt_remain']  = -1 * $insertTrade['receipt_remain'];
                $insertTrade['balance']         = $balance;
                $insertTrade['api_id']          = 'd_' . $res->id; //这里的api_id存的是zbank表里删除记录的id
                $insertTrade['createtime']      = \helper::nowTime();

                // 应付金额改成负值
                $insertTrade['xpcode_pay_money'] = -1 * $insertTrade['xpcode_pay_money'];

                unset($insertTrade['id']);
                Log::error('增加负消费', [$insertTrade], "txb_delete");
                $newInsert  = OilCardViceTrades::add($insertTrade);
                $tradeRes[] = $newInsert->id;
                $tradeRes[] = $tradeInfo->id;

                Capsule::connection()->commit();
            } catch (Exception $e) {
                Capsule::connection()->rollBack();
                Log::error('error:' . strval($e), [], "txb_delete");
                throw new RuntimeException($e->getMessage(), 2);
            }

            //send gos
            if ($tradeRes) {
                try {
                    CardViceTradesToGos::sendBatchCreateTask($tradeRes, 'sync');
                } catch (Exception $exception) {
                    Log::error('推送gos异常', ['exception' => strval($exception)], "txb_delete");
                }
            }

            CardViceToGos::batchUpdateToGos([$cardInfo->vice_no], "async");
        } elseif ($oilCom == OilCom::GAS_FIRST_TALLY) {
            Log::error('共享卡撤销', [], "txb_delete");

            Capsule::connection()->beginTransaction();
            try {
                if (substr($tradeInfo->account_no, 0, 3) == '208') {
                    $creditInfo = OilCreditAccount::getByAccountNoWithProvider($tradeInfo->account_no);
                    if ($creditInfo->is_own == 1) {
                        Log::error('自授信撤销', [], "txb_delete");
                        //查询分配单信息
                        $condition['oil_account_assign.trades_sn'] = $params['trade_id'];
                        $assignInfo                                = OilAccountAssign::getAssignList($condition);
                        Log::error("分配单：" . json_encode($assignInfo), [], "txb_delete");
                        if (count($assignInfo) == 0) {
                            Log::error('该单号' . $params['trade_id'] . "分配单不存在或已删除", [], "txb_delete");
                            throw new RuntimeException('该单号' . $params['trade_id'] . "分配单不存在或已删除", 2);
                        }

                        Log::error('自有产品授信撤销', [], "txb_delete");
                        // 创建还款工单
                        $repayParams['org_id']            = $assignInfo[0]->org_id;
                        $repayParams['credit_account_id'] = $creditInfo->id;
                        $repayParams['repay_money']       = $assignInfo[0]->money_total;
                        $repayParams['apply_time']        = \helper::nowTime();
                        $repayParams['remark']            = $assignInfo[0]->no . '撤销';
                        $repayParams['remark_work']       = $assignInfo[0]->no . '撤销';
                        $repayParams['no_way']            = 20;
                        //$repayParams['status'] = 1;

                        Log::error("创建还款单：" . json_encode($repayParams), [], "txb_delete");
                        $res = OilCreditRepay::add($repayParams);

                        OilAccountAssign::where('id', $assignInfo[0]->id)->update(['status' => -1]);

                        Log::error("还款单审核：" . $res->id, [], "txb_delete");
                        OilCreditRepay::audit(['id' => $res->id]);
                    } else {
                        Log::error('请求账户中心撤销', [], "txb_delete");
                        //throw new RuntimeException('用信消费目前不允许撤销，如有疑问请联系油站运营人员', 2);

                        $consumerInfo = OilCardViceTradesZBank::getByTradeApiId(['trade_id' => $params['trade_id'], 'money_type' => 1]);

                        if ($consumerInfo->billID) {
                            $res = (new AccountService())->revokeCardConsume([
                                'extID'       => $consumerInfo->extID,
                                'billID'      => $consumerInfo->billID,
                                'companyCode' => 'G7_DALIAN',
                                'totalAmount' => round($consumerInfo->trade_money * 100, 0),
                                //'comment' => 'comment',
                            ]);
                        }

                    }

                } else {
                    $is_cash = TRUE;

                    Log::error('现金撤销', [], "txb_delete");

                    //查询分配单信息
                    $condition['oil_account_assign.trades_sn'] = $params['trade_id'];
                    $assignInfo                                = OilAccountAssign::getAssignList($condition);
                    Log::error("分配单：" . json_encode($assignInfo), [], "txb_delete");
                    if (count($assignInfo) == 0) {
                        Log::error('该单号' . $params['trade_id'] . "分配单不存在或已删除", [], "txb_delete");
                        throw new RuntimeException('该单号' . $params['trade_id'] . "分配单不存在或已删除", 2);
                    }

                    // 判断分配单 是否已审核
                    if ($assignInfo[0]->status == AccountAssignStatus::AUDITED) {
                        //todo 销审分配单，把钱返到现金账户
                        try {
                            $res = $this->unAudit($assignInfo[0]->id, TRUE, $is_delete = TRUE);
                            Log::error("销审结果", [$res], 'txb_delete');
                        } catch (Exception $e) {
                            Log::error(__METHOD__ . strval($e), [], 'txb_delete');
                            throw new RuntimeException($e->getMessage(), $e->getCode());
                        }
                    }
                    //todo 删除分配单及详情
                    $condition_detail['oil_account_assign.id'] = $assignInfo[0]->id;
                    $details                                   = OilAccountAssign::getAssignDetails($condition_detail);
                    Log::error("分配单详情：" . json_encode($details), [], "txb_delete");
                    //删除加入回收站
                    $recycle                = [];
                    $recycle['table_name']  = 'oil_account_assign';
                    $recycle['pk']          = $assignInfo[0]->id;
                    $recycle['org_id']      = $assignInfo[0]->org_id;
                    $recycle['no']          = $assignInfo[0]->no;
                    $recycle['sn']          = $assignInfo[0]->sn;
                    $recycle['data']        = json_encode($details[0]);
                    $recycle['operator_id'] = $this->app->myAdmin->id;
                    $recycle['createtime']  = \helper::nowTime();
                    Log::error("删除分配单添加回收站", [$recycle], 'txb_delete');
                    $recycles = OilRecycle::add($recycle);

                    //if ($recycles) {
                    $resd  = OilAccountAssignDetails::removeByAssignId(["assignIds" => $assignInfo[0]->id]);
                    $resdd = OilAccountAssign::remove(["ids" => $assignInfo[0]->id]);
                    //}

                    Log::error("删除：" . $resd . '-=-' . $resdd, [], "txb_delete");

                }

                //删除消费记录
                $this->delTradesInfo($tradeInfo);

                //增加zbank流水
                $addItem['vice_no']      = $tradeInfo->vice_no;
                $addItem['org_id']       = $cardInfo->org_id;
                $addItem['oil_com']      = $oilCom;
                $addItem['is_pay']       = 1;
                $addItem['trade_money']  = -$tradeInfo->trade_money;
                $addItem['trade_api_id'] = $params['trade_id'];
                $addItem['api_id']       = $params['trade_id'];
                $addItem['money_type']   = 2;
                Log::error('增加扣款日志', [$addItem], "txb_delete");
                $res = OilCardViceTradesZBank::add($addItem);
                Log::error('增加扣款日志2', [$addItem], "txb_delete");

                //不进行删除，增加一笔负消费
                $cancel_no = 'D_' . $tradeInfo->id;
                $tradeInfo->update(['cancel_sn' => $cancel_no]);
                $insertTrade                    = $tradeInfo->toArray();
                $insertTrade['cancel_sn']       = $cancel_no;
                $insertTrade['trade_money']     = -1 * $insertTrade['trade_money'];
                $insertTrade['trade_num']       = -1 * $insertTrade['trade_num'];
                $insertTrade['total_money']     = -1 * $insertTrade['total_money'];
                $insertTrade['service_money']   = -1 * $insertTrade['service_money'];
                $insertTrade['use_fanli_money'] = -1 * $insertTrade['use_fanli_money'];
                $insertTrade['receipt_remain']  = -1 * $insertTrade['receipt_remain'];
                $insertTrade['api_id']          = 'D_' . $res->id; //这里的api_id存的是zbank表里删除记录的id
                $insertTrade['createtime']      = \helper::nowTime();

                // 应付金额改成负值
                $insertTrade['xpcode_pay_money'] = -1 * $insertTrade['xpcode_pay_money'];
                unset($insertTrade['id']);
                Log::error('增加负消费', [$insertTrade], "txb_delete");
                $newInsert  = OilCardViceTrades::add($insertTrade);
                $tradeRes[] = $newInsert->id;
                $tradeRes[] = $tradeInfo->id;

                Capsule::connection()->commit();
            } catch (Exception $e) {
                Capsule::connection()->rollBack();
                Log::error(strval($e), [], "txb_delete");
                throw new RuntimeException($e->getMessage(), 2);
            }

            //send gos
            if ($tradeRes) {
                CardViceTradesToGos::sendBatchCreateTask($tradeRes, 'sync');
            }

            //通知gos系统
            AccountAssignToGos::remove([$assignInfo[0]->id]);
        }

        //推送删除gos的消费记录
        //CardViceTradesToGos::sendBatchDeleteTask([$tradeInfo->id],'sync');

        //加油撤销时,给用户发送模板消息
        $item['station_name'] = $tradeInfo->trade_place;
        $item['trade_money']  = $tradeInfo->trade_money . "元";
        $item['vice_no']      = $tradeInfo->vice_no;
        $item['remark']       = $params['remark'] ? $params['remark'] : '';

        WeChatTemplateMsg::cancelTrade($item);

        //新增司机发送短信
        if ($tradeInfo->qz_drivertel && $cardInfo->card_level == 2 && $is_cash) {
            $consumeTime = date('m月d日H点i分', strtotime($tradeInfo->fetch_time));
            $revokeTime  = date('m月d日H点i分', time());
            $short_no    = substr($tradeInfo->vice_no, -6);
            $station     = $tradeInfo->trade_place;
            $oil_name    = $tradeInfo->oil_name;
            $trade_num   = $tradeInfo->trade_num;
            \Fuel\Service\CardVice::sendDriverConsumeTip([
                'orgcode'    => $cardInfo->Org->orgcode,
                'driver_tel' => $tradeInfo->qz_drivertel,
                'content'    => '尊敬的客户您好，您于' . $consumeTime . '使用尾号' . $short_no . '的G7能源账户，在' . $station . ' 加【' . $oil_name . '】' . $trade_num . '升,' . $tradeInfo->trade_money . '元，该笔交易在' . $revokeTime . '已被撤销。如遇疑问详询客服电话************转2。'
            ]);
        }

        Log::error('finist', [], 'txb_delete');

        Response::json($params, 0, "成功");
    }

    /*
     * 模拟还款工单
     */
    public function mockRePay($id)
    {
        require_once APP_MODULE_ROOT . DIRECTORY_SEPARATOR . 'oil_credit_repay' . DIRECTORY_SEPARATOR . 'control.php';

        $objRepay    = new oil_credit_repay();
        $_POST['id'] = $id;
        return $objRepay->audit();
    }

    //撤销时，删除消费记录
    public function delTradesInfo($tradeInfo)
    {
        Log::error('delTradesInfo-撤销消费', [$tradeInfo->api_id], "consume_trades");
        //需要把该条消费的返利返给用户
        $logData     = [];
        $content = [];
        $markWhere['trade_api_id'] = $tradeInfo->api_id;
        $markWhere['type']         = 1;
        $markInfo                  = OilMarkfanliremainLog::getByTradeId($markWhere);
        Log::error('delTradesInfo-撤销消费:count'.count($markInfo), [$tradeInfo->api_id], "consume_trades");
        if (count($markInfo) > 0) {
            $res = OilCardViceTrades::updateAccountFanliDiscount($markInfo->mark_fee,$markInfo->org_id,"+");
            Log::error('delTradesInfo-撤销消费:mark-res'.$res, [$tradeInfo->api_id], "consume_trades");
            if($res) {
                $itemLog['trade_api_id'] = $tradeInfo->api_id;
                $itemLog['org_id'] = $markInfo->org_id;
                $itemLog['type'] = 2;
                $itemLog['createtime'] = $itemLog['updatetime'] = \helper::nowTime();
                $itemLog['mark_fee'] = $markInfo->mark_fee;
                $itemLog['trade_money'] = $markInfo->trade_money;
                $logData[] = $itemLog;
            }
        }else{
            //兼容返利退回
            Log::error('delTradesInfo-撤销消费:use_fanli'.$tradeInfo->use_fanli_money, [$tradeInfo->api_id], "consume_trades");
            $org_info = OilOrg::getById(['id'=>$tradeInfo->org_id]);

            if( !OrgStatus::isUseNewRebateMark($org_info->orgcode) ){
            
                if(bccomp($tradeInfo->use_fanli_money,0,2) > 0){
                    $res = OilCardViceTrades::updateAccountFanliDiscount($tradeInfo->use_fanli_money,$tradeInfo->org_id,"+");
                    Log::error('delTradesInfo-撤销消费:trade-res'.$res, [$tradeInfo->api_id], "consume_trades");
                
                    $content[] = "* 交易ID：" . $tradeInfo->id;
                    $content[] = "* 订单号：" . $tradeInfo->api_id;
                    $content[] = "* 机构ID：" . $tradeInfo->org_id;
                    $content[] = "* 交易金额：" . $tradeInfo->trade_money;
                    $content[] = "* 使用返利：" . $tradeInfo->use_fanli_money;
                    $content[] = "* 交易时间：" .$tradeInfo->trade_time;
                }
            }
            
        }

        if (count($logData) > 0) {
            //记录返利扣减标记记录
            OilMarkfanliremainLog::batchAdd($logData);
        }

        if(count($content) > 0){
            (new DingTalkAlarm())->alarmToGroup("兼容返利退回预警", implode("\n", $content));
        }


        return TRUE;
    }

    /*
     * 充值卡现金帐号逻辑
     */
    public function cardCashAction($params, $cardInfo, $orgInfo,$zbankOrder = NULL)
    {

        global $timeStart;
        Log::error($params['stream_no'].'--充值卡扣款账户为（现金）账户逻辑', [], 'gasDeductMoney');
        //充值卡现金帐号逻辑
        //余额校验
        if (bccomp($params['money'], $cardInfo->card_remain, 2) > 0) {
            Log::error($params['stream_no'].'error:余额不足,vice_no:' . var_export($params, TRUE), [], 'gasDeductMoney');
            throw new RuntimeException($cardInfo->vice_no . '余额不足', '100120');
        }

        if(!$zbankOrder){
            //增加扣款日志
            $addItem['stream_no']              = $params['stream_no'];
            $addItem['client_order_id']        = $params['client_order_id'];
            $addItem['order_type']             = $params['order_type'];
            $addItem['vice_no']                = $params['card_no'];
            $addItem['org_id']                 = $orgInfo->id;
            $addItem['trade_time']             = $params['oil_time'] ? $params['oil_time'] : \helper::nowTime();
            $addItem['oil_name']               = $params['oil_name'] ? $params['oil_name'] : '';
            $addItem['trade_num']              = $params['oil_num'] ? $params['oil_num'] : '';
            $addItem['trade_price']              = $params['price'] ? $params['price'] : '';
            $addItem['trade_place']            = $params['trade_place'] ? $params['trade_place'] : '';
            $addItem['trade_address']          = $params['trade_address'] ? $params['trade_address'] : '';
            $addItem['truck_no']               = $params['truck_no'] ? $params['truck_no'] : '';
            $addItem['truename']               = $params['truename'] ? $params['truename'] : '';
            $addItem['station_id']             = $params['station_id'] ? $params['station_id'] : '';
            $addItem['org_name']               = $orgInfo->org_name;
            $addItem['oil_com']                = $cardInfo->oil_com;
            $addItem['is_pay']                 = 2;
            $addItem['trade_money']            = $params['money'];
            $addItem['trade_api_id']           = $params['id'];
            $addItem['api_id']                 = $params['id'];
            $addItem['money_type']             = 1;
            $addItem['deduction_account_no']   = $cardInfo->deduction_account_no;
            $addItem['deduction_account_name'] = $cardInfo->deduction_account_name ? $cardInfo->deduction_account_name : ConsumeType::FIRST_CHARGE_ACCOUNT_NAME;
            $addItem['data_from']              = $params['trade_from'] ? $params['trade_from'] : null;
            $addItem['createtime']             = \helper::nowTime();
            Log::error($params['stream_no'].'扣卡扣款日志', [$addItem], 'gasDeductMoney');
            $zbankRes = OilCardViceTradesZBank::add($addItem); //不受事务影响
        }else{
            $zbankRes = $zbankOrder;
        }

        Capsule::connection()->beginTransaction();
        try {
            //order锁单
            $this->checkOrder($params,TRUE);

            $_cardInfo             = OilCardVice::getByIdForLock(['id' => $cardInfo->id]);
            $upCard['id']          = $_cardInfo->id;
            $upCard['card_remain'] = $_cardInfo->card_remain - $params['money'];
            //$upCard['trade_time']  = \helper::nowTime();
            //扣卡余额
            Log::error($params['stream_no'].'扣卡余额-开始字段', [$upCard], 'gasDeductMoney');
            $res = OilCardVice::edit($upCard);
            OilCardViceTradesZBank::edit(['id' => $zbankRes->id, 'is_pay' => 1,'balance'=>$upCard['card_remain']]);//修改成为已付

            Log::error($params['stream_no'].'扣卡余额-更新卡现金余额字段' . json_encode($res), [$upCard], 'gasDeductMoney');
            if ((_getTime() - $timeStart) > 12) {
                Log::error($params['stream_no']."执行超时,请稍后再试", [], "gasDeductMoney");
                throw new \RuntimeException('执行超时,请稍后再试！', 1103);
            }
            Capsule::connection()->commit();
        } catch (Exception $e) {
            Log::error('msg', [$e->getMessage(), $e->getCode()], 'gasDeductMoney');
            Capsule::connection()->rollback();
            $zbankRes = OilCardViceTradesZBank::edit(['id' => $zbankRes->id, 'is_pay' => 3]);//修改成为失败
            Log::error($params['stream_no'].'msg:OilCardViceTradesZBank::edit' . var_export($zbankRes, TRUE), [$e->getMessage(), $e->getCode()], 'gasDeductMoney');

            //发送报警
            try {
                (new DingTalkAlarm())->alarmToGroup('充值卡扣款异常', '卡号：' . $params['card_no'] . PHP_EOL . 'trade_api_id:' . $params['id'], [], TRUE, TRUE);
            } catch (\Exception $e) {

            }
            //更改流水的付款状态
            Log::error($params['stream_no'].'msg', [$e->getMessage(), $e->getCode()], 'gasDeductMoney');
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
        //推送到gos
        CardViceToGos::batchUpdateToGos([$cardInfo->vice_no], "async"); //是否变为异步
    }

    /*
     * 充值卡授信帐号逻辑
     */
    public function cardCreditAction($params, $cardAccountInfo, $cardInfo, $orgInfo, $zbankOrder = NULL)
    {
        global $timeStart;
        Log::error($params['stream_no'].'--充值卡扣款账户为（授信）账户逻辑', [], 'gasDeductMoney');

        //卡授信账户校验
        $cardCreditaccountInfo = (new AccountService())->getZBankAccountInfo(['subAccountID' => $cardAccountInfo->cardSubAccountID, "one" => 1]);
        Log::error($params['stream_no']."卡授信账户信息" . var_export($cardCreditaccountInfo, TRUE), [], "gasDeductMoney");
        if ($cardCreditaccountInfo->status != 'NORMAL') {
            throw new RuntimeException("授信账户异常", 2);
        }
        //根据cardSubAccountID获取账户中心的卡授信额度
        $balance = $cardCreditaccountInfo->restCreditAmount / 100;

        //机构授信账户校验
        Log::error($params['stream_no'].'机构授信账户校验,common_account_no:' . var_export($cardAccountInfo->common_account_no, TRUE), [], 'gasDeductMoney');
        $creditinfos = OilCreditAccount::checkCreditAccount($cardAccountInfo->common_account_no);

        //授信额度校验
        $creditBalance = $creditinfos['creditInfo']->restCreditAmount / 100;
        if ($params['money'] > $creditBalance) {
            //throw new RuntimeException('授信额度不足此次消费', 2); //0923修复，卡消费不判断机构额度
        }

        //服务费生成
        $service_fee = bcmul($creditinfos['credit_account_info']->service_fee, $params['oil_num'], 2);
        Log::error($params['stream_no'].'$service_fee：'.$service_fee, [], 'gasDeductMoney');
        //$service_fee     = floor($service_fee * 100) / 100; //12月9号发现服务费算法精度有问题特此注释本行
        $params['money'] = $params['money'] + $service_fee; //加上服务费 截取2位小数
        Log::error($params['stream_no'].'$service_fee：', [$service_fee], 'gasDeductMoney');
        Log::error($params['stream_no'].'money_balance', ['money' => $params['money'], 'balance' => $balance], 'gasDeductMoney');
        //卡余额+服务费判断
        if (bccomp($params['money'], $balance, 2) > 0) {
            Log::error($params['stream_no'].'卡授信额度不足,vice_no:', ['money' => $params['money'], 'balance' => $balance], 'gasDeductMoney');
            throw new RuntimeException($creditinfos['credit_account_info']->name . '信用额度不足', '100120');
        }

        //消费限制
        $this->checkConsumeTop($cardInfo, $creditinfos['creditInfo'], $params['money'],$orgInfo);

        $tradeInfo = OilCardViceTradesZBank::formatData2($params, $orgInfo, $cardInfo, $params['money'], $service_fee, $zbankOrder); //此方法不受事务约束

        Capsule::connection()->beginTransaction();
        try {
            //order加锁查询,不更改支付中,请求成都挪在事务里,formatData修改orderstatus挪到事务里面
            $this->checkOrder($params,TRUE);

            //锁住卡账户 lockForUpdate
            OilCardAccount::getByIdLock(['id' => $cardAccountInfo->id]);

            //请求账户中心扣款接口
            $result = OilCardViceTrades::trades2G7Pay($tradeInfo, $creditinfos, $cardCreditaccountInfo, $orgInfo);
            // @todo 返回值是否符合预期
            if (!$result || !isset($result->billID) || !$result->billID) {
                Log::error($params['stream_no'].__METHOD__ . "#请求支付扣费失败", [$result], 'gasDeductMoney');
                throw new \RuntimeException('请求支付扣费失败，请重试~', 2);
            }

            //cardAccount扣款
            $uCardData = [
                'amount' => $balance - $params['money'],
                'id'     => $cardAccountInfo->id
            ];
            Log::error($params['stream_no'].'cardAccount扣款：', [$uCardData], 'gasDeductMoney');

            $updateResult = OilCardAccount::where('id', $cardAccountInfo->id)->update($uCardData);

            if(!$updateResult){
                Log::error($params['stream_no'].__METHOD__."#本地数据库操作失败", [$updateResult], 'gasDeductMoney');
                throw new \RuntimeException('支付失败，请重试！', 1103);
            }
            if ((_getTime() - $timeStart) > 12) {
                Log::error($params['stream_no']."执行超时,请稍后再试", [], "gasDeductMoney");
                throw new \RuntimeException('执行超时,请稍后再试！', 1103);
            }

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            //如果本地失败，撤销掉账户中心扣费
            try {
                $res = (new AccountService())->revokeCardConsume([
                    'extID'       => $result->extID,
                    'billID'      => $result->billID,
                    'companyCode' => 'G7_DALIAN',
                    'totalAmount' => round($params['money'] * 100, 0),
                    //'comment' => 'comment',
                ]);
                OilCardViceTradesZBank::edit(['id' => $tradeInfo->id, 'is_pay' => 4]);//系统因为异常自动撤销
            } catch (Exception $exception) {
                $content[] = "* 机构编码：" . $orgInfo->orgcode;
                $content[] = "* 机构名称：" . $orgInfo->org_name;
                $content[] = "* 卡号：" . $cardInfo->vice_no;
                $content[] = "* 消费金额：" . $params['money'];
                $content[] = "* 当前余额：" . $balance;
                $content[] = "* 服务费：" . $service_fee;
                $content[] = "* 失败原因：" . $exception->getMessage();
                (new DingTalkAlarm())->alarmToGroup("支付失败，且退款失败", implode("\n", $content), [], TRUE, TRUE);
            }

            Log::error($params['stream_no'].'授信异常：', [$e->getMessage(), $e->getCode()], 'gasDeductMoney');
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        //推送到gos
        CardViceToGos::batchUpdateToGos([$cardInfo->vice_no], "async"); //是否变为异步
    }

    /*
     * 机构现金账户逻辑
     * @todo 共享卡不走分配逻辑
     */
    public function orgCashAction($orgInfo, $params, $creditInfo = null, $cardInfo, $zbankOrder = NULL)
    {
        Log::error($params['stream_no'].'--进入机构现金账户逻辑：', [], 'gasDeductMoney');
        $upCard['id']         = $cardInfo->id;
        $upCard['trade_time'] = \helper::nowTime();
        //更新卡的最新消费时间
        $res = OilCardVice::edit($upCard);
        Log::error($params['stream_no'].'扣卡余额-更新卡现金余额字段' . json_encode($res), [$upCard], 'gasDeductMoney');
        //推送到gos
        CardViceToGos::batchUpdateToGos([$cardInfo->vice_no], "async"); //是否变为异步
        Log::error($params['stream_no'].'推送到gos结束', [], 'gasDeductMoney');

        $service_fee = 0;
        if (!$creditInfo) {
            // @todo 待优化
            $used_money  = FrozenMoney::accountBalance($orgInfo->id);
            Log::error($params['stream_no'].'获取used_money结束', [], 'gasDeductMoney');
            $accountInfo = OilAccountMoney::getByOrgId(['org_id' => $orgInfo->id]);
            Log::error($params['stream_no'].'获取$accountInfo结束', [], 'gasDeductMoney');
            try {
                AccountBalanceAlarm::alarm($used_money, $params['money'], $orgInfo->orgcode);
            } catch (\Exception $e) {

            }
            Log::error($params['stream_no'].'报警AccountBalanceAlarm结束', [], 'gasDeductMoney');
            //1119日现金不够直接不让创建分配单
            if (bcsub($used_money, $params['money'], 2) < 0) {
                Log::error($params['stream_no'].'$used_money---' . $used_money, [], 'gasDeductMoney');
                Log::error($params['stream_no'].'money---' . $params['money'], [], 'gasDeductMoney');
                throw new RuntimeException('账户余额不足，或存在冻结金额导致账户可用余额不足', '100120');
            }
        } else {
            //自授信逻辑
            $accountInfo = $creditInfo;
            $used_money  = (new AccountGredit())->setAccount($accountInfo->account_no, FALSE)->getAccountBalance();
            $service_fee = bcmul($accountInfo->CreditProvider->service_fee,$params['oil_num'],2);
            Log::error($params['stream_no'].'$service_fee：', [$service_fee], 'gasDeductMoney');
            $params['money'] = $params['money'] + $service_fee; //加上服务费 截取2位小数
            Log::error($params['stream_no'].'$service_fee：', [$params['money']], 'gasDeductMoney');
            //账户可用余额判断
            if (round(($used_money - $params['money']), 2) < 0) {
                Log::error($params['stream_no'].'$used_money---' . $used_money, ['stream_no'=>$params['stream_no']], 'gasDeductMoney');
                Log::error($params['stream_no'].'money---' . $params['money'], [], 'gasDeductMoney');
                throw new RuntimeException('授信账户余额不足，或存在冻结金额导致授信账户可用余额不足', '100120');
            }
        }

        if(!$zbankOrder){
            //增加扣款日志
            $addItem['stream_no']              = $params['stream_no'];
            $addItem['client_order_id']        = $params['client_order_id'];
            $addItem['order_type']             = $params['order_type'];
            $addItem['vice_no']                = $params['card_no'];
            $addItem['org_id']                 = $orgInfo->id;
            $addItem['trade_time']             = $params['oil_time'] ? $params['oil_time'] : \helper::nowTime();
            $addItem['oil_name']               = $params['oil_name'] ? $params['oil_name'] : '';
            $addItem['trade_num']              = $params['oil_num'] ? $params['oil_num'] : '';
            $addItem['trade_price']            = $params['price'] ? $params['price'] : '';
            $addItem['trade_place']            = $params['trade_place'] ? $params['trade_place'] : '';
            $addItem['trade_address']          = $params['trade_address'] ? $params['trade_address'] : '';
            $addItem['truck_no']               = $params['truck_no'] ? $params['truck_no'] : '';
            $addItem['truename']               = $params['truename'] ? $params['truename'] : '';
            $addItem['station_id']             = $params['station_id'] ? $params['station_id'] : '';
            $addItem['org_name']               = $orgInfo->org_name;
            $addItem['oil_com']                = $cardInfo->oil_com;
            $addItem['is_pay']                 = 2;
            $addItem['trade_money']            = $params['money'];
            $addItem['trade_api_id']           = $params['id'];
            $addItem['api_id']                 = $params['id'];
            $addItem['money_type']             = 1;
            $addItem['deduction_account_no']   = $cardInfo->deduction_account_no;
            $addItem['deduction_account_name'] = $cardInfo->deduction_account_name ? $cardInfo->deduction_account_name : ConsumeType::FIRST_TALLY_ACCOUNT_NAME;
            $addItem['service_money']          = $service_fee;
            $addItem['data_from']              = $params['trade_from'] ? $params['trade_from'] : null;
            $addItem['createtime']             = \helper::nowTime();
            Log::error($params['stream_no'].'扣卡扣款日志', [$addItem], 'gasDeductMoney');
            $zbankOrder = OilCardViceTradesZBank::add($addItem);
        }

        Log::error($params['stream_no'].'获取is_exit', [], 'gasDeductMoney');
        $is_exit = OilAccountAssign::getByTradesSnForLock($params['id']);
        Log::error($params['stream_no'].'获取is_exit', [], 'gasDeductMoney');
        if (!$is_exit) {
            //创建分配单记录
            $assignParams['orgcode']       = $params['orgcode'];
            $assignParams['assign_total']  = $params['money'];
            $assignParams['account_no']    = $accountInfo->account_no;
            $assignParams['assign_num']    = 1;
            $assignParams['details']       = [['assign_amount' => $params['money'], 'vice_no' => $params['card_no']]];
            $assignParams['other_creator'] = '1号记账卡';
            $assignParams['is_gas_deduct'] = 1;
            $assignParams['gas_trades_id'] = $params['id'];
            $assignParams['data_from']     = 2;
            $assignParams['card_consume']  = 1;

            Log::error($params['stream_no'].'分配开始：$assignParams---' . var_export($assignParams, TRUE), [], 'gasDeductMoney');
            Capsule::connection()->beginTransaction();
            try {
                $data = (new Fuel\Service\Assign($assignParams))
                    ->formatParams()
                    ->splitOrder()
                    ->validateForFisrt()//是否拦截这里没有校验
                    ->save();

                foreach ($data['assignIds'] as $id) {
                    //add work log
                    $this->addWorkLog($id);
                }
                Capsule::connection()->commit();
            } catch (Exception $e) {
                Capsule::connection()->rollback();
                Log::error(__METHOD__ . strval($e), ['stream_no'=>$params['stream_no']], 'gasDeductMoney');
                Log::error('msg', [$e->getMessage(), $e->getCode()], 'gasDeductMoney');
                throw new RuntimeException($e->getMessage(), $e->getCode());
            }
            Log::error($params['stream_no'].'分配结束', [], 'gasDeductMoney');

            //现金账户可用余额判断
            Log::error($params['stream_no'].'卡现金use_money:' . $used_money, [], 'gasDeductMoney');
            $balance = $used_money - $params['money'];
            if (bcsub($used_money, $params['money'], 2) >= 0) {
                Log::error($params['stream_no'].'pushG7pay开始-assignIds' . var_export($data['assignIds'], TRUE), [], 'gasDeductMoney');
                foreach ($data['assignIds'] as $id) {
                    //push至G7Pay
                    (new AssignService())->createById($id);
                }

                Log::error($params['stream_no'].'pushG7pay结束', [], 'gasDeductMoney');
                //直接审核通过
                $this->auditForGas($data['assignIds'], $card_consume = 1,$balance);
                Log::error($params['stream_no'].'auditForGas审核结束', [], 'gasDeductMoney');
            } else {
                Log::error($params['stream_no'].'卡余额不够此笔消费', [], 'gasDeductMoney');
            }

            //push到Gos系统
            AccountAssignToGos::sendBatchUpdateTask($data['assignIds'], 'async');
        } else {
            $balance = $used_money - $params['money'];
            Log::error($params['stream_no'].'分配单此消费以存在', [$params['id']], 'gasDeductMoney');
            //判断单子的状态,1如果待审核,调取审核,2驳回,变为待审核,调取审核,3,已审核,返回成功状态并修改订单状态 同步gos
            if($is_exit->status == 0){
                Log::error($params['stream_no'].'待审核,调取审核', [], 'gasDeductMoney');
                //直接审核通过
                $this->auditForGas([$is_exit->id], $card_consume = 1,$balance);
                Log::error($params['stream_no'].'审核auditForGas结束', [], 'gasDeductMoney');
            }elseif($is_exit->status == -1){
                Log::error($params['stream_no'].'驳回,变为待审核,调取审核', [], 'gasDeductMoney');
                $is_exit->update(['status'=>0]);//驳回改为待审核
                //直接审核通过
                $this->auditForGas([$is_exit->id], $card_consume = 1,$balance);
                Log::error($params['stream_no'].'审核auditForGas结束', [], 'gasDeductMoney');
            }elseif($is_exit->status == 1){
                Log::error($params['stream_no'].'修改订单状态，返回成功状态', [], 'gasDeductMoney');
                OilCardViceTradesZBank::where('id',$zbankOrder->id)->update(['is_pay' => 1,'balance'=>$balance]);

                Response::json(TRUE, 0, '成功');
            }

            //push到Gos系统
            AccountAssignToGos::sendBatchUpdateTask([$is_exit->id], 'async');
            //throw new RuntimeException('此消费已存在', 2);
        }

        Log::error($params['stream_no'].'finish', [], 'gasDeductMoney');
    }

    /*
     * 机构授信帐号逻辑
     */
    public function orgCreditAction($params, $credit_account_no, $cardInfo, $orgInfo, $zbankOrder = NULL)
    {
        Log::error($params['stream_no'].'--进入机构授信帐号逻辑：', [], 'gasDeductMoney');

        //机构授信账户校验
        $creditinfos = OilCreditAccount::checkCreditAccount($credit_account_no);

        //服务费判断
        $service_fee = bcmul($creditinfos['credit_account_info']->service_fee, $params['oil_num'], 2);
        Log::error($params['stream_no'].'$service_fee：', [$service_fee], 'gasDeductMoney');
        $params['money'] = $params['money'] + $service_fee; //floor($service_fee * 100) / 100; //加上服务费 截取2位小数
        Log::error($params['stream_no'].'$service_fee：', [$params['money']], 'gasDeductMoney');

        $tradeInfo = OilCardViceTradesZBank::formatData2($params, $orgInfo, $cardInfo, $params['money'], $service_fee, $zbankOrder);

        Capsule::connection()->beginTransaction();
        try {
            //order加锁
            $this->checkOrder($params,TRUE);

            //机构授信额度校验
            $creditBalance = $creditinfos['creditInfo']->restCreditAmount / 100;
            if ($params['money'] > $creditBalance) {
                throw new RuntimeException('授信额度不足此次消费', '100120');
            }

            //消费限制
            $this->checkConsumeTop($cardInfo, $creditinfos['creditInfo'], $params['money'],$orgInfo);

            //请求账户中心扣款接口
            OilCardViceTrades::trades2G7Pay($tradeInfo, $creditinfos, null, $orgInfo);

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            //如果本地失败，撤销掉账户中心扣费
            try {
                $res = (new AccountService())->revokeCardConsume([
                    'extID'       => $tradeInfo->extID,
                    'billID'      => $tradeInfo->billID,
                    'companyCode' => 'G7_DALIAN',
                    'totalAmount' => round($params['money'] * 100, 0),
                    //'comment' => 'comment',
                ]);
                OilCardViceTradesZBank::edit(['id' => $tradeInfo->id, 'is_pay' => 4]);//系统因为异常自动撤销
            } catch (Exception $exception) {
                $content[] = "* 机构编码：" . $orgInfo->orgcode;
                $content[] = "* 机构名称：" . $orgInfo->org_name;
                $content[] = "* 卡号：" . $cardInfo->vice_no;
                $content[] = "* 消费金额：" . $params['money'];
                $content[] = "* 当前余额：" . $creditBalance;
                $content[] = "* 服务费：" . $service_fee;
                $content[] = "* 失败原因：" . $exception->getMessage();
                (new DingTalkAlarm())->alarmToGroup("支付失败，且退款失败", implode("\n", $content), [], TRUE, TRUE);
            }

            Log::error($params['stream_no'].'机构授信异常：', [$e->getMessage(), $e->getCode()], 'gasDeductMoney');
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

    }

    /*
     * 授信限额规则
     */
    public function checkConsumeTop($cardInfo, $creditInfo, $consumerMoney,$orgInfo)
    {
        $limit_rule = ConsumeType::getCreditLimit($orgInfo->orgcode);

        $creditTotal = $creditInfo->creditAmount / 100;
        if ($cardInfo->paylimit == 1) {
            //如果这张卡启用限额
            $oil_top   = $cardInfo->oil_top <= $limit_rule['oil_top'] ? $cardInfo->oil_top : $limit_rule['oil_top'];
            $day_top   = $cardInfo->day_top <= $limit_rule['day_top'] ? $cardInfo->day_top : $limit_rule['day_top'];
            $month_top = $cardInfo->month_top <= $creditTotal ? $cardInfo->month_top : $creditTotal;
        } else {
            $oil_top   = $limit_rule['oil_top'];
            $day_top   = $limit_rule['day_top'];
            $month_top = $creditTotal;
        }
        Log::error($params['stream_no'].'消费限额:', ['oil_top' => $oil_top, 'day_top' => $day_top, 'month_top' => $month_top], 'gasDeductMoney');
        //判断次限额
        if (bccomp( $consumerMoney,$oil_top,2) > 0) {
            Log::error($params['stream_no'].'本次加油金额超过次限额', [], 'gasDeductMoney');
            throw new RuntimeException('您本次的交易金额（' . $consumerMoney . '）已超过本次交易限额（' . $oil_top . '），请尝试降低交易金额。', '100130');
        }

        //判断日限额,取此卡今天所有消费
        $dayTradesSum = OilCardViceTrades::where('vice_no', $cardInfo->vice_no)
            ->where('createtime', '>', date('Y-m-d') . ' 00:00:00')
            ->sum('trade_money');
        if (bccomp( ($dayTradesSum + $consumerMoney),$day_top,2) > 0 ) {
            Log::error($params['stream_no'].'本次加油金额超过日限额', ['dayTradesSum' => $dayTradesSum, 'day_top' => $day_top], 'gasDeductMoney');
            throw new RuntimeException('您今日的交易总额（' . $dayTradesSum . '）已超过本日交易限额（' . $day_top . '），请尝试降低交易金额或明日再进行交易。', '100131');
        }

        //判断日限额,取此卡今天所有消费
        $monthTradesSum = OilCardViceTrades::where('vice_no', $cardInfo->vice_no)
            ->where('createtime', '>', date('Y-m') . '-01 00:00:00')
            ->sum('trade_money');
        if ( bccomp( ($monthTradesSum + $consumerMoney), $month_top,2) > 0) {
            Log::error($params['stream_no'].'本次加油金额超过月限额', ['monthTradesSum' => $monthTradesSum, 'month_top' => $month_top], 'gasDeductMoney');
            throw new RuntimeException('本次加油金额超过月限额', '100132');
        }

    }

    /*
     * 校验订单
     */
    public function checkOrder(array $params,$islock = FALSE)
    {
        $zbankOrder = NULL;
        $orderInfo = OilCardViceTradesZBank::getByStreamNo($params['stream_no'],$islock);
        if($orderInfo && !$islock){
            if($orderInfo->is_pay == 1){
                Response::json(TRUE, 0, '已成功'); //支持幂等特殊code码 1890
            }

//            if($orderInfo->is_pay == 2){
//                //Response::json(TRUE, 0, '正在支付中');
//                throw new \RuntimeException('正在支付中,耐心等待',2);
//            }

            if($orderInfo->is_pay == 4){
                //Response::json(TRUE, 0, '已取消交易'); //如果交易被取消过，不允许再次发起交易
                throw new \RuntimeException('已取消交易不可继续交易',2);
            }

            if($orderInfo->is_pay == 3 || $orderInfo->is_pay == 2){
                if($orderInfo->extID && !$islock){
                    //查询成都账单状态
                    $payInfo = (new AccountService())->getCardConsume(['extID' => $orderInfo->extID]);

                    if($payInfo){
                        if($payInfo->consumeResult == 'APPLYING'){
                            $orderInfo->update(['is_pay'=>2]); //本地支付状态改为和远程一致
                            throw new \RuntimeException('正在支付中，请稍后再试',2);
                        }

                        if($payInfo->consumeResult == 'SUCCESS'){
                            $orderInfo->update(['is_pay'=>1]); //本地支付状态改为和远程一致
                            Response::json(TRUE, 1890, '已成功'); //支持幂等特殊code码 1890
                        }
                    }

                }

                $old_trade_price = $orderInfo->trade_price;
                //重新发起支付
                //todo 问题，如果同流水号，订单金额发生了变化是否需要覆盖。
                $updateItem['client_order_id']        = $params['client_order_id'];
                $updateItem['order_type']             = $params['order_type'];
                $updateItem['vice_no']                = $params['card_no'];
                $updateItem['trade_time']             = $params['oil_time'] ? $params['oil_time'] : \helper::nowTime();
                $updateItem['oil_name']               = $params['oil_name'] ? $params['oil_name'] : '';
                $updateItem['trade_num']              = $params['oil_num'] ? $params['oil_num'] : '';
                $updateItem['trade_price']            = $params['price'] ? $params['price'] : '';
                $updateItem['trade_place']            = $params['trade_place'] ? $params['trade_place'] : '';
                $updateItem['trade_address']          = $params['trade_address'] ? $params['trade_address'] : '';
                $updateItem['truck_no']               = $params['truck_no'] ? $params['truck_no'] : '';
                $updateItem['truename']               = $params['truename'] ? $params['truename'] : '';
                $updateItem['station_id']             = $params['station_id'] ? $params['station_id'] : '';
                $updateItem['is_pay']                 = 2;
                $updateItem['trade_money']            = $params['money'];
                $updateItem['trade_api_id']           = $params['id'];
                $updateItem['udatetime']             = \helper::nowTime();
                $orderInfo->update($updateItem);

                if($old_trade_price != $params['price']){
                    //发送报警
                    try {
                        (new DingTalkAlarm())->alarmToGroup('扣款有异常', '流水：' . $params['stream_no'] . PHP_EOL . 'trade_api_id:' . $params['id']. PHP_EOL .'两次金额发生变化', ['old'=>$old_trade_price,'new'=>$params['price']], TRUE, TRUE);
                    } catch (\Exception $e) {

                    }
                }

                $orderInfo = OilCardViceTradesZBank::getByStreamNo($params['stream_no']);

                $zbankOrder = $orderInfo;
            }
        }

        return $zbankOrder;
    }

    /**
     * @title  针对一号卡创建分配单，扣除账户消费金额
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function gasDeductMoneyNew()
    {
        $zbankOrder = NULL;
        $params = \helper::filterParams();
        Log::error($params['stream_no'].'addTrades-%params- ' . var_export($params, TRUE), [], 'gasDeductMoney');
        \helper::argumentCheck(['orgcode', 'card_no', 'money','stream_no'], $params);

        //新增参数：trade_from,交易来源
        //新增预授权字段 20:预授权 10:普通
        $params['order_type'] = isset($params['order_type']) && $params['order_type'] == 20 ? 20 : 10;

        $card_no = $params['card_no'];
        $history_id = $params['id'];

        //txb 增加交易来源：ENINET-1902,处理出示付款码情况下的交易来源
        if( !isset($params['trade_from']) || empty($params['trade_from']) ){
            $params['trade_from'] = (new CardTradeService())->getTradeFrom($params['card_no']);
        }

        $from = isset($params['trade_from']) && $params['trade_from'] ? $params['trade_from'] : null;

        $wsData['card_no'] = $card_no;
        $wsData['pre_history_id'] = $history_id;
        $wsData['message'] = '支付成功';
        $wsData['code'] = "0";
        $wsData['msg_type'] = 2;
        //校验订单
        $zbankOrder = $this->checkOrder($params);

        $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            Log::error($params['stream_no'].'error:orgcode' . $params['orgcode'] . '不存在', [], 'gasDeductMoney');
            $wsData['message'] = $params['orgcode'] . '不存在';
            $wsData['code'] = "100100";
            $wsData['msg_type'] = 3;

            (new \Fuel\Service\FossOrderService())->packData( $wsData );
            throw new RuntimeException('orgcode' . $params['orgcode'] . '不存在', 2);
        }

        if ($orgInfo->status != '1') {
            $wsData['message'] = '您的当前机构正在维护请稍后再试';
            $wsData['code'] = "100101";
            $wsData['msg_type'] = 3;
            (new \Fuel\Service\FossOrderService())->packData( $wsData );
            throw new \RuntimeException('您的当前机构正在维护请稍后再试', 2);
        }

        /*if( in_array(strtoupper(substr($orgInfo->orgcode,0,6)),['201WFV']) ){
            throw new RuntimeException('您的加油卡已被逾期冻结，请与您的车队取得联系', 2);
        }*/

        if (isset($params['oil_time']) && strtotime($params['oil_time']) < strtotime('2018-08-23 12:00:00')) {
            Log::error($params['stream_no'].'error:时间小于切割时间2018-08-23 12:00:00不创建分配单', [], 'gasDeductMoney');
            Response::json('时间小于切割时间2018-08-23 12:00:00不创建分配单', 0, '成功');
            exit;
        }

        // @todo 并发情况

        $cardInfo = OilCardVice::getByViceNo(['vice_no' => $params['card_no']]);
        if (!$cardInfo) {
            Log::error($params['stream_no'].'error:卡号：' . $params['card_no'] . '状态异常', [], 'gasDeductMoney');
            $wsData['message'] = "卡号:".$params['card_no'] . '不存在';
            $wsData['code'] = "100102";
            $wsData['msg_type'] = 3;
            (new \Fuel\Service\FossOrderService())->packData( $wsData );
            throw new RuntimeException('子账户账号：' . $params['card_no'] . '不存在', 2);
        }
        if (trim($cardInfo->status) != \Fuel\Defines\ViceCardStatus::USING) {
            Log::error($params['stream_no'].'error:卡号：' . $params['card_no'] . '状态异常', [], 'gasDeductMoney');
            $wsData['message'] = "卡号:".$params['card_no'] . '状态异常';
            $wsData['code'] = "100103";
            $wsData['msg_type'] = 3;
            (new \Fuel\Service\FossOrderService())->packData( $wsData );
            if( !in_array($from,[
                    CardTradeConf::GAS_PATCH,
                    CardTradeConf::GAS_PAD_PATCH,
                    CardTradeConf::GMS_PAD_PATCH,
                    CardTradeConf::AFTERWARDS_ADD,
                    CardTradeConf::GMS_EXCEPTION,
                ]) || trim($cardInfo->status) != \Fuel\Defines\ViceCardStatus::FREEZE ) {
                throw new RuntimeException('子账户账号：' . $params['card_no'] . '状态异常', 2);
            }
        }

        //油站限制校验
        if (isset($params['station_id']) && $params['station_id']) {
            try {
                $data = (new \GosSDK\Gos())
                    ->setMethod('v1/stationLimit/checkStationByCardNoAndStation')
                    ->setParams([
                        'card_no'    => strval($params['card_no']),
                        'station_id' => strval($params['station_id']),
                        'oil_name_val' => isset($params['oil_name_val']) && $params['oil_name_val'] ? strval($params['oil_name_val']) : '',
                    ])
                    ->sync();
            } catch (Exception $e) {
                Log::error($params['stream_no'].'error:油站限制' . strval($e), [], 'gasDeductMoney');
                $wsData['message'] = "油站限制";
                $wsData['code'] = "100104";
                $wsData['msg_type'] = 3;
                (new \Fuel\Service\FossOrderService())->packData( $wsData );
                throw new RuntimeException($e->getMessage(), $e->getCode());
            }
        }
        try {
            if ($cardInfo->oil_com == OilCom::GAS_FIRST_CHARGE || $cardInfo->oil_com == OilCom::FORTUNE_CARD) {
                //充值卡相关逻辑//
                Log::error($params['stream_no'].'--进入充值卡逻辑', [], 'gasDeductMoney');
                if ($cardInfo->deduction_account_no) {
                    //卡账户信息
                    $cardAccountInfo = OilCardAccount::where('cardSubAccountID', $cardInfo->deduction_account_no)->first();
                    if (!$cardAccountInfo) {
                        Log::error($params['stream_no'].'error:卡授信账号不存在', [], 'gasDeductMoney');
                        throw new RuntimeException("子账户授信账号不存在", 2);
                    }

                    if ($cardAccountInfo->subAccountType == 'CREDIT') {
                        //充值卡授信帐号逻辑
                        $this->cardCreditAction($params, $cardAccountInfo, $cardInfo, $orgInfo, $zbankOrder);
                    } else {
                        //充值卡现金帐号逻辑
                        $this->cardCashAction($params, $cardInfo, $orgInfo, $zbankOrder);
                    }
                } else {
                    //充值卡现金帐号逻辑
                    Log::error($params['stream_no'].'--无默认扣款帐号', [], 'gasDeductMoney');
                    $cardAccountInfo = OilCardAccount::getByViceId($cardInfo->id, 'CASH');
                    if ($cardAccountInfo) {
                        OilCardVice::edit([
                            'id' => $cardInfo->id,
                            'deduction_account_no' => $cardAccountInfo->cardSubAccountID,
                            'deduction_account_name' => ConsumeType::FIRST_CHARGE_ACCOUNT_NAME,
                        ]);
                        $cardInfo->deduction_account_no = $cardAccountInfo->cardSubAccountID;
                        $cardInfo->deduction_account_name = ConsumeType::FIRST_CHARGE_ACCOUNT_NAME;
                    }
                    $this->cardCashAction($params, $cardInfo, $orgInfo, $zbankOrder);
                }
            } elseif ($cardInfo->oil_com == OilCom::GAS_FIRST_TALLY) {
                Log::error($params['stream_no'].'--进入共享卡逻辑', [], 'gasDeductMoney');
                //共享卡相关逻辑//
                if (substr($cardInfo->deduction_account_no, 0, 3) == '208') {
                    //扣除机构授信账户逻辑
                    $creditInfo = OilCreditAccount::getByAccountNoWithProvider($cardInfo->deduction_account_no);
                    if (!$creditInfo) {
                        Log::error($params['stream_no'].'error:foss授信账户不存在', [], 'gasDeductMoney');
                        throw new RuntimeException('授信账户不存在', 2);
                    }

                    if ($creditInfo->status != 10) {
                        Log::error($params['stream_no'].'error:foss授信账户状态异常', [], 'gasDeductMoney');
                        throw new RuntimeException('账户已被禁用，请联系车队管理员', 2);
                    }

                    if ($creditInfo->CreditProvider->status != 10) {
                        Log::error($params['stream_no'].'error:foss授信产品已下线', [], 'gasDeductMoney');
                        throw new RuntimeException('授信产品已下线', 2);
                    }

                    if ($creditInfo->is_own == 1) {
                        //自授信
                        $this->orgCashAction($orgInfo, $params, $creditInfo, $cardInfo, $zbankOrder);
                    } else {
                        $this->orgCreditAction($params, $cardInfo->deduction_account_no, $cardInfo, $orgInfo, $zbankOrder);
                    }

                } else {
                    //扣除机构现金账户逻辑
                    $this->orgCashAction($orgInfo, $params, null, $cardInfo, $zbankOrder);
                }
            }
            //更新卡最后消费时间
            OilCardVice::edit(['id'=>$cardInfo->id,"trade_time"=>\helper::nowTime()]);
        }catch (Exception $e) {
            Log::error($params['stream_no'].'msg:', [$e->getMessage(), $e->getCode()], 'gasDeductMoney');
            $wsData['msg_type'] = 3;
            $wsData['message'] = $e->getMessage();
            $wsData['code'] = $e->getCode();
            (new \Fuel\Service\FossOrderService())->packData( $wsData );
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }



//        if($params['order_type'] == 10){
//            //加油时,给用户发送模板消息
//            $msg['station_name'] = $params['station_name']; //需要gas新传
//            $msg['oil_num']  = $params['oil_num'] . "升";
//            $msg['oil_name']  = $params['oil_name']; //需要gas新传
//            $msg['vice_no']      = $params['card_no'];
//            $msg['remark']       = $params['remark'] ? $params['remark'] : '';
//            WeChatTemplateMsg::addTrade($msg);
//        }

        Log::error($params['stream_no'].'消费扣款finish and success', [], 'gasDeductMoney');

        if($cardInfo->oil_com == OilCom::GAS_FIRST_TALLY){
            $accountInfo = ['account_name'=>$cardInfo->deduction_account_name ? $cardInfo->deduction_account_name : ConsumeType::FIRST_TALLY_ACCOUNT_NAME];
        }else{
            $accountInfo = ['account_name'=>$cardInfo->deduction_account_name ? $cardInfo->deduction_account_name : ConsumeType::FIRST_CHARGE_ACCOUNT_NAME];
        }

        Response::json(['accountInfo'=>$accountInfo], 0, '成功');

    }

    /*
     * 同步账单单号
     */
    public function cronBillNo()
    {
        //$params = \helper::filterParams();957222

        //获取glp_bill_no为空的分配信息
        $glp_order_nos = OilAccountAssign::whereNotNull('glp_order_no')->whereNull('glp_bill_no')->limit(1000)
            //->where('createtime','>','2018-12-20')
            ->pluck('glp_order_no');

        //根据order获取账单数据
        $billData = OilCreditBill::whereIn('order_no', $glp_order_nos)->pluck('bill_no', 'order_no');

        $updateArr = $order_nos = [];
        if ($billData) {
            foreach ($billData as $order_no => $bill_no) {
                if (substr($bill_no, 0, 3) === '112') {
                    $order_nos[] = $order_no;
                    //拼接修改sql
                    $updateArr[] = ['glp_bill_no' => $bill_no, 'where' => " glp_order_no = '" . $order_no . "' "];
                }
            }

            if ($updateArr) {
                OilCardViceTrades::batchEditByPdo('oil_account_assign', $updateArr);

                $assign_ids = OilAccountAssign::whereIn('glp_order_no', $order_nos)->pluck('id')->toArray();

                AccountAssignToGos::sendBatchUpdateTask($assign_ids);
            }

        }

        echo json_encode($order_nos);
        echo "finish-edit:" . count($updateArr);
    }

    public function testSend()
    {
        $item['vice_no']       = '****************';
        $item['card_remain']   = 2200;
        $item['assign_amount'] = 200;
        $item['remark_work']   = '抵消10%运费';
        WeChatTemplateMsg::assign($item);

        echo 'ok';
    }

    /**
     * @title 获预支付取服务费
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getServiceMoney()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['trade_num', 'vice_no'], $params);
        Log::error('%params-' . var_export($params, TRUE), [], 'getServiceMoney');

        $service_money = 0.00;

        $card_info = OilCardVice::where('vice_no', $params['vice_no'])->whereIn('oil_com', [20, 21])->first();

        if ($card_info) {
            if ($card_info->deduction_account_no) {
                if ($card_info->oil_com == OilCom::GAS_FIRST_CHARGE) {
                    $cardAccount = OilCardAccount::where('cardSubAccountID', $card_info->deduction_account_no)
                        ->where('subAccountType', 'CREDIT')->first();
                    if ($cardAccount) {
                        $creditInfo = OilCreditAccount::getByAccountNoWithProvider($cardAccount->common_account_no);

                        if ($creditInfo) {
                            $service_money = $creditInfo->CreditProvider->service_fee;
                            //$service_money_temp = $params['trade_num'] * $creditInfo->CreditProvider->service_fee;
                            //$service_money = floor($service_money_temp*100)/100; //服务费;
                        }
                    }
                } else {
                    //共享卡
                    if (substr($card_info->deduction_account_no, 0, 3) == '208') {
                        $accountInfo = OilCreditAccount::getByAccountNoWithProvider($card_info->deduction_account_no);
                        //$service_fee = $accountInfo->CreditProvider->service_fee * $params['trade_num'];
                        //$service_money = floor($service_fee*100)/100; //服务费
                        $service_money = $accountInfo->CreditProvider->service_fee;

                    }
                }
            }
        }

        $data = ['service_money' => $service_money];

        Log::error('%params-' . var_export($data, TRUE), [], 'getServiceMoney');

        Response::json($data, 0, '成功');
    }

    /**
     * @title 获取机构授信账户（消费方式）
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getOrgCredit()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['orgcode'], $params);

        $orgInfo = OilOrg::getByOrgcode($params['orgcode']);

        $creditProviderIds = OilCreditProvider::where('bill_way', 10)->pluck("id")->toArray();

        $creditList = OilCreditAccount::whereIn('credit_provider_id', $creditProviderIds)->whereNotNull('subAccountID')->where('org_id', $orgInfo->id)->get();

        $data = [];
        if ($creditList) {
            foreach ($creditList as $value) {
                try {
                    $data[] = (new AccountService())->getAccountInfoByProductCode(['subAccountID' => $value->subAccountID, 'one' => 1], 'OIL_G7_45');
                } catch (Exception $e) {

                }
            }
        }

        Response::json($data, 0, '成功');
    }

    /**
     * @title 获取机构授信账户（消费方式）
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getCardCredit()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['vice_no'], $params);

        $viceInfo    = OilCardVice::where('vice_no', $params['vice_no'])->first();
        $cardAccount = OilCardAccount::where('vice_id', $viceInfo->id)->where('subAccountType', 'CREDIT')->get();

        $data = [];
        if ($cardAccount) {
            foreach ($cardAccount as $value) {
                try {
                    $data[] = (new AccountService())->getAccountInfoByProductCode(['subAccountID' => $value->cardSubAccountID, 'one' => 1], 'OIL_G7_45');
                } catch (Exception $e) {

                }
            }
        }

        Response::json($data, 0, '成功');
    }

    /*
     * 获取机构现金账户（消费方式）
     */
    public function getCashCredit()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['vice_no'], $params);

        $viceInfo    = OilCardVice::where('vice_no', $params['vice_no'])->first();
        $cardAccount = OilCardAccount::where('vice_id', $viceInfo->id)->where('subAccountType', 'CREDIT')->get();

        $data = [];
        if ($cardAccount) {
            foreach ($cardAccount as $value) {
                try {
                    $data[] = (new AccountService())->getAccountInfoByProductCode(['subAccountID' => $value->cardSubAccountID, 'one' => 1], 'OIL_G7_45');
                } catch (Exception $e) {

                }
            }
        }

        Response::json($data, 0, '成功');
    }

    /*
     * 根据extId查授信消费结果
     */
    public function getCardConsume()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['extID'], $params);
        $data = (new AccountService())->getCardConsume(['extID' => $params['extID']]);

        Response::json($data, 0, '成功');

    }

    /*
     * 根据extid来撤销
     */
    public function revokeCardConsume()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['extID'], $params);

        $info = (new AccountService())->getCardConsume(['extID' => $params['extID']]);
        var_dump([
            'extID'       => $info->extID,
            'billID'      => $info->billID,
            'companyCode' => 'G7_DALIAN',
            'totalAmount' => $info->amount,
        ]);
        $res = (new AccountService())->revokeCardConsume([
            'extID'       => $info->extID,
            'billID'      => $info->billID,
            'companyCode' => 'G7_DALIAN',
            'totalAmount' => $info->amount,
        ]);

        var_dump($res);
        exit;
    }

    /*
     * 每分钟探测付款状态是否成功
     */
    public function cronCheckPayStatus()
    {
        $result = (new \Fuel\Service\Pay())->checkPayStatus();
        $this->assign('data', $result);
        $this->display();
    }

    // test resset

    public function getDetailList()
    {
        $params = \helper::filterParams();

        $result = OilAccountAssignDetails::getDetailList($params);

        $this->assign('data', $result);
        $this->display();
    }

    public function mockConsume()
    {
        $cardInfo = OilCardVice::where('vice_no', '****************')->first();

        $orgInfo = OilOrg::where('id', $cardInfo->org_id)->first();

        //卡账户信息
        $cardAccountInfo = OilCardAccount::where('cardSubAccountID', $cardInfo->deduction_account_no)->first();

        $tradeInfo = OilCardViceTradesZBank::where('extID', 'fb2a751c-0052-11ea-bb3e-869aaa269d46')->first();

        $cardCreditaccountInfo = (new AccountService())->getZBankAccountInfo(['subAccountID' => $cardAccountInfo->cardSubAccountID, "one" => 1]);

        if ($cardCreditaccountInfo->status != 'NORMAL') {
            throw new RuntimeException("授信账户异常", 2);
        }

        //机构授信账户校验
        $creditinfos = OilCreditAccount::checkCreditAccount($cardAccountInfo->common_account_no);
        var_dump($tradeInfo);
        exit;
        $result = OilCardViceTrades::trades2G7Pay($tradeInfo, $creditinfos, $cardCreditaccountInfo, $orgInfo);

        var_dump($result);
        exit;
    }

    //处理分配申请单实际到账金额
    public function setActualAssignMoney()
    {
        $params = \helper::filterParams();
        $start_time = isset($params['start_time']) && $params['start_time'] ? $params['start_time'] : '2020-01-01';
        $limit = isset($params['limit']) && $params['limit'] ? $params['limit'] : '100';
        $assignList = OilAccountAssign::assignListNew($limit,$start_time);
        Log::error("data:".var_export($assignList,true),[],"setActualAssignMoney");
        $batchSql = $assignIds = [];
        if(count($assignList) > 0){
            $list = $assignList->toArray();
            foreach ($list as $_item){
                $assignIds[] = $_item['id'];
                $batchSql[] = "UPDATE oil_account_assign SET actual_money = money_total where id = ".$_item['id'];
                if(count($_item['details']) > 0) {
                    foreach ($_item['details'] as $_subItem) {
                        $batchSql[] = "UPDATE oil_account_assign_details SET actual_money = assign_money where id = ".$_subItem['id'];
                    }
                }
            }
            if(count($batchSql) > 0){
                $bachSqlStr = implode(";",$batchSql);
                Capsule::connection()->getPdo()->exec($bachSqlStr);
            }
            sleep(2);
            //推送Gos
            if( count($assignIds) > 0 ){
                AccountAssignToGos::sendBatchUpdateTask( $assignIds ,'sync');
            }
            $this->setActualAssignMoney(['limit'=>$limit,'start_time'=>$start_time]);
        }else{
            die("success");
        }
    }

    /*
     * 根据detail_id获取任务列表
     */
    public function getTaskListByDetailId()
    {
        $params = \helper::filterParams();

        \helper::argumentCheck(['detail_id'],$params);
        $data = OilAccountAssignTaskDetail::getTaskListByDetailId($params['detail_id']);

        Response::json($data, 0, '成功');
    }

    /**
     * 交易扣费
     */
    public function tradePay()
    {
        $params = \helper::filterParams();
        Log::error('addTrades-%params- ', [$params], 'tradePay');
        \helper::argumentCheck(['orgcode', 'card_no', 'money', 'stream_no'], $params);

        $data = (new CardTradeService())->tradePay($params);

        Response::json($data, 0, '成功');
    }

    /**
     * // 测试生成流水记录
    $paramsExt = [
    'sale_type' => '现金消费',
    'unit' => 1,
    'oil_balance' => 0,
    'imgurl' => null,
    'oil_name' => '0#柴油国V',
    'station_name' => '北京测试站点-new3',
    'station_code' => '3BDU2J',
    'imgurl' => null,
    'drivertel' => '***********',
    'drivername' => '刘培俊',
    'provice_code' => '110000',
    'provice_name' => '北京',
    'city_code' => '110108',
    'city_name' => '海淀区',
    'oil_time' => date('Y-m-d H:i:s', strtotime('-1 Minute')),
    ];
     * 交易扣费（新版pda）
     */
    public function tradePayForGms()
    {
        $start = microtime(true);
        $params = \helper::filterParams();
        Log::error('addTrades-%params- start:'.$start, [$params], 'tradePay');
        \helper::argumentCheck(['orgcode', 'card_no', 'money','stream_no','sale_type','unit','provice_code','provice_name','city_code','city_name','oil_time'], $params);

        //新增预授权字段 20:预授权 10:普通
        $params['order_type'] = isset($params['order_type']) && $params['order_type'] == CardTradeConf::RESERVE_ORDER ? CardTradeConf::RESERVE_ORDER : CardTradeConf::TRADE_ORDER;

        //txb 增加交易来源：ENINET-1902,处理出示付款码情况下的交易来源
        if( !isset($params['trade_from']) || empty($params['trade_from']) ){
            $params['trade_from'] = (new CardTradeService())->getTradeFrom($params['card_no']);
        }

        //G7WALLET-6403
        $pay_ext = [];
        if( substr($params['orgcode'],0,6) == \Fuel\Defines\OrgDefine::getOrgCode() ){
            if( !isset($params['pay_ext_info']) || empty($params['pay_ext_info']) ){
               throw new \RuntimeException('该客户未拆单，支付失败！', 500);
            }

            /*$pay_ext = json_decode($params['pay_ext_info'],true);
            if(count($pay_ext) <= 0){
               throw new \RuntimeException('该客户未拆单，支付失败！', 501);
            }*/
            $pay_ext = $params['pay_ext_info'];
            $sub_fee = array_sum(array_column($pay_ext,'money'));
            Log::error('pay_ext_info:money'.$sub_fee, [$pay_ext], 'tradePay');
            if( bccomp($params['money'],$sub_fee,2) != 0 ){
                throw new \RuntimeException('拆单金额不一致，支付失败！', 502);
            }
            unset($params['pay_ext_info']);
        }

        $queue = 'addCardViceTradesJob';

        $checkZbankOrder = OilCardViceTradesZBank::getByStreamNo($params['stream_no']);

        if (in_array(substr($params['orgcode'], 0, 6), OrgConf::getGrayLevelTestOrg('code'))) {
            $queue = 'addCardViceTradesJob_pre_online';
        }

        $org_name = "";
        // 支持幂等
        if ($checkZbankOrder && $checkZbankOrder['is_pay'] == CardTradeConf::PAY_SUCCESS) {
            $data = (new CardTradeService())->formatZbankInfoForGms($checkZbankOrder);
            $org_name = $checkZbankOrder->org_name;
            // 支付流程
        } else {
            $params['check_pay_limit'] = 1;
            $data = (new CardTradeService())->tradePay($params,true,'gms');
            $org_name = $data['alarm_org_name'];
            unset($data['alarm_org_name']);
            Log::error('tradePay - useTime:'.round(microtime(true) - $start,3), [$data], 'tradePay');

            // 扣费完成生成交易流水  异步处理
            if ($data['pay_status'] == CardTradeConf::PAY_SUCCESS) {
                $addTradesParams = [
                    'params'    => $params,
                    'zbankInfo' => $data,
                    
                    'sub_trades' => $pay_ext
                ];

                //肇庆分配
                if(isset($params['station_code']) && $params['station_code']){
                    Log::error('肇庆模式加油', [$params['station_code']], 'TradeToAssignJob');
                    $vice_info = Station::getAssignCardByStationCode(['station_code'=>$params['station_code'],'gms_order_id'=>$params['stream_no']]);
                    if($vice_info){
                        Log::error('肇庆-vice_info', $vice_info, 'TradeToAssignJob');
                        //异步下发分配任务
                        (new TradeToAssignJob([
                            'supplier_id' => $vice_info['supplier_id'],
                            'supplier_name' => $vice_info['supplier_name'],
                            'station_code' => $params['station_code'],
                            'station_name' => $params['trade_place'],
                            'vice_no' => $vice_info['vice_no'],
                            'api_id' => $params['stream_no'],
                            'trade_money'=>$params['money'],
                            'trade_price'=>$params['price'],
                            'trade_num'=>$params['oil_num'],
                            'xpcode_pay_price' => $params['xpcode_pay_price'],
                            'xpcode_pay_money' => $params['xpcode_pay_money'],
                            'oil_name' => $params['oil_name'],
                            'provice_code' => $params['provice_code'],
                            'trade_place' => $params['trade_place'],
                            'is_cancel' => 1, //1正消费，2负消费
                            'area_code' => $vice_info['area_code'],
                            'ext' => ['real_oil_num' => $params['real_oil_num']],
                        ]))->setTaskName(TradeToAssignJob::TITLE)
                        ->onQueue(TradeToAssignJob::QUEUE)
                        ->setTries(2)
                        ->dispatch();
                    }
                }

                $task = (new \Jobs\AddCardViceTradesJob($addTradesParams))
                    ->setTaskName('异步写交易流水')
                    ->onQueue($queue)
                    ->setTries(3)
                    ->dispatch();
                Log::error(__METHOD__, [$task], "asyncWriteCardViceTrades");
            }

            //推送到gos
            CardViceToGos::batchUpdateToGos([$params['card_no']], "async"); //是否变为异步
        }

        $use_time = round(microtime(true) - $start,3);
        Log::error('payResult-%data- useTime:'.$use_time, [$data], 'tradePay');

        try {
            if ( $use_time > 5 ) {
                $content[] = "* 订单号：" . strval($params['stream_no']);
                $content[] = "* 扣费耗时：" . $use_time."秒";
                $content[] = "* 订单金额：" . $params['money']."元";
                $content[] = "* 扣款卡号：" . $params['card_no'];
                $content[] = "* 客户名称：" . $org_name;
                $content[] = "* 客户编码：" . $params['orgcode'];
                (new DingTalkAlarm())->alarmToGroup("电子卡支付耗时预警", implode("\n\n", $content), [], true, false);
            }
        }catch (\Exception $e){
            Log::error('alarm:', [$e->getMessage()], 'tradePay');
        }

        Response::json($data, 0, '成功');
    }

    /**
     * 检查交易扣费结果
     */
    public function queryPayResult()
    {
        $params = \helper::filterParams();
        Log::error('addTrades-%params- ', [$params], 'queryPayResult');
        \helper::argumentCheck(['stream_no'], $params);

        $data = (new CardTradeService())->queryPayResult($params);

        Response::json($data, 0, '成功');
    }

    /**
     * 批量圈回导入
     */
    public function batchLoopBackImport()
    {
        $params = \helper::filterParams();
        //上传文件
        $upload = $this->file_upload($_FILES['userfile']);
        if (is_numeric($upload) && $upload < 0) {
            if ($upload == -9) {
                $msg = "未选择文件";
            } elseif ($upload == -8) {
                $msg = "文件格式不正确";
            } else {
                $msg = "上传导入失败";
            }
            throw new RuntimeException($msg, $upload);
        }

        $fieldMap    = [
            'vice_no'         => '卡号',
            'truck_no_custom' => '自录入车牌号',
            'assign_money'    => '分配金额',
        ];
        $excelParams = [
            'filePath'  => $upload,
            'fieldsMap' => array_flip($fieldMap),
        ];

        $result = ExcelReader::read($excelParams, function ($rowNum, $fieldName, $cellValue) use ($params) {
            return $this->preLoopBackImportCellValue($rowNum, $fieldName, $cellValue, $params);
        }, function ($rowData) {
            $this->checkLoopBackRowData($rowData);
        });
        $ret = array_values($result[0]);

        // 去除空行
        $resData = [];
        foreach ($ret as $val) {
            if (empty($val['vice_no']) && empty($val['truck_no_custom']) && empty($val['assign_money'])) {
                continue;
            }
            array_push($resData, $val);
        }
        if (!count($resData)) {
            throw new \RuntimeException('未解析到数据，请检查Excel文件内容！',2);
        }
        if (count($resData) > 500) {
            throw new \RuntimeException('模板导入不能大于500条！',2);
        }

        $line = [];
        $keyLine = 1;
        foreach ($resData as $key=>&$item) {
            $item = (array)$item;
            //行重复
            $keyLine = $keyLine+1;
            $line[$item['vice_no']][] = $keyLine;
        }

        //判读重复行
        if($line){
            $errorMsg = [];
            foreach ($line as $value){
                if (count($value) > 1) {
                    $errorMsg[] = implode(',',$value).'行重复';
                }
            }
            if ($errorMsg) {
                throw new \RuntimeException('第'.implode('!',$errorMsg).'，请检查！',2);
            }
        }

        $viceNos = array_keys($line);
        $viceList = OilCardVice::getListByViceNo(['vice_no'=>$viceNos]);
        if (empty($viceList)) {
            throw new \RuntimeException('无有效副卡信息，请检查后重试',2);
        }

        $index = 1;
        $viceData = array_column($viceList, null, 'vice_no');
        foreach ($resData as $v) {
            if ($v['vice_no']) {
                $info = isset($viceData[$v['vice_no']]) ? $viceData[$v['vice_no']] : [];
                if (empty($info)) {
                    throw new \RuntimeException('副卡号'.$v['vice_no'].'信息不存在，请检查后重试！',2);
                }
                if (!in_array($info['vice_oil_com'], OilCom::getImportOilcom())) {
                    throw new \RuntimeException('副卡【'.$v['vice_no'].'】不支持主站圈回！',2);
                }
                $oilCom = OilCom::getById($info['oil_com']);
                $data[] = [
                    'index'           => $index,
                    'org_name'        => $info['org_name'],
                    'truck_no'        => $info['truck_no'],
                    'fanli_region'    => $info['province'],
                    'main_no'         => $info['main_no'],
                    'jifen'           => $info['jifen'],
                    'vice_id'         => $info['id'],
                    'vice_status'     => $info['vice_status'],
                    'oil_com'         => $oilCom['name'],
                    'vice_no'         => $v['vice_no'],
                    'truck_no_custom' => $v['truck_no_custom'],
                    'assign_money'    => floatval($v['assign_money']),
                    'status'          => in_array($info['oil_com'], [OilCom::ZSH,OilCom::ZSY]) ? '主站待分配' : '待审核',
                ];
                $index ++;
            }
        }

        Response::json($data);
    }

    /**
     * 校验导入的行数据
     *
     * @param $rowData
     */
    private function checkLoopBackRowData($rowData)
    {
        if ($rowData['vice_no'] || $rowData['assign_money']) {
            if (!$rowData['vice_no'])
                throw new RuntimeException('导入失败: 卡号必填');
            if (!$rowData['assign_money'])
                throw new RuntimeException('导入失败: 卡号【' . $rowData['vice_no'] . '】的分配金额不能为空');
        }

    }

    /**
     * 批量圈回导入单元格数据预处理
     *
     * @param $rowNum
     * @param $fieldName
     * @param $cellValue
     * @param $params
     * @return array|string
     */
    private function preLoopBackImportCellValue($rowNum, $fieldName, $cellValue, $params)
    {
        $data = $cellValue;
        $msg = '';
        if ($rowNum > 1) {
            if ($cellValue) {
                switch ($fieldName) {
                    case 'vice_no':
                        if (!preg_match('/^(\d{16}|\d{19}|\d{20})$/', $cellValue)) {
                            $msg = '导入失败: 卡号【' . $cellValue . '】格式不正确';
                        }
                        break;
                    case 'assign_money':
                        if (!preg_match('/^(-{1})?[0-9]+(.[0-9]{1,2})?$/', $cellValue)) {
                            $msg = '导入失败: 分配金额【' . $cellValue . '】格式不正确';
                        }
                        break;
                    default :
                        $data = $cellValue;
                }
            }
        }

        if ($msg) {
            throw new RuntimeException($msg, 2);
        }

        return $data;
    }

    /*
      *  主卡分配动账
      *  <AUTHOR> yanglei <<EMAIL>>
      *  @since  :2022年7月12日
      *
     */
    private function setAssignMainCardAccount($assignId)
    {
        try{
            $arams = [
                'assignId'    => $assignId,
                'creator'     => $this->app->myAdmin->true_name
            ];
            (new \Jobs\AddMainCardAccountAssignJob($arams))
            ->setTaskName('异步分配动账')
            ->onQueue('addSupplierAssignChargeJob')
            ->setTries(3)
            ->dispatch();
        }catch(\Exception $e){
            Log::error('assign-%data- ', [$e->getMessage()], 'AddMainCardAccountAssignJobErr');
        }
    }

    /**
     * @return void
     */
    public function determineOrderIsDeductionFailed()
    {
        $params = \helper::filterParams();
        $validator = new Illuminate\Validation\Validator(new Translator('en'), $params, [
            'order_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            Response::json([], 412001, $validator->errors()->first());
        }
        $data = (new CardTradeService())->queryPayResult([
            "stream_no" => $params['order_id']
        ]);
        if (empty($data)) {
            Response::json(true);
        }
        if (!in_array($data['pay_status'], [1, 2])) {
            Response::json(true);
        }
        Response::json(false);
    }

    /**
     * 中物流模式的预约转销售，退款及扣费
     */
    public function cancelTradePay()
    {
        $start = microtime(true);
        $params = \helper::filterParams();
        Log::error('params-' . var_export($params, TRUE), [], 'cancelPay');
        \helper::argumentCheck(['reservation_fuel_order_id','order_id'], $params);
        Capsule::connection()->beginTransaction();
        try {
            $_condition['remark'] = isset($params['remark']) ? $params['remark'] : "";
            $_condition['original_order_id'] = $params['reservation_fuel_order_id'];
            $_condition['document_type'] = CardTradeConf::DOCUMENT_TYPE_RESERVE_CANCEL;

            $refundRes = (new CardTradeService())->cancelTradeOrder([
                "stream_no" => $params['reservation_fuel_order_id'],
                "original_order_id" => $params['reservation_fuel_order_id'],
                "document_type" => CardTradeConf::DOCUMENT_TYPE_RESERVE_CANCEL,
            ]);
            $code = $refundRes['code'];

            $params['id'] = $params['order_id'];
            $params['stream_no'] = $params['order_id'];
            $params['client_order_id'] = $params['order_id'];
            $params['pay_no'] = $params['order_id'];
            $params['original_order_id'] = $params['reservation_fuel_order_id'];
            $params['document_type'] = CardTradeConf::DOCUMENT_TYPE_CONSUMPTION_NORMAL;
            $params['order_type'] = CardTradeConf::TRADE_ORDER;
            $params['trade_from'] = $refundRes['order']->data_from;
            unset($params['order_id']);
            unset($params['reservation_fuel_order_id']);

            Log::error('cancel-end-' . var_export($params, TRUE), ["end"=>round(microtime(true) - $start,3)], 'cancelPay');

            $isPay = false;
            $checkZbankOrder = OilCardViceTradesZBank::getByStreamNo($params['stream_no']);
            // 支持幂等
            if ($checkZbankOrder && $checkZbankOrder->is_pay == CardTradeConf::PAY_SUCCESS) {
                if($code == 0) {
                    $data = (new CardTradeService())->formatZbankInfoForGms($checkZbankOrder->toArray());
                }else{
                    Log::error('exception' . var_export($params, TRUE), [$code], 'cancelPay');
                }
            } else {
                $isPay = true;
                $params['check_pay_limit'] = 1;
                $data = (new CardTradeService())->tradePay($params, true, 'gms');
            }
            Capsule::connection()->commit();

            //todo 需要检测 oil_card_vice_trades_zbank 与 oil_card_vice_trades 一致性
            Log::error('tradepay-end-' . var_export($params, TRUE), ["end" => round(microtime(true) - $start,3)], 'cancelPay');
            if($isPay) {
                //推送到gos
                CardViceToGos::batchUpdateToGos([$params['card_no']], "async");

                //LPOP
                // 扣费完成生成交易流水  异步处理
                if ($data['pay_status'] == CardTradeConf::PAY_SUCCESS) {
                    $_condition['zbank_id'] = $refundRes['order']->id;
                    $_condition['is_cash'] = $refundRes['is_cash'];
                    $_condition['lastBalance'] = $refundRes['lastBalance'];

                    $cancelTask = (new \Jobs\CancelCardTradesJob($_condition))
                        ->setTaskName("电子卡交易撤销")
                        ->onQueue("cancelElectronTrades") //cancelElectronTrades
                        ->setTries(3)
                        ->dispatch();
                    Log::error('cancel-taskInfo', [$cancelTask], 'cancelPay');

                    $addTradesParams = [
                        'params' => $params,
                        'zbankInfo' => $data
                    ];

                    $task = (new \Jobs\AddCardViceTradesJob($addTradesParams))
                        ->setTaskName('异步写交易流水')
                        ->onQueue("cancelElectronTrades") //addCardViceTradesJob
                        ->setTries(3)
                        ->dispatch();
                    Log::error("tradePay-" . __METHOD__, [$task], "cancelPay");

                }
                Log::error('async-end-' . var_export($params, TRUE), ["end" => round(microtime(true) - $start,3)], 'cancelPay');
            }
        }catch (\Exception $e){
            Capsule::connection()->rollBack();
            Log::error('exception-' .$e->getMessage(), [$e->getCode()], 'cancelPay');
            throw new \RuntimeException($e->getMessage(),$e->getCode());
        }
        Log::error('payResult-%data- useTime:'.round(microtime(true) - $start,3), [$data], 'cancelPay');
        Response::json($data, 0, '成功');
    }

    public function checkSysPayLimit()
    {
        $params = \helper::filterParams();
        Log::error('checkSysPayLimit-=-params:' . var_export($params, TRUE), [], 'tradeDayLimit_');
        \helper::argumentCheck(['orgcode','oil_time','card_no','money'], $params);
        $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('orgcode' . $params['orgcode'] . '不存在', 2);
        }
        if ($orgInfo->status != OrgStatus::ORG_STATUS_NORMAL) {
            throw new \RuntimeException('您的当前机构正在维护请稍后再试', 2);
        }
        (new \Fuel\Service\CommonService())->checkSysPayLimit($params,$orgInfo);
        Response::json(["pay_limit"=>1],0,"成功");
    }

    public function getAssignListByOrderId()
    {
        $params = \helper::filterParams();
        $validator = new Illuminate\Validation\Validator(new Translator('en'), $params, [
            'order_id' => 'required|alpha_num',
        ], [
            'order_id.required' => '订单号不能为空',
            'order_id.numeric' => '订单号格式不正确',
        ]);
        // 如果验证失败，返回错误信息
        if ($validator->fails()) {
            Response::json([], 412001, $validator->errors()->first());
        }
        $params['gms_order_id'] = $params['order_id'];
        $data = OilAccountAssign::Filter($params)
                                ->select([
                                    'oil_account_assign_details.vice_no',
                                    'oil_account_assign.no',
                                    'oil_account_assign_details.assign_money',
                                    'oil_account_assign.other_creator',
                                    'oil_account_assign.status',
                                    'oil_account_assign.createtime',
                                    'oil_account_assign_details.callback_time_end',
                                    'oil_account_assign.gms_order_id',
                                ])
                                ->leftJoin(
                                    'oil_account_assign_details',
                                    'oil_account_assign.id',
                                    '=',
                                    'oil_account_assign_details.assign_id'
                                )
                                ->groupBy(['oil_account_assign.id', 'oil_account_assign.apply_time'])
                                ->orderBy('oil_account_assign.apply_time', 'desc')
                                ->get();
        foreach ($data as $v) {
            $v->assign_success = true;
            if ($v->status != 1) {
                $v->callback_time_end = '';
                $v->assign_success = false;
            }
            $v->status_name = AccountAssignStatus::$AccountAssignStatus[$v->status];
        }
        Response::json($data);
    }

    /**
     * gms异常修改
     * 退款+扣费
     */
    public function deleteAndTradePay()
    {
        $start = microtime(true);
        $params = \helper::filterParams();

        Log::error('params-' . var_export($params, TRUE), [], 'deleteAndTradePay');
        \helper::argumentCheck(['cancel_order_id','order_id','orgcode', 'card_no', 'money','stream_no','sale_type','unit',
            'provice_code','provice_name','city_code','city_name','oil_time'], $params);

        Capsule::connection()->beginTransaction();
        try {

            $refundRes = (new CardTradeService())->cancelPayOrder([
                "stream_no" => $params['cancel_order_id'],
                "document_type" => CardTradeConf::DOCUMENT_TYPE_REFUND_MODIFY,
                "original_order_id" => $params['cancel_order_id'],
                "remark" => isset($params['remark']) && $params['remark'] ? $params['remark'] : ''
            ]);
            $code = $refundRes['code'];
            $tradeInfo = $refundRes['origin_trade'];
            $tradeRes = $refundRes['ids'];
            $order_type = $refundRes['order_type'];
            $cardInfo = $refundRes['cardInfo'];
            $is_cash = $refundRes['is_cash'];

            //$params['id'] = $params['order_id'];
            //$params['stream_no'] = $params['order_id'];
            //$params['client_order_id'] = $params['order_id'];
            //$params['pay_no'] = $params['order_id'];
            $params['original_order_id'] = $params['cancel_order_id'];
            $params['document_type'] = CardTradeConf::DOCUMENT_TYPE_CONSUMPTION_MODIFY;
            $params['order_type'] = CardTradeConf::TRADE_ORDER;
            //$params['trade_from'] = $refundRes['order']->data_from;
            unset($params['order_id']);
            unset($params['cancel_order_id']);

            Log::error('cancel-end-' . var_export($params, TRUE), ["end"=>round(microtime(true) - $start,3)], 'deleteAndTradePay');

            $isPay = false;
            $checkZbankOrder = OilCardViceTradesZBank::getByStreamNo($params['stream_no']);
            // 支持幂等
            if ($checkZbankOrder && $checkZbankOrder->is_pay == CardTradeConf::PAY_SUCCESS) {
                if($code == 0) {
                    $data = (new CardTradeService())->formatZbankInfoForGms($checkZbankOrder->toArray());
                }else{
                    Log::error('exception' . var_export($params, TRUE), [$code], 'deleteAndTradePay');
                }
            } else {
                $isPay = true;
                $params['check_pay_limit'] = 1;
                $data = (new CardTradeService())->tradePay($params, true, 'gms');
            }
            Capsule::connection()->commit();

            Log::error('tradepay-end-' . var_export($params, TRUE), ["end" => round(microtime(true) - $start,3)], 'deleteAndTradePay');
            if($isPay) {

                //更新原订单
                CardViceTradesToGos::sendBatchUpdateTaskByQuene([$tradeInfo->id]);
                CardViceTradesToGos::sendBatchCreateTask($tradeRes);

                if($order_type == CardTradeConf::TRADE_ORDER){
                    //加油撤销时,给用户发送模板消息
                    $item['station_name'] = $tradeInfo->trade_place;
                    $item['trade_money']  = $tradeInfo->trade_money . "元";
                    $item['vice_no']      = $tradeInfo->vice_no;
                    $item['remark']       = $params['remark'] ? $params['remark'] : '';
                    WeChatTemplateMsg::cancelTrade($item);
                }

                //新增司机发送短信
                if ($tradeInfo && $tradeInfo->qz_drivertel && $cardInfo->card_level == 2 && $is_cash) {
                    $consumeTime = date('m月d日H点i分', strtotime($tradeInfo->fetch_time));
                    $revokeTime  = date('m月d日H点i分', time());
                    $short_no    = substr($tradeInfo->vice_no, -6);
                    $station     = $tradeInfo->trade_place;
                    $oil_name    = $tradeInfo->oil_name;
                    $trade_num   = $tradeInfo->trade_num;
                    try{
                        \Fuel\Service\CardVice::sendDriverConsumeTip([
                            'orgcode'    => $cardInfo->Org->orgcode,
                            'driver_tel' => $tradeInfo->qz_drivertel,
                            'content'    => '尊敬的客户您好，您于' . $consumeTime . '使用尾号' . $short_no . '的G7能源账户，在' . $station . ' 加【' . $oil_name . '】' . $trade_num . '升,' . $tradeInfo->trade_money . '元，该笔交易在' . $revokeTime . '已被撤销。如遇疑问详询客服电话************转2。'
                        ]);
                    }catch (\Exception $e){
                        Log::error("sms-Exception：" . $e->getMessage(), [], "deleteAndTradePay");
                    }

                }

                //////////////////////
                //推送到gos
                CardViceToGos::batchUpdateToGos([$params['card_no']], "async");

                // 扣费完成生成交易流水  异步处理
                if ($data['pay_status'] == CardTradeConf::PAY_SUCCESS) {
                    $addTradesParams = [
                        'params' => $params,
                        'zbankInfo' => $data
                    ];

                    $task = (new \Jobs\AddCardViceTradesJob($addTradesParams))
                        ->setTaskName('异步写交易流水')
                        ->onQueue("addCardViceTradesJob") //addCardViceTradesJob
                        ->setTries(3)
                        ->dispatch();
                    Log::error("tradePay-" . __METHOD__, [$task], "deleteAndTradePay");

                }
                Log::error('async-end-' . var_export($params, TRUE), ["end" => round(microtime(true) - $start,3)], 'cancelPay');
            }
        }catch (\Exception $e){
            Capsule::connection()->rollBack();
            Log::error('exception-' .$e->getMessage(), [$e->getCode()], 'deleteAndTradePay');
            throw new \RuntimeException($e->getMessage(),$e->getCode());
        }

        if($newInsert){
            //异步标记返利
            (new MarkTradeFanliJob(['trade'=>$newInsert,'flag'=>2]))
                ->setTaskName("异步标记消费记录使用返利")
                ->onQueue("markTradeUseFanli")
                ->setTries(3)
                ->dispatch();

            $task = (new UpstreamSettleDataWriteJob($newInsert))
                ->setTaskName(UpstreamSettleDataWriteJob::TITLE)
                ->onQueue(UpstreamSettleDataWriteJob::QUEUE)
                ->setTries(2)
                ->dispatch();
            Log::error(__METHOD__ . ' 取消订单 异步写上游结算数据 派发', [$task], 'deleteAndTradePay');
        }
        Log::error('payResult-%data- useTime:'.round(microtime(true) - $start,3), [$data], 'deleteAndTradePay');
        Response::json($data, 0, '成功');
    }

    public function bindOrderId()
    {
        $params = \helper::filterParams();
        Log::error('params-' . var_export($params, TRUE), [], 'bindOrderId');
        \helper::argumentCheck(['gms_order_id','id'], $params);

        $detail = OilAccountAssignDetails::getByAssignId($params['id']);
        if ($detail->count() > 1) {
            throw new \RuntimeException('分配明细大于1条，不允许关联电子卡订单！', 2);
        }
        $uData = [
            'id' => $params['id'],
            'gms_order_id' => $params['gms_order_id'],
        ];
        if(isset($params['remark']) && $params['remark']){
            $uData['remark'] = $params['remark'];
        }

        if(isset($params['remark_work']) && $params['remark_work']){
            $uData['remark_work'] = $params['remark_work'];
        }
        $data = OilAccountAssign::edit($uData);

        Response::json($data, 0, '成功');
    }
}
