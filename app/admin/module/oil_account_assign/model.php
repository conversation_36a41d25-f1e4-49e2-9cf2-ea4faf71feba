<?php

/**
 * 开卡信息管理
 * Class oil_account_assignModel
 */
class oil_account_assignModel extends model
{

    /**
     * 获取列表信息
     *
     * @param $where
     * @param $limit
     * @param $desc
     * @return stdClass
     */
    public function search($where, $limit, $desc)
    {
        $sql = "SELECT %s FROM oil_account_assign oaa
                LEFT JOIN oil_account_money oam ON oaa.org_id = oam.org_id
                LEFT JOIN oil_org oo ON oo.id = oam.org_id
                LEFT JOIN gsp_sys_users gsu ON oaa.creator_id = gsu.id
                LEFT JOIN oil_account_assign_details oaad ON oaa.id = oaad.assign_id
                LEFT JOIN oil_card_vice ocv ON oaad.vice_id = ocv.id ";

        $fields = " oaa.*,oam.money, gsu.true_name creator_name ";
        $where .= " GROUP BY oaa.id ";
        //取记录总数
        $total = parent::findBySql($sql, $where, $desc, NULL, 'count(oaa.id) as count');

        //取记录集
        if ($total[0]->count > 0) {
            $datas = parent::findBySql($sql, $where, $desc, $limit, $fields);
        }

        $status = Fuel\Defines\AccountAssignStatus::getAll();
        foreach ($datas as $v) {
            $v->_status = $status[$v->status];
        }
        $result        = new stdClass();
        $result->total = count($total);
        $result->data  = $datas ? $datas : [];

        return $result;
    }

    /**
     * 删除
     *
     * @param array  $sid
     * @param string $tb
     * @return mixed
     */
//    public function delete($sid = array(), $tb = 'oil_account_assign'){
//        return parent::delete($sid,null,$tb);
//    }

    /**
     * 删除
     *
     * @param array  $sid
     * @param string $tb
     * @return mixed
     */
    public function destroy($sid = [], $tb = 'oil_account_assign')
    {
        return parent::delete($sid, NULL, $tb);
    }

    /**
     * 通过id获取数据
     *
     * @param        $where
     * @param        $field
     * @param string $tb
     * @return ArrayIterator
     */
    public function getInfoById($where, $field, $tb = 'oil_account_assign')
    {
        $sql  = "SELECT $field FROM $tb WHERE $where";
        $data = parent::queryBySql($sql);

        return $data;
    }

    /**
     * 通过id获取数据 lock for update
     *
     * @param        $where
     * @param        $field
     * @param string $tb
     * @return ArrayIterator
     */
    public function getInfoByIdByLock($where, $field, $tb = 'oil_account_assign')
    {
        $sql  = "SELECT $field FROM $tb WHERE $where for update";
        $data = parent::queryBySql($sql);

        return $data;
    }

    public function getById($where, $field, $tb = 'oil_account_assign')
    {
        return parent::findUnique($where, NULL, '*', $tb);
    }

    /**
     * 添加
     *
     * @param $record
     * @param $table
     * @return int
     */
    public function add($record, $table)
    {
        $cId = parent::insert($record, $table);
        if ($cId) {
            return $cId;
        } else {
            return 0;
        }
    }

    /**
     * 编辑
     *
     * @param array  $id    where条件
     * @param array  $data  更新的内容
     * @param string $table 表名
     * @return int
     * <AUTHOR>
     * @since  2015/09/18
     */
//    public function update($id = array(),$data = array(),$table = '')
//    {
//        return parent::update($data,$id,$table);
//    }

    /**
     * @param array  $id
     * @param array  $data
     * @param string $table
     * @return int
     */
    public function edit($id = [], $data = [], $table = '')
    {
        return parent::update($data, $id, $table);
    }

    /**
     * 通过机构和副卡号获取主卡及副卡的相关信息
     *
     * @param $vice_no
     * @param $orgcode
     * @param $org_id
     * @return mixed
     */
    public function getInfoByOrgViceNo($vice_no, $orgcode, $org_id)
    {
        $data = [];
        if ($vice_no && $orgcode) {
            $sql = "SELECT ocv.id, oo.org_name, ocv.truck_no, ocm.main_no, ocm.oil_com, op.province, oaj.jifen,ocv.status as vice_status
                    FROM oil_card_vice ocv
                    LEFT JOIN oil_org oo ON ocv.org_id = oo.id
                    LEFT JOIN oil_card_main ocm ON ocv.card_main_id = ocm.id
                    LEFT JOIN oil_provinces op ON ocm.fanli_region = op.id
                    LEFT JOIN oil_account_jifen oaj ON oaj.main_id = ocm.id AND oaj.org_id = $org_id
                    WHERE ocv.vice_no = '$vice_no' AND oo.orgcode LIKE '$orgcode%' AND ocm.oil_com IN (".implode(',', \Fuel\Defines\OilCom::getOilComForAssign()).")";
            $data = parent::queryBySql($sql);
        }

        return $data[0];
    }

    /**
     * 主站圈回时副卡号获取主卡及副卡的相关信息
     *
     * @param $vice_no
     * @param $orgcode
     * @param $org_id
     * @return mixed
     */
    public function getInfoByViceNo($vice_no,$org_id)
    {
        $data = [];
        if ($vice_no ) {
            $sql = "SELECT ocv.id, oo.org_name, ocv.truck_no, ocm.main_no, ocm.oil_com, op.province, oaj.jifen,ocv.status as vice_status
                    FROM oil_card_vice ocv
                    LEFT JOIN oil_org oo ON ocv.org_id = oo.id
                    LEFT JOIN oil_card_main ocm ON ocv.card_main_id = ocm.id
                    LEFT JOIN oil_provinces op ON ocm.fanli_region = op.id
                    LEFT JOIN oil_account_jifen oaj ON oaj.main_id = ocm.id AND oaj.org_id = $org_id
                    WHERE ocv.vice_no = '$vice_no' AND ocm.oil_com IN (".implode(',', \Fuel\Defines\OilCom::getOilComForAssign()).")";

            $data = parent::queryBySql($sql);
        }

        return $data[0];
    }

    /**
     * 获取机构的账户余额
     *
     * @param $org_id
     * @return mixed|string
     */
    public function getOrgMoney($org_id)
    {
        $data = [];
        if (!empty($org_id)) {
            //Story #52472
            $sql = "SELECT id, money,account_no FROM oil_account_money WHERE org_id = '$org_id'";

            $data = parent::queryBysql($sql);
        }

        //Task #52827
        if ($data) {
            return $data[0];    //Story #52472
        } else {
            return '';
        }
    }

    /**
     * 获取分配详情数据
     *
     * @param        $id
     * @param string $limit
     * @return stdClass
     */
    public function getDetailInfo($id, $limit = '')
    {

        $result = new stdClass();
        if (!empty($id)) {
            $sql = "SELECT %s FROM oil_account_assign oaa
                    LEFT JOIN oil_account_assign_details oaad ON oaa.id = oaad.assign_id
                    LEFT JOIN oil_card_vice ocv ON oaad.vice_id = ocv.id
                    LEFT JOIN oil_card_main ocm ON ocv.card_main_id = ocm.id
                    LEFT JOIN oil_provinces op ON ocm.fanli_region = op.id
                    LEFT JOIN oil_account_jifen oaj ON oaa.org_id = oaj.org_id AND ocm.id = oaj.main_id
                    LEFT JOIN oil_org oo ON oaad.org_id = oo.id";

            $where = " oaa.id = '$id' ";
            //Story #52472

            $fields = " oaad.id,oaad.assign_message,oaad.distributeId,oaad.check_assign,oaad.truck_no_custom,oaad.status, oaad.truck_no, 
            oaad.assign_money,oaad.service_money,oaad.actual_money,oaad.assign_jifen,oaad.remark_work, ocv.vice_no, ocv.id card_vice_id, ocv.card_main_id, ocv.card_owner, 
            ocv.pin, ocm.main_no, ocm.oil_com, op.province, oaj.id jifen_id, oaj.jifen, oo.org_name ,ocv.status as vice_status,
            oaa.no_type,oaa.account_type,oaa.reject_flag,ocm.id as main_no_id,oaa.org_name as pay_name,oaa.no";


            //取记录总数
            $total = parent::findBySql($sql, $where, '', NULL, 'count(oaad.id) as count');

            //取记录集
            if ($total[0]->count > 0) {
                $datas = parent::findBySql($sql, $where, '', $limit, $fields);
            }

            $result->total = $total[0]->count;
            $result->data  = $datas ? $datas : [];
        } else {
            $result->total = 0;
            $result->data  = [];
        }

        return $result;
    }

    /**
     * 账户资金更新
     *
     * @param $org_id
     * @param $assign_money
     * @param $fenpei_id
     * @return bool
     */
    public function updateAccountMoney($org_id, $assign_money, $fenpei_id)
    {
        //在现金分配时，默认先分配资金账户中的现金返利余额，把分配的现金返利的金额，记录到“现金返利扣减余额”中；
        $remain                = $this->getInfoById('org_id=' . intval($org_id), 'fanli_discount_remain, cash_fanli_remain', 'oil_account_money');
        $cash_fanli_remain     = $remain[0]->cash_fanli_remain;
        $fanli_discount_remain = $remain[0]->fanli_discount_remain;//现金返利扣减余额
        if (floatval($assign_money) > 0) {
            if (floatval($assign_money) < $cash_fanli_remain) {
                $use_cash_fanli        = floatval($assign_money);
                $fanli_discount_remain = $fanli_discount_remain + $use_cash_fanli;
                $cash_fanli_remain     = $cash_fanli_remain - $use_cash_fanli;
            } else {
                $use_cash_fanli        = $cash_fanli_remain;
                $fanli_discount_remain = $fanli_discount_remain + $use_cash_fanli;
                $cash_fanli_remain     = 0;
            }
            $fanliMoney = $use_cash_fanli;
        } else {
            $remain                = $this->getInfoById('id=' . intval($fenpei_id), 'use_cash_fanli', 'oil_account_assign');
            $use_cash_fanli        = $remain[0]->use_cash_fanli;
            $fanli_discount_remain = $fanli_discount_remain - $use_cash_fanli;
            $cash_fanli_remain     = $cash_fanli_remain + $use_cash_fanli;
            $fanliMoney            = $use_cash_fanli;
            $use_cash_fanli        = 0;
        }

        $sql = "UPDATE oil_account_money SET money = money-$assign_money, assign_total = assign_total+$assign_money, cash_fanli_remain=$cash_fanli_remain, fanli_discount_remain=$fanli_discount_remain WHERE org_id = $org_id";

        if (parent::exec($sql)) {
            //更新现金分配表中的使用现金返利字段
            if (FALSE !== $this->exec("UPDATE oil_account_assign SET use_cash_fanli=$use_cash_fanli WHERE id=" . intval($fenpei_id))) {
                return $fanliMoney;
            } else {
                return FALSE;
            }
        } else {
            return FALSE;
        }
    }

    public function updateAssign($use_cash_fanli, $fenpei_id)
    {

        parent::exec("UPDATE oil_account_assign SET use_cash_fanli=14 WHERE id=631");
    }

    /**
     * 账户积分变动
     *
     * @param $org_id
     * @param $main_id
     * @param $assign_jifen
     * @return bool
     */
    public function updateAccountJifen($org_id, $main_id, $assign_jifen)
    {
        $sql = "UPDATE oil_account_jifen SET jifen = jifen-$assign_jifen, assign_total = assign_total+$assign_jifen WHERE org_id = $org_id AND main_id = $main_id";

        if (parent::exec($sql)) {
            return TRUE;
        } else {
            return FALSE;
        }
    }

    /**
     * 验证副卡号是否属于所选的机构
     *
     * @param $org_id
     * @param $viceIds
     * @return ArrayIterator
     * <AUTHOR>
     * @since  2015/09/29
     */
    public function checkIsBelongOrg($org_id, $viceIds)
    {
        $org_sql  = "SELECT orgcode FROM oil_org WHERE id = $org_id";
        $org_data = parent::queryBySql($org_sql);
        $orgcode  = $org_data[0]->orgcode;

        if ($orgcode) {
            $sql = "SELECT count(*) count, GROUP_CONCAT(ocv.vice_no) vice_nos FROM oil_card_vice ocv
                    LEFT JOIN oil_org oo ON ocv.org_id = oo.id
                    WHERE oo.orgcode NOT LIKE '$orgcode%' AND ocv.id in ($viceIds)";

            $result = parent::queryBySql($sql);
        }

        return $result[0];
    }

    /**
     * 批量通过申请id获取申请单详情
     *
     * @param            $ids
     * @param string     $limit
     * @param bool|false $is_count
     * @return ArrayIterator
     * <AUTHOR>
     * @since  2015/11/23
     */
    public function getDetailsByIds($ids, $limit = '', $is_count = FALSE)
    {
        $sql = "SELECT %s FROM oil_account_assign oaa
                LEFT JOIN oil_account_assign_details oaad ON oaa.id = oaad.assign_id
                LEFT JOIN oil_card_vice ocv ON ocv.id = oaad.vice_id
                LEFT JOIN oil_card_main ocm ON ocm.id = ocv.card_main_id
                LEFT JOIN oil_provinces op ON op.id = ocm.fanli_region
                LEFT JOIN oil_org oo ON oo.id = oaa.org_id
                WHERE oaa.id in (" . $ids . ") ORDER BY oaa.no DESC ";

        if ($is_count) {
            $result = parent::queryBySql(sprintf($sql, 'count(oaa.id) count'));

            return $result[0]->count;
        } else {
            $fileds = " ocv.vice_no, oaad.truck_no, oaad.assign_money, oaad.assign_jifen, oaa.no, ocm.main_no, ocm.oil_com, op.province, oo.org_name ";
            $data   = parent::queryBySql(sprintf($sql, $fileds) . $limit);

            if ($data) {
                $oil_com = $this->loadModel('oil_card_main')->getOilCom();

                $orgSql  = "SELECT id, org_name FROM oil_org WHERE is_del = 0";
                $orgData = parent::queryBySql($orgSql);

                if ($orgData) {
                    foreach ($data as $key => $val) {
                        $val->oil_com_name = $oil_com[$val->oil_com]['oil_com'];

                        foreach ($orgData as $_k => $_v) {
                            if ($val->org_id == $_v->id) {
                                $val->org_name = $_v->org_name;
                            }
                            if ($val->org_id_big == $_v->id) {
                                $val->create_org_name = $_v->org_name;
                            }
                            if ($val->org_id_fanli == $_v->id) {
                                $val->fanli_org_name = $_v->org_name;
                            }
                        }
                    }
                }
            }

            return $data;
        }
    }

    /**
     * 更新副卡的备付金余额和积分备付金
     *
     * @param $id
     * @return bool
     */
    public function updateViceReserve($id)
    {

        $sql        = "SELECT vice_id,ocv.oil_com,ocv.remain_get_time,assign_money,assign_jifen,oaatd.updatetime FROM oil_account_assign_details oaad
                        LEFT JOIN oil_account_assign_task_detail oaatd ON oaatd.assign_detail_id = oaad.id AND oaatd.status = 10
                        INNER JOIN oil_card_vice ocv ON ocv.id = oaad.vice_id
                        INNER JOIN oil_card_main oma ON oma.id = ocv.card_main_id
                        WHERE oaad.assign_id = $id  and ocv.oil_com in (1,2,3,4,6,50)";
        $detailInfo = parent::queryBySql($sql);
        foreach ($detailInfo as $v) {
            if ($v->oil_com == 6 || $v->oil_com == 4) {//针对充值卡直接更新副卡余额
                if ($v->assign_money != 0.00) {
                    $sql = "UPDATE oil_card_vice SET card_remain = card_remain+" . $v->assign_money . ",total_charge = total_charge+" . $v->assign_money . ",assign_time = '" . helper::nowTime() . "' WHERE id = '$v->vice_id'";
                }
            } else {
                if ($v->updatetime && $v->updatetime <= $v->remain_get_time) {//若分配任务完成时间小于副卡余额同步时间，则不同步备付金
                    continue;
                }
                if ($v->assign_money != 0.00 && $v->assign_jifen != 0.00) {
                    $sql = "UPDATE oil_card_vice SET reserve_remain = reserve_remain + " . $v->assign_money . ",total_charge = total_charge+" . $v->assign_money . ",
                        point_reserve_total = point_reserve_total + " . $v->assign_jifen . ",assign_time = '" . helper::nowTime() . "' WHERE id = '$v->vice_id'";
                } else if ($v->assign_money != 0.00) {//更新备付金金额
                    $sql = "UPDATE oil_card_vice SET reserve_remain = reserve_remain + " . $v->assign_money . ",total_charge = total_charge+" . $v->assign_money . ",assign_time = '" . helper::nowTime() . "' WHERE id = '$v->vice_id'";
                } else if ($v->assign_jifen != 0.00) {//更新积分
                    $sql = "UPDATE oil_card_vice SET point_reserve_total = point_reserve_total + " . $v->assign_jifen . ",assign_time = '" . helper::nowTime() . "' WHERE id = '$v->vice_id'";
                }
            }
            parent::exec($sql);
        }

        return TRUE;
    }

    /**
     * 销审后更新副卡的备付金余额和积分备付金
     *
     * @param $id
     * @return bool
     */
    public function updateReserve($id)
    {
        $sql        = "SELECT vice_id,ocv.oil_com,assign_money,assign_jifen FROM oil_account_assign_details oaad
                        INNER JOIN oil_card_vice ocv ON ocv.id = oaad.vice_id
                        INNER JOIN oil_card_main oma ON oma.id = ocv.card_main_id
                        WHERE oaad.assign_id = $id  and ocv.oil_com in (1,2,3,4,6,50)";
        $detailInfo = parent::queryBySql($sql);
        foreach ($detailInfo as $v) {
            if ($v->oil_com == 4 || $v->oil_com == 6) {
                $sql = "UPDATE oil_card_vice SET card_remain = card_remain-" . $v->assign_money . ",total_charge = total_charge-" . $v->assign_money . " WHERE id = '$v->vice_id'";
            } else {
                $sql = "UPDATE oil_card_vice SET reserve_remain = reserve_remain - " . $v->assign_money . ",
                        point_reserve_total = point_reserve_total - " . $v->assign_jifen . ",total_charge = total_charge-" . $v->assign_money . " WHERE id = '$v->vice_id'";
            }
            parent::exec($sql);
        }

        return TRUE;
    }
}