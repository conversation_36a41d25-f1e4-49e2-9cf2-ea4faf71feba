<?php
/**
 * 上游返利计算工单明细表 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/01/05
 * Time: 14:45:15
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilUpstreamRebateCalDetails;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_upstream_rebate_cal_details extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilUpstreamRebateCalDetails::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
            'fileName' => '上游返利计算工单明细表_' . date("YmdHis"),
            'sheetName' => '上游返利计算工单明细表',
            'title' => [
            'id'   =>  '主键',
'order_id'   =>  '关联工单id',
'supplier_id'   =>  '供应商id',
'supplier_name'   =>  '供应商名称',
'cooperation_type'   =>  '10平台，20站点，30主卡',
'station_code'   =>  '站点编码',
'station_name'   =>  '站点名称',
'main_card'   =>  '主卡号',
'operator_id'   =>  '平台运营商id',
'operator_name'   =>  '平台运营商',
'cal_level'   =>  '10 1级-供应商  20 2级-主卡 30 3级-站点',
'cal_obj_name'   =>  '计算主体名称',
'cal_object'   =>  '计算对象 10消费数据 20充值数据',
'cal_object_money'   =>  '计算对象总金额',
'cal_object_num'   =>  '计算对象总升数',
'fanli_val'   =>  '返利值',
'fanli_money'   =>  '返利结果',
'create_time'   =>  '创建时间',
'update'   =>  '更新时间'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilUpstreamRebateCalDetails::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilUpstreamRebateCalDetails::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilUpstreamRebateCalDetails::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilUpstreamRebateCalDetails::remove($params);

        Response::json($data);
    }

}