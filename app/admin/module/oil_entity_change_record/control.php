<?php
/**
 * 服务区/油站变更记录 Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/09/01
 * Time: 17:47:40
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilEntityChangeRecord;
use Models\OilSupplierOperator;
use Framework\Excel\ExcelWriter;
use Fuel\Response;
use Fuel\Defines\StationArea;

class oil_entity_change_record extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilEntityChangeRecord::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $redirect_url = \Fuel\Service\ExportJobSrv::syncExportTask($params,"exportList",$data);
            echo "<script>window.location.href = '/".strtolower(__CLASS__)."';window.open('".$redirect_url."')</script>";
//            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    public function exportList($data)
    {
        $exportData = [
            'fileName' => '服务区油站变更记录_' . date("YmdHis"),
            'sheetName' => '服务区油站变更记录',
            'download'  => 1, //增加
            'title' => [
                #'id'   =>  '主键',
                'no'   =>  '申请单号',
                'res_name'   =>  '油站/服务区/供应商名称',
                'res_code'   =>  '油站/服务区/供应商编码',
                'classify_txt'   =>  '分类',
                'res_type_txt'   =>  '业务类型',
                'res_from_id'   =>  '解绑主体Id',
                'res_from_name'   =>  '解绑主体名称',
                'res_to_id'   =>  '绑定主体Id',
                'res_to_name'   =>  '绑定主体名称',
                'remark'   =>  '备注',
                'creator'   =>  '创建人',
                'last_operator'   =>  '更新人',
                'createtime'   =>  '创建时间',
                'updatetime'   =>  '更新时间'
            ],
            'data' => $data->toArray(),
        ];

        $url = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
        return $url;
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilEntityChangeRecord::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilEntityChangeRecord::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilEntityChangeRecord::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilEntityChangeRecord::remove($params);

        Response::json($data);
    }

    /**
     * 期初历史数据
     */
    public function initHistory()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['type'], $params);
        $data = \Fuel\Service\EntityChange::initHistory($params);
        Response::json($data);
    }

    /**
     * 导入供应商主体变更记录
     * 读取表格数据（供应商ID、供应商名字、换签时间、换签前主体、换前后主体）
     * 根据读取到的换签前主体及换签后主体查询签约运营商表相应的id
     * 整合数据录入至oil_entity_change_record
     * @return mixed
     */
    public function initSupplierChangeOperator()
    {
            // 使用PHPExcel读取上传的Excel文件
            $inputFileType = PHPExcel_IOFactory::identify("/data/web_data/web/换签期初.xlsx");
            $objReader = PHPExcel_IOFactory::createReader($inputFileType);
            $objReader->setReadDataOnly(TRUE);
            $objReader->setLoadAllSheets();
            $objPHPExcel = $objReader->load("/data/web_data/web/换签期初.xlsx");
            $sheet = $objPHPExcel->getSheet(0);
            $highestRow = $sheet->getHighestRow();
            $insertData = [];
            // 从第2行开始读取数据（第1行是表头）
            for ($row = 2; $row <= $highestRow; $row++) {
                $supplier_id = trim($sheet->getCellByColumnAndRow(0, $row)->getValue());
                $supplier_name = trim($sheet->getCellByColumnAndRow(1, $row)->getValue());
                $change_time = trim($sheet->getCellByColumnAndRow(2, $row)->getValue());
                $before_entity = trim($sheet->getCellByColumnAndRow(3, $row)->getValue());
                $after_entity = trim($sheet->getCellByColumnAndRow(4, $row)->getValue());
                // 验证必填字段
                if (empty($supplier_id) || empty($supplier_name) || empty($before_entity) || empty($after_entity) || empty($change_time)) {
                    throw new Exception("第{$row}行数据不完整，供应商id、供应商名称、换签前主体、换签后主体、换签时间为必填项");
                }
                // 查询换签前主体对应的运营商ID
                $before_operator = \Models\OilOperators::getByFilter([
                    'company_name' => $before_entity,
                ]);
                // 查询换签后主体对应的运营商ID
                $after_operator = \Models\OilOperators::getByFilter([
                    'company_name' => $after_entity,
                ]);
                $before_operator_id = $before_operator ? $before_operator->id : 0;
                $after_operator_id = $after_operator ? $after_operator->id : 0;
                // 如果找不到对应的运营商ID，记录错误
                if ($before_operator_id == 0 && $after_operator_id == 0) {
                    throw new Exception("第{$row}行数据的换签前主体'{$before_entity}'和换签后主体'{$after_entity}'在签约运营商表中未找到");
                } else if ($before_operator_id == 0) {
                    throw new Exception("第{$row}行数据的换签前主体'{$before_entity}'在签约运营商表中未找到");
                } else if ($after_operator_id == 0) {
                    throw new Exception("第{$row}行数据的换签后主体'{$after_entity}'在签约运营商表中未找到");
                }
                $insertData[] = [
                    'no' => OilEntityChangeRecord::createNo(),
                    'classify' => 30, // 30表示供应商
                    'res_type' => StationArea::SUPPLIER_CHANGE_OPERATOR, // 主体变更
                    'res_name' => $supplier_name,
                    'res_code' => $supplier_id,
                    'res_from_id' => $before_operator_id,
                    'res_from_name' => $before_entity,
                    'res_to_id' => $after_operator_id,
                    'res_to_name' => $after_entity,
                    'remark' => '供应商主体变更期初',
                    'creator' => '系统',
                    'last_operator' => '系统',
                    'createtime' => date('Y-m-d H:i:s'),
                    'updatetime' => date('Y-m-d H:i:s'),
                    'change_time' => $change_time,
                    'supplier_id' => $supplier_id
                ];
            }
            OilEntityChangeRecord::insert($insertData);
        } catch (\Exception $e) {
            Response::json(['success' => false, 'message' => '导入失败：' . $e->getMessage()], 1);
        }
    }

    /**
     * 导入服务区合并记录
     * 读取表格数据（原服务区ID、原服务区编码、原服务区名字、合并时间、合并后服务区ID、合并后服务区名称、合并后的服务区编码）
     * 整合数据录入至oil_entity_change_record
     * @return mixed
     */
    public function initServiceAreaMerge()
    {
        try {
            // 使用PHPExcel读取上传的Excel文件
            $inputFileType = PHPExcel_IOFactory::identify("/data/web_data/web/服务区合并期初.xlsx");
            $objReader = PHPExcel_IOFactory::createReader($inputFileType);
            $objReader->setReadDataOnly(TRUE);
            $objReader->setLoadAllSheets();
            $objPHPExcel = $objReader->load("/data/web_data/web/服务区合并期初.xlsx");
            $sheet = $objPHPExcel->getSheet(0);
            $highestRow = $sheet->getHighestRow();
            $insertData = [];

            // 从第2行开始读取数据（第1行是表头）
            for ($row = 2; $row <= $highestRow; $row++) {
                $original_area_id = trim($sheet->getCellByColumnAndRow(0, $row)->getValue());
                $original_area_code = trim($sheet->getCellByColumnAndRow(1, $row)->getValue());
                $original_area_name = trim($sheet->getCellByColumnAndRow(2, $row)->getValue());
                $merge_time = trim($sheet->getCellByColumnAndRow(3, $row)->getValue());
                $new_area_id = trim($sheet->getCellByColumnAndRow(4, $row)->getValue());
                $new_area_name = trim($sheet->getCellByColumnAndRow(5, $row)->getValue());
                $new_area_code = trim($sheet->getCellByColumnAndRow(6, $row)->getValue());

                // 验证必填字段
                if (empty($original_area_id) || empty($original_area_code) || empty($original_area_name) ||
                    empty($merge_time) || empty($new_area_id) || empty($new_area_code) || empty($new_area_name)) {
                    throw new \Exception("第{$row}行数据不完整，原服务区ID、原服务区编码、原服务区名称、合并时间、合并后服务区ID、合并后服务区编码、合并后服务区名称为必填项");
                }
                $insertData[] = [
                    'no' => OilEntityChangeRecord::createNo(),
                    'classify' => StationArea::AREA_CLASSIFY, // 20表示服务区
                    'res_type' => StationArea::AREA_MERGE, // 50表示服务区合并
                    'res_name' => $original_area_name,
                    'res_code' => $original_area_code,
                    'res_from_id' => $original_area_id,
                    'res_from_name' => $original_area_name,
                    'res_to_id' => $new_area_id,
                    'res_to_name' => $new_area_name,
                    'remark' => '服务区合并期初',
                    'creator' => '系统',
                    'last_operator' => '系统',
                    'createtime' => date('Y-m-d H:i:s'),
                    'updatetime' => date('Y-m-d H:i:s'),
                    'change_time' => $merge_time
                ];
            }

            if (empty($insertData)) {
                throw new \Exception("没有有效的数据需要导入");
            }
            OilEntityChangeRecord::insert($insertData);
            Response::json(['success' => true, 'message' => '导入成功，共导入 ' . count($insertData) . ' 条记录'], 0);
        } catch (\Exception $e) {
            Response::json(['success' => false, 'message' => '导入失败：' . $e->getMessage()], 1);
        }
    }
}