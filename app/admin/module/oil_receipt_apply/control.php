<?php
/**
 *  转账申请-现金
 * <AUTHOR>
 */
require_once APP_ROOT . DIRECTORY_SEPARATOR . 'Models' . DIRECTORY_SEPARATOR . 'OilReceiptApply.php';

use Framework\Cache;
use Framework\DingTalk\DingTalkAlarm;
use Framework\RedisInstance;
use Fuel\Defines\CardTradeConf;
use Fuel\Defines\ReceiptApplyDefine;
use Fuel\Defines\ReceiptScope;
use Fuel\Defines\ReceiptType;
use Fuel\Service\ReceiptApplyInternal;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilReceiptApply;
use Models\OilReceiptEmailLog;
use Models\OilReceiptInvoiceTrades;
use Models\OilReceiptTitle;
use Models\OilOrg;
use Models\OilCardViceTrades;
use Models\OilReceiptApplyDetails;
use \Framework\Excel\ExcelReader;
use \Fuel\Response;
use \Fuel\Service\OpenReceipt;
use \Framework\Log;
use \Framework\Job;
use \Fuel\Defines\ReceiptApplyStatus;
use \Fuel\Defines\IsOpenInvoiceForTrades;
use Fuel\Service\ReceiptSplitApply;
use Fuel\Defines\ReceiptTranslateDetail;
use Fuel\Service\OpenWhiteReceipt;
use Symfony\Component\Translation\Translator;

class oil_receipt_Apply extends baseControl
{
    protected $fieldMap = [];
    public function __construct()
    {
        parent::__construct();
        $this->fieldMap = [
            'no' => '申请单号',
            'orgcode' => '机构编码',
            'org_name' => '申请机构',
            'corp_name' => '发票抬头',
            'taxpayer_no' => '纳税人识别号',
            'corp_addr' => '地址、电话',
            'bank_name' => '开户行及账号',
            'receipt_type' => '开票类型',
            'is_internal_txt' => '内部票',
            'open_channel_name' => '开票渠道',
            'oil_classify' => '油品种类',
            'receipt_amount' => '申请金额',
            'real_amount' => '实开金额',
            'amount_status' => '金额状态',
            '_receipt_status' => '状态',
            'addr_name' => '接收人',
            'addr_mobile' => '手机号',
            'address' => '邮寄地址',
            'email' => '收票邮箱',
            'deliver_corp' => '快递公司',
            'deliver_no' => '快递单号',
            'email_send_cycle' => '邮件发送次数',
            'apply_time' => '申请时间',
            'org_operators_name' => '机构运营商',
            'seller_name' => '销售方名称',
            'seller_taxpayer_no' => '纳税人识别号(销售方)',
            '_data_from' => '数据来源',
            'creator_name' => '创建人',
            'createtime' => '创建时间',
            'last_operator' => '最后修改人',
            'updatetime' => '最后更新时间',
            'custom_remark' => '客户备注',
            'admin_remark' => '后台备注',
            'receipt_remark' => '票面备注',
        ];
    }

    public function test_exp()
    {
        $params = [
            'ids' => '1681',//'1557',//'1502',//'1207',//,1101
            'is_internal' => 2,
            'open_channel' => 30,
        ];
        $data = (new ReceiptSplitApply())->exportData($params);
        var_dump($data);
    }

    public function exportEmptySupplierUnit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);

        $apply = OilReceiptApply::getList(['receipt_status' => ReceiptApplyStatus::AUDITED, 'ids' => $params['ids'], '_export' => 1]);

        $_data = OilReceiptApplyDetails::getListForInternal([
            'receipt_apply_id' => $apply[0]->id,
            'oil_sec_type_group' => true,
            'oil_type'=> $apply[0]->oil_type,
        ]);

        $data = (new \Fuel\Service\ReceiptSplit())->checkInternalReceiptSplit($_data);

        if(!$data){
            throw new \RuntimeException('无数据导出',2);
        }
        $data = array_values($data);
        Log::error('exportEmptySupplierUnit:',$data,'exportEmptySupplierUnit');

        $realPath = \APP_ROOT . \DIRECTORY_SEPARATOR . 'www';

        $filePath = APP_ROOT . DIRECTORY_SEPARATOR . 'www' . DIRECTORY_SEPARATOR . 'download' . DIRECTORY_SEPARATOR . 'receiptSplit' . DIRECTORY_SEPARATOR;
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }

        $titleArr = [
            'supplier_name' => '供应商名称',
            'supplier_id' => '供应商id',
            'oil_sec_type_name' => '缺失油品类型',
        ];

        $excelFilePath = \Framework\Excel\ExcelWriter::exportXls([
            'fileName'  => 'receiptSplit' . DIRECTORY_SEPARATOR . 'supplier_unit_' . date("YmdHis") . rand(100, 999),
            'sheetName' => '供应商回票单位缺失',
            'fileExt'   => 'xls',
            'download'  => TRUE,
            'title'     => $titleArr,
            'data'      => $data,
        ], function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], [])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });
        $downUrl = str_replace($realPath,'',$excelFilePath);
        Log::error('$excelFilePath:',[$downUrl],'exportEmptySupplierUnit');

        Response::json($downUrl,0,'成功');
    }

    /**
     * 搜索 默认搜全部
     * <AUTHOR> Du
     * @since 2016/03/14
     */
    public function receiptSearch()
    {
        //输出结果
        $params = helper::filterParams();
        Log::info('$params--' . var_export($params, TRUE), [], 'receiptApply');
        if (isset($params['org_flag']) && $params['org_flag']) {
            if (isset($params['org_code'])) {
                $params['orgcode_lk'] = $params['org_code'];
                unset($params['org_code']);
            }
        } else {
            if (isset($params['org_code'])) {
                $params['orgcode_eq'] = $params['org_code'];
                unset($params['org_code']);
            }
        }
        $data = OilReceiptApply::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            //增加
            $redirect_url = \Fuel\Service\ExportJobSrv::syncExportTask($params, "exportList", $data,[],$this->fieldMap);
            if (!empty($redirect_url)) {
                echo "<script>window.location.href = '/" . strtolower(__CLASS__) . "';window.open('" . $redirect_url . "')</script>";
            }
            //$this->exportList($data);
        } else {

            $type = [
                'contact_name' => 1,
                'addr_name' => 1,
                'addr_mobile' => 2,
                'address' => 5,
                'seller_taxpayer_no' => 14,
                'bank_name' => 4,
                'corp_addr' => 5,
            ];
            $data = \Fuel\Service\MaskingService::maskingData($data, $type);

            Response::json($data);
        }
    }

    /**
     * G7S端支持导出
     * @throws PHPExcel_Exception
     */
    public function exportForG7s()
    {
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . 'data' . DIRECTORY_SEPARATOR . 'oil_receipt_apply';
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }
        $url = NULL;
        $params = helper::filterParams();
        $params['_export'] = 1;
        $data = OilReceiptApply::getList($params);
        if (count($data) == 0) {
            throw new \RuntimeException('无数据导出', 2);
        }

        $exportData = [
            'filePath' => $filePath,
            'fileName' => '发票管理_' . \Framework\Helper::uuid(),
            'sheetName' => '发票管理',
            'download' => 1,
            'title' => [
                'apply_time' => '申请时间',
                'seller_name' => '销售方',
                'corp_name' => '开票抬头',
                'trade_start_time' => '消费开始时间',
                'trade_end_time' => '消费结束时间',
                'oil_classify' => '商品类型',
                'receipt_amount' => '金额',
                '_receipt_status_g7s' => '状态',
                'receipt_type' => '发票类型',
                '_deliver_corp' => '快递',
                'download_url' => '电子地址',
                'org_name' => '机构',
            ],
            'data' => $data->toArray(),
        ];

        $filePath = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], [])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });

        if ($filePath) {
            //上传阿里云
            $url = (new \commonModel())->fileUploadToOss($filePath);
            $signUrl = \Fuel\Service\UploadService::getOssSignUrl($url);
        } else {
            throw new \RuntimeException('导出失败！', 2);
        }

        Response::json(['osspath' => $signUrl]);
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    public function exportList($data)
    {
        $exportData = [
            'fileName' => '发票申请_' . date("YmdHis"),
            'sheetName' => '发票申请',
            'download' => 1, //增加
            'title' => $this->fieldMap,
            'data' => $data->toArray(),
        ];

        $url = Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['taxpayer_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });
        return $url;
    }

    public function tonUnitInvoiceWhiteCheck()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);

        $applies = OilReceiptApply::Filter(['ids' => $params['ids'], 'receipt_status' => ReceiptApplyStatus::AUDITED])
            ->get(['id', 'no', 'org_code']);

        $res = (new \Fuel\Service\ReceiptApply())->tonUnitInvoiceWhiteCheck($applies);

        Response::json($res, 0);
    }

    /**
     * @title 导出明细并进行发票拆分
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function exportData()
    {
        $data = null;

        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $validator = new Illuminate\Validation\Validator(new Translator('en'), $params, [
            'open_channel'   => 'required|in:' . implode(',',
                    array_keys(ReceiptType::$receipt_open_channel_list)),
        ], [
            'open_channel.required' => '请选择开票渠道',
            'open_channel.in' => '开票渠道不正确',
        ]);
        if ($validator->fails()) {
            Response::json([], 412001, $validator->errors()->first());
        }
        //校验是否有已审核的工单
        $info = OilReceiptApply::getList(['receipt_status' => ReceiptApplyStatus::AUDITED, 'ids' => $params['ids'], '_export' => 1]);
        $idArr = [];

        $oilType = [
            'urea' => 0,
            'not_urea' => 0
        ];
        $idMap = [];
        if ($info) {
            foreach ($info as $v) {
                $idArr[] = $v->id;
                $idMap[$v->id] = $v->no;
                if ($v->receipt_type != ReceiptType::$receiptTypeList[ReceiptType::SPECIAL] and
                    $v->receipt_type != ReceiptType::$receiptTypeList[ReceiptType::NORMAL] and
                    $params['open_channel'] == ReceiptType::RECEIPT_OPEN_CHANNEL_HX) {
                    throw new \RuntimeException(
                        '该开票渠道不支持申请单' . $v->no .
                        '包含开票类型的发票开具', 2
                    );
                }
            }
        }
        if (!$idArr) {
            throw new \RuntimeException('没有已审核的工单', 2);
        }

        //二级分类分组金额校验
        $checkdata = OilReceiptApplyDetails::getList([
            'receipt_apply_idIn' => $idArr,
            'oil_sec_type_group' => 1,
            'is_direct_return' => 1,
            'receipt_apply_id_group' => 1,
        ]);
        if($checkdata){
            foreach ($checkdata as $item){
                if($item->trade_num_sum <= 0 || $item->trade_money_sum <= 0 || $item->use_fanli_money_sum < 0 || ($item->trade_money_sum - $item->use_fanli_money_sum) <= 0){
                    throw new \RuntimeException('操作失败,二级油品汇总中存在负数或0无法开票,请更换消费期间重试!', 2);
                }
            }
        }

        $condition['receipt_apply_idIn'] = $idArr;
        $condition['oil_name_group'] = 1;
//         $condition['check_oil_sec_type'] = 1;
        $detailArr = OilReceiptApplyDetails::getList($condition);
        if(count($detailArr) > 0){
            $nos = $types = $msg = $no_oil_name = [];
            foreach ($detailArr as $_val){
                if( empty($_val->oil_sec_type) ){
                    $nos[] = isset($idMap[$_val->receipt_apply_id]) ? $idMap[$_val->receipt_apply_id] : "";
                    $types[$_val->oil_name] = $_val->oil_name;
                }

                if(empty($_val->receipt_oil_name)){
                    $no_oil_name[] = $_val->oil_name;
                }
            }

            if(!empty($no_oil_name)){
                $msg[] = "操作失败，以下油品未维护票面油品，请先维护";
                $msg[] = implode(",", $no_oil_name);
                $error = str_replace("\n","<br/>",implode("\n",$msg));
                throw new \RuntimeException($error,2);
            }
            if(count($nos) > 0){
                $msg[] = '环境：' . API_ENV;
                $msg[] = '紧急程度：' . "High";
                $msg[] = "有问题的单据：\n".implode("\n",array_unique($nos));
                $msg[] = "缺失类型的油品：\n".implode("\n",array_values($types));
                (new DingTalkAlarm())->alarmToGroup("***导出开票信息，油品种类校验***", implode("\n", $msg));
                unset($msg[0],$msg[1]);
                $error = str_replace("\n","<br/>",implode("\n",$msg));
                throw new \RuntimeException($error,2);
            }
        }

        $flag = (new \Fuel\Service\ReceiptApply())->tonUnitInvoiceWhiteCheck($info);
        //内部票校验
        if(isset($params['is_internal']) && $params['is_internal'] == 1){
            $this->checkInternalReturnUnit($params,$info);
        }else{
            if ($flag === true && $info[0]->oil_type != \Fuel\Defines\OilType::UREA_YOU && empty($params['unit'])) {
                throw new \RuntimeException('请选择发票申请开具单位', 2);
            }
        }
        // G7WALLET-5370 内部票改造  数量、开票金额 > 0
        // 内部票只能选择一条
        if (count($info) == 1 && $info[0]->is_internal == ReceiptScope::INTERNAL) {

            $dataList = OilReceiptApplyDetails::getListForInternal([
                'receipt_apply_id' => $info[0]->id,
                'oil_sec_type_group' => true,
                'oil_type'=>$info[0]->oil_type,
            ]);

            $_data = (new \Fuel\Service\ReceiptSplit())->internalReceiptSplit($dataList);

            $errMsg = '';
            foreach ($_data as $item){
                if ($item['trade_num'] < 0 || $item['receipt_money'] < 0) {
                    $errMsg = empty($errMsg) ? $errMsg.$item['oil_sec_type_name'].$item['unit']
                        : $errMsg.'、'.$item['oil_sec_type_name'].$item['unit'];
                }
            }
            if (!empty($errMsg)) {
                throw new \RuntimeException('操作失败,'.$errMsg.' 汇总后金额/数量为负值', 2);
            }
        }


        Log::error('开票信息导出参数：' . var_export($idArr, TRUE), [], 'receiptApplyExport');

        try {
            //修改申请单状态为开票中
            $content = [
                'receipt_status' => ReceiptApplyStatus::OPENING,
                'open_channel' => $params['open_channel'],
            ];

            if ($flag === true && $info[0]->oil_type != \Fuel\Defines\OilType::UREA_YOU) {
                $content['unit'] = $params['unit'];
            }

            OilReceiptApply::whereIn('id', $idArr)
                ->where('receipt_status', ReceiptApplyStatus::AUDITED)->update($content);



//            $data = (new \Fuel\Service\ReceiptApply())->exportData(['ids'=>$idArr]);
            global $app;
            if ( $app->config->scheduler->switch == 1 ) {
                $data = (new \Jobs\SplitReceiptDetailsJob($params)) //['ids' => $idArr]
                    ->setTaskName('导出发票明细并拆分')
                    ->setUserInfo($this->app->myAdmin)
                    ->onQueue('invoice_split')
                    ->dispatch();
            }else {
                $data = (new \Jobs\SplitSaleReceiptJob($params)) //['ids' => $idArr]
                    ->setTaskName('导出发票明细并拆分')
                    ->setUserInfo($this->app->myAdmin)
//                ->onQueue('invoice_split_liying')                 // @todo 之后这行需要注释掉
                    ->dispatch();

            }

        } catch (\Exception $e) {
            Log::error('开票信息导出异常：' . $e->getCode() . '--' . $e->getMessage(), [strval($e)], 'receiptApplyError');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

//        Response::json($data, 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
//        Response::json(["redirect_url" => $data->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
        Response::json(true, 0, '任务下发成功');
    }

    public function checkInternalReturnUnit($params,$apply)
    {
        $ids = explode(',',$params['ids']);
        if(count($ids) > 1){
            throw new \RuntimeException('内部票只能选择单条',2);
        }

        $data = OilReceiptApplyDetails::getListForInternal([
            'receipt_apply_id' => $apply[0]->id,
            'oil_sec_type_group' => true,
            'oil_type'=> $apply[0]->oil_type,
        ]);

        $_data = (new \Fuel\Service\ReceiptSplit())->checkInternalReceiptSplit($data);

        if($_data){
            $errMsg = [];
            foreach ($_data as $item){
                $errMsg[] = $item['supplier_name'].'的'.$item['oil_sec_type_name'];
            }
            if($errMsg){
                throw new \RuntimeException('供应商:'.implode(',',$errMsg).'的回票单位为空',2);
            }
        }
    }

    public function getExportDetail()
    {
        $params['ids'] = [500];
        (new \Fuel\Service\ReceiptSplitApply())->exportData($params);
    }

    /**
     * @title 导出消费明细
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function exportTradeDetail()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $id_arr = explode(",",$params['ids']);
        if(count($id_arr) <= 0){
            throw new \RuntimeException('操作失败！请选择要导出的发票', 2);
        }
        if(count($id_arr) > 50){
            throw new \RuntimeException('操作失败！最多支持50条', 2);
        }

        $num = OilReceiptApplyDetails::exportTradesDetails(['receipt_apply_idIn' => $id_arr, 'count' => 1]);
        if ($num <= 0) {
            throw new \RuntimeException('暂无占用的消费明细', 2);
        }
        /*if ($num > 300000) {
            throw new \RuntimeException('单次导出不能超过30万行', 2);
        }*/

        if (isset($params['_export']) && $params['_export'] == 1) {
            /*$data = (new \Jobs\ExportReceiptTradeDetailsJob(['receipt_apply_id' => $params['ids'], 'receipt_no' => $apply->no]))
                ->setTaskName('开票消费-导出')
                ->setUserInfo($this->app->myAdmin)
                ->dispatch();*/

            $params['ids'] = implode(",",array_unique($id_arr));
            //根据角色判断逻辑
            $params['is_show_field'] = $this->checkRole();
            $result = \Fuel\Service\FossTaskExport::addExportJob($params,"发票申请对应的消费明细","oil_receipt_apply","task:export-apply");

            Response::json(["redirect_url" => $result->redirect_url], 0, '正在进行导出处理，请稍候在任务中心中查看结果...');
        } else {
            throw new \RuntimeException('参数错误', 2);
        }
    }

    public function checkRole()
    {
        $is_show = 'hide';
        if (in_array(API_ENV, ["pro", "prod"])) {
            $checkMap = [43,66]; //43财务查询，66系统业务管理员
        }else{
            $checkMap = [30];
        }

        $roleIds = $this->app->myAdmin->roles;
        Log::error('当前角色：',[$roleIds],'exportTradeDetail');
        Log::error('checkMap：',[$checkMap],'exportTradeDetail');
        if($roleIds){
            foreach ($roleIds as $roleId){
                if(in_array($roleId,$checkMap)){
                    $is_show = 'show';
                }
            }
        }
        Log::error('is_show：',[$is_show],'exportTradeDetail');
        return $is_show;

    }

    /**
     * @title 导出快递
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function exportExpress()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);

        $data = [];
        $applyInfo = OilReceiptApply::getList(['ids' => $params['ids'], '_export' => 1]);
        foreach ($applyInfo as $v) {
            $tmp = [
                'no' => $v->no,
                'corp_name' => $v->corp_name,
                'addr_name' => $v->addr_name,
                'addr_mobile' => $v->addr_mobile,
                'address' => $v->address,
                '10' => '企管中心',
                '20' => '靳荣(王楠)',
                '30' => '13478428063',
                '40' => '辽宁省 大连市 长兴岛企管中心',
                '50' => '',
                '60' => '寄付现结',
                '70' => '',
                '80' => '文件',
                '90' => '发票',
                '100' => '',
                '110' => '1',
                '120' => '1',
                '130' => '1',
                '140' => '1',
                '150' => '顺丰标快',
                '160' => '1',
                '170' => '1',
                '180' => '1',
                '190' => '顺丰标快',
                '200' => '',
                '210' => '',
                '220' => '',
                '230' => '',
                '240' => '',
                '250' => '',
                '260' => '',
                '270' => '',
                '280' => '',
                '290' => '',
                '300' => '',
                '310' => '',
                '320' => '',
                '330' => '',
                '340' => '',
                '350' => '',
                '360' => '',
                '370' => '',
                '380' => '',
                '390' => '',
                '400' => '',
                '410' => '',
                '420' => '',
                '430' => '',
                '440' => '',
                '450' => $v->corp_name,
                '460' => '',
                '470' => '',
                '480' => '',
                '490' => '',
                '500' => '',
                '510' => '',
                '520' => '',
                '530' => '',
            ];

            $data[] = $tmp;
        }

        Log::info('$data--' . var_export($data, TRUE), [], 'zzg');

        $exportData = [
            'fileName' => '快递信息_' . date("YmdHis"),
            'sheetName' => '快递信息',
            'title' => [
                'no' => '用户订单号',
                '10' => '寄件公司',
                '20' => '联系人',
                '30' => '联系电话',
                '40' => '寄件地址',
                'corp_name' => '收件公司',
                'addr_name' => '联系人',
                '50' => '联系电话',
                'addr_mobile' => '手机号码',
                'address' => '收件详细地址',
                '60' => '付款方式',
                '70' => '第三方付月结卡号',
                '80' => '托寄物品',
                '90' => '托寄物内容',
                '100' => '托寄物编码',
                '110' => '托寄物数量',
                '120' => '件数',
                '130' => '实际重量（KG）',
                '140' => '计费重量（KG）',
                '150' => '业务类型',
                '160' => '是否代收货款',
                '170' => '代收货款金额',
                '180' => '代收卡号',
                '190' => '是否保价',
                '200' => '保价金额',
                '210' => '标准化包装（元）',
                '220' => '其它费用（元）',
                '230' => '化包装（元）',
                '240' => '是否自取',
                '250' => '是否签回单',
                '260' => '是否定时派送',
                '270' => '派送日期',
                '280' => '派送时段',
                '290' => '是否电子验收',
                '300' => '拍照类型',
                '310' => '是否保单配送',
                '320' => '是否拍照验证',
                '330' => '是否易碎件',
                '340' => '易碎金额',
                '350' => '是否票据专送',
                '360' => '是否超长超重服务',
                '370' => '超长超重服务费',
                '380' => '是否上门安装',
                '390' => '安装类型',
                '400' => '收件员',
                '410' => '寄方签名',
                '420' => '寄件日期',
                '430' => '签收短信通知(MSG)',
                '440' => '派件出仓短信(SMS)',
                '450' => '寄方客户备注',
                '460' => '长(cm)',
                '470' => '宽(cm)',
                '480' => '高(cm)',
                '490' => '扩展字段1',
                '500' => '扩展字段2',
                '510' => '扩展字段3',
                '520' => '扩展字段4',
                '530' => '体积(cm³)',
            ],
            'data' => $data,
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['taxpayer_no'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });
    }

    /**
     * @title 导入快递
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function importExpress()
    {
        $params = helper::filterParams();

        if (isset($params['filePath']) && $params['filePath']) {
            $upload = $params['filePath'];
        } else {
            //上传文件
            $upload = $this->file_upload($_FILES['userfile']);
        }

        $fieldMap = [
            'no' => '订单号',
            'deliver_corp' => '快递公司',
            'deliver_no' => '运单号',
            'status' => '收件状态',
            'orderStatus' => '订单状态',
        ];

        $excelParams = [
            'filePath' => $upload,
            'fieldsMap' => array_flip($fieldMap),
            'ignore' => TRUE
        ];

        $result = ExcelReader::read($excelParams);
        Log::info('$result--' . var_export($result, TRUE), [], 'zzg');
        $num = 0;
        foreach ($result[0] as $v) {
            $info = OilReceiptApply::getSingleRecord(['no' => $v['no']]);
            $status = "";
            if (isset($v['orderStatus']) && !empty($v['orderStatus'])) {
                $status = $v['orderStatus']; //处理峰松顺丰的模板
            } else {
                $status = $v['status'];
            }
            if (in_array($status, ['已收件', '派送中', '派送成功', '派送失败', '运输中', '已签收']) && in_array($info->receipt_status, [ReceiptApplyStatus::SUCCESS, ReceiptApplyStatus::MAILED])) {
                OilReceiptApply::updateByNo([
                    'no' => $v['no'],
                    'deliver_no' => $v['deliver_no'],
                    'deliver_corp' => $v['deliver_corp'],
                    'receipt_status' => ReceiptApplyStatus::MAILED
                ]);
                $num++;
            }
        }

        Response::json(null, 0, '处理成功' . $num . '条');
    }

    /**
     * Desc: 发票申请 - 导入内部票
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 13/7/22 上午11:42
     */
    public function importInternalInvoiceApply()
    {
        $params = helper::filterParams();
        if (isset($params['filePath']) && $params['filePath']) {
            $upload = __DIR__ . DIRECTORY_SEPARATOR . $params['filePath'];
        } else {
            //上传文件
            $upload = $this->file_upload($_FILES['userfile']);
        }

        $fieldMap = [
            'receipt_code' => '发票代码',
            'receipt_no' => '发票号码',
            'buyer_name' => '购买方',
            'seller_name' => '销售方',
            'admin_remark' => '后台备注',
            'custom_remark' => '客户备注',
            'receipt_remark' => '票面备注',
        ];

        $excelParams = [
            'filePath' => $upload,
            'fieldsMap' => array_flip($fieldMap),
            'ignore' => TRUE
        ];

        $result = ExcelReader::read($excelParams);
        $result = $result[0];

        Log::info('上传excel内容 ', $result, 'internal_invoice');

        (new \Fuel\Service\ReceiptApply())->importInternalInvoiceApply($result);

        Response::json(null, 0, '处理成功');
    }

    /**
     * 导入文件上传
     * <AUTHOR>
     * @since  2015/10/14
     */
    public function file_upload($file)
    {
        set_time_limit(0);
        if (empty($file) || empty($file['name'])) {
            //未选择文件
            throw new \RuntimeException('未选择文件', 2);
        }
        //判断文件类型
        if ($file['name']) {
            $ext = strtolower(trim(substr(strrchr($file['name'], '.'), 1)));
            if ($ext != "xls" && $ext != "xlsx") {
                //文件类型不正确
                throw new \RuntimeException('文件类型不正确', 2);
            }
        }

        $dir = '../tmp/data';
        if (!is_dir($dir)) {
            helper::createDir($dir, 0777);
        }

        //文件上传
        $tmp_name = $file['tmp_name'];
        $newname = $dir . '/import_' . date('m-d-H-i-s') . '.' . $ext;

        if (@copy($tmp_name, $newname)) {
            @unlink($tmp_name);
        } elseif (@move_uploaded_file($tmp_name, $newname)) {
        } elseif (@rename($tmp_name, $newname)) {
        } else {
            //上传文件失败
            return -7;
        }
        @chmod($newname, 0777);

        return $newname;
    }

    /**
     * 搜索 默认搜全部
     * <AUTHOR> Du
     * @since 2016/03/14
     */
    public function getList()
    {
        //输出结果
        $params = helper::filterParams();
        if ($params['org_flag']) {
            $params['orgcode_list'] = $params['org_code'];
            unset($params['org_code']);
        }
        Log::info(var_export($params, TRUE), [], 'zzzz');
        $data = OilReceiptApply::getListForWeb($params)->toArray();

        foreach ($data['data'] as $key => $value) {
            $data['data'][$key]['contact_name'] = $value['oil_contact']['contact_name'];
            $data['data'][$key]['addr_name'] = $value['oil_addr']['name'];
            $data['data'][$key]['addr_region'] = $value['oil_addr']['address'];

        }

        //g7s端获取总计开票和总计条目
        if (!isset($params['_export']) && isset($params['requestFrom']) && $params['requestFrom'] == 'g7s') {
            $group = OilReceiptApply::countReceiptApplyMoney($params);
            $data['receipt_amount_total'] = $group[0]->receipt_amount_total;
            $data['receipt_apply_count'] = $group[0]->receipt_apply_count;
        }
        Response::json($data);
    }

    public function searchDataExport()
    {
        $params = helper::filterParams();
        if ($params['org_flag']) {
            $params['orgcode_list'] = $params['org_code'];
            unset($params['org_code']);
        }
        $data = OilReceiptApply::getListForWeb($params)->toArray();
        Response::json($data);
    }

    /**
     * 删除
     * <AUTHOR> Du
     * @since 2016/03/14
     */
    public function receiptDelete()
    {
        $params = helper::filterParams();
        if (!$params['id']) {
            $data = ['success' => FALSE, 'msg' => '删除失败'];
            echo json_encode($data);
            exit;
        }
        $params['is_del'] = 1;
        $ReceiptDel = OilReceiptApply::edit($params);
        if ($ReceiptDel) {
            $data = ['success' => TRUE, 'msg' => '删除成功'];
            echo json_encode($data);
        } else {
            $data = ['success' => FALSE, 'msg' => '删除失败'];
            echo json_encode($data);
            exit;
        }
    }

    public function manualDetails()
    {
        $data = OilReceiptApply::where('createtime', '>=', '2018-04-06 21:20:00')
            ->whereIn('id', [4023])
            ->where('receipt_status', '!=', -1)
            ->orderBy('id', 'asc')
            ->get()
            ->toArray();

        foreach ($data as $v) {
            Capsule::connection()->beginTransaction();
            try {
                $v['receipt_apply_id'] = $v['id'];
                $v['user_id'] = $v['creator_id'];
                $this->addDetail($v);

                Capsule::connection()->commit();
            } catch (\Exception $e) {
                Capsule::connection()->rollBack();
                Log::error('v---' . var_export($v, TRUE), [strval($e)], 'ApplyError');
            }
        }


        echo 'ok';
    }

    /**
     * 为以后发票申请单,补充sn
     * <AUTHOR>
     * @since 2016/03/14
     */
    public function setReceiptSn()
    {
        $params['_export'] = 1;
        $params['snNull'] = 1;
        $data = OilReceiptApply::getListForWeb($params);
        if (count($data) > 0) {
            foreach ($data as $_item) {
                $sn = \Framework\Helper::uuid();
                OilReceiptApply::edit(['id' => $_item->id, 'sn' => $sn]);
            }
        }
        echo "Success";
    }

    /**
     * 开票申请白名单客户
     * @return void
     */
    public function receiptApplyAdd()
    {
        $params = helper::filterParams();
        //参数基础校验
        Log::info('添加参数--' . var_export($params, TRUE), [], 'receiptApplyAdd');
        helper::argumentCheck([],$params);
        $validator = new Illuminate\Validation\Validator(new Translator('en'), $params, [
            'sn' => 'required',
            //'org_id' => 'required',
            'receipt_type' => 'required',
            'oil_type' => 'required',
            'receipt_operator_id' => 'required',
            'receipt_title_id' => 'required',
            'org_contact_id' => 'required',
            //'org_addr_id' => 'required',
            'pay_company_id' => 'required',
            'trade_time_ge' => 'required',
            'trade_time_le' => 'required',
            'receipt_amount' => 'required',
            'data_from' => 'required',
        ], [
            'sn.required'  => 'sn唯一标识不能为空',
            //'org_id.required'  => '所属机构不能为空',
            'receipt_type.required'  => '开票类型不能为空',
            'oil_type.required'  => '油品种类不能为空',
            'receipt_operator_id.required'  => '销售方不能为空',
            'receipt_title_id.required'  => '发票抬头不能为空',
            'org_contact_id.required'  => '负责人不能为空',
            //'org_addr_id.required'  => '接收人不能为空',
            'pay_company_id.required'  => '发票抬头对应的付款公司不能为空',
            'trade_time_ge.required'  => '申请消费期间开始时间不能为空',
            'trade_time_le.required' => '申请消费期间结束时间不能为空',
            'receipt_amount.required' => '申请金额不能为空',
            'data_from.required' => '来源不能为空',
        ]);
        if ($validator->fails()) {
            Response::json([], 412001, $validator->errors()->first());
        }
        $params['trade_start_time'] = date('Y-m-d H:i:s', strtotime($params['trade_time_ge']));
        $params['trade_end_time'] = date('Y-m-d', strtotime($params['trade_time_le'])).' 23:59:59';
        $params['org_operator_id'] = $params['receipt_operator_id'];
        //参数逻辑校验
        $params = $this->checkInput($params);
        Capsule::connection()->beginTransaction();
        $redisObj = RedisInstance::getInstance();
        try {
            //防止并发,增加sn校验
            $isHas = OilReceiptApply::getBySnForLock(['sn' => $params['sn']]);
            if ($isHas) {
                throw new \RuntimeException("开票已提交,请勿重复提交", 10);
            }

            //由于lockForUpdate不生效，因此改用redis
            if ($redisObj->set($params['sn'], 1, ['nx', 'ex' => 5 * 60]) != 1) {
                throw new \RuntimeException("开票已提交,请勿重复提交", 10);
            }

            //txb g7s 开票申请验证开票抬头和油品类型是否开票
            if ($params['data_from'] == 2) {
                $receiptParams = [
                    'org_code' => $params['orgcode'],
                    'receipt_title_id' => $params['receipt_title_id'],
                    'receipt_statusIn' => [
                        ReceiptApplyStatus::PRE_AUDIT,
                        ReceiptApplyStatus::AUDITED,
                        ReceiptApplyStatus::HANDLING,
                        ReceiptApplyStatus::OPENING,
                        ReceiptApplyStatus::SUCCESS,
                        ReceiptApplyStatus::MAILED
                    ],
                    'apply_time_from' => date('Y-m-01'),
                    'oil_type' => $params['oil_type'] ? $params['oil_type'] : 12,
                ];
                $redisKey = "apply-" . md5(json_encode($receiptParams));

                if (bccomp($params['receipt_amount'], 1000, 2) == -1) {
                    throw new \RuntimeException("最小开票金额为1000元", 11);
                }
            }

            $params['receipt_status'] = 10;
            $insertData = OilReceiptApply::add($params);

            $params['receipt_apply_id'] = $insertData->id;
            $params['no'] = $insertData->no;
            $params['user_id'] = isset($this->app->myAdmin->id) ? $this->app->myAdmin->id : 1;
            $params['last_operator'] = $this->app->myAdmin->true_name ? $this->app->myAdmin->true_name : '';
            $params['receipt_no'] = $insertData->no;

            //记录操作日志
            if ($params['receipt_apply_id']) {
                \Models\OilReceiptApplyLog::add([
                    'app_id' => $params['receipt_apply_id'],
                    'status' => $params['receipt_status'],
                    'last_operator' => $this->app->myAdmin->true_name ? $this->app->myAdmin->true_name : '',
                    'last_operator_id' => $this->app->myAdmin->id ? $this->app->myAdmin->id : '',
                    'createtime' => helper::nowTime(),
                ]);
            }

            if (!in_array(API_ENV, ["pro", "prod"])) {
                $redisObj->del($redisKey);
                $redisObj->del($params['sn']);
            }
            Capsule::connection()->commit();

            Log::error("receiptAddJob",[$params],"addReceiptJob_");
            //异步任务下发
            (new \Jobs\AddReceiptApplyDetailsJob(["receipt_apply_id"=>$params["receipt_apply_id"]]))
                ->setTaskName("生成白名单发票明细")
                ->onQueue("whiteReceiptApplyDetails")
                ->setTries(3)
                ->dispatch();
                
        }catch (\Exception $e)
        {
            Log::error('receiptApplyAdd--' . var_export($params, TRUE), [strval($e)], 'receiptApplyAdd');
            Capsule::connection()->rollBack();
        }

        Response::json($params, 0, '添加成功');
    }

    /**
     * 添加
     * <AUTHOR> Du
     * @since 2016/03/14
     */
    public function receiptAdd()
    {
        Log::info('POST--' . var_export($_POST, TRUE), [], 'receiptAdd');
        $params = helper::filterParams();
        Log::info('添加参数--' . var_export($params, TRUE), [], 'receiptAdd');
        
        if (!isset($params['oil_type']) || empty($params['oil_type'])) {
            throw new \RuntimeException("请选择油品种类", 2);
        }

        if (isset($params['is_split']) && $params['is_split'] == 1 && $params['data_from'] == 2) {
            //拆分汽柴油
            $orgInfo = \Models\OilOrg::getByOrgcode($params['orgcode']);
            $splitAmount = \Fuel\Service\OpenReceipt::getSplitAmount($orgInfo, $params['receipt_amount']);

            $qi_money = $splitAmount['qi_money'];
            $chai_money = $splitAmount['chai_money'];
            if ($qi_money < 10000) {
                throw new \RuntimeException("汽油开票额度不足10000元，不能分类开票", 2);
            }

            if ($chai_money < 10000) {
                throw new \RuntimeException("柴油开票额度不足10000元，不能分类开票", 2);
            }

            $params['oil_type'] = 1;
            $this->receiptAddContent($params);

            $params['oil_type'] = 2;
            $this->receiptAddContent($params);

        } else {
            $params['oil_type'] = $params['oil_type'] ? $params['oil_type'] : 12;
            $this->receiptAddContent($params);
        }

        Response::json($params, 0, '添加成功');
    }

    /*
     * 开票主要逻辑
     */
    public function receiptAddContent($params)
    {
        Capsule::connection()->beginTransaction();
        $redisObj = RedisInstance::getInstance();
        $lockId = null;
        try {
            $params = $this->checkInput($params);
            $lockId = Cache::lock(
                $params['orgcode'] . "_" . $params['oil_type'] . "_" . $params['corp_name'] . "_" .
                $params['seller_taxpayer_no'],
                300
            );
            if(!$lockId){
                throw new \RuntimeException('操作失败！同油品5分钟内只能提交1次，请稍后重试~', 2);
            }
            helper::argumentCheck(['sn'], $params);
            //防止并发,增加sn校验
            $isHas = OilReceiptApply::getBySnForLock(['sn' => $params['sn']]);
            if ($isHas) {
                throw new \RuntimeException("开票已提交,请勿重复提交", 10);
            }

            //由于lockForUpdate不生效，因此改用redis
            if ($redisObj->set($params['sn'], 1, ['nx', 'ex' => 5 * 60]) != 1) {
                throw new \RuntimeException("开票已提交,请勿重复提交", 10);
            }

            //txb g7s 开票申请验证开票抬头和油品类型是否开票
            if ($params['data_from'] == 2) {
                $receiptParams = [
                    'org_code' => $params['orgcode'],
                    'receipt_title_id' => $params['receipt_title_id'],
                    'receipt_statusIn' => [
                        ReceiptApplyStatus::PRE_AUDIT,
                        ReceiptApplyStatus::AUDITED,
                        ReceiptApplyStatus::HANDLING,
                        ReceiptApplyStatus::OPENING,
                        ReceiptApplyStatus::SUCCESS,
                        ReceiptApplyStatus::MAILED
                    ],
                    'apply_time_from' => date('Y-m-01'),
                    'oil_type' => $params['oil_type'] ? $params['oil_type'] : 12,
                ];
                $redisKey = "apply-" . md5(json_encode($receiptParams));
                //由于lockForUpdate不生效，因此改用redis
              /**       if ($redisObj->set($redisKey, 1, ['nx', 'ex' => 5 * 60]) != 1) {
                    throw new \RuntimeException("本月已开票", 10);
                }

                $info = OilReceiptApply::getUniqueTitle($receiptParams);
                if ($info) {
                    throw new \RuntimeException("本月已开票", 10);
                } else {
                    if (bccomp($params['receipt_amount'], 10000, 2) == -1) {
                        throw new \RuntimeException("最小开票金额为10000元", 11);
                    }
                }**/

                if (bccomp($params['receipt_amount'], 1000, 2) == -1) {
                    throw new \RuntimeException("最小开票金额为1000元", 11);
                }
                //G7WALLET-3974
                if( date("Y-m") != '2023-01' ) {
                    //新增加开票时间段限制
                    $startTime = date('Y-m') . '-06 00:00:00';
                    $endTime = date('Y-m') . '-25 23:59:59';
                    if ((strtotime(date('Y-m-d H:i:s')) < strtotime($startTime)) || (strtotime(date('Y-m-d H:i:s')) > strtotime($endTime))) {
                        //throw new \RuntimeException("因月初需要计算返利，开票时间为每月6日0点 - 25日24点。自提交后10个工作日左右开具发票并寄出，为保证当月收到，请尽量于20日前提交", 11);
                    }
                }

            }

            //判断每月最后两工作日15：00：00之后不能提开票申请
//            $lastTwoWorkDay = \Framework\Helper::getLastWeekNo(date('Y'),date('m'),3).' 15:00:00';
//            if(strtotime(date('Y-m-d H:i:s')) > strtotime($lastTwoWorkDay)){
//                throw new \RuntimeException("月初5号后可提交开票申请，如果有疑问请联系在线客服!",2);
//            }

//            if($params['data_from'] == 2) {
//                //临时发票调整
//                if (strtotime(date('Y-m-d H:i:s')) < strtotime('2019-03-26 00:00:00') || strtotime(date('Y-m-d H:i:s')) > strtotime('2019-04-02 00:00:00')) {
//                    throw new \RuntimeException("月初5号后可提交开票申请，如果有疑问请联系在线客服", 2);
//                }
//            }


            $params['receipt_status'] = 10;
            $insertData = OilReceiptApply::add($params);

            $params['receipt_apply_id'] = $insertData->id;
            $params['no'] = $insertData->no;
            $params['user_id'] = isset($this->app->myAdmin->id) ? $this->app->myAdmin->id : 1;
            $params['last_operator'] = $this->app->myAdmin->true_name ? $this->app->myAdmin->true_name : '';
            $params['receipt_no'] = $insertData->no;

            Log::error("receiptAddJob",[$params],"addReceiptJob_");
            //异步任务下发
            $this->jobForAdd($params);

            //记录操作日志
            if ($params['receipt_apply_id']) {
                \Models\OilReceiptApplyLog::add([
                    'app_id' => $params['receipt_apply_id'],
                    'status' => $params['receipt_status'],
                    'last_operator' => $this->app->myAdmin->true_name ? $this->app->myAdmin->true_name : '',
                    'last_operator_id' => $this->app->myAdmin->id ? $this->app->myAdmin->id : '',
                    'createtime' => helper::nowTime(),
                ]);
            }

            if (!in_array(API_ENV, ["pro", "prod"])) {
                $redisObj->del($redisKey);
                $redisObj->del($params['sn']);
            }
            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            if ($lockId) {
                Cache::unlock(
                    $params['orgcode'] . "_" . $params['oil_type'] . "_" . $params['corp_name'] . "_" .
                    $params['seller_taxpayer_no'],
                    $lockId
                );
            }
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        if ($params['receipt_apply_id']) {
            //修改顶级机构首次开票时间
            $orgroot = substr($params['org_code'], 0, 6);
            $orgRootInfo = OilOrg::getByOrgcode($orgroot);
            if ($orgRootInfo && !$orgRootInfo->first_apply_receipt) {
                $orgRootInfo->update(['first_apply_receipt' => helper::nowTime()]);
            }

            try {
                //短信和叮叮提醒
//                \Framework\Sms\NoticeSender::sendAll([
//                    'message' => '亲，有发票【' . $params['no'] . '】申请，请及时处理哦～',
//                ]);
            } catch (Exception $e) {
                Log::error(strval($e), [], 'sendBug');
            }
        }
    }

    /**
     * @title   开票申请前检查机构授信能力
     * @desc
     * @param $params
     * @return mixed|null
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     */
    public function checkOrgCredit($isReturn = FALSE)
    {
        $params = helper::filterParams();
        helper::argumentCheck(['org_id'], $params);
        $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
        if (empty($orgInfo)) {
            throw new \RuntimeException('机构不存在', 2);
        }
        $code = 1;
        $msg = "";
        //校验授信状态是否正常
        $subAccountId = (new \Fuel\Service\AccountGredit())->getSubAccountIdByOrgcode($orgInfo->orgcode);

        Log::error('$subAccountId' . $subAccountId, [], 'checkCredit');

//        if(in_array($orgInfo->orgcode,['200YP0','200VLO'])){
//            $subAccountId = NULL;
//        }

        if ($subAccountId) {

            //2018.10.11 任务：YP-3548，foss端停用可提
            /*$credit = \Models\OilCreditAccount::getBySubAccountNoForLock(['subAccountID'=>$subAccountId]);
            if($credit->status == 20){
                throw new \RuntimeException('授信账户已停用', -2);
            }*/
            $code = 1;

            //首先对信用状态校验
            $creditInfo = (new \Fuel\Service\AccountCenter\AccountService())->getAccountDetail(['subAccountID' => $subAccountId]);
            //$creditInfo->creditGlpInfo->creditGlpStatus = 'FROZEN';
            if (!$creditInfo) {
                //throw new \RuntimeException('信用账户状态异常，请联系客服人员', -5);
                $code = -2;
            } else {
                if (!$creditInfo->creditGlpInfo) {
                    //throw new \RuntimeException('信用账户状态异常，请联系客服人员', -4);
                    $code = -2;
                }
            }

            $noPayNum = \Models\OilCreditBill::getLastMonthByOrgId(['org_id' => $params['org_id']]);
            Log::error('$noPayNum' . $noPayNum, [], 'checkCredit');
            if ($noPayNum && $noPayNum > 0) {
                throw new \RuntimeException('当前有未结清账单，不能开票', -1);
            }

            //todo 动力宝非正常
            //todo foss非停用    2018.10.11 任务：YP-3548，foss端停用可提
            //todo 无未还的信用账单
            if ($creditInfo && $creditInfo->creditGlpInfo && $creditInfo->creditGlpInfo->creditGlpStatus != 'NORMAL' && $noPayNum <= 0) {
                $code = -2;
                $msg = "";
            }
        }
        if ($isReturn) {
            return TRUE;
        } else {
            Response::json($code, 0, $msg);
        }
    }

    public function manualCreateJobForAdd()
    {
        $params = helper::filterParams();

        $receiptInfo = OilReceiptApply::where('no', $params['no'])->first();
    }

    /**
     * @title   添加申请单异步任务下发
     * @desc
     * @param $params
     * @return mixed|null
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    private function jobForAdd($params)
    {
        global $app;
        if ( $app->config->scheduler->switch == 1 ) {
            $params['receiptJobType'] = 1;
            return (new \Jobs\ReceiptApplyJob($params))
                ->setTaskName('添加开票明细')
                ->setUserInfo($this->app->myAdmin)
                ->onQueue('addreceiptapplydetails')
                ->setTries(3)
                ->dispatch();
        }else {

            return (new Job())
                ->setTaskName('添加开票明细')
                ->pushTask(function () use ($params) {
                    require_once APP_MODULE_ROOT . '/oil_receipt_apply/control.php';

                    Capsule::connection()->beginTransaction();
                    try {
                        $selfObj = new oil_receipt_Apply();

                        Log::error("jobForAdd",[$params],"addReceiptJob_");

                        //添加明细
                        $result = $selfObj->addDetail($params);

//                    $receiptStatus = $result ? \Fuel\Defines\ReceiptApplyStatus::PRE_AUDIT : ReceiptApplyStatus::REJECT;
                        $receiptStatus = \Fuel\Defines\ReceiptApplyStatus::PRE_AUDIT;

                        //记录操作日志
                        \Models\OilReceiptApplyLog::add([
                            'app_id' => $params['receipt_apply_id'],
                            'status' => $receiptStatus,
                            'last_operator' => $params['last_operator'],
                            'last_operator_id' => $params['user_id'],
                            'createtime' => helper::nowTime(),
                        ]);

                        OilReceiptApply::edit(['id' => $params['receipt_apply_id'], 'receipt_status' => $receiptStatus]);

                        Capsule::connection()->commit();
                        OilReceiptApplyDetails::checkAndUpdateApplyToFetchAmountDiffByApplyId(
                            $params['receipt_apply_id'],
                            $params['receipt_amount']
                        );
                        ReceiptApplyInternal::receiptMonitor($params['receipt_apply_id'], 2);

                    } catch (Exception $e) {
                        Capsule::connection()->rollBack();

                        $title = $params['no'] . '发票预处理异常;' . $e->getMessage();
                        $content[] = "* 标题：" . $title . "\n";
                        $content[] = "* 环境：" . API_ENV . "\n";
                        $content[] = "* 单号：" . $params['no'] . "\n";
                        $content[] = "* 描述：" . strval($e) . "\n";

                        (new \Framework\DingTalk\DingTalkAlarm())
                            ->alarmToGroup($params['no'] . '发票预处理异常;' . $e->getMessage(), implode("", $content), [], TRUE);

                        Log::error('添加时处理明细异常:' . $e->getCode() . '--' . $e->getMessage() . '|exception:' . strval($e), [], 'receiptApplyError');
                        throw new \RuntimeException($e->getMessage(), 2);
                    }
                })
                ->channel('addreceiptapplydetails')
                ->exec();
        }
    }

    /**
     * @title 表单校验
     * @desc
     * @param array $params
     * @return array
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     */
    private function checkInput(array $params)
    {
        $params['data_from'] = $params['data_from'] == 2 ? 2 : 1;
        if (!isset($params['receipt_amount']) || !$params['receipt_amount']) {
            throw new \RuntimeException('开票金额不能为空', 2);
        }

        if ($params['receipt_amount'] <= 0) {
            throw new \RuntimeException('开票金额不能小于等于零', 2);
        }

        if (!$params['receipt_title_id']) {
            throw new \RuntimeException('发票抬头不能为空', 2);
        }
        if (!$params['org_contact_id']) {
            throw new \RuntimeException('负责人不能为空', 2);
        }
        if (!$params['org_addr_id'] && $params['receipt_type'] != '数电专票') {
            throw new \RuntimeException('接收人不能为空', 2);
        }
        Log::error('$noPayNum-params', [$params], 'checkInput');
        //校验机构号是否有效
        $receipt_mode = 1;
        if (!isset($params['org_id']) || !$params['org_id']) {
            if (!isset($params['orgcode']) || !$params['orgcode']) {
                throw new \RuntimeException('无机构信息', 2);
            } else {
                $Info = \Models\OilOrg::getByOrgcode($params['orgcode']);
                if (!$Info) {
                    throw new \RuntimeException('机构号无效', 2);
                } else {
                    $params['org_id'] = $Info->id;
                    $params['org_code'] = $Info->orgcode;
                    $params['org_name'] = $Info->org_name;
                    $params['is_recepit_nowtime'] = $Info->is_recepit_nowtime;
                    $receipt_mode = $Info->receipt_mode == 2 ? 2 : 1;
                    $seller_id = isset($params['receipt_operator_id']) && !empty($params['receipt_operator_id']) ? $params['receipt_operator_id'] : $Info->operators_id;
                    //lp发票申请添加销售方相关信息
                    $operator = \Models\OilOperators::getById(['id' => $seller_id]);
                    if (!$operator) {
                        throw new \RuntimeException('销售方不存在');
                    }
                    $params['seller_name'] = $operator->company_name;
                    $params['seller_taxpayer_no'] = $operator->taxpayer_no;
                    $params['seller_company_code'] = $operator->company_code;
                    $params['org_operator_id'] = $seller_id;
                }
            }
        } else {
            $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
            $params['org_code'] = $orgInfo->orgcode;
            $params['org_name'] = $orgInfo->org_name;
            $params['is_recepit_nowtime'] = $orgInfo->is_recepit_nowtime;
            $receipt_mode = $orgInfo->receipt_mode == 2 ? 2 : 1;
            $seller_id = isset($params['receipt_operator_id']) && !empty($params['receipt_operator_id']) ? $params['receipt_operator_id'] : $orgInfo->operators_id;
            //lp发票申请添加销售方相关信息
            $operator = \Models\OilOperators::getById(['id' => $seller_id]);
            if (!$operator) {
                throw new \RuntimeException('销售方不存在');
            }
            $params['seller_name'] = $operator->company_name;
            $params['seller_taxpayer_no'] = $operator->taxpayer_no;
            $params['seller_company_code'] = $operator->company_code;
            $params['org_operator_id'] = $seller_id;
        }

        $params['is_direct_dl'] = $params['org_operator_id'] == 2 ? 1 : 2;
        $change = \Fuel\Service\OrgChangeOperatorService::getOrgChangeData(['org_code'=>$params['org_code']]);
        if( count($change['changeList']) == 0 ){
            unset($params['receipt_operator_id']);
        }else{
            $isEndTime = "";
            foreach ($change['changeList'] as $_item){
                if($_item->down_receipt_status == 2){
                    $isEndTime = $_item->change_time;
                    break;
                }
            }

            if(isset($params['trade_end_time']) && !empty($params['trade_end_time']) && !empty($isEndTime)){
                if( strtotime($params['trade_end_time']) > strtotime($isEndTime) ){
                    throw new \RuntimeException('消费截止日期不能大于预约换签时间', 2);
                }
            }

            if(!empty($isEndTime)) {
                $params['sign_lock_time'] = $isEndTime;
            }
        }

        Log::error('params', [$params], 'checkInput');

        $info = OilReceiptTitle::getById(['id' => $params['receipt_title_id']]);

        //校验是否合同签约
        $pay_company = $this->checkContract($info->pay_company_id);

        if (!$info || $info->status != 1 || $info->is_del == 1) {
            throw new \RuntimeException('发票抬头不可用', 2);
        }

        $params['receipt_type'] = $info->receipt_type;
        $params['pay_company_id'] = $info->pay_company_id;
        $params['corp_name'] = $info->corp_name;
        $params['pay_company_name'] = $pay_company->company_name;

        if (!$params['receipt_type']) {
            throw new \RuntimeException('发票类型不能为空', 2);
        }

        //校验授信状态是否正常
        $subAccountId = (new \Fuel\Service\AccountGredit())->getSubAccountIdByOrgcode($params['org_code']);

//        if(in_array($params['org_code'],['200YP0','200VLO'])){
//            $subAccountId = NULL;
//        }

        Log::error('$subAccountId' . $subAccountId, [], 'checkInput');
        if ($subAccountId) {
            if ($params['data_from'] == 1) {
                $this->checkOrgCredit(TRUE);
            } else {
                //首先对信用状态校验
                $creditInfo = (new \Fuel\Service\AccountCenter\AccountService())->getAccountDetail(['subAccountID' => $subAccountId]);
                if ($creditInfo) {
                    if ($creditInfo->creditGlpInfo && $creditInfo->creditGlpInfo->creditGlpStatus == 'OVERDUE') {
                        throw new \RuntimeException('信用账户状态异常，请联系客服人员', 2);
                    }
                }

                //todo 暂时注释5月1之前打开
                Log::error('params2', [$params], 'checkInput');
                //校验授信账单是否有未还款（上月末之前）
                Log::error('$noPayNum-params', ['orgcode' => $params['org_code']], 'checkInput');

                $noPayNum = \Models\OilCreditBill::getLastMonthByOrgId(['org_id' => $params['org_id'], '']); //todo 后期调整
                Log::error('$noPayNum' . $noPayNum, [], 'checkInput');
                if ($noPayNum && $noPayNum > 0 && isset($params['data_from']) && $params['data_from'] == 2) {
                    throw new \RuntimeException('当前有未结清账单，不能开票', 2);
                }
            }
        }

        //获取可开票金额
//        $max_amount = OpenReceipt::maxAmount($params);
//        if ($params['receipt_amount'] > $max_amount) {
//            throw new \RuntimeException('开票金额超出限制', 2);
//        }
        //txb 2018.11.13 增加独立核算方式
        /*if($receipt_mode == 1) {
            $max_amount = OpenReceipt::maxAmountForFoss($params);
        }else{
            $max_amount = OpenReceipt::getReceiptAllFee($params);
        }*/
        $params['oil_type'] = $params['oil_type'] ? $params['oil_type'] : 12;

        //判断是否是先开后结
        $conf = (new \Fuel\Service\OrgConfigService())->getOrgRebateUseSetting($orgInfo->orgcode); // 标用一体
        $bigConf = (new \Fuel\Service\OrgConfigService())->getBigCustomerSetting($orgInfo->orgcode); // 先开后结
        if(isset($params['is_new_mode']) && $params['is_new_mode'] == 1){
            if(isset($params['customer_type']) && $params['customer_type'] == 2){
                $mayOpenEndTime = date('Y-m-d',strtotime('-1 day')).' 23:59:59';
            }else{
                $mayOpenEndTime = $params['is_recepit_nowtime'] == 2  ? date('Y-m-d',strtotime('-1 day')).' 23:59:59' : date('Y-m-t', strtotime('first day of last month')).' 23:59:59';
            }
            if(strtotime($mayOpenEndTime) < strtotime($params['trade_end_time'])){
                throw new \RuntimeException('结束时间不合法', 2);
            }
            $oil_type = $params['oil_type'];
            if( empty($oil_type) || in_array($oil_type,[1,2]) ){
                $oil_type = 12;
            }

            $max_amount['max_amount'] = OpenWhiteReceipt::getOrgQuotaAmount([
                'unset_base_id' => 1,
                'org_id' => $params['org_id'],
                'org_operator_id' => $params['org_operator_id'],
                'oil_type' => $oil_type,
                'receipt_title_id' => $params['receipt_title_id'],
                //'trade_end_time' =>  $params['trade_end_time'] ? date('Y-m-d',strtotime('+1 day',strtotime($params['trade_end_time']))).' 00:00:00' : ''
            ]);
            
        }else{
            if (isset($conf['equal_use']) && $conf['equal_use'] == 1 && isset($bigConf['pre_open_receipt']) && $bigConf['pre_open_receipt'] == 1) {
                if (!$params['trade_end_time']) {
                    throw new \RuntimeException('消费截止日期不能为空', 2);
                }
                $max_amount = OpenReceipt::getMaxReceiptAmountForYT($params);
            } else {
                if (!$params['apply_time']) {
                    throw new \RuntimeException('申请时间不能为空', 2);
                }
                $max_amount = OpenReceipt::getUseChargeByCompany($params);
            }
        }
        //由于获取可用开票时加上了，当前开票额度，需要减后在进行验证
        if (isset($params['id']) && !empty($params['id'])) {
            $info = OilReceiptApply::getByIdForLock(['id' => $params['id']]);
            if ($info->receipt_title_id != $params['receipt_title_id']) {
                $use_balace = $max_amount['max_amount'] - $params['receipt_amount'];
            } else {
                $use_balace = $max_amount['max_amount'];
            }
        } else {
            $use_balace = $max_amount['max_amount'];
        }

        if (bccomp($params['receipt_amount'], $use_balace, 2) > 0) {
            throw new \RuntimeException('开票金额超出限制', 2);
        }

        return $params;
    }

    /*
     * 校验合同签约
     */
    public function checkContract($pay_company_id)
    {
        $contractOnOff = \Fuel\Defines\ReceiptConfig::$is_open_check_contract;

        $pay_company_info = \Models\OilPayCompany::getById(['id' => $pay_company_id]);

        if ($contractOnOff) {
            if ($pay_company_info && $pay_company_id->is_check_contract == 10) {
                if (!$pay_company_info->contract_id) {
                    throw new \RuntimeException('此付款公司未签约合同档案', 2);
                }

                //查找合同档案信息
                $contract_info = \Models\OilContract::getById(['id' => $pay_company_info->contract_id]);

                if (!$contract_info) {
                    throw new \RuntimeException('合同档案已不存在，请联系您的对接人', 2);
                }

                if ($contract_info->status != 10) {
                    throw new \RuntimeException('合同已中止无法开票，请联系您的对接人', 2);
                }

                if ($contract_info->archive_status == 10) {
                    throw new \RuntimeException('合同未归档无法开票，请联系您的对接人。', 2);
                }
            }
        }
        return $pay_company_info;
    }

    /*
     * 根据发票抬头校验合同是否签约
     */
    public function checkContractForTitle()
    {
        $params = helper::filterParams();
        \helper::argumentCheck(['receipt_title_id'], $params);

        //合同签约校验
        $receipt_title = OilReceiptTitle::getById(['id' => $params['receipt_title_id']]);
        $this->checkContract($receipt_title->pay_company_id);

        Response::json('', 0, '更改成功');
    }

    /*
     * 根据付款公司校验合同是否签约
     */
    public function checkContractForAudit()
    {
        $params = helper::filterParams();

        if (empty($params['pay_company_ids'])) {
            Response::json('', 0, '更改成功');
        }

        \helper::argumentCheck(['pay_company_ids'], $params);

        //合同签约校验
        $pay_company_id_arr = explode(',', $params['pay_company_ids']);
        if ($pay_company_id_arr) {
            foreach ($pay_company_id_arr as $pay_company_id) {
                $this->checkContract($pay_company_id);
            }
        }

        Response::json('', 0, '更改成功');
    }

    /**
     * @title 添加明细
     * @desc
     * @param array $params
     * @return array
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     */
    public function addDetail(array $params)
    {
        Log::error('addDetail-params--' . var_export($params, TRUE), [], 'addReceiptApplyDetails');

        $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
        //$childOrgIds = OilOrg::getByOrgcodeLike(substr($orgInfo->orgcode,0,6));
        $pageSize = 1500;
        $receiptAmount = $params['receipt_amount'];//开票金额
        $searchParams = [
            //            'org_id_list' => $childOrgIds,
            'orgcode' => substr($orgInfo->orgcode, 0, 6),
            #'is_fanli'     => 1,
            'createtimeLne' => $orgInfo->is_recepit_nowtime == 2
                ? date('Y-m-d H:i:s')
                //: date('Y-m-t', strtotime('-1 month')) . ' 23:59:59',
                : date('Y-m-01') . ' 00:00:00',
            'receipt_operator_id' => isset($params['receipt_operator_id']) && $params['receipt_operator_id'] ? $params['receipt_operator_id'] : '',
            'sign_lock_time' => isset($params['sign_lock_time']) && $params['sign_lock_time'] ? $params['sign_lock_time'] : '',
        ];

        $receipt_info = OilReceiptApply::getById(['id' => $params['receipt_apply_id']]);
        if ($receipt_info->trade_end_time && $receipt_info->customer_type == 2) {
            $searchParams['createtimeLne'] = $receipt_info->trade_end_time;
        }

        if ($orgInfo->receipt_mode == 2) {
            $searchParams['orgcode'] = $orgInfo->orgcode;
            #$searchParams['is_fanli'] = 1;
        }

        if (isset($params['oil_type'])) {
            switch ($params['oil_type']) {
                case \Fuel\Defines\OilType::GAS_YOU:
                    $searchParams['oil_type_eq'] = \Fuel\Defines\OilType::GAS_YOU;
                    $searchParams['oil_comIn'] = \Fuel\Defines\OilCom::getAllFirstList();
                    array_push($searchParams['oil_comIn'], 2);//中石油的加油卡，支持天然气品类的开票
                    break;
                case \Fuel\Defines\OilType::UREA_YOU:
                    $searchParams['oil_type_eq'] = \Fuel\Defines\OilType::UREA_YOU;
                    $searchParams['oil_comIn'] = \Fuel\Defines\OilCom::getAllFirstList();
                    break;
                case \Fuel\Defines\OilType::QI_YOU:
                    $searchParams['oil_type_eq'] = \Fuel\Defines\OilType::QI_YOU;
                    break;
                case \Fuel\Defines\OilType::CHAI_YOU:
                    $searchParams['oil_type_eq'] = \Fuel\Defines\OilType::CHAI_YOU;
                    break;
                default:
                    $searchParams['oil_type_list'] = [\Fuel\Defines\OilType::QI_YOU, \Fuel\Defines\OilType::CHAI_YOU];
            }
        }

        if(isset($params['trade_end_time']) && !empty($params['trade_end_time'])) {
            $searchParams['createtimeLne'] = $params['trade_end_time'];
        }

        $use_fee = 0;
        $left_fee = 0;
        $sqlArr = [];
        for ($pageNo = 0; $pageNo < 1000000; $pageNo++) {
            Log::error('$params--' . var_export($params, TRUE), [], 'addReceiptApplyDetails');
            Log::error('$receiptAmount--' . $receiptAmount, [], 'addReceiptApplyDetails');
            if (bccomp($receiptAmount, 0, 2) === 0) {
                break;
            }

            $searchParams['take'] = $pageSize;
            //获取需要开票的消费记录:开票方式 1:集中，2:独立
            if ($orgInfo->receipt_mode == 2) {
                $searchParams['orgCodeNow'] = $orgInfo->orgcode;
                unset($searchParams['orgcode']);
                $viceTradesInfo = OilCardViceTrades::getListForOpenReceiptForOnly($searchParams);
                Log::error('独立核算count--' . count($viceTradesInfo), [], 'addReceiptApplyDetails');
                if (count($viceTradesInfo) == 0) {
                    break;
                }
                $res = (new OpenReceipt())->writeBackRecord($viceTradesInfo, $params, $receiptAmount - $use_fee);
                if ($res < 0) {
                    return FALSE;
                }
                $use_fee += $res;
            } else {
                $left_fee = 0;
                $viceTradesInfo = OilCardViceTrades::getListForOpenReceipt($searchParams);
                Log::error('count--' . count($viceTradesInfo), [], 'addReceiptApplyDetails');
                if (count($viceTradesInfo) == 0) {
                    Log::error('未找到消费明细---' . var_export($receiptAmount, TRUE) . '|' . var_export($params, TRUE), [], 'ApplyError');
                    return FALSE;
                }
                $backRes = (new OpenReceipt())->writeBackRecord($viceTradesInfo, $params, $receiptAmount - $use_fee);
                if ($backRes < 0) {
                    return FALSE;
                }
                $use_fee += $backRes;
            }

        }
        $left_fee = $receiptAmount - $use_fee;
        Log::error('独立核算剩余金额--' . $left_fee . "，已用金额" . $use_fee . ",开票金额" . $receiptAmount, [], 'addReceiptApplyDetails');
        global $app;
        if ($left_fee > 0 && $orgInfo->receipt_mode == 2) {
            //处理独立核算子级机构的消费记录
            for ($pageNo = 0; $pageNo < 1000000; $pageNo++) {
                if (bccomp($left_fee, 0, 2) === 0) {
                    break;
                }
                $searchParams['orgcode'] = $orgInfo->orgcode;
                $searchParams['notOrgCode'] = $orgInfo->orgcode;
                unset($searchParams['orgCodeNow']);
                $viceTradesInfo = OilCardViceTrades::getListForOpenReceiptForOnly($searchParams, "oil_card_vice_trades.id");
                if (count($viceTradesInfo) == 0) {
                    Log::error('未找到消费明细---' . var_export($receiptAmount, TRUE) . '|' . var_export($params, TRUE), [], 'ApplyError');
                    if ($app->config->receipt->modeSwitch == 1 && count($app->config->receipt->modeOrgList) > 0 &&
                        in_array($orgInfo->orgcode, array_keys($app->config->receipt->modeOrgList))) {
                        break;
                    } else {
                        return FALSE;
                    }
                }
                $subRes = (new OpenReceipt())->writeBackRecord($viceTradesInfo, $params, $left_fee);
                if ($subRes < 0) {
                    return FALSE;
                }
                $left_fee = $left_fee - $subRes;
                if ($left_fee <= 0) {
                    return FALSE;
                }
            }
            //由集中开票改成独立开票后，机构捞取消费不够，支持占用其他机构消费
            //G7WALLET-415 针对：200D8X,可以捞取其它机构消费
            if ($app->config->receipt->modeSwitch == 1 && count($app->config->receipt->modeOrgList) > 0 &&
                in_array($orgInfo->orgcode, array_keys($app->config->receipt->modeOrgList)) && $left_fee > 0) {
                for ($pageNo = 0; $pageNo < 1000000; $pageNo++) {
                    if (bccomp($left_fee, 0, 2) === 0) {
                        break;
                    }
                    $useOrg = isset($app->config->receipt->modeOrgList[$orgInfo->orgcode]) ? $app->config->receipt->modeOrgList[$orgInfo->orgcode] : "";
                    if (empty($useOrg)) {
                        return FALSE;
                    }
                    unset($searchParams['notOrgCode']);
                    unset($searchParams['orgcode']);
                    if( is_array($useOrg) ){
                        $searchParams['orgCodeNowIn'] = $useOrg;
                    }else {
                        $searchParams['orgCodeNow'] = $useOrg;
                    }
                    Log::error('博通美达捞取消费---' . var_export($left_fee, TRUE) . '|' . var_export($searchParams, TRUE), [], 'ApplyError');
                    $viceTradesInfoOther = OilCardViceTrades::getListForOpenReceiptForOnly($searchParams, "oil_card_vice_trades.id");
                    if (count($viceTradesInfoOther) == 0) {
                        Log::error('未找到消费明细---' . var_export($receiptAmount, TRUE) . '|' . var_export($params, TRUE), [], 'ApplyError');
                        return FALSE;
                    }
                    $subRes = (new OpenReceipt())->writeBackRecord($viceTradesInfoOther, $params, $left_fee);
                    if ($subRes < 0) {
                        return FALSE;
                    }
                    $left_fee = $left_fee - $subRes;
                    if ($left_fee <= 0) {
                        return FALSE;
                    }
                }
            }

        }

        return $sqlArr;
    }


    /**
     * 编辑
     * <AUTHOR> Du
     * @since 2016/03/16
     */
    public function receiptEdit()
    {
        $params = helper::filterParams();
        Log::info('修改参数－－' . var_export($params, TRUE), [], 'receiptApply');

        Capsule::connection()->beginTransaction();
        try {
            $info = OilReceiptApply::getByIdForLock(['id' => $params['id']]);
            if (!$info) {
                throw new \RuntimeException('发票信息不存在 ', 2);
            }
            $params['apply_time'] = $info->apply_time;

            if ($info->receipt_status == \Fuel\Defines\ReceiptApplyStatus::HANDLING) {
                throw new \RuntimeException('当前申请单为处理中不能进行修改操作 ', 2);
            }

            //是否内部票(2:外部票; 1:内部票)
            if ($info->is_internal == \Fuel\Defines\ReceiptScope::EXTERNAL) {
                $this->editExternalApply($info, $params);
            } else {
                (new \Fuel\Service\ReceiptApplyInternal())->editInternalApply($params, $this->app->myAdmin);
            }

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException('修改失败:' . $e->getMessage(), 2);
        }

        Response::json('', 0, '更改成功');
    }

    /**
     * 外部票申请编辑
     * @param $info OilReceiptApply
     * @param $params
     */
    protected function editExternalApply($info, $params)
    {
        //ENINET-4824
        if ((isset($params['receipt_type']) && $info->receipt_type != trim($params['receipt_type'])) || (isset($params['oil_type']) && $info->oil_type != trim($params['oil_type']))) {
            throw new \RuntimeException('发票工单编辑时，禁止修改开票类型及油品种类 ', 2);
        }

        $params['receipt_no'] = $info->no;

        //只允许修改备注
        OilReceiptApply::edit(
            [
                'id' => $params['id'],
                'custom_remark' => $params['custom_remark'],
                'admin_remark' => $params['admin_remark'],
                'last_operator' => $this->app->myAdmin->true_name ? $this->app->myAdmin->true_name : '',
            ]
        );

//        if ($info->receipt_status != \Fuel\Defines\ReceiptApplyStatus::PRE_AUDIT) {
//            //只允许修改备注
//            OilReceiptApply::edit(
//                [
//                    'id' => $params['id'],
//                    'custom_remark' => $params['custom_remark'],
//                    'admin_remark' => $params['admin_remark'],
//                    'last_operator' => $this->app->myAdmin->true_name ? $this->app->myAdmin->true_name : '',
//                ]
//            );
//        } elseif ($info->receipt_status == \Fuel\Defines\ReceiptApplyStatus::PRE_AUDIT) {
//            $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
//            if ($orgInfo) {
//                $params['org_code'] = $orgInfo->orgcode;
//                $params['org_name'] = $orgInfo->org_name;
//            }
//            $params['user_id'] = isset($this->app->myAdmin->id) ? $this->app->myAdmin->id : 1;
//            $params['last_operator'] = $this->app->myAdmin->true_name ? $this->app->myAdmin->true_name : '';
//
//            //判断是否有金额与开票主体变动
//            $flag = $info->org_id == $params['org_id'] && $info->receipt_title_id == $params['receipt_title_id'] && $info->receipt_amount == $params['receipt_amount'];
//
//            $operatorInfo = \Models\OilOperators::getByFilter(['taxpayer_no'=>$info->seller_taxpayer_no]);
//            $seller_id = $operatorInfo && isset($operatorInfo->id) ? $operatorInfo->id : '';
//
//            /*if($seller_id != $params['receipt_operator_id']){
//                $flag = false;
//            }*/
//
//            $params['receipt_operator_id'] = $seller_id;
//
//
//            if (!$flag) {//变动了发票金额需重新进行计算与明细
//                //G7WALLET-1062 //不能更改油品种类
//                $params['oil_type'] = $info->oil_type;
//                $params['receipt_type'] = $info->receipt_type;
//                $params['customer_type'] = $info->customer_type;
//                $params = $this->checkInput($params);//表单校验
//                $params['receipt_status'] = \Fuel\Defines\ReceiptApplyStatus::HANDLING;
//                unset($params['data_from']); //保证发票来源不变
//                OilReceiptApply::edit($params);
//
//                //记录操作日志
//                \Models\OilReceiptApplyLog::add([
//                    'app_id' => $params['id'],
//                    'status' => $params['receipt_status'],
//                    'last_operator' => $this->app->myAdmin->true_name ? $this->app->myAdmin->true_name : '',
//                    'last_operator_id' => $this->app->myAdmin->id ? $this->app->myAdmin->id : '',
//                    'createtime' => helper::nowTime(),
//                ]);
//
//                Log::error("receiptEditJob",[$params],"addReceiptJob_");
//                //下发异步任务
//                $this->jobForEdit($params);
//            } else {
//                OilReceiptApply::edit($params);
//            }
//        }
    }

    /**
     * @title   工单编辑-异步任务下发（恢复额度，同时进行新的冻结）
     * @desc
     * @param $params
     * @returns
     * []
     * @returns
     * <AUTHOR> @since
     * @params  type filedName required?
     * @version
     * @level 1
     */
    private function jobForEdit($params)
    {
        global $app;
        if ( $app->config->scheduler->switch == 1 ){
            $params['receiptJobType'] = 2;
            return (new \Jobs\ReceiptApplyJob($params))
                ->setTaskName('编辑开票明细')
                ->setUserInfo($this->app->myAdmin)
                ->onQueue('addreceiptapplydetails')
                ->setTries(3)
                ->dispatch();
        }else {
            (new Job())
                ->setTaskName('添加开票明细')
                ->pushTask(function () use ($params) {
                    require_once APP_MODULE_ROOT . '/oil_receipt_apply/control.php';
                    Capsule::connection()->beginTransaction();
                    try {
                        $receiptStatus = \Fuel\Defines\ReceiptApplyStatus::PRE_AUDIT;

                        Log::error("jobForEdit",[$params],"addReceiptJob_");
                        $selfObj = new oil_receipt_Apply();
                        //恢复额度
                        $selfObj->recoveryQuota($params['id'],$params['receipt_no']);

                        //删除明细
                        OilReceiptApplyDetails::removeByReceiptApplyId($params['id']);


                        $params['receipt_apply_id'] = $params['id'];

                        //添加明细
                        $selfObj->addDetail($params);
                        OilReceiptApply::edit(['id' => $params['receipt_apply_id'], 'receipt_status' => $receiptStatus]);

                        //记录操作日志
                        \Models\OilReceiptApplyLog::add([
                            'app_id' => $params['receipt_apply_id'],
                            'status' => $receiptStatus,
                            'last_operator' => $params['last_operator'],
                            'last_operator_id' => $params['user_id'],
                            'createtime' => helper::nowTime(),
                        ]);

                        Capsule::connection()->commit();

                        ReceiptApplyInternal::receiptMonitor($params['receipt_apply_id'],2);

                    } catch (Exception $e) {
                        Capsule::connection()->rollBack();

                        $title = $params['no'] . '发票预处理异常;' . $e->getMessage();
                        $content[] = "标题：" . $title . "\n";
                        $content[] = "环境：" . API_ENV . "\n";
                        $content[] = "单号：" . $params['no'] . "\n";
                        $content[] = "描述：> " . strval($e) . "\n";

                        (new \Framework\DingTalk\DingTalkAlarm())
                            ->alarmToGroup($params['no'] . '发票预处理异常;' . $e->getMessage(), implode("", $content), [], TRUE);

                        Log::error('作废时处理明细异常:' . $e->getCode() . '--' . $e->getMessage() . '|exception:' . strval($e), [], 'receiptApplyError');

                        throw new \RuntimeException($e->getMessage(), 2);
                    }
                })
                ->channel('addreceiptapplydetails')
                ->exec();
        }
    }

    /**
     * @title   作废-异步任务下发
     * @desc
     * @param $params
     * @returns
     * []
     * @returns
     * <AUTHOR> @since
     * @params  type filedName required?
     * @version
     * @level 1
     */
    private function jobForReject($params)
    {

        global $app;
        if ( $app->config->scheduler->switch == 1 ) {
            $params['receiptJobType'] = 3;
            return (new \Jobs\ReceiptApplyJob($params))
                ->setTaskName('作废开票明细')
                ->setUserInfo($this->app->myAdmin)
                ->onQueue('addreceiptapplydetails')
                ->setTries(3)
                ->dispatch();
        }else {

            //恢复额度
            (new Job())
                ->setTaskName('添加开票明细')
                ->pushTask(function () use ($params) {
                    require_once APP_MODULE_ROOT . '/oil_receipt_apply/control.php';

                    Capsule::connection()->beginTransaction();
                    try {
                        $receiptStatus = \Fuel\Defines\ReceiptApplyStatus::REJECT;
                        $selfObj = new oil_receipt_Apply();

                        if ($params['is_internal'] == \Fuel\Defines\ReceiptScope::EXTERNAL) {
                            $selfObj->recoveryQuota($params['id'],$params['receipt_no']);
                        } else {
                            $selfObj->recoverTranslateDetailTransform($params['id']);
                        }

                        OilReceiptApply::edit(
                            [
                                'id' => $params['id'],
                                'receipt_status' => $receiptStatus,
                                'last_operator' => $params['last_operator']
                            ]
                        );
                        //记录操作日志
                        \Models\OilReceiptApplyLog::add([
                            'app_id' => $params['id'],
                            'status' => $receiptStatus,
                            'last_operator' => $params['last_operator'],
                            'last_operator_id' => $params['user_id'],
                            'createtime' => helper::nowTime(),
                        ]);

                        Capsule::connection()->commit();
                    } catch (Exception $e) {
                        Capsule::connection()->rollBack();
                        Log::error('作废时处理明细异常:' . $e->getCode() . '--' . $e->getMessage(), [], 'receiptApply');

                        throw new \RuntimeException($e->getMessage(), 2);
                    }
                })
                ->channel('addreceiptapplydetails')
                ->exec();
        }
    }

    /**
     * @title 审核
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     */
    public function auditBy()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);

        Capsule::connection()->beginTransaction();
        try {
            $idsArr = explode(',', $params['id']);

            $list = OilReceiptApply::Filter(['id' => $idsArr])->get();

            $error = [];
            $allIds = $outerIds = $innerIds = [];
            foreach ($list as $item) {
                if ($item->receipt_status != ReceiptApplyStatus::PRE_AUDIT) {
                    throw new \RuntimeException('存在非待审核状态工单', 2);
                }
                $allIds[] = $item->id;

                //G7WALLET-6692
                $detail_num = OilReceiptApplyDetails::getApplyDetailsNum(['receipt_apply_id' => $item->id]);
                if($detail_num <= 0){
                    $error[] = $item->no;
                }

                if ($item->is_internal == \Fuel\Defines\ReceiptScope::INTERNAL){
                    $innerIds[] = $item->id;
                    continue;
                }

                $outerIds[] = $item->id;
            }

            if (!empty($outerIds)) {
                //增加开票额度和开票明细额度是否一致
                (new \Fuel\Service\ReceiptApply())->checkTotalMoney($outerIds);
            }
            //G7WALLET-5541
            if(count($innerIds) > 0){
                (new \Fuel\Service\ReceiptApply())->checkTotalNum($innerIds);
            }

            if(count($error) > 0){
                throw new \RuntimeException('操作失败，以下申请单未捞取到消费：<br/>'. implode('<br/>', $error), 2);
            }

            foreach ($allIds as $v) {
                $applyInfo = OilReceiptApply::getByIdForLock(['id' => $v]);

                //校验：只有待审核状态才可审核
                if ($applyInfo->receipt_status == -1) {
                    throw new \RuntimeException('该单号已作废', 2);
                } elseif ($applyInfo->receipt_status == 1) {
                    throw new \RuntimeException('该单号已审核', 2);
                }

                $AuditStatus['id'] = $v;
                $AuditStatus['receipt_status'] = ReceiptApplyStatus::AUDITED;
                $AuditStatus['last_operator_id'] = $this->app->myAdmin->id;
                $AuditStatus['last_operator'] = $this->app->myAdmin->true_name;
                $AuditStatus['updatetime'] = helper::nowTime();
                OilReceiptApply::edit($AuditStatus);

                //记录操作日志
                \Models\OilReceiptApplyLog::add([
                    'app_id' => $params['id'],
                    'status' => ReceiptApplyStatus::AUDITED,
                    'last_operator' => $this->app->myAdmin->true_name,
                    'last_operator_id' => $this->app->myAdmin->id,
                    'createtime' => helper::nowTime(),
                ]);
            }

            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json('', 0, '审核成功');
    }

    /**
     * @title 确认开票
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function confirmInvoice()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        Capsule::connection()->beginTransaction();
        try {
            $idsArr = explode(',', $params['id']);
            $success_num = 0;
            foreach ($idsArr as $v) {
                $applyInfo = OilReceiptApply::getByIdForLock(['id' => $v]);
                //校验：只有已审核状态才可确认开票
                if ($applyInfo->receipt_status != ReceiptApplyStatus::OPENING) {
//                    throw new \RuntimeException('只有开票中的单号才可确认开票', 2);
                    Log::info("发票申请单，apply_id:" . $v . "，不是开票中的状态:" . $applyInfo->receipt_status, [], "info");
                    break;
                }
                //校验申请金额与实开金额
                $real_amount = \Models\OilReceiptSalesDetails::getRealMoney($v);

                if (bccomp($applyInfo->receipt_amount, $real_amount, 2) != 0) {
//                    throw new \RuntimeException('金额不一致', 2);
                    Log::info("发票申请单，apply_id:" . $v . "，金额不一致:amount" . $applyInfo->receipt_amount . ",real_amount:" . $real_amount, [], "info");
                    break;
                }

                $AuditStatus['receipt_status'] = ReceiptApplyStatus::SUCCESS;
                $AuditStatus['last_operator_id'] = $this->app->myAdmin->id;
                $AuditStatus['last_operator'] = $this->app->myAdmin->true_name;
                $AuditStatus['updatetime'] = helper::nowTime();
                $AuditStatus['id'] = $v;
                $upRes = OilReceiptApply::edit($AuditStatus);
                if ($upRes) {
                    $success_num++;
                }

                //记录操作日志
                \Models\OilReceiptApplyLog::add([
                    'app_id' => $params['id'],
                    'status' => ReceiptApplyStatus::SUCCESS,
                    'last_operator' => $this->app->myAdmin->true_name,
                    'last_operator_id' => $this->app->myAdmin->id,
                    'createtime' => helper::nowTime(),
                ]);

                //通知
                if ($applyInfo->data_from == 3) {
                    Framework\TruckAppNotify\Notify::send([
                        'uid' => $applyInfo->other_creator_id,
                        'title' => '开票申请通过提醒',
                        'description' => '已成功给您开具发票，请注意查收',
                    ]);
                }
                //开票完成之后对 对内部票 对应的翻译明细中的状态转换中置成已转化 同时更改进项票管理的转化状态
                if ($applyInfo->data_from == 3 && $applyInfo->is_internal == 1) {
                    //判断是否是内部票 内部票才需要转化
                    //将翻译明细中转化中的状态变更为已转化  并更新 进项票管理的转化状态
                    $translateIds = \Models\OilReceiptApplyInternalDetails::getPluckField([
                        'receipt_apply_id' => $applyInfo->id,
                    ], 'receipt_translate_detail_id');
                    if (!empty($translateIds)) {
                        //翻译明细中转化中的状态变更为已转化
                        $translateDetails = \Models\OilReceiptTranslateDetail::batchEdit($translateIds, [
                            'transform' => ReceiptTranslateDetail::TRANSFORMED
                        ], ['transform' => ReceiptTranslateDetail::TRANSFORMING]);
                        //更新进项票转化状态 查询翻译明细转化状态
                        $transformStatus = \Models\OilReceiptTranslateDetail::getFilterByPluckFiled([
                            'id' => $translateIds
                        ], 'transform');
                        $receipt_internal_detail = \Models\OilReceiptApplyInternalDetails::getInfoByFilter([
                            'receipt_apply_id' => $applyInfo->id,
                        ]);
                        if (in_array(ReceiptTranslateDetail::TRANSFORMED, $transformStatus) &&
                            !in_array(ReceiptTranslateDetail::TRANSFORM_NO, $transformStatus) &&
                            !in_array(ReceiptTranslateDetail::TRANSFORMING, $transformStatus)) {
                            \Models\OilReceiptReturn::edit(['id' => $receipt_internal_detail->receipt_return_id, 'transform_status' => ReceiptTranslateDetail::TRANSFORMED]);
                        } elseif (in_array(ReceiptTranslateDetail::TRANSFORM_NO, $transformStatus) &&
                            !in_array(ReceiptTranslateDetail::TRANSFORMED, $transformStatus) &&
                            !in_array(ReceiptTranslateDetail::TRANSFORMING, $transformStatus)) {
                            \Models\OilReceiptReturn::edit(['id' => $receipt_internal_detail->receipt_return_id, 'transform_status' => ReceiptTranslateDetail::TRANSFORM_NO]);
                        } elseif (in_array(ReceiptTranslateDetail::TRANSFORMING, $transformStatus) &&
                            !in_array(ReceiptTranslateDetail::TRANSFORMED, $transformStatus) &&
                            !in_array(ReceiptTranslateDetail::TRANSFORM_NO, $transformStatus)) {
                            \Models\OilReceiptReturn::edit(['id' => $receipt_internal_detail->receipt_return_id, 'transform_status' => ReceiptTranslateDetail::TRANSFORMING]);
                        } elseif (in_array(ReceiptTranslateDetail::TRANSFORM_NO_NEED, $transformStatus) &&
                            !in_array(ReceiptTranslateDetail::TRANSFORMED, $transformStatus) &&
                            !in_array(ReceiptTranslateDetail::TRANSFORM_NO, $transformStatus) &&
                            !in_array(ReceiptTranslateDetail::TRANSFORMING, $transformStatus)) {
                            \Models\OilReceiptReturn::edit(['id' => $receipt_internal_detail->receipt_return_id, 'transform_status' => ReceiptTranslateDetail::TRANSFORM_NO_NEED]);
                        }
                        //库存操作 库存扣减 销售方 销项票
                        $receipt_translate_details = \Models\OilReceiptTranslateDetail::getListByFilter([
                            'id' => $translateIds
                        ]);
                        foreach ($receipt_translate_details as $receipt_translate_detail) {
                            $receipt_return = \Models\OilReceiptReturn::getById(['id' => $receipt_translate_detail['receipt_return_id']]);
                            if ($receipt_translate_detail['is_inner'] == \Fuel\Defines\ReceiptReturn::IS_INNER_YES && $receipt_translate_detail['translate_status'] == ReceiptTranslateDetail::TRANSLATE_STATUS_OVER) {
                                //库存操作
                                $result = (new  \Fuel\Service\InvoiceStockService())->insertStock(false, [
                                    'oil_type_id' => $receipt_translate_detail['in_second_oil_id'],
                                    'classify' => 20,//销项票
                                    'res_id' => $receipt_translate_detail['id'],
                                    'res_type' => 10,//蓝票
                                ], true);
                            }
                        }
                    }
                }
            }
            Capsule::connection()->commit();
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json('', 0, '处理成功' . $success_num . "条");
    }

    /**
     * @title 恢复额度
     * @desc
     * @param $receipt_apply_id
     * @return array
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     */
    public function recoveryQuota($receipt_apply_id,$receipt_no = '')
    {

        $detailsList = OilReceiptApplyDetails::getApplyDetails(['receipt_apply_id' => $receipt_apply_id]);

        $invoiceTradeArr = OilReceiptInvoiceTrades::getPluckData([
            //"data_fromIn"=>[10,30],
            //"is_sale_relation"=>CardTradeConf::Trade_Sale_Normal,
            "relation_receipt_no_lk"=>$receipt_no
        ],"trades_id");

        Log::error("recoveryQuota-trades",[$invoiceTradeArr],"invoice_trades");

        foreach ($detailsList as $key => $value) {
            $tradesInfo = OilCardViceTrades::getById(['id' => $value->trades_id]);
            if ($tradesInfo) {
                $updateArr['receipt_remain'] = bcadd($tradesInfo->receipt_remain, $value->receipt_money, 2);
                $orginReceipt = bcsub($tradesInfo->trade_money, $tradesInfo->use_fanli_money, 2);
                if (bccomp($orginReceipt, $updateArr['receipt_remain'], 2) == 0) {
                    $updateArr['is_open_invoice'] = null;
                } else {
                    $updateArr['is_open_invoice'] = IsOpenInvoiceForTrades::PART;
                }
                if ( $value->last_operator != '关联消费' ) {
                    $tradesInfo->update($updateArr);
                }

                if ( in_array($value->trades_id,$invoiceTradeArr) ){
                    $updateSql = "update oil_receipt_invoice_trades set is_sale_relation = ".CardTradeConf::Trade_Sale_No_Relation.",
                     relation_receipt_no = null,relation_sale_no = null,updatetime = '".helper::nowTime()."'  where trades_id = ".$value->trades_id.";";

                    Log::error("recoveryQuota-trades",[$updateSql],"invoice_trades");

                    Capsule::connection()->getPdo()->exec($updateSql);
                }

            } else {
                throw new \RuntimeException('未找到消费记录:' . $value->trades_id, 2);
            }
        }

        return TRUE;
    }

    /**
     * 重置内部发票申请关联的进项票翻译明细转化状态
     * @param $receipt_apply_id
     * @return bool
     */
    public function recoverTranslateDetailTransform($receipt_apply_id)
    {
        $items = \Models\OilReceiptApplyInternalDetails::getList(['receipt_apply_id' => $receipt_apply_id, '_export' => 1]);

        $returnIds = $detailIds = [];
        foreach ($items as $item) {
            $returnIds[] = $item->receipt_return_id;
            $detailIds[] = $item->receipt_translate_detail_id;
        }
        $returnIds = array_unique($returnIds);
        $detailIds = array_unique($detailIds);

        $data = [
            'transform' => \Fuel\Defines\ReceiptTranslateDetail::TRANSFORM_NO
        ];
        \Models\OilReceiptTranslateDetail::whereIn('id', $detailIds)->update($data);
        \Models\OilReceiptReturn::whereIn('id', $returnIds)->update(['transform_status' => \Fuel\Defines\ReceiptTranslateDetail::TRANSFORM_NO]);
    }

    /**
     * @title 作废
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     */
    public function reject()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);

        Capsule::connection()->beginTransaction();
        try {
            //校验
            $info = OilReceiptApply::getByIdForLock(['id' => $params['id']]);
            if ($info->receipt_status == ReceiptApplyStatus::HANDLING) {
                throw new \RuntimeException('该单号作废中，请勿重复操作', 2);
            }
            if ($info->receipt_status == ReceiptApplyStatus::REJECT) {
                throw new \RuntimeException('该单号已作废', 2);
            }
            //是否存在已开的销售单据和对应发票
            $receiptSalesRecord = \Models\OilReceiptSalesDetails::getByApplyIdForUse($info->id);
            if (intval($receiptSalesRecord) > 0) {
                throw new \RuntimeException('该申请单的销售单据下还存在已勾稽发票', 2);
            }

            $params['receipt_status'] = \Fuel\Defines\ReceiptApplyStatus::HANDLING;
            $params['user_id'] = isset($this->app->myAdmin->id) ? $this->app->myAdmin->id : 1;
            $params['last_operator'] = $this->app->myAdmin->true_name ? $this->app->myAdmin->true_name : '';
            $params['receipt_no'] = $info->no;

            //变更申请单
            OilReceiptApply::edit($params);

            //销售单据作废
            \Models\OilReceiptSalesDetails::rejectByApplyId($info->id);

            $params['is_internal'] = $info->is_internal;
            if($info->is_internal == 1){
                $job_params['receiptJobType'] = 3;
                $operator_data = \Models\OilOperators::getListByFilter([
                    'taxpayer_no'  => $info->seller_taxpayer_no,
                ]);
                $is_month = 2;
                $month_time = [];
                if($info->trade_start_time == $info->trade_end_time){
                    $is_month = 1;
                    //$month_time = substr($info->trade_start_time,0,7);
                    $month_time = explode(";",$info->receipt_time);
                }
                $job_params = [
                    "id" => $info->id,//供给公司
                    "up_operator_id" => $operator_data[0]['id'],//供给公司
                    'down_operator_id' => $info->org_id, //销售公司
                    'trade_start_time' => $info->trade_start_time,//开始时间
                    'trade_end_time' => $info->trade_end_time, //结束时间,
                    'oil_type' => $info->oil_type, //结束时间,
                    'receipt_apply_id' => $info->id,//结束时间,
                    'user_id' =>$this->app->myAdmin->id,
                    'last_operator' =>$this->app->myAdmin->true_name,
                    'is_month'  => $is_month,
                    'month_time' => $month_time,
                    'receiptJobType' => 3
                ];
                (new \Jobs\ReceiptApplyInternalJob($job_params))
                    ->setTaskName('作废开票明细')
                    ->setUserInfo($this->app->myAdmin)
                    ->onQueue('addReceiptApplyInternalJob')
                    ->setTries(3)
                    ->dispatch();
            }else{
                $this->jobForReject($params);
            }

            //记录操作日志
            \Models\OilReceiptApplyLog::add([
                'app_id' => $params['receipt_apply_id'],
                'status' => $params['receipt_status'],
                'last_operator' => $this->app->myAdmin->true_name,
                'last_operator_id' => $this->app->myAdmin->id,
                'createtime' => helper::nowTime(),
            ]);

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        //通知
        if ($info->data_from == 3) {
            Framework\TruckAppNotify\Notify::send([
                'uid' => $info->other_creator_id,
                'title' => '开票申请驳回提醒',
                'description' => '您的开票申请很遗憾被驳回，驳回原因：提交资料不正确',
            ]);
        }

        Response::json('', 0, '操作成功');
    }

    /**
     * @title 删除
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     */
    public function remove()
    {
        $params = helper::filterParams();

        helper::argumentCheck(['id'], $params);

        //校验
        $info = OilReceiptApply::getById(['id' => $params['id']]);

        if ($info->receipt_status != -1) {
            throw new \RuntimeException('该单号非作废状态，不能删除', 2);
        }

        Capsule::connection()->beginTransaction();
        try {
            //进回收站
            \Models\OilRecycle::add([
                'table_name' => 'oil_receipt_apply',
                'pk' => $params['id'],
                'org_id' => $info->org_id,
                'no' => $info->no,
                'data' => json_encode($info->toArray()),
                'operator_id' => $this->app->myAdmin->id,
            ]);

            //删除log
            \Models\OilReceiptApplyLog::removeByAppId($params['id']);

            //删除单子
            OilReceiptApply::remove(['ids' => $params['id']]);

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        Response::json(TRUE, 0, '操作成功');

    }

    /**
     * @title 获取付款公司可开额度
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     */
    public function maxAmount()
    {
        $params = helper::filterParams();

        $maxAmount = OpenReceipt::getQuota($params);

        Response::json($maxAmount);
    }

    //todo 创建发票申请单


    public function addExpressInfo()
    {
        $params = helper::filterParams();
        //校验：只有待审核状态才可编辑
        $info = OilReceiptApply::getById(['id' => $params['id']]);
        if (!in_array($info->receipt_status, [ReceiptApplyStatus::MAILED, ReceiptApplyStatus::SUCCESS])) {
            Response::json('', 2, '该单号不是已审核或开票成功状态，修改失败');
        }

        $addInfo['id'] = $params['id'];
        $addInfo['deliver_corp'] = $params['express_com'];
        $addInfo['deliver_no'] = $params['express_no'];
        $addInfo['receipt_status'] = ReceiptApplyStatus::MAILED;
        $addInfo['last_operator_id'] = $this->app->myAdmin->id;
        $addInfo['last_operator'] = $this->app->myAdmin->true_name;
        $addInfo['updatetime'] = helper::nowTime();
        $express = OilReceiptApply::edit($addInfo);
        if ($express) {
            Response::json($data = '', $code = 0, $msg = '添加快递信息成功');
        } else {
            Response::json($data = 1, $code = 1, $msg = '添加失败');
        }

    }

    /**
     * getReceiptAll 按月统计默认5个月的发票申请统计
     * <AUTHOR>
     * @since ${DATE}
     */
    public function getReceiptAll()
    {
        $params = helper::filterParams();
        //验证
        helper::argumentCheck(['orgcode'], $params);
        //校验机构号是否有效
        $orgInfo = \Models\OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('机构号无效', 2);
        } else {
            $params['org_id'] = $orgInfo->id;
        }
        //Capsule::connection()->enableQueryLog();
        $data = OilReceiptApply::getReceiptAll($params);
        //$sql = Capsule::connection()->getQueryLog();
        Response::json($data);
    }

    /**
     * 当前月发票申请列表,默认当前月，如果传month可为某月的发票申请记录
     * @params type money|detail  默认是detail
     * <AUTHOR>
     * @since 2016/03/14
     */
    public function getReceiptMonth()
    {
        $params = helper::filterParams();
        //验证
        helper::argumentCheck(['orgcode'], $params);

        //校验机构号是否有效
        $orgInfo = \Models\OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('机构号无效', 2);
        } else {
            $params['org_id'] = $orgInfo->id;
        }

        $params['type'] = $params['type'] ? $params['type'] : 'detail';
        $data = OilReceiptApply::getReceiptMonth($params);

        Response::json($data);
    }

    /**
     * 发票申请单详情
     * <AUTHOR>
     * @since 2016/03/30
     */
    public function show()
    {
        $data = helper::filterParams("*");

        //验证
        helper::argumentCheck(['id'], $data);

        $data = OilReceiptApply::getById(['id' => $data['id']]);

        if ($data) {
            $progress = \Models\OilReceiptApplyLog::getProgressByAppId([
                'app_id' => $data['id'],
            ]);

            $data['progress'] = $progress;
        }

        Response::json($data);
    }

    public function minAmount()
    {
        $params['sys_key'] = 'receipt_limit';
        $data = \Models\OilConfigure::getBySysKey($params);

        Response::json($data);
    }

    /**
     * @title   获取状态
     * <AUTHOR>
     */
    public function getStatus()
    {
        $info = \Fuel\Defines\ReceiptApplyStatus::getAll();
        $data = [];
        foreach ($info as $k => $v) {
            $tmp = [];
            $tmp['key'] = strval($k);
            $tmp['value'] = $v;
            $data[] = $tmp;
        }

        Response::json($data);
    }

    /**
     * 取油品类型配置
     */
    public function getOilTYpeConfig()
    {
        $config = \Fuel\Defines\OilType::getReceiptList();

        $ret = [];
        array_walk($config, function ($val, $key) use (&$ret) {
            $ret[] = [
                'id' => $key,
                'value' => $val,
            ];
        });

        Response::json($ret);
    }


    /**
     * Desc: 添加内部票
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 29/11/22 下午5:13
     */
    public function receiptInternalAdd()
    {
        Log::info('POST--' . var_export($_POST, TRUE), [], 'receiptAdd');
        $params = helper::filterParams();
        Log::info('添加参数--' . var_export($params, TRUE), [], 'receiptAdd');
        try{
            (new \Fuel\Service\ReceiptApplyInternal())->ReceiptApplyInternalAdd($params);
        }catch (RuntimeException $exception){
            throw new \RuntimeException($exception->getMessage(), 2);
        }
        Response::json($params, 0, '添加成功');
    }

    /**
     * 编辑
     * <AUTHOR> Du
     * @since 2016/03/16
     */
    public function receiptInEdit()
    {
        $params = helper::filterParams();
        Log::info('修改参数－－' . var_export($params, TRUE), [], 'receiptInEdit');

        Capsule::connection()->beginTransaction();
        try {
            (new \Fuel\Service\ReceiptApplyInternal())->editInternalApply($params,$this->app->myAdmin);
            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException('修改失败:' . $e->getMessage(), 2);
        }

        Response::json('', 0, '更改成功');
    }


    /**
     * Desc: 获取可开额度
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 30/11/22 下午4:12
     */
    public function getAmount(){
        Log::info('POST--' . var_export($_POST, TRUE), [], 'receiptAdd');
        $params = helper::filterParams();
        Log::info('getAmount--' . var_export($params, TRUE), [], 'receiptAdd');

        $money = (new \Fuel\Service\ReceiptApplyInternal())->getReceiptMoney($params);
        Response::json($money, 0, '添加成功');
    }

    /**
     * Desc: 获取可开额度
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 30/11/22 下午4:12
     */
    public function getReceiptTime(){
        Log::info('POST--' . var_export($_POST, TRUE), [], 'receiptAdd');
        $params = helper::filterParams();
        Log::info('getReceiptTime--' . var_export($params, TRUE), [], 'receiptAdd');

        $time = (new \Fuel\Service\ReceiptApplyInternal())->getReceiptTime($params);
        $arr_time = explode(';',$time);
        $stime_arr = $etime_arr = [];
        foreach ($arr_time as $value){
            $info = explode('~',$value);
            $stime_arr[date("Y-m-d",strtotime($info[0]))] = $info[0];
            $etime_arr[date("Y-m-d",strtotime($info[1]))] = $info[1];
        }
        Response::json(['stime_arr'=>$stime_arr,'etime_arr'=>$etime_arr,'time'=>$time], 0, '添加成功');
    }


    /**
     * Desc:
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 22/12/22 上午11:03
     */
    public function reloadReceiptInternalApplyData()
    {
        $params = ["filePath" => "线上B类运营商税盘库存数2.1.xlsx"];
        $service = new \Fuel\Service\Import\ReceiptInternalApply();

        $return = $service->reloadReceiptReturnStatusData($params);
        var_dump($return);
    }

    /**
     * Desc: 推送Job
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 22/12/22 上午11:03
     */
    public function UpstreamOilTradeWriteJob()
    {
        $job_params = [
            'createtime_stime'=>'2022-01-01 00:00:00',
            'createtime_etime'=>'2022-01-02 00:00:00',
        ];
        $task = (new \Jobs\UpstreamOilTradeWriteJob($job_params))->handle();
    }

    public function getStepMonth()
    {
        $params = helper::filterParams();
        \helper::argumentCheck(['operator_id', 'seller_id', 'oil_type'], $params);
        $params['oil_type'] = ReceiptApplyInternal::getBaseType($params['oil_type']);
        $month = \Fuel\Service\OperatorReceipt::getStepMonth($params);
        Response::json($month, 0, 'success');
    }

    /**
     * 内部换签
     * @throws Exception
     */
    public function addReceiptByMonth()
    {
        $params = helper::filterParams();
        Log::info('添加参数--month' . var_export($params, TRUE), [], 'receiptAdd');
        \helper::argumentCheck(['operator_id', 'seller_id', 'oil_type','s_month_in','split_amount_type'], $params);
        $params['oil_type'] = ReceiptApplyInternal::getBaseType($params['oil_type']);
        try{
            $monthArr = explode(",",$params['s_month_in']);
            if(count($monthArr) == 0){
                throw new \RuntimeException('请选择可开票月份', 2);
            }
            $receiptMonth = \Fuel\Service\OperatorReceipt::getStepMonth($params);
            if(count($receiptMonth) == 0){
                throw new \RuntimeException('没有换签解锁月份', 22);
            }

            if( count($monthArr) > 1 ){
                $time = ReceiptApplyDefine::receiptProfitRate($params['operator_id']);
                $map = [];
                foreach ($monthArr as $_month) {
                    foreach ($time as $_val) {
                        if (strtotime($_month."-01") >= strtotime($_val['start']) && strtotime($_month."-01") < strtotime($_val['end'])
                        ) {
                            $map[] = $_val['classify'];
                        }
                    }
                }
                if( count(array_unique($map)) > 1 ){
                    throw new \RuntimeException('换签月份不允许跨时间段', 40011);
                }
            }

            $sn = isset($params['sn']) && $params['sn'] ? $params['sn'] : time();
            $_total = [];
            foreach($monthArr as $_val) {
                //unset($params['s_month_in']);
                if(!in_array($_val,$receiptMonth['month'])){
                    throw new \RuntimeException('该月份【'.$_val.'】未解锁', 22);
                }
                $money = isset($receiptMonth['data'][$_val]) ? $receiptMonth['data'][$_val] : [];
                if(count($money) == 0){
                    throw new \RuntimeException('该月份【'.$_val.'】可开票金额为0', 22);
                }
                $_total['receipt_amount'] += $money['receipt_amount'];
                $_total['receipt_num'] += $money['receipt_num'];
            }
            $params['trade_start_time'] = $monthArr[0]."-01 00:00:00";
            $params['trade_end_time'] = $monthArr[0]."-01 00:00:00";
            $params['sn'] = $sn.'-'.rand(100000,1000000);
            $params['month_time'] = $params['s_month_in'];
            unset($params['s_month_in']);
            (new \Fuel\Service\ReceiptApplyInternal())->ReceiptApplyInternalAdd($params, $_total,1);
        }catch (RuntimeException $exception){
            throw new \RuntimeException($exception->getMessage(), 2);
        }
        Response::json($params, 0, '添加成功');
    }

    /**
     * 发送邮件
     */
    public function receiptSendEmail()
    {
        $params = helper::filterParams();

//        $params['content'] = [
//            ['receipt_apply_id'=> 1393,'email'=> '<EMAIL>'],
//            //['receipt_apply_id'=> 1390,'email'=> '<EMAIL>'],
//        ];
        \helper::argumentCheck(['content'], $params);
        $params['content'] = json_decode($params['content'],true);
        Log::error('params',$params,'receiptSendEmail');
        //校验
        foreach ($params['content'] as $key=>$item){
            //邮件地址为空校验
            if(!isset($item['receipt_apply_id']) || !$item['receipt_apply_id']){
                throw new \RuntimeException(($key+1).'行申请单id未空',2);
            }

            if(!isset($item['email']) || !$item['email']){
                throw new \RuntimeException(($key+1).'行邮箱未空',2);
            }
        }

        $data = (new \Fuel\Service\ReceiptApply())->receiptSendEmail($params);

        Response::json($data, 0, '发送成功');
    }

    public function getReceiptOpenChannelList()
    {
        Response::json(
            collect(ReceiptType::$receipt_open_channel_list)->map(function ($v, $k) {
                if ($k == ReceiptType::RECEIPT_OPEN_CHANNEL_XX) {
                    return null;
                }
                return [
                    'name'  => $v,
                    'value' => $k,
                ];
            })->filter()->values()->toArray()
        );
    }

    public function getReceiptByApplyIds()
    {
        $params = helper::filterParams();
        $params['ids'] = explode(',', $params['ids'] ?? '');
        $validator = new Illuminate\Validation\Validator(new Translator('en'), $params, [
            'ids'   => 'required|array',
            'ids.*' => 'required|integer',
        ], [
            'ids.required' => '申请单ID数组不能为空',
            'ids.array' => '申请单ID数组格式不正确',
            'ids.*.required' => '申请单ID不能为空',
            'ids.*integer' => '申请单ID格式不正确',
        ]);
        if ($validator->fails()) {
            Response::json([], 412001, $validator->errors()->first());
        }
        Response::json(
            OilReceiptApply::getReceiptByApplyIds($params['ids'])
        );
    }

    public function getSendEmailLogByApplyId()
    {
        $params = helper::filterParams();
        $validator = new Illuminate\Validation\Validator(new Translator('en'), $params, [
            'id'   => 'required|integer',
        ], [
            'id.required' => '申请单ID不能为空',
            'id.integer' => '申请单ID格式不正确',
        ]);
        if ($validator->fails()) {
            Response::json([], 412001, $validator->errors()->first());
        }
        Response::json(
            OilReceiptEmailLog::Filter([
                'receipt_apply_id' => $params['id']
            ])->orderBy('createtime','desc')->get()->toArray()
        );
    }

    /**
     * 获取对应开票油品列表
     * @return void
     */
    public function getOilNameBySaleName()
    {
        $params = helper::filterParams();
        if($params['is_new_mode'] == 1){
            if($params['operator_name'] == '汇通天下石油化工（大连）有限公司'){
                $data = [
                    ['id'=>1,'name'=>'汽油'],
                    ['id'=>2,'name'=>'柴油'],
                    ['id'=>12,'name'=>'燃油'],
                    ['id'=>5,'name'=>'天然气'],
                ];
            }elseif($params['operator_name'] == '天津汇通天下物联科技有限公司'){
                $data = [
                    ['id'=>1,'name'=>'汽油'],
                    ['id'=>2,'name'=>'柴油'],
                    ['id'=>12,'name'=>'燃油'],
                    ['id'=>5,'name'=>'天然气'],
                ];
            }else{
                if (in_array(API_ENV, ["pro", "prod"])) {
                    $data = [];
                }else{
                    $data = [];
                }
            }
        }else{
            $data = [
                ['id'=>1,'name'=>'汽油'],
                ['id'=>2,'name'=>'柴油'],
                ['id'=>12,'name'=>'燃油'],
                ['id'=>5,'name'=>'天然气'],
                ['id'=>6,'name'=>'尿素'],
            ];
        }

        Response::json($data);
    }

    /**
     * 获取机构开票黑白名单相关信息
     * @return void
     * @throws Exception
     */
    public function getOrgOpenReceiptInfo()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['orgcode'], $params);

        $data = ReceiptApplyInternal::getOrgOpenReceiptInfo($params);

        Response::json($data);
    }
}


