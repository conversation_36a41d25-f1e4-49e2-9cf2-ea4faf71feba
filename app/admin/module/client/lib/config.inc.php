<?php
require_once 'Util.php';

$config = \Framework\Config::get('dsp');
//if($config){
//    define('APP_KEY', $config['app_key']); //1132
//    define('APP_SECRET', $config['app_secret']);
//    define('OIL_API_URL',$config['apiUrl']); //pro
//}else{
//    define('APP_KEY', 'c157d6'); //1132
//    define('APP_SECRET', '568797BE8EDED59630F8B51A5C186441');
//    /*******************油品业务测试接口*************************/
//    //define('OIL_API_URL','http://172.16.1.143:8080/router/rest');
//    define('OIL_API_URL','http://172.16.1.144/router/rest'); //pro
//}

//define("PARENTCARDINFO","huoyunren.gascard.parentCardInfo",TRUE); //获取主卡信息
//define("ADDPARENTCARD","huoyunren.gascard.addparentcard",TRUE); //添加主卡
//define("DELPCARD","huoyunren.gascard.delpcard",TRUE); //删除主卡
//define("LISTCHARGEDETAIL","huoyunren.gascard.listChargeDetail",TRUE); //获取充值明细
//define("ADDCRAWLERTIME","huoyunren.gascard.addCrawlerTime",TRUE); //设置抓取时间
//define("STARTACCOUNT","crawl-provider.fuelCardService.startTask",TRUE); //设置开始时间
//define("STOPACCOUNT","crawl-provider.fuelCardService.stopTask",TRUE); //设置关闭时间
//define("CHILDCARDINFO","huoyunren.gascard.childCardInfo",TRUE); //获取子卡信息
//define("LISTRECORD", "huoyunren.gascard.listrecord", TRUE);  //主卡充值记录同步

//钉钉接口地址
//define('DINGTALK_KEY','112445');
//define('DINGTALK_SECRET','8f5271');
//
////gsp接口地址
//define('GSPADMIN_URL','http://admin.gsp.cn/rest/');
////define('GSPADMIN_URL','http://gsp.huoyunren.com/rest/');
//define('GSPADMIN_KEY','ips');
//define('GSPADMIN_SECRET','********************************');
