<?php
/**
 * 队列消费
 */


use Framework\Config;
use Fuel\Service\SyncGasRecord;
use Fuel\Service\CustomerStream;

class subscribe extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    public function test()
    {
        $conf = Config::get('subscribe.tcard.main_card');

//        var_dump($conf);
    }

    /**
     * 副卡交易队列
     */
    public function subscribe_trades()
    {
        $params = \helper::filterParams();

        $queue = Config::get('subscribe.tcard.trades');

        (new SyncGasRecord())->subscribe_trades($queue);
    }

    /**
     * 分配队列
     */
    public function subscribe_assign()
    {
        $queue_name = Config::get('subscribe.tcard.assign');
        (new CustomerStream())->subscribe_assign($queue_name);
    }

    /**
     * 充值记录队列消费
     */
    public function subscribe_recharge()
    {
        $queue_name = Config::get('subscribe.tcard.recharge');
        (new \Fuel\Service\CustomerStream())->subscribe_recharge($queue_name);
    }

    /**
     * 主卡队列消费
     */
    public function subscribe_main_card()
    {
        $queue_name = Config::get('subscribe.tcard.main_card');
        (new \Fuel\Service\CustomerCard())->subscribe_main_card($queue_name);
    }

    /**
     * 主卡队列消费
     */
    public function subscribe_gsp_main_card()
    {
        (new \Fuel\Service\CustomerCard())->subscribe_gsp_main_card('list:foss:gsp:parent:card:result');
    }

    /**
     * 副卡队列消费
     */
    public function subscribe_vice_card()
    {
        $queue_name = Config::get('subscribe.tcard.vice_card');
        (new \Fuel\Service\CustomerCard())->subscribe_vice_card($queue_name);
    }

    public function push_vice_card()
    {
        $data = file_get_contents("php://input");

        $data = json_decode($data, true);

        if (! is_array($data)) {
            echo '入参需为数组';
            exit;
        }

        global $app;
        \helper::import($app->getAppRoot() . "lib/redis.queue.class.php");
        $queue_name = Config::get('subscribe.foss.trades');
        $config = Config::get('redis');
        $queue  = new \redisQueue($config);
        $queue->setQueue($queue_name);

        foreach ($data as $item) {
            helper::argumentCheck(['cardNo', 'address'], $item);
            if (empty($item['fueldate'])) {
                $item['fueldate'] = time();
            }
            if (empty($item['amount'])) {
                $item['amount'] = round($item['price'] * $item['count'], 2);
            }
            if (empty($item['id'])) {
                $item['id'] = uniqid();
            }
            $queue->produce(json_encode($item));
        }

        echo '写入数据: ', count($data), " 条";
    }

    public function push_vice_card_tcard()
    {
        $data = file_get_contents("php://input");

        $data = json_decode($data, true);

        if (! is_array($data)) {
            echo '入参需为数组';
            exit;
        }

        global $app;
        \helper::import($app->getAppRoot() . "lib/redis.queue.class.php");
        $queue_name = Config::get('subscribe.tcard.trades');
        $config = Config::get('redis');
        $queue  = new \redisQueue($config);
        $queue->setQueue($queue_name);

        foreach ($data as $item) {
            helper::argumentCheck(['cardNo', 'address'], $item);
            if (empty($item['fueldate'])) {
                $item['fueldate'] = time();
            }
            if (empty($item['amount'])) {
                $item['amount'] = round($item['price'] * $item['count'], 2);
            }
            if (empty($item['id'])) {
                $item['id'] = uniqid();
            }
            $queue->produce(json_encode($item));
        }

        echo '写入数据: ', count($data), " 条";
    }

    /**
     * 自营卡副卡队列消费
     */
    public function subscribe_gsp_trades()
    {
        $queue_name = Config::get('subscribe.foss.trades');
        (new SyncGasRecord())->subscribe_trades($queue_name);
    }

    /**
     * 自营卡分配队列
     */
    public function subscribe_gsp_assign()
    {
        $queue_name = Config::get('subscribe.foss.assign');
        (new CustomerStream())->subscribe_assign($queue_name);
    }

    public function sendAccountAlarm()
    {
        $queue_name = Config::get('subscribe.tcard.account_alarm');
        (new SyncGasRecord())->subscribe_account_alarm($queue_name);
    }

    /**
     * 充值记录队列消费
     */
    public function subscribe_recharge_foss()
    {
        $queue_name = Config::get('subscribe.foss.recharge');
        (new \Fuel\Service\CustomerStream())->subscribe_recharge($queue_name);
    }

    /**
     * desp-bi导出队列消费
     */
    public function subscribe_desp_bi_export()
    {
        $queue_name = "desp:export:list:profit";
        (new \Fuel\Service\OilProfit())->consumeProfitExport($queue_name);
    }
}
