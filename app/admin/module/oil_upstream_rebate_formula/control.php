<?php
use Fuel\Response;

use Fuel\Service\UpstreamRebate\FormulaService;

class oil_upstream_rebate_formula extends baseControl
{
    /**
     * @var FormulaService
     */
    protected $service;

    public function __construct()
    {
        parent::__construct();
        $this->service = FormulaService::getInstance();
    }

    /**
     * 获取元数据
     */
    public function getMeta()
    {
        $params = helper::filterParams();
        $data = $this->service->getMeta($params);
        Response::json($data);
    }

    /**
     * 新增
     * @return void
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = $this->service->createFormula($params);
        Response::json($data, 0, '添加成功');
    }

    /**
     * 编辑
     * @return void
     */
    public function update()
    {
        $params = helper::filterParams();
        $data = $this->service->updateFormula($params);
        Response::json($data, 0, '更新成功');
    }

    /**
     * 列表
     * @return void
     */
    public function list() : void
    {
        $params = helper::filterParams();
        $data = $this->service->getList($params);
        Response::json($data, 0, '获取成功');
    }

    /**
     * 详情
     * @return void
     */
    public function detail() : void
    {
        $params = helper::filterParams();
        $data = $this->service->detail($params);
        Response::json($data ?? new stdClass, 0, '获取成功');
    }
}