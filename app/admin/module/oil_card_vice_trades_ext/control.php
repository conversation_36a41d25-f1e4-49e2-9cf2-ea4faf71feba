<?php
/**
 * oil_card_vice_trades_ext Control
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/02/18
 * Time: 17:17:33
 */
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCardViceTradesExt;
use Framework\Excel\ExcelWriter;
use Fuel\Response;

class oil_card_vice_trades_ext extends baseControl
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 列表查询
     * @return array
     */
    public function getList()
    {
        $params = helper::filterParams();

        $data = OilCardViceTradesExt::getList($params);
        if (isset($params['_export']) && $params['_export'] == 1){
            $this->exportList($data);
        }else{
            Response::json($data);
        }
    }

    /**
     * 列表数据导出
     * @param  $data
     */
    private function exportList($data)
    {
        $exportData = [
                'fileName' => 'oil_card_vice_trades_ext_' . date("YmdHis"),
                'sheetName' => 'oil_card_vice_trades_ext',
                'title' => [
                'id'   =>  'ID',
                'trades_id'   =>  '交易ID',
                'merchant_id'   =>  '商户ID',
                'merchant_name'   =>  '商户名称',
                'createtime'   =>  'createtime'
            ],
            'data' => $data->toArray(),
        ];

        Framework\Excel\ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });
    }

    /**
     * 详情查询
     * @return object
     */
    public function show()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilCardViceTradesExt::getById($params);

        Response::json($data);
    }

    /**
     * 新增
     * @return mixed
     */
    public function create()
    {
        $params = helper::filterParams();
        $data = OilCardViceTradesExt::add($params);

        Response::json($data,0,'添加成功');
    }

    /**
     * 编辑
     * @return mixed
     */
    public function edit()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['id'], $params);
        $data = OilCardViceTradesExt::edit($params);

        Response::json($data,0,'编辑成功');
    }

    /**
     * 根据ids删除或批量删除
     * @return int
     */
    public function remove()
    {
        $params = helper::filterParams();
        helper::argumentCheck(['ids'], $params);
        $data = OilCardViceTradesExt::remove($params);

        Response::json($data);
    }

    /**
     * 2020年1月1号后的销售归属脚本
     */
    public function tradeSaleAction()
    {
        \Fuel\Service\TradesService::tradeSaleAction();

    }

}