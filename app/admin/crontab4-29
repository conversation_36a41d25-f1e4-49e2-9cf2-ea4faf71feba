##执行compare assign record##
0 06 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=pushAssignToDsp" > /dev/null 2>&1 &
30 06 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_assign_statistic&f=compareAssignData" > /dev/null 2>&1 &

##执行compare assign record##
30 19 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=pushAssignToDsp" > /dev/null 2>&1 &
0 20 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_assign_statistic&f=compareAssignData" > /dev/null 2>&1 &

##执行compare assign record##
0 16 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=pushAssignToDsp" > /dev/null 2>&1 &
30 16 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_assign_statistic&f=compareAssignData" > /dev/null 2>&1 &

##执行compare assign record 09:00##
0 09 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=pushAssignToDsp" > /dev/null 2>&1 &
30 09 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_assign_statistic&f=compareAssignData" > /dev/null 2>&1 &

##执行compare assign record 12:00##
0 12 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=pushAssignToDsp" > /dev/null 2>&1 &
30 12 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_assign_statistic&f=compareAssignData" > /dev/null 2>&1 &

##执行compare assign record 21:00##
0 21 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=pushAssignToDsp" > /dev/null 2>&1 &
30 21 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_assign_statistic&f=compareAssignData" > /dev/null 2>&1 &

#0 */1 * * * sh /data/web_data/git/publish/jobs/autoPublishOrderTrace.sh > /dev/null 2>&1 &

59 23 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_gos_task&f=deleteWeekBefore" > /dev/null 2>&1 &

#
##副卡余额刷新
#0 */2 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask" > /dev/null 2>&1 &

## 石化卡刷新
0 */4 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask2&oil_com=1&pageNo=1&pageSize=300" > /dev/null 2>&1 &

## 石油卡
0 */4 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask2&oil_com=2&pageNo=1&pageSize=300&lt_id=3500000" > /dev/null 2>&1 &

0 */4 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask2&oil_com=2&pageNo=1&pageSize=300&gt_id=3500000&lt_id=7000000" > /dev/null 2>&1 &

0 */4 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask2&oil_com=2&pageNo=1&pageSize=300&gt_id=7000000&lt_id=10500000" > /dev/null 2>&1 &

0 */4 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask2&oil_com=2&pageNo=1&pageSize=300&gt_id=10500000&lt_id=14000000" > /dev/null 2>&1 &

0 */4 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask2&oil_com=2&pageNo=1&pageSize=300&gt_id=14000000" > /dev/null 2>&1 &

## 中交石油3
0 */4 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask2&oil_com=3&pageNo=1&pageSize=300" > /dev/null 2>&1 &

## 中交石化50
0 */4 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask2&oil_com=50&pageNo=1&pageSize=300" > /dev/null 2>&1 &

## 柴油专用卡
0 */4 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=balanceRefreshTask2&oil_com=52&pageNo=1&pageSize=300" > /dev/null 2>&1 &

#定时扣除壹号卡共享卡分配单
#*/10 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=cronAssignStatus" > /dev/null 2>&1 &

#定时检查1号共享卡分配单状态
*/5 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=cronAlarmAssign" > /dev/null 2>&1 &

#为1号共享卡的授信机构生成中石化虚拟卡
0 */4 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_virtual_vice_relation&f=createVirtulCard" > /dev/null 2>&1 &


##补注册cardID和卡账户
*/2 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=reRegisterCardToAccountCenter" > /dev/null 2>&1 &

##副卡余额更新后增量推送到gos
# */5 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=syncIncrementBalance" > /dev/null 2>&1 &

##主卡余额刷新
*/30 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_main&f=balanceRefreshTask" > /dev/null 2>&1 &

##增量同步副卡分配记录
#*/10 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_vice_assign&f=syncAssignData"

##增量同步新产生的加油记录
*/15 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice_trades&f=syncNewGasRecord" > /dev/null 2>&1 &
##增量修改已更新的加油记录
*/30 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice_trades&f=syncUpdatedGasRecord" > /dev/null 2>&1 &

##增量同步撬装1号卡加油记录
#*/10 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice_trades&f=synSkidMountedPlatformViceTrades" > /dev/null 2>&1 &


##取出交易记录表中的油站信息、油品信息，将数据添加到相应表中
*/15 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_station&f=addOilExtraData" > /dev/null 2>&1 &

##回写消费记录中省份信息为空的数据
0 */2 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice_trades&f=updateTradeProvince" > /dev/null 2>&1 &

## 统计指定机构开票额度
0 1 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/unitTest.php "m=ReceiptQuotaCase&f=receiptQuotaDayStatistic"


##自动分配复核
#*/1 * * * * curl "http://zqx.chinawayltd.com/inside.php?t=json&m=oil_account_assign&f=checkAssign" > /dev/null 2>&1 &
*/4 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=checkAssign" > /dev/null 2>&1 &


##open DSP set per 30 min
0 */1 * * * curl "http://zqx.chinawayltd.com/inside.php?t=json&m=oil_card_main&f=my_open" > /dev/null 2>&1 &

##每天零点重启execQueueTask zqx_jobs
*/30 * * * * sh /data/web_data/jobsShell/kill_zqx_jobs.sh

##同步主卡充值记录
*/10 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_main_charge&f=syncChargeDetail" > /dev/null 2>&1 &
##同步预分配记录
*/30 * * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_pre_assign&f=syncAssignOrderData" > /dev/null 2>&1 &

#生成上个月的机构统计月报数据
0 1 1 * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_org_month_statistic&f=syncOrgOilData" 2>&1

#生成上个月的机构统月报下载ECELL
0 5 1 * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_org_month_statistic&f=generateAttachment" 2>&1

##平账探测
#0 */6 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_money&f=accountChecking" 2>&1 &

##柴油专用卡开票额度释放
#0 5 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_invoice_quota_release&f=cronQuotaRelease" > /dev/null 2>&1 &

##可疑消费套现
0 3 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_trades_out&f=tradesOutTask" 2>&1 &

##自动删除易维超时工单
#59 23 * * * /usr/local/php55/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=deleteTimeOutEWeiWorkOrder" > /dev/null 2>&1 &

##初始化账户金额和卡金额##
30 3 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_statement&f=snapshot" > /dev/null 2>&1 &

##推送昨日业务数据到账户中心##
#10 23 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_pre_dispatch&f=init" > /dev/null 2>&1 &

##定期删除account_statement数据##
30 22 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_statement&f=cronSetTimeOutRemove" > /dev/null 2>&1 &

##执行fanli_push_tash数据##
#*/5 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_fanli_push_task&f=fanLiToG7Pay" > /dev/null 2>&1 &

##执行根据account_money抹平G7pay余额数据##
#0 3 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_statement&f=moneyFlatForAccountMoney" > /dev/null 2>&1 &

##执行根据G7s的机构名称同步到油品系统##
0 */6 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_org&f=syncOrgNameFromG7s" > /dev/null 2>&1 &

##返利计算审核推到账户中心##
0 12 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_fanli_push_task&f=fanLiToG7Pay" > /dev/null 2>&1 &

##定时爬取托管卡副卡信息##
0 */4 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_customer_card&f=syncAccountInfo" > /dev/null 2>&1 &

##定时爬取账单记录根据id自增##
#0 */6 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_credit_bill&f=syncBillList" > /dev/null 2>&1 &

##定时爬取账单记录根据id和update时间自增##
0 */1 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_credit_bill&f=syncBillListForUpdateTime" > /dev/null 2>&1 &

##定时爬取400工单数据##
*/3 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_customer_issue&f=syncGetCaseData" > /dev/null 2>&1 &

##对比昨天的分配记录
#00 10 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_assign_statistic&f=compareAssignData" > /dev/null 2>&1 &

##检查及修改主卡封禁状态
*/5 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_main&f=checkCloseStatus" > /dev/null 2>&1 &

#生成峰松账单
0 2 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_credit_carrier_bill&f=createBill" > /dev/null 2>&1 &

#同步卡到snapshot表
0 23 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice_snapshot&f=syncCardViceToSnapshot" > /dev/null 2>&1 &

#同步账单到分配表中
0 12 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=cronBillNo" > /dev/null 2>&1 &
0 17 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=cronBillNo" > /dev/null 2>&1 &

#拉取gos油站运营商到gsp系统
0 3 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_station&f=getGosStationOperators" > /dev/null 2>&1 &

#自动认领
*/2 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_bank_records&f=autoClaim" > /dev/null 2>&1 &

#机构返利对账
30 0 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_money&f=compareOrgFanli"

#财务数据推送
30 1 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_org&f=financeCron" > /dev/null 2>&1 &


# 探测是否标记了天然气和尿素的返利使用
#*/30 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=cron&f=checkUseFanLiMoneyOfTrades"

# 库存节省统计
45 1 * * * /usr/local/php70/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_receipt_quota&f=checkReceiptStock" > /dev/null 2>&1 &

#生成卡现金库存
*/20 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_account&f=addCardAccountForCash" > /dev/null 2>&1 &

#生成卡积分库存
*/20 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_account&f=addCardAccountForPoint" > /dev/null 2>&1 &

#生成卡授信库存
*/20 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_account&f=addCardAccountForCredit" > /dev/null 2>&1 &

# 为免息或保理的机构补充card_account的common_account_no
*/2 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_vice&f=historyCreditCardAccount" > /dev/null 2>&1 &

# 生成授信回款单
0 05 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_credit_payments&f=syncPayMentBill" > /dev/null 2>&1 &

# 开票金额与明细是否一致探测
*/5 * * * * /usr/local/php71/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/unitTest.php "m=ReceiptCase&f=checkReceiptAmountNotEqual" > /dev/null 2>&1 &

## 每天凌晨一点跑一次流水数据
00 1 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_supplier_stations_stat&f=cronCreateStat" > /dev/null 2>&1 &

## 每隔三小时跑一次回票数据
0 */3 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_receipt_return&f=autoGenerateHanXinData" > /dev/null 2>&1 &

## 检查授信消费是否和账户中心一致
*/10 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_account_assign&f=cronCheckPayStatus" > /dev/null 2>&1 &

## 同步消费授信保理账单
0 */1 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_credit_repay&f=cronRepayRecord" > /dev/null 2>&1 &

## 同步Crm数据
55 23 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_org&f=getCrmOrgList&maxUpdate=2015-01-01" > /dev/null 2>&1 &
55 11 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_org&f=getCrmOrgList&maxUpdate=2015-01-01" > /dev/null 2>&1 &

## 定时拉取圆通流水
15 1 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_supplier_stations_stat&f=cronCreateGasYuanTongStat" > /dev/null 2>&1 &

## 定时拉取gas未知新增站点
0 */3 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_supplier_relation&f=cronhandleGasNewStations" > /dev/null 2>&1 &


## 抓取待处理的新增站点
0 */1 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_supplier_stations_stat&f=cronUpdateConfigure" > /dev/null 2>&1 &

## 新增站点拉取历史消费统计
*/15 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_supplier_stations_stat&f=cronHandleDayIncStationsStat" > /dev/null 2>&1 &

## 定时检查待审核付款单
*/5 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_foss_payment_records&f=cronCheckBalance" > /dev/null 2>&1 &

## 定时处理支付中付款单
*/30 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_foss_payment_records&f=cronHandleRechargeOrder" > /dev/null 2>&1 &

## 消费日报统计
10 01 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_trade_day_report&f=cron_report" > /dev/null 2>&1 &


# 每日健康提醒
00 09 * * * /usr/local/php71/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/unitTest.php "m=AlarmCase&f=healthAlarm" > /dev/null 2>&1 &


##标记1号卡消费已返利
0 10 1 * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_fanli_calculate&f=setIsFanliForFirst" > /dev/null 2>&1 &

## 每周进行返利
0 09 * * 1 /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_fanli_policy&f=checkPolicy&isWeek=1" > /dev/null 2>&1 &

## 同步gos油站到foss，用于过滤消费数据给大屏
*/30 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/unitTest.php "m=StationCase&f=syncGosStationForLatest"

##同步foss单到Gsp
05 */1 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_customer_issue&f=syncWorkCashToGsp" > /dev/null 2>&1 &

## 待审核付款单钉钉提醒
0 10-17 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_foss_payment_records&f=cronNotify" > /dev/null 2>&1 &

## gos任务重试
*/1 * * * * /usr/local/php70/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/unitTest.php "m=GosTaskCase&f=reTry&take=100" > /dev/null 2>&1 &

### 合同档案脚本 30分钟一次
*/30 * * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "m=oil_pay_company&f=cronContractAction" > /dev/null 2>&1 &

### 开票数据消费月统计
#0 23 1 * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "m=oil_org_trade_month_stat&f=initData" > /dev/null 2>&1 &

### 电子卡销售日报统计
0 3 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "m=oil_org_day_stat&f=cronOrgDayAction" > /dev/null 2>&1 &

### CAT-FUEL Cron

## 汽车尾气监测
*/1 * * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_fuel/cron/exec.php "m=gastest&f=updateGasListPerHour" > /dev/null 2>&1


##拉取德邦分配托管数据
0 06 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_vice_assign&f=getAgentAssignList&idDay=1" > /dev/null 2>&1 &

##拉取德邦分配托管数据
05 */4 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_vice_assign&f=getAgentAssignList" > /dev/null 2>&1 &

##拉取德邦充值托管数据
0 06 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_main_recharge&f=getOilAgentChargeList&idDay=1" > /dev/null 2>&1 &

##拉取德邦充值托管数据
05 */4 * * * /usr/local/product/php-7.0.10/bin/php /data/web_data/web/app/gsp_fuel/app/admin/cron/inside.php "t=json&m=oil_card_main_recharge&f=getOilAgentChargeList" > /dev/null 2>&1 &


#############################################GAS######################################
#GAS 所有定时任务

#删除日志
30 10 * * * /home/<USER>/del_gas_log.sh > /dev/null 2>&1
*/1 * * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/cronzeus.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/cronzeus.log 2>&1 &

*/30 * * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/jiankong.php logs >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/jiankong.log 2>&1 &
*/7 1-23 * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/cronjk.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/cronjk.log 2>&1 &
01 00 * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/report_task_1.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/report.log 2>&1 &
03 00 * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/report_task_2.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/report_history.log 2>&1 &
#油价自动下发至加油机
03 00 * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/setpricelist.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/setpricelist.log 2>&1
00 */1 * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/setPriceHoursList.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/setPriceHoursList.log 2>&1
*/7 * * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/sendhistorytocron.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/sysc_history.log 2>&1
#液位仪定时查询指令
*/7 * * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/auto_levelmeter.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/auto_levelmeter.log 2>&1
#启动kafka设备事件
*/1 * * * * /data/web_data/web/app/cat_gas/app/cat_gas/cron/cmd/macKafkaStart.sh > /dev/null 2>&1
#异步向GOS发送数据
*/1 * * * * /data/web_data/web/app/cat_gas/app/cat_gas/cron/cmd/sendgos.sh > /dev/null 2>&1
#机构加油日报
01 */2 * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/report_task_org.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/report_task_org.php.log 2>&1
#油卡余额日报汇总
59 23 * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/report_task_card_balance.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/report_task_card_balance.php.log 2>&1
#较码表日报
03 */2 * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/checkmeterbystation.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/checkmeterbystation.php.log 2>&1

#历史价格的处理
*/60 * * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/setpricetohistory.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/setpricetohistory.php.log 2>&1
#找油价格的处理
*/1 * * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/zhaoyouprice.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/zhaoyouprice.log 2>&1
*/30 * * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/stationService.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/stationservice.log 2>&1
#老板日报对账
07 08 * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/sendBossReport.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/sendBossReport.log 2>&1
*/1 * * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/oneCardSendWecat.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/oneCardSendWecat.log 2>&1
#民营数据上报检测
*/1 * * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/cron_mac_info.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/cron_mac_info.php.log 2>&1
#更新设备活动时间
*/3 3-23 * * * /usr/local/product/php-5.5.18/bin/php  /data/web_data/web/app/cat_gas/app/cat_gas/cron/cronsetSmartTime.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/cronsetSmartTime.log 2>&1 
#离线报警
01 01 * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/sendSmartWarnList.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/sendSmartWarnList.log 2>&1
*/3 10-23 * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/sendSmartWarn.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/sendSmartWarn.log 2>&1

#删除log_kafka日志
*/7 3-4 * * * /usr/local/product/php-5.5.18/bin/php /data/web_data/web/app/cat_gas/app/cat_gas/cron/del_logs.php >> /data/web_data/web/app/cat_gas/app/cat_gas/tmp/del_log.log 2>&1
