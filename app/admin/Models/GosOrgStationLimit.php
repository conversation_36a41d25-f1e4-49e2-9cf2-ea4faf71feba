<?php

namespace Models;

/**
 * Class OrgStationLimit
 * 油站限制
 *
 * @package App\Models
 * <AUTHOR>
 * @since   2019-01-08 10:17:14
 */

use Framework\Helper;

class GosOrgStationLimit extends \Framework\Database\Model
{
    protected $connection = "gos_online";
    /**
     * 表名
     */
    protected $table = 'gos_org_station_limit';

    /**
     * 缓存组名
     */
    static protected $cacheTag = 'gos_org_station_limit';

    /**
     * 可入库字段
     */
    protected $fillAble = ['id', 'limit_rule', 'org_code', 'classify', 'created_at', 'updated_at', 'deleted_at', 'creator'];

    //disable incrementing id;
    public $incrementing = FALSE;

    protected $guarded = [];
    /**
     * 隐藏输出字段
     */
    protected $hidden = [];

    /**
     * 数据库连接
     *
     * @var string
     */

    public function getFillAble()
    {
        return $this->fillAble;
    }

    public function getConnection()
    {
        return $this->connection;
    }

    //Model RelationShip

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return mixed
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id']) {
            $query->where('id', '=', $params['id']);
        }

        //Search By orgcode
        if (isset($params['org_code']) && $params['org_code']) {
            $query->where('org_code', '=', $params['org_code']);
        }

        //Search By created_at
        if (isset($params['created_at']) && $params['created_at']) {
            $query->where('created_at', '=', $params['created_at']);
        }

        //Search By updated_at
        if (isset($params['updated_at']) && $params['updated_at']) {
            $query->where('updated_at', '=', $params['updated_at']);
        }

        //Search By deleted_at
        if (isset($params['deleted_at']) && $params['deleted_at']) {
            $query->where('deleted_at', '=', $params['deleted_at']);
        }

        return $query;
    }

    /**
     * @title   分页及导出查询
     * @desc    分页及导出数据查询
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package App\Models\MySQL
     * @since   2019-01-08 10:17:14
     * @params  type filedName required?
     * @version 1.0.0
     * @level   1
     * <AUTHOR>
     */
    public static function getList(array $params)
    {
        $pageSize = isset($params['per_page']) && $params['per_page'] ? intval($params['per_page']) : 10;
        $page     = isset($params['page']) ? intval($params['page']) : 1;


        $cacheName = __METHOD__ . var_export($params, TRUE);
        $data      = app('cache')->tags(self::$cacheTag)->get($cacheName);

        if (!$data) {
            if (isset($params['sortField']) && isset($params['sort'])) {
                $orderField = $params['sortField'];
                $orderType  = $params['sort'];
            } else {
                $orderField = 'createtime';
                $orderType  = 'desc';
            }

            $sqlObj = self::Filter($params)->orderBy($orderField, $orderType);

            if (isset($params['_export']) && $params['_export'] == 1) {
                $data = $sqlObj->get();
            } else {
                $data = $sqlObj->Paginate($pageSize, ['*'], 'page', $page);
            }
            app('cache')->tags(self::$cacheTag)->put($cacheName, $data, Config('cache.expireTime'));
        }

        return $data;
    }

    /**
     * @title   根据id查询数据
     * @desc    根据id查询数据
     * @param id
     * @params  char id 主键  是
     * @return mixed
     * @returns
     * {}
     * @returns
     * @package App\Models\MySQL
     * @version 1.0.0
     * <AUTHOR>
     */
    public static function getById($id = NULL)
    {
        $cacheName = __METHOD__ . var_export($id, TRUE);
        $data      = app('cache')->tags(self::$cacheTag)->get($cacheName);
        if (!$data) {
            $data = self::where('id', $id)->first();
            app('cache')->tags(self::$cacheTag)->put($cacheName, $data, Config('cache.expireTime'));
        }

        return $data;
    }

    /**
     * @title   根据id数组，查找多条
     * @desc
     * @param array $idList
     * @return mixed
     * @returns
     * []
     * @returns
     * @package App\Models\MySQL
     * @since
     * @params  type filedName required?
     * @version 1.0.0
     * <AUTHOR>
     */
    public static function getByIdList(array $idList = [])
    {
        $cacheName = __METHOD__ . var_export($idList, TRUE);
        $data      = app('cache')->tags(self::$cacheTag)->get($cacheName);
        if (!$data) {
            $data = self::whereIn("id", $idList)->get();
            app('cache')->tags(self::$cacheTag)->put($cacheName, $data, Config('cache.expireTime'));
        }

        return $data;
    }

    /**
     * @title   根据third_id，查找多条
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package App\Models\MySQL
     * @since   2019-01-08 10:17:14
     * @params  type filedName required?
     * @version 1.0.0
     * @level   1
     * <AUTHOR>
     */
    public static function getByThirdIdList(array $params = [])
    {
        $cacheName = __METHOD__ . var_export($params, TRUE);
        $data      = app('cache')->tags(self::$cacheTag)->get($cacheName);
        if (!$data) {
            $data = self::Filter($params)->get();
            app('cache')->tags(self::$cacheTag)->put($cacheName, $data, Config('cache.expireTime'));
        }

        return $data;
    }

    /**
     * @title    添加数据
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * {}
     * @returns
     * @package  App\Models\MySQL
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
     */
    public static function add(array $params)
    {
        $params['id'] = Helper::uuid();
        $data         = self::create($params);
        app('cache')->tags(self::$cacheTag)->flush();

        return $data;
    }

    /**
     * @title    修改记录
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * {}
     * @returns
     * @package  App\Models\MySQL
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
     */
    public static function edit(array $params)
    {
        $record = self::where('id', $params['id'])->first();
        if (!$record) {
            throw new \RuntimeException("该数据不存在", 2);
        }

        $data = $record->update($params);
        app('cache')->tags(self::$cacheTag)->flush();

        return $data;
    }

    /**
     * @title    修改记录
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * {}
     * @returns
     * @package  App\Models\MySQL
     * @since    2019-01-08 10:17:14
     * @params   type filedName required?
     * @version  1.0.0
     * @level    1
     * <AUTHOR>
     */
    public static function updateByThirdIdAndSysId(array $params)
    {
        $record = self::where('third_id', $params['third_id'])
                      ->where('sys_id', $params['sys_id'])
                      ->first();

        $params['updated_at'] = date("Y-m-d H:i:s");
        if (!$record) {
            $data = self::add($params);
        } else {
            $data = $record->update($params);
        }

        app('cache')->tags(self::$cacheTag)->flush();

        return $data;
    }

    /**
     * @title    删除记录
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * int
     * @returns
     * @package  App\Models\MySQL
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
     */
    public static function remove(array $params = [])
    {
        $data = self::whereIn('id', $params['idList'])->delete();
        app('cache')->tags(self::$cacheTag)->flush();

        return $data;
    }

    /**
     * @title    删除记录根据third_id删除
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * int
     * @returns
     * @package  App\Models\MySQL
     * @since    2019-01-08 10:17:14
     * @params   type filedName required?
     * @version  1.0.0
     * @level    1
     * <AUTHOR>
     */
    public static function removeByThirdIdAndSysId(array $params = [])
    {
        if (!isset($params['thirdIdList']) || !is_array($params['thirdIdList'])) {
            throw new \RuntimeException('thirdIdList must exist and it\'s array', 2);
        }

        $data = self::whereIn('third_id', Helper::arrayValueToString($params['thirdIdList']))
                    ->where('sys_id', $params['sys_id'])
                    ->delete();

        app('cache')->tags(self::$cacheTag)->flush();

        return $data;
    }

    /**
     * 获取机构树油站限制规则
     *
     * @param string $orgCode
     * @return array
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since  2019-04-23 18:27
     */
    public static function getLimitConditionByTree($orgCode = '')
    {
        $condition = [];

        if (empty($orgCode)) {

            return $condition;
        }

        $topOrgCode          = substr((string)$orgCode, 0, 6);
        $data                = GosOrgStationLimit::whereIn('org_code', [$orgCode, $topOrgCode])
                                              ->select('org_code', 'limit_rule')
                                              ->get()
                                              ->toArray();
        $orgStationLimitDict = Helper::convertListToDict($data, 'org_code');
        $orgStationLimitRule = [];

        // 如果当前机构没有配置油站限制规则,需要判断下顶级机构是否配置油站限制规则
        if (isset($orgStationLimitDict[$orgCode])) {

            $orgStationLimitRule = json_decode($orgStationLimitDict[$orgCode]['limit_rule'], true);
        } else {

            if (isset($orgStationLimitDict[$topOrgCode])) {

                $orgStationLimitRule = json_decode($orgStationLimitDict[$topOrgCode]['limit_rule'], true);
            }
        }

        if (empty($orgStationLimitRule)) {

            return $condition;
        }

        if (isset($orgStationLimitRule["oil_station_rule"])) {

            if (isset($orgStationLimitRule['oil_station_rule']['type']) and
                isset($orgStationLimitRule['oil_station_rule']['typeVal'])) {

                switch ($orgStationLimitRule['oil_station_rule']['type']) {

                    case '2':

                        $condition['third_id'] = array_column($orgStationLimitRule['oil_station_rule']['typeVal'],
                            'third_id');
                        break;
                    case '3':

                        $condition['pcode'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                        break;
                    case '4':

                        $condition['station_brand'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                        break;
                    case '5':

                        $condition['rebate_grade'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                        break;
                }
            }
        }

        return $condition;
    }

    public static function splitOrg($org_code)
    {
        $orgList = [];
        $org_len = strlen($org_code);
        if ($org_len > 6) {
            $each_num = ($org_len - 6) / 2;

            $orgList[] = $org_code;

            for ($i = 1; $i <= $each_num; $i++) {
                $orgList[] = substr($org_code, 0, $org_len - ($i * 2));
            }
        } else {
            $orgList = [$org_code];
        }

        return $orgList;
    }

    /**
     * 获取机构树油站限制规则
     *
     * @param string $orgCode
     * @return array
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since  2019-04-23 18:27
     */
    public static function getLimitConditionByTreeNew($orgCode = '')
    {
        $condition = [];

        if (empty($orgCode)) {

            return $condition;
        }

        //$topOrgCode = substr((string)$orgCode, 0, 6);
        //2020-04-03逻辑改为逐级找他的父集的规则 ENINET-1376
        $org_list = self::splitOrg($orgCode);
        //$data = OrgStationLimit::whereIn('org_code', [$orgCode, $topOrgCode])
        $data                = GosOrgStationLimit::whereIn('org_code', $org_list)
                                              ->select('org_code', 'limit_rule')
                                              ->get()
                                              ->toArray();
        $orgStationLimitDict = Helper::convertListToDict($data, 'org_code');
        $orgStationLimitRule = [];

        // 如果当前机构没有配置油站限制规则,需要判断下顶级机构是否配置油站限制规则 //就逻辑
        /*if (isset($orgStationLimitDict[$orgCode])) {

            $orgStationLimitRule = json_decode($orgStationLimitDict[$orgCode]['limit_rule'], true);
        } else {

            if (isset($orgStationLimitDict[$topOrgCode])) {

                $orgStationLimitRule = json_decode($orgStationLimitDict[$topOrgCode]['limit_rule'], true);
            }
        }*/

        //新逻辑
        foreach ($org_list as $item) {
            if (isset($orgStationLimitDict[$item])) {
                $rule = json_decode($orgStationLimitDict[$item]['limit_rule'], true);
                //处理子级机构无限制，顶级有限制不生效的Bug
                if (isset($rule['oil_name_rule']) && isset($rule['oil_name_rule']['type']) && $rule['oil_name_rule']['type'] == 1 && isset($rule['oil_station_rule']) && isset($rule['oil_station_rule']['type']) && $rule['oil_station_rule']['type'] == 1) {
                    continue;
                }
                $orgStationLimitRule = $rule;

                break;
            }
        }

        if (empty($orgStationLimitRule)) {

            return $condition;
        }

        if (isset($orgStationLimitRule["oil_station_rule"])) {

            if (isset($orgStationLimitRule['oil_station_rule']['type']) and
                isset($orgStationLimitRule['oil_station_rule']['typeVal'])) {

                switch ($orgStationLimitRule['oil_station_rule']['type']) {

                    case '2':

                        $condition['third_id'] = array_column($orgStationLimitRule['oil_station_rule']['typeVal'],
                            'third_id');
                        break;
                    case '3':

                        $condition['pcode'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                        break;
                    case '4':

                        $condition['station_brand'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                        break;
                    case '5':

                        $condition['rebate_grade'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                        break;
                }
            }
        }
        Log::info('$orgStationLimitRule--' . var_export($orgStationLimitRule, TRUE), [], 'station');
        if (isset($orgStationLimitRule["oil_name_rule"])) {
            if (isset($orgStationLimitRule['oil_name_rule']['type']) and
                isset($orgStationLimitRule['oil_name_rule']['typeVal'])) {

                switch ($orgStationLimitRule['oil_name_rule']['type']) {
                    case '2':

                        $condition['oil_name_limit'] = $orgStationLimitRule['oil_name_rule']['typeVal'];
                        break;
                }
            }
        }

        return $condition;
    }

    /**
     * 根据条件查找数据
     *
     * @param string $cardNo
     * @return array
     * ---------------------------------------------------
     * <AUTHOR>
     * @since  2019-12-10
     */
    public static function getInfo(array $params = [])
    {
        return self::Filter($params)->first();
    }

    /**
     * 获取机构树油站限制规则
     *
     * @param string $cardNo
     * @return array
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since  2019-04-23 18:27
     */
    public static function getLimitStationIdByTree($cardNo = '')
    {
        $cardInfo = CardVice::getByViceNo([$cardNo]);

        if (empty($cardInfo)) {

            return [];
        }

        $orgInfo = Org::getById($cardInfo[0]['org_id']);

        $condition = [];

        if (!empty($orgInfo->orgcode)) {

            //$topOrgCode = substr((string)$orgInfo->orgcode, 0, 6);
            $org_list = self::splitOrg($orgInfo->orgcode);
            $_Tdata   = GosOrgStationLimit::whereIn('org_code', $org_list)
                                       ->select('org_code', 'limit_rule')
                                       ->get();
            //->toArray();

            if (empty($_Tdata)) {

                return [];
            }
            $data                = $_Tdata->toArray();
            $orgStationLimitDict = Helper::convertListToDict($data, 'org_code');
            $orgStationLimitRule = [];

            /*// 如果当前机构没有配置油站限制规则,需要判断下顶级机构是否配置油站限制规则
            if (isset($orgStationLimitDict[$orgInfo->orgcode])) {
                
                $orgStationLimitRule = json_decode($orgStationLimitDict[$orgInfo->orgcode]['limit_rule'], true);
            } else {
                
                if (isset($orgStationLimitDict[$topOrgCode])) {
                    
                    $orgStationLimitRule = json_decode($orgStationLimitDict[$topOrgCode]['limit_rule'], true);
                }
            }*/

            //新逻辑
            foreach ($org_list as $item) {
                if (isset($orgStationLimitDict[$item])) {
                    $rule = json_decode($orgStationLimitDict[$item]['limit_rule'], true);
                    //处理子级机构无限制，顶级有限制不生效的Bug
                    if (isset($rule['oil_name_rule']) && isset($rule['oil_name_rule']['type']) && $rule['oil_name_rule']['type'] == 1 && isset($rule['oil_station_rule']) && isset($rule['oil_station_rule']['type']) && $rule['oil_station_rule']['type'] == 1) {
                        continue;
                    }
                    $orgStationLimitRule = $rule;

                    break;
                }
            }
            if (empty($orgStationLimitRule)) {
                return [];
            }

            if (!empty($orgStationLimitRule)) {

                if (isset($orgStationLimitRule["oil_station_rule"])) {

                    if (isset($orgStationLimitRule['oil_station_rule']['type']) and
                        isset($orgStationLimitRule['oil_station_rule']['typeVal'])) {

                        switch ($orgStationLimitRule['oil_station_rule']['type']) {

                            case '2':

                                $condition['third_id'] = array_column($orgStationLimitRule['oil_station_rule']['typeVal'],
                                    'third_id');
                                break;
                            case '3':

                                $condition['pcode'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                                break;
                            case '4':

                                $condition['station_brand'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                                break;
                            case '5':

                                $condition['rebate_grade'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                                break;
                        }
                    }
                }
                if (isset($orgStationLimitRule["oil_name_rule"])) {
                    if (isset($orgStationLimitRule['oil_name_rule']['type']) and
                        isset($orgStationLimitRule['oil_name_rule']['typeVal'])) {
                        switch ($orgStationLimitRule['oil_name_rule']['type']) {
                            case '2':
                                $oil_name_limit_content = $orgStationLimitRule['oil_name_rule']['typeVal'];
                                $stationIds             = StationServicePrice::getStationByOilName(['oil_name_valIn' => $oil_name_limit_content]);
                                if ($stationIds) {
                                    if (isset($condition['third_id']) && count($condition['third_id']) > 0) {
                                        $lastIds = array_intersect($condition['third_id'], $stationIds->toArray());
                                    } else {
                                        $lastIds = $stationIds->toArray();
                                    }
                                    $condition['third_id'] = $lastIds;
                                }
                                break;
                        }
                    }
                }
            }
        }

        $_TthirdIds = Station::Filter($condition)->select("third_id")->where('card_classify', 2)->get();
        $returnData = [];

        if ($_TthirdIds) {
            $thirdIds = $_TthirdIds->toArray();
        }

        if ($thirdIds and !empty($thirdIds)) {

            foreach ($thirdIds as $v) {

                $returnData[$v['third_id']] = $v['third_id'];
            }
        }

        return $returnData;
    }

    public static function checkStationByCardNoAndStationId($params = [])
    {
        Log::error('consume-params-' . var_export($params, true), [], 'checkStation');
        $returnData = [
            'code' => 0,
            'msg'  => '成功',
        ];
        //优先判断站点是否开放
        $stationInfo = Station::Filter(['third_id' => $params['station_id']])
                              ->select("third_id", "station_code", "pcode","operator_id")
                              ->where('card_classify', 2)
                              ->first();
        if (!$stationInfo || !$stationInfo->third_id) {
            $returnData['code'] = 5000001;
            $returnData['msg']  = '该油站已下线，请司机以站点列表中显示的站点为准';
            Log::error('consume-result-' . var_export($returnData, true), [], 'checkStation');
            return $returnData;
        }
        if( isset($params['except_ids']) && !empty($params['except_ids']) ){
            $_tmp = explode(",",$params['except_ids']);
            if( in_array($stationInfo->operator_id,$_tmp) ){
                $returnData['code'] = 5000005;
                $returnData['msg']  = '不允许使用供给公司站点！';
                Log::error('consume-result-' . var_export($returnData, true), [$stationInfo->toArray()], 'checkStation');
                return $returnData;
            }
        }

        $cardInfo = CardVice::getByViceNo([$params['card_no']]);

        //如果没有查到卡信息默认检查通过
        if (empty($cardInfo)) {
            Log::error('consume-result-' . var_export($returnData, true), [], 'checkStation');
            return $returnData;
        }

        $orgInfo   = Org::getById($cardInfo[0]['org_id']);
        $limitStation = Station::filterStationWithLimitConfig($orgInfo->orgcode);
        if (in_array($params['station_id'], $limitStation)) {
            $returnData['code'] = 5000002;
            $returnData['msg']  = '站点交易限制';
            return $returnData;
        }
        $condition = $oil_name_limit_content = [];

        $is_have_oil_name_limit = false; //是否有商品限制

        //如果查到卡片所属机构则根据机构查询限制规则
        if (!empty($orgInfo->orgcode)) {
            // ENINET-1190【油卡】【客户】非京顺达的1号卡不可在道科站点加油
            try {
                self::checkDaoKe($orgInfo->orgcode, $stationInfo);
            } catch (\Exception $e) {
                $returnData['code'] = $e->getCode();
                $returnData['msg']  = $e->getMessage();
                Log::error('consume-result-' . var_export($returnData, true), [], 'checkStation');
                return $returnData;
            }
            $org_list = self::splitOrg($orgInfo->orgcode);
            //$data = OrgStationLimit::whereIn('org_code', [$orgCode, $topOrgCode])
            $data = GosOrgStationLimit::whereIn('org_code', $org_list)
                //$topOrgCode = substr((string)$orgInfo->orgcode, 0, 6);
                //$data = OrgStationLimit::whereIn('org_code', [$orgInfo->orgcode, $topOrgCode])
                                   ->select('org_code', 'limit_rule')
                                   ->get()
                                   ->toArray();
            //如未查询到相关限制规则,那么允许加油
            if (empty($data)) {
                Log::error('consume-result-' . var_export($returnData, true), [], 'checkStation');
                return $returnData;
            }

            $orgStationLimitDict = Helper::convertListToDict($data, 'org_code');
            $orgStationLimitRule = [];

            // 如果当前机构没有配置油站限制规则,需要判断下顶级机构是否配置油站限制规则
            /*if (isset($orgStationLimitDict[$orgInfo->orgcode])) {
                
                $orgStationLimitRule = json_decode($orgStationLimitDict[$orgInfo->orgcode]['limit_rule'], true);
            } else {
                
                if (isset($orgStationLimitDict[$topOrgCode])) {
                    
                    $orgStationLimitRule = json_decode($orgStationLimitDict[$topOrgCode]['limit_rule'], true);
                }
            }*/

            //新逻辑
            foreach ($org_list as $item) {
                if (isset($orgStationLimitDict[$item])) {
                    $rule = json_decode($orgStationLimitDict[$item]['limit_rule'], true);
                    //处理子级机构无限制，顶级有限制不生效的Bug
                    if (isset($rule['oil_name_rule']) && isset($rule['oil_name_rule']['type']) && $rule['oil_name_rule']['type'] == 1 && isset($rule['oil_station_rule']) && isset($rule['oil_station_rule']['type']) && $rule['oil_station_rule']['type'] == 1) {
                        continue;
                    }
                    $orgStationLimitRule = $rule;

                    break;
                }
            }

            if (empty($orgStationLimitRule)) {
                if(isset($params['oil_name_val']) && !empty($params['oil_name_val'])) {
                    if ( in_array(trim($params['oil_name_val']), ["维修保养", "润滑油类"]) ) {
                        $returnData['code'] = 5000002;
                        $returnData['msg']  = $params['oil_name_val'] . '被限定使用，不能加油，请司机联系车队长';
                        Log::error('consume-result-' . var_export($returnData, true), [], 'checkStation');
                        return $returnData;
                    }
                }
                return $returnData;
            }

            //Log::info('$orgStationLimitRule-'.var_export($orgStationLimitRule,true),[],'checkStation');

            if (!empty($orgStationLimitRule)) {

                if (isset($orgStationLimitRule["oil_station_rule"])) {

                    if (isset($orgStationLimitRule['oil_station_rule']['type']) and
                        isset($orgStationLimitRule['oil_station_rule']['typeVal'])) {

                        switch ($orgStationLimitRule['oil_station_rule']['type']) {

                            case '2':

                                $condition['third_id'] = array_column($orgStationLimitRule['oil_station_rule']['typeVal'],
                                    'third_id');
                                break;
                            case '3':

                                $condition['pcode'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                                break;
                            case '4':

                                $condition['station_brand'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                                break;
                            case '5':

                                $condition['rebate_grade'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                                break;
                        }
                    }
                }
                if (isset($orgStationLimitRule["oil_name_rule"])) {
                    if (isset($orgStationLimitRule['oil_name_rule']['type']) and
                        isset($orgStationLimitRule['oil_name_rule']['typeVal'])) {

                        switch ($orgStationLimitRule['oil_name_rule']['type']) {

                            case '2':

                                $is_have_oil_name_limit = true;
                                $oil_name_limit_content = $orgStationLimitRule['oil_name_rule']['typeVal'];
                                break;
                        }
                    }
                }
            }
        }

        if (isset($params['oil_name_val']) && $params['oil_name_val'] ) {
            if($is_have_oil_name_limit && $oil_name_limit_content) {
                //Log::info('$oil_name_limit_content-'.var_export($oil_name_limit_content,true),[],'checkStation');
                if (!in_array($params['oil_name_val'], $oil_name_limit_content)) {
                    $returnData['code'] = 5000002;
                    $returnData['msg'] = '该商品被限定使用，请司机联系车队长~';
                    Log::error('consume-result-' . var_export($returnData, true), [], 'checkStation');
                    return $returnData;
                }
            }else{
                if ( in_array(trim($params['oil_name_val']), ["维修保养", "润滑油类"]) ) {
                    $returnData['code'] = 5000002;
                    $returnData['msg']  = $params['oil_name_val'] . '被限定使用，不能加油，请司机联系车队长';
                    Log::error('consume-result-' . var_export($returnData, true), [], 'checkStation');
                    return $returnData;
                }
            }
        }

        $stationData = Station::Filter($condition)->select("third_id", "pcode")->where('card_classify', 2)->get()->toArray();


        if ($stationData and !empty($stationData)) {

            $stationCodes = array_column($stationData, 'third_id');

            if (!in_array($params['station_id'], $stationCodes)) {

                $returnData['code'] = 5000002;
                $returnData['msg']  = '站点被限定使用，不能加油，请司机联系车队长';
            }
        }
        Log::error('consume-result-' . var_export($returnData, true), [], 'checkStation');
        return $returnData;
    }

    /**
     * ENINET-1190【油卡】【客户】非京顺达的1号卡不可在道科站点加油
     *
     * @param $orgCode
     * @param $stationInfo
     * @return bool
     */
    public static function checkDaoKe($orgCode, $stationInfo)
    {
        // 京顺达的机构编码
        $orgCodeMap = Station::$specialOrgCode;
        // 道科运营商编码
        $pcodeMap = ['20008QONTN2E', '2000SCVC'];

        $stationWhiteMap = [
            'JDLMC5',
            'AKJ5VD',
            'M3JSM5',
        ];

        $_orgCode = strtoupper(substr($orgCode, 0, 6));
        $_pCode   = strtoupper($stationInfo->pcode);
        $stationCode = !empty($stationInfo->station_code) ? $stationInfo->station_code : '';
        $oneStationLimit = self::daokeOneStationLimit($_orgCode, $stationCode);

        if (!in_array($_orgCode, $orgCodeMap)
            && in_array($_pCode, $pcodeMap)
            && !in_array($stationCode, $stationWhiteMap)
            && $oneStationLimit
        ) {
            Log::error('请使用主动付款的方式，向站点付款', [], 'checkDaoKe');
    
            throw new \RuntimeException('请使用主动付款的方式，向站点付款', '5000003');
        }

        return true;
    }
    
    /**
     * 道科单站限制
     * @param $orgCode
     * @param $stationCode
     * @return bool
     */
    public static function daokeOneStationLimit($orgCode, $stationCode)
    {
        $isLimit = true;
        // 将道科站点 专属-北京伟氏加油站 （VMB435）只开放给 203PV4 北京世纪物流有限公司-油品 开放
        if ($orgCode == '203PV4' && $stationCode == 'VMB435') {
            $isLimit = false;
        }
        return $isLimit;
    }

    static public function checkStationByOrgCodesAndStationIds($params = [])
    {
        $orgInfos      = [];
        $queryOrgCodes = [];
        $usingStations = Helper::convertListToDict(Station::Filter([
            'third_id' => array_column($params['data'], 'station_id')
        ])->select([
            "third_id",
            "card_classify",
            "pcode",
            "station_brand",
            "rebate_grade"
        ])->get()->toArray(), "third_id");
        $orgCodes      = array_column($params['data'], 'org_code');

        foreach ($orgCodes as $v) {

            $queryOrgCodes[] = $v;
            $queryOrgCodes[] = substr((string)$v, 0, 6);
        }

        $queryOrgCodes       = array_unique($queryOrgCodes);
        $orgStationLimitData = GosOrgStationLimit::whereIn('org_code', $queryOrgCodes)
                                              ->select('org_code', 'limit_rule')
                                              ->get()
                                              ->toArray();
        $orgStationLimitDict = Helper::convertListToDict($orgStationLimitData, 'org_code');

        foreach ($params['data'] as &$v) {

            $v['status'] = true;
            $v['desc']   = '';

            $orgStationLimitRule = [];
            $topOrgCode          = substr((string)$v['org_code'], 0, 6);
            // 如果当前机构没有配置油站限制规则,需要判断下顶级机构是否配置油站限制规则
            if (isset($orgStationLimitDict[$v['org_code']])) {

                $orgStationLimitRule = json_decode($orgStationLimitDict[$v['org_code']]['limit_rule'], true);
            } else {

                if (isset($orgStationLimitDict[$topOrgCode])) {

                    $orgStationLimitRule = json_decode($orgStationLimitDict[$topOrgCode]['limit_rule'], true);
                }
            }
            
            if (!isset($usingStations[$v['station_id']])) {

                $v['status'] = false;
                $v['desc']   = '该油站已下线，请司机以站点列表中显示的站点为准';
                continue;
            }

            if ($usingStations[$v['station_id']]['card_classify'] != 2) {

                $v['status'] = false;
                $v['desc']   = '该油站已下线，请司机以站点列表中显示的站点为准';
                continue;
            }

            if (!empty($orgStationLimitRule)) {

                if (isset($orgStationLimitRule["oil_station_rule"])) {

                    if (isset($orgStationLimitRule['oil_station_rule']['type']) and
                        isset($orgStationLimitRule['oil_station_rule']['typeVal'])) {

                        switch ($orgStationLimitRule['oil_station_rule']['type']) {

                            case '2':

                                if (!in_array($v['station_id'], array_column($orgStationLimitRule['oil_station_rule']
                                ['typeVal'], 'third_id'))) {

                                    $v['status'] = false;
                                    $v['desc']   = '站点被限定使用，不能加油，请司机联系车队长';
                                }
                                break;
                            case '3':

                                if (!in_array($usingStations[$v['station_id']]['pcode'],
                                    $orgStationLimitRule['oil_station_rule']['typeVal'])) {

                                    $v['status'] = false;
                                    $v['desc']   = '站点被限定使用，不能加油，请司机联系车队长';
                                }
                                break;
                            case '4':

                                if (!in_array($usingStations[$v['station_id']]['station_brand'],
                                    $orgStationLimitRule['oil_station_rule']['typeVal'])) {

                                    $v['status'] = false;
                                    $v['desc']   = '站点被限定使用，不能加油，请司机联系车队长';
                                }
                                break;
                            case '5':

                                if (!in_array($usingStations[$v['station_id']]['rebate_grade'],
                                    $orgStationLimitRule['oil_station_rule']['typeVal'])) {

                                    $v['status'] = false;
                                    $v['desc']   = '站点被限定使用，不能加油，请司机联系车队长';
                                }
                                break;
                        }
                    }
                }

                if (isset($v['oil_name']) && $v['oil_name']) {
                    if (isset($orgStationLimitRule['oil_name_rule']) && isset($orgStationLimitRule['oil_name_rule']['type']) && $orgStationLimitRule['oil_name_rule']['type'] == 2) {
                        if (!in_array(trim($v['oil_name']), $orgStationLimitRule['oil_name_rule']['typeVal'])) {
                            $v['status'] = false;
                            $v['desc']   =$v['oil_name'] . '被限定使用，不能加油，请司机联系车队长';
                        }
                    }else{
                        if( in_array(trim($v['oil_name']) ,[ "维修保养","润滑油类" ]) ){
                            $v['status'] = false;
                            $v['desc']   =$v['oil_name'] . '被限定使用，不能加油，请司机联系车队长';
                        }
                    }
                }

            }else{
                if (isset($v['oil_name']) && $v['oil_name']) {
                    if( in_array(trim($v['oil_name']) ,[ "维修保养","润滑油类" ]) ){
                        $v['status'] = false;
                        $v['desc']   =$v['oil_name'] . '被限定使用，不能加油，请司机联系车队长';
                    }
                }
            }
        }

        return $params['data'];
    }

    public static function getStationLimitList($params = [])
    {
        $orgInfo = Org::getByOrgCode($params['org_code']);
        if (!$orgInfo) {
            return ["stationLimitRuleList" => [], "stationIsAvailable" => ""];
        }
        $isAvailable = '可用';
        if (isset($params['station_id']) && $params['station_id']) {//验证该油站是否可用
            $limitRule        = self::getLimitData($orgInfo->orgcode);
            $stationCondition = GosOrgStationLimit::getStationCondition($limitRule);
            $stationData      = Station::Filter($stationCondition)->select("third_id")->where('card_classify', 2)->get();
            if ($stationData and !empty($stationData)) {
                $stationCodes = array_column($stationData->toArray(), 'third_id');
                if (!in_array($params['station_id'], $stationCodes)) {
                    $isAvailable = '不可用';
                }
            }
        }
        $org_list = self::splitOrg($orgInfo->orgcode);
        //$topOrgCode = substr((string), 0, 6);
        $data = GosOrgStationLimit::whereIn('org_code', $org_list)
                               ->get();

        return [
            'stationLimitRuleList' => $data ? $data->toArray() : [],
            'stationIsAvailable'   => empty($params['station_id']) ? '未指定油站' : $isAvailable,
        ];
    }

    public static function getStationLimitAll($params = [])
    {

        if (!empty($params['org_code'])) {

            $orgInfo = Org::getByOrgCode($params['org_code']);
        } else {

            if (empty($params['card_no'])) {

                throw new \Exception("卡号(子账号)或者司机手机号必填一项", 412005);
            }

            if (!empty($params['phone'])) {

                $cardQueryParams['driver_tel'] = $params['phone'];
            }

            if (!empty($params['card_no'])) {

                $cardQueryParams['vice_no'] = $params['card_no'];
            }

            $cardObject = CardVice::Filter($cardQueryParams)->select('org_id')->first();

            if (!$cardObject) {

                throw new \Exception("卡片(子账户)信息不存在", 412004);
            }

            $orgInfo = Org::getById($cardObject->org_id);
        }

        $isAvailable         = '可用';
        $topOrgCode          = substr((string)$orgInfo->orgcode, 0, 6);
        $data                = GosOrgStationLimit::whereIn('org_code', [$orgInfo->orgcode, $topOrgCode])
                                              ->get()
                                              ->toArray();
        $orgStationLimitDict = Helper::convertListToDict($data, 'org_code');
        $orgStationLimitRule = [];
        $condition           = [];

        // 如果当前机构没有配置油站限制规则,需要判断下顶级机构是否配置油站限制规则
        if (isset($orgStationLimitDict[$orgInfo->orgcode])) {

            $orgStationLimitRule = json_decode($orgStationLimitDict[$orgInfo->orgcode]['limit_rule'], true);
        } else {

            if (isset($orgStationLimitDict[$topOrgCode])) {

                $orgStationLimitRule = json_decode($orgStationLimitDict[$topOrgCode]['limit_rule'], true);
            }
        }

        if (!empty($orgStationLimitRule)) {

            if (isset($orgStationLimitRule["oil_station_rule"])) {

                if (isset($orgStationLimitRule['oil_station_rule']['type']) and
                    isset($orgStationLimitRule['oil_station_rule']['typeVal'])) {

                    switch ($orgStationLimitRule['oil_station_rule']['type']) {

                        case '2':

                            $condition['third_id'] = array_column($orgStationLimitRule['oil_station_rule']['typeVal'],
                                'third_id');
                            break;
                        case '3':

                            $condition['pcode'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                            break;
                        case '4':

                            $condition['station_brand'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                            break;
                        case '5':

                            $condition['rebate_grade'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                            break;
                    }
                }
            }
        }

        if (!isset($orgStationLimitDict[$orgInfo->orgcode])) {

            throw new \Exception("暂无数据", 4040001);
        }

        $availableStationCount = [];
        $realStationLimitRule  = json_decode($orgStationLimitDict[$orgInfo->orgcode]['limit_rule'], true);
        $stationData           = Station::Filter($condition)->select("third_id")->where('card_classify', 2)->get()->toArray();

        if ($stationData and !empty($stationData)) {

            $stationCodes = array_column($stationData, 'third_id');

            if (!in_array($params['station_id'], $stationCodes)) {

                $isAvailable = '不可用';
            }
        }

        switch ($realStationLimitRule['oil_station_rule']['type']) {
            case '1':

                $availableStationCount = [
                    'supplierCount' => 0,
                    'stationCount'  => 0,
                    'brandCount'    => 0,
                    'rebateCount'   => 0,
                ];
                break;
            case '2':

                $availableStationCount = [
                    'supplierCount' => 0,
                    'stationCount'  => count(array_values(array_column($realStationLimitRule['oil_station_rule']['typeVal'],
                        'third_id'))),
                    'brandCount'    => 0,
                    'rebateCount'   => 0,
                ];
                break;
            case '3':

                $availableStationCount = [
                    'supplierCount' => count($realStationLimitRule['oil_station_rule']['typeVal']),
                    'stationCount'  => 0,
                    'brandCount'    => 0,
                    'rebateCount'   => 0,
                ];
                break;
            case '4':

                $availableStationCount = [
                    'supplierCount' => 0,
                    'stationCount'  => 0,
                    'brandCount'    => count($realStationLimitRule['oil_station_rule']['typeVal']),
                    'rebateCount'   => 0,
                ];
                break;
            case '5':

                $availableStationCount = [
                    'supplierCount' => 0,
                    'stationCount'  => 0,
                    'brandCount'    => 0,
                    'rebateCount'   => $realStationLimitRule['oil_station_rule']['typeVal'],
                ];
                break;
        }
        $availableStationCount['oilNameCount'] = 0;
        if (isset($realStationLimitRule['oil_name_rule']) && isset($realStationLimitRule['oil_name_rule']['type'])) {
            switch ($realStationLimitRule['oil_name_rule']['type']) {
                case 2:
                    $availableStationCount['oilNameCount'] = count($realStationLimitRule['oil_name_rule']['typeVal']);
                    break;
                default:
                    $availableStationCount['oilNameCount'] = 0;
                    break;
            }
        }

        return [
            'creator'            => $orgStationLimitDict[$orgInfo->orgcode]['creator'],
            'id'                 => $orgStationLimitDict[$orgInfo->orgcode]['id'],
            'created_at'         => $orgStationLimitDict[$orgInfo->orgcode]['created_at'],
            'org_name'           => $orgInfo->org_name,
            'org_code'           => $orgInfo->orgcode,
            'available'          => $availableStationCount,
            'stationIsAvailable' => empty($params['station_id']) ? '未指定油站' : $isAvailable,
        ];
    }

    /**
     * @param array $params
     * @return mixed
     * ---------------------------------------------------
     * @throws \Exception
     * <AUTHOR> <<EMAIL>>
     * @since  2019-06-25 11:36
     */
    public static function getStationLimitDetail($params = [])
    {
        $data = GosOrgStationLimit::where('id', $params['id'])
                               ->first();

        if ($data) {

            $data                 = $data->toArray();
            $orgInfo              = Org::getByOrgCode($data['org_code']);
            $data['org_name']     = $orgInfo->org_name;
            $realStationLimitRule = json_decode($data['limit_rule'], true);
            $availableStation     = [];

            switch ($realStationLimitRule['oil_station_rule']['type']) {
                case '1':

                    $availableStation = [
                        'availableSupplier' => [],
                        'availableStation'  => [],
                        'availableBrand'    => [],
                        'availableRebate'   => [],
                    ];
                    break;
                case '2':

                    $thirdIds         = array_values(array_column($realStationLimitRule['oil_station_rule']['typeVal'],
                        'third_id'));
                    $stationData      = Station::Filter(['third_id' => $thirdIds])->get();
                    $availableStation = [
                        'availableSupplier' => [],
                        'availableStation'  => $stationData,
                        'availableBrand'    => [],
                        'availableRebate'   => [],
                    ];
                    break;
                case '3':

                    $availableStation = [
                        'availableSupplier' => $realStationLimitRule['oil_station_rule']['typeVal'],
                        'availableStation'  => [],
                        'availableBrand'    => [],
                        'availableRebate'   => [],
                    ];
                    break;
                case '4':

                    $availableStation = [
                        'availableSupplier' => [],
                        'availableStation'  => [],
                        'availableBrand'    => $realStationLimitRule['oil_station_rule']['typeVal'],
                        'availableRebate'   => [],
                    ];
                    break;
                case '5':

                    $availableStation = [
                        'availableSupplier' => [],
                        'availableStation'  => [],
                        'availableBrand'    => [],
                        'availableRebate'   => $realStationLimitRule['oil_station_rule']['typeVal'],
                    ];
                    break;
            }

            $availableStation['availableOilName'] = [];
            if (isset($realStationLimitRule['oil_name_rule']) && isset($realStationLimitRule['oil_name_rule']['type'])) {
                if ($realStationLimitRule['oil_name_rule']['type'] == 2) {
                    $availableStation['availableOilName'] = $realStationLimitRule['oil_name_rule']['typeVal'];
                }
            }

            $data['availableStation'] = $availableStation;
            return $data;
        }

        throw new \Exception("暂无数据", 4040001);

    }

    public static function exportStationLimitDetail($params = [])
    {

        if (!empty($params['org_code'])) {

            $orgInfo = Org::getByOrgCode($params['org_code']);
        } else {

            if (empty($params['card_no'])) {

                throw new \Exception("卡号(子账号)或者司机手机号必填一项", 412005);
            }

            if (!empty($params['phone'])) {

                $cardQueryParams['driver_tel'] = $params['phone'];
            }

            if (!empty($params['card_no'])) {

                $cardQueryParams['vice_no'] = $params['card_no'];
            }

            $cardObject = CardVice::Filter($cardQueryParams)->select('org_id')->first();

            if (!$cardObject) {

                throw new \Exception("卡片(子账户)信息不存在", 412004);
            }

            $orgInfo = Org::getById($cardObject->org_id);
        }

        $isAvailable         = true;
        $topOrgCode          = substr((string)$orgInfo->orgcode, 0, 6);
        $data                = GosOrgStationLimit::whereIn('org_code', [$orgInfo->orgcode, $topOrgCode])
                                              ->get()
                                              ->toArray();
        $orgStationLimitDict = Helper::convertListToDict($data, 'org_code');
        $orgStationLimitRule = [];
        $condition           = [];

        // 如果当前机构没有配置油站限制规则,需要判断下顶级机构是否配置油站限制规则
        if (isset($orgStationLimitDict[$orgInfo->orgcode])) {

            $orgStationLimitRule = json_decode($orgStationLimitDict[$orgInfo->orgcode]['limit_rule'], true);
        } else {

            if (isset($orgStationLimitDict[$topOrgCode])) {

                $orgStationLimitRule = json_decode($orgStationLimitDict[$topOrgCode]['limit_rule'], true);
            }
        }

        if (!empty($orgStationLimitRule)) {

            if (isset($orgStationLimitRule["oil_station_rule"])) {

                if (isset($orgStationLimitRule['oil_station_rule']['type']) and
                    isset($orgStationLimitRule['oil_station_rule']['typeVal'])) {

                    switch ($orgStationLimitRule['oil_station_rule']['type']) {

                        case '2':

                            $condition['third_id'] = array_column($orgStationLimitRule['oil_station_rule']['typeVal'],
                                'third_id');
                            break;
                        case '3':

                            $condition['pcode'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                            break;
                        case '4':

                            $condition['station_brand'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                            break;
                        case '5':

                            $condition['rebate_grade'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                            break;
                    }
                }
            }
        }

        if (!isset($orgStationLimitDict[$orgInfo->orgcode])) {

            throw new \Exception("暂无数据", 4040001);
        }

        $stationData = Station::Filter($condition)->get()->toArray();

        if ($stationData and !empty($stationData)) {

            $limitStation = [];

            foreach ($stationData as $v) {

                if ($v['card_classify'] == 2) {

                    $limitStation[] = $v['third_id'];
                }
            }

            if (!in_array($params['station_id'], $limitStation)) {

                $isAvailable = false;
            }
        }

        $realStationLimitRule = json_decode($orgStationLimitDict[$orgInfo->orgcode]['limit_rule'], true);
        $availableStation     = [];

        switch ($realStationLimitRule['oil_station_rule']['type']) {
            case '1':

                $availableStation = [
                    'availableSupplier' => [],
                    'availableStation'  => [],
                    'availableBrand'    => [],
                    'availableRebate'   => [],
                ];
                break;
            case '2':

                $availableStation = [
                    'availableSupplier' => [],
                    'availableStation'  => $stationData,
                    'availableBrand'    => [],
                    'availableRebate'   => [],
                ];
                break;
            case '3':

                $availableStation = [
                    'availableSupplier' => $realStationLimitRule['oil_station_rule']['typeVal'],
                    'availableStation'  => [],
                    'availableBrand'    => [],
                    'availableRebate'   => [],
                ];
                break;
            case '4':

                $availableStation = [
                    'availableSupplier' => [],
                    'availableStation'  => [],
                    'availableBrand'    => $realStationLimitRule['oil_station_rule']['typeVal'],
                    'availableRebate'   => [],
                ];
                break;
            case '5':

                $availableStation = [
                    'availableSupplier' => [],
                    'availableStation'  => [],
                    'availableBrand'    => [],
                    'availableRebate'   => $realStationLimitRule['oil_station_rule']['typeVal'],
                ];
                break;
        }

        $availableStation['availableOilName'] = [];
        if (isset($realStationLimitRule['oil_name_rule']) && isset($realStationLimitRule['oil_name_rule']['type'])) {
            if ($realStationLimitRule['oil_name_rule']['type'] == 2) {
                $availableStation['availableOilName'] = $realStationLimitRule['oil_name_rule']['typeVal'];
            }
        }

        $data['availableStation'] = $availableStation;

        return [
            'creator'            => $orgStationLimitDict[$orgInfo->orgcode]['creator'],
            'id'                 => $orgStationLimitDict[$orgInfo->orgcode]['id'],
            'created_at'         => $orgStationLimitDict[$orgInfo->orgcode]['created_at'],
            'org_name'           => $orgInfo->org_name,
            'org_code'           => $orgInfo->orgcode,
            'availableStation'   => $availableStation,
            'stationIsAvailable' => $isAvailable,
        ];
    }

    //获取油站限定
    public static function getLimitData($orgCode)
    {
        if (empty($orgCode)) {
            return [];
        }
        //2020-04-03逻辑改为逐级找他的父集的规则 ENINET-1376
        $org_list = self::splitOrg($orgCode);
        $_data    = GosOrgStationLimit::whereIn('org_code', $org_list)
                                   ->select('org_code', 'limit_rule')
                                   ->get();
        if ($_data) {
            $data = $_data->toArray();
        }
        $orgStationLimitDict = Helper::convertListToDict($data, 'org_code');
        $orgStationLimitRule = [];

        //新逻辑
        foreach ($org_list as $item) {
            if (isset($orgStationLimitDict[$item])) {
                $rule = json_decode($orgStationLimitDict[$item]['limit_rule'], true);
                //处理子级机构无限制，顶级有限制不生效的Bug
                if (isset($rule['oil_name_rule']) && isset($rule['oil_name_rule']['type']) && $rule['oil_name_rule']['type'] == 1 && isset($rule['oil_station_rule']) && isset($rule['oil_station_rule']['type']) && $rule['oil_station_rule']['type'] == 1) {
                    continue;
                }
                $orgStationLimitRule = $rule;

                break;
            }
        }

        return $orgStationLimitRule;
    }

    //根据油站限定规则,获取油站数据条件
    public static function getStationCondition($orgStationLimitRule = [])
    {
        $condition = [];
        if (!empty($orgStationLimitRule)) {
            if (isset($orgStationLimitRule["oil_station_rule"])) {
                if (isset($orgStationLimitRule['oil_station_rule']['type']) and
                    isset($orgStationLimitRule['oil_station_rule']['typeVal'])) {
                    switch ($orgStationLimitRule['oil_station_rule']['type']) {
                        case '2':
                            $condition['third_id'] = array_column($orgStationLimitRule['oil_station_rule']['typeVal'],
                                'third_id');
                            break;
                        case '3':
                            $condition['pcode'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                            break;
                        case '4':
                            $condition['station_brand'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                            break;
                        case '5':
                            $condition['rebate_grade'] = $orgStationLimitRule['oil_station_rule']['typeVal'];
                            break;
                    }
                }
            }
            if (isset($orgStationLimitRule["oil_name_rule"])) {
                if (isset($orgStationLimitRule['oil_name_rule']['type']) and
                    isset($orgStationLimitRule['oil_name_rule']['typeVal'])) {
                    switch ($orgStationLimitRule['oil_name_rule']['type']) {
                        case '2':
                            $oil_name_limit_content = $orgStationLimitRule['oil_name_rule']['typeVal'];
                            $stationIds             = StationServicePrice::getStationByOilName(['oil_name_valIn' => $oil_name_limit_content]);
                            if ($stationIds) {
                                if (isset($condition['third_id']) && count($condition['third_id']) > 0) {
                                    $lastIds = array_intersect($condition['third_id'], $stationIds->toArray());
                                } else {
                                    $lastIds = $stationIds->toArray();
                                }
                                $condition['third_id'] = $lastIds;
                            }
                            break;
                    }
                }
            }
        }
        return $condition;
    }
}