<?php
/**
 * 机构付款公司表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/11/29
 * Time: 17:18:40
 */

namespace Models;

use Framework\Log;
use Fuel\Defines\OilType;
use Fuel\Defines\PayCompanyStatus;
use Fuel\Defines\ReceiptApplyStatus;
use Fuel\Defines\ReceiptStatus;
use Fuel\Service\OpenReceipt;
use Fuel\Service\OrgChangeOperatorService;
use Fuel\Service\UploadService;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Defines\PayConfigStatus;

class OilPayCompany extends \Framework\Database\Model
{
    protected $table = 'oil_pay_company';

    protected $guarded  = ["id"];
    protected $fillable = [
        'orgroot', 'company_name', 'status', 'charge_total', 'charge_total_date', 'dynamic_money', 'remark', 'creator_id',
        'taxpayer_no', 'contract_id', 'creator_name', 'other_creator_id', 'other_creator', 'last_operator_id', 'last_operator',
        'createtime', 'updatetime', 'is_check_contract', 'contract_bind_time','is_to_crm','new_accountid'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function ReceiptApply()
    {
        return $this->belongsTo('Models\OilReceiptApply', 'pay_company_id', 'id');
    }

    public function receiptTitle()
    {
        return $this->hasOne('Models\OilReceiptTitle', 'pay_company_id', 'id');
    }

    public function org()
    {
        return $this->belongsTo('Models\OilOrg', 'orgroot', 'orgcode');
    }

    public function changeRecord()
    {
        return $this->hasOne('Models\OilPayCompanyChangeRecord', 'pay_company_id', 'id');
    }

    public function contract()
    {
        return $this->belongsTo('Models\OilContract', 'contract_id', 'id');
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By contract_id
        if (isset($params['contract_id']) && $params['contract_id'] != '') {
            $query->where('contract_id', '=', $params['contract_id']);
        }

        //Search By taxpayer_no
        if (isset($params['taxpayer_no']) && $params['taxpayer_no'] != '') {
            $query->where('taxpayer_no', '=', $params['taxpayer_no']);
        }

        //Search By orgroot
        if (isset($params['orgroot']) && $params['orgroot'] != '') {
            $query->where('orgroot', '=', $params['orgroot']);
        }

        if (! empty($params['orgroot_pre_like'])) {
            $query->where('orgroot', 'like', $params['orgroot_pre_like'].'%');
        }

        //Search By orgrootIn
        if (isset($params['orgroot']) && $params['orgroot'] != '') {
            $query->where('orgroot', '=', $params['orgroot']);
        }

        //Search By orgrootLk
        if (isset($params['orgrootLk']) && $params['orgrootLk'] != '') {
            $query->where('orgroot', 'like', $params['orgrootLk'] . "%");
        }

        //Search By orgroot_in
        if (isset($params['orgroot_in']) && $params['orgroot_in'] != '') {
            if (is_array($params['orgroot_in'])) {
                $query->whereIn('orgroot', $params['orgroot_in']);
            } else {
                $query->where('orgroot', '=', $params['orgroot_in']);
            }
        }

        //Search By company_name
        if (isset($params['company_name']) && $params['company_name'] != '') {
            $query->where('company_name', '=', $params['company_name']);
        }

        //Search By company_nameLike
        if (isset($params['company_nameLike']) && $params['company_nameLike'] != '') {
            $query->where('company_name', 'like', '%' . $params['company_nameLike'] . '%');
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By creator
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (! empty($params['createtimeLe'])) {
            $query->where('createtime', '<', $params['createtimeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By createtimepPGe
        if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
            $query->where('oil_pay_company.updatetime', '>=', $params['updatetimeGe']);
        }

        //Search By createtimeLe
        if (isset($params['updatetimeLt']) && $params['updatetimeLt'] != '') {
            $query->where('oil_pay_company.updatetime', '<', $params['updatetimeLt']);
        }

        if (isset($params['last_operatorNeq']) && $params['last_operatorNeq'] == '1') {
            $query->whereNotNull('oil_pay_company.last_operator');
        }

        return $query;
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilterForReceiptTitle($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_receipt_title.id', '=', $params['id']);
        }

        //Search By id_nq
        if (isset($params['id_nq']) && $params['id_nq'] != '') {
            $query->where('oil_receipt_title.id', '!=', $params['id_nq']);
        }

        //Search By pay_company_id
        if (isset($params['pay_company_id']) && $params['pay_company_id'] != '') {
            $query->where('oil_receipt_title.pay_company_id', '=', $params['pay_company_id']);
        }

        //Search By orgroot
        if (isset($params['orgroot']) && $params['orgroot'] != '') {
            $query->where('oil_pay_company.orgroot', '=', $params['orgroot']);
        }

        //Search By corp_nameLike
        if (isset($params['corp_nameLike']) && $params['corp_nameLike']) {
            $query->where('oil_receipt_title.corp_name', 'like', '%' . $params['corp_nameLike'] . '%');
        }
        if (isset($params['corp_name']) && $params['corp_name']) {
            $query->where('oil_receipt_title.corp_name', '=', $params['corp_name']);
        }

        //Search By receipt_type
        if (isset($params['receipt_type']) && $params['receipt_type'] != '') {
            if ($params['receipt_type'] == '未维护') {
                $query->whereNull('oil_receipt_title.receipt_type');
            } else {
                $query->where('oil_receipt_title.receipt_type', '=', $params['receipt_type']);
            }
        }

        //Search By corp_addr
        if (isset($params['corp_addr']) && $params['corp_addr'] != '') {
            $query->where('oil_receipt_title.corp_addr', '=', $params['corp_addr']);
        }

        //Search By corp_tel
        if (isset($params['corp_tel']) && $params['corp_tel'] != '') {
            $query->where('oil_receipt_title.corp_tel', '=', $params['corp_tel']);
        }

        //Search By taxpayer_no
        if (isset($params['taxpayer_no']) && $params['taxpayer_no'] != '') {
            $query->where('oil_receipt_title.taxpayer_no', '=', $params['taxpayer_no']);
        }

        //Search By bank_name
        if (isset($params['bank_name']) && $params['bank_name'] != '') {
            $query->where('oil_receipt_title.bank_name', '=', $params['bank_name']);
        }

        //Search By bank_account
        if (isset($params['bank_account']) && $params['bank_account'] != '') {
            $query->where('oil_receipt_title.bank_account', '=', $params['bank_account']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_receipt_title.status', '=', $params['status']);
        }
        if (isset($params['multi_status']) && $params['multi_status'] != '') {
            $query->whereIn('oil_receipt_title.status', $params['multi_status']);
        }

        //Search By admin_remark
        if (isset($params['admin_remark']) && $params['admin_remark'] != '') {
            $query->where('oil_receipt_title.admin_remark', '=', $params['admin_remark']);
        }

        //Search By custom_remark
        if (isset($params['custom_remark']) && $params['custom_remark'] != '') {
            $query->where('oil_receipt_title.custom_remark', '=', $params['custom_remark']);
        }

        //Search By corp_licence
        if (isset($params['corp_licence']) && $params['corp_licence'] != '') {
            $query->where('corp_licence', '=', $params['corp_licence']);
        }

        //Search By exclusive_custom
        if (isset($params['exclusive_custom']) && $params['exclusive_custom'] != '') {
            $query->where('oil_org.exclusive_custom', '=', $params['exclusive_custom']);
        }

        //Search By taxpayer_licence
        if (isset($params['taxpayer_licence']) && $params['taxpayer_licence'] != '') {
            $query->where('taxpayer_licence', '=', $params['taxpayer_licence']);
        }

        //Search By tax_licence
        if (isset($params['tax_licence']) && $params['tax_licence'] != '') {
            $query->where('tax_licence', '=', $params['tax_licence']);
        }

        //Search By bank_licence
        if (isset($params['bank_licence']) && $params['bank_licence'] != '') {
            $query->where('bank_licence', '=', $params['bank_licence']);
        }

        //Search By is_previous
        if (isset($params['is_previous']) && $params['is_previous'] != '') {
            $query->where('is_previous', '=', $params['is_previous']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By other_creator_id
        if (isset($params['other_creator_id']) && $params['other_creator_id'] != '') {
            $query->where('other_creator_id', '=', $params['other_creator_id']);
        }

        //Search By other_creator
        if (isset($params['other_creator']) && $params['other_creator'] != '') {
            $query->where('other_creator', '=', $params['other_creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtimeGe
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_receipt_title.createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtimeLe
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('oil_receipt_title.createtime', '<=', $params['createtimeLe']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetimeGe
        if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
            $query->where('oil_receipt_title.updatetime', '>=', $params['updatetimeGe']);
        }

        //Search By updatetimeLe
        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('oil_receipt_title.updatetime', '<=', $params['updatetimeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * 机构付款公司表 列表查询
     *
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data            = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 500;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        //Capsule::connection()->enableQueryLog();
        $sqlObj = OilPayCompany::Filter($params)
                               ->with([
                                   'org'          => function ($query) {
                                       $query->select('id', 'org_name', 'orgcode', 'status', 'crm_id');
                                   },
                                   'receiptTitle' => function ($query) {
                                       $query->select("id", "pay_company_id", "corp_name");
                                   },
                                   'changeRecord' => function ($query) {
                                       $query->select("id", "pay_company_id", "company_name");
                                   },
                                   'contract'     => function ($query) {
                                       $query->select('id', 'no');
                                   }
                               ]);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            if (!isset($params['limit'])) {
                $params['page']  = 1;
                $params['limit'] = 10000;
            }
            $data = $sqlObj->orderBy('id', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        foreach ($data as &$v) {
            $v->changeTotal              = count($v->changeRecord);
            $v->total              = count($v->receiptTitle);
            $v->org_name           = $v->org->org_name;
            $v->_status            = PayConfigStatus::getById($v->status);
            $v->_is_check_contract = $v->is_check_contract == 10 ? '是' : '否';
            $v->contract_no        = $v->contract->no;
        }

        return $data;
    }

    /**
     * @title   以付款公司为维度查询发票抬头
     * @desc
     * @param array $params
     * @return array
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    static public function getListForReceiptTitle(array $params)
    {
        $data             = [];
        $params['is_del'] = '0';
        $params['limit']  = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']   = isset($params['page']) ? $params['page'] : 1;

//        Capsule::connection()->enableQueryLog();
        if (isset($params['mode']) && $params['mode'] == 10) {
            $sqlObj = OilReceiptTitle::Filter($params)
                                     ->select(Capsule::connection()->raw("oil_receipt_title.*,oil_pay_company.id pay_id,
                oil_pay_company.company_name as _pay_company_id,
                oil_pay_company.status as company_status, oil_org.exclusive_custom,gsp_sys_users.true_name"));
            $sqlObj->leftJoin('oil_pay_company', function ($query) {
                $query->on('oil_pay_company.id', '=', 'oil_receipt_title.pay_company_id')->where('oil_receipt_title.is_del', '=', '0');
            })->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.orgcode', '=', 'oil_receipt_title.orgroot')->where('oil_org.is_del', '=', '0');
            });
        } else {
            $sqlObj = self::FilterForReceiptTitle($params)->select(Capsule::connection()->raw("oil_receipt_title.*,
            oil_pay_company.orgroot,oil_pay_company.id pay_id,oil_pay_company.company_name as _pay_company_id,
            oil_pay_company.status as company_status, oil_org.exclusive_custom,gsp_sys_users.true_name"))
                          ->leftJoin('oil_receipt_title', function ($query) {
                              $query->on('oil_pay_company.id', '=', 'oil_receipt_title.pay_company_id')->where('oil_receipt_title.is_del', '=', '0');
                          })->leftJoin('oil_org', function ($query) {
                    $query->on('oil_org.orgcode', '=', 'oil_pay_company.orgroot')->where('oil_org.is_del', '=', '0');
                });
        }

        $sqlObj->leftJoin('gsp_sys_users', 'gsp_sys_users.id', '=', 'oil_receipt_title.creator_id')
               ->orderBy('oil_receipt_title.updatetime', 'desc');

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

//        Log::debug('sql--' . \GuzzleHttp\json_encode(Capsule::connection()->getQueryLog()), [], 'zzg');

        $orgcodeArr = [];
        foreach ($data as $v) {
            $orgcodeArr[] = $v->orgroot;
        }
        if ($orgcodeArr) {
            $orgInfo = OilOrg::getByOrgCodesMap($orgcodeArr);
            foreach ($data as &$v) {
                $v->receipt_type      = $v->receipt_type ? $v->receipt_type : '未维护';
                $statusInfo           = ReceiptStatus::getById($v->status);
                $v->_status           = $statusInfo['status'];
                $v->org_name          = isset($orgInfo[$v->orgroot]) ? $orgInfo[$v->orgroot] : '';
                $v->_exclusive_custom = \Fuel\Defines\ExclusiveCustom::getById($v->exclusive_custom);
                $v->creator_name      = $v->other_creator ? $v->other_creator : $v->true_name;
                if (is_null($v->pay_company_id) && is_null($v->pay_id)) {
                    $v->company_status = '无';
                } else {
                    $v->company_status = PayCompanyStatus::getById($v->company_status);
                }

                $v->corp_licence = UploadService::getOssSignUrl($v->corp_licence);
                $v->taxpayer_licence = UploadService::getOssSignUrl($v->taxpayer_licence);
                $v->tax_licence = UploadService::getOssSignUrl($v->tax_licence);
                $v->bank_licence = UploadService::getOssSignUrl($v->bank_licence);
            }
        }

        return $data;
    }

    /**
     * 机构付款公司表 详情查询
     *
     * @param array   $params
     * @param integer $status
     * @return object
     */
    static public function getById(array $params, $status = 1)
    {
        \helper::argumentCheck(['id'], $params);

        $sqlObj = OilPayCompany::where('id', $params['id']);
        if (!empty($status)) {
            $sqlObj->where('status', $status);
        }

        return $sqlObj->first();
    }

    /**
     * 悲观锁查询
     *
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilPayCompany::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * @title   获取单条记录
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    static public function getSingleRecord(array $params)
    {
        return self::Filter($params)->first();
    }

    /**
     * 机构付款公司表 新增
     *
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        global $app;
        if (isset($app->myAdmin)) {
            $params['creator_id']   = $app->myAdmin->id;
            $params['creator_name'] = $app->myAdmin->true_name;
        }

        if (isset($params['company_name']) && $params['company_name']) {
            $params['company_name'] = trim($params['company_name']);
        }

        $params['other_creator_id'] = isset($params['other_creator_id']) ? $params['other_creator_id'] : NULL;
        $params['other_creator']    = isset($params['other_creator']) ? $params['other_creator'] : NULL;
        $params['createtime']       = \helper::nowTime();

        return OilPayCompany::create($params);
    }

    /**
     * 批量新增付款公司
     *
     * @param array $params
     * @return mixed
     */
    static public function batchInsert(array $params)
    {
        return OilPayCompany::insert($params);
    }

    /**
     * 机构付款公司表 编辑
     *
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        if (isset($params['company_name']) && $params['company_name']) {
            $params['company_name'] = trim($params['company_name']);
        }
        return OilPayCompany::find($params['id'])->update($params);
    }

    //  /**
    //  * 机构付款公司表 编辑
    //  *
    //  * @param array $params
    //  * @return mixed
    //  */
    static public function newedit($id,$params)
    {
        return OilPayCompany::where('id',$id)->update($params);
    }


    /**
     * 机构付款公司表 根据ids删除或批量删除
     *
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilPayCompany::destroy($params['ids']);
    }

    /**
     * 校验付款公司是否存在
     *
     * @param array $params
     * @todo 此处逻辑有误
     */
    static public function checkPayCompany(array $params)
    {
        $orgcode = "";
        \helper::argumentCheck(['org_id'], $params);
        //根据org_id获取顶级机构的org_id
        $orgInfo = OilOrg::getById(['id' => $params['org_id']]);

        //获取付款信息
        $payCompany = OilPayCompany::where('orgroot', substr($orgInfo->orgcode, 0, 6))->where('status', 1)->first();

        if (isset($params['company_name']) && $params['company_name']) {
            $params['company_name'] = trim($params['company_name']);
            if(in_array((substr($orgInfo->orgcode, 0, 6)),["200MQT","201XW3"])){
                $orgcode =  substr($orgInfo->orgcode, 0, 10); //无车承运
            } else {
                $orgcode =  substr($orgInfo->orgcode, 0, 6);
            }
            $payCompany = OilPayCompany::where('orgroot', $orgcode)->where('company_name',$params['company_name'])->where('status', 1)->first();
        }
        if ($payCompany === NULL) {
            throw new \RuntimeException('付款公司不存在', 2);
        }
        return $payCompany;
    }

    /**
     * 根据机构获取付款公司
     *
     * @param array $params
     * @return array
     */
    static public function getPayNameByOrg(array $params)
    {

        $sqlObj = self::select('oil_pay_company.id as id', 'company_name as pay_name', 'orgroot', "company_name")
                      ->where('oil_pay_company.status', 1);
        if (isset($params['orgcode']) && $params['orgcode']) {
            $sqlObj->where('orgroot', 'like', substr($params['orgcode'], 0, 6) . '%');
        } elseif (isset($params['newOrgCode']) && $params['newOrgCode']) {
            $sqlObj->where('orgroot', $params['newOrgCode']);
        } elseif (isset($params['company_nameLike']) && $params['company_nameLike']) {
            $sqlObj->where('company_name', 'like', '%' . $params['company_nameLike'] . '%');
        } elseif (isset($params['keyword']) && $params['keyword']) {
            $sqlObj->where(function ($query) use ($params) {
                $query->where('orgroot', 'like', trim($params['keyword']) . '%')
                      ->orWhere('company_name', 'like', '%' . trim($params['keyword']) . '%');
            });
        }
        list($user_type,$operator_id) = authCheck();
        if ($operator_id){
            $sqlObj->leftJoin("oil_org","oil_pay_company.orgroot","=","orgcode")->dataRange();
        }
        //查询兼容内部公司
        return $sqlObj->get();
    }

    static public function getByOrgrootCompanyName(array $params)
    {
        \helper::argumentCheck(['orgroot', 'company_name'], $params);
        $sqlObj = OilPayCompany::where('orgroot', $params['orgroot'])->where('company_name', $params['company_name']);
        if (isset($params['id'])) {
            $sqlObj->where('id', '!=', $params['id']);
        }

        return $sqlObj->first();
    }

    static public function getByOrgTreeCompanyName(array $params)
    {
        \helper::argumentCheck(['orgroot', 'company_name'], $params);
        $sqlObj = OilPayCompany::where('orgroot','like', $params['orgroot'].'%')->where('company_name', $params['company_name']);
        if (isset($params['id'])) {
            $sqlObj->where('id', '!=', $params['id']);
        }

        return $sqlObj->first();
    }

    static public function getByOrgrootCompanyNameForLock(array $params)
    {
        \helper::argumentCheck(['orgroot', 'company_name'], $params);
        $sqlObj = OilPayCompany::lockForUpdate()->where('orgroot', $params['orgroot'])->where('company_name', $params['company_name']);
        if (isset($params['id'])) {
            $sqlObj->where('id', '!=', $params['id']);
        }

        return $sqlObj->first();
    }

    static public function getByOrgcodeCompanyName(array $params)
    {
        \helper::argumentCheck(['orgcode', 'company_name'], $params);
        $sqlObj = OilPayCompany::where('orgroot', $params['orgcode'])->where('company_name', $params['company_name'])
                               ->where('status', 1);
        if (isset($params['id'])) {
            $sqlObj->where('id', '!=', $params['id']);
        }

        return $sqlObj->first();
    }

    static public function getByIdArrMap(array $idArr, $value = 'company_name', $key = 'id')
    {
        return self::whereIn('id', $idArr)->lists($value, $key)->toArray();
    }

    static public function getByOrgrootArrMap(array $orgrootArr, $value = 'company_name', $key = 'id')
    {
        return self::whereIn('orgroot', $orgrootArr)->where('status', 1)->lists($value, $key)->toArray();
    }

    static public function getLikeOrgRootArrMap($orgcode, $value = 'company_name', $key = 'id')
    {
        return self::where('orgroot', 'like', $orgcode . "%")->where('status', 1)->lists($value, $key)->toArray();
    }

    static public function getByPayCompanyName($payCompanyName)
    {
        $name = [$payCompanyName];
        if( stripos($payCompanyName,")") !== false || stripos($payCompanyName,"(") !== false ){
            $_tmpName = str_replace("(","（",$payCompanyName);
            $_tmpName = str_replace(")","）",$_tmpName);
            array_push($name,$_tmpName);
        }
        if( stripos($payCompanyName,"）") !== false || stripos($payCompanyName,"（") !== false ){
            $_tmpName = str_replace("）",")",$payCompanyName);
            $_tmpName = str_replace("（","(",$_tmpName);
            array_push($name,$_tmpName);
        }
        return self::leftJoin('oil_org', "oil_org.orgcode", '=', 'oil_pay_company.orgroot')
                   ->whereIn('oil_pay_company.company_name', $name)
                   ->where('oil_pay_company.status', 1)
                   ->where("oil_org.is_del", "=", 0)
                   ->get();
    }

    static public function invoiceTitleQuotaSearch(array $params)
    {
        Capsule::connection()->disableQueryLog();
//        Capsule::connection()->enableQueryLog();

        $data = NULL;
        if ($params['receipt_mode'] == 2) { //独立核算
            $sqlObj = OilPayCompany::leftJoin('oil_org', 'oil_org.orgcode', '=', 'oil_pay_company.orgroot');
        } else {
            $sqlObj = OilPayCompany::leftJoin('oil_org', Capsule::Raw("LEFT(oil_org.orgcode,6)"), '=', 'oil_pay_company.orgroot');
        }
        $sqlObj = $sqlObj->where("oil_org.is_del", "=", 0)->where("oil_pay_company.status", "=", 1)
                         ->leftJoin('oil_receipt_title', 'oil_pay_company.id', '=', 'oil_receipt_title.pay_company_id')
                         ->select('oil_pay_company.charge_total_date', 'oil_org.orgcode as orgroot', 'oil_org.org_name',
                             'oil_org.first_apply_receipt', 'oil_org.id as org_id', 'oil_receipt_title.id as receipt_title_id',
                             'oil_receipt_title.corp_name', 'oil_receipt_title.receipt_type', 'oil_pay_company.id as pay_company_id',
                             'oil_org.exclusive_custom', 'oil_org.is_recepit_nowtime', 'oil_org.is_test', 'oil_org.receipt_mode',
                             'oil_pay_company.company_name', 'oil_pay_company.remark','oil_org.operators_id as operators_id',
                             'oil_org.can_change_sign')
                         ->whereNotNull('oil_org.id')
                         ->where('oil_receipt_title.status', '=', 1)
                         ->where('oil_receipt_title.is_del', '=', 0)
            //->whereNotNull('oil_receipt_title.orgroot')
                         ->whereNotNull('oil_pay_company.id');

        if (isset($params['exclusive_custom']) && $params['exclusive_custom']) {
            $sqlObj->where('oil_org.exclusive_custom', $params['exclusive_custom']);
        }

        /*if(isset($params['operators_id']) && !empty($params['operators_id'])){
            $sqlObj->where('oil_org.operators_id',$params['operators_id']);
        }*/

        if (isset($params['is_recepit_nowtime']) && $params['is_recepit_nowtime']) {
            $sqlObj->where('oil_org.is_recepit_nowtime', $params['is_recepit_nowtime']);
        }

        if (isset($params['is_have_corp']) && $params['is_have_corp'] == 1) {
            $sqlObj->whereNull('oil_receipt_title.orgroot');
        } elseif (isset($params['is_have_corp']) && $params['is_have_corp'] == 2) {
            $sqlObj->whereNotNull('oil_receipt_title.orgroot');
        }

        if (isset($params['is_first_receipt_apply']) && $params['is_first_receipt_apply'] == 1) {
            $sqlObj->whereNotNull('oil_org.first_apply_receipt');
        } elseif (isset($params['is_first_receipt_apply']) && $params['is_first_receipt_apply'] == 2) {
            $sqlObj->whereNull('oil_org.first_apply_receipt');
        }

        if (isset($params['pay_company_name']) && $params['pay_company_name']) {
            $sqlObj->where('oil_pay_company.company_name', 'like', '%' . $params['pay_company_name'] . '%');
        }

        if (isset($params['type']) && $params['type'] == 'credit') {
            $sqlObj->leftJoin('oil_credit_account', 'oil_credit_account.org_id', '=', 'oil_org.id')
                   ->whereNotNull('oil_credit_account.id');
        }

        if (isset($params['is_test']) && $params['is_test']) {
            $sqlObj->where('oil_org.is_test', $params['is_test']);
        }

        $sqlObj->groupBy('oil_pay_company.id', 'oil_receipt_title.id');
        $orgIds = [];

        if (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['take']) && isset($params['skip'])) {
//            $tradesObj = OilCardViceTrades::where('id', '>', 0);
//            if ((isset($params['createtimeGe']) && $params['createtimeGe']) || (isset($params['createtimeLe']) && $params['createtimeLe'])) {
//                if (isset($params['createtimeGe']) && $params['createtimeGe']) {
//                    $tradesObj->where('trade_time', '>=', $params['createtimeGe']);
//                }
//                if (isset($params['createtimeLe']) && $params['createtimeLe']) {
//                    $tradesObj->where('trade_time', '<=', $params['createtimeLe']);
//                }
//            }
//
//            if (!$orgIds) {
//                $orgIds = $tradesObj->whereNotNull('org_id')->groupBy('org_id')->pluck('org_id')->toArray();
//            }

//            $sqlObj->whereIn('oil_org.id', $orgIds);
            if ($params['receipt_mode'] == 1) {
                if (isset($params['org_id']) && $params['org_id']) {
                    $sqlObj->where('oil_org.id', $params['org_id']);
                }
            } else {
                $sqlObj->where('oil_org.orgcode', '=', $params['hiddenOrgCode']);
                //$sqlObj->whereRaw("LEFT(oil_org.orgcode,6) = '".substr($params['hiddenOrgCode'],0,6)."'");
            }

            $data = $sqlObj->offset(intval($params['skip']))->limit(intval($params['take']))->get()->toArray();
        } elseif (isset($params['_export']) && $params['_export'] == 1) {
            $orgIds = [];
            if ((isset($params['createtimeGe']) && $params['createtimeGe']) || (isset($params['createtimeLe']) && $params['createtimeLe'])) {
                $tradesObj = OilCardViceTrades::where('id', '>', 0);
                if (isset($params['createtimeGe']) && $params['createtimeGe']) {
                    $tradesObj->where('trade_time', '>=', $params['createtimeGe']);
                }
                if (isset($params['createtimeLe']) && $params['createtimeLe']) {
                    $tradesObj->where('trade_time', '<=', $params['createtimeLe']);
                }
                $orgIds = $tradesObj->whereNotNull('org_id')->groupBy('org_id')->pluck('org_id')->toArray();
            }

            if ($orgIds) {
                $sqlObj->whereIn('oil_org.id', $orgIds);
            }

            if ($params['receipt_mode'] == 1) {
                if (isset($params['org_id']) && $params['org_id']) {
                    $sqlObj->where('oil_org.id', $params['org_id']);
                }
            } else {
                $sqlObj->where('oil_org.orgcode', '=', $params['hiddenOrgCode']);
                //$sqlObj->whereRaw("LEFT(oil_org.orgcode,6) = '".substr($params['hiddenOrgCode'],0,6)."'");
            }

            $data = $sqlObj->get()->toArray();
        } else {
            //\helper::argumentCheck(['org_id'],$params);
            if (!$params['org_id'] && !$params['pay_company_name']) {
                throw new \RuntimeException('请选择申请机构或付款公司', 2);
            }

            if ($params['receipt_mode'] == 1) {
                if (isset($params['org_id']) && $params['org_id']) {
                    $sqlObj->where('oil_org.id', $params['org_id']);
                }
            } else {
                $sqlObj->where('oil_org.orgcode', '=', $params['hiddenOrgCode']);
                //$sqlObj->whereRaw("LEFT(oil_org.orgcode,6) = '".substr($params['hiddenOrgCode'],0,6)."'");
            }

            if (isset($params['pay_company_id']) && $params['pay_company_id']) {
                $sqlObj->where('oil_pay_company.id', $params['pay_company_id']);
            }

            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page'])->toArray();
        }

//        $sql = Capsule::connection()->getQueryLog();
//        Log::error('$data:' . var_export($sql, TRUE), [], 'receiptQuotaDayStatistic');
        $data = self::receiptNewFormat($params, $data);

        return $data;
    }


    static public function receiptNewFormat($params, $data)
    {
        $dataList        = [];
        $totalTradesList = [];
        if ($data) {
            if (isset($params['limit'])) {
                $params['page'] = intval($params['page']) > 0 ? intval($params['page']) : 1;
                $index          = 1 + ($params['page'] - 1) * $params['limit'];
            }

            $result      = isset($data['data']) && count($data['data']) ? $data['data'] : $data;
            $totalTrades = $no_receipt_trades = $fanliChargeAmount = [];

            $operatorMap = OilOperators::getIdMapName("company_name");
            foreach ($result as &$v) {
                if(!isset($v['pay_company_id']) || !$v['pay_company_id']){
                    continue;
                }

                //针对返利审核&动力宝账单，只统计到大连运营商or维持原来逻辑
                $params['is_direct_dl'] = $v['operators_id'] == 2 ? 1 : 2;
                $change = [];
                $params['receipt_operator_id'] = '';
                $isEndTime = "";
                //receipt_mode 开票方式 1:集中，2:独立
                //todo 机构上存在，能否换签的标签，如果客户不可换签，前端传的运营商不生效，直接只为空，维持原来的统计逻辑
                $changeData = OrgChangeOperatorService::getOrgChangeData( ['org_code'=>$v['orgroot']] );
                //$isBreak = false;
                if(isset($changeData['changeList']) && count($changeData['changeList']) > 0){
                    foreach ($changeData['changeList'] as $_sub){
                        //下游开票额度:1已释放;2已锁定
                        if( $_sub->status == OilOrgChangeOperatorLog::SIGN_SUCCESS && $_sub->down_receipt_status == 1 ){
                            if( isset($params['operators_id']) && !empty($params['operators_id']) ){
                                $change[$_sub->orgroot][$params['operators_id']] = $params['operators_id'];
                            }else{
                                $change[$_sub->orgroot][$_sub->before_operator_id] = $_sub->before_operator_id;
                                $change[$_sub->orgroot][$_sub->after_operator_id] = $_sub->after_operator_id;
                            }
                        }
//                        else{
//                            $change[$_sub->orgroot][$_sub->before_operator_id] = $_sub->before_operator_id;
//                            if( isset($params['operators_id']) && !empty($params['operators_id']) ){
//                                if($_sub->before_operator_id != $params['operators_id']){
//                                    $isBreak = true;
//                                }
//                            }
//                            if($_sub->down_receipt_status == 2) {
//                                $isEndTime = $_sub->change_time;
//                                break;
//                            }
//                        }
                    }
                }

                Log::error("change",[$change],"receiptNewFormat");
//                if ($isBreak){
//                    $dataList = [];
//                    break;
//                }

                $nowOrg = substr($v['orgroot'],0,6);

                Log::error("机构：".$nowOrg."，换签数据",[$change],"receiptNewFormat");

                $conf = (new \Fuel\Service\OrgConfigService())->getOrgRebateUseSetting($v['orgroot']); // 标用一体
                $bigConf = (new \Fuel\Service\OrgConfigService())->getBigCustomerSetting($v['orgroot']); // 先开后结
                if(isset($conf['equal_use']) && $conf['equal_use'] == 1 && isset($bigConf['pre_open_receipt']) && $bigConf['pre_open_receipt'] == 1 ){
                    if(is_array($change) && count($change) > 0 ) {
                        $changeDetail = isset($change[$nowOrg]) ? $change[$nowOrg] : [];
                        foreach (array_keys($changeDetail) as $_v) {
                            /*if($_v != 2){
                                $params['is_direct_dl'] = 2;
                            }*/
                            $params['is_direct_dl'] = $_v == 2 ? 1 : 2;
                            $params['receipt_operator_id'] = $_v;

                            if( !empty($isEndTime) ) {
                                $params['_createtimeLe'] =$isEndTime;
                            }
                            $_quota = self::getReceiptQuotaYt($v,$params);
                            foreach ($_quota as $_val) {
                                $_val['operator_name'] = isset($operatorMap[$_v]) ? $operatorMap[$_v] : "";
                                $_val['receipt_operator_id'] = $_v;
                                $dataList[] = $_val;
                            }
                        }
                    } else {

                        if(isset($params['operators_id']) && !empty($params['operators_id']) && $params['operators_id'] != $v['operators_id']){
                            $dataList = [];
                            break;
                        }

                        $_quota = self::getReceiptQuotaYt($v, $params);
                        foreach ($_quota as $_val) {
                            $_val['operator_name'] = isset($operatorMap[$v['operators_id']]) ? $operatorMap[$v['operators_id']] : "";
                            $_val['receipt_operator_id'] = $v['operators_id'];
                            $dataList[] = $_val;
                        }
                    }

                }else{
                    $oneParams = [
                        'org_id'         => $params['org_id'],
                        'receipt_type'   => $v['receipt_type'],
                        'pay_company_id' => $v['pay_company_id'],
                        'receipt_operator_id' => $params['receipt_operator_id'],
                        'is_direct_dl'   => $params['is_direct_dl'],
                    ];
                    if(isset($params['snapshot']) && $params['snapshot'] == 'on'){
                        $oneParams['snapshot'] = $params['snapshot'];
                    }
                    if (isset($params['_createtimeLe']) && $params['_createtimeLe']) {
                        $oneParams['_createtimeLe'] = $params['_createtimeLe'];
                    }

                    if( !empty($isEndTime) ){
                        $oneParams['sign_lock_time'] = $isEndTime;
                    }

                    if(is_array($change) && count($change) >= 1){
                        $changeDetail = isset($change[$nowOrg]) ? $change[$nowOrg] : [];
                        foreach (array_keys($changeDetail) as $_v){
                            /*if($_v != 2){
                                $oneParams['is_direct_dl'] = 2;
                            }*/
                            $oneParams['is_direct_dl'] = $_v == 2 ? 1 : 2;
                            $oneParams['receipt_operator_id'] = $_v;

                            $_quotaMap = self::getReceiptQuota($v,$oneParams,$totalTradesList,$index);
                            $_tmpQuota = $_quotaMap['receipt'];
                            $totalTradesList = $_quotaMap['trade'];
                            foreach ($_tmpQuota as $_val){
                                $_val['operator_name'] = isset($operatorMap[$_v]) ? $operatorMap[$_v] : "";
                                $_val['receipt_operator_id'] = $_v;
                                $dataList[] = $_val;
                            }
                        }
                    }else{

                        if(isset($params['operators_id']) && !empty($params['operators_id']) && $params['operators_id'] != $v['operators_id']){
                            $dataList = [];
                            break;
                        }

                        $_quotaMap = self::getReceiptQuota($v,$oneParams,$totalTradesList,$index);
                        $_tmpQuota = $_quotaMap['receipt'];
                        $totalTradesList = $_quotaMap['trade'];
                        foreach ($_tmpQuota as $_val){
                            $_val['operator_name'] = isset($operatorMap[$v['operators_id']]) ? $operatorMap[$v['operators_id']] : "";
                            $_val['receipt_operator_id'] = $v['operators_id'];
                            $dataList[] = $_val;
                        }
                    }

                }

                Log::error('$dataList', [$dataList], 'receiptNewFormat');
            }

            if (isset($data['data']) && count($data['data'])) {
                $data['data']  = $dataList;
                $data['total'] = count($dataList);
            } else {
                $data = $dataList;
            }
        }
        return $data;
    }


    static public function getReceiptQuota($v = [],$oneParams = [],$totalTradesList = [],$index = 1)
    {
        $amountInfo = OpenReceipt::getUseChargeByCompany($oneParams); //todo

        $v['total_receipt']                                                = $amountInfo['total_receipt'];
        $v['total_receipt_frozen']                                         = $amountInfo['total_receipt_frozen'];
        $v['total_charge_add_repayed_subtraction_receipted_receiptFrozen'] = $amountInfo['total_charge_add_repayed_subtraction_receipted_receiptFrozen'];

        Log::error('amountInfo', [$amountInfo], 'receiptNewFormat');
        $v['total_charge']                 = $amountInfo['total_charge'];
        $v['total_repaid']                 = $amountInfo['total_repaid'];
        $v['total_charge_addition_repaid'] = $amountInfo['total_charge_addition_repaid'];

        $v['tatal_charge_subtraction_receipted'] = $amountInfo['tatal_charge_subtraction_receipted'];

        $v['total_frozen']                    = $amountInfo['total_frozen'];
        $v['total_trades_subtraction_frozen'] = $amountInfo['total_trades_subtraction_frozen'];
        $v['total_fanli_charge']              = $amountInfo['total_fanli_charge'];
        $v['total_fanli_transfer_into']       = $amountInfo['total_fanli_transfer_into'];
        $v['total_fanli_transfer_out']        = $amountInfo['total_fanli_transfer_out'];
        $v['total_fanli']                     = $amountInfo['total_fanli'];

        $v['total_no_receipt_trade']     = $amountInfo['total_no_receipt_trade'];
        $v['total_no_receipt_fanli']     = $amountInfo['total_no_receipt_fanli'];
        $v['total_no_receipt_use_fanli'] = $amountInfo['total_no_receipt_use_fanli'];
        $v['total_fanli_calculate']      = $amountInfo['total_fanli_calculate'];

        $v['is_have_corp']           = $v['corp_name'] ? '有' : '无';
        $v['_exclusive_custom']      = \Fuel\Defines\ExclusiveCustom::getById($v['exclusive_custom']);
        $v['_is_recepit_nowtime']    = $v['is_recepit_nowtime'] == 2 ? '是' : '否';
        $v['is_first_receipt_apply'] = $v['first_apply_receipt'] ? '否' : '是';
        $v['_is_test']               = $v['is_test'] == 1 ? '否' : '是';
        if (!isset($v['corp_name']) || !$v['corp_name']) {
            $v['corp_name']    = '未维护';
            $v['receipt_type'] = '未维护';
        }
        $amountInfo['params']['tradesList'] = $totalTradesList;
        $list                               = OpenReceipt::getUseChargeByCompanyAll($amountInfo, $amountInfo['params']);
        //$v['max_amount'] = $list['max_amount'];
        foreach ($list as $oilType => $item) {
            $totalTradesList[$oneParams['receipt_operator_id']][$oilType] = $item['total_trades'];
            if (isset($params['limit'])) {
                $v['id'] = $index;
                $index++;
            }

            $v['max_amount']               = $item['amount'];
            $v['oil_classify']             = $item['name'];
            $v['total_receipt_trades']     = $item['total_receipt_trades'];
            $v['total_trades']             = $item['total_trades'];
            $v['total_use_fanli']          = $item['total_use_fanli'];
            $v['total_org_receipt']        = $item['total_org_receipt'];
            $v['total_org_receipt_frozen'] = $item['total_org_receipt_frozen'];
            if ($oilType != OilType::FUEL_YOU) {
                $v['total_fanli_charge']    = "0.00";
                $v['total_fanli_calculate'] = "0.00";
                $v['total_fanli']           = "0.00";
                $v['total_fanli_transfer_into'] = "0.00";
                $v['total_fanli_transfer_out'] = "0.00";
            }
            $dataList[] = $v;
        }
        return ['receipt'=>$dataList,'trade'=>$totalTradesList];
    }

    static public function getReceiptQuotaYt($v = [],$params = [])
    {
        $oilTypeList = OilType::getReceiptList();
        foreach ($oilTypeList as $oilTypeNo => $oilTypeName) {
            $oneParams = [
                'org_id'         => $params['org_id'],
                'receipt_type'   => $v['receipt_type'],
                'pay_company_id' => $v['pay_company_id'],
                'receipt_operator_id' => isset($params['receipt_operator_id']) && $params['receipt_operator_id'] ? $params['receipt_operator_id'] : '',
                'is_direct_dl'    => $params['is_direct_dl'],
            ];
            if(isset($params['snapshot']) && $params['snapshot'] == 'on'){
                $oneParams['snapshot'] = $params['snapshot'];
            }
            $oneParams['trade_end_time'] = !empty($params['_createtimeLe']) ? $params['_createtimeLe'] : \helper::nowTime();
            $oneParams['oil_type'] = $oilTypeNo;
            $amountInfo = OpenReceipt::getMaxReceiptAmountForYT($oneParams);

            $amountInfo['is_have_corp']           = $v['corp_name'] ? '有' : '无';
            $amountInfo['_exclusive_custom']      = \Fuel\Defines\ExclusiveCustom::getById($v['exclusive_custom']);
            $amountInfo['_is_recepit_nowtime']    = $v['is_recepit_nowtime'] == 2 ? '是' : '否';
            $amountInfo['is_first_receipt_apply'] = $v['first_apply_receipt'] ? '否' : '是';
            $amountInfo['_is_test']               = $v['is_test'] == 1 ? '否' : '是';
            if (!isset($v['corp_name']) || !$v['corp_name']) {
                $amountInfo['corp_name']    = '未维护';
                $amountInfo['receipt_type'] = '未维护';
            }
            $amountInfo['oil_classify']             = $oilTypeName;

            if ($oilTypeNo != OilType::FUEL_YOU) {
                $amountInfo['total_fanli_charge']    = "0.00";
                $amountInfo['total_fanli_calculate'] = "0.00";
                $amountInfo['total_fanli']           = "0.00";
                $amountInfo['total_fanli_transfer_into'] = "0.00";
                $amountInfo['total_fanli_transfer_out'] = "0.00";
            }

            $amountInfo['charge_total_date'] = $v['charge_total_date'];
            $amountInfo['company_name'] = $v['company_name'];
            $amountInfo['corp_name'] = $v['corp_name'];
            $amountInfo['exclusive_custom'] = $v['exclusive_custom'];
            $amountInfo['first_apply_receipt'] = $v['first_apply_receipt'];
            $amountInfo['is_recepit_nowtime'] = $v['is_recepit_nowtime'];
            $amountInfo['org_name'] = $v['org_name'];
            $amountInfo['orgroot'] = $v['orgroot'];
            $amountInfo['receipt_type'] = $v['receipt_type'];
            $amountInfo['pay_company_id'] = $v['pay_company_id'];

            $dataList[] = $amountInfo;
        }
        return $dataList;
    }

    /*
     * 数据格式化
     */
    static public function receiptFormat($params, $data)
    {
        if ($data) {
            if (isset($params['limit'])) {
                $params['page'] = intval($params['page']) > 0 ? intval($params['page']) : 1;
                $index          = 1 + ($params['page'] - 1) * $params['limit'];
            }

            $result      = isset($data['data']) && count($data['data']) ? $data['data'] : $data;
            $totalTrades = $no_receipt_trades = $fanliChargeAmount = [];

            if ($params['receipt_mode'] == 1) {
                foreach ($result as &$v) {
                    if (isset($params['limit'])) {
                        $v['id'] = $index;
                        $index++;
                    }

                    if (!isset($totalTrades[$v['orgroot'] . $v['receipt_type']])) {
                        $totalTrades[$v['orgroot'] . $v['receipt_type']] = \Fuel\Service\OpenReceipt::calTotalTradesByOrgCode(['org_id' => $v['org_id'], 'receipt_type' => $v['receipt_type']]);
                    }

                    if (!isset($total_fanli_calculate[$v['orgroot'] . $v['receipt_type']])) {
                        $total_fanli_calculate[$v['orgroot'] . $v['receipt_type']] = \Fuel\Service\OpenReceipt::calTotalCalculateByOrgCode(['org_id' => $v['org_id'], 'receipt_type' => $v['receipt_type']]);
                    }

                    $amountInfo                                                        = \Fuel\Service\OpenReceipt::maxAmountForFoss([
                        'total_trades'          => isset($totalTrades[$v['orgroot'] . $v['receipt_type']]) ? $totalTrades[$v['orgroot'] . $v['receipt_type']] : 0,
                        'total_fanli_calculate' => isset($total_fanli_calculate[$v['orgroot'] . $v['receipt_type']]) ? $total_fanli_calculate[$v['orgroot'] . $v['receipt_type']] : 0,
                        'pay_company_id'        => $v['pay_company_id'],
                        'org_id'                => $v['org_id'],
                        'receipt_type'          => $v['receipt_type']
                    ]);
                    $v['total_charge']                                                 = $amountInfo['total_charge'];
                    $v['total_repaid']                                                 = $amountInfo['total_repaid'];
                    $v['total_charge_addition_repaid']                                 = $amountInfo['total_charge_addition_repaid'];
                    $v['total_receipt']                                                = $amountInfo['total_receipt'];
                    $v['tatal_charge_subtraction_receipted']                           = $amountInfo['tatal_charge_subtraction_receipted'];
                    $v['total_trades']                                                 = $amountInfo['total_trades'];
                    $v['total_frozen']                                                 = $amountInfo['total_frozen'];
                    $v['total_trades_subtraction_frozen']                              = $amountInfo['total_trades_subtraction_frozen'];
                    $v['total_fanli_charge']                                           = $amountInfo['total_fanli_charge'];
                    $v['total_fanli']                                                  = $amountInfo['total_fanli'];
                    $v['total_use_fanli']                                              = $amountInfo['total_use_fanli'];
                    $v['total_receipt_frozen']                                         = $amountInfo['total_receipt_frozen'];
                    $v['total_no_receipt_trade']                                       = $amountInfo['total_no_receipt_trade'];
                    $v['total_no_receipt_fanli']                                       = $amountInfo['total_no_receipt_fanli'];
                    $v['total_no_receipt_use_fanli']                                   = $amountInfo['total_no_receipt_use_fanli'];
                    $v['max_amount']                                                   = $amountInfo['max_amount'];
                    $v['total_charge_add_repayed_subtraction_receipted_receiptFrozen'] = $amountInfo['total_charge_add_repayed_subtraction_receipted_receiptFrozen'];
                    $v['total_receipt_trades']                                         = $amountInfo['total_receipt_trades'];
                    $v['total_fanli_calculate']                                        = $amountInfo['total_fanli_calculate'];
                    $v['total_org_receipt']                                            = $amountInfo['total_org_receipt'];
                    $v['total_org_receipt_frozen']                                     = $amountInfo['total_org_receipt_frozen'];

                    $v['is_have_corp']           = $v['corp_name'] ? '有' : '无';
                    $v['_exclusive_custom']      = \Fuel\Defines\ExclusiveCustom::getById($v['exclusive_custom']);
                    $v['_is_recepit_nowtime']    = $v['is_recepit_nowtime'] == 2 ? '是' : '否';
                    $v['is_first_receipt_apply'] = $v['first_apply_receipt'] ? '否' : '是';
                    $v['_is_test']               = $v['is_test'] == 1 ? '否' : '是';
                    if (!isset($v['corp_name']) || !$v['corp_name']) {
                        $v['corp_name']    = '未维护';
                        $v['receipt_type'] = '未维护';
                    }
                    unset($amountInfo);
                }
            } else {
                foreach ($result as &$item) {
                    $receiptCondition['pay_company_id']                                   = $item['pay_company_id'];
                    $receiptCondition['org_id']                                           = $item['org_id'];
                    $receiptCondition['receipt_type']                                     = $item['receipt_type'];
                    $receiptModeFee                                                       = \Fuel\Service\OpenReceipt::getReceiptAllFee($receiptCondition);
                    $item['total_charge']                                                 = $receiptModeFee['total_charge'];
                    $item['total_repaid']                                                 = $receiptModeFee['total_repaid'];
                    $item['total_charge_addition_repaid']                                 = $receiptModeFee['total_charge_addition_repaid'];
                    $item['total_receipt']                                                = $receiptModeFee['total_receipt'];
                    $item['tatal_charge_subtraction_receipted']                           = $receiptModeFee['tatal_charge_subtraction_receipted'];
                    $item['total_trades']                                                 = $receiptModeFee['total_trades'];
                    $item['total_frozen']                                                 = $receiptModeFee['total_frozen'];
                    $item['total_trades_subtraction_frozen']                              = $receiptModeFee['total_trades_subtraction_frozen'];
                    $item['total_fanli_charge']                                           = $receiptModeFee['total_fanli_charge'];
                    $item['total_fanli']                                                  = $receiptModeFee['total_fanli'];
                    $item['total_use_fanli']                                              = $receiptModeFee['total_use_fanli'];
                    $item['total_receipt_frozen']                                         = $receiptModeFee['total_receipt_frozen'];
                    $item['total_no_receipt_trade']                                       = $receiptModeFee['total_no_receipt_trade'];
                    $item['total_no_receipt_fanli']                                       = $receiptModeFee['total_no_receipt_fanli'];
                    $item['total_no_receipt_use_fanli']                                   = $receiptModeFee['total_no_receipt_use_fanli'];
                    $item['max_amount']                                                   = $receiptModeFee['max_amount'];
                    $item['total_charge_add_repayed_subtraction_receipted_receiptFrozen'] = $receiptModeFee['total_charge_add_repayed_subtraction_receipted_receiptFrozen'];
                    $item['total_receipt_trades']                                         = $receiptModeFee['total_receipt_trades'];
                    $item['total_fanli_calculate']                                        = $receiptModeFee['total_fanli_calculate'];
                    $item['total_org_receipt']                                            = $receiptModeFee['total_org_receipt'];
                    $item['total_org_receipt_frozen']                                     = $receiptModeFee['total_org_receipt_frozen'];

                    $item['is_have_corp']           = $item['corp_name'] ? '有' : '无';
                    $item['_exclusive_custom']      = \Fuel\Defines\ExclusiveCustom::getById($item['exclusive_custom']);
                    $item['_is_recepit_nowtime']    = $item['is_recepit_nowtime'] == 2 ? '是' : '否';
                    $item['is_first_receipt_apply'] = $item['first_apply_receipt'] ? '否' : '是';
                    $item['_is_test']               = $item['is_test'] == 1 ? '否' : '是';
                    if (!isset($item['corp_name']) || !$item['corp_name']) {
                        $item['corp_name']    = '未维护';
                        $item['receipt_type'] = '未维护';
                    }
                    unset($receiptModeFee);
                }
            }


            if (isset($data['data']) && count($data['data'])) {
                $data['data'] = $result;
            } else {
                $data = $result;
            }
        }

        return $data;
    }

    /**
     * 根据id获取字段值
     *
     * @param        $id
     * @param string $field
     * @return string
     */
    static public function getFieldById($id, $field = 'Orgroot')
    {
        if (!$id) {
            return '';
        }

        $ret = self::where('id', $id)->pluck($field)->toArray();

        return !empty($ret[0]) ? $ret[0] : '';
    }

    /**
     * 取付款公司信息
     *
     * @param $id
     * @return array
     */
    public static function getInfoById($id)
    {
        if (!$id) {
            return [];
        }

        return OilPayCompany::find($id);
    }

    /**
     * 获取驹马机构的累计还款
     *
     * @param $params
     * @return array
     */
    public static function sumReplayMoney(array $params)
    {
        return OilPayCompany::Filter($params)->sum("charge_total");
    }

    /*
     * 有返回,无新增
     */
    public static function getOrAddPayCompanyId(array $params)
    {
        $pay_company_id = NULL;
        //根据机构编码和公司名称查询
        $payCompanyInfo = OilPayCompany::where('orgroot', $params['orgcode'])
                                       ->where('company_name', $params['corpName'])
                                       ->first();

        if ($payCompanyInfo) {
            $pay_company_id = $payCompanyInfo->id;
        } else {
            $data = self::add([
                'orgroot'      => $params['orgcode'],
                'company_name' => $params['corpName'],
                'status'       => 1
            ]);

            if ($data) {
                $pay_company_id = $data->id;
            }
        }

        return $pay_company_id;

    }

    /**
     * 取字段值
     *
     * @param array $params
     * @param       $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        $res = OilPayCompany::Filter($params)->pluck($pluckField);

        return !$res ? [] : $res->toArray();
    }

    /**
     * 列表查询
     *
     * @param array $params
     * @return array
     */
    static public function getFilterList(array $params)
    {
        return OilPayCompany::Filter($params)->orderBy('createtime', 'desc')->get()->toArray();
    }

    static public function getByIdLockForMap(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        if (is_array($params['id'])) {
            return OilPayCompany::lockForUpdate()->whereIn('id', $params['id'])->get()->keyBy('id');
        }
        return OilPayCompany::lockForUpdate()->where('id', $params['id'])->first()->keyBy('id');
    }

    static public function getCompanyByOrgCode($orgcode = '',$receiptMode = '')
    {
        if(empty($receiptMode) || empty($orgcode)){
            return [];
        }
        //集中核算机构:直接找消费上【顶级机构】上挂的唯一付款公司(若无则为空,多个则为空);
        //独立核算机构(包括标准/非标):直接找消费上【所属机构沟】上挂的「正常」状态唯一的付款公司:若无
        //以此类推...直到确定为空或者找到唯一「正常」状态的的付款公司。
        if($receiptMode == 1){ //集中
            $info = OilPayCompany::where('orgroot',substr($orgcode,0,6))->where('status',1)->get();
            if(count($info) == 1){
                return ['pay_company_id'=>$info[0]['id'],'pay_company_name'=>$info[0]['company_name'],'code'=>substr($orgcode,0,6)];
            }
        }elseif ($receiptMode == 2){ //独立
            $orgList = self::splitOrg($orgcode);
            for($i=0;$i<count($orgList);$i++){
                $info = OilPayCompany::where('orgroot',$orgList[$i])->where('status',1)->get();
                if(count($info) > 1){
                    return [];
                }
                if(count($info) == 1){
                    return ['pay_company_id'=>$info[0]['id'],'pay_company_name'=>$info[0]['company_name'],'code'=>$orgList[$i]];
                }
            }
        }
        return [];
    }

    static public function splitOrg($org_code)
    {
        $orgList = [];
        $org_len = strlen($org_code);
        if ($org_len > 6) {
            $each_num = ($org_len - 6) / 2;

            $orgList[] = $org_code;

            for ($i = 1; $i <= $each_num; $i++) {
                $orgList[] = substr($org_code, 0, $org_len - ($i * 2));
            }
        } else {
            $orgList = [$org_code];
        }

        return $orgList;
    }
}
