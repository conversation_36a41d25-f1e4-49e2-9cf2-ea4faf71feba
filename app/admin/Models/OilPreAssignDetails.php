<?php
/**
 * 预分配记录详情表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/10/12
 * Time: 10:34:00
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilPreAssignDetails extends \Framework\Database\Model
{
    protected $table = 'oil_pre_assign_details';

    protected $guarded = ["id"];
    protected $fillable = ['api_id','assign_id','vice_no','money','jifen','card_owner','fetch_time','assign_time','status','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By api_id
        if (isset($params['api_id']) && $params['api_id'] != '') {
            $query->where('api_id', '=', $params['api_id']);
        }

        //Search By assign_id
        if (isset($params['assign_id']) && $params['assign_id'] != '') {
            $query->where('assign_id', '=', $params['assign_id']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }

        //Search By jifen
        if (isset($params['jifen']) && $params['jifen'] != '') {
            $query->where('jifen', '=', $params['jifen']);
        }

        //Search By card_owner
        if (isset($params['card_owner']) && $params['card_owner'] != '') {
            $query->where('card_owner', '=', $params['card_owner']);
        }

        //Search By fetch_time
        if (isset($params['fetch_time']) && $params['fetch_time'] != '') {
            $query->where('fetch_time', '=', $params['fetch_time']);
        }

        //Search By assign_time
        if (isset($params['assign_time']) && $params['assign_time'] != '') {
            $query->where('assign_time', '=', $params['assign_time']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 预分配记录详情表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilPreAssignDetails::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 预分配记录详情表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreAssignDetails::find($params['id']);
    }

    /**
     * 预分配记录详情表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilPreAssignDetails::create($params);
    }

    /**
     * 预分配记录详情表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPreAssignDetails::find($params['id'])->update($params);
    }

    /**
     * 预分配记录详情表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilPreAssignDetails::destroy($params['ids']);
    }




}