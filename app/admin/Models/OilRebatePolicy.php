<?php
/**
 * 返利政策
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/09/21
 * Time: 11:38:57
 */
namespace Models;
use Framework\Log;
use Fuel\Defines\CooperationType;
use Carbon\Carbon;
use Fuel\Defines\OilType;
use Fuel\Defines\RebateFormula;
use Fuel\Defines\RebatePolicy;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\UpstreamRebate\Formula;

/**
 * Class OilRebatePolicy
 * @package Models
 * @property $id
 * @property $policy_type
 * @property $end_time
 */
class OilRebatePolicy extends \Framework\Database\Model
{
    protected $table = 'oil_rebate_policy';

    protected $guarded = ["id"];
    protected $fillable = ['formula_id','cooperation_type','supplier_id','direct_drop_formula_id','policy_type','name','suit_obj','suit_oil','ca_status','start_time','end_time','creator','last_operator',
        'status','createtime','updatetime','min_price_id','card_type','sync_error','sync_status','ossUrl','optime','opname'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    public function Property()
    {
        return $this->hasMany('Models\OilRebateProperty','policy_id','id');
    }

    public function Formula()
    {
        return $this->hasOne('Models\OilRebateFormula','id','formula_id');
    }

    //上游后返返利公式
    public function UpstreamAfterFormula()
    {
        return $this->hasOne('Models\UpstreamRebate\Formula','id','formula_id');
    }

    //上游直降返利公式
    public function UpstreamDirectFormula()
    {
        return $this->hasOne('Models\UpstreamRebate\Formula','id','direct_drop_formula_id');
    }

    public function MinPrice()
    {
        return $this->hasOne('Models\OilRebateMinPrice','id','min_price_id');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            if (is_array($params['id'])) {
                $query->whereIn('id', $params['id']);
            } else {
                $query->where('id', '=', $params['id']);
            }
        }

        //Search By idIn
        if (isset($params['idIn']) && $params['idIn'] != '') {
            $query->whereIn('id', $params['idIn']);
        }

        if (!empty($params['ids'])) {
            $query->whereIn('id', explode(',', $params['ids']));
        }

        if (isset($params['id_neq']) && $params['id_neq'] != '') {
            $query->where('id', '!=',$params['id_neq']);
        }

        //Search By formula_id
        if (isset($params['formula_id']) && $params['formula_id'] != '') {
            $query->where('formula_id', '=', $params['formula_id']);
        }

        //Search By formula_idIn
        if (isset($params['formula_idIn']) && $params['formula_idIn'] != '') {
            if (is_array($params['formula_idIn'])) {
                $query->whereIn('formula_id', $params['formula_idIn']);
            } else {
                $query->where('formula_id', '=', $params['formula_idIn']);
            }
        }

        //Search By formula_idIn
        if (isset($params['direct_drop_formula_id_in']) && $params['direct_drop_formula_id_in'] != '') {
            if (is_array($params['direct_drop_formula_id_in'])) {
                $query->whereIn('direct_drop_formula_id', $params['direct_drop_formula_id_in']);
            } else {
                $query->where('direct_drop_formula_id', '=', $params['direct_drop_formula_id_in']);
            }
        }

        //Search By type
        if (isset($params['policy_type']) && $params['policy_type'] != '') {
            $query->where('policy_type', '=', $params['policy_type']);
        }

        if (isset($params['policy_type_in']) && $params['policy_type_in'] != '') {
            $query->whereIn('policy_type', $params['policy_type_in']);
        }

        //Search By card_type
        if (isset($params['card_type']) && $params['card_type'] != '') {
            $query->where('card_type', '=', $params['card_type']);
        }

        //Search By min_price_id
        if (isset($params['min_price_id']) && $params['min_price_id'] != '') {
            $query->where('min_price_id', '=', $params['min_price_id']);
        }

        //Search By min_price_idIn
        if (isset($params['min_price_idIn']) && $params['min_price_idIn'] != '') {
            if (is_array($params['min_price_idIn'])) {
                $query->whereIn('min_price_id', $params['min_price_idIn']);
            } else {
                $query->where('min_price_id', '=', $params['min_price_idIn']);
            }
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        //Search By name_lk
        if (isset($params['name_lk']) && $params['name_lk'] != '') {
            $query->where('name', 'like', '%'.$params['name_lk'].'%');
        }

        //Search By suit_obj
        if (isset($params['suit_obj']) && $params['suit_obj'] != '') {
            $query->where('suit_obj', '=', $params['suit_obj']);
        }

        //Search By suit_oil
        if (isset($params['suit_oil']) && $params['suit_oil'] != '') {
            $query->where('suit_oil', '=', $params['suit_oil']);
        }

        if (isset($params['suit_oilIn']) && $params['suit_oilIn'] != '') {
            if (is_array($params['suit_oilIn'])) {
                $suitOilArr = $params['suit_oilIn'];
                $query->where(function ($query) use ($suitOilArr) {
                    foreach ($suitOilArr as $val) {
                        $query->orWhereRaw('FIND_IN_SET(?,suit_oil)', [$val]);
                    }
                });
            } else {
                $query->whereRaw('FIND_IN_SET(?,suit_oil)', [$params['suit_oilIn']]);
            }
        }

        //Search By ca_status
        if (isset($params['ca_status']) && $params['ca_status'] != '') {
            $query->where('ca_status', '=', $params['ca_status']);
        }

        //Search By ca_statusIn
        if (isset($params['ca_statusIn']) && $params['ca_statusIn'] != '') {
            if (is_array($params['ca_statusIn'])) {
                $query->whereIn('ca_status', $params['ca_statusIn']);
            } else {
                $query->where('ca_status', '=', $params['ca_statusIn']);
            }
        }

        //Search By start_timeGte
        if (isset($params['start_timeGte']) && $params['start_timeGte'] != '') {
            $query->where('start_time', '>=', $params['start_timeGte']);
        }

        //Search By start_timeLte
        if (isset($params['start_timeLte']) && $params['start_timeLte'] != '') {
            $query->where('start_time', '<=', $params['start_timeLte']);
        }

        //sectionTime
        if (isset($params['sectionTime']) && $params['sectionTime']){
            $query->where(function ($query) use ($params){
                $query->where(function ($query) use ($params){
                    $query->where('start_time', 'like', date('Y-m',strtotime($params['sectionTime'])).'%')
                        ->orWhere('end_time', 'like', date('Y-m',strtotime($params['sectionTime'])).'%');
                })->orWhere(function ($query) use ($params){
                    $query->where('start_time','<=',$params['sectionTime'])
                        ->where('end_time','>=',$params['sectionTime']);
                });
            });
        }

        //Search By start_time
        if (isset($params['start_time']) && $params['start_time'] != '') {
            $query->where('start_time', '=', $params['start_time']);
        }

        //Search By end_time
        if (isset($params['end_time']) && $params['end_time'] != '') {
            $query->where('end_time', '=', $params['end_time']);
        }

        //Search By end_timeGte
        if (isset($params['end_timeGte']) && $params['end_timeGte'] != '') {
            $query->where('end_time', '>=', $params['end_timeGte']);
        }

        //Search By end_timeLte
        if (isset($params['end_timeLte']) && $params['end_timeLte'] != '') {
            $query->where('end_time', '<=', $params['end_timeLte']);
        }

        //Search By end_timeLt
        if (isset($params['end_timeLt']) && $params['end_timeLt'] != '') {
            $query->where('end_time', '<', $params['end_timeLt']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_rebate_policy.status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By supplier_id
        if (isset($params['supplier_id_in']) && $params['supplier_id_in'] != '') {
            $query->whereIn('supplier_id', $params['supplier_id_in']);
        }

        //Search By supplier_id
        if (isset($params['supplier_id']) && $params['supplier_id'] != '') {
            $query->where('supplier_id', '=', $params['supplier_id']);
        }

        //Search By current_effect
        if (isset($params['current_effect']) && $params['current_effect'] != '') {
            $query->where('start_time', '<=', $params['current_effect']);
            $query->where('end_time','>=',$params['current_effect']);
        }

        if (isset($params['range_effect']) && $params['range_effect'] != '') {
            $query->where('start_time', '<=', $params['_end_time']);
            $query->where('end_time','>=',$params['_start_time']);
        }

        if (!empty($params['cooperation_type'])) {
            $query->where('cooperation_type', $params['cooperation_type']);
        }

        if (! empty($params['rebate_unit'])) {
            if (is_array($params['rebate_unit'])) {
                $query->whereIn('oil_upstream_rebate_formula.rebate_unit', $params['rebate_unit']);
            } else {
                $query->where('oil_upstream_rebate_formula.rebate_unit', $params['rebate_unit']);
            }
        }

        if (! empty($params['res_code'])) {
            $query->where('res_code', $params['res_code']);
        }

        if (! empty($params['sub_type'])) {
            if (is_array($params['sub_type'])) {
                $query->whereIn('oil_rebate_property.sub_type', $params['sub_type']);
            } else {
                $query->where('oil_rebate_property.sub_type', $params['sub_type']);
            }
        }

        if (! empty($params['rebate_type'])) {
            if (is_array($params['rebate_type'])) {
                $query->whereIn('oil_upstream_rebate_formula.rebate_type', $params['rebate_type']);
            } else {
                $query->where('oil_upstream_rebate_formula.rebate_type', $params['rebate_type']);
            }
        }

        // Search By is_current 是否当前政策
        if (isset($params['is_current'])) {
            $current = Carbon::now()->format('Y-m-d H:i:s');
            switch (intval($params['is_current'])) {
                // 过去
                case 1:
                    $query->where('end_time', '<', $current);
                    break;
                // 当前
                case 2:
                    $query->where('start_time', '<=', $current)
                        ->where('end_time', '>', $current);
                    break;
                // 未来
                case 3:
                    $query->where('start_time', '>', $current);
                    break;
            }
        }

        return $query;
    }

    /**
     * 返利政策 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        //Capsule::connection()->enableQueryLog();
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilRebatePolicy::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        }else if(isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->orderBy('createtime', 'desc')->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
//        $sql = Capsule::connection()->getQueryLog();
//        var_dump($sql);exit;
        if(count($data) > 0){
            if(isset($params['is_upstream']) && $params['is_upstream'] == 1){
                $supplier_ids = $data->pluck('supplier_id')->toArray();
                $supplier_map = OilStationSupplier::getSupplierMap(['ids'=>$supplier_ids]);
                $params['supplierMap'] = $supplier_map;
            }
            foreach ($data as $key=>$info) {
                $data[$key] = self::formatInfo($info->toArray(),$params);
            }
        }

        return $data;
    }

    public static function formatInfo($info,$params=[])
    {
        if (!$info) {
            return [];
        }

        $ret = $info;

        $ret['suit_obj_name']    = RebatePolicy::$suitObj[$info['suit_obj']];
        $ret['card_type_name'] = RebatePolicy::$cardType[$info['card_type']];
        $ret['policy_type_name'] = RebatePolicy::$policyType[$info['policy_type']];
        $ret['ca_status_name']   = RebatePolicy::$caStatus[$info['ca_status']];
        $ret['last_operator']   = $info['last_operator'];
        $ret['sync_status_name'] = RebatePolicy::$syncStatus[$info['sync_status']];//执行状态

        $suitOil = OilRebatePolicy::getSuitOilValue(['suit_oil'=>$info['suit_oil']]);
        $ret['suit_oil_txt']     = implode(',', array_values($suitOil));

        // 关联公式
//        $ret['formula_info'] = OilRebateFormula::formatInfo(OilRebateFormula::getById(['id'=>$info['formula_id']]));
        if(in_array($info['policy_type'],array_keys(RebatePolicy::$policyType))){
            $formulaInfo = OilRebateFormula::formatInfo(OilRebateFormula::getById(['id'=>$info['formula_id']]));
            $ret['formula_name'] = !empty($formulaInfo['name']) ? $formulaInfo['name'] : '';
            $ret['ca_obj']       = !empty($formulaInfo['ca_obj']) ? $formulaInfo['ca_obj'] : '';
            $ret['ca_obj_name']  = !empty($formulaInfo['ca_obj_name']) ? $formulaInfo['ca_obj_name'] : '';
            $ret['rebate_type'] = !empty($formulaInfo['rebate_type']) ? $formulaInfo['rebate_type'] : '';
            $ret['rebate_type_name'] = !empty($formulaInfo['rebate_type_name']) ? $formulaInfo['rebate_type_name'] : '';
            $ret['ca_method'] = !empty($formulaInfo['ca_method']) ? $formulaInfo['ca_method'] : '';
            $ret['ca_method_name'] = !empty($formulaInfo['ca_method_name']) ? $formulaInfo['ca_method_name'] : '';
            $ret['rebate_unit'] = !empty($formulaInfo['rebate_unit']) ? $formulaInfo['rebate_unit'] : '';
            $ret['rebate_unit_name'] = !empty($formulaInfo['rebate_unit_name']) ? $formulaInfo['rebate_unit_name'] : '';
            $ret['rebate_value'] = !empty($formulaInfo['rebate_value']) ? $formulaInfo['rebate_value'] : '';
            $ret['min_amount'] = !empty($formulaInfo['min_amount']) ? $formulaInfo['min_amount'] : '';
            $ret['formula_ext'] = !empty($formulaInfo['ext']) ? $formulaInfo['ext'] : '';
        }

        if ($info['policy_type'] == RebatePolicy::POLICY_TYPE_ELEC) {
            // 适用站点
            $resSuitStation = OilRebateProperty::getSuitData(['policy_id'=>$info['id'], 'type'=>RebatePolicy::TYPE_STATION]);
            $ret['suit_station'] = $resSuitStation['suit_data'];
            $ret['suit_station_txt'] = implode(',', array_values($resSuitStation['suit_sub_type']));

            // 适用客户（机构）
            $resSuitOrg = OilRebateProperty::getSuitData(['policy_id'=>$info['id'], 'type'=>RebatePolicy::TYPE_CUSTOMER]);
            $ret['suit_org'] = $resSuitOrg['suit_data'];
            $ret['suit_org_txt'] = implode(',', array_values($resSuitOrg['suit_sub_type']));
        } elseif($info['policy_type'] == RebatePolicy::POLICY_TYPE_ENTITY) {
            // 适用省份
            $resSuitProvince = OilRebateProperty::getSuitData(['policy_id'=>$info['id'], 'type'=>RebatePolicy::TYPE_PROVINCE]);
            $ret['suit_province'] = $resSuitProvince['suit_data'];
            $ret['suit_province_txt'] = implode(',', array_values($resSuitProvince['suit_sub_type']));

            // 适用卡片
            $resSuitCard = OilRebateProperty::getSuitData(['policy_id'=>$info['id'], 'type'=>RebatePolicy::TYPE_CARD]);
            $ret['suit_card'] = $resSuitCard['suit_data'];
            $ret['suit_card_txt'] = implode(',', array_values($resSuitCard['suit_sub_type']));

            // 适用客户（机构）
            $resSuitOrg = OilRebateProperty::getSuitData(['policy_id'=>$info['id'], 'type'=>RebatePolicy::TYPE_ORG]);
            $ret['suit_org'] = $resSuitOrg['suit_data'];
            $ret['suit_org_txt'] = implode(',', array_values($resSuitOrg['suit_sub_type']));
        }else{
            /****上游****/
            $ret['cooperation_type_name'] = CooperationType::getById($info['cooperation_type']);
            $ret['supplier_id_name'] = $params['supplierMap'][$info['supplier_id']];
            $ret['policy_type_name'] = RebatePolicy::$policyUpstreamType[$info['policy_type']];

            $formulaInfo = OilRebateFormula::formatUpstreamInfo(Formula::getById(['id'=>$info['formula_id']]));
            $ret['formula_name'] = !empty($formulaInfo['name']) ? $formulaInfo['name'] : '';
            $ret['ca_obj']       = !empty($formulaInfo['ca_obj']) ? $formulaInfo['ca_obj'] : '';
            $ret['ca_obj_name']  = !empty($formulaInfo['ca_obj_name']) ? $formulaInfo['ca_obj_name'] : '';
            $ret['rebate_type'] = !empty($formulaInfo['rebate_type']) ? $formulaInfo['rebate_type'] : '';
            $ret['rebate_type_name'] = !empty($formulaInfo['rebate_type_name']) ? $formulaInfo['rebate_type_name'] : '';
            $ret['ca_method'] = !empty($formulaInfo['ca_method']) ? $formulaInfo['ca_method'] : '';
            $ret['ca_method_name'] = !empty($formulaInfo['ca_method_name']) ? $formulaInfo['ca_method_name'] : '';
            $ret['rebate_unit'] = !empty($formulaInfo['rebate_unit']) ? $formulaInfo['rebate_unit'] : '';
            $ret['rebate_unit_name'] = !empty($formulaInfo['rebate_unit_name']) ? $formulaInfo['rebate_unit_name'] : '';
            $ret['rebate_value'] = !empty($formulaInfo['rebate_value']) ? $formulaInfo['rebate_value'] : '';
            $ret['min_amount'] = !empty($formulaInfo['min_amount']) ? $formulaInfo['min_amount'] : '';
            $ret['formula_ext'] = !empty($formulaInfo['ext']) ? json_decode($formulaInfo['ext'],true) : '';

            $directFormulaInfo = [];
            if($info['direct_drop_formula_id']){
                $directFormulaInfo = OilRebateFormula::formatUpstreamInfo(Formula::getById(['id'=>$info['direct_drop_formula_id']]));
            }

            $ret['direct_formula_name'] = !empty($directFormulaInfo['name']) ? $directFormulaInfo['name'] : '';
            $ret['direct_ca_obj']       = !empty($directFormulaInfo['ca_obj']) ? $directFormulaInfo['ca_obj'] : '';
            $ret['direct_ca_obj_name']  = !empty($directFormulaInfo['ca_obj_name']) ? $directFormulaInfo['ca_obj_name'] : '';
            $ret['direct_rebate_type'] = !empty($directFormulaInfo['rebate_type']) ? $directFormulaInfo['rebate_type'] : '';
            $ret['direct_rebate_type_name'] = !empty($directFormulaInfo['rebate_type_name']) ? $directFormulaInfo['rebate_type_name'] : '';
            $ret['direct_ca_method'] = !empty($directFormulaInfo['ca_method']) ? $directFormulaInfo['ca_method'] : '';
            $ret['direct_ca_method_name'] = !empty($directFormulaInfo['ca_method_name']) ? $directFormulaInfo['ca_method_name'] : '';
            $ret['direct_rebate_unit'] = !empty($directFormulaInfo['rebate_unit']) ? $directFormulaInfo['rebate_unit'] : '';
            $ret['direct_rebate_unit_name'] = !empty($directFormulaInfo['rebate_unit_name']) ? $directFormulaInfo['rebate_unit_name'] : '';
            $ret['direct_rebate_value'] = !empty($directFormulaInfo['rebate_value']) ? $directFormulaInfo['rebate_value'] : '';
            $ret['direct_min_amount'] = !empty($directFormulaInfo['min_amount']) ? $directFormulaInfo['min_amount'] : '';
            $ret['direct_formula_ext'] = !empty($directFormulaInfo['ext']) ? json_decode($directFormulaInfo['ext'],true) : '';

            // 适用省份
            $resSuitProvince = OilRebateProperty::getSuitData(['policy_id'=>$info['id'], 'type'=>RebatePolicy::TYPE_UP_PROVINCE]);
            $ret['suit_province'] = $resSuitProvince['suit_data'];
            $ret['suit_province_txt'] = implode(',', array_values($resSuitProvince['suit_sub_type']));
            $ret['suit_province_values'] = empty($resSuitProvince['suit_data']) ? '' : implode(',', array_unique(array_column($resSuitProvince['suit_data'],'value')));

            $ret['suit_province_sub_type'] = implode(',', array_unique(array_column($resSuitProvince['suit_data'],'sub_type')));
            $ret['suit_province_ids'] = $ret['suit_province_sub_type'] == RebatePolicy::SUB_TYPE_UP_ALL_PROVINCE ? "" : implode(',', array_unique(array_column($resSuitProvince['suit_data'],'res_code')));

            // 适用油品
            $resSuitOil = OilRebateProperty::getSuitData(['policy_id'=>$info['id'], 'type'=>RebatePolicy::TYPE_UP_OIL]);
            $ret['suit_oil_name'] = $resSuitOil['suit_data'];
            $ret['suit_oil_name_txt'] = implode(',', array_values($resSuitOil['suit_sub_type']));
            $ret['suit_oil_name_values'] = empty($resSuitOil['suit_data']) ? '' : implode(',', array_unique(array_column($resSuitOil['suit_data'],'value')));
            $ret['suit_oil_name_sub_type'] = implode(',', array_unique(array_column($resSuitOil['suit_data'],'sub_type')));
            $ret['suit_oil_name_ids'] = implode(',', array_unique(array_column($resSuitOil['suit_data'],'res_code')));

            // 适用站点
            $resSuitStation = OilRebateProperty::getSuitData(['policy_id'=>$info['id'], 'type'=>RebatePolicy::TYPE_UP_STATION]);
            $ret['suit_station'] = $resSuitStation['suit_data'];
            $ret['suit_station_txt'] = implode(',', array_values($resSuitStation['suit_sub_type']));
            $ret['suit_station_values'] = empty($resSuitStation['suit_data']) ? '' : implode(',', array_unique(array_column($resSuitStation['suit_data'],'value')));
            $ret['suit_station_sub_type'] = implode(',', array_unique(array_column($resSuitStation['suit_data'],'sub_type')));
            $ret['suit_station_ids'] = $ret['suit_station_sub_type'] == RebatePolicy::SUB_TYPE_UP_ALL_STATION ? "" : implode(',', array_unique(array_column($resSuitStation['suit_data'],'res_code')));

            // 适用卡片
            $resSuitCard = OilRebateProperty::getSuitData(['policy_id'=>$info['id'], 'type'=>RebatePolicy::TYPE_UP_CARD]);
            $ret['suit_card'] = $resSuitCard['suit_data'];
            $ret['suit_card_txt'] = implode(',', array_values($resSuitCard['suit_sub_type']));
            $ret['suit_card_sub_type'] = implode(',', array_unique(array_column($resSuitCard['suit_data'],'sub_type')));
            $ret['suit_card_ids'] = $ret['suit_card_sub_type'] == RebatePolicy::SUB_TYPE_UP_ALL_CARD ? "" : implode(',', array_unique(array_column($resSuitCard['suit_data'],'res_code')));
            $ret['suit_card_values'] = $ret['suit_card_sub_type'] == empty($resSuitCard['suit_data']) ? '' : implode(',', array_unique(array_column($resSuitCard['suit_data'],'res_code')));

            $ret['suit_start_time'] = strtotime($info['start_time']);
            $ret['suit_end_time'] = strtotime($info['end_time']);

            Log::error('foreach'.date('Y-m-d H:i:s'),[],'tim321312');
        }

        // 免惠最低价
        $ret['min_price_name'] = '';
        if (!empty($info['min_price_id'])) {
            $ret['min_price_name'] = OilRebateMinPrice::getResField(['id'=>$info['min_price_id']], 'name');
        }

        // 最高权重
        $ret['max_weight'] = OilRebateSku::getMaxWeight(['policy_id'=>$info['id']]);

        return $ret;
    }

    /**
     * 返利政策 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRebatePolicy::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRebatePolicy::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 返利政策 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilRebatePolicy::create($params);
    }

    /**
     * 返利政策 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilRebatePolicy::find($params['id'])->update($params);
    }

    /**
     * 返利政策 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilRebatePolicy::destroy($params['ids']);
    }

    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return self::Filter($params)->pluck($pluckField)->toArray();
    }

    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();

        return !empty($res[0]) ? $res[0] : '';
    }

    /**
     * 根据条件取信息
     * @param array $params
     * @return object
     */
    static public function getInfoByFilter(array $params)
    {

        return self::Filter($params)->first();
    }

    /**
     * 发票列表查询
     * @param array $params
     * @return array
     */
    static public function getFilterList(array $params)
    {
        return self::Filter($params)->orderBy('createtime', 'desc')->get()->toArray();
    }

    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return self::Filter($params)->count();
    }

    /**
     * 取公式绑定的政策数量
     * @param array $params
     * @return mixed
     */
    public static function getFormulaBindNum(array $params)
    {
        \helper::argumentCheck(['formula_id'], $params);

        $formulaUsedNum = OilRebatePolicy::getTotal([
            'formula_id'=>$params['formula_id'],
            'status'=>1,
        ]);

        return $formulaUsedNum;
    }

    /**
     * 取公式绑定的政策已计算的数量
     * @param array $params
     * @return mixed
     */
    public static function getFormulaCalNum(array $params)
    {
        \helper::argumentCheck(['formula_id'], $params);

        $formulaCalNum = OilRebatePolicy::getTotal([
            'formula_id'=>$params['formula_id'],
            'cal_statusIn'=>[RebatePolicy::CA_STATUS_ING,RebatePolicy::CA_STATUS_USED],
            'status'=>1,
        ]);

        return $formulaCalNum;
    }

    public static function getPolicyWithFormula( array $params )
    {
        $obj = self::Filter($params)
            ->with(
                [
                    'Formula' => function($obj)use($params){
                        if (isset($params['policy_expire']) && $params['policy_expire'] != '') {
                            $obj->where("end_time", ">=", $params['policy_expire'])->where("start_time",">=",$params['policy_expire']);
                        }
                    }
                ]
            );
        $data = $obj->get();
        return $data;
    }

    //后返返利规则
    public static function getPolicyWithUpstreamAfterFormula( array $params )
    {
        $obj = self::Filter($params)
            ->with(
                [
                    'UpstreamAfterFormula' => function($obj)use($params){
                        if (isset($params['policy_expire']) && $params['policy_expire'] != '') {
                            $obj->where("end_time", ">=", $params['policy_expire'])->where("start_time",">=",$params['policy_expire']);
                        }
                    }
                ]
            );
        $data = $obj->get();
        return $data;
    }

    //后返返利规则
    public static function getPolicyWithUpstreamDirectFormula( array $params )
    {
//        Capsule::connection()->enableQueryLog();
        $obj = self::Filter($params)
            ->with(
                [
                    'UpstreamDirectFormula' => function($obj)use($params){
                        if (isset($params['policy_expire']) && $params['policy_expire'] != '') {
                            $obj->where("end_time", ">=", $params['policy_expire'])->where("start_time",">=",$params['policy_expire']);
                        }
                    }
                ]
            );
        $data = $obj->get();

//        Log::info('获取规则SQL ', Capsule::connection()->getQueryLog());

        return $data;
    }

    public static function getSuitOilValue(array $params)
    {
        \helper::argumentCheck(['suit_oil'],$params);

        $suitOilArr = explode(',', $params['suit_oil']);
        $ret = [];
        foreach ($suitOilArr as $key=>$value) {
            $ret[$value] = OilType::$oil_type[$value];
        }

        return $ret;
    }

    /**
     * 取免惠最低价绑定的政策数量
     * @param array $params
     * @return mixed
     */
    public static function getMinPriceBindNum(array $params)
    {
        \helper::argumentCheck(['min_price_id'], $params);

        $formulaUsedNum = OilRebatePolicy::getTotal([
            'min_price_id'=>$params['min_price_id'],
            'status'=>1,
        ]);

        return $formulaUsedNum;
    }

    /**
     * 取免惠最低价绑定的政策已计算的数量
     * @param array $params
     * @return mixed
     */
    public static function getMinPriceCalNum(array $params)
    {
        \helper::argumentCheck(['min_price_id'], $params);

        $formulaCalNum = OilRebatePolicy::getTotal([
            'min_price_id'=>$params['min_price_id'],
            'cal_statusIn'=>[RebatePolicy::CA_STATUS_ING,RebatePolicy::CA_STATUS_USED],
            'status'=>1,
        ]);

        return $formulaCalNum;
    }

    /**
     * 取上游公式绑定的政策ID
     * @param $formulaId
     * @param string $fields
     * @return array
     */
    public static function getUpstreamPolicyIdsByFormulaId($formulaId, $fields='*')
    {
        $ret = OilRebatePolicy::where('formula_id', $formulaId)
            ->where('suit_obj', RebatePolicy::SUIT_OBJ_SY)
            ->orWhere('direct_drop_formula_id', $formulaId)
            ->select($fields)
            ->get();

        return !$ret ? [] : $ret->toArray();
    }
}