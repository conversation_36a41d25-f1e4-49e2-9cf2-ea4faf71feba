<?php
/**
 * oil_act
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/12/28
 * Time: 19:41:28
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAct extends \Framework\Database\Model
{
    protected $table = 'oil_act';

    protected $guarded = ["id"];
    protected $fillable = ['uniq_code','title','start_time','end_time','prize_url','condition','is_del','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By uniq_code
        if (isset($params['uniq_code']) && $params['uniq_code'] != '') {
            $query->where('uniq_code', '=', $params['uniq_code']);
        }

        //Search By title
        if (isset($params['title']) && $params['title'] != '') {
            $query->where('title', '=', $params['title']);
        }

        //Search By start_time
        if (isset($params['start_time']) && $params['start_time'] != '') {
            $query->where('start_time', '=', $params['start_time']);
        }

        //Search By end_time
        if (isset($params['end_time']) && $params['end_time'] != '') {
            $query->where('end_time', '=', $params['end_time']);
        }

        //Search By prize_url
        if (isset($params['prize_url']) && $params['prize_url'] != '') {
            $query->where('prize_url', '=', $params['prize_url']);
        }

        //Search By condition
        if (isset($params['condition']) && $params['condition'] != '') {
            $query->where('condition', '=', $params['condition']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_act 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAct::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_act 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAct::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAct::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_act 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAct::create($params);
    }

    /**
     * oil_act 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAct::find($params['id'])->update($params);
    }

    /**
     * oil_act 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAct::destroy($params['ids']);
    }
    
    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return self::Filter($params)->pluck($pluckField)->toArray();
    }
    
    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();
        
        return !empty($res[0]) ? $res[0] : '';
    }
    
    /**
     * 根据条件取信息
     * @param array $params
     * @return object
     */
    static public function getInfoByFilter(array $params)
    {
        
        return self::Filter($params)->first();
    }
    
    /**
     * 发票列表查询
     * @param array $params
     * @return array
     */
    static public function getFilterList(array $params)
    {
        return self::Filter($params)->orderBy('createtime', 'desc')->get()->toArray();
    }
    
    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return self::Filter($params)->count();
    }


}