<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/2/29/029
 * Time: 12:46
 */

namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class TableInfo extends \Framework\Database\Model
{
    public $timestamps = false;

    protected static $dbName = 'gsp_fuel';

    protected $table = 'oil_provinces';

    static public function getAllTables($tbName = NULL)
    {
        if(!$tbName){

        }
        return Capsule::select("SELECT TABLE_NAME,TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '".self::$dbName."'");
    }

    static public function getOneTableColumns($tbName = NULL)
    {
        return Capsule::select("select COLUMN_NAME,column_comment from information_schema.COLUMNS where table_name = '".$tbName."' and table_schema = '".self::$dbName."'");
    }

    static public function getTableInfo($tbName)
    {
        if(!$tbName){

        }
        $data = Capsule::select("SHOW TABLE status LIKE '".$tbName."'");
        $result = [ ['TABLE_NAME'=>$tbName,'TABLE_COMMENT'=>$data[0]->Comment] ];
        return $result;
    }
}