<?php
/**
 * oil_card_register
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardRegister extends \Framework\Database\Model
{
    protected $table = 'oil_card_register';

    protected $guarded = ["id"];

    protected $fillable = ['vice_no','truck_no','status','sendtime','backtime'];

    public function getFillAble()
    {
        return $this->fillable;
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->where('truck_no', '=', $params['truck_no']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By sendtime
        if (isset($params['sendtime']) && $params['sendtime'] != '') {
            $query->where('sendtime', '=', $params['sendtime']);
        }

        //Search By backtime
        if (isset($params['backtime']) && $params['backtime'] != '') {
            $query->where('backtime', '=', $params['backtime']);
        }


        return $query;
    }

    /**
     * oil_card_register 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardRegister::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_card_register 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardRegister::find($params['id']);
    }

    static public function getByIdList(array $ids)
    {
        return OilCardRegister::whereIn('id', $ids)->get()->toArray();
    }

    /**
     * oil_card_register 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardRegister::create($params);
    }

    /**
     * oil_card_register 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardRegister::find($params['id'])->update($params);
    }

    /**
     * oil_card_register 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardRegister::destroy($params['ids']);
    }

    static public function getCardRegisterInfo(array $ids)
    {
        return OilCardRegister::leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_register.vice_no')
            ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
            ->whereNotNull('oil_org.id')
            ->select(Capsule::connection()->raw('oil_card_register.*, oil_card_vice.org_id, oil_org.orgcode'))
            ->whereIn('oil_card_register.id', $ids)->get()->toArray();
    }

    /**
     * g7s卡片设置增加用卡登记功能
     * <AUTHOR>
     * @param array $params
     * @param       $cardTruck
     */
    static public function addForCardSet(array $params,$cardTruck)
    {
        \helper::argumentCheck(['vice_no'],$params);

        $nowTime = date('Y-m-d H:i:s');
        foreach ($params['vice_no'] as $v){
            if(isset($params['truck_no'])){
                //查出用卡登记信息
                $info = OilCardRegister::where('vice_no',$v)->whereIn('status',[1])->first();
                if($params['truck_no']){
                    if(empty($cardTruck[$v])){
                        if(empty($info)){//如果添加车牌号（初始就没有车牌号）
                            //发卡
                            OilCardRegister::add([
                                'vice_no'=>$v,
                                'truck_no'=>$params['truck_no'],
                                'status'=>1,
                                'sendtime'=>$nowTime,
                            ]);
                        }
                    }else if($params['truck_no'] != $cardTruck[$v]){
                        if($info){//如果修改车牌号（原先是有车牌号的，有用卡登记过并未收回）
                            //收回
                            OilCardRegister::where('vice_no',$v)->where('status',1)->update(['status'=>2,'backtime'=>$nowTime]);
                        }
                        //发卡
                        OilCardRegister::add([
                            'vice_no'=>$v,
                            'truck_no'=>$params['truck_no'],
                            'status'=>1,
                            'sendtime'=>$nowTime,
                        ]);
                    }
                }else if($cardTruck[$v]){
                    if($info){//如果修改车牌号为空（原先是有车牌号的，有用卡登记过并未收回）
                        //收回
                        OilCardRegister::where('vice_no',$v)->where('status',1)->update(['status'=>2,'backtime'=>$nowTime]);
                    }
                }
            }
        }
    }

    static public function registerTruckNoAndViceNo($params)
    {
        $data = OilCardRegister::where('vice_no',$params['vice_no'])
            ->where('status',1)->first();

        if($data){
            //have register and edit close
            if($params['truck_no'] != $data->truck_no){
                //old close and create new
                self::editRegister($data);

                self::addNewRegister($params);
            }

        }else{
            self::addNewRegister($params);
        }

        return TRUE;
    }

    static public function addNewRegister($params)
    {
        $params['sendtime'] = date('Y-m-d H:i:s');
        $params['status'] = 1;

        //no register and create
        $insertId = self::add($params);

        //push至Gos系统
        \Fuel\Service\CardRegisterToGos::sendBatchCreateTask([$insertId->id]);
    }

    static public function editRegister($data)
    {
        $data->update([
            'backtime' => date('Y-m-d H:i:s'),
            'status' => 2,
            'updatetime' => date('Y-m-d H:i:s'),
        ]);

        //push至Gos系统
        \Fuel\Service\CardRegisterToGos::sendBatchUpdateTask([$data->id]);
    }

    static public function unBindRegisterByViceNo($params)
    {
        $data = OilCardRegister::where('vice_no',$params['vice_no'])
            ->where('status',1)->first();

        if($data){
            //have register and edit close
            self::editRegister($data);
        }

        return TRUE;
    }

}