<?php
/**
 * 回票认领帐户表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/10/29
 * Time: 11:43:13
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptReturnClaimAccount extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_return_claim_account';

    protected $guarded = ["id"];
    protected $fillable = ['supplier_id','supplier_name','collect_company_id','collect_company_name','oil_type_id','oil_type_name','init_money','init_num','money','num','discount','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By supplier_id
        if (isset($params['supplier_id']) && $params['supplier_id'] != '') {
            $query->where('supplier_id', '=', $params['supplier_id']);
        }

        //Search By supplier_name
        if (isset($params['supplier_name']) && $params['supplier_name'] != '') {
            $query->where('supplier_name', '=', $params['supplier_name']);
        }

        //Search By collect_company_id
        if (isset($params['collect_company_id']) && $params['collect_company_id'] != '') {
            $query->where('collect_company_id', '=', $params['collect_company_id']);
        }

        //Search By collect_company_name
        if (isset($params['collect_company_name']) && $params['collect_company_name'] != '') {
            $query->where('collect_company_name', '=', $params['collect_company_name']);
        }

        //Search By oil_type_id
        if (isset($params['oil_type_id']) && $params['oil_type_id'] != '') {
            $query->where('oil_type_id', '=', $params['oil_type_id']);
        }

        //Search By oil_type_name
        if (isset($params['oil_type_name']) && $params['oil_type_name'] != '') {
            $query->where('oil_type_name', '=', $params['oil_type_name']);
        }

        //Search By init_money
        if (isset($params['init_money']) && $params['init_money'] != '') {
            $query->where('init_money', '=', $params['init_money']);
        }

        //Search By init_num
        if (isset($params['init_num']) && $params['init_num'] != '') {
            $query->where('init_num', '=', $params['init_num']);
        }
        //Search By discount
        if (isset($params['discount']) && $params['discount'] != '') {
            $query->where('discount', '=', $params['discount']);
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }

        //Search By num
        if (isset($params['num']) && $params['num'] != '') {
            $query->where('num', '=', $params['num']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 回票认领帐户表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptReturnClaimAccount::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 回票认领帐户表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptReturnClaimAccount::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptReturnClaimAccount::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 回票认领帐户表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptReturnClaimAccount::create($params);
    }

    /**
     * 回票认领帐户表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptReturnClaimAccount::find($params['id'])->update($params);
    }

    /**
     * 回票认领帐户表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptReturnClaimAccount::destroy($params['ids']);
    }




}