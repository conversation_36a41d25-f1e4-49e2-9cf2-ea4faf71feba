<?php
/**
 * 欠票调整记录
 */

namespace Models;
use Fuel\Defines\CooperationType;
use Fuel\Defines\ReceiptReturnClaimAppConf;
use Fuel\Defines\ReceiptReturnClaimArrearsConf;
use Fuel\Defines\SupplierConf;
use Fuel\Service\OwingTicket\SupplierCompanyStatisticsService;

class OilReceiptReturnClaimArrears extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_return_claim_arrears';

    protected $guarded = ["id"];
    protected $fillable = ['no','supplier_id','supplier_name','cooperation_type','type','oil_type_id','oil_type_name','money','starttime','endtime','status','change_type','remark','deltime','creator','del_user','createtime','updatetime','oil_receipt_return_claim_app_no'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', 'like', '%'.$params['no'].'%');
        }

        //Search By supplier_name
        if (isset($params['supplier_name']) && $params['supplier_name'] != '') {
            $query->where('supplier_name', 'like', '%'.$params['supplier_name'].'%');
        }

        //Search By oil_type_id
        if (isset($params['oil_type_id']) && $params['oil_type_id'] != '') {
            $query->where('oil_type_id', '=', $params['oil_type_id']);
        }

        //Search By oil_receipt_return_claim_app_no
        if (isset($params['oil_receipt_return_claim_app_no']) && $params['oil_receipt_return_claim_app_no'] != '') {
            $query->where('oil_receipt_return_claim_app_no', 'like', '%'.$params['oil_receipt_return_claim_app_no'].'%');
        }

        //Search By change_type
        if (isset($params['change_type']) && $params['change_type'] != '') {
            $query->where('change_type', 'like', '%'.$params['change_type'].'%');
        }


        //Search By type
        if (isset($params['type']) && $params['type'] != '') {
            $query->where('type', '=', $params['type']);
        }

        //Search By cooperation_type
        if (isset($params['cooperation_type']) && $params['cooperation_type'] != '') {
            $query->where('cooperation_type', '=', $params['cooperation_type']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By del_user
        if (isset($params['del_user']) && $params['del_user'] != '') {
            $query->where('del_user', 'like', '%'.$params['del_user'].'%');
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', 'like', '%'.$params['creator'].'%');
        }

        if (isset($params['create_timeGe']) && $params['create_timeGe'] != '') {
            $query->where('createtime', '>=', $params['create_timeGe']);
        }

        if (isset($params['create_timeLe']) && $params['create_timeLe'] != '') {
            $query->where('createtime', '<=', $params['create_timeLe']);
        }

        if (isset($params['deltimeLe']) && $params['deltimeLe'] != '') {
            $query->where('deltime', '<=', $params['deltimeLe']);
        }

        if (isset($params['deltimeGe']) && $params['deltimeGe'] != '') {
            $query->where('deltime', '>=', $params['deltimeGe']);
        }

        return $query;
    }

    /**
     * oil_receipt_return_claim 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptReturnClaimArrears::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        $data = self::preListData($data);

        return $data;
    }




    /**
     * Desc: 整合数据
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 3/8/22 下午5:34
     */
    public static function preListData($data)
    {
        $statusList = ReceiptReturnClaimArrearsConf::$statusList;
        $claim = SupplierConf::$claim;
        foreach ($data as &$v) {
            $v->cooperation_type_txt = CooperationType::getById($v->cooperation_type);
            $v->type_txt = isset($claim[$v->type]) ? $claim[$v->type] : '';
            $v->status   = isset($statusList[$v->status]) ? $statusList[$v->status] : '';
            $statisticsService = new SupplierCompanyStatisticsService();
            //获取 结算节点
            $oilNodeData = $statisticsService->getOilStatisticalNode(ReceiptReturnClaimAppConf::TEMPLAT_ID);
            $v->oil_type_name  = isset($oilNodeData[$v->oil_type_id]) ? $oilNodeData[$v->oil_type_id]['name'] : '';
            $v->starttime  = $v->starttime == '0000-00-00 00:00:00' ? '' : $v->starttime;
            $v->endtime  = $v->endtime == '0000-00-00 00:00:00' ? '' : $v->endtime;
            $v->deltime  = $v->deltime == '0000-00-00 00:00:00' ? '' : $v->deltime;
        }

        return $data;
    }

    /**
     * oil_receipt_return_claim 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptReturnClaimArrears::create($params);
    }

    /**
     * oil_receipt_return_claim 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptReturnClaimArrears::find($params['id'])->update($params);
    }

    /**
     * oil_receipt_return_claim 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptReturnClaimArrears::destroy($params['ids']);
    }

    /**
     * 取某张回票回填的记录列表
     * @return array
     */
    static public function getReceiptClaimList($params)
    {
        \helper::argumentCheck(['receipt_return_id'],$params);

        return OilReceiptReturnClaimArrears::Filter($params)->orderBy('id', 'asc')->get()->toArray();
    }

    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return OilReceiptReturnClaimArrears::Filter($params)->pluck($pluckField)->toArray();
    }

    static public function getFilterList(array $params)
    {
        return OilReceiptReturnClaimArrears::Filter($params)->orderBy('createtime', 'desc')->get()->toArray();
    }

    /**
     * 根据条件更新
     * @param array $params
     * @param array $updateInfo
     * @return mixed
     */
    static public function updateByFilter(array $params, array $updateInfo)
    {
        return OilReceiptReturnClaimArrears::Filter($params)->update($updateInfo);
    }


    /**
     * Desc:
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 8/8/22 上午10:14
     */
    static public function getOilList(){
        $statisticsService = new SupplierCompanyStatisticsService();
        //获取 结算节点
        $oilNodeData = $statisticsService->getOilStatisticalNode(ReceiptReturnClaimAppConf::TEMPLAT_ID);
        $data = [];
        foreach ($oilNodeData as $key=>$val){
            $data[] = [
                'id' => $key,
                'value' => $val['name']
            ];
        }
        return $data;
    }
}