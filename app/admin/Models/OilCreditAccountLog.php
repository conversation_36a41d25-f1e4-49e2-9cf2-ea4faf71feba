<?php
/**
 * 授信账户操作日志
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/04/21
 * Time: 10:05:02
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCreditAccountLog extends \Framework\Database\Model
{
    protected $table = 'oil_credit_account_log';

    protected $guarded = ["id"];
    protected $fillable = ['credit_account_id','account_no','credit_total','status','remark','remark_work','snapshot','creator_id','creator_name','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By credit_account_id
        if (isset($params['credit_account_id']) && $params['credit_account_id'] != '') {
            $query->where('credit_account_id', '=', $params['credit_account_id']);
        }

        //Search By credit_total
        if (isset($params['credit_total']) && $params['credit_total'] != '') {
            $query->where('credit_total', '=', $params['credit_total']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By status_name
        if (isset($params['status_name']) && $params['status_name'] != '') {
            $query->where('status_name', '=', $params['status_name']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        return $query;
    }

    /**
     * 授信账户操作日志 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCreditAccountLog::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 授信账户操作日志 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditAccountLog::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditAccountLog::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 授信账户操作日志 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCreditAccountLog::create($params);
    }

    /**
     * 授信账户操作日志 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCreditAccountLog::find($params['id'])->update($params);
    }

    /**
     * 授信账户操作日志 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCreditAccountLog::destroy($params['ids']);
    }




}