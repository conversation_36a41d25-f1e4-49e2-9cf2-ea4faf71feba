<?php
/**
 * 客服管理
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/09/28
 * Time: 15:47:32
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;
use Framework\SDK\EWei\orderComments;

class OilCustomerService extends \Framework\Database\Model
{
    protected $table = 'oil_customer_service';

    protected $guarded = ["id"];
    protected $fillable = ['title','content','creator_id','last_operator','createtime','updatetime','is_del'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By title
        if (isset($params['title']) && $params['title'] != '') {
            $query->where('title', '=', $params['title']);
        }

        //Search By content
        if (isset($params['content']) && $params['content'] != '') {
            $query->where('content', '=', $params['content']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }

        return $query;
    }

    /**
     * 客服管理 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = orderComments::engineersOrderList($params);

        return $data;
    }

    /**
     * 客服管理 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCustomerService::find($params['id']);
    }

    /**
     * 客服管理 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCustomerService::create($params);
    }

    /**
     * 客服管理 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCustomerService::find($params['id'])->update($params);
    }

    /**
     * 客服管理 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCustomerService::destroy($params['ids']);
    }




}