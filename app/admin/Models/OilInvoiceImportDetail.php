<?php

namespace Models;

use Framework\Database\Model;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilInvoiceImportDetail extends Model
{
    protected $table = 'oil_invoice_import_detail';

    protected $guarded = ["id"];

    protected $fillable = [
        'invoice_type', 'operator_name', 'operator_id', 'business_time',
        'receipt_no', 'receipt_code', 'tax_code',
        'unit', 'num', 'goods_name', 'receipt_amount', 'num_statistics', 'num_taxation',
        'taxpayer_no', 'buyer_name', 'seller_taxpayer_no', 'seller_name', 'province_in',
        'creator_name', 'last_operator', 'createtime', 'updatetime','invoice_goods_name'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        if (isset($params['operator_id']) && $params['operator_id'] != '') {
            $query->where('operator_id', '=', $params['operator_id']);
        }

        if (isset($params['invoice_type']) && $params['invoice_type'] != '') {
            $query->where('invoice_type', '=', $params['invoice_type']);
        }

        if (isset($params['business_time']) && $params['business_time'] != '') {
            $query->where('business_time', '=', $params['business_time']);
        }
        
        if (isset($params['export_business_timeGe']) && $params['export_business_timeGe'] != '') {
            $businessTimeGe = date('Y-m-01 00:00:00', strtotime($params['export_business_timeGe']));
            $query->where('business_time', '>=', $businessTimeGe);
        }
        
        if (isset($params['export_business_timeLe']) && $params['export_business_timeLe'] != '') {
            $businessTimeLe = date('Y-m-01 00:00:00', strtotime($params['export_business_timeLe']));
            $query->where('business_time', '<=', $businessTimeLe);
        }

        if (isset($params['business_timeGe']) && $params['business_timeGe'] != '') {
            $businessTimeGe = date('Y-m-01 00:00:00', strtotime($params['business_timeGe']));
            $query->where('business_time', '>=', $businessTimeGe);
        }

        if (isset($params['business_timeLe']) && $params['business_timeLe'] != '') {
            $businessTimeLe = date('Y-m-01 00:00:00', strtotime($params['business_timeLe']));
            $query->where('business_time', '<=', $businessTimeLe);
        }

        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilInvoiceImportDetail::Filter($params)->select('*')
            ->orderBy('business_time', 'desc')->orderBy('operator_id', 'desc');
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        if (count($data) > 0) {
            foreach ($data as &$v) {
                $v->invoice_type_name = $v->invoice_type == 1 ? '进项' : '销项';
                $v->business_time_ym = date('Y-m', strtotime($v->business_time));
                $v->province_in_text = $v->province_in == 1 ? '省内' : '省外';
                $v->tax_code = (String)$v->tax_code;
                $v->seller_taxpayer_no = (String)$v->seller_taxpayer_no;
                $v->receipt_code = (String)$v->receipt_code;
                $v->taxpayer_no = (String)$v->taxpayer_no;
            }
        }


        return $data;
    }


    static public function querySum($startTime, $endTime, $operatorId, $goodsName, $sumItem, $invoiceType, $provinceIn = -1)
    {
        $sqlObj = OilInvoiceImportDetail::where("operator_id", $operatorId)
            ->where('goods_name', '=', $goodsName)
            ->where('business_time','>=', $startTime)
            ->where('business_time','<', $endTime)
            ->where('invoice_type', $invoiceType);
        if ($provinceIn == 1 || $provinceIn == 0) {
            $sqlObj = $sqlObj->where('province_in', $provinceIn);
        }

        return $sqlObj->sum($sumItem);
    }

    static public function distinctMonth($startTime, $endTime, $operatorId, $invoiceType)
    {
        $sql = "select distinct(MONTH(business_time)) as m
from oil_invoice_import_detail  where  operator_id = ".$operatorId." and business_time >= '".$startTime.
            "' and business_time < '". $endTime. "' and invoice_type = ". $invoiceType;
        return Capsule::connection()->select($sql);
    }

    static public function deleteDetails($businessTime, $operatorId, $invoiceType)
    {
        return OilInvoiceImportDetail::where("operator_id", $operatorId)
            ->where('business_time','=', $businessTime)
            ->where('invoice_type', $invoiceType)
            ->delete();
    }


    static public function querySumByGoodsName($startTime, $endTime, $operatorId, $invoiceType, $provinceIn = -1)
    {
        $sql = "select sum(receipt_amount) as total_amount, sum(num_statistics) as total_num_s, 
       sum(num_taxation) as total_num_t ,goods_name 
from oil_invoice_import_detail where operator_id = ". $operatorId." 
and business_time >= '".$startTime. "' and business_time <= '". $endTime. "' and invoice_type = ". $invoiceType;

        if ($provinceIn == 1 || $provinceIn == 0) {
            $sql = $sql." and province_in = ". $provinceIn;
        }

        $sql = $sql." group by goods_name";

        return Capsule::connection()->select($sql);
    }
    
    static public function querySumByStaticGoodsName($startTime, $endTime, $operatorId, $invoiceType, $provinceIn = -1)
    {
        $sql = "select sum(m_quantity) as m_quantity, sum(m_quantity_province_out) as m_quantity_province_out,
       sum(m_amount) as m_amount ,sum(m_amount_province_out) as m_amount_province_out,sum(m_quantity_statistics) as m_quantity_statistics,sum(m_quantity_statistics_province_out) as m_quantity_statistics_province_out,    oil_type
from oil_invoice_import_statistics where operator_id = ". $operatorId."
and business_time >= '".$startTime. "' and business_time <= '". $endTime. "' and invoice_type = ". $invoiceType;
    
//         if ($provinceIn == 1 || $provinceIn == 0) {
//             $sql = $sql." and province_in = ". $provinceIn;
//         }
    
        $sql = $sql." group by oil_type";
    
        return Capsule::connection()->select($sql);
    }


}
