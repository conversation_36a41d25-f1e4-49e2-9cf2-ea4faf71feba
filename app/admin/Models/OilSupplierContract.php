<?php
/**
 * 供应商与收款公司合同档案表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/09/23
 * Time: 15:23:52
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSupplierContract extends \Framework\Database\Model
{
    protected $table = 'oil_supplier_contract';

    protected $guarded = ["id"];
    protected $fillable = ['supplier_company_id',
        'contract_no',
        'external_no',
        'contract_title',
        'operators_id',
        'first_party_name',
        'second_party_name',
        'contract_type',
        'effective_status',
        'start_time',
        'end_time',
        'open_status',
        'remark',
        'open_user',
        'open_time','status','creator_id','creator_name','last_operator_id','last_operator','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By id
        if (isset($params['check_id']) && $params['check_id'] != '') {
            $query->where('id', '<>', $params['check_id']);
        }

        //Search By id
        if (isset($params['ids']) && $params['ids'] != '') {
            $query->whereIn('id', $params['ids']);
        }

        //Search By supplier_company_id
        if (isset($params['supplier_company_id']) && $params['supplier_company_id'] != '') {
            $query->where('oil_supplier_contract.supplier_company_id', '=', $params['supplier_company_id']);
        }

        //Search By contract_no
        if (isset($params['contract_no']) && $params['contract_no'] != '') {
            $query->where('contract_no', '=', $params['contract_no']);
        }
        //Search By external_no
        if (isset($params['external_no']) && $params['external_no'] != '') {
            $query->where('external_no', '=', $params['external_no']);
        }

        //Search By contract_title
        if (isset($params['contract_title']) && $params['contract_title'] != '') {
            $query->where('contract_title', '=', $params['contract_title']);
        }

        //Search By operators_id
        if (isset($params['operators_id']) && $params['operators_id'] != '') {
            $query->where('operators_id', '=', $params['operators_id']);
        }

        //Search By first_party_name
        if (isset($params['first_party_name']) && $params['first_party_name'] != '') {
            $query->where('first_party_name', '=', $params['first_party_name']);
        }

        //Search By first_party_name
        if (isset($params['first_name_lk']) && $params['first_name_lk'] != '') {
            $query->where('first_party_name', 'like', "%".$params['first_name_lk']."%");
        }

        //Search By second_party_name
        if (isset($params['second_party_name']) && $params['second_party_name'] != '') {
            $query->where('second_party_name', '=', $params['second_party_name']);
        }

        //Search By contract_type
        if (isset($params['contract_type']) && $params['contract_type'] != '') {
            $query->where('contract_type', '=', $params['contract_type']);
        }

        //Search By contract_type
        if (isset($params['effective_status']) && $params['effective_status'] != '') {
//            $query->where('effective_status', '=', $params['effective_status']);
            if ($params['effective_status'] == 10)
            {
                $query->where('start_time', '<=', date("Y-m-d H:i:s",time()));
                $query->where('end_time', '>=', date("Y-m-d H:i:s",time()));
            }
            elseif ($params['effective_status'] == 20)
            {
                $query->whereRaw("(end_time < ".date("Y-m-d H:i:s",time())." or start_time > ".date("Y-m-d H:i:s",time()).")");
            }
        }

        //Search By start_time
        if (isset($params['open_user']) && $params['open_user'] != '') {
            $query->where('open_user', '=', $params['open_user']);
        }

        //Search By start_time
        if (isset($params['open_time']) && $params['open_time'] != '') {
            $query->where('open_time', '=', $params['open_time']);
        }

        //Search By start_time
        if (isset($params['start_time']) && $params['start_time'] != '') {
            $query->where('start_time', '=', $params['start_time']);
        }

        //Search By end_time
        if (isset($params['end_time']) && $params['end_time'] != '') {
            $query->where('end_time', '=', $params['end_time']);
        }

        //Search By open_status
        if (isset($params['open_status']) && $params['open_status'] != '') {
            $query->where('open_status', '=', $params['open_status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }
        //Search By remark
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_supplier_contract.status', '=', $params['status']);
        }
        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By creator_name
        if (isset($params['creator_name']) && $params['creator_name'] != '') {
            $query->where('creator_name', '=', $params['creator_name']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }
        /**
         * 数据列表查询
         */
        if (isset($params['s_contract_no']) && $params['s_contract_no'] != '') {
            $query->where('oil_supplier_contract.contract_no', 'like', "%".$params['s_contract_no']."%");
        }

        if (isset($params['s_contract_title']) && $params['s_contract_title'] != '') {
            $query->where('oil_supplier_contract.contract_title', 'like', "%".$params['s_contract_title']."%");
        }

        if (isset($params['s_second_party_name']) && $params['s_second_party_name'] != '') {
            $query->where('oil_supplier_contract.second_party_name', 'like', "%".$params['s_second_party_name']."%");
        }

        if (isset($params['create_start_time']) && $params['create_start_time'] != '') {
            $query->where('oil_supplier_contract.oil_supplier_contract.createtime', '>=', $params['create_start_time']);
        }
        if (isset($params['create_end_time']) && $params['create_end_time'] != '') {
            $query->where('oil_supplier_contract.createtime', '<=', $params['create_end_time']);
        }
        if (isset($params['update_start_time']) && $params['update_start_time'] != '') {
            $query->where('oil_supplier_contract.updatetime', '>=', $params['update_start_time']);
        }
        if (isset($params['update_end_time']) && $params['update_end_time'] != '') {
            $query->where('oil_supplier_contract.updatetime', '<=', $params['update_end_time']);
        }

        return $query;
    }

    /**
     * 供应商与收款公司合同档案表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilSupplierContract::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 供应商与收款公司合同档案表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierContract::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierContract::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 供应商与收款公司合同档案表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSupplierContract::create($params);
    }

    /**
     * 供应商与收款公司合同档案表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierContract::find($params['id'])->update($params);
    }

    /**
     * 批量修改
     * @param array $where
     * @param array $params
     * @return mixed
     */
    static public function batchEdit(array $where,array $params)
    {
        return self::Filter($where)->update($params);
    }

    /**
     * 供应商与收款公司合同档案表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSupplierContract::destroy($params['ids']);
    }

    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return self::Filter($params)->pluck($pluckField)->toArray();
    }

    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();

        return !empty($res[0]) ? $res[0] : '';
    }

    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return self::Filter($params)->count();
    }

    /**
     * 根据条件取信息
     * @param array $params
     * @param mixed $field
     * @return OilSupplierAccount
     */
    static public function getInfoByFilter(array $params,$field = "*")
    {
        return self::Filter($params)->select($field)->first();
    }

    /**
     * 根据条件 查询 指定字段 数据列表
     * @param array $params
     * @param string $field
     * @return mixed
     */
    static public function getListByFilter(array $params,$field = ["*"])
    {
        return self::Filter($params)->select($field)->get()->toArray();
    }

}