<?php
/**
 * 发票销售明细
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/02/21
 * Time: 08:33:18
 */
namespace Models;
use Fuel\Defines\ReceiptApplyDefine;
use Fuel\Defines\ReceiptScope;
use Fuel\Defines\Unit;
use Fuel\Service\ReceiptSalesDetails;
use Fuel\Service\TypeCategoryService;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Defines\OilType;

class OilReceiptSalesDetailsRelation extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_sales_details_relation';

    protected $guarded = ["id"];
    protected $fillable = ['sales_id','type_no_id','sale_no','line_code','oil_type','oil_sec_type','unit',
        'tax_no','sku','snapshot_oil_sec_type','snapshot_sku', 'unit_price','num','amount','tax_rate',
        'disamount','last_operator','createtime','updatetime','snapshot_unit_price','snapshot_num','addr_name',
        'addr_mobile','address','discount_price'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By sales_id
        if (isset($params['sales_id']) && $params['sales_id'] != '') {
            $query->where('sales_id', '=', $params['sales_id']);
        }

        //Search By sales_idIn
        if (isset($params['sales_idIn']) && $params['sales_idIn'] != '') {
            $query->whereIn('sales_id', $params['sales_idIn']);
        }

        //Search By type_no_id
        if (isset($params['type_no_id']) && $params['type_no_id'] != '') {
            $query->where('type_no_id', '=', $params['type_no_id']);
        }

        //Search By sale_no
        if (isset($params['sale_no']) && $params['sale_no'] != '') {
            $query->where('sale_no', '=', $params['sale_no']);
        }

        //Search By line_code
        if (isset($params['line_code']) && $params['line_code'] != '') {
            $query->where('line_code', '=', $params['line_code']);
        }

        //Search By oil_type
        if (isset($params['oil_type']) && $params['oil_type'] != '') {
            $query->where('oil_type', '=', $params['oil_type']);
        }

        //Search By oil_sec_type
        if (isset($params['oil_sec_type']) && $params['oil_sec_type'] != '') {
            $query->where('oil_sec_type', '=', $params['oil_sec_type']);
        }

        //Search By unit
        if (isset($params['unit']) && $params['unit'] != '') {
            $query->where('unit', '=', $params['unit']);
        }

        //Search By tax_no
        if (isset($params['tax_no']) && $params['tax_no'] != '') {
            $query->where('tax_no', '=', $params['tax_no']);
        }

        //Search By sku
        if (isset($params['sku']) && $params['sku'] != '') {
            $query->where('sku', '=', $params['sku']);
        }

        //Search By snapshot_oil_sec_type
        if (isset($params['snapshot_oil_sec_type']) && $params['snapshot_oil_sec_type'] != '') {
            $query->where('snapshot_oil_sec_type', '=', $params['snapshot_oil_sec_type']);
        }

        //Search By snapshot_sku
        if (isset($params['snapshot_sku']) && $params['snapshot_sku'] != '') {
            $query->where('snapshot_sku', '=', $params['snapshot_sku']);
        }

        //Search By unit_price
        if (isset($params['unit_price']) && $params['unit_price'] != '') {
            $query->where('unit_price', '=', $params['unit_price']);
        }

        //Search By num
        if (isset($params['num']) && $params['num'] != '') {
            $query->where('num', '=', $params['num']);
        }

        //Search By amount
        if (isset($params['amount']) && $params['amount'] != '') {
            $query->where('amount', '=', $params['amount']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By unit_price
        if (isset($params['unit_priceLt']) && $params['unit_priceLt'] != '') {
            $query->where('unit_price', '<', $params['unit_priceLt']);
        }

        return $query;
    }

    /**
     * 发票销售明细 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptSalesDetailsRelation::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('line_code', 'asc')->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        if (count($data) > 0) {
            $data = self::preFormatData($data);
        }

        return $data;
    }

    static public function preFormatData($data)
    {
        $saleIds = [];
        foreach ($data as $v) {
            $saleIds[] = $v->sales_id;
        }
        $apply = (new ReceiptSalesDetails())->batchGetApplyInfoBySalesId($saleIds);

//        $oilConfig = TypeCategoryService::getOilConfigData(ReceiptApplyDefine::OIL_TEMPLATE);

        foreach ($data as &$val) {

            $val->waste_num = bcsub($val->num,$val->snapshot_num,6); // 浪费库存

//            if (isset($apply[$val->sales_id]) && $apply[$val->sales_id]->is_internal == ReceiptScope::INTERNAL) {
//                $val->oil_sec_type_value = $oilConfig['second'][$val->oil_sec_type]['name'];
//
//                $pid = $oilConfig['second'][$val->oil_sec_type]['pid'];
//                $val->oil_type_value = $oilConfig['top'][$pid]['name'];
//
//                $secondOilType = $val->oil_sec_type;
//            } else {
//                $val->oil_type_value = OilType::$oil_type[$val->oil_type];
//                $val->oil_sec_type_value = OilType::$oil_sec_type[$val->oil_sec_type]['title'];
//
//                $secondOilType = OilType::$oil_sec_type[$val->oil_sec_type]['second_oil_type_id'];
//            }
            $val->oil_type_value = OilType::$oil_type[$val->oil_type];
            $val->oil_sec_type_value = OilType::$oil_sec_type[$val->oil_sec_type]['title'];


            $secondOilType = OilType::$oil_sec_type[$val->oil_sec_type]['second_oil_type_id'];

            if (isset($apply[$val->sales_id]) && $apply[$val->sales_id]->unit == Unit::INVOICE_TON) { // 吨票
                $unit = OilType::transformValueToId($val->unit);
                $val->unit = Unit::getTonValue();
                $val->num = Unit::transform($secondOilType, $unit, Unit::getTonId(), $val->num);
                $val->unit_price = rtrim(sprintf('%0.9f', bcdiv(bcadd($val->amount, $val->disamount,2), $val->num,9)), '0');
                $val->waste_num = Unit::transform($secondOilType, $unit, Unit::getTonId(), $val->waste_num);
            }
        }

        return $data;
    }

    /**
     * 发票销售明细 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptSalesDetailsRelation::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptSalesDetailsRelation::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 发票销售明细 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptSalesDetailsRelation::create($params);
    }

    static public function batchAdd(array $params)
    {
        return OilReceiptSalesDetailsRelation::insert($params);
    }

    /**
     * 发票销售明细 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptSalesDetailsRelation::find($params['id'])->update($params);
    }

    /**
     * 发票销售明细 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptSalesDetailsRelation::destroy($params['ids']);
    }

    /**
     * 根据条件取信息
     * @param array $params
     * @return object
     */
    static public function getInfoByFilter(array $params)
    {

        return OilReceiptSalesDetailsRelation::Filter($params)->first();
    }

    /**
     * 发票销售明细 编辑
     * @param array $params
     * @return mixed
     */
    static public function updateBySaleNoAndLineCode(array $params)
    {
        \helper::argumentCheck(['sale_no', 'line_code'],$params);

        return self::where('sale_no', '=', $params['sale_no'])->where('line_code', '=', $params['line_code'])->update($params);
    }

    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return OilReceiptSalesDetailsRelation::Filter($params)->count();
    }

    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getSumAmount(array $params)
    {
        return OilReceiptSalesDetailsRelation::Filter($params)->sum('amount');
    }

    /**
     * 发票销售明细 列表查询
     * @param array $params
     * @return array
     */
    static public function getFilterList(array $params)
    {
        return OilReceiptSalesDetailsRelation::Filter($params)->orderBy('createtime', 'desc')->get()->toArray();
    }

    /**
     * 发票销售明细 列表查询
     * @param array $params
     * @param $field
     * @return array
     */
    static public function getPluckFieldByFilter(array $params,$field)
    {
        return OilReceiptSalesDetailsRelation::Filter($params)->orderBy('createtime', 'desc')->pluck($field)->toArray();
    }

}