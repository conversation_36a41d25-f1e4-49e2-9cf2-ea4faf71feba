<?php
/**
 * oil_account_assign_task
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/05/23
 * Time: 21:14:51
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAccountAssignTask extends \Framework\Database\Model
{
    protected $table = 'oil_account_assign_task';

    protected $guarded = ["id"];

    protected $fillable = ['assign_id','serialnumber','main_no','taskId','status','creator_id','type','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function AccountAssignTaskDetail()
    {
        return $this->hasMany('Models\OilAccountAssignTaskDetail','taskId','taskId');
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By assign_id
        if (isset($params['assign_id']) && $params['assign_id'] != '') {
            $query->where('assign_id', '=', $params['assign_id']);
        }

        //Search By task_no
        if (isset($params['task_no']) && $params['task_no'] != '') {
            $query->where('task_no', '=', $params['task_no']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * oil_account_assign_task 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAccountAssignTask::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_account_assign_task 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountAssignTask::find($params['id']);
    }

    /**
     * oil_account_assign_task 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAccountAssignTask::create($params);
    }

    /**
     * oil_account_assign_task 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['taskId'],$params);

        return OilAccountAssignTask::where('taskId',$params['taskId'])->update($params);
    }

    /**
     * oil_account_assign_task 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountAssignTask::destroy($params['ids']);
    }

    /**
     * 获取任务ids
     * @param $assignId
     * @return mixed
     */
    static public function getTaskIds($assignId)
    {
        return OilAccountAssignTask::where('assign_id',$assignId)->whereNotNull('taskId')->where('type',1)->where('status',0)
            ->pluck('taskId')->toArray();
    }

    /**
     * 获取任务serialnumber
     * @param $assignId
     * @return mixed
     */
    static public function getSerialnumbers($assignId)
    {
        return OilAccountAssignTask::where('assign_id',$assignId)
            ->where('type',1)
//             ->where('status','!=',10)
//            ->where(function ($query){
//                //$query->whereNull('taskId');
//                $query->orWhere('status',0);
//            })
            ->get();
    }

    static public function getTaskInfo(array $params)
    {
        $data = OilAccountAssignTask::Filter($params)->orderBy("id","desc")->first();
        return $data;
    }
}