<?php
/**
 * oil_pay_config
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/11/28
 * Time: 15:41:18
 */
namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Defines\PayType;
use Fuel\Defines\PayConfigStatus;
use Fuel\Defines\FeeType;

class OilPayConfig extends \Framework\Database\Model
{
    protected $table = 'oil_pay_config';

    protected $guarded  = ["id"];
    protected $fillable = ['account_no', 'pay_type', 'pay_channel', 'operators_id', 'fee_type', 'fee_ratio', 'fee_ceil', 'status', 'remark', 'is_del','creator_id', 'creator', 'last_operator_id', 'last_operator', 'createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By account_no
        if (isset($params['account_no']) && $params['account_no'] != '') {
            $query->where('account_no', '=', $params['account_no']);
        }

        //Search By pay_type
        if (isset($params['pay_type']) && $params['pay_type'] != '') {
            $query->where('pay_type', '=', $params['pay_type']);
        }

        //Search By pay_channel
        if (isset($params['pay_channel']) && $params['pay_channel'] != '') {
            $query->where('pay_channel', '=', $params['pay_channel']);
        }

        //Search By operators_id
        if (isset($params['operators_id']) && $params['operators_id'] != '') {
            $query->where('operators_id', '=', $params['operators_id']);
        }

        //Search By fee_type
        if (isset($params['fee_type']) && $params['fee_type'] != '') {
            $query->where('fee_type', '=', $params['fee_type']);
        }

        //Search By fee_ratio
        if (isset($params['fee_ratio']) && $params['fee_ratio'] != '') {
            $query->where('fee_ratio', '=', $params['fee_ratio']);
        }

        //Search By fee_ratioGe
        if (isset($params['fee_ratioGe']) && $params['fee_ratioGe'] != '') {
            $query->where('fee_ratio', '>=', $params['fee_ratioGe']);
        }

        //Search By fee_ratioLe
        if (isset($params['fee_ratioLe']) && $params['fee_ratioLe'] != '') {
            $query->where('fee_ratio', '<=', $params['fee_ratioLe']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By is_del
        if (isset($params['is_del'])) {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By is_delIn
        if (isset($params['is_delIn']) && $params['is_delIn'] != '') {
            $query->whereIn('is_del', explode(',',$params['is_delIn']));
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('creator_id', '=', $params['creator_id']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_pay_config 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilPayConfig::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            if(!isset($params['limit'])){
                $params['page'] = 1;
                $params['limit'] = 10000;
            }
            $orderField = isset($params['orderField']) ? $params['orderField'] : 'createtime';
            $orderType = isset($params['orderType']) ? $params['orderType'] : 'desc';
            $data = $sqlObj->orderBy($orderField, $orderType)->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        $data = OilPayConfig::preHandleData($data);

        return $data;
    }

    /**
     * 预处理数据
     * @param $data
     * @return mixed
     */
    static private function preHandleData($data)
    {
        foreach ($data as &$v) {
            $v->_pay_type = PayType::getById($v->pay_type);
            $v->_fee_type = FeeType::getById($v->fee_type);
            $v->_status   = PayConfigStatus::getById($v->status);
            $v->_fee_ceil = $v->fee_ceil ? $v->fee_ceil . '元' : '';
            $v->operators_name = OilOperators::getById(['id'=>$v->operators_id])->name;

            if($v->fee_type == 1){
                $v->_fee_ratio = sprintf('%.2f',$v->fee_ratio).'%';
            }else if($v->fee_type == 2){
                $v->_fee_ratio = sprintf('%.2f',$v->fee_ratio).'元';
            }
        }

        return $data;
    }

    /**
     * 规则：如果支付方式、渠道、运营商全部相同，则判断为重复的支付方式
     * @param array $params
     */
    static public function checkUniquePayConfig(array $params)
    {
        \helper::argumentCheck(['operators_id', 'pay_type', 'pay_channel'], $params);

        $data = OilPayConfig::where('pay_type', $params['pay_type'])->where('pay_channel', $params['pay_channel'])
            ->where('operators_id', $params['operators_id'])
            ->where('is_del', 0)->first();
        if ($data) {
            throw new \RuntimeException('添加失败：重复的支付方式', 2);
        }
    }


    static public function checkUniqueAccountNo(array $params)
    {
        \helper::argumentCheck(['account_no'], $params);

        $data = OilPayConfig::where('account_no', $params['account_no'])->where('is_del', 0)->first();
        if ($data) {
            throw new \RuntimeException('操作失败：账号【' . $params['account_no'] . '】已存在', 2);
        }
    }

    /**
     * oil_pay_config 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilPayConfig::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilPayConfig::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_pay_config 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilPayConfig::create($params);
    }

    /**
     * oil_pay_config 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilPayConfig::find($params['id'])->update($params);
    }

    /**
     * oil_pay_config 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilPayConfig::destroy($params['ids']);
    }

    /**
     * 查出支付基础信息
     * @param array $params
     * @return mixed
     */
    static public function getPayConfigInfo(array $params)
    {
        \helper::argumentCheck(['pay_type','pay_channel','operators_id'], $params);

        $info = OilPayConfig::lockForUpdate()->where('pay_type', $params['pay_type'])->where('pay_channel', $params['pay_channel'])
            ->where('operators_id', $params['operators_id'])->where('is_del', 0)->where('status',1)->first();

        if($info === NULL){
            throw new \RuntimeException('支付基础信息不存在',2);
        }

        return $info;
    }

    /**
     * 根据账号获取支付基础信息
     * @param array $params
     * @return mixed
     */
    static public function getPayConfigByAccountNo(array $params)
    {
        $info = OilPayConfig::where('account_no', $params['account_no'])->where('is_del',0)->where('status',1)->first();
        if($info === NULL){
            throw new \RuntimeException('支付基础信息不存在',2);
        }

        return $info;
    }

    /**
     * @title 根据账号获取支付（收款信息）信息
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getOperatorsByAccountNo(array $params)
    {
        \helper::argumentCheck(['account_no'], $params);

        return OilPayConfig::leftJoin('oil_operators','oil_operators.id','=','oil_pay_config.operators_id')
            ->selectRaw('oil_pay_config.operators_id ,oil_operators.sub_name,oil_operators.company_name,oil_operators.company_code,oil_pay_config.id as pay_id,
            oil_pay_config.pay_type,oil_pay_config.pay_channel')
            ->where('oil_pay_config.account_no',$params['account_no'])
            ->first();
    }

    static public function getOperatorsByOperatorsId(array $params)
    {
        \helper::argumentCheck(['operators_id'], $params);

        return OilPayConfig::leftJoin('oil_operators','oil_operators.id','=','oil_pay_config.operators_id')
            ->where('oil_pay_config.operators_id',$params['operators_id'])
            ->pluck('oil_pay_config.account_no')
            ->toArray();
    }
}