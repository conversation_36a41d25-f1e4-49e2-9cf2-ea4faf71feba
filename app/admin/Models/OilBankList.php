<?php
/**
 * OilBankList
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/01/31
 * Time: 14:39:35
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilBankList extends \Framework\Database\Model
{
    protected $table = 'oil_bank_list';

    protected $guarded = ["id"];

    protected $fillable = ['cnaps_code', 'bank_name','province','city','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By cnaps_code
        if (isset($params['cnaps_code']) && $params['cnaps_code'] != '') {
            $query->where('cnaps_code', '=', $params['cnaps_code']);
        }

        //Search By bank_name
        if (isset($params['bank_name']) && $params['bank_name'] != '') {
            $query->where('bank_name', '=', $params['bank_name']);
        }
        //Search By bank_nameLk
        if (isset($params['bank_nameLk']) && $params['bank_nameLk'] != '') {
            $query->where('bank_name', 'like', '%'.$params['bank_nameLk']."%");
        }

        //Search By province
        if (isset($params['province']) && $params['province'] != '') {
            $query->where('province', '=', $params['province']);
        }

        //Search By city
        if (isset($params['city']) && $params['city'] != '') {
            $query->where('city', '=', $params['city']);
        }
        return $query;
    }

    /**
     * 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilBankList::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 获取前200个银行信息
     * @param array $params
     * @return object
     */
    static public function getLimitBank(array $params)
    {
        $sqlObj = OilBankList::Filter($params);
        $data = $sqlObj->orderBy('createtime', 'desc')->limit(200)->get();
        return $data;
    }

    /**
     * 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilBankList::find($params['id']);
    }

    /**
     * 详情查询
     * @param array $params
     * @return object
     */
    static public function getByFilter(array $params)
    {
        return OilBankList::Filter($params)->first();
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilBankList::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilBankList::create($params);
    }

    /**
     * 批量新增
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return OilBankList::insert($params);
    }

    /**
     * 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilBankList::find($params['id'])->update($params);
    }

    /**
     * 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilBankList::destroy($params['ids']);
    }




}