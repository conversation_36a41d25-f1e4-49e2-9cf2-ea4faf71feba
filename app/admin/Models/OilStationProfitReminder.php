<?php
/**
 * 油站负收益提醒记录表
 */

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class OilStationProfitReminder extends \Framework\Database\Model
{
    protected $table = 'oil_station_profit_reminder';

    protected $guarded = ['id'];
    protected $fillable = ['stat_day', 'email', 'station_code', 'createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 根据条件取信息
     * @param array $params
     * @param mixed $field
     * @return OilSupplierAccount
     */
    static public function getInfoByFilter(array $params, $field = "*")
    {
        return self::Filter($params)->select($field)->first();
    }
}