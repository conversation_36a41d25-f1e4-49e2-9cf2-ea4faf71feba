<?php
/**
 * 渠道签约运营商
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2019/10/23
 * Time: 17:11:19
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSupplierOperator extends \Framework\Database\Model
{
    protected $table = 'oil_supplier_operator';

    protected $guarded = ["id"];
    protected $fillable = ['supplier_id','gec_bank_id','operator_id','bank_account_no','branch_no','company_name','receipt_title','start_time',
        'end_time','is_on', 'is_del', 'remark','createtime','updatetime', 'bank_name', 'start_date', 'end_date','unin_code','merchant_id','bank_account_id','eas_code'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            if (is_array($params['id'])) {
                $query->whereIn('id', $params['id']);
            } else {
                $query->where('id', '=', $params['id']);
            }
        }

        //Search By supplier_id
        if (isset($params['supplier_id']) && $params['supplier_id'] != '') {
            $query->where('supplier_id', '=', $params['supplier_id']);
        }

        //Search By gec_bank_id
        if (isset($params['gec_bank_id']) && $params['gec_bank_id'] != '') {
            $query->where('gec_bank_id', '=', $params['gec_bank_id']);
        }

        //Search By operator_id
        if (isset($params['operator_id']) && $params['operator_id'] != '') {
            $query->where('operator_id', '=', $params['operator_id']);
        }
    
        //Search By eas_code
        if (isset($params['eas_code']) && $params['eas_code'] != '') {
            $query->where('eas_code', '=', $params['eas_code']);
        }
    
        //Search By merchant_id
        if (isset($params['merchant_id']) && $params['merchant_id'] != '') {
            $query->where('merchant_id', '=', $params['merchant_id']);
        }
    
        //Search By bank_account_id
        if (isset($params['bank_account_id']) && $params['bank_account_id'] != '') {
            $query->where('bank_account_id', '=', $params['bank_account_id']);
        }
        
        //Search By bank_account_no
        if (isset($params['bank_account_no']) && $params['bank_account_no'] != '') {
            $query->where('bank_account_no', '=', $params['bank_account_no']);
        }
    
        //Search By bank_name
        if (isset($params['bank_name']) && $params['bank_name'] != '') {
            $query->where('bank_name', '=', $params['bank_name']);
        }
    
        //Search By unin_code
        if (isset($params['unin_code']) && $params['unin_code'] != '') {
            $query->where('unin_code', '=', $params['unin_code']);
        }

        //Search By branch_no
        if (isset($params['branch_no']) && $params['branch_no'] != '') {
            $query->where('branch_no', '=', $params['branch_no']);
        }

        //Search By company_name
        if (isset($params['company_name']) && $params['company_name'] != '') {
            $query->where('company_name', '=', $params['company_name']);
        }

        //Search By receipt_title
        if (isset($params['receipt_title']) && $params['receipt_title'] != '') {
            $query->where('receipt_title', '=', $params['receipt_title']);
        }

        //Search By start_time
        if (isset($params['start_time']) && $params['start_time'] != '') {
            $query->where('start_time', '=', $params['start_time']);
        }

        //Search By end_time
        if (isset($params['end_time']) && $params['end_time'] != '') {
            $query->where('end_time', '=', $params['end_time']);
        }
    
        //Search By start_date
        if (isset($params['start_date']) && $params['start_date'] != '') {
            $query->where('start_date', '=', $params['start_date']);
        }
    
        //Search By end_date
        if (isset($params['end_date']) && $params['end_date'] != '') {
            $query->where('end_date', '=', $params['end_date']);
        }

        //Search By is_on
        if (isset($params['is_on']) && $params['is_on'] != '') {
            $query->where('is_on', '=', $params['is_on']);
        }
    
        //Search By is_del
        if (isset($params['is_del'])) {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }
    
    public function OilOperators()
    {
        return $this->belongsTo('Models\OilOperators','operator_id','id');
    }
    
    /**
     * 渠道签约运营商 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $params['is_del'] = 0;
        $sqlObj = OilSupplierOperator::Filter($params)->with([
                    'OilOperators' => function ($query) {
                $query->select('id', 'name', 'createtime');
            }
        ]);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
    
        if(count($data) > 0){
            foreach($data as &$v){
                $v->operator_name = isset($v->OilOperators) && $v->OilOperators ? $v->OilOperators->name : '';
                $v->is_on_value = ($v->is_on == 1) ? '是' :  '否';
            }
        }

        return $data;
    }

    /**
     * 渠道签约运营商 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierOperator::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierOperator::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 渠道签约运营商 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSupplierOperator::create($params);
    }

    /**
     * 渠道签约运营商 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierOperator::find($params['id'])->update($params);
    }

    /**
     * 渠道签约运营商 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSupplierOperator::destroy($params['ids']);
    }
    
    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return OilSupplierOperator::Filter($params)->pluck($pluckField)->toArray();
    }
    
    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = OilSupplierOperator::Filter($params)->pluck($pluckField)->toArray();
        
        return !empty($res[0]) ? $res[0] : '';
    }
    
    /**
     * 根据条件取信息
     * @param array $params
     * @return object
     */
    static public function getInfoByFilter(array $params)
    {
        return OilSupplierOperator::Filter($params)->first();
    }
    
    /**
     * 取当前在使用的数量
     * @param array $params
     * @return object
     */
    static public function getTotal($params)
    {
        return count(self::getPluckFields($params, 'id'));
    }
    
    
    static public function getSupplierOpeartors($supplier_id)
    {
        $operator_ids = self::getPluckFields(['supplier_id'=>$supplier_id], 'operator_id');
        
        $ret = [];
        if ($operator_ids) {
            $ret = \Models\OilOperators::getByIdMap(['idList'=>$operator_ids]);
        }
        
        return $ret;
    }
    
    /**
     * 根据条件更新
     * @param array $params
     * @param array $updateInfo
     * @return mixed
     */
    static public function updateByFilter(array $params, array $updateInfo)
    {
        return self::Filter($params)->update($updateInfo);
    }
}