<?php
/**
 * 机构资金账户表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:17
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilAccountMoneyCharge;
use Framework\Log;

class OilAccountMoney extends \Framework\Database\Model
{
    protected $table = 'oil_account_money';

    protected $guarded = ["id"];

    protected $fillable = ['subAccountID', 'account_no','org_id','money','cash_fanli_remain','fanli_discount_remain',
        'charge_total','assign_total','fanli_total','last_charge_time','createtime','updatetime','total_transfer_in',
        'total_transfer_out','service_total','shared_card_trade_total','is_fanli_init','init_time'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function CardVice()
    {
        return $this->hasMany('Models\OilCardVice','org_id','org_id');
    }

    public function Org()
    {
        return $this->belongsTo('Models\OilOrg','org_id','id');
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By account_no
        if (isset($params['account_no']) && $params['account_no'] != '') {
            if (is_array($params['account_no'])) {
                $query->whereIn('account_no', $params['account_no']);
            } else {
                $query->where('account_no', '=', $params['account_no']);
            }
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('oil_account_money.org_id', '=', $params['org_id']);
        }
        if (isset($params['org_id_list']) && $params['org_id_list'] != '') {
            $query->whereIn('oil_account_money.org_id', $params['org_id_list']);
        }

        if (isset($params['orgcode_like']) && $params['orgcode_like'] != '') {
            $orgIds = OilOrg::getByOrgcodeLike($params['orgcode_like']);
            $query->whereIn('oil_account_money.org_id', $orgIds);
        }

        if (isset($params['orgcode_list']) && $params['orgcode_list']) {
            $orgIds = OilOrg::getByOrgCodesFieldArr(explode(',', $params['orgcode_list']));
            $query->whereIn('oil_account_money.org_id', $orgIds);
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }

        //Search By money_ge
        if (isset($params['money_ge']) && $params['money_ge'] != '') {
            $query->where('money', '>=', $params['money_ge']);
        }

        //Search By money_le
        if (isset($params['money_le']) && $params['money_le'] != '') {
            $query->where('money', '<=', $params['money_le']);
        }

        //Search By cash_fanli_remain
        if (isset($params['cash_fanli_remain']) && $params['cash_fanli_remain'] != '') {
            $query->where('cash_fanli_remain', '=', $params['cash_fanli_remain']);
        }

        //Search By fanli_discount_remain
        if (isset($params['fanli_discount_remain']) && $params['fanli_discount_remain'] != '') {
            $query->where('fanli_discount_remain', '=', $params['fanli_discount_remain']);
        }

        //Search By charge_total
        if (isset($params['charge_total']) && $params['charge_total'] != '') {
            $query->where('charge_total', '=', $params['charge_total']);
        }

        //Search By assign_total
        if (isset($params['assign_total']) && $params['assign_total'] != '') {
            $query->where('assign_total', '=', $params['assign_total']);
        }

        //Search By fanli_total
        if (isset($params['fanli_total']) && $params['fanli_total'] != '') {
            $query->where('fanli_total', '=', $params['fanli_total']);
        }

        //Search By last_charge_time
        if (isset($params['last_charge_time']) && $params['last_charge_time'] != '') {
            $query->where('last_charge_time', '=', $params['last_charge_time']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By oil_org.is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('oil_org.is_del', '=', $params['is_del']);
        }

        //Search By oil_org.org_code
        if (isset($params['orgcodes']) && $params['orgcodes'] != '') {
            $query->whereIn('oil_org.orgcode', explode(',',$params['orgcodes']));
        }

        //Search By subAccountIDNotNull
        if (isset($params['subAccountIDNotNull']) && $params['subAccountIDNotNull'] == 1) {
            $query->whereNotNull('oil_account_money.subAccountID');
        }

        if (isset($params['fanli_discount_remainLt']) && $params['fanli_discount_remainLt'] != "") {
            $query->where('oil_account_money.fanli_discount_remain','<',$params['fanli_discount_remainLt'])->where('oil_account_money.fanli_discount_remain','>',0);
        }

        return $query;
    }

    /**
     * 机构资金账户表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params, $connection = '')
    {

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj  = OilAccountMoney::on($connection)->select(Capsule::connection('')->raw('oil_account_money.*,oil_org.orgcode,oil_org.org_name,COUNT(oil_card_vice.id) vice_card_num'))
            ->leftJoin('oil_org','oil_account_money.org_id','=','oil_org.id')
            ->leftJoin('oil_card_vice','oil_account_money.org_id','=','oil_card_vice.org_id')
            ->where('oil_org.is_del',0);
        $oilAccountMoney = new OilAccountMoney();
        $sqlObj = $oilAccountMoney->scopeFilter($sqlObj,$params);

        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj
                    ->orderBy('oil_org.orgcode','asc')
                    ->groupBy('oil_account_money.org_id')
                    ->get();
        } elseif (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj
                    ->orderBy('oil_org.orgcode','asc')
                    ->groupBy('oil_account_money.org_id')
                    ->skip(intval($params['skip']))
                    ->take(intval($params['take']))
                    ->get();
        } elseif (isset($params['count']) && intval($params['count']) == 1) {
            $data = $sqlObj->count(Capsule::connection()->raw('DISTINCT(oil_account_money.org_id)'));
        } else{
            $data = $sqlObj
                    ->orderBy('oil_org.orgcode','asc')
                    ->groupBy('oil_account_money.org_id')
                    ->paginate($params['limit'],['*'],'page',$params['page']);
        }

        foreach($data as $k=>$v){
            if(isset($v->money)){
                $v->money = floatval($v->money);
            }
            if(isset($v->charge_total)){
                $v->charge_total = floatval($v->charge_total);
            }
            if(isset($v->assign_total)){
                $v->assign_total = floatval($v->assign_total);
            }
            if(isset($v->fanli_total)){
                $v->fanli_total = floatval($v->fanli_total);
            }
            if(isset($v->total_transfer_in)){
                $v->total_transfer_in = floatval($v->total_transfer_in);
            }
            if(isset($v->total_transfer_out)){
                $v->total_transfer_out = floatval($v->total_transfer_out);
            }
            $v->cash_fanli_remain = $v->is_fanli_init == 1 ? $v->cash_fanli_remain : '--';
        }

        return $data;
    }

    static public function getAccountInfo()
    {
        return OilAccountMoney::get();
    }

    static public function getFilterList($params)
    {
        $data = self::Filter($params)
            ->leftJoin('oil_org','oil_account_money.org_id','=','oil_org.id')
            ->select('oil_org.orgcode','oil_org.org_name','oil_account_money.account_no','oil_account_money.money')
            ->get()
            ->toArray();

        return $data;
    }

    /**
     * 机构资金账户表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountMoney::find($params['id']);
    }

    static public function getByIdList(array $params)
    {
        return OilAccountMoney::with(
            [
                'Org'   => function($query){
                    $query->with([
                        'Operators'
                    ]);
                }
            ]
        )
            ->select('oil_account_money.*')
            ->leftJoin('oil_org','oil_org.id','=','oil_account_money.org_id')
            ->where('oil_org.is_del','=','0')
            ->whereIn('oil_account_money.id', $params['idList'])->get();
    }

    /**
     * @title 机构资金账户表 详情查询 并锁定
     * @desc
     * @version
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getByIdForLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountMoney::where('id',$params['id'])->lockForUpdate()->first();
    }

    /**
     * 根据org_id查询账户信息
     * @param array $params
     * @return object
     */
    static public function getByOrgId(array $params)
    {
        \helper::argumentCheck(['org_id'],$params);

        return OilAccountMoney::where('org_id','=',$params['org_id'])->first();
    }

    /**
     * 根据org_id查询账户信息
     * @param array $params
     * @return object
     */
    static public function getByOrgIdList(array $orgIdArr)
    {
        return OilAccountMoney::whereIn('org_id',$orgIdArr)->get();
    }

    /**
     * @title 根据org_id查询账户信息 并锁定
     * @desc
     * @version
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getByOrgIdForLock(array $params)
    {
        \helper::argumentCheck(['org_id'],$params);

        return OilAccountMoney::where('org_id','=',$params['org_id'])->lockForUpdate()->first();
    }

    /**
     * 机构资金账户表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAccountMoney::create($params);
    }

    /**
     * 机构资金账户表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountMoney::where('id','=',$params['id'])->update($params);
    }

    /**
     * 机构资金账户表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountMoney::destroy($params['ids']);
    }

    /**
     * 获取机构的账户信息
     * @param array $params
     * @return object
     */
    static public function myAccountInfo(array $params)
    {
        return OilAccountMoney::where('org_id','=',$params['org_id'])->first();
    }

    static public function maxAmount_bak(array $params){
        \helper::argumentCheck(['org_id'],$params);

        $orgInfo = OilOrg::getById(['id'=>$params['org_id']]);
        $params['org_code']=$orgInfo->orgcode;

        //找我及我的所有上级的充值-返利充值总额-已开票-已冻结的额度，即每一层上级的可开票额度
        while(strlen($params['org_code'])>=6){
            //找指定orgcode及其下属机构的org_id
            $orgIdList= OilOrg::getInferior(
                [
                    'orgcodeAll'    =>  $params['org_code'],
                    'is_del'    =>  '0'
                ]
            );

            //求指定orgIdList充值之和
            $chargeAmount = OilAccountMoney::sumChargeTotalByOrgIdList(
                [
                    'org_id_list'=>$orgIdList
                ]
            );
            if(!$chargeAmount){
                return [['max_amount'=>0]];
            }

            //求指定orgIdList返利充值之和
            $fanliAmount = OilAccountMoneyCharge::sumFanliTotalByOrgIdList(
                [
                    'org_id_list'=>$orgIdList,
                    'status'    => 1,
                ]
            );

            //求指定orgIdList已开金额和冻结额和
            $receiptAmount = OilReceiptApply::sumReceiptAmountByOrgIdList(
                [
                    'org_id_list'   =>  $orgIdList,
                    'is_not_del'    =>  ['1','0']
                ]
            );
            Log::info('获取账户可开票金额', ['params'=>$params,'chargeAmount'=>$chargeAmount,'receiptAmount'=>$receiptAmount,
                                    'fanliAmount'=>$fanliAmount],'oilAccountMoney');
            //将充值总和 - （已开，冻结总和，返利充值总额） = 可开
            $maxAmount[]= sprintf("%.2f", ($chargeAmount-$receiptAmount-$fanliAmount));
            $params['org_code']=substr($params['org_code'],0,strlen($params['org_code'])-2);
        }

        //该机构的顶级机构下所有orgId的数组
        $orgIdList=OilOrg::getInferior(['orgcodeAll'=>$orgInfo->orgcode]);
        $viceTradesTotal = OilCardViceTrades::sumReceiptRemainByOrgIdList(
            [
                'org_id_list'   =>  $orgIdList,
                'is_fanli'    =>  1,
                'tradetimeLe' => $orgInfo->is_recepit_nowtime == 2 ? date('Y-m-d H:i:s') : date('Y-m-t', strtotime('-1 month')).' 23:59:59'
            ]
        );

        $maxAmount[] = $viceTradesTotal ? $viceTradesTotal : 0;
        $max_amount = min($maxAmount);
        Log::info('max--'.$max_amount,$maxAmount,'oilAccountMoney');

        return  $max_amount < 0 ? 0 : sprintf("%.2f", $max_amount);
    }

    /**
     *
     * @param array $params
     * @return mixed
     */
    static public function sumChargeTotalByOrgIdList(array $params)
    {
        return OilAccountMoney::Filter($params)->sum('charge_total');
    }

    /**
     * 对资金账户扣减现金余额或返利余额
     * decrementMoney
     * @param array $params
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function updateMoney(array $params)
    {
        \helper::argumentCheck(['id','money'],$params);
        $record = OilAccountMoney::find($params['id']);
        if(!$record){
            throw new \RuntimeException("所操作的账户不存在",2);
        }
        $updateArr = [];
        if(isset($params['money'])){
            $updateArr['money'] = $record->money + $params['money'];
        }

        if(isset($params['cash_fanli_remain'])){
            $updateArr['cash_fanli_remain'] = $record->cash_fanli_remain + $params['cash_fanli_remain'];
        }

        if(isset($params['assign_total'])){
            $updateArr['assign_total'] = $record->assign_total + $params['assign_total'];
        }

        return $record->update($updateArr);
    }

    /**
     * 账户充值时变动资金
     * updateMoneyForChargeById
     * @param array $params
     * @return mixed
     * <AUTHOR>
     * @since ${DATE}
     */
    static public function updateMoneyForChargeById(array $params)
    {
        \helper::argumentCheck(['id','money'],$params);
        $record = OilAccountMoney::find($params['id']);
        //print_r($record->charge_total);exit;
        if(!$record){
            throw new \RuntimeException("所操作的账户不存在",2);
        }
        $updateArr = [];
        if(isset($params['money'])){
            $updateArr['money'] = $record->money + $params['money'] + $params['fanli_charge'];
            $updateArr['charge_total'] = $record->charge_total + $params['money'];
        }

        if(isset($params['cash_fanli_remain'])){
            $updateArr['cash_fanli_remain'] = $record->cash_fanli_remain + $params['cash_fanli_remain'];
        }
        if(isset($params['fanli_charge'])){
            $updateArr['cash_fanli_remain'] = $record->cash_fanli_remain + $params['fanli_charge'];
            $updateArr['fanli_total'] = $record->fanli_total + $params['fanli_charge'];
        }

        $updateArr['last_charge_time'] = date("Y-m-d H:i:s");

        return $record->update($updateArr);
    }


    /**
     * 得到开通壹号卡的机构及现金账户
     */
    static public function getOrgCashAccountNo(array $condition)
    {
        //Capsule::connection()->enableQueryLog();
        $sqlObj = self::leftJoin('oil_org', 'oil_org.id', '=', 'oil_account_money.org_id')
            ->select("oil_org.id", "oil_org.orgcode",'oil_account_money.account_no','oil_account_money.id as CashId');
        if(isset($condition['OrgIn']) && !empty($condition['OrgIn'])){
            $sqlObj->whereIn("oil_org.orgcode",$condition['OrgIn']);
        }else {
            $sqlObj->where($condition);
        }
        $data = $sqlObj->whereNotNull("oil_account_money.subAccountID")
            ->where("oil_org.is_del", 0)
            ->groupBy("oil_org.id")
            ->orderBy("oil_org.createtime", "desc")
            ->get();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }

    /**
     * 生成机构账号
     */
    static public function createAccountNo()
    {
        $result = OilAccountMoney::where('account_no','like','108%')->max('account_no');

        if($result)
        {
            $account_no = $result + 1;
        }
        else
        {
            $account_no = '********';
        }
        return $account_no;
    }

    /**
     * 判断现金帐号
     */
    static public function checkAccount($transferInfo){
        $from_org_info = \Models\OilOrg::where('id','=',$transferInfo->org_id)->where('is_del','=',0)->first();
        $into_org_info = \Models\OilOrg::where('id','=',$transferInfo->org_id)->where('is_del','=',0)->first();
        if(!$from_org_info || !$into_org_info){
            return false;
        }
        $from_account_info = \Models\OilAccountMoney::where('org_id','=',$transferInfo->org_id)->first();
        $into_account_info = \Models\OilAccountMoney::where('org_id','=',$transferInfo->org_id)->first();
        if(!$from_account_info->id){
           $from_account =[
               'account_no'=> OilAccountMoney::createAccountNo(),
               'org_id' => $transferInfo->org_id,
               'createtime' =>helper::nowTime()
           ];
           $result =  OilAccountMoney::add($from_account);
            if(!$result){
               return false;
            }
        }
        if(!$into_account_info->id){
            $into_account =[
                'account_no'=> OilAccountMoney::createAccountNo(),
                'org_id' => $transferInfo->into_org_id,
                'createtime' =>helper::nowTime()
            ];
            $result =  OilAccountMoney::add($into_account);
            if(!$result){
                return false;
            }
        }
        return true;
    }

    /**
     * 累计转入
     */
    static public function totalTransferIn($params){
        $accountInfo = OilAccountMoney::where('org_id','=',$params['org_id'])->first();
        $result = OilAccountMoney::edit(['id'=>$accountInfo->id,'total_transfer_in'=>$accountInfo->total_transfer_in+$params['money']]);
        return $result;
    }

    /**
     * 累计转出
     */
    static public function totalTransferOut($params){
        $accountInfo = OilAccountMoney::where('org_id','=',$params['org_id'])->first();
        $result = OilAccountMoney::edit(['id'=>$accountInfo->id,'total_transfer_out'=>$accountInfo->total_transfer_out+$params['money']]);
        return $result;
    }

    /**
     * @title   账户平账校验
     * @desc
     * @version 1.0.0
     * <AUTHOR>
     * @package Models
     * @since
     * @params
     * @return array
     * @returns
     * {}
     * @returns
     */
    static public function accountChecking()
    {
        $sql = "SELECT
b.*
FROM
(
SELECT
oo.org_name,oo.orgcode,a.*, (a.in_money - a.out_money - a.money) as diff_money
FROM
(
SELECT
org_id,money,assign_total,total_transfer_out,total_transfer_in,charge_total,fanli_total,(assign_total+total_transfer_out) as out_money, (total_transfer_in+charge_total + fanli_total) as in_money
FROM
oil_account_money
where org_id not in ( SELECT id from oil_org where orgcode like '200I1A%')
) a
LEFT JOIN oil_org oo on oo.id = a.org_id
) b where abs(b.diff_money) > 0
ORDER BY b.diff_money DESC";

        return Capsule::connection()->select($sql);
    }

    static public function getAll()
    {
        $sql = 'SELECT
oo.orgcode,oo.org_name,oam.*
FROM
oil_account_money oam
LEFT JOIN oil_org oo on oo.id = oam.org_id
where oo.is_del = 0 AND oo.orgcode not like \'200I1A%\'';
        return Capsule::connection()->select($sql);
    }

    /**
     * @title 根据account_no获取现金账户信息
     * <AUTHOR>
     * @param array $params
     * @return mixed
     */
    static public function getByAccountNo(array $params)
    {
        return OilAccountMoney::where('account_no',$params['account_no'])->with([
            'Org'
        ])->first();
    }

    static public function getByAccountNos(array $accountNoArr)
    {
        return OilAccountMoney::whereIn('account_no',$accountNoArr)->get();
    }

    /**
     * @title 根据account_no 和 org_id同时查
     * @desc
     * @version
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param $account
     * @param $org_id
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getByAccountNoAndOrgId($account,$org_id){
        return OilAccountMoney::where('account_no',$account)->where('org_id',$org_id)->first();
    }

    static public function accountCheck()
    {
        $sqlObj = self::select(Capsule::connection()->raw("oil_org.orgcode,oil_org.org_name as orgname,oil_account_money.charge_total as total_charge,oil_account_money.assign_total as total_assign,oil_account_money.fanli_total as total_fanli,oil_account_money.total_transfer_in,oil_account_money.total_transfer_out,oil_account_money.money as balance,oil_account_money.org_id"))
            ->leftJoin('oil_org', 'oil_account_money.org_id','=','oil_org.id');
        $result = $sqlObj->get()->toArray();

        return $result;
    }

    static public function getAccountUseMoney(array $params)
    {
        if(isset($params['is_assign']) && $params['is_assign'] == 1){
           $wherestr = " and is_own != 1";
        }else{
            $wherestr = "";
        }
        $sql = "SELECT
	account.subAccountID,
	account.account_no,
	account.account_type,
	account.balance,
	account.credit_provider_id,
	account.is_own,
	account.status,
	(
		CASE
		WHEN account.balance > 0
		AND account.account_type = 10 THEN
			(
				account.balance - IFNULL(
					assign.total_assign_money,
					0
				) - IFNULL(
					transfer.total_transfer_money,
					0
				)
			) 
		WHEN account.balance > 0
		AND account.account_type = 20 THEN
			(
				account.balance - IFNULL(
					assign.total_assign_money,
					0
				) 
			)
		ELSE
			0
		END
	) use_balance
FROM
(
	(
	SELECT
	org_id,account_no, 10 as account_type, money as balance,0 as credit_provider_id,subAccountID,1 as is_own,10 status
	FROM
	oil_account_money
	where org_id = ".$params['org_id']."
	)
	UNION
	(
	SELECT
	org_id,account_no, 20 as account_type, (credit_total - used_total) as balance,credit_provider_id,subAccountID,is_own,status
	FROM
	oil_credit_account
	where org_id = ".$params['org_id']." and status = 10 {$wherestr}
	)
) account
LEFT JOIN
(
	SELECT
	account_no,SUM(money_total) as total_assign_money
	FROM oil_account_assign 
	where `status` <> 1 AND money_total > 0 and account_type in (10,20) and org_id = ".$params['org_id']."
	GROUP BY account_no
) assign on assign.account_no = account.account_no
LEFT JOIN
(
	SELECT
	org_id,SUM(money) as total_transfer_money
	FROM oil_account_money_transfer 
	where `status` = 0 AND money > 0 and org_id = ".$params['org_id']." and no_type != 'HB'
	GROUP BY org_id
) transfer on transfer.org_id = account.org_id";

        return Capsule::connection()->select($sql);
    }

    /**
     * @title 根据机构id获取支付平台id map
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $orgIds
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getSubAccountMap(array $orgIds)
    {
        return self::whereIn('org_id',$orgIds)->pluck('subAccountID','org_id');
    }

    /**
     * @title 根据机构id获取支付平台id map
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $orgIds
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getBackSubAccountMap(array $orgIds)
    {
        return self::whereIn('org_id',$orgIds)->pluck('backSubAccountID','org_id');
    }

    static public function getCashAccountNoList($params)
    {
        //验证
        \helper::argumentCheck(['orgcode'], $params);

        $sqlObj = OilAccountMoney::leftJoin('oil_org','oil_org.id','=','oil_account_money.org_id')
            ->where('oil_org.is_del',0)
            ->where('oil_org.orgcode','like',$params['orgcode'].'%')
            ->selectRaw('oil_org.orgcode,oil_org.org_name,oil_account_money.account_no');

        if(isset($params['keyword']) && $params['keyword']){
            $sqlObj->where(function($query)use($params){
                $query->where('oil_org.orgcode','like','%'.$params['keyword'].'%')
                    ->orWhere('oil_org.org_name','like','%'.$params['keyword'].'%');
            });
        }

        return $sqlObj->get();
    }

    /**
     * @title 获取机构返利剩余金额
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @param array $orgIds
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function getFanliRemain(array $params)
    {
        $fields = "sum( IFNULL(cash_fanli_remain,0) + IFNULL(fanli_discount_remain,0) ) as fanliremain";
        $data = self::Filter($params)->select(Capsule::connection()->raw($fields))->first();
        return $data->fanliremain ? $data->fanliremain : 0;
    }

    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return OilAccountMoney::Filter($params)->pluck($pluckField)->toArray();
    }

    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = OilAccountMoney::Filter($params)->pluck($pluckField)->toArray();

        return !empty($res[0]) ? $res[0] : '';
    }

    /**
     * 获取账户列表
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getListByFilter(array $params)
    {
        $res = OilAccountMoney::Filter($params)
            ->leftJoin('oil_org','oil_org.id','=','oil_account_money.org_id')
            ->selectRaw("oil_account_money.*,oil_org.orgcode,oil_org.org_name")
            ->get();

        return $res;
    }

    /**
     * 更新账户余额 及其累计字段金额
     * @param $id
     * @param $money
     * @param int $case 1:共享卡消费，2:共享卡消费撤销
     * @param float $fanliDeductMoney
     * @return int
     */
    public static function updateAccountBalanceById($id, $money, $case=1, $fanliDeductMoney=0.00)
    {
        $sql = "UPDATE `oil_account_money` SET ";

        switch ($case) {
            // case1：共享卡消费
            case 1:
                $sql .= "money=money - $money,shared_card_trade_total=shared_card_trade_total + $money";
                // 使用返利账户金额
                if ($fanliDeductMoney > 0) {
                    $sql .= ",cash_fanli_remain=cash_fanli_remain - $fanliDeductMoney,fanli_discount_remain=fanli_discount_remain + $fanliDeductMoney";
                }
                break;
            // case2：共享卡消费撤销
            case 2:
                $sql .= "money=money + $money,shared_card_trade_total=shared_card_trade_total - $money";
                break;
            default:
                $sql .= "money=money - $money";
        }

        $sql .= " WHERE id = $id";
        if (in_array($case, [1])) {
            $sql .= " AND money >= $money";
            //G7WALLET-6056
            /*if ($fanliDeductMoney > 0) {
                $sql .= " AND cash_fanli_remain >= $fanliDeductMoney";
            } else {
                $sql .= " AND money - $money >= cash_fanli_remain";
            }*/
        }

        $res = Capsule::connection()->getPdo()->exec($sql);

        Log::error("updateAccountBalanceById:up-sql:",["res"=>$res,"sql"=>$sql],"tradePay");
        return $res;
    }

    /**
     * 更新账户返利金额
     * @param $id
     * @param float $fanliDeductMoney
     * @param int $case 1返利扣减 2返利退回
     * @return int
     */
    public static function updateAccountRebateBalanceById($id, $fanliDeductMoney=0.00, $case=1)
    {
        $sql  = "UPDATE `oil_account_money` SET ";
        if ($case == 1) {
            $sql .= "money=money-$fanliDeductMoney,cash_fanli_remain=cash_fanli_remain - $fanliDeductMoney,";
            $sql .= "fanli_discount_remain=fanli_discount_remain + $fanliDeductMoney,shared_card_trade_total=shared_card_trade_total + $fanliDeductMoney";
        } elseif ($case == 2) {
            $sql .= "money=money+$fanliDeductMoney,cash_fanli_remain=cash_fanli_remain + $fanliDeductMoney,";
            $sql .= "shared_card_trade_total=shared_card_trade_total - $fanliDeductMoney";
        }elseif ($case == 3) {
            $sql .= "money=money+$fanliDeductMoney,cash_fanli_remain=cash_fanli_remain + $fanliDeductMoney,";
            $sql .= "fanli_discount_remain=fanli_discount_remain - $fanliDeductMoney,shared_card_trade_total=shared_card_trade_total - $fanliDeductMoney";
        }

        $sql .= " WHERE id = $id";
        if ($case == 1) {
            $sql .= " AND cash_fanli_remain >= $fanliDeductMoney AND money >= $fanliDeductMoney";
        }

        $res = Capsule::connection()->getPdo()->exec($sql);

        Log::error("updateAccountRebateBalanceById:up-sql:",["res"=>$res,"sql"=>$sql],"tradePay");

        return $res;
    }


    /**
     * Desc: 按机构账户分组查询
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 27/3/23 下午5:31
     * @param array $params
     * @return int
     */
    static public function getFanliRemainGroupByOrgcode($orgcode)
    {
        $sql = "SELECT
	sum( IFNULL( cash_fanli_remain, 0 ) + IFNULL( fanli_discount_remain, 0 ) ) AS fanliremain,oil_org.orgcode
FROM
	oil_account_money
	LEFT JOIN oil_org ON oil_org.id = oil_account_money.org_id 
WHERE
	oil_org.orgcode LIKE '".$orgcode."%'
	GROUP BY oil_org.orgcode";

        $data_list = Capsule::connection()->select($sql);

        $ret_data_list = [];
        foreach ($data_list as $data){
            $ret_data_list[$data->orgcode] = $data->fanliremain ? $data->fanliremain : 0;
        }
        return $ret_data_list;
    }
}