<?php
/**
 * 上游返利公式
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/09/21
 * Time: 11:39:27
 */
namespace Models\UpstreamRebate;

class Formula extends \Framework\Database\Model
{
    protected $table = 'oil_upstream_rebate_formula';

    protected $guarded = ['id'];
    protected $fillable = [
        'id',
        'name',
        'ca_obj',
        'ca_method',
        'rebate_type',
        'rebate_value',
        'rebate_unit',
        'has_rebate',
        'lowest_recharge_limit',
        'recharge_limit_type',
        'recharge_limit_val',
        'ext',
        'status',
        'creator',
        'last_operator',
        'createtime',
        'updatetime',
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By idIn
        if (!empty($params['idIn'])) {
            if (is_array($params['idIn'])) {
                $query->whereIn('id', $params['idIn']);
            } else {
                $query->where('id', '=', $params['idIn']);
            }
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        //Search By name_lk
        if (isset($params['name_lk']) && $params['name_lk'] != '') {
            $query->where('name', 'like', '%'.$params['name_lk'].'%');
        }

        //Search By ca_obj
        if (isset($params['ca_obj']) && $params['ca_obj'] != '') {
            $query->where('ca_obj', '=', $params['ca_obj']);
        }

        //Search By ca_method
        if (isset($params['ca_method']) && $params['ca_method'] != '') {
            $query->where('ca_method', '=', $params['ca_method']);
        }

        //Search By ca_method
        if (isset($params['ca_method_in']) && $params['ca_method_in'] != '') {
            $query->whereIn('ca_method', $params['ca_method_in']);
        }

        //Search By rebate_type
        if (isset($params['rebate_type']) && $params['rebate_type'] != '') {
            $query->where('rebate_type', '=', $params['rebate_type']);
        }

        //Search By step_unit
        if (isset($params['step_unit']) && $params['step_unit'] != '') {
            $query->where('step_unit', '=', $params['step_unit']);
        }

        //Search By rebate_unit
        if (isset($params['rebate_unit']) && $params['rebate_unit'] != '') {
            $query->where('rebate_unit', '=', $params['rebate_unit']);
        }

        //Search By rebate_value
        if (isset($params['rebate_value']) && $params['rebate_value'] != '') {
            $query->where('rebate_value', '=', $params['rebate_value']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return self::Filter($params)->pluck($pluckField)->toArray();
    }

    static public function getById($params)
    {
        \helper::argumentCheck(['id'],$params);

        return self::find($params['id']);
    }
}