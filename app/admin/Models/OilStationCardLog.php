<?php
/**
 * 供应商站点副卡表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/06/23
 * Time: 22:58:04
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilStationCardLog extends \Framework\Database\Model
{
    protected $table = 'oil_station_card_log';

    protected $guarded = ["id"];
    protected $fillable = ['code','vice_no','card_main_id','creator','createtime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By code
        if (isset($params['code']) && $params['code'] != '') {
            $query->where('code', '=', $params['code']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        //Search By card_main_id
        if (isset($params['card_main_id']) && $params['card_main_id'] != '') {
            $query->where('card_main_id', '=', $params['card_main_id']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        return $query;
    }

    /**
     * 供应商站点副卡表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilStationCardLog::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 供应商站点副卡表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilStationCardLog::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilStationCardLog::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 供应商站点副卡表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilStationCardLog::create($params);
    }

    /**
     * 供应商站点副卡表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilStationCardLog::find($params['id'])->update($params);
    }

    /**
     * 供应商站点副卡表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilStationCardLog::destroy($params['ids']);
    }




}