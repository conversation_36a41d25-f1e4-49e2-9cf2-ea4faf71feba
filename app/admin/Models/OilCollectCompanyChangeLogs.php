<?php
/**
 * 工商变更记录表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/10/14
 * Time: 11:38:27
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCollectCompanyChangeLogs extends \Framework\Database\Model
{
    protected $table = 'oil_collect_company_change_logs';

    protected $guarded = ["id"];
    protected $fillable = ['original_id','original_name','change_id','change_name','change_time','change_operator','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By original_id
        if (isset($params['original_id']) && $params['original_id'] != '') {
            $query->where('original_id', '=', $params['original_id']);
        }

        //Search By original_name
        if (isset($params['original_name']) && $params['original_name'] != '') {
            $query->where('original_name', '=', $params['original_name']);
        }

        //Search By change_id
        if (isset($params['change_id']) && $params['change_id'] != '') {
            $query->where('change_id', '=', $params['change_id']);
        }

        //Search By change_name
        if (isset($params['change_name']) && $params['change_name'] != '') {
            $query->where('change_name', '=', $params['change_name']);
        }

        //Search By change_time
        if (isset($params['change_time']) && $params['change_time'] != '') {
            $query->where('change_time', '=', $params['change_time']);
        }

        //Search By change_operator
        if (isset($params['change_operator']) && $params['change_operator'] != '') {
            $query->where('change_operator', '=', $params['change_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 工商变更记录表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCollectCompanyChangeLogs::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 工商变更记录表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCollectCompanyChangeLogs::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCollectCompanyChangeLogs::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 工商变更记录表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCollectCompanyChangeLogs::create($params);
    }

    /**
     * 工商变更记录表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCollectCompanyChangeLogs::find($params['id'])->update($params);
    }

    /**
     * 工商变更记录表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCollectCompanyChangeLogs::destroy($params['ids']);
    }


    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        return self::Filter($params)->pluck($pluckField)->toArray();
    }

    /**
     * 取字段值 返回第一个值
     * @param array $params
     * @param $pluckField
     * @return mixed
     */
    static public function getResField(array $params, $pluckField)
    {
        $res = self::Filter($params)->pluck($pluckField)->toArray();

        return !empty($res[0]) ? $res[0] : '';
    }

    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return self::Filter($params)->count();
    }

    /**
     * 根据条件取信息
     * @param array $params
     * @param mixed $field
     * @return OilSupplierAccount
     */
    static public function getInfoByFilter(array $params,$field = "*")
    {
        return self::Filter($params)->select($field)->first();
    }

    /**
     * 根据条件 查询 指定字段 数据列表
     * @param array $params
     * @param string $field
     * @return mixed
     */
    static public function getListByFilter(array $params,$field = ["*"])
    {
        return self::Filter($params)->select($field)->get()->toArray();
    }

}