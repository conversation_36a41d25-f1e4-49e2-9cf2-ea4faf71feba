<?php
/**
 * 油品副卡表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */

namespace Models;

use Framework\Cache;
use Framework\Database\Model;
use Framework\Helper;
use Fuel\Defines\AutoAssignTaskType;
use Fuel\Defines\CardFrom;
use Fuel\Defines\CardViceConf;
use Fuel\Defines\ConsumeType;
use Fuel\Defines\OilCom;
use Fuel\Defines\TradesType;
use Fuel\Defines\ViceCardStatus;
use Fuel\Request\GasClient;
use Fuel\Service\AccountCenter\AccountService;
use Fuel\Service\CardAccountStock;
use Fuel\Service\CardViceApp;
use Fuel\Service\CardViceToGos;
use G7Pay\Core\Account;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Service\AutoAssign;
use Framework\Log;

/**
 * Class OilCardVice
 * @package Models
 * @property $id
 * @property $card_level
 * @property $status
 * @property $driver_tel
 * @property $driver_name
 */
class OilCardVice extends Model
{
    protected $table = 'oil_card_vice';

    protected $guarded = ["id"];

    protected $fillable = [
        'cardID', 'third_id', 'oil_balance', 'vice_no', 'vice_password', 'oil_com', 'card_from', 'card_main_id',
        'refresh_status', 'vice_tmp_id', 'truck_no', 'driver_tel', 'driver_line', 'driver_name', 'card_owner', 'org_id', 'org_id_fanli', 'reserve_remain',
        'comp_remain', 'card_remain', 'point_reserve_total', 'point_remain', 'remain_get_time', 'remain_syn_time',
        'active_time', 'gsporg_id', 'user_id', 'bind_status', 'is_return_abnormal', 'return_result', 'remark',
        'remark_work', 'status', 'isreceive', 'creator_id', 'last_operator', 'assign_time', 'createtime', 'updatetime', 'pin',
        'is_tip_sms', 'oil_top', 'day_top', 'unit', 'gas_money_id', 'card_level', 'check_truckno', 'month_top', 'unique_id',
        'limit_type', 'paylimit', 'deduction_account_no', 'deduction_account_name', 'ischeck', 'able_transfer', 'is_use', 'trade_time',
        'property','id_card','truck_type',
    ];

    /**
     * 用卡方式 1 司机卡 2 车辆卡
     */
    const CARD_LEVEL_SJ = 1;
    const CARD_LEVEL_CL = 2;
    public static $card_level = [
        self::CARD_LEVEL_SJ => '司机卡',
        self::CARD_LEVEL_CL => '车辆卡',
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function CardMain()
    {
        return $this->belongsTo('Models\OilCardMain', 'card_main_id', 'id');
    }

    public function CardAccount()
    {
        return $this->belongsTo('Models\OilCardAccount', 'cardID', 'cardID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function CardViceAppTmp()
    {
        return $this->belongsTo('Models\OilCardViceTmp', 'vice_no', 'vice_no');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function Org()
    {
        return $this->belongsTo('Models\OilOrg', 'org_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function FanLiOrg()
    {
        return $this->belongsTo('Models\OilOrg', 'org_id_fanli', 'id');
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_card_vice.id', '=', $params['id']);
        }

        //Search By idList
        if (isset($params['idList']) && $params['idList']) {
            $query->whereIn('oil_card_vice.id', $params['idList']);
        }

        if (isset($params['idGe']) && $params['idGe']) {
            $query->where('oil_card_vice.id', '>=', $params['idGe']);
        }

        if (isset($params['idLt']) && $params['idLt']) {
            $query->where('oil_card_vice.id', '<', $params['idLt']);
        }

        //cardID
        if (isset($params['cardID']) && $params['cardID']) {
            $query->where('oil_card_vice.cardID', '=', $params['cardID']);
        }

        //cardID
        if (isset($params['cardIDNull']) && $params['cardIDNull']) {
            $query->whereNull('oil_card_vice.cardID');
        }

        //Search By unit
        if (isset($params['unit']) && $params['unit'] != '') {
            $query->where('oil_card_vice.unit', '=', $params['unit']);
        }

        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_card_vice.oil_com', '=', $params['oil_com']);
        }

        //Search By unique_id
        if (isset($params['unique_id']) && $params['unique_id'] != '') {
            $query->where('oil_card_vice.unique_id', '=', $params['unique_id']);
        }

        //Search By keywords
        if (isset($params['keywords']) && $params['keywords'] != '') {
            $query->where(function ($query) use ($params) {
                $query->where('oil_card_vice.vice_no', 'like', '%' . $params['keywords'] . '%')
                      ->orWhere('oil_card_vice.truck_no', 'like', '%' . $params['keywords'] . '%');
            });
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('oil_card_vice.vice_no', '=', $params['vice_no']);
        }

        //Search By vice_noOnly
        if (isset($params['vice_noOnly']) && $params['vice_noOnly'] != '') {
            $query->where('oil_card_vice.vice_no', '=', $params['vice_noOnly']);
        }

        //Search By vice_no
        if (isset($params['vice_no_lk']) && $params['vice_no_lk'] != '') {
            $query->where('oil_card_vice.vice_no', 'like', '%' . $params['vice_no_lk'] . '%');
        }

        //Search By vice_noIn
        if (isset($params['vice_noIn']) && $params['vice_noIn'] != '') {
            $query->whereIn('oil_card_vice.vice_no', $params['vice_noIn']);
        }
        //Search By card_main_id
        if (isset($params['card_main_id']) && $params['card_main_id'] != '') {
            $query->where('oil_card_vice.card_main_id', '=', $params['card_main_id']);
        }

        //Search By vice_tmp_id
        if (isset($params['vice_tmp_id']) && $params['vice_tmp_id'] != '') {
            $query->where('oil_card_vice.vice_tmp_id', '=', $params['vice_tmp_id']);
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->where('oil_card_vice.truck_no', '=', $params['truck_no']);
        }

        //Search By truck_no
        if (isset($params['truck_no_lk']) && $params['truck_no_lk'] != '') {
            $query->where('oil_card_vice.truck_no', 'like', '%' . $params['truck_no_lk'] . '%');
        }

        //Search By driver_tel
        if (isset($params['driver_tel']) && $params['driver_tel'] != '') {
            $query->where('oil_card_vice.driver_tel', '=', $params['driver_tel']);
        }

        if (! empty($params['driver_tel_neq_blank'])) {
            $query->whereRaw("oil_card_vice.driver_tel <> ''");
        }

        //Search By card_owner
        if (isset($params['card_owner']) && $params['card_owner'] != '') {
            $query->where('oil_card_vice.card_owner', '=', $params['card_owner']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('oil_card_vice.org_id', '=', $params['org_id']);
        }

        if (isset($params['org_id_in']) && $params['org_id_in'] != '') {
            $query->whereIn('oil_card_vice.org_id', $params['org_id_in']);
        }

        //Search By org_id_fanli
        if (isset($params['org_id_fanli']) && $params['org_id_fanli'] != '') {
            $query->where('oil_card_vice.org_id_fanli', '=', $params['org_id_fanli']);
        }

        //Search By reserve_remain
        if (isset($params['reserve_remain']) && $params['reserve_remain'] != '') {
            $query->where('oil_card_vice.reserve_remain', '=', $params['reserve_remain']);
        }

        //Search By comp_remain
        if (isset($params['comp_remain']) && $params['comp_remain'] != '') {
            $query->where('oil_card_vice.comp_remain', '=', $params['comp_remain']);
        }

        //Search By card_remain
        if (isset($params['card_remain']) && $params['card_remain'] != '') {
            $query->where('oil_card_vice.card_remain', '=', $params['card_remain']);
        }

        //Search By point_reserve_total
        if (isset($params['point_reserve_total']) && $params['point_reserve_total'] != '') {
            $query->where('oil_card_vice.point_reserve_total', '=', $params['point_reserve_total']);
        }

        //Search By point_remain
        if (isset($params['point_remain']) && $params['point_remain'] != '') {
            $query->where('oil_card_vice.point_remain', '=', $params['point_remain']);
        }

        //Search By remain_get_time
        if (isset($params['remain_get_time']) && $params['remain_get_time'] != '') {
            $query->where('oil_card_vice.remain_get_time', '=', $params['remain_get_time']);
        }

        //Search By remain_syn_time
        if (isset($params['remain_syn_time']) && $params['remain_syn_time'] != '') {
            $query->where('oil_card_vice.remain_syn_time', '=', $params['remain_syn_time']);
        }

        //Search By active_time
        if (isset($params['active_time']) && $params['active_time'] != '') {
            $query->where('oil_card_vice.active_time', '=', $params['active_time']);
        }

        //Search By active_timeGe
        if (isset($params['active_timeGe']) && $params['active_timeGe'] != '') {
            $query->where('oil_card_vice.active_time', '>=', $params['active_timeGe']);
        }

        //Search By active_timeLe
        if (isset($params['active_timeLe']) && $params['active_timeLe'] != '') {
            $query->where('oil_card_vice.active_time', '<=', $params['active_timeLe']);
        }

        //Search By assign_timeLe
        if (isset($params['assign_timeLe']) && $params['assign_timeLe'] != '') {
            $query->where('oil_card_vice.active_time', '<=', $params['assign_timeLe']);
        }

        //Search By trade_timeLe
        if (isset($params['trade_timeLe']) && $params['trade_timeLe'] != '') {
            $query->where('oil_card_vice.trade_time', '<=', $params['trade_timeLe']);
        }

        //Search By gsporg_id
        if (isset($params['gsporg_id']) && $params['gsporg_id'] != '') {
            $query->where('oil_card_vice.gsporg_id', '=', $params['gsporg_id']);
        }

        //Search By user_id
        if (isset($params['user_id']) && $params['user_id'] != '') {
            $query->where('oil_card_vice.user_id', '=', $params['user_id']);
        }

        //Search By bind_status
        if (isset($params['bind_status']) && $params['bind_status'] != '') {
            $query->where('oil_card_vice.bind_status', '=', $params['bind_status']);
        }

        //Search By is_return_abnormal
        if (isset($params['is_return_abnormal']) && $params['is_return_abnormal'] != '') {
            $query->where('oil_card_vice.is_return_abnormal', '=', $params['is_return_abnormal']);
        }

        //Search By return_result
        if (isset($params['return_result']) && $params['return_result'] != '') {
            $query->where('oil_card_vice.return_result', '=', $params['return_result']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('oil_card_vice.remark', '=', $params['remark']);
        }

        //Search By remark_work
        if (isset($params['remark_work']) && $params['remark_work'] != '') {
            $query->where('oil_card_vice.remark_work', '=', $params['remark_work']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_card_vice.status', '=', $params['status']);
        }

        //Search By status
        if (isset($params['statusIn']) && $params['statusIn'] && is_array($params['statusIn'])) {
            $query->whereIn('oil_card_vice.status', $params['statusIn']);
        }

        //Search By card_from
        if (isset($params['card_from']) && $params['card_from'] != '') {
            $query->where('oil_card_vice.card_from', '=', $params['card_from']);
        }

        //Search By cardFromIn
        if (isset($params['cardFromIn']) && $params['cardFromIn']) {
            $query->whereIn('oil_card_vice.card_from', $params['cardFromIn']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('oil_card_vice.creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('oil_card_vice.last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('oil_card_vice.createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_card_vice.updatetime', '=', $params['updatetime']);
        }

        //Search By updatetimeGt
        if (isset($params['updatetimeGt']) && $params['updatetimeGt'] != '') {
            $query->where('oil_card_vice.updatetime', '>', $params['updatetimeGt']);
        }

        //Search By updatetimeLe
        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('oil_card_vice.updatetime', '<=', $params['updatetimeLe']);
        }

        //Search By createtimeGt
        if (isset($params['createtimeGt']) && $params['createtimeGt'] != '') {
            $query->where('oil_card_vice.createtime', '>', $params['createtimeGt']);
        }

        //Search By createtimeGe
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_card_vice.createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtimeLe
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('oil_card_vice.createtime', '<=', $params['createtimeLe']);
        }

        //Search By assign_time
        if (isset($params['assign_time']) && $params['assign_time'] != '') {
            $query->where('oil_card_vice.assign_time', '=', $params['assign_time']);
        }

        //Search By assign_time
        if (isset($params['trade_time']) && $params['trade_time'] != '') {
            $query->where('oil_card_vice.trade_time', '=', $params['trade_time']);
        }

        //Search By pin
        if (isset($params['pin']) && $params['pin'] != '') {
            $query->where('oil_card_vice.pin', '=', $params['pin']);
        }

        //Search By vice_no
        if (isset($params['oil_comIn']) && $params['oil_comIn']) {
            $query->whereIn('oil_card_vice.oil_com', $params['oil_comIn']);
        }

        //Search By vice_no
        if (isset($params['org_id_lk']) && $params['org_id_lk'] != '') {
            $query->whereIn('oil_card_vice.org_id', $params['org_id_lk']);
        }

        //Search By main_no
        if (isset($params['main_no']) && $params['main_no'] != '') {
            $query->where('oil_card_main.main_no', $params['main_no']);
        }

        //Search By card_level
        if (isset($params['card_level']) && $params['card_level'] != '') {
            $query->where('oil_card_vice.card_level', $params['card_level']);
        }

        //Search By check_truckno
        if (isset($params['check_truckno']) && $params['check_truckno'] != '') {
            $query->where('oil_card_vice.check_truckno', $params['check_truckno']);
        }

        //Search By card_level
        if (isset($params['month_top']) && $params['month_top'] != '') {
            $query->where('oil_card_vice.month_top', $params['month_top']);
            $query->where('oil_card_vice.month_top', $params['month_top']);
        }

        if (isset($params['card_main_id_in']) && $params['card_main_id_in'] != '') {
            $query->whereIn('oil_card_vice.card_main_id', $params['card_main_id_in']);
        }

        if (isset($params['driver_telIn']) && $params['driver_telIn'] != '') {
            $query->whereIn('oil_card_vice.driver_tel', $params['driver_telIn']);
        }

        if (isset($params['driver_telEq']) && $params['driver_telEq'] != '') {
            $query->where('oil_card_vice.driver_tel', '=', $params['driver_telEq']);
        }

        //Search By statusNotIn
        if (isset($params['statusNotIn']) && $params['statusNotIn'] != '') {
            $statusNotIn = !is_array($params['statusNotIn']) ? explode(",", $params['statusNotIn']) : $params['statusNotIn'];
            $query->whereNotIn('oil_card_vice.status', $statusNotIn);
        }

        //Search By statusIn
        if (isset($params['statusIn']) && $params['statusIn'] != '') {
            $statusIn = !is_array($params['statusIn']) ? explode(",", $params['statusIn']) : $params['statusIn'];
            $query->whereIn('oil_card_vice.status', $statusIn);
        }

        if (isset($params['property']) && $params['property'] != '') {
            $query->where('oil_card_vice.property', '=',$params['property']);
        }

        if (isset($params['propertyIn']) && $params['propertyIn'] != '') {
            $query->whereIn('oil_card_vice.property', $params['propertyIn']);
        }

        //Search By cardFromNotIn
        if (isset($params['cardFromNotIn']) && count($params['cardFromNotIn']) > 0) {
            $query->whereNotIn('oil_card_vice.card_from', $params['cardFromNotIn']);
        }

        return $query;
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilterForSearch($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_card_vice.id', '=', $params['id']);
        }

        //Search By card_from
        if (isset($params['card_from']) && $params['card_from'] != '') {
            $query->where('oil_card_vice.card_from', '=', $params['card_from']);
        }

        //Search By unit
        if (isset($params['unit']) && $params['unit'] != '') {
            $query->where('oil_card_vice.unit', '=', $params['unit']);
        }

        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_card_vice.oil_com', '=', $params['oil_com']);
        }

        if (isset($params['oil_comIn']) && $params['oil_comIn'] != '') {
            $query->whereIn('oil_card_vice.oil_com', $params['oil_comIn']);
        }

        //Search By oil_com
        if (isset($params['is_one_card']) && $params['is_one_card'] == 1) {
            $query->whereIn('oil_card_vice.oil_com', [20, 21]);
        }

        //Search By keywords
        if (isset($params['keywords']) && $params['keywords'] != '') {
            $query->where(function ($query) use ($params) {
                $query->where('oil_card_vice.vice_no', 'like', '%' . $params['keywords'] . '%')
                      ->orWhere('oil_card_vice.truck_no', 'like', '%' . $params['keywords'] . '%');
            });
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no']) {
            $viceNoArr = explode('|', $params['vice_no']);
            if (count($viceNoArr) == 1) {
                //$query->where('oil_card_vice.vice_no', 'like', '%' . $params['vice_no'] . '%');
                $query->where('oil_card_vice.vice_no', '=', $params['vice_no']);
            } else {
                $query->whereIn('oil_card_vice.vice_no', $viceNoArr);
            }
        }

        //Search By vice_no_foss
        if (isset($params['vice_no_foss']) && $params['vice_no_foss']) {
            $viceNoArr = explode('|', $params['vice_no_foss']);
            if (count($viceNoArr) == 1) {
                $query->where('oil_card_vice.vice_no', 'like', '%' . $params['vice_no_foss'] . '%');
            } else {
                $query->whereIn('oil_card_vice.vice_no', $viceNoArr);
            }
        }

        //Search By vice_noIn
        if (isset($params['vice_noIn']) && $params['vice_noIn'] != '') {
            $query->whereIn('oil_card_vice.vice_no', $params['vice_noIn']);
        }
        //Search By card_main_id
        if (isset($params['card_main_id']) && $params['card_main_id'] != '') {
            $query->where('oil_card_vice.card_main_id', '=', $params['card_main_id']);
        }

        //Search By vice_tmp_id
        if (isset($params['vice_tmp_id']) && $params['vice_tmp_id'] != '') {
            $query->where('oil_card_vice.vice_tmp_id', '=', $params['vice_tmp_id']);
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->where('oil_card_vice.truck_no', 'like', '%' . $params['truck_no'] . '%');
        }

        //Search By truck_no
        if (isset($params['truck_no_lk']) && $params['truck_no_lk'] != '') {
            $query->where('oil_card_vice.truck_no', 'like', '%' . $params['truck_no_lk'] . '%');
        }

        //Search By driver_tel
        if (isset($params['driver_tel']) && $params['driver_tel'] != '') {
            $query->where('oil_card_vice.driver_tel', 'like', '%' . $params['driver_tel'] . '%');
        }

        //Search By card_owner
        if (isset($params['card_owner']) && $params['card_owner'] != '') {
            $query->where('oil_card_vice.card_owner', '=', $params['card_owner']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('oil_card_vice.org_id', '=', $params['org_id']);
        }

        //Search By org_id_fanli
        if (isset($params['org_id_fanli']) && $params['org_id_fanli'] != '') {
            $query->where('oil_card_vice.org_id_fanli', '=', $params['org_id_fanli']);
        }

        //Search By reserve_remain
        if (isset($params['reserve_remain']) && $params['reserve_remain'] != '') {
            $query->where('oil_card_vice.reserve_remain', '=', $params['reserve_remain']);
        }

        //Search By comp_remain
        if (isset($params['comp_remain']) && $params['comp_remain'] != '') {
            $query->where('oil_card_vice.comp_remain', '=', $params['comp_remain']);
        }

        //Search By card_remain
        if (isset($params['card_remain']) && $params['card_remain'] != '') {
            $query->where('oil_card_vice.card_remain', '=', $params['card_remain']);
        }

        //Search By is_test
        if (isset($params['is_test']) && $params['is_test'] != '') {
            $query->where('oo.is_test', '=', $params['is_test']);
        }

        //Search By point_reserve_total
        if (isset($params['point_reserve_total']) && $params['point_reserve_total'] != '') {
            $query->where('oil_card_vice.point_reserve_total', '=', $params['point_reserve_total']);
        }

        //Search By point_remain
        if (isset($params['point_remain']) && $params['point_remain'] != '') {
            $query->where('oil_card_vice.point_remain', '=', $params['point_remain']);
        }

        //Search By remain_get_time
        if (isset($params['remain_get_time']) && $params['remain_get_time'] != '') {
            $query->where('oil_card_vice.remain_get_time', '=', $params['remain_get_time']);
        }

        //Search By remain_syn_time
        if (isset($params['remain_syn_time']) && $params['remain_syn_time'] != '') {
            $query->where('oil_card_vice.remain_syn_time', '=', $params['remain_syn_time']);
        }

        //Search By active_time
        if (isset($params['active_time']) && $params['active_time'] != '') {
            $query->where('oil_card_vice.active_time', '=', $params['active_time']);
        }

        //Search By active_timeGe
        if (isset($params['active_timeGe']) && $params['active_timeGe'] != '') {
            $query->where('oil_card_vice.active_time', '>=', $params['active_timeGe']);
        }

        //Search By active_timeLe
        if (isset($params['active_timeLe']) && $params['active_timeLe'] != '') {
            $query->where('oil_card_vice.active_time', '<=', $params['active_timeLe'] . ' 23:59:59');
        }

        //Search By assign_timeLe
        if (isset($params['assign_timeLe']) && $params['assign_timeLe'] != '') {
            $query->where(function ($query) use ($params) {
                $query->where('oil_card_vice.assign_time', '<=', $params['assign_timeLe'])
                      ->orWhereNull('oil_card_vice.assign_time');
            });
        }

        //Search By trade_timeLe
        if (isset($params['trade_timeLe']) && $params['trade_timeLe'] != '') {
            $query->where(function ($query) use ($params) {
                $query->where('oil_card_vice.trade_time', '<=', $params['trade_timeLe'])
                      ->orWhereNull('oil_card_vice.trade_time');
            });
        }

        //Search By gsporg_id
        if (isset($params['gsporg_id']) && $params['gsporg_id'] != '') {
            $query->where('oil_card_vice.gsporg_id', '=', $params['gsporg_id']);
        }

        //Search By user_id
        if (isset($params['user_id']) && $params['user_id'] != '') {
            $query->where('oil_card_vice.user_id', '=', $params['user_id']);
        }

        //Search By bind_status
        if (isset($params['bind_status']) && $params['bind_status'] != '') {
            $query->where('oil_card_vice.bind_status', '=', $params['bind_status']);
        }

        //Search By is_return_abnormal
        if (isset($params['is_return_abnormal']) && $params['is_return_abnormal'] != '') {
            $query->where('oil_card_vice.is_return_abnormal', '=', $params['is_return_abnormal']);
        }

        //Search By return_result
        if (isset($params['return_result']) && $params['return_result'] != '') {
            $query->where('oil_card_vice.return_result', '=', $params['return_result']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('oil_card_vice.remark', '=', $params['remark']);
        }

        //Search By remark_work
        if (isset($params['remark_work']) && $params['remark_work'] != '') {
            $query->where('oil_card_vice.remark_work', '=', $params['remark_work']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_card_vice.status', '=', $params['status']);
        }

        //Search By statusNotIn
        if (isset($params['statusNotIn']) && $params['statusNotIn'] != '') {
            $statusNotIn = !is_array($params['statusNotIn']) ? explode(",", $params['statusNotIn']) : $params['statusNotIn'];
            $query->whereNotIn('oil_card_vice.status', $statusNotIn);
        }

        //Search By isreceive
        if (isset($params['isreceive']) && $params['isreceive'] != '') {
            if ($params['isreceive'] > 0) {
                if ($params['isreceive'] == 1) {
                    $query->where('oil_card_vice.isreceive', '=', $params['isreceive']);
                } else {
                    $query->whereIn('oil_card_vice.isreceive', [0, 2]);
                }
            } else {
                $query->whereNotIn('oil_card_vice.oil_com', [20, 21]);
            }
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('oil_card_vice.creator_id', '=', $params['creator_id']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('oil_card_vice.last_operator', '=', $params['last_operator']);
        }

        //Search By createtimeGe
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_card_vice.createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtimeLe
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('oil_card_vice.createtime', '<=', $params['createtimeLe']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('oil_card_vice.createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_card_vice.updatetime', '=', $params['updatetime']);
        }

        //Search By updatetimeGe
        if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
            $query->where('oil_card_vice.updatetime', '>=', $params['updatetimeGe']);
        }

        //Search By updatetimeGt
        if (isset($params['updatetimeGt']) && $params['updatetimeGt'] != '') {
            $query->where('oil_card_vice.updatetime', '>', $params['updatetimeGt']);
        }

        //Search By updatetimeLe
        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('oil_card_vice.updatetime', '<=', $params['updatetimeLe']);
        }

        //Search By assign_time
        if (isset($params['assign_time']) && $params['assign_time'] != '') {
            $query->where('oil_card_vice.assign_time', '=', $params['assign_time']);
        }

        //Search By assign_time
        if (isset($params['trade_time']) && $params['trade_time'] != '') {
            $query->where('oil_card_vice.trade_time', '=', $params['trade_time']);
        }

        //Search By pin
        if (isset($params['pin']) && $params['pin'] != '') {
            $query->where('oil_card_vice.pin', '=', $params['pin']);
        }

        //Search By truck_no_not_null
        if (isset($params['truck_no_not_null']) && $params['truck_no_not_null'] != '') {
            $query->where('oil_card_vice.truck_no', '!=', "");
        }

        //Search By vice_no
        if (isset($params['oil_comIn']) && $params['oil_comIn'] != '') {
            $query->whereIn('oil_card_vice.oil_com', $params['oil_comIn']);
        }

        //Search By vice_no
        if (isset($params['org_id_lk']) && $params['org_id_lk'] != '') {
            $query->whereIn('oil_card_vice.org_id', $params['org_id_lk']);
        }

        //Search By fanli_region
        if (isset($params['fanli_region']) && $params['fanli_region'] != '') {
            $query->where('oil_card_main.fanli_region', $params['fanli_region']);
        }

        //Search By main_no
        if (isset($params['main_no']) && $params['main_no'] != '') {
            $query->whereIn('oil_card_main.main_no', explode('|', $params['main_no']));
        }

        //Search By gas_money_id
        if (isset($params['gas_money_id']) && $params['gas_money_id'] != '') {
            $query->where('oo3.id', $params['gas_money_id']);
        }

        //Search By gso_org_code
        if (isset($params['gso_org_code']) && $params['gso_org_code']) {
            $query->where('gsp_sys_orgs.org_code', 'like', $params['gso_org_code'] . '%');
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '' && isset($params['org_flag'])) {
            $query->where('oo.orgcode', 'like', $params['orgcode'] . '%');
        }
        if (isset($params['orgcode']) && $params['orgcode'] != '' && !isset($params['org_flag'])) {
            $query->where('oo.orgcode', '=', $params['orgcode']);
        }

        //Search By oil_operator_name
        if (isset($params['oil_operator_name']) && $params['oil_operator_name'] != '') {
            $query->where('op2.name', '=', $params['oil_operator_name']);
        }

        //Search By oil_operator_name
        if (isset($params['operator_company_name']) && $params['operator_company_name'] != '') {
            $query->where('op2.company_name', '=', $params['operator_company_name']);
        }
        //Search By oil_operator_name
        if (isset($params['main_operator_name']) && $params['main_operator_name'] != '') {
            $query->where('op1.name', '=', $params['main_operator_name']);
        }

        if (isset($params['supplyer_id']) && $params['supplyer_id'] != '') {
            $query->where('oil_card_main.supplyer_id', '=', $params['supplyer_id']);
        }

        if (isset($params['card_level']) && $params['card_level'] != '') {
            $query->where('oil_card_vice.card_level', '=', $params['card_level']);
        }

        if (isset($params['property']) && $params['property'] != '') {
            $query->where('oil_card_vice.property', '=', $params['property']);
        }

        return $query;
    }

    static private function getSqlObj(array $params, $fields)
    {
        $sqlObj            = Capsule::connection('online_only_read')->table('oil_card_vice')
                                    ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
                                    ->leftJoin('oil_org as oo', function ($query) {
                                        $query->on('oo.id', '=', 'oil_card_vice.org_id')->where('oo.is_del', '=', 0);
                                    })
                                    ->leftJoin('oil_org as oo2', function ($query) {
                                        $query->on('oo2.id', '=', 'oil_card_vice.org_id_fanli')->where('oo2.is_del', '=', 0);
                                    })
                                    ->leftJoin('oil_org as oo3', function ($query) {
                                        $query->on('oo3.id', '=', 'oil_card_vice.gas_money_id')->where('oo3.is_del', '=', 0);
                                    })
                                    ->leftJoin('gsp_sys_orgs', 'gsp_sys_orgs.id', '=', 'oil_card_vice.gsporg_id')
                                    ->leftJoin('gsp_sys_users', 'gsp_sys_users.id', '=', 'oil_card_vice.user_id')
                                    ->leftJoin('oil_provinces', 'oil_provinces.id', '=', 'oil_card_main.fanli_region')
                                    ->leftJoin('oil_operators as op1', 'op1.id', '=', 'oil_card_main.operators_id')
                                    ->leftJoin('oil_operators as op2', 'op2.id', '=', 'oo.operators_id')
                                    ->select(Capsule::raw($fields));
        $oilCardViceTrades = new OilCardVice();
        $sqlObj            = $oilCardViceTrades->scopeFilterForSearch($sqlObj, $params);

        return $sqlObj;
    }

    public static function search(array $params)
    {
        $data            = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        if (!isset($params['oil_comIn']) || !$params['oil_comIn']) {
            $params['oil_comIn'] = OilCom::getNotGssType();
        }

        if (isset($params['_countdata']) && $params["_countdata"] == 1) {
            $fields = "count(oil_card_vice.id) as total_count,sum(oil_card_vice.reserve_remain) as total_reserve_remain,
            sum(oil_card_vice.card_remain) as total_card_remain";
        } else {
            $fields = " oil_card_vice.card_from,oil_card_vice.paylimit,oil_card_vice.month_top,
                oil_card_vice.deduction_account_no,oil_card_vice.deduction_account_name,oil_card_vice.card_level,
                oil_card_vice.reserve_remain+oil_card_vice.comp_remain as reserve_remain,oil_card_vice.id,
                oil_card_vice.vice_no,oil_card_vice.card_main_id,oil_card_vice.vice_tmp_id,
                oil_card_vice.truck_no,oil_card_vice.driver_tel,oil_card_vice.card_owner,oil_card_vice.driver_name,
                oil_card_vice.org_id,oil_card_vice.org_id_fanli,oil_card_vice.comp_remain,oil_card_vice.card_remain,
                oil_card_vice.point_reserve_total,oil_card_vice.point_remain,oil_card_vice.remain_get_time,
                oil_card_vice.remain_syn_time,oil_card_vice.active_time,oil_card_vice.gsporg_id,
                oil_card_vice.user_id,oil_card_vice.bind_status,oil_card_vice.is_return_abnormal,
                oil_card_vice.return_result,oil_card_vice.remark,oil_card_vice.remark_work,
                oil_card_vice.status,oil_card_vice.isreceive,oil_card_vice.creator_id,oil_card_vice.last_operator,
                oil_card_vice.createtime,oil_card_vice.updatetime,oil_card_vice.pin,oil_card_vice.gas_money_id,
                oo3.org_name gas_org_name,oil_card_vice.day_top,oil_card_vice.oil_top,oil_card_vice.limit_type,
                oil_provinces.province, oil_card_main.main_no,oil_card_main.supplyer_id, oil_card_vice.oil_com,
                oil_card_vice.assign_time,oil_card_vice.trade_time, oil_card_main.fanli_region, oo.org_name, 
                oo2.org_name fanli_org_name, gsp_sys_orgs.org_name dept_name, gsp_sys_users.true_name ,
                oo.orgcode,oo.sub_org_name,oo2.orgcode fanli_orgcode,oo.is_test,op1.name main_operator_name,
                op2.name oil_operator_name,op2.code operator_code,op2.company_name operator_company_name,
                oil_card_vice.vice_password,oil_card_vice.ischeck,oil_card_vice.able_transfer,oil_card_vice.property,
                oo.operators_id,oil_card_vice.truck_type";
        }

//        Capsule::connection()->enableQueryLog();
        $sqlObj = OilCardVice::getSqlObj($params, $fields);
        $sqlObj->where('oil_card_vice.card_from', '!=', 41); //不显示数据托管卡
        $sqlObj = $sqlObj->orderBy('oil_card_vice.active_time', 'desc')->orderBy('oil_card_vice.vice_no', 'asc');

        if (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['_countdata']) && $params["_countdata"] == 1) {
            return $sqlObj->get();
        } elseif (isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        } elseif (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

//        Log::error('sql--' . json_encode(Capsule::connection()->getQueryLog()), [], 'jobOk');
        $supplyerMap = OilCardSupplyer::getSupplyerMap([]);
        foreach ($data as $k => &$v) {
            $v->oil_com_text        = \Fuel\Defines\OilCom::getOilCom($v->oil_com);
            $v->orgroot             = substr($v->orgcode, 0, 6);
            $cardFrom               = CardFrom::getById($v->card_from);
            $v->_card_from          = $cardFrom ? $cardFrom['name'] : '';
            $v->point_reserve_total = $v->oil_com == 2 ? 0 : $v->point_reserve_total;
            $v->property_val = CardViceConf::getCardPropertyById($v->property);
            if (in_array($v->oil_com, [20, 21])) {
                $v->receive_txt = $v->isreceive == 1 ? '已领取' : '未领取';
            } else {
                $v->receive_txt = '';
            }
            if ($v->card_level == 1) {
                $v->card_level_text = '司机卡';
            } elseif ($v->card_level == 2) {
                $v->card_level_text = '车辆卡';
            }
            if ($v->truck_type == 1) {
                $v->truck_type_text = '自有车';
            } elseif ($v->truck_type == 2) {
                $v->truck_type_text = '外协车';
            } else {
                if ($v->truck_type == 0) {
                    $v->truck_type = null;
                }
                $v->truck_type_text = '';
            }
            $v->supplyer_name = $supplyerMap[$v->supplyer_id] ? $supplyerMap[$v->supplyer_id] : '常规';

            $v->card_remain_desc = '';
            if ($v->oil_com == 21) {
                $orgAccountRemain = OilAccountMoney::getResField(['org_id' => $v->org_id], 'money');
                if ($orgAccountRemain > 10000) {
                    $v->card_remain_desc = '大于一万元';
                } else {
                    $v->card_remain_desc = $orgAccountRemain . '元';
                }
            }
            $cardViceIdArr[] = $v->id;
        }

        //获取卡账户信息,显示授信账户信息
        $cardAccountList = CardViceToGos::getCardAccountList($cardViceIdArr);
        foreach ($data as $k => &$v) {
            $creditStr = [];
            //取卡的授信余额展示给前端
            $cardAccount = isset($cardAccountList[$v->id]) ? $cardAccountList[$v->id] : [];
            if ($cardAccount) {
                foreach ($cardAccount as $val) {
                    $creditStr[] = $val['amount'];
                }
                $v->credit_card_remain = implode(',', $creditStr);
            } else {
                $v->credit_card_remain = '--';
            }

        }

        //$data = \Models\OilOrg::handleData($data);

        return $data;
    }

    /**
     * 油品副卡表 列表查询
     *
     * @param array $params
     * @return array
     */
    public static function getList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        Capsule::connection()->enableQueryLog();
        $sqlObj = OilCardVice::Filter($params)->with([
            'CardMain' => function ($query) {
                $query->select('id', 'main_no', 'active_region');
            },
            'Org'      => function ($query) {
                $query->select('id', 'org_name', 'orgcode');
            }
        ])->orderBy('active_time', 'desc')->orderBy('vice_no', 'asc');

        if (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['take']) && isset($params['skip'])) {
            $data = $sqlObj->offset(intval($params['skip']))->limit(intval($params['take']))->get();
        } elseif (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        $sql = Capsule::connection()->getQueryLog();
        //Log::error('sql' . var_export($sql, TRUE), [], 'oilCardVice');

        if ($data) {
            foreach ($data as $k => &$v) {
                $v->oil_com_name = OilCom::getOilCom($v->oil_com);
                $cardFrom        = CardFrom::getById($v->card_from);
                $v->_card_from   = $cardFrom ? $cardFrom['sub_name'] : '';
                $v->main_no      = $v->CardMain->main_no;
                $v->orgcode      = $v->Org->orgcode;
                $v->org_name     = $v->Org->org_name;
                $v->property_val = CardViceConf::getCardPropertyById($v->property);
            }
        }

        return $data;
    }

    public static function getCardList(array $params, $field = "*")
    {
//        Capsule::connection()->enableQueryLog();
        $sqlObj = OilCardVice::Filter($params)->with([
            'Org',
        ])->selectRaw($field);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['page_size'], ['*'], 'page', $params['page_no']);
        }

        return $data;
    }

    public static function getCardListReadOnly(array $params, $field = "*")
    {
        $connection = "online_only_read";
        if (API_ENV == "dev") {
            $connection = "";
        }
        $sqlObj  = Capsule::connection($connection)->table('oil_card_vice');
        $viceObj = new OilCardVice();
        $sqlObj  = $viceObj->scopeFilter($sqlObj, $params)->selectRaw($field);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['page_size'], ['*'], 'page', $params['page_no']);
        }

        return $data;
    }

    /**
     * 油品副卡表 详情查询
     *
     * @param array $params
     * @return object
     */
    public static function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardVice::find($params['id']);
    }

    /**
     * @title
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
    public static function getByIdForLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return self::lockForUpdate()
                   ->select('oil_card_vice.*', 'oil_org.org_name', 'oil_card_main.oil_com', 'oil_card_main.main_no')
                   ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
                   ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                   ->where('oil_card_vice.id', $params['id'])
                   ->first();
    }

    /**
     * 油品副卡表 详情查询
     *
     * @param array $params
     * @return object
     */
    public static function getByIdHaveOrgInfo(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardVice::with([
            'Org' => function ($query) {
                $query->select('id', 'org_name', 'orgcode');
            }
        ])->find($params['id']);
    }

    /**
     * 油品副卡表 详情查询
     *
     * @param array $params
     * @return OilCardVice
     */
    public static function getByViceNo(array $params)
    {
        \helper::argumentCheck(['vice_no'], $params);

        return OilCardVice::Filter($params)->with([
            'CardMain' => function ($query) {
                $query->select('id', 'main_no', 'active_region', 'fanli_region', 'operators_id', 'card_from', 'oil_com', 'is_jifen');
            },
            'Org'      => function ($query) {
                $query->select('id', 'org_name', 'operators_id', 'exclusive_custom', 'orgcode', 'receipt_mode');
            },
            'FanLiOrg' => function ($query) {
                $query->select('id', 'org_name', 'orgcode');
            },
        ])->first();
    }

    public static function getByViceNoForLock(array $params)
    {
        \helper::argumentCheck(['vice_no'], $params);

        return self::lockForUpdate()->where('vice_no', $params['vice_no'])->first();
    }

    public static function getByUniqueIdForLock(array $params)
    {
        \helper::argumentCheck(['unique_id'], $params);

        return self::lockForUpdate()->where('unique_id', $params['unique_id'])->where("oil_com", $params['oil_com'])->count("id");
    }

    public static function getExistsByPhone(array $params)
    {
        \helper::argumentCheck(['driver_tel'], $params);

        return self::lockForUpdate()->where('driver_tel', $params['driver_tel'])->where("org_id", $params['org_id'])->where("oil_com", $params['oil_com'])->count("id");
    }

    /**
     * 通过副卡号查询返利机构id和返利机构名称
     *
     * @param array $params
     * @return object
     */
    public static function getOrgByViceNo(array $params)
    {
        \helper::argumentCheck(['vice_no'], $params);

        $sqlObj = Capsule::connection()->table('oil_card_vice');

        $sqlObj->select('oil_org.id', 'oil_org.org_name')->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id_fanli');

        return $sqlObj->where('oil_card_vice.vice_no', '=', $params['vice_no'])->first();
    }

    /**
     * 通过副卡ID查询所属机构id和所属机构名称
     *
     * @param array $params
     * @return object
     */
    public static function getOrgByViceId(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        $sqlObj = Capsule::connection()->table('oil_card_vice');

        $sqlObj->select('oil_org.id', 'oil_org.org_name')->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id');

        return $sqlObj->where('oil_card_vice.id', '=', $params['id'])->first();
    }

    /**
     * 按卡号获取副卡信息，连带对应的主卡与机构数据
     *
     * @param array $params
     * @return mixed
     */
    public static function getViceDetail(array $params)
    {
//        Capsule::connection()->enableQueryLog();
        $data = OilCardVice::Filter($params)
                           ->with([
                               'CardMain' => function ($query) {
                                   $query->select('id', 'main_no', 'oil_com', 'fanli_region', 'active_region', 'operators_id', 'card_from')->with([
                                       'FanLiRegion'    => function ($query) {
                                           $query->select('id', 'code', 'province');
                                       },
                                       'ActiveProvince' => function ($query) {
                                           $query->select('id', 'code', 'province');
                                       },
                                   ]);
                               },
                               'Org'      => function ($query) {
                                   $query->select('id', 'orgcode', 'org_name', 'exclusive_custom');
                               },

                           ])->first();
//        $sql = Capsule::connection()->getQueryLog();
//        Framework\Log::dataLog(json_encode($sql),'aaa1');
        return $data;
    }

    /**
     * 获取副卡信息（分配单导入专用）
     *
     * @param array $params
     * @return array
     * <AUTHOR>
     */
    public static function getViceInfo(array $params)
    {
        \helper::argumentCheck(['vice_no', 'orgcode'], $params);

        $data = Capsule::connection()->select("SELECT ocv.id, oo.org_name, ocv.truck_no, ocm.main_no, ocm.oil_com, op.province, oaj.jifen,ocv.status as vice_status
                    FROM oil_card_vice ocv
                    LEFT JOIN oil_org oo ON ocv.org_id = oo.id
                    LEFT JOIN oil_card_main ocm ON ocv.card_main_id = ocm.id
                    LEFT JOIN oil_provinces op ON ocm.fanli_region = op.id
                    LEFT JOIN oil_account_jifen oaj ON oaj.main_id = ocm.id AND oaj.org_id = ocv.org_id
                    WHERE ocv.vice_no = '$params[vice_no]' AND oo.orgcode LIKE '$params[orgcode]%'");

        return $data[0];
    }

    /**
     * 撬装分配单获取卡的详细信息
     *
     * @param array $params
     * @return array
     * <AUTHOR>
     */
    public static function getCardInfo(array $params)
    {
        \helper::argumentCheck(['vice_no', 'orgcode'], $params);

        $data = Capsule::connection()->select("SELECT ocv.id, oo.org_name,oo.id as org_id ,ocv.truck_no
                    FROM oil_card_vice ocv
                    LEFT JOIN oil_org oo ON ocv.org_id = oo.id
                    WHERE ocv.vice_no = '$params[vice_no]' AND oo.orgcode LIKE '$params[orgcode]%'");

        return $data[0];
    }

    /**
     * 按卡号获取副卡信息，连带对应的主卡与机构数据
     *
     * @param array $params
     * @return mixed
     */
    public static function getCardViceDetail(array $params)
    {
//        Capsule::connection()->enableQueryLog();
        $data = OilCardVice::Filter($params)
                           ->with([
                               'CardMain' => function ($query) {
                                   $query->select('id', 'main_no', 'oil_com', 'fanli_region', 'active_region','origin_supplier_id')->with([
                                       'FanLiRegion' => function ($query) {
                                           $query->select('id', 'code', 'province');
                                       },
                                   ]);
                               },
                               'Org'      => function ($query) {
                                   $query->select('id', 'org_name');
                               },

                           ])->get();
//        $sql = Capsule::connection()->getQueryLog();
//        Framework\Log::dataLog(json_encode($sql),'aaa1');
        return $data;
    }

    /**
     * 油品副卡表 新增
     *
     * @param array $params
     * @return mixed
     */
    public static function add(array $params)
    {
        return OilCardVice::create($params);
    }

    /**
     * 油品副卡表 编辑
     *
     * @param array $params
     * @return mixed
     */
    public static function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardVice::find($params['id'])->update($params);
    }

    /**
     * 油品副卡表 批量编辑
     *
     * @param array $params
     * @return mixed
     */
    public static function edits(array $params, $idin)
    {

        \helper::argumentCheck(['id'], $idin);

        return OilCardVice::whereIn('id', $idin['id'])->update($params);
    }

    /**
     * PDO批量修改
     *
     * @param       $tableName
     * @param array $apiData
     * @return bool|int
     */
    public static function batchEditByPdo($tableName = 'oil_card_vice', $apiData = [])
    {
        //批量入库
        $data = FALSE;
        if ($apiData) {
            $batchSqlArr = [];
            foreach ($apiData as $v) {
                if (!isset($v['where'])) {
                    throw new \RuntimeException('where条件缺失', 6);
                }
                $fieldStr = '';
                $where    = '';
                foreach ($v as $key => $val) {
                    if ($key != 'where')
                        $fieldStr .= $fieldStr ? ",`" . $key . "`='$val'" : "`" . $key . "`='$val'";
                    else
                        $where = $val;
                }
                $batchSqlArr[] = "UPDATE $tableName SET $fieldStr WHERE $where";
            }
            $batchSql = implode(";", $batchSqlArr);
            Log::info('$batchSql--' . $batchSql, [], 'updateCardVice');
            $data = Capsule::connection()->getPdo()->exec($batchSql);
        }

        return $data;
    }

    public static function updateBalanceByViceNo(array $params)
    {
        \helper::argumentCheck(['vice_no'], $params);
        Log::info('updateBalanceByViceNo参数--' . var_export($params, TRUE), [], 'updateCardVice');

        return OilCardVice::where('vice_no', '=', $params['vice_no'])->update($params);
    }

    public static function updateByViceNo(array $params)
    {
        \helper::argumentCheck(['vice_no'], $params);
        Log::info('updateByViceNo参数--' . var_export($params, TRUE), [], 'updateCardVice');

        return OilCardVice::where('vice_no', '=', $params['vice_no'])->update($params);
    }

    public static function updateByViceNoArr(array $viceNoArr, array $params)
    {
        Log::info('updateByViceNoArr卡号--' . var_export($viceNoArr, TRUE) . '--参数' . var_export($params, TRUE), [], 'updateCardVice');

        return OilCardVice::whereIn('vice_no', $viceNoArr)->update($params);
    }

    /**
     * 油品副卡表 根据ids删除或批量删除
     *
     * @param array $params
     * @return int
     */
    public static function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardVice::destroy($params['ids']);
    }

    public static function getViceByOrgid(array $params)
    {
        \helper::argumentCheck(['org_id'], $params);

        $data = OilCardVice::where('org_id', '=', $params['org_id'])->get();

        return $data;
    }

    /**
     * 探测指定机构下是否有充值卡和公司卡
     *
     * @param null $orgCode
     * @return mixed
     */
    public static function checkHasSkidCard($orgCode = NULL)
    {
        $skidType = OilCom::getSkidType();

        if (!$orgCode) {
            return NULL;
        }

        return OilCardVice::whereIn("oil_card_vice.oil_com", $skidType)
                          ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                          ->where('oil_org.orgcode', 'like', $orgCode . '%')
                          ->first();
    }

    public static function getMoneyByGasMoneyId(array $params)
    {
        $sqlObj = OilCardViceTrades::select(Capsule::connection()->raw('oil_org.orgcode,sum(oil_card_vice_trades.fanli_money) as fanli_money'));
        $sqlObj->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no');
        $sqlObj->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.gas_money_id');
        $sqlObj->whereIn('oil_card_vice.gas_money_id', $params)
               ->groupBy('oil_card_vice.gas_money_id')
               ->lists('orgcode', 'fanli_money');

        $group = $sqlObj->get()->toArray();

        return $group;
    }

    /**
     * @title    根据gasMoneyId获取卡的张数
     * @desc
     * @param array $gasMoneyIds
     * @return array
     * @returns
     * array
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function getGasCardNum(array $gasMoneyIds)
    {
        if ($gasMoneyIds) {
            return OilCardVice::whereIn("oil_card_vice.gas_money_id", $gasMoneyIds)
                              ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.gas_money_id')
                              ->select(Capsule::connection()->raw('oil_org.orgcode,count(*) as cardNum'))
                              ->groupBy('gas_money_id')->get()->toArray();
        } else {
            return [];
        }
    }

    /**
     * 根据卡号批量查询，并组织成卡号和id对应数组
     * getByViceNosForMaps
     *
     * @param array $viceNos
     * @return mixed
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function getByViceNosForMaps(array $viceNos = [])
    {
        return OilCardVice::whereIn("vice_no", $viceNos)->lists("id", "vice_no");
    }

    /**
     * 按副卡id批量查询
     *
     * @param array $ids
     * @return mixed
     */
    public static function getByIds(array $ids = [])
    {
        return OilCardVice::whereIn("id", $ids)->with(
            [
                'Org' => function ($query) {
                    $query->select('id', 'org_name');
                },
            ]
        )->get();
    }

    /**
     * 按副卡号批量查询
     *
     * @param array $ids
     * @return mixed
     */
    public static function getByViceIds(array $ids = [])
    {
        return OilCardVice::whereIn("id", $ids)->with(
            [
                'Org'      => function ($query) {
                    $query->select('id', 'orgcode', 'org_name', 'operators_id');
                },
                'CardMain' => function ($query) {
                    $query->select('id', 'main_no', 'oil_com', 'is_jifen', 'account_name', 'account_password', 'active_region', 'fanli_region', 'operators_id');
                },
            ]
        )->get();
    }

    /**
     * 按副卡号批量查询
     *
     * @param array $viceNos
     * @return mixed
     */
    public static function getByViceNos(array $viceNos = [])
    {
        return OilCardVice::whereIn("vice_no", $viceNos)->with(
            [
                'Org'      => function ($query) {
                    $query->select('id', 'orgcode', 'org_name', 'operators_id');
                },
                'CardMain' => function ($query) {
                    $query->select('id', 'main_no', 'oil_com', 'is_jifen', 'account_name', 'account_password', 'active_region', 'fanli_region', 'operators_id', 'card_from');
                    //$query->select('id', 'main_no', 'oil_com', 'is_jifen', 'account_name', 'active_region', 'fanli_region', 'operators_id', 'card_from');
                },
                'CardAccount',
            ]
        )->get();
    }

    /**
     * 按副卡号批量查询
     *
     * @param array $viceNos
     * @return mixed
     */
    public static function getByViceNosForG7s(array $viceNos = [])
    {
        return OilCardVice::whereIn("vice_no", $viceNos)->with(
            [
                'Org'      => function ($query) {
                    $query->select('id', 'orgcode', 'org_name', 'operators_id');
                },
                'CardMain' => function ($query) {
                    //$query->select('id', 'main_no', 'oil_com', 'is_jifen', 'account_name', 'account_password', 'active_region', 'fanli_region', 'operators_id', 'card_from');
                    $query->select('id', 'main_no', 'oil_com', 'is_jifen', 'active_region', 'fanli_region', 'operators_id', 'card_from');
                },
                'CardAccount',
            ]
        )->get();
    }


    /**
     * @title    预处理查询任务数据
     * @desc
     * @param $viceNos
     * @return array
     * @returns
     * array
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function preTaskData($viceNos)
    {
        $viceNosArr = explode(',', $viceNos);
        $data       = self::getByViceNos($viceNosArr);
        $beginDate  = date('Y-m-01', time());                                               //月初
        $endDate    = date('Y-m-d', strtotime(date('Y-m-01', time()) . ' +1 month -1 day'));//月末
        $task       = [];
        $viceNosArr = $gasViceNos = [];

        $clientEnv = OilConfigure::getBySysKey('client_env');
        foreach ($data as $v) {
            if (in_array($v->oil_com, OilCom::getFisrtType())) {
                $gasViceNos[] = $v->vice_no;
            } else {
                $oilCardType                           = $v->CardMain->oil_com;
                $mainNo                                = $v->CardMain->main_no;
                $oilCom                                = OilCom::getById($oilCardType);
                $task[$mainNo]['cardtype']             = $oilCom ? $oilCom['inter_name'] : '';
                $task[$mainNo]['account']              = ($clientEnv && in_array($oilCardType,[2,26])) ? $mainNo : $v->CardMain->account_name;
                $task[$mainNo]['level']                = 1;
                $task[$mainNo]['callback']             = '';
                $task[$mainNo]['params']['taskType']   = AutoAssignTaskType::getQueryType($oilCardType);
                $task[$mainNo]['params']['account']    = $v->CardMain->account_name;
                $task[$mainNo]['params']['password']   = $v->CardMain->account_password;
                $task[$mainNo]['params']['parentcard'] = $mainNo;
                $task[$mainNo]['params']['beginDate']  = $beginDate;
                $task[$mainNo]['params']['endDate']    = $endDate;
                $task[$mainNo]['params']['page']       = '1';
                $task[$mainNo]['params']['limit']      = '1000';
                $task[$mainNo]['params']['cardList'][] = $v->vice_no;

            }
        }

        foreach ($task as &$v) {
            $v['params'] = json_encode($v['params']);
        }

        return ['task' => $task, 'gasViceNos' => $gasViceNos];
    }

    /**
     * 余额实时刷新
     *
     * @param $viceNos
     * @param bool $returnTask
     * @return bool|array
     */
    public static function balanceRefresh($viceNos, $returnTask = false)
    {
        //修改副卡余额刷新状态
        OilCardVice::whereIn('vice_no', explode(',', $viceNos))->update(['refresh_status' => 5]);
        $taskInfo = self::preTaskData($viceNos);
        $taskData = $taskInfo['task'];

        //加入1号卡的余额刷新功能
        if ($taskInfo['gasViceNos']) {
            self::balanceRefreshForGas($taskInfo['gasViceNos']);
        }

        if ($taskData) {
            global $app;
            $creator_id = $app->myAdmin ? $app->myAdmin->id : '1';
            //开启事务
            Capsule::connection()->beginTransaction();
            try {
                $autoAssign = new AutoAssign();
                $autoAssign
                    ->setData($taskData)
                    ->sendQueryTask(function ($result) use ($creator_id, &$taskData, $viceNos) {
                        //得到taskId写入分配任务表
                        if ($result['taskId']) {
                            $taskData['taskId'] = $result['taskId'];
                            Cache::put("query_card_balance_task_args_{$result['taskId']}", $viceNos, 120);
                            $nowTime = \helper::nowTime();
                            $data    = OilAccountAssignTask::add([
                                'main_no'    => $result['main_no'],
                                'taskId'     => $result['taskId'],
                                'creator_id' => $creator_id,
                                'status'     => 0,
                                'type'       => 2,
                                'createtime' => $nowTime,
                            ]);
                            Log::notice('task--', $data, 'balanceRefresh');
                            //添加任务明细
                            $params     = json_decode($taskData[$result['main_no']]['params'], TRUE);
                            $viceNosArr = $params['cardList'];
                            Log::notice('vice--', $viceNosArr, 'balanceRefresh');
                            foreach ($viceNosArr as $v) {
                                $data = OilAccountAssignTaskDetail::add([
                                    'taskId'     => $result['taskId'],
                                    'vice_no'    => $v,
                                    'status'     => 0,
                                    'createtime' => $nowTime,
                                ]);
                                Log::notice('taskdetail--', $data, 'notice');
                            }
                        }

                        return TRUE;
                    });
                //事务提交
                Capsule::connection()->commit();
            } catch (\Exception $e) {
                //事务回滚
                Capsule::connection()->rollBack();
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }
        }

        if ($returnTask) {
            return $taskData;
        }
        return TRUE;
    }

    /*
     * 桥装卡余额实时刷新
     */
    public static function balanceRefreshForGas($viceNos)
    {
        global $app;
        if ($viceNos && is_array($viceNos)) {
            $viceData = GasClient::post(
                [
                    'method' => 'gas.api.getCardsBalance',
                    'data'   => $viceNos
                ]
            );
            Log::error('$viceData' . var_export($viceData, TRUE), [], 'balanceRefreshForGas');
            $updateBalanceArr = [];
            if ($viceData) {
                $viceNoArr = $mapViceMoney = [];
                foreach ($viceData as $v) {
                    $updateBalanceArr[$v->card_no] = [
                        'where'       => " vice_no = '" . $v->card_no . "' ",
                        'card_remain' => $v->money,
                        'oil_balance' => $v->oil_money,
                    ];
                    $viceNoArr[]                   = $v->card_no;
                    $mapViceMoney[$v->card_no]     = ['card_remain' => $v->money, 'oil_balance' => $v->oil_money];
                }
                try {
                    //todo 18号不能更新卡余额
                    if ($app->config->gas->is_expire == 0) {
                        //self::batchEditByPdo('oil_card_vice', $updateBalanceArr);
                    }
                } catch (\Exception $e) {
                    Log::error('update' . var_export($updateBalanceArr, TRUE), [], 'balanceRefreshForGas');
                    Log::error(strval($e), [], 'balanceRefreshForGas');
                }

                if ($viceNoArr) {
                    if ($app->config->gas->is_expire == 0) {
                        //CardViceToGos::batchUpdateToGosByViceNos($viceNoArr);
                    }
                }
            }
            return $mapViceMoney;
        }
    }

    /**
     * @title    更改卡片油量
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * mixed
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function editAllOilBlance(array $params)
    {
        \helper::argumentCheck(['vice_no', 'oil_balance'], $params);

        return OilCardVice::where('vice_no', '=', $params['vice_no'])->increment('oil_balance', $params['oil_balance']);
    }

    /**
     * 检查是否是或有中石化卡
     *
     * @param array $oilComArr
     * @param null  $oil_com
     * @return bool
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function isZsh($oilComArr = [], $oil_com = NULL)
    {
        return ($oilComArr && in_array(1, $oilComArr)) || ($oil_com && !$oilComArr && intval($oil_com) == 1);
    }

    /**
     * 检查是否是或有中石油卡
     *
     * @param array $oilComArr
     * @param null  $oil_com
     * @return bool
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function isZsy($oilComArr = [], $oil_com = NULL)
    {
        return ($oilComArr && in_array(2, $oilComArr)) || ($oil_com && !$oilComArr && intval($oil_com) == 2);
    }

    /**
     * 检查是否是或有中车油卡
     *
     * @param array $oilComArr
     * @param null  $oil_com
     * @return bool
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function isZcy($oilComArr = [], $oil_com = NULL)
    {
        return ($oilComArr && (in_array(3, $oilComArr) || in_array(50, $oilComArr))) || ($oil_com && !$oilComArr && in_array(intval($oil_com), [3, 50]));
    }

    /**
     * 检查是否是充值卡
     *
     * @param array $oilComArr
     * @param null  $oil_com
     * @return bool
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function isCzk($oilComArr = [], $oil_com = NULL)
    {
        return ($oilComArr && in_array(6, $oilComArr)) || ($oil_com && !$oilComArr && intval($oil_com) == 6);
    }

    /**
     * 检查是否是记录卡
     *
     * @param array $oilComArr
     * @param null  $oil_com
     * @return bool
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function isJlk($oilComArr = [], $oil_com = NULL)
    {
        return ($oilComArr && in_array(8, $oilComArr)) || ($oil_com && !$oilComArr && intval($oil_com) == 8);
    }

    /**
     * 检查是否是或有公司卡
     *
     * @param array $oilComArr
     * @param null  $oil_com
     * @return bool
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function isGsk($oilComArr = [], $oil_com = NULL)
    {
        return ($oilComArr && in_array(7, $oilComArr)) || ($oil_com && !$oilComArr && intval($oil_com) == 7);
    }

    /**
     * 判断副卡是否为充值卡或记账卡
     *
     * @param array $viceNo
     * @return bool
     * <AUTHOR>
     */
    public static function allIsCzkOrGsk($viceNo = [])
    {
        $oilComArr = OilCardVice::getOilComByViceNo($viceNo);
        $oilComArr = array_unique($oilComArr);
        foreach ($oilComArr as $v) {
            if (!in_array($v, [6, 7]))
                return FALSE;
        }

        return TRUE;
    }

    /**
     * 获取副卡类型
     *
     * @param array $viceNo
     * @return mixed
     * <AUTHOR>
     */
    public static function getOilComByViceNo($viceNo = [])
    {
        return OilCardVice::whereIn('vice_no', $viceNo)->pluck('oil_com')->toArray();
    }

    /**
     * 增加副卡所属机构变更日志
     *
     * @param $params
     * @param $fields
     * <AUTHOR>
     */
    public static function addOilViceOrgChange(&$params, &$fields)
    {
        //如果是充值卡或记账卡，则允许修改副卡所属机构
        if (OilCardVice::allIsCzkOrGsk($params['vice_no'])) {
            $fields[] = 'org_id';
            $fields[] = 'status';
            $fields[] = 'bind_status';
            if (isset($params['truck_no']) && $params['truck_no']) {
                $params['bind_status'] = 1;
            } else {
                $params['bind_status'] = 0;
            }

            if (isset($params['orgcode_use']) && $params['orgcode_use']) {
                $orgInfo          = OilOrg::getByOrgcode($params['orgcode_use']);
                $params['org_id'] = $orgInfo->id;
                $cardViceInfo     = OilCardVice::getByViceNos($params['vice_no']);
                $cards            = [];
                foreach ($cardViceInfo as $v) {
                    $cards[$v->vice_no] = $v->org_id;
                }
                foreach ($params['vice_no'] as $v) {
                    //如果修改所属机构，则添加副卡机构变更日志
                    $orgChange = OilViceOrgChange::getInfo(['vice_no' => $v, 'begin_time' => date("Y-m-d", strtotime("+1 day"))]);
                    if ($orgChange) {
                        if ($orgChange->org_id_original != $params['org_id']) {
                            OilViceOrgChange::add([
                                'vice_no'          => $v,
                                'org_id'           => $params['org_id'],
                                'org_id_original'  => $orgChange->org_id_original,
                                'begin_time'       => date("Y-m-d", strtotime("+1 day")),//一天后生效
                                'createtime'       => date('Y-m-d H:i:s'),
                                'other_creator_id' => $params['other_creator_id'],
                                'other_creator'    => $params['other_creator'],
                            ]);
                        }
                        OilViceOrgChange::remove(['ids' => $orgChange->id]);
                    } else {
                        if ($cards[$v] != $params['org_id']) {
                            OilViceOrgChange::add([
                                'vice_no'          => $v,
                                'org_id'           => $params['org_id'],
                                'org_id_original'  => $cards[$v],
                                'begin_time'       => date("Y-m-d", strtotime("+1 day")),//一天后生效
                                'createtime'       => date('Y-m-d H:i:s'),
                                'other_creator_id' => $params['other_creator_id'],
                                'other_creator'    => $params['other_creator'],
                            ]);
                        }
                    }
                }
            }
        }
    }

    /**
     * @title    综合修改逻辑
     * @desc
     * @param array $params
     * @param array $Card_bindArr
     * @param array $cardMap
     * @return bool
     * @returns
     * bool
     * @returns
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     */
    public static function updateForAllNotJlk(array $params, array $Card_bindArr, array $cardMap)
    {
        //allow update [truck_no,driver_tel,driver_name,driver_line,remark]
        if (count($params['vice_no']) > 1) {
            $fields = ['driver_line', 'remark_work'];
        } else {
            $fields = ['truck_no', 'driver_tel', 'driver_name', 'driver_line', 'remark_work'];
        }
        //增加副卡所属机构变更日志
        OilCardVice::addOilViceOrgChange($params, $fields);

        $gasCard      = [];
        $upDataParams = OilCardVice::getUpdateParams($params, $fields, ['remark_work']);
        if ($upDataParams && $params['vice_no']) {
            foreach ($params['vice_no'] as $v) {
                //其中中石油，中石化不能修改已绑定卡的卡信息
                if (isset($Card_bindArr[$v]) && $Card_bindArr[$v] == 1) {
                    throw new \RuntimeException($v . '此卡已绑定,不能再次进行修改', 2);
                    //continue;
                }

                OilCardVice::where('vice_no', '=', $v)->update($upDataParams);

                //筛选出充值卡和公司卡
                if ($cardMap[$v]->oil_com == 6 || $cardMap[$v]->oil_com == 7) {
                    $gasCard[] = $params['vice_no'];
                }
            }
            if ($gasCard && isset($upDataParams['truck_no'])) {
                $gasData = ['card_no' => $gasCard, 'trukc_no' => $upDataParams['truck_no']];
                /************************充值卡或公司卡修改需调撬装平台油卡修改接口************************/
                GasClient::put(
                    [
                        'method' => 'gas.api.cardSet',
                        'data'   => $gasData,
                    ]
                );
            }
        }

        return TRUE;
    }

    /**
     * @title    综合修改逻辑
     * @desc
     * @param array $params
     * @param array $Card_bindArr
     * @param array $cardMap
     * @return bool
     * @returns
     * bool
     * @returns
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     */
    public static function updateForAll(array $params, array $Card_bindArr, array $cardMap)
    {
        //allow update [org_id,truck_no,driver_tel,driver_name,driver_line,remark]
        if (count($params['vice_no']) > 1) {
            $fields = ['driver_line', 'remark_work'];
        } else {
            $fields = ['driver_tel', 'driver_name', 'driver_line', 'remark_work'];
        }
        //增加副卡所属机构变更日志
        OilCardVice::addOilViceOrgChange($params, $fields);

        $upDataParams = OilCardVice::getUpdateParams($params, $fields, ['remark_work']);

        //Log::debug('$params:', $params, 'oilCardVice');
        //Log::debug('$upDataParams', $upDataParams, 'oilCardVice');

        if ($upDataParams && $params['vice_no']) {
            foreach ($params['vice_no'] as $v) {
                OilCardVice::where('vice_no', '=', $v)->update($upDataParams);
            }
        }

        return TRUE;
    }

    /**
     * @title    记录员卡修改逻辑
     * @desc
     * @param array $params
     * @return bool
     * @returns
     * bool
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function updateForJlk(array $params)
    {

        //allow update [truck_no,driver_tel,driver_name,driver_line,remark,day_top,oil_top] [消费限额，绑定车辆，解绑车辆，修改密码，锁定油卡，解锁油卡]
        //Log::debug('count:' . count($params['vice_no']), [], 'oilCardVice');
        if (count($params['vice_no']) > 1) {
            $fields    = ['driver_line', 'remark_work', 'day_top', 'oil_top', 'orgcode'];
            $gasFields = ['day_top', 'oil_top', 'password', 'orgcode'];
        } else {
            $fields    = ['driver_tel', 'driver_name', 'driver_line', 'remark_work', 'day_top', 'oil_top', 'orgcode'];
            $gasFields = ['day_top', 'oil_top', 'password', 'orgcode'];
        }

        $upDataParams = OilCardVice::getUpdateParams($params, $fields, ['remark_work', 'day_top', 'oil_top']);
        $gasParams    = OilCardVice::getUpdateParams($params, $gasFields, ['day_top', 'oil_top', 'password']);

        Log::debug('$params:', $params, 'oilCardVice');
        Log::debug('$upDataParams', $upDataParams, 'oilCardVice');
        //$gasParams['card_no'] = $params['vice_no'];
        Log::debug('$gasParams', $gasParams, 'oilCardVice');

        if ($upDataParams && $params['vice_no']) {
            foreach ($params['vice_no'] as $v) {
                /*********************加油员卡修改所属账户********************/
                if (isset($upDataParams['orgcode']) && $upDataParams['orgcode'] != '') {
                    $org                          = OilOrg::getByOrgcode($upDataParams['orgcode']);
                    $upDataParams['gas_money_id'] = $org->id;
                    unset($upDataParams['orgcode']);
                }
                if (isset($upDataParams['orgcode']) && $upDataParams['orgcode'] == '')
                    unset($upDataParams['orgcode']);
                OilCardVice::where('vice_no', '=', $v)->update($upDataParams);
            }
        }

        if ($gasParams) {
            $gasParams['card_no'] = $params['vice_no'];
            if (isset($params['orgcode_use']) && !empty($params['orgcode_use']))
                $gasParams['gascode'] = $params['orgcode_use'];
            /************************充值卡或公司卡修改需调撬装平台油卡修改接口************************/
            $gasData = GasClient::put(
                [
                    'method' => 'gas.api.cardSet',
                    'data'   => $gasParams,
                ]
            );
            Log::debug('$gasData', $gasData, 'oilCardVice');
        }

        return TRUE;
    }

    /**
     * @title    充值卡修改逻辑
     * @desc
     * @param array $params
     * @return bool
     * @returns
     * bool
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function updateForCzk(array $params)
    {

        //allow update [truck_no,driver_tel,driver_name,driver_line,remark,day_top,oil_top] [消费限额，绑定车辆，解绑车辆，修改密码，锁定油卡，解锁油卡]
        //Log::info('count:' . count($params['vice_no']), [], 'oilCardVice');
        if (count($params['vice_no']) > 1) {
            $fields    = ['driver_line', 'remark_work', 'day_top', 'oil_top', 'month_top', 'orgcode'];
            $gasFields = ['day_top', 'oil_top', 'month_top', 'password', 'orgcode'];
        } else {
            $fields    = ['truck_no', 'driver_tel', 'driver_name', 'driver_line', 'remark_work', 'day_top', 'oil_top', 'month_top', 'orgcode'];
            $gasFields = ['truck_no', 'driver_tel', 'driver_name', 'driver_line', 'day_top', 'oil_top', 'password', 'orgcode'];
        }
        //增加副卡所属机构变更日志
        OilCardVice::addOilViceOrgChange($params, $fields);

        $upDataParams = OilCardVice::getUpdateParams($params, $fields, ['remark_work', 'day_top', 'oil_top']);
        if (isset($upDataParams['truck_no']) && $upDataParams['truck_no']) {
            $upDataParams['bind_status'] = 1;
        }
        $gasParams = OilCardVice::getUpdateParams($params, $gasFields, ['day_top', 'oil_top', 'password']);

        Log::debug('$params:', $params, 'oilCardVice');
        Log::debug('$upDataParams', $upDataParams, 'oilCardVice');
        //$gasParams['card_no'] = $params['vice_no'];
        Log::debug('$gasParams', $gasParams, 'oilCardVice');
        if (count($params['vice_no']) == 1) {
            if (!$upDataParams['truck_no'])
                $upDataParams['bind_status'] = 0;
        }
        if ($upDataParams && $params['vice_no']) {
            foreach ($params['vice_no'] as $v) {
                /*********************充值卡修改所属账户********************/
                if (isset($upDataParams['orgcode']) && $upDataParams['orgcode'] != '') {
                    $org                          = OilOrg::getByOrgcode($upDataParams['orgcode']);
                    $upDataParams['gas_money_id'] = $org->id;
                    unset($upDataParams['orgcode']);
                } elseif ($upDataParams['orgcode'] == '') {
                    unset($upDataParams['orgcode']);
                }
                OilCardVice::where('vice_no', '=', $v)->update($upDataParams);
            }
        }
        if ($gasParams) {
            $gasParams['card_no'] = $params['vice_no'];
            if (isset($params['driver_tel']) && !empty($params['driver_tel']))
                $gasParams['driverphone'] = $params['driver_tel'];
            if (isset($params['driver_name']) && !empty($params['driver_name']))
                $gasParams['drivername'] = $params['driver_name'];
            if (isset($params['driver_line']) && !empty($params['driver_line']))
                $gasParams['driveroade'] = $params['driver_line'];

            $gasParams['islock'] = 0;
            if ($params['status'] == '锁定') {
                $gasParams['islock'] = 1;
            }


            if (isset($params['orgcode_use']) && !empty($params['orgcode_use']))
                $gasParams['gascode'] = $params['orgcode_use'];

            /************************充值卡或公司卡修改需调撬装平台油卡修改接口************************/
            $gasData = GasClient::put(
                [
                    'method' => 'gas.api.cardSet',
                    'data'   => $gasParams,
                ]
            );
            Log::debug('$gasData', $gasData, 'oilCardVice');
        }

        return TRUE;
    }

    /**
     * @title    公司卡修改逻辑
     * @desc
     * @param array $params
     * @return bool
     * @returns
     * bool
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function updateForGsk(array $params)
    {
        //allow update [truck_no,driver_tel,driver_name,driver_line,remark] 【公司卡】 [消费限额，绑定车辆，解绑车辆，充值密码，锁定油卡，解锁油卡]
        if (count($params['vice_no']) > 1) {
            $fields    = ['driver_line', 'remark_work', 'day_top', 'oil_top', 'orgcode'];
            $gasFields = ['day_top', 'oil_top', 'password', 'orgcode'];
        } else {
            $fields    = ['truck_no', 'driver_tel', 'driver_name', 'driver_line', 'remark_work', 'day_top', 'oil_top', 'orgcode'];
            $gasFields = ['truck_no', 'day_top', 'oil_top', 'password', 'orgcode'];
        }
        //增加副卡所属机构变更日志
        OilCardVice::addOilViceOrgChange($params, $fields);

        $upDataParams = OilCardVice::getUpdateParams($params, $fields, ['remark_work', 'day_top', 'oil_top', 'orgcode']);
        if (isset($upDataParams['truck_no']) && $upDataParams['truck_no']) {
            $upDataParams['bind_status'] = 1;
        }
        $gasParams = OilCardVice::getUpdateParams($params, $gasFields, ['day_top', 'oil_top', 'password', 'orgcode']);

        Log::debug('$params:', $params, 'oilCardVice');
        Log::debug('$upDataParams', $upDataParams, 'oilCardVice');
        //$gasParams['card_no'] = $params['vice_no'];
        Log::debug('$gasParams', $gasParams, 'oilCardVice');
        if (count($params['vice_no']) == 1) {
            if (!$upDataParams['truck_no'])
                $upDataParams['bind_status'] = 0;
        }
        if ($upDataParams && $params['vice_no']) {
            foreach ($params['vice_no'] as $v) {
                if (isset($upDataParams['orgcode']) && $upDataParams['orgcode'] != '') {
                    $org                          = OilOrg::getByOrgcode($upDataParams['orgcode']);
                    $upDataParams['gas_money_id'] = $org->id;
                    unset($upDataParams['orgcode']);
                }
                OilCardVice::where('vice_no', '=', $v)->update($upDataParams);
            }
        }

        if ($gasParams) {
            $gasParams['card_no'] = $params['vice_no'];
            if (isset($params['orgcode_use']) && !empty($params['orgcode_use']))
                $gasParams['gascode'] = $params['orgcode_use'];
            /************************充值卡或公司卡修改需调撬装平台油卡修改接口************************/
            $gasData = GasClient::put(
                [
                    'method' => 'gas.api.cardSet',
                    'data'   => $gasParams,
                ]
            );
            Log::debug('$gasData', $gasData, 'oilCardVice');
        }

        return TRUE;
    }

    /**
     * @title    中石油，中石化，中车油
     * @desc
     * @param array $params
     * @param array $Card_bindArr
     * @return bool
     * @returns
     * bool
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Models
     */
    public static function updateForZsyAndZshAndZcy(array $params, array $Card_bindArr, $cardMap = [])
    {
        //allow update [truck_no,driver_tel,driver_name,driver_line,remark]
        // 批量操作
        if (!empty($params['is_batch_op'])) {
            $fields              = ['driver_line', 'remark_work', 'org_id', 'ischeck'];
            $force_assign_fields = [
                'driver_line' => '',
                'remark_work' => ''
            ];

            // 单条操作
        } else {
            $fields              = ['truck_no', 'driver_tel', 'driver_name', 'driver_line', 'remark_work', 'org_id', 'able_transfer', 'ischeck', 'last_operator', 'truck_type'];
            $force_assign_fields = [
                'truck_no'    => '',
                'driver_tel'  => '',
                'driver_name' => '',
                'driver_line' => '',
                'remark_work' => ''
            ];

            // 一号卡不可更改手机号
            if (!empty($params['is_one_card'])) {
                unset($force_assign_fields['driver_tel']);
                $fields = array_merge($fields,['card_owner']);
            }
        }

        Log::error('updateForZsyAndZshAndZcy $params==' . var_export($params, true), [$fields], 'cardSet');
        $upDataParams = OilCardVice::getUpdateParams($params, $fields, [], $force_assign_fields);

        if ($upDataParams && $params['vice_no']) {
            foreach ($params['vice_no'] as $v) {
                //其中中石油，中石化不能修改已绑定卡的卡信息
                if (isset($Card_bindArr[$v]) && $Card_bindArr[$v] == 1) {
                    throw new \RuntimeException($v . '此卡已绑定,不能再次进行修改', 2);
                    //continue;
                }
                //查询卡信息
                if (in_array($Card_bindArr[$v]['oil_com'], OilCom::getAllFirstList())) {
                    $upDataParams['status'] = ViceCardStatus::getStatusName($params['status']);
                    /*if ($params['paylimit'] == 1) {
                        $upDataParams['day_top'] = $params['day_top'];
                        $upDataParams['oil_top'] = $params['oil_top'];
                        $upDataParams['month_top'] = $params['month_top'];
                    } else {
                        $upDataParams['day_top'] = 0;
                        $upDataParams['oil_top'] = 0;
                        $upDataParams['month_top'] = 0;
                    }*/
                    // 单条时可设置消费限制、日限额、次限额、月限额
                    if (empty($params['is_batch_op'])) {
                        $upDataParams['paylimit']  = $params['paylimit'];
                        $upDataParams['day_top']   = $params['day_top'];
                        $upDataParams['oil_top']   = $params['oil_top'];
                        $upDataParams['month_top'] = $params['month_top'];
                    }
                    if (isset($params['card_level'])) {
                        $upDataParams['card_level']    = $params['card_level'];
                        $upDataParams['check_truckno'] = $params['card_level'] && $params['card_level'] == 2 ? 1 : 2;
                    }

                    $upDataParams['vice_password'] = $params['password'];

                    //针对1号共享卡扣款账号的判断逻辑
                    if ($Card_bindArr[$v]['oil_com'] == OilCom::GAS_FIRST_TALLY) {
                        if (isset($params['deduction_account_no']) && $params['deduction_account_no']) {
                            $upDataParams['deduction_account_no'] = $params['deduction_account_no'];
                            if (substr($params['deduction_account_no'], 0, 3) == '208') {
                                $creditAccount = \Models\OilCreditAccount::getByAccountNoWithProvider($params['deduction_account_no']);
                                if (!$creditAccount) {
                                    throw new \RuntimeException($params['deduction_account_no'] . '扣款账号不存在', 2);
                                }
                                $upDataParams['deduction_account_name'] = $creditAccount->CreditProvider->name;
                            } else {
                                $upDataParams['deduction_account_name'] = ConsumeType::FIRST_TALLY_ACCOUNT_NAME;//ENINET-598
                            }
                        }
                    }
                    // 是否可转油 只支持单条设置
                    if (isset($params['able_transfer']) && in_array($params['able_transfer'], [1, 2]) && empty($params['is_batch_op'])) {
                        $upDataParams['able_transfer'] = $params['able_transfer'];
                    }
                    Log::error('updateForZsyAndZshAndZcy $upDataParams==' . var_export($upDataParams, true), [], 'cardSet');
                }

                $res = OilCardVice::where('vice_no', '=', $v)->update($upDataParams);

                //如果修改所属机构，则添加副卡机构变更日志 todo 待测试
                $info = $cardMap[$v];
                if ($info && $params['org_id'] && $info->org_id != $params['org_id']) {
                    $addArr['vice_no']         = $info->vice_no;
                    $addArr['org_id']          = $params['org_id'];
                    $addArr['org_id_original'] = $info->org_id;
                    $addArr['begin_time']      = date('Y-m-d H:i:s');
                    $addArr['createtime']      = date('Y-m-d H:i:s');
                    $addArr['creator_id']      = 1; //G7s端
                    \Models\OilViceOrgChange::add($addArr);

                    //卡片变更机构
                    if (in_array($info->card_from, CardFrom::getNotCustomerCard())) {
                        (new \Fuel\Service\AccountCenter\CardService())->changeOrgByViceNo($v);
                    }
                }
            }
        }

        return TRUE;
    }

    /**
     * @title    根据能修改的字段对参数进行过滤
     * @desc
     * @param array $params
     * @param array $fields
     * @param array $enableEmpty
     * @param array $force_assign_fields // 强制赋值字段, 必须是关联数组，key是字段 val是值;key在fields中且params没有该key的值
     * @return array
     * @returns
     *                                   array
     * @returns
     * @example  ['name'=>'', 'user_id'=>0]
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     */
    public static function getUpdateParams(array $params, array $fields, array $enableEmpty, $force_assign_fields = [])
    {
        $fieldArr = [];
        foreach ($fields as $v) {
            if (isset($params[$v])) {
                $fieldArr[$v] = $params[$v];
            }
        }
        if ($enableEmpty) {
            foreach ($enableEmpty as $val) {
                if (isset($fieldArr[$val]) && !$fieldArr[$val]) {
                    unset($fieldArr[$val]);
                }
            }
        }

        // 20190703 强制赋值字段
        if (!empty($force_assign_fields)) {
            foreach ($force_assign_fields as $key => $val) {
                if (in_array($key, $fields) && !isset($params[$key])) {
                    $fieldArr[$key] = $val;
                }
            }
        }

        return $fieldArr;
    }

    /**
     * 获取所有副卡id
     */
    public static function getAllViceIds()
    {

        return OilCardVice::whereIn('oil_com', OilCom::getNotGssType())->select('id', 'vice_no')->get();

    }

    /**
     * @title    根据id获取vice_no的map
     * @desc
     * @param array $ids
     * @return null
     * @returns
     * null
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function getViceMapByIds(array $ids)
    {
        $res = NULL;

        $data = OilCardVice::whereIn('id', $ids)->select('id', 'vice_no')->get();
        if ($data) {
            foreach ($data as $v) {
                $res[$v->id] = $v->vice_no;
            }
        }

        return $res;
    }

    /**副卡余额刷新统计
     * 去除status限制：`status` in ('".ViceCardStatus::USING."','".ViceCardStatus::LIMITED."') AND
     *
     * @param array $params
     * @return array
     */
    public static function balanceStats(array $params)
    {
        $checkDate = isset($params['checkDate']) ? date("Y-m-d H:i:s", strtotime(['checkDate'])) : date("Y-m-d H:i:s",
            strtotime("-1 day"));

        // 2019-05-14 变更统计标准
        $sql = "SELECT
DATE_FORMAT(remain_get_time,'%Y-%m-%d') as sync_date,count(*) as total,oil_com
FROM
oil_card_vice
WHERE status = '使用' and remain_get_time < '" . $checkDate . "' and card_from not in (40,41)";

//        $sql = "SELECT
//DATE_FORMAT(remain_get_time,'%Y-%m-%d') as sync_date,count(*) as total,oil_com
//FROM
//oil_card_vice
//WHERE status in ('使用','受限','其他') and remain_get_time > '".date("Y-m-d H:i:s",strtotime("-40 day"))."' and remain_get_time < '" . $checkDate . "' and card_from not in (40,41)";

        if (isset($params['oilComIn']) && $params['oilComIn']) {
            $sql .= " AND oil_com in (" . implode(",", $params['oilComIn']) . ")";
        } else {
            $sql .= " and oil_com in (1,2,50,52,26) ";
        }

        if (isset($params['groupByDate']) && $params['groupByDate']) {
            $sql .= " GROUP BY oil_com, DATE_FORMAT(remain_get_time,'%Y-%m-%d')";
        } else {
            $sql .= " GROUP BY oil_com";
        }

        if (isset($params['filterGt']) && $params['filterGt'] != '') {
            $sql .= " HAVING(total > " . $params['filterGt'] . ")";
        }

        $sql .= " ORDER BY sync_date DESC,total";

        $data = Capsule::connection()->select($sql);

        return $data;
    }

    /**
     * @title    根据field 查卡信息的field信息
     * @desc
     * @param $vice_no
     * @param $field
     * @return mixed
     * @returns
     * mixed
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Models
     */
    public static function getFieldByViceNo($vice_no, $field)
    {
        $cacheName = __CLASS__ . '_' . __METHOD__ . 'getFieldByViceNo' . $vice_no . var_export($field, TRUE);
        $data      = Cache::get($cacheName);
        if (!$data) {
            $data = OilCardVice::select('id', $field)->where('vice_no', '=', $vice_no)->first();
            Cache::put($cacheName, $data, 60);
        }

        return $data;
    }

    /**
     * @param $vice_no
     * @return mixed
     */
    public static function getOneByViceNo($vice_no)
    {
        return self::where('vice_no', $vice_no)->first();
    }

    /**
     * @title    油卡消费分析
     * @desc
     * @param array $params
     * @return array
     * @returns
     * array
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function getCardTradeAnalysis(array $params)
    {
        $pageSize     = $params['limit'] ? $params['limit'] : 10;
        $pageNo       = $params['page'] ? $params['page'] : 1;
        $oilTradeType = TradesType::getTradesType(TRUE);
        $firstday     = isset($params['firstday']) && $params['firstday'] ? $params['firstday'] . ' 00:00:00' : date('Y-m-01', strtotime('-1 month')) . ' 00:00:00';
        $lastday      = isset($params['lastday']) && $params['lastday'] ? $params['lastday'] . ' 23:59:59' : date('Y-m-d', strtotime("$firstday +1 month -1 day")) . ' 23:59:59';
        $orgcode      = $params['orgcode'];
        $where        = 'where 1=1';
        //油卡id
        if (isset($params['vice_no']) && !empty($params['vice_no'])) {
            $vice_nos = [];
            foreach ($explode = explode(',', $params['vice_no']) as $val) {
                if ($val) {
                    $vice_nos[] = $val;
                }
            }
            if (!empty($vice_nos)) {
                $viceNosStr = implode("','", $vice_nos);
                $where      .= " AND vice_no IN ('{$viceNosStr}')";
            }
        }

        if (isset($params['orgcodes']) && !empty($params['orgcodes'])) {
            $orgcodes = "'" . str_replace(',', "','", $params['orgcodes']) . "'";
            $where    .= " and orgcode in (" . $orgcodes . ")";
        }

//车牌号
        if (isset($params['truck_no']) && !empty($params['truck_no'])) {
            $truck_nos = [];
            foreach ($explode = explode(',', $params['truck_no']) as $val) {
                if ($val) {
                    $truck_nos[] = $val;
                }
            }
            if (!empty($truck_nos)) {
                $truck_nosStr = implode("','", $truck_nos);
                $where        .= " AND truck_no IN ('{$truck_nosStr}')";
            }
        }
        if (isset($params['tag_id']) && $params['tag_id']) {
            $viceNo = OilViceTags::getViceNoByTagId(explode(',', $params['tag_id']));
            if ($viceNo) {
                $viceNo = array_unique($viceNo);
                $viceNo = "'" . implode("','", $viceNo) . "'";
                $where  .= " AND vice_no IN($viceNo)";
            } else {
                $where .= " AND vice_no = ''";
            }
        }
        $sortColumns = isset($params['sortColumns']) && !empty($params['sortColumns']) ? 'order by ' . $params['sortColumns'] : '';
        $sql         = "SELECT
  a.*, ROUND(
    a.total_num / (a.counts - a.refundCount * 2),
    2
  ) AS oil_per_count,
  ROUND(
    a.total_money / (a.counts - a.refundCount * 2),
    2
  ) AS money_per_count,
  ROUND(
    a.total_money / a.total_num,
    2
  ) AS per_price,
  a.counts - a.refundCount * 2 as total_count
FROM
  (
    SELECT
      oo.orgcode,
      oo.org_name,
      ocvt.vice_no,
      ocvt.truck_no,
      count(*) AS counts,
      SUM(ocvt.trade_num) AS total_num,
      SUM(ocvt.trade_money) AS total_money,
      SUM(
            CASE
            WHEN ocvt.trade_money < 0 THEN
                1
            ELSE
                0
            END
        ) AS refundCount
    FROM
      oil_card_vice_trades ocvt
    LEFT JOIN oil_org oo ON ocvt.org_id = oo.id
    WHERE
      oo.orgcode LIKE '" . $orgcode . "%'
    AND ocvt.trade_time > '" . $firstday . "' AND ocvt.trade_time <= '" . $lastday . "'
    AND trade_type IN (" . $oilTradeType . ")
    AND oo.is_del = 0
    GROUP BY
      ocvt.vice_no
  ) a " . $where . " " . $sortColumns . "";

        $total_count = $total_num = $total_money = 0;
        $record      = Capsule::connection()->select($sql);
        if ($record) {
            foreach ($record as $k => $v) {
                $total_count += $v->total_count;
                $total_num   += $v->total_num;
                $total_money += $v->total_money;
            }
        }

        $_data = array_slice($record, ($pageNo - 1) * $pageSize, $pageSize);

        $data                  = [];
        $data['data']          = $_data;
        $data['total']         = count($record);
        $data['from']          = 1;
        $data['to']            = floor($data['total'] / $pageSize);
        $data['per_page']      = $pageSize;
        $data['current_page']  = $pageNo;
        $data['last_page']     = $data['to'];
        $data['total_count']   = $total_count > 0 ? $total_count : 0;
        $data['total_num']     = $total_num > 0 ? sprintf('%0.2f', $total_num) : 0;
        $data['total_money']   = $total_money > 0 ? sprintf('%0.2f', $total_money) : 0;
        $data['avg_total_num'] = $total_count > 0 ? sprintf('%0.2f', $total_num / $total_count) : 0;
        $data['avg_money_num'] = $total_count > 0 ? sprintf('%0.2f', $total_money / $total_count) : 0;
        $data['avg_price']     = $total_num > 0 ? sprintf('%0.2f', $total_money / $total_num) : 0;

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data['data'] = $record;
        }

        return $data;
    }

    /**
     * 车辆消费分析
     *
     * @param array $params
     * @return array
     * <AUTHOR>
     */
    public static function getTruckTradeAnalysis(array $params)
    {
        $pageSize     = $params['limit'] ? $params['limit'] : 10;
        $pageNo       = $params['page'] ? $params['page'] : 1;
        $oilTradeType = TradesType::getTradesType(TRUE);
        $firstday     = isset($params['firstday']) && $params['firstday'] ? $params['firstday'] . ' 00:00:00' : date('Y-m-01', strtotime('-1 month')) . ' 00:00:00';
        $lastday      = isset($params['lastday']) && $params['lastday'] ? $params['lastday'] . ' 23:59:59' : date('Y-m-d', strtotime("$firstday +1 month -1 day")) . ' 23:59:59';
        $orgcode      = $params['orgcode'];
        $where        = 'where 1=1';
        if (isset($params['vice_no']) && !empty($params['vice_no'])) {
            $where .= " and vice_no like '%" . $params['vice_no'] . "%'";
        }
        if (isset($params['orgcodes']) && !empty($params['orgcodes'])) {
            $orgcodes = "'" . str_replace(',', "','", $params['orgcodes']) . "'";
            $where    .= " and orgcode in (" . $orgcodes . ")";
        }
        if (isset($params['truck_no']) && !empty($params['truck_no'])) {
            $truckNo = explode(',', $params['truck_no']);
            $truckNo = implode("','", $truckNo);
            $where   .= " and truck_no in ('" . $truckNo . "')";
            Log::info('tn--' . $where, [], 'zzg');

        }
        //司机姓名
        if (isset($params['driver_name']) && !empty($params['driver_name'])) {
            $where .= " and driver_name like '%{$params['driver_name']}%'";
        }
        if (isset($params['tag_id']) && $params['tag_id']) {
            $viceNo = OilViceTags::getViceNoByTagId(explode(',', $params['tag_id']));
            if ($viceNo) {
                $viceNo = array_unique($viceNo);
                $viceNo = "'" . implode("','", $viceNo) . "'";
                $where  .= " AND vice_no IN($viceNo)";
            } else {
                $where .= " AND vice_no = ''";
            }
        }
        $sortColumns = isset($params['sortColumns']) && !empty($params['sortColumns']) ? 'order by ' . $params['sortColumns'] : '';

        //统计车牌号为空德玛数据
        $sql1 = "SELECT
      '' orgcode,
      '多个机构' org_name,
      '' vice_no,
      a.truck_no, 
      '' driver_name,
      a.counts,
      a.total_num,
      a.total_money,
      a.refundCount,
  ROUND(
    a.total_num / (a.counts - a.refundCount * 2),
    2
  ) AS oil_per_count,
  ROUND(
    a.total_money / (a.counts - a.refundCount * 2),
    2
  ) AS money_per_count,
  ROUND(
    a.total_money / a.total_num,
    2
  ) AS per_price,
  a.counts - a.refundCount * 2 as total_count
FROM
  (
    SELECT
      oo.orgcode,
      oo.org_name,
      ocvt.vice_no,
      ocvt.truck_no,
      ocv.driver_name,
      count(*) AS counts,
      SUM(ocvt.trade_num) AS total_num,
      SUM(ocvt.trade_money) AS total_money,
      SUM(
            CASE
            WHEN ocvt.trade_money < 0 THEN
                1
            ELSE
                0
            END
        ) AS refundCount
    FROM
      oil_card_vice_trades ocvt
    LEFT JOIN oil_org oo ON ocvt.org_id = oo.id
    LEFT JOIN oil_card_vice ocv ON ocvt.vice_no = ocv.vice_no
    WHERE
      oo.orgcode LIKE '" . $orgcode . "%'
    AND ocvt.trade_time > '" . $firstday . "' AND ocvt.trade_time <= '" . $lastday . "'
    AND trade_type IN (" . $oilTradeType . ")
    AND oo.is_del = 0
    AND (ocvt.truck_no IS NULL OR ocvt.truck_no = '')
    GROUP BY
      ocvt.truck_no
  ) a " . $where;
        //按车牌号和机构分组统计
        $sql2 = "SELECT
      a.orgcode,
      a.org_name,
      a.vice_no,
      a.truck_no,
      a.driver_name,
      a.counts,
      a.total_num,
      a.total_money, 
      a.refundCount, 
  ROUND(
    a.total_num / (a.counts - a.refundCount * 2),
    2
  ) AS oil_per_count,
  ROUND(
    a.total_money / (a.counts - a.refundCount * 2),
    2
  ) AS money_per_count,
  ROUND(
    a.total_money / a.total_num,
    2
  ) AS per_price,
  a.counts - a.refundCount * 2 as total_count
FROM
  (
    SELECT
      oo.orgcode,
      oo.org_name,
      ocvt.vice_no,
      ocvt.truck_no,
      ocv.driver_name,
      count(*) AS counts,
      SUM(ocvt.trade_num) AS total_num,
      SUM(ocvt.trade_money) AS total_money,
      SUM(
            CASE
            WHEN ocvt.trade_money < 0 THEN
                1
            ELSE
                0
            END
        ) AS refundCount
    FROM
      oil_card_vice_trades ocvt
    LEFT JOIN oil_org oo ON ocvt.org_id = oo.id
    LEFT JOIN oil_card_vice ocv ON ocvt.vice_no = ocv.vice_no
    WHERE
      oo.orgcode LIKE '" . $orgcode . "%'
    AND ocvt.trade_time > '" . $firstday . "' AND ocvt.trade_time <= '" . $lastday . "'
    AND trade_type IN (" . $oilTradeType . ")
    AND oo.is_del = 0
    AND ocvt.truck_no IS NOT NULL AND ocvt.truck_no != ''
    GROUP BY
      ocvt.org_id,
      ocvt.truck_no
  ) a " . $where . " " . $sortColumns . "";

        $sql = $sql1 . " UNION " . $sql2;

        $total_count = $total_num = $total_money = 0;
        $record      = Capsule::connection()->select($sql);
        if ($record) {
            foreach ($record as $k => $v) {
                $total_count += $v->total_count;
                $total_num   += $v->total_num;
                $total_money += $v->total_money;
            }
        }

        $_data = array_slice($record, ($pageNo - 1) * $pageSize, $pageSize);

        $data                  = [];
        $data['data']          = $_data;
        $data['total']         = count($record);
        $data['from']          = 1;
        $data['to']            = floor($data['total'] / $pageSize);
        $data['per_page']      = $pageSize;
        $data['current_page']  = $pageNo;
        $data['last_page']     = $data['to'];
        $data['total_count']   = $total_count > 0 ? $total_count : 0;
        $data['total_num']     = $total_num > 0 ? sprintf('%0.2f', $total_num) : 0;
        $data['total_money']   = $total_money > 0 ? sprintf('%0.2f', $total_money) : 0;
        $data['avg_total_num'] = $total_count > 0 ? sprintf('%0.2f', $total_num / $total_count) : 0;
        $data['avg_money_num'] = $total_count > 0 ? sprintf('%0.2f', $total_money / $total_count) : 0;
        $data['avg_price']     = $total_num > 0 ? sprintf('%0.2f', $total_money / $total_num) : 0;

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data['data'] = $record;
        }

        return $data;
    }

    /**
     * 根据司机姓名得到副卡号
     *
     * @param $driverName
     * @return mixed
     * <AUTHOR>
     */
    public static function getViceNoByDriverName($driverName)
    {
        return OilCardVice::where('driver_name', 'like', '%' . $driverName . '%')->pluck('vice_no')->toArray();
    }

    /**
     * 根据司机手机号查询副卡
     *
     * @param $driver_tel
     * @return mixed
     * <AUTHOR>
     */
    public static function getViceNoByDriverTel($driver_tel, $orgCode = '201MKF')
    {
        return OilCardVice::whereIn("oil_card_vice.oil_com", OilCom::getFirstList())
                          ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                          ->where('oil_org.orgcode', 'like', $orgCode . "%")
                          ->where('oil_card_vice.driver_tel', $driver_tel)
                          ->first();
    }

    /**
     * 根据车牌号得到副卡号
     *
     * @param array $truckNo
     * @return mixed
     * <AUTHOR>
     */
    public static function getViceNoByTruckNo(array $truckNo)
    {
        return OilCardVice::whereIn('truck_no', $truckNo)->pluck('vice_no')->toArray();
    }

    /**
     * 获取车牌号
     *
     * @param array $params
     * @return mixed
     * <AUTHOR>
     */
    public static function getTruckNo(array $params)
    {
        \helper::argumentCheck(['orgcode', 'truck_no'], $params);
        $orgId = OilOrg::getByOrgcodeLike($params['orgcode']);

        return OilCardViceTrades::whereIn('org_id', $orgId)->where('truck_no', 'like', '%' . $params['truck_no'] . '%')->groupBy('truck_no')->pluck('truck_no')->toArray();
    }

    /**
     * 获取司机姓名
     *
     * @param array $params
     * @return array
     * <AUTHOR>
     */
    public static function getDriverName(array $params)
    {
        \helper::argumentCheck(['orgcode', 'driver_name'], $params);
        Log::info('driver_name--' . var_export($params, TRUE), [], 'zzg');

        $orgId = OilOrg::getByOrgcodeLike($params['orgcode']);

        return OilCardVice::whereIn('org_id', $orgId)->where('driver_name', 'like', '%' . $params['driver_name'] . '%')
                          ->pluck('driver_name')->toArray();
    }

    /**
     * 获取车牌号根据卡表纬度
     *
     * @param array $params
     * @return mixed
     * <AUTHOR>
     */
    public static function getTruckNoForVice(array $params)
    {
        \helper::argumentCheck(['orgcode', 'truck_no'], $params);
        $orgId = OilOrg::getByOrgcodeLike($params['orgcode']);

        $cardTruckNo = OilCardVice::whereIn('org_id', $orgId)->where('truck_no', 'like', '%' . $params['truck_no'] . '%')->groupBy('truck_no')->pluck('truck_no')->toArray();
        $regTruckNo  = OilCardRegister::leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_register.vice_no')
                                      ->whereIn('oil_card_vice.org_id', $orgId)
                                      ->where('oil_card_register.truck_no', 'like', '%' . $params['truck_no'] . '%')
                                      ->groupBy('oil_card_register.truck_no')
                                      ->pluck('oil_card_register.truck_no')
                                      ->toArray();

        return array_unique(array_merge($cardTruckNo, $regTruckNo));
    }

    /**
     * @title    根据卡号搜索
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * mixed
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function searchViceNo(array $params)
    {
        $sqlObj = OilCardVice::leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                             ->where('oil_org.orgcode', 'like', $params['orgcode'] . '%')
                             ->select('oil_card_vice.id', 'oil_card_vice.vice_no');
        if (isset($params['viceIn']) && $params['viceIn']) {
            $sqlObj->whereIn('oil_card_vice.vice_no', $params['viceIn']);
        } else {
            $sqlObj->where('oil_card_vice.vice_no', 'like', '%' . $params['vice_no'] . '%');
        }

        return $sqlObj->get();
    }

    /**
     * @title    增量获取余额刷新时间
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * mixed
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function getIncrementByRemainGetTime(array $params)
    {
        $sqlObj = self::where('remain_get_time', '>=', $params['remain_get_time']);
        if (isset($params['id']) && $params['id']) {
            $sqlObj->where('id', '>', $params['id']);
        }

        return $sqlObj->orderBy('id', 'asc')
                      ->limit(200)
                      ->get()
                      ->toArray();
    }

    public static function execByPdo($sqlString)
    {
        return Capsule::connection()->getPdo()->exec($sqlString);
    }

    /*
     * 根据org_id获取卡片数量
     */
    public static function getCardNumByOrgId($org_id)
    {
        return self::where('org_id', $org_id)->where('status', '使用')->count();
    }

    /**
     * @title   获取卡余额
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
    public static function getCardBalance(array $params = [])
    {
        return self::select('oil_card_vice.id as vice_id', 'oil_card_vice.reserve_remain', 'oil_card_vice.comp_remain', 'oil_card_vice.card_remain',
            'oil_card_vice.point_reserve_total', 'oil_card_vice.point_remain')
//            ->leftJoin('oil_card_account', 'oil_card_account.vice_id', '=', 'oil_card_vice.id')
                   ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                   ->Filter($params)
                   ->get();
    }

    /**
     * @title   根据主卡id删除副卡
     * @desc
     * @param array $params
     * @returns
     * []
     * @returns
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     */
    public static function removeByMainIds(array $params)
    {
        \helper::argumentCheck(['mainIds'], $params);

        return self::whereIn('card_main_id', $params['mainIds'])
                   ->where('card_from', CardFrom::CUSTOMER_CARD_SIMPLE)->delete();
    }

    public static function getByThirdId($third_id)
    {
        return self::where('third_id', $third_id)->first();
    }

    /**
     * @title   提供给gsp查看油卡运营情况
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
    public static function getCardStatisticForGsp(array $params)
    {
        if (isset($params['per_page']) && intval($params['per_page'])) {
            if (intval($params['per_page']) > 1000) {
                throw new \RuntimeException('每页最大条数不能超过1000', 2);
            }
            $params['limit'] = intval($params['per_page']) > 1000 ? 1000 : intval($params['per_page']);
            $params['page']  = isset($params['page_no']) ? intval($params['page_no']) : 1;
        } else {
            $params['limit'] = 50;
            $params['page']  = 1;
        }

        $sqlObj = self::select(Capsule::connection()->raw("oil_org.orgcode,count(*) as total,oil_card_vice.oil_com"))
                      ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                      ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
                      ->where('oil_org.is_del', 0)
                      ->whereNotIn('oil_card_main.card_from', [40, 41])
                      ->where('oil_card_vice.status', '使用');

        if (isset($params['orgcodes']) && $params['orgcodes']) {
            $sqlObj->whereIn('oil_org.orgcode', explode(',', trim($params['orgcodes'])));
        } elseif (isset($params['orgcode_like']) && $params['orgcode_like']) {
            $sqlObj->where('oil_org.orgcode', 'like', $params['orgcode_like'] . '%');
        } elseif (isset($params['orgname_like']) && $params['orgname_like']) {
            $sqlObj->where('oil_org.org_name', 'like', '%' . $params['orgname_like'] . '%');
        }

        if (isset($params['oil_com']) && $params['oil_com']) {
            $sqlObj->whereIn('oil_card_vice.oil_com', explode(',', trim($params['oil_com'])));
        } else {
            $sqlObj->whereIn('oil_card_vice.oil_com', OilCom::getFanLiCalculate());
        }

        if (isset($params['start_time']) && $params['start_time']) {
            $sqlObj->where('oil_card_vice.createtime', '>=', $params['start_time']);
        }

        if (isset($params['end_time']) && $params['end_time']) {
            $sqlObj->where('oil_card_vice.createtime', '<=', $params['end_time']);
        }

        $data = $sqlObj->groupBy('oil_card_vice.org_id', 'oil_card_vice.oil_com')
                       ->paginate($params['limit'], ['*'], 'page', $params['page']);

        if (count($data) > 0) {
            foreach ($data as &$v) {
                $oilCom          = OilCom::getById($v->oil_com);
                $v->oil_com_name = $oilCom['name'];
            }
        }

        return $data;
    }

    public function getMainIdByViceNo(array $vice_nos)
    {
        return self::whereIn('vice_no', $vice_nos)->select('vice_no', 'card_main_id')->get();
    }

    //txb 根据条件获取 id
    public function getCardIds()
    {
        return OilCardVice::whereNull("cardID")
                          ->whereNotIn("card_from", [40, 41])
                          ->whereNotIn("oil_com", [4, 6, 7, 8])->select("id")->get()->toArray();
    }

    /**
     * 获取卡信息
     *
     * @param array $params
     * @return object
     */
    public function getInfoByViceNo(array $params)
    {
        \helper::argumentCheck(['vice_no'], $params);

        return self::where('vice_no', $params['vice_no'])->first();
    }

    /**
     * @title   修改卡余额
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    public function editViceCardRemain(array $params)
    {
        \helper::argumentCheck(['card_no', 'money'], $params);

        $data = self::where('vice_no', $params['card_no'])->first();

        $res = $data->update(['card_remain' => $data->card_remain + $params['money']]);

        //push至Gos系统
        CardViceToGos::batchUpdateToGos([$params['card_no']]);

        return $res;
    }

    /**
     * 查找及更新
     */
    public static function updateOrAdd(array $params, $values)
    {
        \helper::argumentCheck(['vice_no'], $params);
        return self::updateOrCreate($params, $values);
    }

    /**
     * 得到开通壹号卡的机构
     */
    public static function getOrgIds(array $condition)
    {
        return self::whereIn("oil_card_vice.oil_com", OilCom::getAllFirstList())
                   ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                   ->select("oil_org.id", "oil_org.orgcode")
                   ->where("oil_card_vice.card_from", CardFrom::GAS_CARD)
                   ->where("oil_org.is_del", 0)
                   ->where($condition)
                   ->orderBy("oil_card_vice.createtime", "desc")
                   ->groupBy("oil_card_vice.org_id")
                   ->get();
    }

    /**
     * 得到开通壹号卡的机构及现金账户
     */
    public static function getOrgCashAccountNo(array $condition)
    {
        //Capsule::connection()->enableQueryLog();
        $data = self::whereIn("oil_card_vice.oil_com", OilCom::getAllFirstList())
                    ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                    ->leftJoin('oil_account_money', 'oil_account_money.org_id', '=', 'oil_org.id')
                    ->select("oil_org.id", "oil_org.orgcode", 'oil_account_money.account_no', 'oil_account_money.id as CashId')
                    ->where("oil_card_vice.card_from", CardFrom::GAS_CARD)
                    ->where($condition)
                    ->where("oil_org.is_del", 0)
            #->where('oil_card_vice.isreceive',1)
                    ->groupBy("oil_org.id")
                    ->orderBy("oil_card_vice.createtime", "desc")
                    ->get();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }


    /**
     * 得到开通壹号卡的机构或积分账户
     */
    public static function getOrgPointAccountNo(array $condition)
    {
        //Capsule::connection()->enableQueryLog();
        $data = self::whereIn("oil_card_vice.oil_com", OilCom::getAllFirstList())
                    ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                    ->leftJoin('oil_account_jifen', 'oil_org.id', '=', 'oil_account_jifen.org_id')
                    ->select("oil_org.id", "oil_org.orgcode", "oil_account_jifen.account_no", "oil_account_jifen.id as PointId")
                    ->where("oil_card_vice.card_from", CardFrom::GAS_CARD)
                    ->where("oil_org.is_del", 0)
                    ->where($condition)
            #->where('oil_card_vice.isreceive',1)
                    ->groupBy("oil_org.id")
                    ->orderBy("oil_card_vice.createtime", "desc")
                    ->get();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }


    /**
     * 查询没有CREDIT账户的1号充值卡
     */
    public static function getCardNoCredit($orgId, $createtime, $account_no)
    {
        //Capsule::connection()->enableQueryLog();
        $data = self::whereIn("oil_card_vice.oil_com", [OilCom::GAS_FIRST_CHARGE, OilCom::GAS_FIRST_ZBANK_CHARGE])
                    ->leftJoin('oil_card_account', 'oil_card_account.vice_id', '=', 'oil_card_vice.id')
                    ->select(Capsule::Raw("FIND_IN_SET('" . $account_no . "',GROUP_CONCAT( oil_card_account.common_account_no)) as isHas,GROUP_CONCAT( oil_card_account.subAccountType ) as accountTypes,count(oil_card_vice.id) as num,oil_card_vice.id as vice_id,oil_card_vice.vice_no"))
            #->where("oil_card_vice.card_from", CardFrom::GAS_CARD)
            #->where('oil_card_vice.org_id','=',$orgId)
                    ->whereIn('oil_card_vice.org_id', $orgId)
                    ->where('oil_card_vice.createtime', '>=', $createtime)
                    ->groupBy("oil_card_vice.vice_no")
                    ->havingRaw("isHas = 0 or isHas is NULL")
                    ->get();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }


    public static function createCardCredit($orgcode = '', $org_id = '', $operTime)
    {
        try {
            $conditionCredit = [];
            if (!empty($org_id)) {
                $conditionCredit = ["oil_org.id" => $org_id];
            }
            $creditList = OilCreditAccount::getCreditAccountList(\Fuel\Defines\CreditProvider::getProductName(), $conditionCredit);
            Log::error("Org -- creditList" . var_export($creditList->toArray(), TRUE), [$orgcode], "historyCreditCardAccount_");
            if (count($creditList) == 0) {
                return TRUE;
            }
            $orgIds      = OilOrg::getByOrgcodeLike($orgcode);
            $accountList = [];
            foreach ($creditList as $_one) {
                $cardList = self::getCardNoCredit($orgIds, $operTime, $_one->account_no);
                Log::error("noCreditCardList" . var_export($cardList->toArray(), TRUE), [$orgcode], "historyCreditCardAccount_");
                if (count($cardList) > 0) {
                    foreach ($cardList as $_val) {
                        $accountList[$_one->account_no][] = $_val->vice_id;
                    }
                }
                /*foreach ($cardList as $_val) {
                    $_key = $_one->account_no . '#' . $_val->vice_id;
                    if (!array_key_exists($_key, $creditId)) {
                        $condition['vice_id']           = $_val->vice_id;
                        $condition['subAccountType']    = "CREDIT";
                        $condition['common_account_no'] = $_one->account_no;
                        $isHas                          = OilCardAccount::getByUniqueLock($condition);
                        if (!$isHas) {
                            $creditId[$_key]                  = ["vice_id" => $_val->vice_id, "account_no" => $_one->account_no];
                            $accountList[$_one->account_no][] = $_val->vice_id;
                        }
                    }
                }*/
            }
            if (count($accountList) > 0) {
                return CardAccountStock::addAccount($accountList, $orgcode);
            }
        } catch (\Exception $e) {
            Log::error("error:" . var_export($e->getMessage(), TRUE), [], "historyCreditCardAccount_");
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }


    /*
     * 每10分钟一同步gas卡余额
     */
    public static function syncGasCardBalance()
    {
        $cardLimit = 300;
        //获取1号充值卡
        $viceData = self::where('oil_com', 20)->where('status', '使用')->get();

        if ($viceData) {
            $viceArr = [];
            foreach ($viceData as $v) {
                $viceArr[] = $v->vice_no;
            }

            Log::error('$viceArr count' . var_export(count($viceArr), TRUE), [], 'balanceRefreshForGas');

            if (count($viceArr) >= $cardLimit) {
                $newViceArr = array_chunk($viceArr, $cardLimit);
                foreach ($newViceArr as $newArr) {
                    self::balanceRefreshForGas($newArr);
                }
            } else {
                self::balanceRefreshForGas($viceArr);
            }

        }

        return TRUE;
    }

    public static function getByViceNosForMove1Card(array $viceNos = [])
    {
        return OilCardVice::whereIn("vice_no", $viceNos)
                          ->select("id as vice_id", "vice_no", "oil_com", "card_main_id", "card_remain", "oil_balance")
                          ->get();
    }

    //获取机构下的卡id
    public static function getCardIdByOrgId($orgid, $oilComIn = [])
    {
        $sqlobj = OilCardVice::where("org_id", $orgid);
        if (count($oilComIn) > 0) {
            $sqlobj->whereIn("oil_com", $oilComIn);
        }
        return $sqlobj->pluck("id");
    }

    //获取卡累计加油量
    public static function getCardOilLimit($vice_no)
    {
        //todo 获取卡已消费的额度
        //todo 获取卡冻结的额度
        $condition['vice_no'] = $vice_no;
        //日加油量
        $condition['tradetimeGe'] = date("Y-m-d", time());
        $condition['tradetimeLe'] = $condition['tradetimeGe'];
        $dayTop                   = OilCardViceTrades::getSumTradeNum($condition);

        $condition['is_payNeq'] = 1;
        $dayFrozen              = OilCardViceTradesZBank::getSumTradeNum($condition);

        $condition['tradetimeGe'] = date("Y-m-1", time());
        $condition['tradetimeLe'] = date("Y-m-t", time());
        $monthTop                 = OilCardViceTrades::getSumTradeNum($condition);

        $monthFrozen = OilCardViceTradesZBank::getSumTradeNum($condition);
        return ["day_top" => $dayTop + $dayFrozen, "month_top" => $monthTop + $monthFrozen];
    }

    //为oilwechat,批量校验卡信用账户状态
    public static function batchCheckCreditCard(array $params)
    {
        //$cardInfo = (new OilCardVice)->getInfoByViceNo(['vice_no'=>$params[0]]);
        //$creditInfo = OilCreditAccount::getCreditAccount($cardInfo->org_id,"");
        $condition['accountTypeIn'] = ["CREDIT"];
        $condition['cardViceIn']    = $params;
        $accountInfo                = OilCardAccount::getCardListByOrg($condition);

        $result = [];
        if (count($accountInfo) > 0) {
            foreach ($accountInfo as $item) {
                if (in_array($item->vice_no, $params) && !empty($item->common_account_no)) {
                    $creditInfo = OilCreditAccount::getByAccountNoWithProvider($item->common_account_no);

                    if ($creditInfo->status == 10 && $creditInfo->CreditProvider->status == 10) {
                        $result[$item->vice_no]['amount']                                += $item->amount > 0 ? $item->amount : 0;
                        $result[$item->vice_no]['creditStatus'][$item->cardSubAccountID] = 10;
                    } else {
                        $result[$item->vice_no]['amount']                                = 0;
                        $result[$item->vice_no]['creditStatus'][$item->cardSubAccountID] = 20;
                    }
                }
            }
        }
        return $result;
    }

    //验证众邦卡卡账户状态
    public static function checkCardStatus(array $params = [], $accountID = NULL)
    {
        $viceInfo       = self::getByViceNoForLock($params);
        $back['status'] = '异常';
        if (count($viceInfo) == 0) {
            Log::error("result:卡不存在", [$params], "checkStatus_");
            return $back;
        }
        if (!in_array($viceInfo->oil_com, OilCom::getZBankFirstList())) {
            Log::error("result:非众邦卡", [$params], "checkStatus_");
            return $back;
        }
        $cardAccount = OilCardAccount::getCardAccount(['vice_id' => $viceInfo->id]);
        if (count($cardAccount) == 0) {
            Log::error("result:卡账户不存在", [$params], "checkStatus_");
            return $back;
        }
        if (empty($cardAccount->cardSubAccountID)) {
            Log::error("result:卡账户id不存在", [$params], "checkStatus_");
            return $back;
        }
        /*if(empty($cardAccount->cardID)){
            Log::error("result:卡id不存在",[$params],"checkStatus_");
            return $back;
        }*/
        $subAccountID = $cardAccount->cardSubAccountID;
        if ($viceInfo->oil_com == OilCom::GAS_FIRST_ZBANK_TALLY) {
            $subAccountID = $accountID;
        }
        $accountInfo = (new \Fuel\Service\AccountCenter\AccountService())->getZBankAccountInfo(['subAccountID' => $subAccountID, "one" => 1]);
        Log::error("result:卡账户信息" . var_export($accountInfo, TRUE), [], "checkStatus_");
        if (count($accountInfo) == 0) {
            return $back;
        }
        if ($accountInfo) {
            if ($accountInfo->restCreditAmount < 0) {
                return $back;
            }
            if ($accountInfo->status == 'OVERDUE') {
                $back['status'] = '逾期';
            } elseif ($accountInfo->status == 'FROZEN') {
                $back['status'] = '冻结';
            } else {
                $back['status'] = '正常';
            }
            $back['info']        = $accountInfo;
            $back['accountInfo'] = $cardAccount;
        }
        return $back;
    }

    public static function getViceNoList($params)
    {
        if (isset($params['oil_com']) && is_array($params['oil_com'])) {
            $oilComIn = $params['oil_com'] ? $params['oil_com'] : [1, 2, 3, 50, 52];
        } elseif (isset($params['oil_com']) && !is_array($params['oil_com'])) {
            $oilComIn = $params['oil_com'] ? explode(",", $params['oil_com']) : [1, 2, 3, 50, 52];
        } else {
            $oilComIn = [1, 2, 3, 50, 52];
        }

        if (isset($params['pageNo']) && $params['pageNo'] != '') {
            $page = intval($params['pageNo']) > 0 ? $params['pageNo'] - 1 : 0;
        } else {
            throw new \RuntimeException('page不能为空', 2);
        }

        $limit = isset($params['pageSize']) && intval($params['pageSize']) > 0 ? intval($params['pageSize']) : 300;

        //Capsule::connection()->enableQueryLog();
        $sqlObj = self::select('id', 'vice_no', 'assign_time')
                      ->whereNotIN('card_from', [40, 41])
                      ->whereIn("oil_com", $oilComIn);

        if (isset($params['start_remain_syn_time']) && $params['start_remain_syn_time']) {
            $sqlObj->where('remain_syn_time', '>', $params['start_remain_syn_time']);
        }

        if (isset($params['end_remain_syn_time']) && $params['end_remain_syn_time']) {
            $sqlObj->where('remain_syn_time', '<=', $params['end_remain_syn_time']);
        }

        if (isset($params['vice_no']) && $params['vice_no']) {
            $sqlObj->where('vice_no', $params['vice_no']);
        }

        if (isset($params['gt_id']) && $params['gt_id']) {
            $sqlObj->where('id', '>=', $params['gt_id']);
        }

        if (isset($params['lt_id']) && $params['lt_id']) {
            $sqlObj->where('id', '<', $params['lt_id']);
        }

        $list = $sqlObj
            ->orderBy('remain_syn_time', 'asc')
            ->orderBy('id', 'desc')
            ->skip($page * $limit)
            ->take($limit)->get();

        //Log::error('$SQL:' . var_export(Capsule::connection()->getQueryLog(), TRUE), [], 'balanceRefreshTaskNew-sql');


        return $list;
    }

    public static function oneShareCardDeductionType($params)
    {
        $listArr = [
            10 => '现金帐户',
        ];

        //查询机构是否开通G7宝理，G7免息授信产品
        $creditAccount = \Models\OilCreditAccount::leftJoin('oil_credit_provider', 'oil_credit_account.credit_provider_id', '=', 'oil_credit_provider.id')
                                                 ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_credit_account.org_id')
                                                 ->whereIn('oil_credit_provider.name', ['G7免息', 'G7保理'])
                                                 ->where('oil_org.orgcode', $params['orgcode'])
                                                 ->where('oil_credit_provider.status', 10)
                                                 ->where('oil_credit_account.status', 10)
                                                 ->pluck('oil_credit_provider.name')->toArray();


        if (in_array('G7免息', $creditAccount)) {
            $listArr[20] = 'G7免息';
        }

        if (in_array('G7保理', $creditAccount)) {
            $listArr[20] = 'G7保理';
        }

        return $listArr;
    }

    /**
     * 根据手机号或车牌号探测是否已存在
     *
     * @param $params
     * @return mixed
     */
    public static function checkCardExist($params)
    {
        if ((!isset($params['orgcode']) || !$params['orgcode'])
            && (!isset($params['driver_tel_in']) || !$params['driver_tel_in'])
            && (!isset($params['truck_no_in']) || !$params['truck_no_in'])) {
            throw new \RuntimeException('必要参数缺失', 2);
        }

        $sqlObj = self::leftJoin("oil_org", "oil_org.id", "=", "oil_card_vice.org_id")
                      ->where("oil_org.orgcode", "like", $params['orgcode'] . "%")
                      ->where('oil_org.is_del', 0)
                      ->orderBy('id', 'desc');
        if (isset($params['driver_tel_in']) && $params['driver_tel_in']) {
            if (!is_array($params['driver_tel_in'])) {
                $params['driver_tel_in'] = explode(',', $params['driver_tel_in']);
            }

            $sqlObj->whereIn('driver_tel', $params['driver_tel_in']);
        }

        if (isset($params['truck_no_in']) && $params['truck_no_in']) {
            if (!is_array($params['truck_no_in'])) {
                $params['truck_no_in'] = explode(',', $params['truck_no_in']);
            }

            $sqlObj->whereIn('truck_no', $params['truck_no_in']);
        }

        if (isset($params['card_from']) && $params['card_from'] != '') {
            $sqlObj->where('card_from', $params['card_from']);
        }

        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $sqlObj->where('oil_com', $params['oil_com']);
        }

        if (isset($params['card_level']) && $params['card_level'] != '') {
            $sqlObj->where('card_level', $params['card_level']);
        }

        return $sqlObj->select('oil_card_vice.id', 'oil_card_vice.card_from', 'oil_card_vice.vice_no',
            'oil_card_vice.oil_com', 'oil_card_vice.card_level', 'oil_card_vice.status', 'oil_card_vice.truck_no',
            'oil_card_vice.driver_tel')
                      ->get();
    }

    /*
     * 修改卡的扣款账号以及卡的当前使用
     */
    public static function updateCardAccount($vice_no, $phone, $uData)
    {
        return self::where('vice_no', $vice_no)
                   ->where('driver_tel', $phone)
                   ->update($uData);
    }

    /*
     * 批量后卡其他统一变为未使用
     */
    public static function updateOtherCardAccount($vice_no, $phone)
    {
        $obj = self::where('vice_no', '!=', $vice_no);

        if (is_array($phone) && count($phone) > 0) {
            $obj->whereIn('driver_tel', $phone);
        } else {
            $obj->where('driver_tel', $phone);
        }
        return $obj->update([
            'is_use' => 1,
        ]);
    }

    /*
     * 根据condition 来查找
     */
    public static function getByCondition($condition)
    {
        return self::where($condition)->get();
    }

    //组装卡信息
    public static function packCardInfo($val, $orgInfo, $mainNo)
    {
        $nowtime    = \helper::nowTime();
        $oil_com    = substr($val->card_no, 0, 1) == 8 ? 21 : 20;
        $status_txt = \Fuel\Defines\ViceCardStatus::getStatusName($val->status);
        $viceInfo   = [
            'org_id'                 => $orgInfo->id,
            'org_id_fanli'           => $val->org_id_fanli,
            'card_remain'            => $val->card_remain ? $val->card_remain : 0,
            'oil_balance'            => $val->oil_balance ? $val->oil_balance : 0,
            'card_from'              => 30,
            'truck_no'               => $val->truck_no,
            'card_owner'             => $val->drivername,
            'driver_name'            => $val->drivername != '' ? $val->drivername : NULL,
            'driver_tel'             => $val->driverphone,
            'vice_no'                => $val->card_no,
            'vice_password'          => $val->password,
            'remark'                 => $val->remark,
            'paylimit'               => isset($val->paylimit) ? $val->paylimit : 1,
            'oil_top'                => $val->oil_top,
            'day_top'                => $val->day_top,
            'oil_com'                => $oil_com,//油卡类型
            'card_main_id'           => $mainNo[$oil_com],
            'creator_id'             => 8888,
            "last_operator"          => '系统自动',
            'unit'                   => $val->unit == 2 ? 2 : 1,
            'createtime'             => $nowtime,
            'updatetime'             => $nowtime,
            'active_time'            => $nowtime,
            'isreceive'              => $val->isreceive == 1 ? 1 : 2,
            'status'                 => $status_txt ? $status_txt : '使用',
            'is_use'                 => CardViceApp::getCardIsUse($val->driver_tel), //默认使用
            'deduction_account_no'   => NULL,
            'deduction_account_name' => NULL,
            'ischeck'                => $val->ischeck ? $val->ischeck : 4,
            'card_level'             => $val->card_level == 2 ? 2 : 1, //车辆卡标识
            'check_truckno'          => $val->check_truckno == 1 ? 1 : 2, //是否车牌一致
            'limit_type'             => $val->limit_type ? $val->limit_type : 1, //车辆卡限制类型
            'month_top'              => $val->month_top ? $val->month_top : 200000,
            'able_transfer'          => $val->able_transfer == 1 ? 1 : 2,
        ];
        if ($oil_com == OilCom::GAS_FIRST_TALLY) {
            $viceInfo['oil_balance'] = 0;
            $viceInfo['card_remain'] = 0;
            $deductionAccount        = \Models\OilAccountMoney::getByOrgId(['org_id' => $orgInfo->id]);
            if ($deductionAccount && isset($deductionAccount->account_no) && $deductionAccount->account_no) {
                $viceInfo['deduction_account_no']   = $deductionAccount->account_no;
                $viceInfo['deduction_account_name'] = ConsumeType::FIRST_TALLY_ACCOUNT_NAME;
            }
        }
        $viceInfo['bind_status'] = $val->truck_no ? 1 : 0;
        return $viceInfo;
    }

    //根据条件获取卡号累计消费
    public static function getSumTrade($params)
    {
        \helper::argumentCheck(['vice_no', 'limitType'], $params);
        if ($params['limitType'] == 1) { //日
            $start_time = date('Y-m-d') . ' 00:00:00';
            $end_time   = date("Y-m-d 00:00:00", strtotime("+1day", strtotime($start_time)));
        }
        if ($params['limitType'] == 2) { //月
            $start_time = date('Y-m') . '-01 00:00:00';
            $end_time   = date("Y-m-d 00:00:00", strtotime("+1month", strtotime($start_time)));
        }
        $connection = "online_only_read";
        if (API_ENV == "dev") {
            $connection = "";
        }
        //Capsule::connection($connection)->enableQueryLog();
        $totalMoney = Capsule::connection($connection)
                             ->table('oil_card_vice_trades')
                             ->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`idx_vice_no_createtime`)'))
                             ->where('vice_no', $params['vice_no'])
                             ->whereNull('cancel_sn') //排除掉撤销的订单
                             ->where('createtime', '>=', $start_time)
                             ->where('createtime', "<", $end_time)
                             ->sum('trade_money');
        //$sql = Capsule::connection($connection)->getQueryLog();
        //print_r($sql);exit;
        return $totalMoney ? $totalMoney : 0;
    }

    /**
     * 更新卡余额
     *
     * @param        $viceNo
     * @param        $money
     * @param string $op
     * @return int
     */
    public static function updateRemainByViceNo($viceNo, $money, $op = '+')
    {
        $sql = " UPDATE `oil_card_vice` SET card_remain=card_remain $op $money  WHERE vice_no = $viceNo ";

        if ($op == '-') {
            $sql .= "AND card_remain >= $money";
        }

        return Capsule::connection()->getPdo()->exec($sql);
    }

    /**
     * 取字段值 返回第一个值
     *
     * @param array $params
     * @param       $pluckField
     * @return mixed
     */
    public static function getResField(array $params, $pluckField)
    {
        $res = self::getPluckFields($params, $pluckField);

        return !empty($res[0]) ? $res[0] : '';
    }

    /**
     * 取字段值
     *
     * @param array $params
     * @param       $pluckField
     * @return array
     */
    public static function getPluckFields(array $params, $pluckField)
    {
        $res = OilCardVice::Filter($params)->pluck($pluckField);

        return !$res ? [] : $res->toArray();
    }

    /**
     * 根据条件取信息
     *
     * @param array $params
     * @return OilCardVice
     */
    public static function getInfoByFilter(array $params)
    {
        return OilCardVice::Filter($params)->first();
    }

    /**
     * @param $params
     * @return array|\Illuminate\Database\Query\Builder[]
     */
    public static function getViceByOrgCodeAndIds($params)
    {
        $result = Capsule::connection()->table("oil_card_vice")->select("oil_card_vice.*")->leftJoin("oil_org", "oil_org.id", "=", "oil_card_vice.org_id")
                         ->where("oil_org.orgcode", "like", $params['orgcode'] . "%")
                         ->whereIn("oil_card_vice.card_from", [40, 41])
                         ->whereIn('oil_card_vice.id', explode(",", $params['ids']))
                         ->get();

        return $result;
    }

    /**
     * @param $ids
     * @param $updateParams
     * @return mixed
     */
    public static function updateByIds($ids, $updateParams)
    {
        return self::whereIn('id', $ids)->update($updateParams);
    }

    /**
     * 根据ID更新卡余额
     *
     * @param        $id
     * @param        $money
     * @param string $op
     * @return int
     */
    public static function updateRemainById($id, $money, $op = '+')
    {
        $sql = "UPDATE `oil_card_vice` SET card_remain=card_remain $op $money  WHERE id = $id ";

        if ($op == '-') {
            $sql .= "AND card_remain >= $money";
        }

        return Capsule::connection()->getPdo()->exec($sql);
    }

    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return self::Filter($params)->count();
    }

    static public function getSumMoney($params = [])
    {
        //Capsule::connection()->enableQueryLog();
        $data =  self::Filter($params)->selectRaw('SUM(ifnull(card_remain,0) + ifnull(reserve_remain,0)) as total')->first();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data->total;
    }

    /**
     * @param $params
     * @return int
     */
    public static function getCardNumByOrgCode($params)
    {
        return Capsule::connection()->table("oil_card_vice")->leftJoin("oil_org", "oil_org.id", "=", "oil_card_vice.org_id")
            ->where("oil_org.orgcode",  $params['orgcode'])
            ->where("oil_card_vice.oil_com",  $params['oil_com'])
            ->count();
    }

    /**
     * 获取副卡信息列表
     *
     * @param array $params
     * @return array
     * <AUTHOR>
     */
    public static function getListByViceNo(array $params)
    {
        \helper::argumentCheck(['vice_no'], $params);
        $viceNo = is_array($params['vice_no']) ? $params['vice_no'] : implode(',', $params['vice_no']);
        $data = Capsule::connection()->select("SELECT ocv.id, ocv.vice_no, ocv.oil_com as vice_oil_com, oo.org_name, ocv.truck_no, ocm.main_no, ocm.oil_com, op.province,ocv.status as vice_status
                    FROM oil_card_vice ocv
                    LEFT JOIN oil_org oo ON ocv.org_id = oo.id
                    LEFT JOIN oil_card_main ocm ON ocv.card_main_id = ocm.id
                    LEFT JOIN oil_provinces op ON ocm.fanli_region = op.id
                    WHERE ocv.vice_no IN ('".implode("','",$viceNo)."')");

        return !$data ? [] : \helper::objectToArray($data);
    }

    /**
     * 根据司机手机号、机构唯一标识(id or code)、卡类型获取卡信息
     * @param $driver_tel
     * @param mixed $orgFlag
     * @param string $orgFlagType
     * @param array $oilCom
     * @return mixed
     */
    public static function getViceByDriverTelAndOrgFlagAndOilCom($driver_tel, $orgFlag = '', string $orgFlagType = 'code', array $oilCom = [], array $cardLevel = [1])
    {
        $query = OilCardVice::whereIn("oil_card_vice.oil_com", $oilCom)->whereIn("oil_card_vice.card_level", $cardLevel);
        if ($orgFlagType == 'code') {
            $query = $query->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                           ->where('oil_org.orgcode', '=', $orgFlag);
        }
        if ($orgFlagType == 'name') {
            $query = $query->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
                           ->where('oil_org.org_name', '=', $orgFlag);
        }
        if ($orgFlagType == 'id') {
            $query = $query->where('org_id', '=', $orgFlag);
        }
        return $query->where('oil_card_vice.driver_tel', $driver_tel)
              ->select('oil_card_vice.status as card_status')
              ->first();
    }
}
