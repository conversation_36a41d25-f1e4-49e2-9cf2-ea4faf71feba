<?php
/**
 * 上游转账申请
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/07/07
 * Time: 16:14:14
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilUpstreamTransferApply extends \Framework\Database\Model
{
    protected $table = 'oil_upstream_transfer_apply';

    protected $guarded = ["id"];
    protected $fillable = ['no','cooperation_type','transfer_type','amount','out_account_id','out_supplier_id','out_res_type','out_res_id','out_name','in_account_id','in_supplier_id','in_res_type','in_res_id','in_name','statistics_type','business_no','card_no','remark','audit_status','creator','auditor','operator','create_time','audit_time','update_time'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['ids']) && is_array($params['ids']) && !empty($params['ids']))
            $query->whereIn('id', $params['ids']);

        //Search By no
        if (isset($params['no']) && $params['no'] != '') {
            $query->where('no', '=', $params['no']);
        }

        //Search By transfer_type
        if (isset($params['transfer_type']) && $params['transfer_type'] != '') {
            $query->where('transfer_type', '=', $params['transfer_type']);
        }

        //Search By amount
        if (isset($params['amount']) && $params['amount'] != '') {
            $query->where('amount', '=', $params['amount']);
        }

        //Search By out_account_id
        if (isset($params['out_account_id']) && $params['out_account_id'] != '') {
            $query->where('out_account_id', '=', $params['out_account_id']);
        }

        //Search By out_supplier_id
        if (isset($params['out_supplier_id']) && $params['out_supplier_id'] != '') {
            $query->where('out_supplier_id', '=', $params['out_supplier_id']);
        }

        //Search By out_res_type
        if (isset($params['out_res_type']) && $params['out_res_type'] != '') {
            $query->where('out_res_type', '=', $params['out_res_type']);
        }

        //Search By out_res_id
        if (isset($params['out_res_id']) && $params['out_res_id'] != '') {
            $query->where('out_res_id', '=', $params['out_res_id']);
        }

        //Search By out_name
        if (isset($params['out_name']) && $params['out_name'] != '') {
            $query->where('out_name', 'like', '%'.$params['out_name'].'%');
        }

        //Search By in_account_id
        if (isset($params['in_account_id']) && $params['in_account_id'] != '') {
            $query->where('in_account_id', '=', $params['in_account_id']);
        }

        //Search By in_supplier_id
        if (isset($params['in_supplier_id']) && $params['in_supplier_id'] != '') {
            $query->where('in_supplier_id', '=', $params['in_supplier_id']);
        }

        //Search By in_res_type
        if (isset($params['in_res_type']) && $params['in_res_type'] != '') {
            $query->where('in_res_type', '=', $params['in_res_type']);
        }

        //Search By in_res_id
        if (isset($params['in_res_id']) && $params['in_res_id'] != '') {
            $query->where('in_res_id', '=', $params['in_res_id']);
        }

        //Search By in_name
        if (isset($params['in_name']) && $params['in_name'] != '') {
            $query->where('in_name', 'like', '%'.$params['in_name'].'%');
        }

        //Search By statistics_type
        if (isset($params['statistics_type']) && $params['statistics_type'] != '') {
            $query->where('statistics_type', '=', $params['statistics_type']);
        }

        //Search By business_no
        if (isset($params['business_no']) && $params['business_no'] != '') {
            $query->where('business_no', '=', $params['business_no']);
        }

        //Search By card_no
        if (isset($params['card_no']) && $params['card_no'] != '') {
            $query->where('card_no', '=', $params['card_no']);
        }

        //Search By remark
        if (isset($params['remark']) && $params['remark'] != '') {
            $query->where('remark', '=', $params['remark']);
        }

        //Search By audit_status
        if (isset($params['audit_status'])) {
            if (is_array($params['audit_status']) && !empty($params['audit_status']))
                $query->whereIn('audit_status', $params['audit_status']);
            elseif ($params['audit_status'] != '')
                $query->where('audit_status', '=', $params['audit_status']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By auditor
        if (isset($params['auditor']) && $params['auditor'] != '') {
            $query->where('auditor', '=', $params['auditor']);
        }

        //Search By operator
        if (isset($params['operator']) && $params['operator'] != '') {
            $query->where('operator', '=', $params['operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By audit_time
        if (isset($params['audit_time']) && $params['audit_time'] != '') {
            $query->where('audit_time', '=', $params['audit_time']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By createtime
        if (isset($params['createtime_s']) && $params['createtime_s'] != '') {
            $query->where('createtime', '>=', $params['createtime_s']);
        }

        //Search By createtime
        if (isset($params['createtime_e']) && $params['createtime_e'] != '') {
            $query->where('createtime', '<=', $params['createtime_e']);
        }

        //Search By updatetime
        if (isset($params['updatetime_s']) && $params['updatetime_s'] != '') {
            $query->where('updatetime', '>=', $params['updatetime_s']);
        }

        //Search By updatetime
        if (isset($params['updatetime_e']) && $params['updatetime_e'] != '') {
            $query->where('updatetime', '<=', $params['updatetime_e']);
        }

        return $query;
    }

    /**
     * 上游转账申请 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilUpstreamTransferApply::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('id', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 上游转账申请 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        $res = OilUpstreamTransferApply::find($params['id']);

        if (! empty($res)) {
            $res = $res->toArray();
        } else {
            $res = [];
        }

        return $res;
    }

    static public function batchGet(array $params, $fields=[])
    {
        \helper::argumentCheck(['ids'],$params);

        if (empty($fields)) {
            return OilUpstreamTransferApply::Filter($params)->get()->toArray();
        } else {
            return OilUpstreamTransferApply::Filter($params)->get($fields)->toArray();
        }
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilUpstreamTransferApply::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 上游转账申请 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilUpstreamTransferApply::create($params);
    }

    /**
     * 上游转账申请 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilUpstreamTransferApply::find($params['id'])->update($params);
    }

    static public function batchEdit($params, $data)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilUpstreamTransferApply::Filter($params)->update($data);
    }

    /**
     * 上游转账申请 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilUpstreamTransferApply::destroy($params['ids']);
    }

    static public function createNo($prefix)
    {
        $microTime = microtime();

        list($us, $sec) = explode(" ", $microTime);

        $str = $prefix . date("ymds", time());
        $str .= substr($us, 3, 3);
        $str .= sprintf('%02d', rand(0, 99));

        $exist = self::where("no", "=", $str)->lockForUpdate()->first();
        if ($exist) {
            return self::createNo($prefix);
        }

        return $str;
    }


}