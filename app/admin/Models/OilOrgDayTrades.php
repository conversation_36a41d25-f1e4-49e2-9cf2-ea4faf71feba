<?php
/**
 * 机构每日消费统计
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/08/14
 * Time: 11:38:17
 */

namespace Models;

use Fuel\Defines\TradesType;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgDayTrades extends \Framework\Database\Model
{
    protected $table = 'oil_org_day_trades';

    protected $guarded  = ["id"];
    protected $fillable = [
        'day','org_id','orgcode','zsy_total','zsy_money','zsy_num','zsy_count','zsh_total','zsh_money','zsh_num','zsh_count',
        'cylmk_total','cylmk_money','cylmk_num','cylmk_count','qiaopai_total','qiaopai_money','qiaopai_num','qiaopai_count',
        'zsy_tg_total','zsy_tg_money','zsy_tg_num','zsy_tg_count','zsh_tg_total','zsh_tg_money','zsh_tg_num','zsh_tg_count','sj_czk_total','sj_czk_money',
        'sj_czk_num','sj_czk_count','sj_gxk_total','sj_gxk_money','sj_gxk_num','sj_gxk_count','clk_czk_total','clk_czk_money','clk_czk_num',
        'clk_czk_count','clk_gxk_total','clk_gxk_money','clk_gxk_num','clk_gxk_count','fck_total','fck_money','fck_num','fck_count',
        'createtime', 'updatetime'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By day
        if (isset($params['day']) && $params['day'] != '') {
            $query->where('day', '=', $params['day']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }
        return $query;
    }

    /**
     * 机构每日办卡统计 列表查询
     *
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        $sqlObj          = OilOrgDayTrades::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 机构每日办卡统计 详情查询
     *
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOrgDayTrades::find($params['id']);
    }

    /**
     * 悲观锁查询
     *
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOrgDayTrades::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 机构每日办卡统计 新增
     *
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgDayTrades::create($params);
    }

    /**
     * 机构每日办卡统计 编辑
     *
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOrgDayTrades::find($params['id'])->update($params);
    }

    /**
     * 机构每日办卡统计 根据ids删除或批量删除
     *
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilOrgDayTrades::destroy($params['ids']);
    }

    /**
     * 开卡日报统计
     * @param $params
     * @return array
     */
    public static function statisticByDay($params)
    {
        $start_time = isset($params['start_time']) && $params['start_time'] ? $params['start_time'] : date("Y-m-d", strtotime('-1 day')) . ' 00:00:00';
        $end_time   = isset($params['end_time']) && $params['end_time'] ? $params['end_time'] : date("Y-m-d", strtotime('-1 day')) . ' 23:59:59';
        $sql        = "insert into oil_org_day_trades
(day,org_id,orgcode,zsy_total,zsy_count,zsy_money,zsy_num,zsh_total,zsh_count,zsh_money,zsh_num,cylmk_total,cylmk_count,cylmk_money,cylmk_num,
qiaopai_total,qiaopai_count,qiaopai_money,qiaopai_num,zsy_tg_total,zsy_tg_count,zsy_tg_money,zsy_tg_num,zsh_tg_total,zsh_tg_count,zsh_tg_money,
zsh_tg_num,sj_czk_total,sj_czk_count,sj_czk_money,sj_czk_num,sj_gxk_total,sj_gxk_count,sj_gxk_money,sj_gxk_num,clk_czk_total,clk_czk_count,clk_czk_money,
clk_czk_num,clk_gxk_total,clk_gxk_count,clk_gxk_money,clk_gxk_num,fck_total,fck_count,fck_money,fck_num,createtime)
(
SELECT
	date_format( trades.createtime, '%Y-%m-%d' ) AS day,org.id,org.orgcode,
	
	count(DISTINCT(case when vice.card_from not in (40,41) and vice.oil_com = 2 then  vice.vice_no else null end)) as zsy_total,
	count(case when vice.card_from not in (40,41) and vice.oil_com = 2 then trades.id else null end) as zsy_count,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 2 then trade_money else 0 end) as zsy_money,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 2 then trade_num else 0 end) as zsy_num,
	
	count(DISTINCT(case when vice.card_from not in (40,41) and vice.oil_com = 1 then vice.vice_no else null end)) as zsh_total,
	count(case when vice.card_from not in (40,41) and vice.oil_com = 1 then trades.id else null end) as zsh_count,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 1 then trade_money else 0 end) as zsh_money,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 1 then trade_num else 0 end) as zsh_num,
	
	count(DISTINCT(case when vice.card_from not in (40,41) and vice.oil_com = 52 then  vice.vice_no else null end)) as cylmk_total,
	count(case when vice.card_from not in (40,41) and vice.oil_com = 52 then trades.id else null end) as cylmk_count,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 52 then trade_money else 0 end) as cylmk_money,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 52 then trade_num else 0 end) as cylmk_num,
	
	count(DISTINCT(case when vice.card_from not in (40,41) and vice.oil_com = 9 then vice.vice_no else null end)) as qiaopai_total,
	count(case when vice.card_from not in (40,41) and vice.oil_com = 9 then trades.id else null end) as qiaopai_count,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 9 then trade_money else 0 end) as qiaopai_money,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 9 then trade_num else 0 end) as qiaopai_num,
	
	count(DISTINCT(case when vice.card_from in (40,41) and vice.oil_com = 2 then  vice.vice_no else null end)) as zsy_tg_total,
	count(case when vice.card_from in (40,41) and vice.oil_com = 2 then trades.id else null end) as zsy_tg_count,
	sum(case when vice.card_from in (40,41) and vice.oil_com = 2 then trade_money else 0 end) as zsy_tg_money,
	sum(case when vice.card_from in (40,41) and vice.oil_com = 2 then trade_num else 0 end) as zsy_tg_num,
	
	count(DISTINCT (case when vice.card_from in (40,41) and vice.oil_com = 1 then vice.vice_no else null end)) as zsh_tg_total,
	count(case when vice.card_from in (40,41) and vice.oil_com = 1 then trades.id else null end) as zsh_tg_count,
	sum(case when vice.card_from in (40,41) and vice.oil_com = 1 then trade_money else 0 end) as zsh_tg_money,
	sum(case when vice.card_from in (40,41) and vice.oil_com = 1 then trade_num else 0 end) as zsh_tg_num,
			
	count(DISTINCT (case when vice.card_level = 1 and vice.oil_com = 20 then vice.vice_no else NULL end)) as sj_czk_total,
	count(case when vice.card_level = 1 and vice.oil_com = 20 then trades.id else NULL end) as sj_czk_count,
	sum(case when vice.card_level = 1 and vice.oil_com = 20 then trade_money else 0 end) as sj_czk_money,
	sum(case when vice.card_level = 1 and vice.oil_com = 20 then trade_num  else 0 end) as sj_czk_num,
	
	count(DISTINCT(case when vice.card_level = 1 and vice.oil_com = 21 then vice.vice_no  else NULL  end )) as sj_gxk_total,
	count(case when vice.card_level = 1 and vice.oil_com = 21 then trades.id else NULL end ) as sj_gxk_count,
	sum(case when vice.card_level = 1 and vice.oil_com = 21 then trade_money else 0 end ) as sj_gxk_money,
	sum(case when vice.card_level = 1 and vice.oil_com = 21 then trade_num else 0 end ) as sj_gxk_num,

	count(DISTINCT(case when vice.card_level = 2 and vice.oil_com = 20 then  vice.vice_no else NULL end)) as clk_czk_total,
	count(case when vice.card_level = 2 and vice.oil_com = 20 then trades.id else null end) as clk_czk_count,
	sum(case when vice.card_level = 2 and vice.oil_com = 20 then trade_money else 0 end) as clk_czk_money,
	sum(case when vice.card_level = 2 and vice.oil_com = 20 then trade_num else 0 end) as clk_czk_num,

    count(DISTINCT(case when vice.card_level = 2 and vice.oil_com = 21 then  vice.vice_no else null end)) as clk_gxk_total,
	count(case when vice.card_level = 2 and vice.oil_com = 21 then trades.id else null end) as clk_gxk_count,
	sum(case when vice.card_level = 2 and vice.oil_com = 21 then trade_money else 0 end) as clk_gxk_money,
	sum(case when vice.card_level = 2 and vice.oil_com = 21 then trade_num else 0 end) as clk_gxk_num,

    count(DISTINCT(case when vice.card_from not in (40,41) and vice.oil_com = 30 then vice.vice_no else null end)) as fck_total,
	count(case when vice.card_from not in (40,41) and vice.oil_com = 30 then trades.id else null end) as fck_count,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 30 then trade_money else 0 end) as fck_money,
	sum(case when vice.card_from not in (40,41) and vice.oil_com = 30 then trade_num else 0 end) as fck_num,
	NOW() as createtime
FROM
	oil_card_vice_trades AS trades
	LEFT JOIN oil_org AS org ON trades.org_id = org.id
	LEFT JOIN oil_card_vice as vice on trades.vice_no = vice.vice_no
WHERE
	trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true)." ) 
	AND vice.oil_com in (1,2,52,9,20,21,30)
	AND trades.createtime >= '".$start_time."' 
	AND trades.createtime <= '".$end_time."'
GROUP BY org.orgcode,date_format( trades.createtime, '%Y-%m-%d' )
)";
        //'加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油','消费','优惠消费'
        $res = Capsule::connection('default')->select($sql);
        //设置顶级机构上,最新消费时间和累计消费卡数
        $nowDay = substr($end_time,0,10);
        self::setLatestTime($nowDay);
    }


    public static function setLatestTime($day)
    {
        $connection = "online_only_read";
        if(API_ENV == 'dev'){
            $connection = "";
        }
        $next_day = date("Y-m-d",strtotime("+1 day",strtotime($day)));
        $sql = "SELECT
	IF( LEFT ( orgcode, 6 ) = '201XW3', orgcode, LEFT ( orgcode, 6 ) ) AS orgroot,
	`day`,
	sum( CASE WHEN (LENGTH( orgcode ) = 6 or LEFT ( orgcode, 6 ) = '201XW3') THEN 1 ELSE 0 END ) is_orgroot 
FROM
	oil_org_day_trades 
WHERE
	orgcode IS NOT NULL 
	AND `day` >= '".$day."'
	and `day` < '".$next_day."'
GROUP BY
	IF( LEFT ( orgcode, 6 ) = '201XW3', orgcode, LEFT ( orgcode, 6 ) ),
	`day`";
        //防止只读库没有数据,使用主库
        $list = Capsule::connection("")->select($sql);
        if( count($list) > 0 ){
            foreach ($list as $_item){
                $result = self::getTrades($_item);
                if($_item->is_orgroot == 1){
                    if(strlen($result->createtime) > 0) {
                        $updateSql = "update oil_org_day_trades set orgroot_card_total = ".$result->total.",orgroot_entity_card_total = ".$result->entity_card_total.",
orgroot_electron_card_total = ".$result->electron_card_total.",latest_time = '".$result->createtime."', updatetime = '".\helper::nowTime()."'
 where orgcode= '".$_item->orgroot."' and `day` = '".$_item->day."' limit 1";
                    }else{
                        $updateSql = "update oil_org_day_trades set orgroot_card_total = ".$result->total.",orgroot_entity_card_total = ".$result->entity_card_total.",
orgroot_electron_card_total = ".$result->electron_card_total.",latest_time = null, updatetime = '".\helper::nowTime()."'
 where orgcode= '".$_item->orgroot."' and `day` = '".$_item->day."' limit 1";
                    }
                    Capsule::connection("")->getPdo()->exec($updateSql);
                }else{
                    $orgInfo = OilOrg::getByOrgcode($_item->orgroot);
                    if(strlen($result->createtime) > 0) {
                        $addSql = "insert into oil_org_day_trades 
(day, latest_time,org_id, orgcode, orgroot_card_total,orgroot_entity_card_total,orgroot_electron_card_total,createtime) 
values ('" . $_item->day . "','" . $result->createtime . "'," . $orgInfo->id . ",'" . $_item->orgroot . "'," . $result->total . "," . $result->entity_card_total .
                            "," . $result->electron_card_total . ",'" . \helper::nowTime() . "')";
                    }else{
                        $addSql = "insert into oil_org_day_trades 
(day, latest_time,org_id, orgcode, orgroot_card_total,orgroot_entity_card_total,orgroot_electron_card_total,createtime) 
values ('" . $_item->day . "',null," . $orgInfo->id . ",'" . $_item->orgroot . "'," . $result->total . "," . $result->entity_card_total .
                            "," . $result->electron_card_total . ",'" . \helper::nowTime() . "')";
                    }
                    Capsule::connection("")->getPdo()->exec($addSql);
                }
            }
        }
    }

    static public function getTrades( $params )
    {
        $connection = "online_only_read";
        if(API_ENV == 'dev'){
            $connection = "";
        }
        $sql = "SELECT
	count( DISTINCT vice_no ) AS total,
	max( a.createtime ) AS createtime,
	count(
	DISTINCT ( CASE WHEN a.oil_com IN ( 1, 2, 52, 9 ) THEN a.vice_no ELSE NULL END )) AS entity_card_total,
	count(
	DISTINCT ( CASE WHEN a.oil_com IN ( 20, 21, 30 ) THEN a.vice_no ELSE NULL END )) AS electron_card_total 
FROM
	oil_card_vice_trades a
	LEFT JOIN oil_org b ON a.org_id = b.id 
WHERE
	a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true)." ) 
	AND a.card_from NOT IN ( 40, 41 ) 
	AND a.oil_com IN ( 1, 2, 52, 9, 20, 21, 30 )
	AND a.createtime <= '".$params->day." 23:59:59' 
	AND b.orgcode LIKE '".$params->orgroot."%'";
        //'加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油','消费','优惠消费'
        $list = Capsule::connection($connection)->select($sql);
        if($list && count($list) > 0) {
            return $list[0];
        }else{
            $res = new \stdClass();
            $res->total = 0;
            $res->createtime = null;
            $res->entity_card_total = 0;
            $res->electron_card_total = 0;
            return $res;
        }
    }

    /**
     * 根据交易地点，获取时间
     */
    static public function getTimeByTradePlace($trade_place = [])
    {
        $connection = "online_only_read";
        if(API_ENV == 'dev'){
            $connection = "";
        }

        $sql = "SELECT
    a.trade_place,
	max( a.createtime ) AS latesttime,
	min( a.createtime ) AS firsttime
FROM
	oil_card_vice_trades a
WHERE
	a.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true)." ) 
	AND a.card_from NOT IN ( 40, 41 ) 
	AND a.oil_com IN ( 1, 2, 52, 9, 20, 21, 30 )
	and a.trade_place in ('".implode("','",$trade_place)."') group by trade_place";
        $list = Capsule::connection($connection)->select($sql);
        return $list;
    }
    // '加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油','消费','优惠消费'
}