<?php
/**
 * 字典
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/03/27
 * Time: 19:39:20
 */

namespace Models;

use Framework\Helper;
use GosSDK\Gos;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilDict extends \Framework\Database\Model
{
    const CommodityType = '837db89a-31b8-11e8-8008-28d24462ff5a';
    
    protected $table = 'oil_dict';
    
    public $incrementing = FALSE;
    
    protected $guarded  = ['id'];
    protected $fillable = ['id', 'pid', 'code', 'name', 'status', 'createtime', 'updatetime'];
    
    public function getFillAble()
    {
        return $this->fillable;
    }
    
    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }
        
        //Search By pid
        if (isset($params['pid']) && $params['pid'] != '') {
            $query->where('pid', '=', $params['pid']);
        }
        
        //Search By code
        if (isset($params['code']) && $params['code'] != '') {
            $query->where('code', '=', $params['code']);
        }
        
        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }
        
        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }
        
        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }
        
        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }
        
        return $query;
    }
    
    /**
     * 字典 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilDict::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        
        return $data;
    }
    
    /**
     * 字典 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilDict::find($params['id']);
    }
    
    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilDict::lockForUpdate()->where('id', $params['id'])->first();
    }
    
    /**
     * 字典 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['id'] = Helper::uuid();
        
        return OilDict::create($params);
    }
    
    /**
     * 字典 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);
        
        return OilDict::find($params['id'])->update($params);
    }
    
    /**
     * 字典 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        
        return OilDict::destroy($params['ids']);
    }
    
    static public function getByPidAndName(array $params)
    {
        return self::where('pid', $params['pid'])->where('name', trim($params['name']))->first();
    }
    
    static public function getByPid($pid)
    {
        return self::where('pid', $pid)->where('status', 1)->get();
    }
    
    static public function getCommodityType()
    {
        return self::getByPid(self::CommodityType);
    }
    
    static public function getByType($type)
    {
        $data = (new Gos())
            ->setRequestType('GET')
            ->setMethod('v1/dict/getList')
            ->setParams(['type' => $type, '_export' => 1])
            ->sync();
        
        return $data;
    }
}