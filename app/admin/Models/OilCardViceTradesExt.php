<?php
/**
 * oil_card_vice_trades_ext
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/02/18
 * Time: 17:17:33
 */

namespace Models;

use Framework\Log;
use Fuel\Defines\CardTradeConf;
use Fuel\Defines\CardViceBillConf;
use Fuel\Defines\CardViceConf;
use Fuel\Defines\ChargeOffStatus;
use Fuel\Defines\IsFinal;
use Fuel\Defines\PcodeConf;
use Fuel\Defines\StationOperatorsConf;
use Fuel\Defines\TradesType;
use Fuel\Service\CardViceBill;
use Fuel\Service\OilTypeService;
use Fuel\Service\TradeReceiptInvoiceRed;
use Illuminate\Database\Capsule\Manager as Capsule;

/**
 * Class OilCardViceTradesExt
 * @package Models
 * @property $charge_off_status
 * @property $can_invoice
 * @property $upstream_settle_price
 * @property $upstream_settle_money
 * @property $mac_price
 * @property $mac_amount
 * @property $real_oil_num
 * @property $is_final
 * @property $plummet_rebate_money
 * @property $later_rebate_money
 */
class OilCardViceTradesExt extends \Framework\Database\Model
{
    protected $table = 'oil_card_vice_trades_ext';

    protected $guarded  = ["id"];
    protected $fillable = [
        'trades_id', 'supplier_id', 'area_id', 'is_proxy', 'merchant_id', 'merchant_name', 'createtime',
        'aplownerid', 'aplowneridname', 'sale_email', 'oil_centercode', 'oil_centername', 'org_id_fanli',
        'fanli_orgcode', 'fanli_org_name','data_from','real_oil_num', 'mac_price', 'mac_amount', 'charge_off_status', 'can_invoice',
        'upstream_settle_price', 'upstream_settle_money', 'plummet_rebate_money',
        'later_rebate_money','final_plummet_rebate_money','final_later_rebate_money', 'cal_cost_price', 'cal_cost_money',
        'is_final','operator_id','original_order_id','document_type','ocr_truck_no_url'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By trades_id
        if (isset($params['trades_id'])) {
            if (is_array($params['trades_id']) && !empty($params['trades_id'])) {
                $query->whereIn('trades_id', $params['trades_id']);
            } elseif ($params['trades_id'] != '') {
                $query->where('trades_id', '=', $params['trades_id']);
            }
        }

        //Search By merchant_id
        if (isset($params['merchant_id']) && $params['merchant_id'] != '') {
            $query->where('merchant_id', '=', $params['merchant_id']);
        }

        //Search By merchant_name
        if (isset($params['merchant_name']) && $params['merchant_name'] != '') {
            $query->where('merchant_name', '=', $params['merchant_name']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        return $query;
    }

    /**
     * oil_card_vice_trades_ext 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardViceTradesExt::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 初始化核销状态、可开票状态
     * @param $param array
     * @param $trade array
     */
    protected static function initChargeOffAndCanInvoice(&$param, $trade, $consumeTradeExt=null)
    {
        if (empty($trade))
            return;
        if (in_array($trade['card_from'], [40, 41]) || in_array($trade['oil_com'], [6,7,8])) {
            $param['can_invoice'] = CardTradeConf::CAN_INVOICE_NO;
            return;
        }
        if (empty($trade['pcode']))
            return;
        $operator = OilStationOperators::Filter(['operators_code' => $trade['pcode']])->first();
        if (empty($operator))
            return;
        if (! empty($trade['cancel_sn']) && substr($trade['api_id'], 0, 2) == 'd_') {
            $tradeId = substr($trade['cancel_sn'], 2);
            $sTrade = self::getOneInfo(['trades_id' => $tradeId]);
            $param['charge_off_status'] = $sTrade->charge_off_status;
            $param['can_invoice'] = $sTrade->can_invoice;
            return;
        }
        if ($operator->is_second_check == StationOperatorsConf::IS_SECOND_CHECK_YES) {
            $param['charge_off_status'] = ChargeOffStatus::NO_CHARGE_OFF;
            if ($operator->no_used_order_invoice == StationOperatorsConf::NO_USED_ORDER_INVOICE_NO) {
                $param['can_invoice'] = CardTradeConf::CAN_INVOICE_NO;
            }
        }
    }

    /**
     * 是否为撤销消费
     * @param $trade
     * @return bool
     */
    static public function isTradeCancel($trade)
    {
        return !empty($trade['cancel_sn']) && substr($trade['api_id'], 0, 2) == 'd_';
    }

//    static public function addTradesExt($trade_id, $station_code, $org_id = null)
    static public function addTradesExt($params,$data_from = [])
    {
        \Framework\Log::error("addTradeExt入参:" . var_export($params, true), [], "tradeExt_");

        if (!isset($params['id']) || !$params['id']) {
            return false;
        }

        $trade_id = $params['id'];//trade_id
        $station_code = isset($params['station_code']) && $params['station_code'] ? $params['station_code'] : null;
        $pcode = !empty($params['pcode']) ? $params['pcode'] : null;
        $org_id = isset($params['org_id']) && $params['org_id'] ? $params['org_id'] : null;
        //$station_code, $org_id = null

        $insert = false;

        $data['trades_id']      = $trade_id;
        //txb 增加交易来源
        $data['data_from'] = isset($data_from[$params['api_id']]) ? $data_from[$params['api_id']] : null;

        $data['real_oil_num'] = isset($params['real_oil_num']) && $params['real_oil_num'] ? $params['real_oil_num'] : $params['trade_num'];

        $data['is_final'] = isset($params['ext_is_final']) && $params['ext_is_final'] ? $params['ext_is_final'] : 100;

        $data['original_order_id'] = isset($params['original_order_id']) && $params['original_order_id'] ? $params['original_order_id'] : '';

        \Framework\Log::error("addTradeExt-before:" . var_export($data, true), [], "tradeExt_");

        if( in_array($data['data_from'],[ 501 ]) ){
            $data['is_final'] = 310;
        }

        \Framework\Log::error("addTradeExt-after:" . var_export($data, true), [$data_from], "tradeExt_");

        if (!empty($params['cancel_sn']) && substr($params['api_id'], 0, 2) == 'd_') {
            // 消费撤销
            $tradeId = substr($params['cancel_sn'], 2);
            $sourceExt = self::getOneInfo(['trades_id' => $tradeId]);

            $data['real_oil_num'] = $sourceExt->real_oil_num * -1;
            $data['mac_price'] = $sourceExt->mac_price;
            $data['mac_amount'] = -1 * $sourceExt->mac_amount;

//            if (IsFinal::isStable($sourceExt->is_final)) {
//                // 取消时，消费记录已定版，
//                $data['plummet_rebate_money'] = $sourceExt->plummet_rebate_money * -1;
//                $data['later_rebate_money'] = $sourceExt->later_rebate_money * -1;
//            }

        } else {
            $data['mac_price'] = isset($params['mac_price']) ? $params['mac_price'] : $params['trade_price'];
            $data['mac_amount'] = isset($params['mac_amount']) ? $params['mac_amount'] : $params['trade_money'];
        }

        if ($station_code) {
            $supplierInfo = \Models\OilSupplierRelation::getByCodeAndType($station_code, 20, 1);
            \Framework\Log::error("addTradeExt:" . var_export($supplierInfo, true), ["trade_id" => $trade_id, "$station_code" => $station_code], "tradeExt_");
            if ($supplierInfo && isset($supplierInfo['station_supplier'])) {
                $merInfo = $supplierInfo['station_supplier'];
                $data['merchant_id'] = isset($merInfo['merchant_id']) ? $merInfo['merchant_id'] : '';
                $data['merchant_name'] = isset($merInfo['company_name']) ? $merInfo['company_name'] : '';

                $insert = true;
            }
        }

        if ($org_id) {
            //取机构对应的销售信息
            $saleInfo = OilCrmOrg::getByOrgId($org_id);
            if ($saleInfo) {
                $data['aplownerid'] = $saleInfo->aplownerid;
                $data['aplowneridname'] = $saleInfo->aplowneridname;
                $data['sale_email'] = $saleInfo->saleEmail;
                $data['oil_centercode'] = $saleInfo->oil_centercode;
                $data['oil_centername'] = $saleInfo->oil_centername;

                $insert = true;
            }

            //更新机构的最近和首次消费时间
            OilOrg::updateTradesTime($org_id);
        }

        $isCarCard = false;

        if (isset($params['vice_no']) && $params['vice_no']) {
            $cardInfo = OilCardVice::getByViceNo(['vice_no' => $params['vice_no']]);
            if (isset($cardInfo->id) && $cardInfo->id) {
                $data['org_id_fanli'] = $cardInfo->org_id_fanli;
                $data['fanli_orgcode'] = $cardInfo->FanLiOrg->orgcode;
                $data['fanli_org_name'] = $cardInfo->FanLiOrg->org_name;

                if ($cardInfo->card_level == CardViceConf::CARD_LEVEL_CAR) {
                    $isCarCard = true;
                }

                $insert = true;
            }
        }

        // 获取运营商的未核销订单是否计入开票配置
        self::initChargeOffAndCanInvoice($data, $params);

//        if( in_array($params['trade_type'],['加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油']) && !in_array($params['card_from'],[40,41]) ) {
        if( in_array($params['trade_type'],TradesType::getCashAndSelfOilTradeTypeArr()) && !in_array($params['card_from'],[40,41]) ) {
            //(new OilTypeService())->checkByTrades($params);
            global $app;
            if( !$app->myAdmin || !$app->myAdmin->id ){
                $app->myAdmin = new \stdClass();
                $app->myAdmin->id = 8888;
                $app->myAdmin->true_name = '系统自动';
            }
            $task = (new \Jobs\AsyncAddStationTypeJob($params))
                ->setTaskName('异步写油品和油站数据')
                ->onQueue('chargeForTemplate')
                ->setUserInfo($app->myAdmin)
                ->setTries(2)
                ->dispatch();

            if( $data['is_final'] == 310 ){
                TradeReceiptInvoiceRed::addReceiptInvoiceTrade($params,$data['data_from']);
            }
        }

        // 消费记录卡流水，消费和维修保养（根据运营商区分）
        $_dataItem = [];
        //入库卡流水表
        $_dataItem['card_no'] = $params['vice_no'];
        $_dataItem['res_id'] = $trade_id;
        $_dataItem['amount'] = $params['trade_money'] * -1;
        $pay_type = CardViceBillConf::PAY_TYPE_CASH_ACCOUNT;
        if( !empty($params['account_no']) ){
            if( !in_array($params['account_name'],['余额','储值账户','储值账号','卡现金账号','现金账户','现金账号']) ){
                $pay_type = CardViceBillConf::PAY_TYPE_CREDIT_ACCOUNT;
            }
        }
        $_dataItem['pay_type'] = $pay_type;
        $_dataItem['mobile'] = $params['qz_drivertel'];
        $trade_time = $_dataItem['trade_time'] = $params['trade_time'];

        // 维修保养
        if ( in_array($pcode, PcodeConf::getAllPcode()) ) {
            $type = CardViceBillConf::RES_TYPE_WXBY;
            if(in_array($pcode,PcodeConf::getKLPcode())){
                $type = CardViceBillConf::RES_TYPE_RHY;
            }
            $place = $params['trade_place'].'-'.CardViceBillConf::$res_type[$type];
            if($params['service_money'] > 0){
                $place .= "（含服务费".$params['service_money']."元）";
            }
            $_dataItem['res_type'] = $type;

            // 加油消费
        } else {
            $oil_name = $params['oil_name'];
            if (stripos($oil_name, "天然气") !== false) {
                $unit = 'Kg';
                if (stripos($oil_name, "压缩天然气") !== false) {
                    $unit = '立方';
                }
                if (stripos($oil_name, "液化天然气") !== false) {
                    $unit = 'Kg';
                }
            } else {
                $unit = "升";
            }
            $place = $params['trade_place']."-消费".$params['trade_num'].$unit;
            if($params['service_money'] > 0){
                $place .= "（含服务费".$params['service_money']."元）";
            }
            $_dataItem['res_type'] = CardViceBillConf::RES_TYPE_XF;
        }

        //新增单据类型
        $data['document_type'] = $params['document_type'] ?? 0;
        if (strpos($params['trade_type'], '圈存') !== false) {
            if (strpos($params['trade_type'], '现金') !== false) {
                $data['document_type'] = CardTradeConf::DOCUMENT_TYPE_CASH_LOAD;
            } elseif (strpos($params['trade_type'], '积分') !== false) {
                $data['document_type'] = CardTradeConf::DOCUMENT_TYPE_POINTS_LOAD;
            } else {
                $data['document_type'] = CardTradeConf::DOCUMENT_TYPE_CASH_LOAD;
            }
        } elseif(strpos($params['trade_type'], '圈提') !== false){
            $data['document_type'] = CardTradeConf::DOCUMENT_TYPE_CASH_LOOP;
        } else {
            if(in_array($params['trade_type'],TradesType::getCashTradeTypeArr())){
                $data['document_type'] = CardTradeConf::DOCUMENT_TYPE_CONSUMPTION_NORMAL;
            } elseif (in_array($params['trade_type'],TradesType::getJiFenTradeTypeArr())) {
                $data['document_type'] = CardTradeConf::DOCUMENT_TYPE_CONSUMPTION_NORMAL;
            }
        }

        // 消费撤销
        if(!empty($params['cancel_sn']) && $_dataItem['amount'] > 0){
            $_dataItem['res_type'] = CardViceBillConf::RES_TYPE_XFCX;
            $place = $params['trade_place']."-消费撤销";
            $_dataItem['trade_time'] = $params['createtime'];
        }

        if($params['trade_type'] == CardTradeConf::RESERVE_TRADE_TYPE){
            $_dataItem['res_type'] = CardViceBillConf::RES_TYPE_YY;
            $place = $params['trade_place']."-预约加油".$params['trade_num'].$unit;
        }
        if($params['trade_type'] == CardTradeConf::RESERVE_CANCEL_TRADE_TYPE){
            $_dataItem['res_type'] = CardViceBillConf::RES_TYPE_YYCX;
            $place = $params['trade_place']."-预约撤销";
            $_dataItem['trade_time'] = $trade_time;
        }

        $_dataItem['trade_desc'] = $place;
        $_dataItem['use_fanli'] = $params['use_fanli_money'];
        $_dataItem['oil_com'] = $params['oil_com'];
        $_dataItem['account_name'] = $pay_type == 10 ? "" : $params['account_name'];
        $_dataItem['account_no'] = $params['account_no'];
        $_dataItem['account_balance'] = $params['balance'] ? $params['balance'] : 0;

        if ($insert) {
            $tradeType = array_merge(TradesType::getCashAndSelfOilTradeTypeArr(),[CardTradeConf::RESERVE_CANCEL_TRADE_TYPE,CardTradeConf::RESERVE_TRADE_TYPE]);
//            if( in_array($params['trade_type'],['加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油'])) {
            if( in_array($params['trade_type'],$tradeType) ) {
                if ( $isCarCard && !in_array($_dataItem['res_type'], [CardViceBillConf::RES_TYPE_XF, CardViceBillConf::RES_TYPE_XFCX,
                        CardViceBillConf::RES_TYPE_YY,CardViceBillConf::RES_TYPE_YYCX]) ) {
                    $_dataItem['mobile'] = '';
                }
                (new CardViceBill())->generateBill($_dataItem);
            }
            $data['ocr_truck_no_url'] = $params['ocr_truck_no_url'] ?? '';
            return OilCardViceTradesExt::add($data);
        }

        return false;
    }

    /**
     * oil_card_vice_trades_ext 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTradesExt::find($params['id']);
    }

    /**
     * oil_card_vice_trades_ext 详情查询
     * @param array $params
     * @return OilCardViceTradesExt
     */
    static public function getOneInfo(array $params)
    {
        return OilCardViceTradesExt::Filter($params)->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTradesExt::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * oil_card_vice_trades_ext 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardViceTradesExt::create($params);
    }

    /**
     * oil_card_vice_trades_ext 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTradesExt::find($params['id'])->update($params);
    }

    /**
     * oil_card_vice_trades_ext 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardViceTradesExt::destroy($params['ids']);
    }

    /**
     * 提供给支付中心交易增量获取
     * @param $params
     * @return mixed
     */
    public static function get_merchant_trades($params)
    {
        $sqlObj = Capsule::connection('online_only_read')->table('oil_card_vice_trades_ext')
            ->selectRaw('oil_card_vice_trades.id,oil_card_vice_trades.vice_no,oil_card_vice_trades.station_code,
		oil_card_vice_trades.trade_place as station_name,oil_card_vice_trades.trade_money,
		oil_card_vice_trades_ext.merchant_id,oil_card_vice_trades_ext.merchant_name,oil_card_vice_trades.stream_no,
		oil_card_vice_trades.createtime')
            ->leftJoin('oil_card_vice_trades', 'oil_card_vice_trades.id', '=', 'oil_card_vice_trades_ext.trades_id')
            ->where('oil_card_vice_trades_ext.merchant_id', '!=', '')
            ->whereNotNull('oil_card_vice_trades_ext.merchant_id');

        if (isset($params['createtime_gt']) && $params['createtime_gt']) {
            $sqlObj->where('oil_card_vice_trades_ext.updatetime', '>', $params['createtime_gt']);
        }

        if (isset($params['createtime_le']) && $params['createtime_le']) {
            $sqlObj->where('oil_card_vice_trades_ext.updatetime', '<=', $params['createtime_le']);
        }

        if (isset($params['id_gt']) && $params['id_gt'] != '') {
            $sqlObj->where('oil_card_vice_trades_ext.trades_id', '>', $params['id_gt']);
        }

        $take = isset($params['page_size']) && intval($params['page_size']) >= 0 && intval($params['page_size']) < 300 ? intval($params['page_size']) : 100;
        $page_no = isset($params['page_no']) && intval($params['page_no']) >= 0 ? intval($params['page_no']) : 1;
        $skip = ($page_no - 1) * $take;

        return $sqlObj->orderBy('oil_card_vice_trades_ext.trades_id', 'asc')->skip($skip)->take($take)->get();

    }

    public static function getTradesFromId($params)
    {
        $sqlObj = Capsule::connection('online_only_read')->table('oil_card_vice_trades_ext')
            ->selectRaw('oil_card_vice_trades.*,oil_card_vice_trades_ext.data_from')
            ->leftJoin('oil_card_vice_trades', 'oil_card_vice_trades.id', '=', 'oil_card_vice_trades_ext.trades_id');

        if (isset($params['trade_id']) && $params['trade_id'] != '') {
            $sqlObj->where('oil_card_vice_trades.id', $params['trade_id']);
        }

        if (isset($params['api_id']) && $params['api_id'] != '') {
            $sqlObj->where('oil_card_vice_trades.api_id', strval($params['api_id']));
        }


        $data = $sqlObj->first();
        return $data;
    }

}