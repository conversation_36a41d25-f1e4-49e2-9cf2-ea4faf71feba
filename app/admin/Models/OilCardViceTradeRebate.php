<?php
/**
 * 交易记录返利表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/06/21
 * Time: 16:10:54
 */
namespace Models;
use Fuel\Defines\OilCom;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardViceTradeRebate extends \Framework\Database\Model
{
    protected $table = 'oil_card_vice_trade_rebate';

    protected $guarded = ["id"];
    protected $fillable = ['trade_id','trade_create_time','rebate_no','straight_policy_id','straight_fanli_way',
        'straight_down_rebate','final_straight_down_rebate','cal_no','after_policy_id','after_fanli_way','after_rebate',
        'final_after_rebate','is_final','down_cal_no','down_cal_policy_id','down_fanli_way','down_cal_rebate',
        'final_down_cal_rebate','down_is_final','mark_rebate', 'is_share','gms_price_id','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By trade_id
        if (isset($params['trade_id']) && $params['trade_id'] != '') {
            $query->where('trade_id', '=', $params['trade_id']);
        }

        //Search By trade_create_time
        if (isset($params['trade_create_time']) && $params['trade_create_time'] != '') {
            $query->where('trade_create_time', '=', $params['trade_create_time']);
        }

        if (isset($params['trade_create_time_ge']) && $params['trade_create_time_ge'] != '') {
            $query->where('trade_create_time', '>=', $params['trade_create_time_ge']);
        }

        if (isset($params['trade_create_time_le']) && $params['trade_create_time_le'] != '') {
            $query->where('trade_create_time', '<=', $params['trade_create_time_le']);
        }

        //Search By rebate_no
        if (isset($params['rebate_no']) && $params['rebate_no'] != '') {
            $query->where('rebate_no', '=', $params['rebate_no']);
        }

        //Search By straight_policy_id
        if (isset($params['straight_policy_id']) && $params['straight_policy_id'] != '') {
            $query->where('straight_policy_id', '=', $params['straight_policy_id']);
        }

        //Search By straight_fanli_way
        if (isset($params['straight_fanli_way']) && $params['straight_fanli_way'] != '') {
            $query->where('straight_fanli_way', '=', $params['straight_fanli_way']);
        }

        //Search By straight_down_rebate
        if (isset($params['straight_down_rebate']) && $params['straight_down_rebate'] != '') {
            $query->where('straight_down_rebate', '=', $params['straight_down_rebate']);
        }

        //Search By final_straight_down_rebate
        if (isset($params['final_straight_down_rebate']) && $params['final_straight_down_rebate'] != '') {
            $query->where('final_straight_down_rebate', '=', $params['final_straight_down_rebate']);
        }

        //Search By cal_no
        if (isset($params['cal_no']) && $params['cal_no'] != '') {
            $query->where('cal_no', '=', $params['cal_no']);
        }

        //Search By after_policy_id
        if (isset($params['after_policy_id']) && $params['after_policy_id'] != '') {
            $query->where('after_policy_id', '=', $params['after_policy_id']);
        }

        //Search By after_fanli_way
        if (isset($params['after_fanli_way']) && $params['after_fanli_way'] != '') {
            $query->where('after_fanli_way', '=', $params['after_fanli_way']);
        }

        //Search By after_rebate
        if (isset($params['after_rebate']) && $params['after_rebate'] != '') {
            $query->where('after_rebate', '=', $params['after_rebate']);
        }

        //Search By final_after_rebate
        if (isset($params['final_after_rebate']) && $params['final_after_rebate'] != '') {
            $query->where('final_after_rebate', '=', $params['final_after_rebate']);
        }

        //Search By is_final
        if (isset($params['is_final']) && $params['is_final'] != '') {
            $query->where('is_final', '=', $params['is_final']);
        }

        //Search By down_cal_no
        if (isset($params['down_cal_no']) && $params['down_cal_no'] != '') {
            $query->where('down_cal_no', '=', $params['down_cal_no']);
        }

        //Search By down_cal_policy_id
        if (isset($params['down_cal_policy_id']) && $params['down_cal_policy_id'] != '') {
            $query->where('down_cal_policy_id', '=', $params['down_cal_policy_id']);
        }

        //Search By down_fanli_way
        if (isset($params['down_fanli_way']) && $params['down_fanli_way'] != '') {
            $query->where('down_fanli_way', '=', $params['down_fanli_way']);
        }

        //Search By down_cal_rebate
        if (isset($params['down_cal_rebate']) && $params['down_cal_rebate'] != '') {
            $query->where('down_cal_rebate', '=', $params['down_cal_rebate']);
        }

        //Search By final_down_cal_rebate
        if (isset($params['final_down_cal_rebate']) && $params['final_down_cal_rebate'] != '') {
            $query->where('final_down_cal_rebate', '=', $params['final_down_cal_rebate']);
        }

        //Search By down_is_final
        if (isset($params['down_is_final']) && $params['down_is_final'] != '') {
            $query->where('down_is_final', '=', $params['down_is_final']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 交易记录返利表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilCardViceTradeRebate::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    static public function getListForDateRange($params)
    {
        $data = [];
        //todo 只读
        //Capsule::connection()->enableQueryLog();
        $_data = OilCardViceTradeRebate::leftJoin('oil_trades','oil_trades.trades_id','=','oil_card_vice_trade_rebate.trade_id')
            ->where('oil_trades.station_code',$params['station_code'])
            ->where('oil_trades.orgcode','like',$params['org_code'].'%')
            ->where('oil_card_vice_trade_rebate.trade_create_time','>=',$params['start_time'])
            ->where('oil_card_vice_trade_rebate.trade_create_time','<=',$params['end_time'])
//            ->where('oil_card_vice_trade_rebate.is_final','>',100)
//            ->where('oil_card_vice_trade_rebate.down_is_final','>',100)
            ->whereIn('oil_trades.oil_com',OilCom::getAllFirstList())
            ->selectRaw('sum(oil_trades.trade_money) AS gmv,
       sum(if(oil_card_vice_trade_rebate.is_final > 100,oil_card_vice_trade_rebate.final_straight_down_rebate + oil_card_vice_trade_rebate.final_after_rebate,0)) + sum(oil_card_vice_trade_rebate.mark_rebate) AS up_profit,
       (sum(if(oil_card_vice_trade_rebate.down_is_final > 100,oil_card_vice_trade_rebate.final_down_cal_rebate,0)) + sum(oil_trades.mac_amount - oil_trades.trade_money)) AS down_profit')
            ->groupBy('oil_trades.station_code')
            ->first();
        //$sql = Capsule::connection()->getQueryLog();
        if($_data){
            $data = $_data->toArray();
        }
        return $data;
    }

    /**
     * 交易记录返利表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceTradeRebate::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceTradeRebate::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 交易记录返利表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardViceTradeRebate::create($params);
    }

    /**
     * 交易记录返利表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilCardViceTradeRebate::find($params['id'])->update($params);
    }

    /**
     * 交易记录返利表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilCardViceTradeRebate::destroy($params['ids']);
    }




}