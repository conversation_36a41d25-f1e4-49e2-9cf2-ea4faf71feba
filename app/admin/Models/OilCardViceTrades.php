<?php
/**
 * 油品副卡交易明细表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */

namespace Models;

use Framework\DingTalk\DingTalkAlarm;
use Fuel\Defines\CardFrom;
use Fuel\Defines\CardTradeConf;
use Fuel\Defines\ChargeOffStatus;
use Fuel\Defines\CreditProvider;
use Fuel\Defines\IsFinal;
use Fuel\Defines\IsOpenInvoiceForTrades;
use Fuel\Defines\OilCom;
use Fuel\Defines\OilType;
use Fuel\Defines\PcodeConf;
use Fuel\Defines\ReceiptConfig;
use Fuel\Defines\StationLimit;
use Fuel\Defines\StationOperatorsConf;
use Fuel\Defines\TradesType;
use Fuel\Request\GasClient;
use Fuel\Request\GspClient;
use Fuel\Service\AccountCenter\AccountService;
use Fuel\Service\AdapterClient;
use Fuel\Service\CardViceTradesToGos;
use Fuel\Service\ReceiptApplyInternal;
use Fuel\Service\TradesService;
use Fuel\Service\TypeCategoryService;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Response as Response;
use Models\OilCardVice as OilCardVice;
use Models\OilErrorLogs as OilErrorLogs;
use Framework\Cache;
use Framework\Log;
use \Framework\Database\Model;
use Fuel\Defines\CardViceConf;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Class OilCardViceTrades
 * @package Models
 * @property $id
 */
class OilCardViceTrades extends Model
{
    protected $table = 'oil_card_vice_trades';

    protected $guarded = ["id"];

    public $timestamps = FALSE; //修改不指定时间修改，如需修改必须手动制定updatetime

    protected $fillable = [
        'api_id', 'is_open_invoice', 'imgurl', 'vice_no', 'trade_time', 'trade_type', 'oil_name', 'trade_money', 'cancel_sn',
        'unit', 'use_fanli_money', 'trade_price', 'trade_num', 'trade_jifen', 'balance', 'receipt_remain', 'trade_place', 'station_id', 'regions_id',
        'regions_name', 'fetch_time', 'is_fanli', 'main_no', 'card_owner', 'oil_com', 'card_from', 'main_operator_id', 'org_operator_id', 'active_region',
        'org_id', 'org_name', 'consume_type', 'truck_no', 'fanli_no', 'fanli_money', 'fanli_jifen', 'policy_id', 'fanli_way', 'qz_drivername',
        "xpcode_pay_price", "xpcode_pay_money",
        'qz_drivertel', 'createtime', 'updatetime', 'data_updated', 'trade_place_provice_code', 'trade_place_provice_name', 'trade_place_city_code',
        'trade_place_city_name', 'mileage', 'mini_trade_price', 'billID', 'extId', 'station_code', 'fanli_level', 'service_money', 'total_money', 'account_no', 'account_name',
        'pcode', 'remark', 'stream_no', 'client_order_id' , 'pay_company_id','pay_company_name' 
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function oilTypeNo()
    {
        return $this->belongsTo('Models\OilTypeNo', 'oil_name', 'oil_no');
    }

    /**
     * 副卡信息
     *
     * @return BelongsTo
     */
    public function oilCardVice()
    {
        return $this->belongsTo('Models\OilCardVice', 'vice_no', 'vice_no');
    }

    /**
     * 获取机构
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function Org()
    {
        return $this->belongsTo('Models\OilOrg', 'org_id', 'id');
    }

    public function tradeExt()
    {
        return $this->hasOne('Models\OilCardViceTradesExt', 'trades_id', 'id');
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        //针对g7s费用管理功能，要获取托管卡消费
        if (!isset($params['withDeposit'])) {
            $query->whereNotIn('oil_card_vice_trades.card_from', [40, 41]);
        }

        //参数处理
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            if (preg_match('/(.*)\s/', $params['orgcode'])) {
                preg_match('/(.*)\s/', $params['orgcode'], $arr);
                $params['orgcode'] = $arr[1];
            }
        }
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('oil_card_vice_trades.id', '=', $params['id']);
        }

        //Search By id
        if (isset($params['idIn']) && $params['idIn'] != '') {
            $idMap = !is_array($params['idIn']) ? explode('|', $params['idIn']) : $params['idIn'];
            $query->whereIn('oil_card_vice_trades.id', $idMap);
        }
        //Search By id
        if (isset($params['idNotIn']) && $params['idNotIn'] != '') {
            if (is_array($params['idNotIn'])) {
                $query->whereNotIn('oil_card_vice_trades.id', $params['idNotIn']);
            }
        }
        //Search By idGt
        if (isset($params['idGt']) && $params['idGt'] != '') {
            $query->where('oil_card_vice_trades.id', '>', $params['idGt']);
            $query->orderBy('oil_card_vice_trades.id', 'asc');
        }

        //Search By api_id
        if (isset($params['api_id']) && $params['api_id'] != '') {
            $query->where('api_id', '=', strval($params['api_id']));
        }

        if (isset($params['api_idIn']) && $params['api_idIn'] != '') {
            $query->whereIn('api_id', $params['api_idIn']);
        }

        //Search By cancel_sn
        if (isset($params['cancel_sn']) && $params['cancel_sn'] != '') {
            $query->where('cancel_sn', '=', $params['cancel_sn']);
        }

        //ENINET-485
        if (isset($params['cancel_sn_in']) && $params['cancel_sn_in'] && is_array($params['cancel_sn_in'])) {
            $query->whereIn('cancel_sn', $params['cancel_sn_in']);
        }

        if (isset($params['cancel_sn_not_null']) && $params['cancel_sn_not_null'] != '') {
            $query->whereNotNull('cancel_sn');
        }

        if (isset($params['cancel_sn_null']) && $params['cancel_sn_null'] != '') {
            $query->whereNull('cancel_sn');
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $viceNoArr = explode('|', $params['vice_no']);
            if (count($viceNoArr) == 1) {
                $query->where('oil_card_vice_trades.vice_no', '=', $params['vice_no']);
            } else {
                $query->whereIn('oil_card_vice_trades.vice_no', $viceNoArr);
            }
        }

        //Search By vice_noIn
        if (isset($params['vice_noIn']) && $params['vice_noIn'] != '') {
            $query->whereIn('vice_no', $params['vice_noIn']);
        }

        //Search By trade_time
        if (isset($params['timeGe']) && $params['timeGe'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '>', $params['timeGe']);
        }

        //Search By tradetimeGt
        if (isset($params['tradetimeGt']) && $params['tradetimeGt'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '>', $params['tradetimeGt']);
        }

        //Search By tradetimeGe
        if (isset($params['tradetimeGe']) && $params['tradetimeGe'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '>=', $params['tradetimeGe']);
        }

        //Search By tradetimeLe
        if (isset($params['tradetimeLe']) && $params['tradetimeLe'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '<=', $params['tradetimeLe']);
        }

        //Search By tradetimeLe
        if (isset($params['tradetimeLne']) && $params['tradetimeLne'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '<', $params['tradetimeLne']);
        }

        //Search By tradetimeGe
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe']);
        }

        //Search By tradetimeLe
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        if (isset($params['createtimeLne']) && $params['createtimeLne'] != '') {
            $query->where('oil_card_vice_trades.createtime', '<', $params['createtimeLne']);
        }

        //Search By trade_time
        if (isset($params['trade_time']) && $params['trade_time'] != '') {
            $query->where('oil_card_vice_trades.trade_time', '=', $params['trade_time']);
        }

        //Search By trade_typeList
        if (isset($params['tradeTypeList']) && $params['tradeTypeList']) {
            if (count($params['tradeTypeList']) > 1 && (in_array('交易撤销', $params['tradeTypeList']) || in_array(CardTradeConf::RESERVE_CANCEL_TRADE_TYPE, $params['tradeTypeList']))) {

                if(in_array('交易撤销', $params['tradeTypeList']) && !in_array(CardTradeConf::RESERVE_CANCEL_TRADE_TYPE, $params['tradeTypeList'])) {
                    $query->where(function ($query) use ($params) {
                        $query->whereIn('oil_card_vice_trades.trade_type', $params['tradeTypeList'])
                            ->orWhere(function ($query) use ($params) {
                                $query->WhereNotNull('oil_card_vice_trades.cancel_sn');
                                $query->whereIn("oil_card_vice_trades.trade_type", [CardTradeConf::ELECTRON_TRADE_TYPE, CardTradeConf::ELECTRON_CANCEL_TRADE_TYPE]);
                            });
                    });
                }

                if(in_array(CardTradeConf::RESERVE_CANCEL_TRADE_TYPE, $params['tradeTypeList']) && !in_array('交易撤销', $params['tradeTypeList'])) {
                    $query->where(function ($query) use ($params) {
                        $query->whereIn('oil_card_vice_trades.trade_type', $params['tradeTypeList'])
                            ->orWhere(function ($query) use ($params) {
                                $query->WhereNotNull('oil_card_vice_trades.cancel_sn');
                                $query->whereIn("oil_card_vice_trades.trade_type", [CardTradeConf::RESERVE_TRADE_TYPE, CardTradeConf::RESERVE_CANCEL_TRADE_TYPE]);
                            });
                    });
                }

                if(in_array(CardTradeConf::RESERVE_CANCEL_TRADE_TYPE, $params['tradeTypeList']) && in_array('交易撤销', $params['tradeTypeList'])) {
                    $query->where(function ($query) use ($params) {
                        $query->whereIn('oil_card_vice_trades.trade_type', $params['tradeTypeList'])
                            ->orWhere(function ($query) use ($params) {
                                $query->WhereNotNull('oil_card_vice_trades.cancel_sn');
                                $query->whereIn("oil_card_vice_trades.trade_type", [CardTradeConf::RESERVE_TRADE_TYPE, CardTradeConf::RESERVE_CANCEL_TRADE_TYPE]);
                            });
                    })->orWhere(function ($query) use ($params) {
                        $query->whereIn('oil_card_vice_trades.trade_type', $params['tradeTypeList'])
                            ->orWhere(function ($query) use ($params) {
                                $query->WhereNotNull('oil_card_vice_trades.cancel_sn');
                                $query->whereIn("oil_card_vice_trades.trade_type", [CardTradeConf::ELECTRON_TRADE_TYPE, CardTradeConf::ELECTRON_CANCEL_TRADE_TYPE]);
                            });
                    });
                }

            } elseif (count($params['tradeTypeList']) == 1 && in_array('交易撤销', $params['tradeTypeList'])) {
                $query->where(function ($query) use($params){
                    $query->whereNotNull('oil_card_vice_trades.cancel_sn');
                    $query->whereIn("oil_card_vice_trades.trade_type",[CardTradeConf::ELECTRON_TRADE_TYPE,CardTradeConf::ELECTRON_CANCEL_TRADE_TYPE]);
                });
            } elseif (count($params['tradeTypeList']) == 1 && in_array(CardTradeConf::RESERVE_CANCEL_TRADE_TYPE, $params['tradeTypeList'])) {
                $query->where(function ($query) use($params){
                    $query->whereNotNull('oil_card_vice_trades.cancel_sn');
                    $query->whereIn("oil_card_vice_trades.trade_type",[CardTradeConf::RESERVE_TRADE_TYPE,CardTradeConf::RESERVE_CANCEL_TRADE_TYPE]);
                });
            } else {
                $query->whereIn('oil_card_vice_trades.trade_type', $params['tradeTypeList']);
            }

        }

        //Search By oil_name
        if (isset($params['oil_name']) && $params['oil_name'] != '') {
            $query->where('oil_card_vice_trades.oil_name', '=', $params['oil_name']);
        }

        //Search By trade_money
        if (isset($params['trade_money']) && $params['trade_money'] != '') {
            $query->where('oil_card_vice_trades.trade_money', '=', $params['trade_money']);
        }

        if (isset($params['trade_money_gt']) && $params['trade_money_gt'] != '') {
            $query->where('oil_card_vice_trades.trade_money', '>', 0);
        }

        //Search By use_fanli_money
        if (isset($params['use_fanli_money']) && $params['use_fanli_money'] != '') {
            $query->where('use_fanli_money', '=', $params['use_fanli_money']);
        }

        //Search By trade_price
        if (isset($params['trade_price']) && $params['trade_price'] != '') {
            $query->where('trade_price', '=', $params['trade_price']);
        }

        //Search By trade_num
        if (isset($params['trade_num']) && $params['trade_num'] != '') {
            $query->where('trade_num', '=', $params['trade_num']);
        }

        //Search By trade_jifen
        if (isset($params['trade_jifen']) && $params['trade_jifen'] != '') {
            $query->where('trade_jifen', '=', $params['trade_jifen']);
        }

        //Search By balance
        if (isset($params['balance']) && $params['balance'] != '') {
            $query->where('balance', '=', $params['balance']);
        }

        //Search By receipt_remain
        if (isset($params['receipt_remain']) && $params['receipt_remain'] != '') {
            $query->where('receipt_remain', '=', $params['receipt_remain']);
        }

        //Search By trade_place
        if (isset($params['trade_place']) && $params['trade_place'] != '') {
//            $query->where('oil_card_vice_trades.trade_place', 'like', '%' . $params['trade_place'] . '%');
            $tradePlaceArr = explode('|', $params['trade_place']);
            if (count($tradePlaceArr) == 1) {
                $query->where('oil_card_vice_trades.trade_place', '=', $params['trade_place']);
            } else {
                $query->whereIn('oil_card_vice_trades.trade_place', $tradePlaceArr);
            }
        }

        //Search By regions_id
        if (isset($params['regions_id']) && $params['regions_id'] != '') {
            $query->where('regions_id', '=', $params['regions_id']);
        }

        //Search By regions_name
        if (isset($params['regions_name']) && $params['regions_name'] != '') {
            $query->where('oil_card_vice_trades.regions_name', '=', $params['regions_name']);
        }

        //Search By fetch_time
        if (isset($params['fetch_time']) && $params['fetch_time'] != '') {
            $query->where('fetch_time', '=', $params['fetch_time']);
        }

        //Search By is_fanli
        if (isset($params['is_fanli']) && $params['is_fanli'] !== '') {
            $query->where('oil_card_vice_trades.is_fanli', '=', $params['is_fanli']);
        }

        if (isset($params['is_fanliIn']) && $params['is_fanliIn'] !== '') {
            $query->whereIn('oil_card_vice_trades.is_fanli', $params['is_fanliIn']);
        }

        //Search By main_no
        if (isset($params['main_no']) && $params['main_no'] != '') {
            $query->where('oil_card_vice_trades.main_no', '=', $params['main_no']);
        }

        //Search By card_owner
        if (isset($params['card_owner']) && $params['card_owner'] != '') {
            $query->where('oil_card_vice_trades.card_owner', '=', $params['card_owner']);
        }

        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_card_vice_trades.oil_com', '=', $params['oil_com']);
        }

        //Search By oil_com_in
        if (isset($params['oil_com_in']) && $params['oil_com_in']) {
            $oilComIn = !is_array($params['oil_com_in']) ? explode(",", $params['oil_com_in']) : $params['oil_com_in'];
            $query->whereIn('oil_card_vice_trades.oil_com', $oilComIn);
        }


        //Search By active_region
        if (isset($params['active_region']) && $params['active_region'] != '') {
            $query->where('oil_card_vice_trades.active_region', '=', $params['active_region']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('oil_card_vice_trades.org_id', '=', $params['org_id']);
        }

        //Search By org_name
        if (isset($params['org_name']) && $params['org_name'] != '') {
            $query->where('oil_card_vice_trades.org_name', '=', $params['org_name']);
        }

        //Search By consume_type
        if (isset($params['consume_type']) && $params['consume_type'] != '') {
            $query->where('oil_card_vice_trades.consume_type', '=', $params['consume_type']);
        }

        //Search By truck_no
        if (isset($params['truck_no']) && $params['truck_no'] != '') {
            $query->where('truck_no', '=', $params['truck_no']);
        }

        //Search By fanli_no
        if (isset($params['fanli_no']) && $params['fanli_no'] != '') {
            $query->where('fanli_no', '=', $params['fanli_no']);
        }

        //Search By account_name
        if (isset($params['account_name']) && $params['account_name'] != '') {
            $accountNameMap = explode(",", trim($params['account_name']));
            if (in_array('储值账户', $accountNameMap)) {
                $accountNameMap = array_merge(['现金账户'], $accountNameMap);
            }
            $query->whereIn('oil_card_vice_trades.account_name', $accountNameMap);
        }

        //Search By fanli_noIn
        if (isset($params['fanli_noIn']) && $params['fanli_noIn'] != '') {
            $query->whereIn('fanli_no', $params['fanli_noIn']);
        }

        //Search By fanli_money
        if (isset($params['fanli_money']) && $params['fanli_money'] != '') {
            $query->where('fanli_money', '=', $params['fanli_money']);
        }

        //Search By fanli_jifen
        if (isset($params['fanli_jifen']) && $params['fanli_jifen'] != '') {
            $query->where('fanli_jifen', '=', $params['fanli_jifen']);
        }

        //Search By policy_id
        if (isset($params['policy_id']) && $params['policy_id'] != '') {
            $query->where('policy_id', '=', $params['policy_id']);
        }

        //Search By fanli_way
        if (isset($params['fanli_way']) && $params['fanli_way'] != '') {
            $query->where('fanli_way', '=', $params['fanli_way']);
        }

        //Search By qz_drivername
        if (isset($params['qz_drivername']) && $params['qz_drivername'] != '') {
            $query->where('qz_drivername', '=', $params['qz_drivername']);
        }

        //Search By qz_drivertel
        if (isset($params['qz_drivertel']) && $params['qz_drivertel'] != '') {
            $query->where('qz_drivertel', '=', $params['qz_drivertel']);
        }

        //Search By qz_drivertel
        if (isset($params['phones']) && $params['phones'] != '') {
            $query->whereIn('qz_drivertel', $params['phones']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_card_vice_trades.updatetime', '=', $params['updatetime']);
        }

        //Search By updatetimeGt
        if (isset($params['updatetimeGt']) && $params['updatetimeGt'] != '') {
            $query->where('oil_card_vice_trades.updatetime', '>', $params['updatetimeGt']);
        }

        //Search By updatetimeLe
        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('oil_card_vice_trades.updatetime', '<=', $params['updatetimeLe']);
        }

        if (isset($params['org_id_list']) && is_array($params['org_id_list'])) {
            $query->whereIn('org_id', $params['org_id_list']);
        }

        //Search By vice_no
        if (isset($params['oil_comIn']) && $params['oil_comIn'] != '') {
            $query->whereIn('oil_card_vice_trades.oil_com', $params['oil_comIn']);
        }

        //Search By card_from
        if (isset($params['card_from']) && $params['card_from'] != '') {
            $query->where('oil_card_vice_trades.card_from', '=', $params['card_from']);
        }

        //Search By card_fromIn
        if (isset($params['card_fromIn']) && $params['card_fromIn'] != '') {
            $query->whereIn('oil_card_vice_trades.card_from', $params['card_fromIn']);
        }

        //Search By oil_type
        if (isset($params['oil_type']) ) {
            if (is_array($params['oil_type'])) {
                $query->whereIn('oil_type_no.oil_type', $params['oil_type']);
            } elseif ($params['oil_type'] != '') {
                $query->where('oil_type_no.oil_type', $params['oil_type']);
            }
        }
        //Search By org_id
        if (isset($params['orgcode']) && $params['orgcode'] != '' && isset($params['org_flag'])) {
            $query->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');
        }
        if (isset($params['orgcode']) && $params['orgcode'] != '' && !isset($params['org_flag'])) {
            $query->where('oil_org.orgcode', '=', $params['orgcode']);
        }
        if (isset($params['orgcode']) && $params['orgcode'] && isset($params['is_org_root'])) {
            $query->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');
        }
        if (isset($params['customer_orgcode']) && $params['customer_orgcode'] && isset($params['is_org_root'])) {
            $query->where('oil_org.orgcode', 'like', $params['customer_orgcode'] . '%');
        }

        //Search By orgcode_in
        if (isset($params['orgcode_in']) && $params['orgcode_in']) {
            $orgCodeIn = !is_array($params['orgcode_in']) ? explode(",", $params['orgcode_in']) : $params['orgcode_in'];
            $query->whereIn('oil_org.orgcode', $orgCodeIn);
        }

        //Search By consume_region
        if (isset($params['consume_region']) && $params['consume_region'] != '') {
            $query->where('oil_station.regions_id', $params['consume_region']);
        }

        //Search By is_test
        if (isset($params['is_test']) && $params['is_test'] != '') {
            $query->where('oil_org.is_test', '=', $params['is_test']);
        }

        //Search By trade_place_provice_code
        if (isset($params['trade_place_provice_code']) && $params['trade_place_provice_code'] != '') {
            $query->where('oil_card_vice_trades.trade_place_provice_code', 'like', $params['trade_place_provice_code'] . '%');
        }

        //Search By is_test
        if (isset($params['trade_place_city_code']) && $params['trade_place_city_code'] != '') {
            $query->where('oil_card_vice_trades.trade_place_city_code', 'like', $params['trade_place_city_code'] . '%');
        }

        //Search By trade_place_provice_name
        if (isset($params['trade_place_provice_name']) && $params['trade_place_provice_name'] != '') {
            $query->where('oil_card_vice_trades.trade_place_provice_name', 'like', $params['trade_place_provice_name'] . '%');
        }

        //Search By trade_place_city_name
        if (isset($params['trade_place_city_name']) && $params['trade_place_city_name'] != '') {
            $query->where('oil_card_vice_trades.trade_place_city_name', 'like', $params['trade_place_city_name'] . '%');
        }

        //Search By trade_place_city_name
        if (isset($params['main_operators_id']) && $params['main_operators_id'] != '') {
            $query->where('oil_card_vice_trades.main_operator_id', $params['main_operators_id']);
        }

        //Search By trade_place_city_name
        if (isset($params['org_operators_id']) && $params['org_operators_id'] != '') {
            $query->where('oil_card_vice_trades.org_operator_id', $params['org_operators_id']);
        }

        //Search By trade_place_city_name
        if (isset($params['supplyer_id']) && $params['supplyer_id'] != '') {
            $query->where('oil_card_main.supplyer_id', $params['supplyer_id']);
        }

        //Search By station_code
        if (isset($params['station_codeNeq']) && $params['station_codeNeq'] != '') {
            $query->whereNotNull('oil_card_vice_trades.station_code');
        }

        if (! empty($params['station_codeIsNotEmpty'])) {
            $query->whereRaw('oil_card_vice_trades.station_code <> \'\' ');
        }

        //Search By fanli_level
        if (isset($params['station_code']) && $params['station_code'] != '') {
            $query->where('oil_card_vice_trades.station_code', $params['station_code']);
        }

        //Search By fanli_level
        if (isset($params['fanli_levelEq']) && $params['fanli_levelEq'] != '') {
            $query->whereNull('oil_card_vice_trades.fanli_level');
        }

        //Search By fanli_level
        if (isset($params['extIdNeqNull']) && $params['extIdNeqNull'] != '') {
            $query->whereNotNull('oil_card_vice_trades.extId');
        }

        if (isset($params['pcode_in']) && $params['pcode_in']) {
            $pcode_map = explode(',', $params['pcode_in']);
            $query->whereIn('oil_card_vice_trades.pcode', $pcode_map);
        }

        if (!empty($params['pcode'])) {
            $query->where('oil_card_vice_trades.pcode', '=', $params['pcode']);
        }

        if (!empty($params['trade_type_for_tcard'])) {
            $query->where('oil_card_vice_trades.trade_type', '=', $params['trade_type_for_tcard']);
        }

        //Search By data_from
        if (isset($params['ext_data_from']) && $params['ext_data_from'] != "") {
            $from_map = explode(',', $params['ext_data_from']);
            if (count($from_map) == 1 && $from_map[0] == 'empty') {
                $query->whereNull("ext.data_from");
            } else {
                if (in_array("empty", $from_map)) {
                    $query->where(function ($query) use ($from_map) {
                        $query->whereIn('ext.data_from', $from_map)
                            ->orWhereNull("ext.data_from");
                    });
                } else {
                    $query->whereIn('ext.data_from', $from_map);
                }
            }
        }

        if (!empty($params['charge_off_status'])) {
            $query->whereExists(function ($query) use ($params) {
                $query->select('id')->from('oil_card_vice_trades_ext')
                    ->where('oil_card_vice_trades_ext.charge_off_status', '=', $params['charge_off_status'])
                    ->whereRaw('oil_card_vice_trades_ext.trades_id = oil_card_vice_trades.id');
            });
        }

        //供应商签约主体-查询
        if (isset($params['supplier_operators_id']) && $params['supplier_operators_id']) {
            if(is_array($params['supplier_operators_id']) && count($params['supplier_operators_id']) > 0){
                $query->whereIn('ext.operator_id', $params['supplier_operators_id']);
            }else {
                $query->where('ext.operator_id', $params['supplier_operators_id']);
            }
        }

        if (isset($params['station_supplier_id']) && $params['station_supplier_id']) {
            $query->where('ext.supplier_id', $params['station_supplier_id']);
        }

        if (isset($params['original_order_id']) && $params['original_order_id']) {
            $query->where('ext.original_order_id', $params['original_order_id']);
        }
        if (isset($params['document_type']) && $params['document_type']) {
            $query->where('ext.document_type', $params['document_type']);
        }

        if (isset($params['pay_company_id']) && $params['pay_company_id']) {
            $query->where('oil_card_vice_trades.pay_company_id', $params['pay_company_id']);
        }
        
//         if (isset($params['charge_off_status']) && $params['charge_off_status']) {
//             $query->where('ext.charge_off_status', $params['charge_off_status']);
//         }

        return $query;
    }

    public static function getSqlObj($params, $fields, $connection = '', $includeJoins = true)
    {

        $sqlObj = Capsule::connection($connection)->table('oil_card_vice_trades as oil_card_vice_trades');
        if($includeJoins) {
//            ->leftJoin('oil_provinces as activeProvince', 'activeProvince.id', '=', 'oil_card_vice_trades.active_region')
            $sqlObj->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id')->where('oil_org.is_del', '=', 0);
            })
                ->leftJoin('oil_station', function ($query) {
                    $query->on('oil_station.station_name', '=', 'oil_card_vice_trades.trade_place')->where('oil_station.is_del', '=', 0);
                })
//            ->leftJoin('oil_provinces', 'oil_provinces.id', '=', 'oil_station.regions_id') // redis缓存，做字典
                ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
                ->leftJoin('oil_card_main', 'oil_card_main.main_no', '=', 'oil_card_vice_trades.main_no')
                ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no')
                ->leftJoin('oil_org as org_fanli', 'org_fanli.id', '=', 'oil_card_vice.org_id_fanli')
                ->leftJoin('oil_card_vice_trades_ext as ext', 'ext.trades_id', '=', 'oil_card_vice_trades.id')
                ->leftJoin('oil_station_supplier as ssp', 'ssp.id', '=', 'ext.supplier_id')
                ->leftJoin('oil_card_vice_trade_mark as mark', 'mark.vice_trade_id', '=', 'oil_card_vice_trades.id');
        }
        $sqlObj->select(Capsule::raw($fields));
        if($includeJoins) {
            if (!empty($params['left_join_oil_card_vice_trade_rebate'])) {
                $sqlObj->leftJoin('oil_card_vice_trade_rebate as rebate',
                    'rebate.trade_id',
                    '=',
                    'oil_card_vice_trades.id');
            }
        }
        //tidb不处理索引
        if($connection != 'foss_read_TiDb') {

            $isForce = self::forceIndex2Trades($params);

            if ($isForce) {
                //处理任务：ENINET-222,由于选择油品类型时,没用使用时间索引
                if (isset($params['tradetimeGe']) && !empty($params['tradetimeGe']) && isset($params['tradetimeLe']) && !empty($params['tradetimeLe'])) {
                    $sqlObj->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`trade_time_dx`)'));
                } elseif (isset($params['createtimeGe']) && !empty($params['createtimeGe']) && isset($params['createtimeLe']) && !empty($params['createtimeLe'])) {
                    $sqlObj->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`create_time_dx`)'));
                }
            }
        }


        if (isset($params['oil_type']) && $params['oil_type']) {
            $oilTypeIn = explode(",", trim($params['oil_type']));
            $sqlObj->whereIn("oil_type_no.oil_type", $oilTypeIn);
        }
        if (isset($params['trade2Gms']) && $params['trade2Gms'] == 1) {
            $sqlObj->where(function ($sqlObj) use ($params) {
                $sqlObj->whereRaw("1 = 1");
                if (isset($params['station_code_in']) && $params['station_code_in'] != '') {
                    $sqlObj->whereIn('oil_card_vice_trades.station_code', $params['station_code_in']);
                }
                if (isset($params['pcode_sub_in']) && $params['pcode_sub_in'] != '') {
                    $sqlObj->whereIn('oil_card_vice_trades.pcode', $params['pcode_sub_in']);
                }
                $sqlObj->orWhere("oil_card_vice_trades.main_operator_id", $params['sign_id']);
            });
        }
        $oilCardViceTrades = new OilCardViceTrades();
        $sqlObj = $oilCardViceTrades->scopeFilter($sqlObj, $params);

        return $sqlObj;
    }

    /**
     * 油品副卡交易明细表 列表查询
     *
     * @param array $params
     * @return array
     */
    public static function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = OilCardViceTrades::Filter($params)->with([
            'Org',
        ]);
        if (isset($params['ext']) && $params['ext'] == 1) {
            $sqlObj = $sqlObj->with(['tradeExt']);
        }
        if (isset($params['count']) && $params['count']) {
            $data = $sqlObj->count();
        } elseif (isset($params['take']) && $params['skip']) {
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        } elseif (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('trade_time', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        foreach ($data as $v) {
            //请求adapter,生成壳牌支付码
            $v->showQrcode = 1;
            //if (!empty($v->pcode) && in_array($v->pcode, StationLimit::payMentPcodeList()) && empty($v->cancel_sn)) {
            if (!empty($v->pcode) && in_array($v->pcode, OilStationOperators::getShowQrCodePcode()) && empty($v->cancel_sn)
                && isset($v->tradeExt) && isset($v->tradeExt->data_from) && in_array($v->tradeExt->data_from, [CardTradeConf::WECHAT_ON_LINE, CardTradeConf::WMP_ON_LINE, CardTradeConf::H5_ON_LINE])) {
                $v->showQrcode = 2;
            }
            $v->orgcode = $v->Org->orgcode;
        }
        return $data;
    }

    /*
     * 根据手机号获取消费记录列表
     */
    public static function getListByPhones($params)
    {
        $data = Capsule::connection('online_only_read')->table('oil_card_vice_trades')
            ->whereIn('qz_drivertel', $params['phones'])
            ->where('createtime', '>=', $params['createtimeGe'])
            ->paginate($params['limit'], ['*'], 'page', $params['page']);

        return $data;
    }

    /**
     * 油品副卡交易明细表 列表查询
     *
     * @param array $params
     * @return array
     * @todo 精简字段&运营商无需连表
     */
    public static function getTradesList(array $params, $connection = 'slave')
    {
        $show_charge_off_status = empty($params['charge_off_status']) ? false : true;
        if(isset($params['charge_off_status']) && $params['charge_off_status'] == -1){
            $params['charge_off_status'] = '';
        }
        if (API_ENV == 'dev') {
            $connection = "";
        }

        Capsule::connection($connection)->enableQueryLog();

        if (isset($params['trade_type']) && $params['trade_type']) {
            $trade_type_list = TradesService::getTradeType($params['trade_type']);
            $params['tradeTypeList'] = $trade_type_list;
        }

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        if (isset($params['_countdata']) && $params['_countdata'] == 1) {
            $fields = "count(oil_card_vice_trades.id) as total_count,sum(oil_card_vice_trades.trade_money) as total_money,
            sum(oil_card_vice_trades.fanli_money) as fanli_total_money,sum(oil_card_vice_trades.fanli_jifen) as fanli_total_jifen";
        } else {
//            $fields = 'oil_card_vice_trades.*,oil_card_vice.card_level,activeProvince.province,oil_provinces.province consume_region,
            $fields = 'oil_card_vice_trades.*,oil_card_vice.card_level,
                oil_type_no.oil_type,oil_org.is_test,oil_org.orgcode,
                oil_org.sub_org_name,oil_card_main.supplyer_id,ext.fanli_org_name,
                ext.fanli_orgcode, oil_station.station_operators_id,ext.aplownerid,ext.aplowneridname,ext.sale_email,
                ext.oil_centercode,ext.oil_centername,ext.data_from,mark.mark_supplier_id,mark.mark_supplier_name,
                ext.real_oil_num, ext.mac_price, ext.mac_amount, ext.upstream_settle_price,ext.upstream_settle_money, 
                ext.supplier_id,ext.area_id,ext.operator_id,
                rebate.straight_down_rebate,rebate.final_straight_down_rebate,rebate.after_rebate,rebate.final_after_rebate,
                rebate.is_final,rebate.down_cal_rebate,rebate.down_fanli_way,
                rebate.down_is_final, rebate.is_share,rebate.mark_rebate,ssp.supplier_name as supplier_supplier_name,
                ssp.cooperation_type,ext.charge_off_status,ext.can_invoice,ext.document_type,ext.original_order_id,
                oil_station.regions_id as station_regions_id';

            $params['left_join_oil_card_vice_trade_rebate'] = 1;
        }
        $sqlObj = OilCardViceTrades::getSqlObj($params, $fields, $connection)->dataRange('oil_card_vice_trades','oil_card_vice_trades_list');

        $limit = intval($params['limit']) > 0 ? intval($params['limit']) : 50;

        if (isset($params['_count']) || isset($params['count'])) {
            $result = $sqlObj->count();
            return $result;
        } elseif (isset($params['skip']) && isset($params['take'])) {
            $result = $sqlObj->orderBy('oil_card_vice_trades.trade_time', 'desc')->skip(intval($params['skip']))->take(intval($params['take']))->get();

            return self::preListData($result);
        } elseif (isset($params['_export']) && $params['_export'] == 1) {
            $sqlObj->orderBy('oil_card_vice_trades.trade_time', 'desc');

            $result = $sqlObj->get();
            return self::preListData($result);
        } elseif (isset($params['_countdata']) && $params['_countdata'] == 1) {
            $sumObj = OilCardViceTrades::getSqlObj($params, $fields, $connection, false)->dataRange('oil_card_vice_trades','oil_card_vice_trades_list');
            $sumCondition = $sumObj->wheres;
            $sumObj = OilCardViceTrades::joinTables($sumCondition, $sumObj);
            $result = $sumObj->get();
            return $result;
        } else {
            //todo 由于is_fanli = 0时，加上排序导致sql非常慢，暂时先去掉
            if (isset($params['is_fanli']) && $params['is_fanli'] != 0) {
                $sqlObj->orderBy('oil_card_vice_trades.trade_time', 'desc');
            }
            if($connection == 'foss_read_TiDb') {
                $countObj = OilCardViceTrades::getSqlObj($params, $fields, $connection, false)->dataRange('oil_card_vice_trades','oil_card_vice_trades_list');
                $condition = $countObj->wheres;
                $countObj = OilCardViceTrades::joinTables($condition, $countObj);
                $total = $countObj->count();
                $data = $sqlObj->orderBy("oil_card_vice_trades.trade_time", "desc")
                    ->skip(intval($params['start']))
                    ->take($limit)
                    ->get();
                // 创建分页器实例并设置总记录数
                $result = (new LengthAwarePaginator(
                    $data,          // 当前页的数据
                    $total,         // 总记录数
                    $limit,         // 每页显示的记录数
                    $params['page'] // 当前页码
                ))->toArray();
            } else {
                $result = $sqlObj->orderBy("oil_card_vice_trades.trade_time", "desc")->paginate($limit, ['*'], 'page', $params['page'])->toArray();
            }

        }

        $sql = Capsule::connection($connection)->getQueryLog();
        //print_r($sql);exit;
        Log::error('trades-sql:' . var_export($sql, TRUE), [], 'sql-trade');
        $result['data'] = self::preListData($result['data'], $show_charge_off_status);

        //return array("data"=>$result,"total_money"=>$total_money,"fanli_money"=>$fanli_money,"fanli_jifen"=>$fanli_jifen);
        return $result;
    }



    /**
     * 获取查询出的交易记录id
     *
     * @param array $params
     * @return string
     */
    public static function getTradesIds(array $params)
    {
        $fields = "oil_card_vice_trades.id";
        $params['oil_comIn'] = OilCom::getFanLiCalculate();
        $params['tradeTypeList'] = TradesType::getFanLiTradeType();
        $params['card_fromIn'] = CardFrom::getFanliCardFrom();
        $result = OilCardViceTrades::getSqlObj($params, $fields)->get();

        $ids = "";
        foreach ($result as $v) {
            $ids .= $ids ? "," . $v->id : $v->id;
        }

        return $ids;
    }

    /**
     * 通过交易地点获取查询出的交易记录id
     *
     * @param array $params
     * @return string
     */
    public static function getTradeIds(array $params)
    {
        $fields = "oil_card_vice_trades.id";
        $result = OilCardViceTrades::getSqlObj($params, $fields)->get();

        $ids = "";
        foreach ($result as $v) {
            $ids .= $ids ? "," . $v->id : $v->id;
        }

        return $ids;
    }

    /**
     * 列表数据预处理
     *
     * @param $data
     * @return mixed
     */
    public static function preListData($data, $show_charge_off_status = true)
    {
        if (count($data) > 0) {
            $supplyerMap = OilCardSupplyer::getSupplyerMap([]);
            //G7WALLET-5428
            $operator = OilStationOperators::getOperatorMapNew("operators_code", "operators_name");
            $topOrgCodeArr = [];
            $tradeFrom = CardTradeConf::$trade_from;
            $operatorNameMap = OilOperators::getIdMapName();
            $provinceDict = provinceDict();
            list(,$operator_id) = authCheck();
            foreach ($data as &$v) {
                $v->true_money = $v->trade_money - $v->use_fanli_money;
                $v->card_owner = str_replace(PHP_EOL, '', $v->card_owner);
                $v->qz_drivername = str_replace(PHP_EOL, '', $v->qz_drivername);
                $v->_oil_com = $v->oil_com;
                $oilCom = OilCom::getById($v->oil_com);
                $v->oil_com = $oilCom['oil_com'];
                $cardFrom = CardFrom::getById($v->card_from);
                $v->_card_from = $cardFrom ? $cardFrom['name'] : '';
                $card_level_value = CardViceConf::$card_level[$v->card_level];
                $v->card_level_value = isset($card_level_value) ? $card_level_value : '--';
                $v->top_org_name = '';
                $topOrgCode = substr($v->orgcode, 0, 6);
                $v->top_orgcode = $topOrgCode;
                $topOrgCodeArr[$topOrgCode] = $topOrgCode;
                switch ($v->is_fanli) {
                    case 1:
                        $v->is_fanli_txt = '已返';
                        break;
                    case 2:
                        $v->is_fanli_txt = '计算中';
                        break;
                    default:
                        $v->is_fanli_txt = '未算';
                }
                $v->supplyer_name = $supplyerMap[$v->supplyer_id] ? $supplyerMap[$v->supplyer_id] : '常规';
                $v->station_operators = isset($operator[$v->pcode]) ? $operator[$v->pcode] : '';
                $v->data_from_txt = isset($tradeFrom[$v->data_from]) ? $tradeFrom[$v->data_from] : '';

                $v->consumeFlag = 0;
                if (isset($v->pcode) && $v->pcode && in_array($v->pcode, PcodeConf::getAllPcode())) {
                    $v->consumeFlag = 1;
                    $v->trade_num = "--";
                    $v->trade_price = "--";
                    $v->xpcode_pay_price = "--";
                    $v->service_money = "--";
                }

                //内部公司统一查询调整
                //oo2.name supplier_operator_name,oo1.name oil_operator_name,oo.name main_operator_name
                $v->main_operator_name = isset($operatorNameMap[$v->main_operator_id]) ? $operatorNameMap[$v->main_operator_id] :'';
                $v->oil_operator_name  = isset($operatorNameMap[$v->org_operator_id]) ? $operatorNameMap[$v->org_operator_id] :'';
                $v->supplier_operator_name  = isset($operatorNameMap[$v->operator_id]) ? $operatorNameMap[$v->operator_id] :'';
                $v->supplier_relation_val  = self::getSupplierRelation($v->cooperation_type,$v);

                $v->charge_off_status_txt = $v->charge_off_status == ChargeOffStatus::CHARGE_OFFED ? '已核销' : '未核销';
//                 $v->charge_off_status_txt = $show_charge_off_status ? $v->charge_off_status_txt : '';
                //$v->can_invoice_txt = $v->can_invoice == CardTradeConf::CAN_INVOICE_YES ? '可开票' : '不可开票';
                $v->province = $v->active_region && array_key_exists($v->active_region,$provinceDict) ? $provinceDict[$v->active_region] : '';
                $v->consume_region = $v->station_regions_id && array_key_exists($v->station_regions_id,$provinceDict) ? $provinceDict[$v->station_regions_id] : '';
                self::prepareProfitData($v);
                $v->document_type_name = CardTradeConf::DOCUMENT_TYPE_DESC[$v->document_type] ?? '';

                //中油汇通字段处理
                if ($operator_id){
                    if ($operator_id != $v->org_operator_id){
                        $v->card_owner          = $v->qz_drivername         = $v->qz_drivertel = '';
                        $v->org_name            = $v->top_org_name          = $v->oil_operator_name;    //机构//顶级机构
                    }
                    if ($operator_id != $v->operator_id){
                        $v->station_operators   = $v->supplier_supplier_name= $v->supplier_relation_val = $v->supplier_operator_name;   //油站运营商//供应商//服务区/主卡运营商
                    }
                }
            }
            if ($topOrgCodeArr) {
                $topOrgRecords = OilOrg::getByOrgCodesMap(array_values($topOrgCodeArr));
                if ($topOrgRecords) {
                    foreach ($data as &$v) {
                        if ((!$operator_id || $v->org_operator_id == $operator_id) && isset($topOrgRecords[$v->top_orgcode])) {
                            $v->top_org_name = $topOrgRecords[$v->top_orgcode];
                        }
                    }
                }
            }
        }
        return $data;
    }


    /**
     * Desc: 查询供应商合作关系，分情况展示
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 21/7/22 下午5:43
     */
    public static function getSupplierRelation($cooperation_type,$v){
        //平台
        $supplier_relation_val = '';
        if($cooperation_type==10){
            $supplier_relation_val = $v->station_operators;
        }elseif($cooperation_type==20){
            $supplier_relation_val = $v->area_id ? \Models\OilStationArea::getResField(['id'=>$v->area_id],"name") : $v->area_id;
        }elseif($cooperation_type==30){
            $supplier_relation_val = $v->main_no;
        }
        return $supplier_relation_val;
    }

    public static function prepareProfitData(&$v)
    {
        self::formatUpstreamRebateMoney($v);

        $down_fanli = 0;
        if (strtotime($v->createtime) >= strtotime("2022-08-01")) {
            if (IsFinal::isRebateCalculated($v->down_is_final)) {

                $down_fanli = $v->down_cal_rebate;

                if($v->down_fanli_way == 1){
                    $v->fanli_money = $v->down_cal_rebate;
                    $v->fanli_jifen = '0.00';
                }else{
                    $v->fanli_money = '0.00';
                    $v->fanli_jifen = $v->down_cal_rebate;
                }
            } else {
                $v->fanli_money = $v->fanli_jifen = '--';
            }
        } else {
            if ($v->is_fanli != 0) {
                if ($v->fanli_way == 1) { // 现金
                    $down_fanli = $v->fanli_money;
                } else {
                    $down_fanli = $v->fanli_jifen;
                }
            } else {
                $v->fanli_money = $v->fanli_jifen = '--';
            }
        }

        if ($v->fanli_money != '--') {
            $v->fanli_money = sprintf('%.2f', round($v->fanli_money, 2));
        }

        if ($v->fanli_money != '--') {
            $v->fanli_money = sprintf('%.2f', round($v->fanli_money, 2));
        }

        $v->direct_total = sprintf('%.2f',round(bcsub($v->xpcode_pay_money, $v->total_money, 6),2));//下游直降总额
        $v->back_cost_money = round(bcsub($v->trade_money, $down_fanli, 6),2);//下游成本总额 = 结算总额 - 下游返利总额
        $v->back_cost_price = $v->real_oil_num > 0 ? sprintf('%.2f', bcdiv($v->back_cost_money, $v->real_oil_num, 6)) : '0.00';//下游成本单价
        $v->back_cost_money = sprintf('%.2f',$v->back_cost_money);

        $v->real_money = sprintf('%.2f', round(bcsub($v->back_cost_money, $v->cal_cost_money, 6),2));//实际盈利 = 下游成本总额 - 上游成本总额

        //
        $profit_val = ReceiptApplyInternal::getProfit($v->operator_id,$v->createtime);
        $_tmpFee = bcmul($v->real_money,$profit_val,6);

        $v->operator_fee = sprintf('%.2f',round(bcsub($v->back_cost_money, $_tmpFee, 6),2));//中游成本总额 = 下游成本总额 - 下游让利
        $v->operator_discount_fee = sprintf('%.2f',round(bcadd($down_fanli, $_tmpFee, 6),2));//折扣金额 = 下游后返现金 + 下游后返积分 + 实际盈利 * 60%
    }

    protected static function formatUpstreamRebateMoney(&$v)
    {
        // 上游返利
        $straight_down_rebate   = $v->straight_down_rebate + 0;
        $after_rebate           = $v->after_rebate + 0;
        $mark_rebate            = $v->mark_rebate + 0;
        if (IsFinal::isStable($v->is_final)) {
            $v->plummet_rebate_money = $straight_down_rebate = $v->final_straight_down_rebate;
            $v->later_rebate_money = $after_rebate= $v->final_after_rebate;
        } else {
            if (! IsFinal::isDownRebateCalculated($v->is_final)) {
                $v->plummet_rebate_money = $v->straight_down_rebate = '--';
                $straight_down_rebate = 0;
            } else {
                $v->plummet_rebate_money = $v->straight_down_rebate;
            }

            if (! IsFinal::isLaterRebateCalculated($v->is_final)) {
                $v->later_rebate_money = $v->after_rebate = '--';
                $after_rebate = 0;
            } else {
                $v->later_rebate_money = $v->after_rebate;
            }
        }
        if (empty($v->is_share) || $v->is_share == 0) {
            $v->mark_rebate = '--';
        } else {
            $v->mark_rebate = sprintf('%.2f', round($v->mark_rebate, 2));
        }

        if ($v->plummet_rebate_money != '--') {
            $v->plummet_rebate_money = sprintf('%.2f', round($v->plummet_rebate_money, 2));
        }

        // 直降成本总额、单价
        $v->down_cost_money = $down_cost_money = $v->mac_amount - $straight_down_rebate;
        $v->down_cost_money_price = bcdiv($v->down_cost_money, $v->real_oil_num, 6);
        $v->down_cost_money = sprintf('%.2f', round($v->down_cost_money, 2));
        $v->down_cost_money_price = sprintf('%.2f', round($v->down_cost_money_price, 2));

        if ($v->later_rebate_money != '--') {
            $v->later_rebate_money = sprintf('%.2f', round($v->later_rebate_money, 2));
        }

        //最终成本总额 = 直降成本总额 - 消费后返总额- 充值后返总额
        $v->cal_cost_money = $down_cost_money - $after_rebate - $mark_rebate;
        $v->cal_cost_price = bcdiv($v->cal_cost_money, $v->real_oil_num, 6);
        $v->cal_cost_money = sprintf('%.2f', round($v->cal_cost_money, 2));
        $v->cal_cost_price = sprintf('%.2f', round($v->cal_cost_price, 2));
    }

    /**
     * 油品副卡交易明细表 详情查询
     *
     * @param array $params
     * @return object
     */
    public static function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTrades::find($params['id']);
    }

    /**
     * 油品副卡交易明细表 详情查询
     *
     * @param array $params
     * @return object
     */
    public static function getInfoByIds(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardViceTrades::whereIn("id", $params['ids'])->get();
    }

    /**
     * 获取需要新版返利的消费记录
     *
     * @param array $params
     * @return object
     */
    public static function getTradeInfoById($trade_ids)
    {
        $sql = "SELECT ocvt.id,ocvt.org_id,ocvt.trade_price,ocvt.use_fanli_money, (ocvt.trade_money-ocvt.use_fanli_money) as trade_money,ocvt.vice_no,ocvt.trade_time ,ocvt.trade_num, ocvt.oil_com,ocvt.createtime,otn.oil_type as oil_type_id,ocm.supplyer_id as supplyer_id,ocm.id as main_id,os.id as station_id,os.station_operators_id,os.regions_id as regions_id
                FROM oil_card_vice_trades ocvt
                LEFT JOIN oil_card_vice ocv ON ocvt.vice_no = ocv.vice_no
                LEFT JOIN oil_card_main ocm ON ocv.card_main_id = ocm.id
                LEFT JOIN oil_station os ON ocvt.trade_place = os.station_name
                LEFT JOIN oil_type_no otn ON ocvt.oil_name = otn.oil_no
                WHERE ocvt.id in (" . $trade_ids . ") AND ocvt.is_fanli = 0 AND ocvt.trade_type in (" . TradesType::getFanLiTradeType(TRUE) . ") ORDER BY ocvt.createtime ASC";

        $data = Capsule::connection()->select($sql);
        return $data;
    }

    public static function getByIds(array $ids)
    {
        return OilCardViceTrades::select('oil_card_vice_trades.*', 'activeProvince.code as regions_code',
            'oil_type_no.oil_type', 'oil_org.orgcode', 'ext.original_order_id', 'ext.document_type', 'ext.ocr_truck_no_url')
            ->leftJoin('oil_provinces as activeProvince', 'activeProvince.id', '=', 'oil_card_vice_trades.regions_id')
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_card_vice_trades_ext as ext', 'ext.trades_id', '=', 'oil_card_vice_trades.id')
            ->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id');
            })
            ->whereIn('oil_card_vice_trades.id', $ids)
            ->get()
            ->toArray();
    }

    public static function getByApiId(array $params, $lock=true)
    {
        \helper::argumentCheck(['api_id'], $params);

        if ($lock) {
            return OilCardViceTrades::lockForUpdate()->where('api_id', strval($params['api_id']))->first();
        } else {
            return OilCardViceTrades::where('api_id', strval($params['api_id']))->first();
        }
    }

    /**
     * 获取一条交易记录
     * @param array $params
     * @return OilCardViceTrades
     */
    public static function getOneInfo(array $params)
    {
//        Capsule::connection()->enableQueryLog();
        $data =  OilCardViceTrades::Filter($params)->first();
//        $sql = Capsule::connection()->getQueryLog();
//        Log::error('$sql', [$sql], 'subscribe_trades_temp');
        return $data;
    }

    /**
     * 悲观锁查询
     *
     * @param array $params
     * @return object
     */
    public static function getByExtId(array $params)
    {
        \helper::argumentCheck(['extID'], $params);

        return self::lockForUpdate()->where('extID', $params['extID'])->first();
    }

    /**
     * 油品副卡交易明细表 新增
     *
     * @param array $params
     * @return mixed
     */
    public static function add(array $params)
    {
        return OilCardViceTrades::create($params);
    }

    /**
     * 油品副卡交易明细表 编辑
     *
     * @param array $params
     * @return mixed
     */
    public static function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardViceTrades::find($params['id'])->update($params);
    }

    public static function updateByFanLiNoForFanLiCal(array $params)
    {
        \helper::argumentCheck(['fanli_no'], $params);
        $fan_li_no = $params['fanli_no'];
        $params['fanli_no'] = NULL;

        return OilCardViceTrades::where('fanli_no', $fan_li_no)->update($params);
    }

    /**
     * 油品副卡交易明细表 根据ids删除或批量删除
     *
     * @param array $params
     * @return int
     */
    public static function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardViceTrades::destroy($params['ids']);
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilterOther($query, $params)
    {
        //Search By id
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        return $query;
    }

    static public function getCustomerList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        if (API_ENV == 'dev') {
            $connection = "";
        } else {
            $connection = "foss_read_TiDb";
        }

        $accountList = [];
        if (isset($params['orgcodeLk']) && $params['orgcodeLk'] != '') {
            $accountList = OilCustomerCard::getAccountList(['is_parent' => 1, "orgcode" => $params['orgcodeLk']]);
        }

        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $accountList = OilCustomerCard::getAccountList(["orgcode" => $params['orgcode']]);
        }

        if (count($accountList) > 0) {
            $params['account_nameIn'] = $accountList->toArray();
        } else {
            $emptyData['total'] = 0;
            $emptyData['per_page'] = 50;
            $emptyData['current_page'] = 1;
            $emptyData['last_page'] = 0;
            $emptyData['next_page_url'] = NULL;
            $emptyData['prev_page_url'] = NULL;
            $emptyData['from'] = NULL;
            $emptyData['to'] = NULL;
            $emptyData['data'] = [];
            return $emptyData;
        }
        unset($params['orgcode']);
        //Capsule::connection($connection)->enableQueryLog();
        $sqlObj = Capsule::connection($connection)->table("oil_card_vice_trades")
            ->leftJoin('oil_card_vice', 'oil_card_vice_trades.vice_no', '=', 'oil_card_vice.vice_no')
            ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice.org_id')
            ->leftJoin('oil_card_main', 'oil_card_vice.card_main_id', '=', 'oil_card_main.id')
            ->select(Capsule::connection($connection)->raw('oil_card_vice_trades.id,oil_card_vice_trades.vice_no,
            oil_card_vice_trades.trade_money,oil_card_vice_trades.balance,oil_card_vice_trades.trade_type,oil_card_vice_trades.oil_name,
            oil_card_vice_trades.trade_num,oil_card_vice_trades.trade_time,oil_card_vice_trades.createtime,oil_card_vice_trades.trade_place,
            oil_card_vice_trades.oil_com,oil_card_vice_trades.truck_no,oil_card_vice_trades.trade_price,oil_card_vice_trades.updatetime,
            oil_card_main.main_no,oil_org.orgcode,oil_org.org_name,GROUP_CONCAT(oil_card_vice_trades.id) as str_ids'))
            ->orderBy('oil_card_vice_trades.trade_time', 'desc');

        //G7WALLET-1666,排除重复的消费数据
        $sqlObj = $sqlObj->groupBy("oil_card_vice_trades.vice_no", "oil_card_vice_trades.trade_time",
            "oil_card_vice_trades.trade_money", "oil_card_vice_trades.trade_num", "oil_card_vice_trades.trade_price",
            "oil_card_vice_trades.trade_place");

        if (isset($params['tradetimeGe']) && $params['tradetimeGe'] != '') {
            $sqlObj->where('oil_card_vice_trades.trade_time', '>=', $params['tradetimeGe']);
        }

        if (isset($params['tradetimeLe']) && $params['tradetimeLe'] != '') {
            $tradetimeLe = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $params['tradetimeLe'], $matches) ? $params['tradetimeLe'] . ' 23:59:59' : $params['tradetimeLe'];
            $sqlObj->where('oil_card_vice_trades.trade_time', '<=', $tradetimeLe);
        }

        if (isset($params['tradetimeLne']) && $params['tradetimeLne'] != '') {
            $sqlObj->where('oil_card_vice_trades.trade_time', '<', $params['tradetimeLne']);
        }

        if (isset($params['tradetimeLt']) && $params['tradetimeLt'] != '') {
            $sqlObj->where('oil_card_vice_trades.trade_time', '<', $params['tradetimeLt']);
        }

        if (isset($params['tradetimeGt']) && $params['tradetimeGt'] != '') {
            $sqlObj->where('oil_card_vice_trades.trade_time', '>', $params['tradetimeGt']);
        }

        if (isset($params['createtimeGt']) && $params['createtimeGt'] != '') {
            $sqlObj->where('oil_card_vice_trades.createtime', '>', $params['createtimeGt']);
        }

        if (isset($params['createtimeLt']) && $params['createtimeLt'] != '') {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['createtimeLt']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $createtimeLe = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $params['createtimeLe'], $matches) ? $params['createtimeLe'] . ' 23:59:59' : $params['createtimeLe'];
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $createtimeLe);
        }

        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $viceNoArr = explode('|', $params['vice_no']);
            if (count($viceNoArr) == 1) {
                $sqlObj->where('oil_card_vice_trades.vice_no', '=', $params['vice_no']);
            } else {
                $sqlObj->whereIn('oil_card_vice_trades.vice_no', $viceNoArr);
            }
        }

        if (isset($params['main_no']) && $params['main_no'] != '') {
            $mainNoArr = explode('|', $params['main_no']);
            if (count($viceNoArr) == 1) {
                $sqlObj->where('oil_card_main.main_no', '=', $params['main_no']);
            } else {
                $sqlObj->whereIn('oil_card_main.main_no', $mainNoArr);
            }
        }

        if (isset($params['oil_comIn']) && $params['oil_comIn'] != '') {
            $sqlObj->whereIn('oil_card_vice_trades.oil_com', $params['oil_comIn']);
        }

        if (isset($params['account_nameIn']) && $params['account_nameIn'] != '') {
            $sqlObj->whereIn('oil_card_main.account_name', $params['account_nameIn']);
        }

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        foreach ($data as &$_item) {
            $_item->card_type_txt = "";
            if (isset($params['_export']) && $params['_export'] == 1) {
                $_item->vice_no = $_item->vice_no . "\t";
                $_item->main_no = $_item->main_no . "\t";
            }
            if (in_array($_item->oil_com, [OilCom::ZSH, OilCom::ZCW_ZSH_CYZYK])) {
                $_item->card_type_txt = "中石化";
            }
            if ($_item->oil_com == OilCom::ZSY) {
                $_item->card_type_txt = "中石油";
            }

            //G7WALLET-1666,排除重复的消费数据
            if (stripos($_item->str_ids, ",") !== false) {
                $ids = explode(",", $_item->str_ids);
                $tmpTrade = OilCardViceTrades::whereIn("id", $ids)->orderBy('createtime', "desc")->first();
                $_item->id = $tmpTrade->id;
                $_item->trade_type = $tmpTrade->trade_type;
                $_item->oil_name = $tmpTrade->oil_name;
                $_item->createtime = $tmpTrade->createtime;
                $_item->balance = $tmpTrade->balance;
            }
            //机构名称显示机构层级关系处理(又临时取消)
            /*if(isset($params['curent_orgcode']) && $params['curent_orgcode']){
                $_item->org_name = \Models\OilOrg::getOrgTreePart($params['curent_orgcode'],$_item->orgcode);
            }*/
        }
        //$sql = Capsule::connection($connection)->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }

    /**
     * getTradesMonth
     *
     * @param array $params
     * @return mixed
     */
    public static function getTradesMonth(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $year = isset($params['month']) && strlen($params['month']) > 5 ? substr($params['month'], 0, 4) : date("Y");
        $startTime = isset($params['month']) && $params['month'] ? $year . '-' . str_pad(substr($params['month'], 4, 2), 2, "0", STR_PAD_LEFT) . '-01 00:00:00' : date("Y-m-01", time()) . " 00:00:00";

        $endTime = date('Y-m-d', strtotime(date('Y-m-01', strtotime($startTime)) . ' +1 month -1 day')) . ' 23:59:59';

        $sqlObj = OilCardViceTrades::FilterOther($params)->where('trade_time', '>=', $startTime)
            ->where('trade_time', '<=', $endTime)
            ->where('org_id', '=', $params['org_id'])
            ->where(function ($query) {
                $query->where('trade_type', '!=', '圈存')
                    ->orWhere('trade_type', '!=', '圈提');
            })->select('id', 'trade_type as type', 'trade_money as money', 'vice_no', 'trade_time as optiontime')->orderBy('trade_time', 'desc');

        if ($params['type'] == 'money') {
            $data = $sqlObj->sum('trade_money');
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * getTradesAll
     *
     * @param array $params
     * @return array
     */
    public static function getTradesAll(array $params)
    {
        $pageSize = isset($params['limit']) && $params['limit'] ? $params['limit'] : 6;
        $pageNo = isset($params['page']) && $params['page'] ? $params['page'] : 1;

        $_startMonth = $pageSize * ($pageNo - 1);
        $startMonth = date("Ym", strtotime("-{$_startMonth} month"));

        $_endMonth = ($pageSize * $pageNo) - 1;
        $endMonth = date("Ym", strtotime("-{$_endMonth} month"));

        $where = isset($params['vice_no']) && $params['vice_no'] ? " and vice_no = '" . $params['vice_no'] . "'" : '';

        $record = Capsule::connection()->select("SELECT
	DATE_FORMAT(trade_time,'%Y%m') as month,sum(trade_money) as trade_money
FROM
	oil_card_vice_trades
WHERE
	org_id = {$params['org_id']}
AND (trade_type != '圈存' OR trade_type != '圈提')
{$where}
AND DATE_FORMAT(trade_time,'%Y%m') >= {$endMonth} AND DATE_FORMAT(trade_time,'%Y%m') <= {$startMonth}
GROUP BY DATE_FORMAT(trade_time,'%Y%m') ORDER BY month desc");

        $record_count = Capsule::connection()->select("SELECT
	DATE_FORMAT(trade_time,'%Y%m') as month,sum(trade_money) as trade_money
FROM
	oil_card_vice_trades
WHERE
	org_id = {$params['org_id']}
AND (trade_type != '圈存' OR trade_type != '圈提')
{$where}
GROUP BY DATE_FORMAT(trade_time,'%Y%m') ORDER BY month desc");

//        $_data = array_slice($record, ($pageNo - 1) * $pageSize, $pageSize);

        $data = [];
        $data['data'] = $record;
        $data['total'] = count($record);
        $data['from'] = 1;
        $data['to'] = ceil($data['total'] / $pageSize);
        $data['per_page'] = $pageSize;
        $data['current_page'] = $pageNo;
        $data['last_page'] = $data['to'];

        Log::info('getTradesAll', $data, 'oilCardViceTrades');

        return $data;
    }

    /**
     * getFanliAll 获取按月返利的统计
     *
     * @param array $params
     * @return array
     */
    public static function getFanliAll(array $params)
    {
        $pageSize = $params['limit'] ? $params['limit'] : 6;
        $pageNo = $params['page'] ? $params['page'] : 1;

        $_startMonth = $pageSize * ($pageNo - 1);
        $startMonth = date("Ym", strtotime("-{$_startMonth} month"));

        $_endMonth = ($pageSize * $pageNo) - 1;
        $endMonth = date("Ym", strtotime("-{$_endMonth} month"));

        $where = isset($params['vice_no']) && $params['vice_no'] ? " and oil_card_vice_trades.vice_no = '" . $params['vice_no'] . "'" : '';

        $record = Capsule::connection()->select("SELECT
	DATE_FORMAT(oil_card_vice_trades.trade_time,'%Y%m') as month,round(sum(oil_card_vice_trades.fanli_money),2) as fanli_money
FROM
	oil_card_vice_trades oil_card_vice_trades
LEFT JOIN oil_fanli_calculate oil_fanli_calculate ON oil_fanli_calculate.fanli_no = oil_card_vice_trades.fanli_no AND oil_fanli_calculate.status = 1
WHERE
	oil_card_vice_trades.fanli_no != ''
AND oil_card_vice_trades.policy_id != ''
AND oil_card_vice_trades.is_fanli = 1
AND oil_card_vice_trades.fanli_money > 0
AND oil_card_vice_trades.org_id = {$params['org_id']}
{$where}
AND DATE_FORMAT(trade_time,'%Y%m') >= {$endMonth} AND DATE_FORMAT(trade_time,'%Y%m') <= {$startMonth}
GROUP BY DATE_FORMAT(oil_card_vice_trades.trade_time,'%Y%m') ORDER BY month desc");

        $record_count = Capsule::connection()->select("SELECT
	DATE_FORMAT(oil_card_vice_trades.trade_time,'%Y%m') as month,round(sum(oil_card_vice_trades.fanli_money),2) as fanli_money
FROM
	oil_card_vice_trades oil_card_vice_trades
LEFT JOIN oil_fanli_calculate oil_fanli_calculate ON oil_fanli_calculate.fanli_no = oil_card_vice_trades.fanli_no AND oil_fanli_calculate.status = 1
WHERE
	oil_card_vice_trades.fanli_no != ''
AND oil_card_vice_trades.policy_id != ''
AND oil_card_vice_trades.is_fanli = 1
AND oil_card_vice_trades.fanli_money > 0
AND oil_card_vice_trades.org_id = {$params['org_id']}
{$where}
GROUP BY DATE_FORMAT(oil_card_vice_trades.trade_time,'%Y%m') ORDER BY month desc");

//        $_data = array_slice($record, ($pageNo - 1) * $pageSize, $pageSize);

        $data = [];
        $data['data'] = $record;
        $data['total'] = count($record_count);
        $data['from'] = 1;
        $data['to'] = floor($data['total'] / $pageSize);
        $data['per_page'] = $pageSize;
        $data['current_page'] = $pageNo;
        $data['last_page'] = $data['to'];

        return $data;
    }

    /**
     * getFanliAll 获取按月返利的统计
     *
     * @param array $params
     * @return array
     */
    public static function getFanliBlanceAll(array $params)
    {
        $pageSize = $params['limit'] ? $params['limit'] : 6;
        $pageNo = $params['page'] ? $params['page'] : 1;

        $_startMonth = $pageSize * ($pageNo - 1);
        $startMonth = date("Ym", strtotime("-{$_startMonth} month"));

        $_endMonth = ($pageSize * $pageNo) - 1;
        $endMonth = date("Ym", strtotime("-{$_endMonth} month"));

        $where = isset($params['vice_no']) && $params['vice_no'] ? " and oil_card_vice_trades.vice_no = '" . $params['vice_no'] . "'" : '';

        $record = Capsule::connection()->select("SELECT a.month,a.fanli_money,b.use_fanli_money FROM (
SELECT
	DATE_FORMAT(oil_card_vice_trades.trade_time,'%Y%m') as month,round(sum(oil_card_vice_trades.fanli_money),2) as fanli_money
FROM
	oil_card_vice_trades oil_card_vice_trades
LEFT JOIN oil_fanli_calculate oil_fanli_calculate ON oil_fanli_calculate.fanli_no = oil_card_vice_trades.fanli_no AND oil_fanli_calculate.status = 1
WHERE
	oil_card_vice_trades.fanli_no != ''
AND oil_card_vice_trades.policy_id != ''
AND oil_card_vice_trades.is_fanli = 1
AND oil_card_vice_trades.fanli_money > 0
AND oil_card_vice_trades.org_id = {$params['org_id']}
AND DATE_FORMAT(trade_time,'%Y%m') >= {$endMonth} AND DATE_FORMAT(trade_time,'%Y%m') <= {$startMonth}
GROUP BY month ORDER BY month desc) a
LEFT JOIN (
SELECT DATE_FORMAT(apply_time,'%Y%m') as month,round(sum(use_cash_fanli),2) as use_fanli_money FROM oil_account_assign
where
DATE_FORMAT(apply_time,'%Y%m') >= {$endMonth} AND DATE_FORMAT(apply_time,'%Y%m') <= {$startMonth}
AND org_id = {$params['org_id']}
GROUP BY month ORDER BY month desc
) b ON a.month = b.month");

        $record_count = Capsule::connection()->select("SELECT a.month,a.fanli_money,b.use_fanli_money FROM (
SELECT
	DATE_FORMAT(oil_card_vice_trades.trade_time,'%Y%m') as month,round(sum(oil_card_vice_trades.fanli_money),2) as fanli_money
FROM
	oil_card_vice_trades oil_card_vice_trades
LEFT JOIN oil_fanli_calculate oil_fanli_calculate ON oil_fanli_calculate.fanli_no = oil_card_vice_trades.fanli_no AND oil_fanli_calculate.status = 1
WHERE
	oil_card_vice_trades.fanli_no != ''
AND oil_card_vice_trades.policy_id != ''
AND oil_card_vice_trades.is_fanli = 1
AND oil_card_vice_trades.fanli_money > 0
AND oil_card_vice_trades.org_id = {$params['org_id']}
GROUP BY month ORDER BY month desc) a
LEFT JOIN (
SELECT DATE_FORMAT(apply_time,'%Y%m') as month,round(sum(use_cash_fanli),2) as use_fanli_money FROM oil_account_assign
where
org_id = {$params['org_id']}
GROUP BY month ORDER BY month desc
) b ON a.month = b.month");

//        $_data = array_slice($record, ($pageNo - 1) * $pageSize, $pageSize);

        $data = [];
        $data['data'] = $record;
        $data['total'] = count($record_count);
        $data['from'] = 1;
        $data['to'] = floor($data['total'] / $pageSize);
        $data['per_page'] = $pageSize;
        $data['current_page'] = $pageNo;
        $data['last_page'] = $data['to'];

        return $data;
    }


    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilterFanli($query, $params)
    {
        //Search By id
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('oil_card_vice_trades.vice_no', '=', $params['vice_no']);
        }

        return $query;
    }

    /**
     * getFanliMonth 获取年月的返利流水记录
     *
     * @param array $params
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function getFanliMonth(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $year = strlen($params['month']) > 5 ? substr($params['month'], 0, 4) : date("Y");
        $startTime = $params['month'] ? $year . '-' . str_pad(substr($params['month'], 4, 2), 2, "0", STR_PAD_LEFT) . '-01 00:00:00' : date("Y-m-01", time()) . " 00:00:00";

        $endTime = date('Y-m-d', strtotime(date('Y-m-01', strtotime($startTime)) . ' +1 month -1 day')) . ' 23:59:59';
        $sqlObj = OilCardViceTrades::FilterFanli($params)->where('oil_card_vice_trades.trade_time', '>=', $startTime)
            ->where('oil_card_vice_trades.trade_time', '<=', $endTime)
            ->leftJoin('oil_fanli_calculate', function ($join) {
                $join->on('oil_fanli_calculate.fanli_no', '=', 'oil_card_vice_trades.fanli_no')
                    ->where('oil_fanli_calculate.status', '=', 1);
            })->where('oil_card_vice_trades.org_id', '=', $params['org_id'])
            ->where("oil_card_vice_trades.fanli_no", '!=', '')
            ->where("oil_card_vice_trades.policy_id", '!=', '')
            ->where('oil_card_vice_trades.is_fanli', '=', 1)
            ->where('oil_card_vice_trades.fanli_money', '>', 0)
            ->select('oil_card_vice_trades.fanli_money', 'oil_card_vice_trades.trade_time', 'oil_card_vice_trades.trade_money', 'oil_card_vice_trades.vice_no', 'oil_card_vice_trades.regions_name')
            ->orderBy('oil_card_vice_trades.trade_time', 'desc');

        if ($params['type'] == 'money') {
            $data = $sqlObj->sum('fanli_money');
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * getFanliBlanceMonth 获取年月的返利和返利消费的综合数据
     *
     * @param array $params
     * @return array
     */
    public static function getFanliBlanceMonth(array $params)
    {
        $pageSize = $params['limit'] ? $params['limit'] : 10;
        $pageNo = $params['page'] ? $params['page'] : 1;

        $year = strlen($params['month']) > 5 ? substr($params['month'], 0, 4) : date("Y");
        $startTime = $params['month'] ? $year . '-' . str_pad(substr($params['month'], 4, 2), 2, "0", STR_PAD_LEFT) . '-01 00:00:00' : date("Y-m-01", time()) . " 00:00:00";

        $endTime = date('Y-m-d', strtotime(date('Y-m-01', strtotime($startTime)) . ' +1 month -1 day')) . ' 23:59:59';

        Capsule::connection()->enableQueryLog();
        $record = Capsule::connection()->select("SELECT
	a.*
FROM
	(
		(
			SELECT
				vice_no,
				(
					CASE
					WHEN fanli_jifen > 0 THEN
						round(fanli_jifen, 2)
					ELSE
						round(fanli_money, 2)
					END
				) AS fanli,
				1 AS status,
				1 as type,
				trade_time AS createtime
			FROM
				oil_card_vice_trades ocvt
			WHERE
				trade_time >= '" . $startTime . "' AND trade_time <= '" . $endTime . "'
			AND (
				fanli_jifen > 0.01
				OR fanli_money > 0.01
			)
			AND org_id = {$params['org_id']}
		)
		UNION
			(
				SELECT
					ocv.vice_no,
					oaa.use_cash_fanli AS fanli,
					oaa.status,
					2 as type,
					oaa.apply_time AS createtime
				FROM
				oil_account_assign oaa
				LEFT JOIN oil_account_assign_details oaad ON oaa.id = oaad.assign_id
				LEFT JOIN oil_card_vice ocv ON ocv.id = oaad.vice_id
				WHERE
					use_cash_fanli > 0
				AND oaa.apply_time >= '" . $startTime . "' AND oaa.apply_time <= '" . $endTime . "'
				AND ocv.org_id = {$params['org_id']}
				GROUP BY oaa.id
			)
	) a ORDER BY a.createtime DESC");

        Log::debug('getFanliBlanceMonth', Capsule::connection()->getQueryLog(), 'oilCardViceTrades');

        $_data = array_slice($record, ($pageNo - 1) * $pageSize, $pageSize);

        $data = [];
        $data['data'] = $_data;
        $data['total'] = count($record);
        $data['from'] = 1;
        $data['to'] = floor($data['total'] / $pageSize);
        $data['per_page'] = $pageSize;
        $data['current_page'] = $pageNo;
        $data['last_page'] = $data['to'];

        return $data;
    }

    public static function getMaxUpdateTime()
    {
        $sqlObj = Capsule::connection()->table('oil_card_vice_trades');
        $data = $sqlObj->select(Capsule::connection()->raw("max(updatetime) as max_time"))->whereIn('oil_com', OilCom::getFisrtType())->first();

        $max_time = $data->max_time ? date('Y-m-d H:i:s', strtotime('-5 min', strtotime($data->max_time))) : '1900-01-01 00:00:00';

        return $max_time;
    }

    /**
     * @title   此方法开票专用
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    static private function getSqlObjForOpenReceipt(array $params)
    {
        Log::error('receiptApply-$params' . var_export($params, TRUE), [], 'receiptApplyTmp');
        $sqlObj = OilCardViceTrades::leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice_trades.org_id')
            ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no')
            ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
            //->leftJoin('oil_card_vice_trades_ext', 'oil_card_vice_trades_ext.trades_id', '=', 'oil_card_vice_trades.id')
            //G7WALLET-6136->G7WALLET-6497
            //->where('oil_card_vice_trades.receipt_remain', '>', 0)
            ->whereRaw("if (oil_card_vice_trades.receipt_remain = 0, oil_card_vice_trades.createtime > '2024-12-17', 1 = 1 )")
            ->where('oil_card_vice_trades.trade_money', '!=', 0)
            ->where('oil_card_vice_trades.trade_num', '!=', 0)
            //->where('oil_card_main.is_open_invoice', 10)
            ->where(function ($query) {
                $query->where('oil_card_vice_trades.is_open_invoice','!=',10)->orWhereNull('oil_card_vice_trades.is_open_invoice');
            })
            ->whereRaw("if (oil_card_main.operators_id = 3, oil_card_vice_trades.createtime < '2020-03-02', if (oil_card_vice_trades.createtime > '2018-03-01' , oil_card_main.is_open_invoice = 10 , 1 = 1) )")
            //->where('oil_card_vice_trades.is_fanli', $params['is_fanli'])
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41])
            ->whereNotIn('oil_card_vice_trades.oil_com', [6, 7, 8])
//          ->whereIn('oil_card_vice_trades.trade_type', ['加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油','消费','优惠消费'])
            ->whereIn('oil_card_vice_trades.trade_type', TradesType::getCashAndSelfOilTradeTypeArr())
            ->whereNull("oil_card_vice_trades.cancel_sn");
//          >where('can_invoice', CardTradeConf::CAN_INVOICE_YES);
//          ->where("oil_card_vice_trades.is_open_invoice",IsOpenInvoiceForTrades::YES);

        $orgMainCardTradeLimit = true;
        if (isset($params['orgcode']) && $params['orgcode']) {
            $sqlObj->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');

            $orgRoot1 = substr($params['orgcode'], 0, 6);
            // G7WALLET-1672
            $orgMainCardTradeWhiteList = ReceiptConfig::getOrgTradeForMainCardWhiteList();
            if (in_array($orgRoot1, $orgMainCardTradeWhiteList)) {
                $orgMainCardTradeLimit = false;
            }

            //G7WALLET-4018
            /*if ( in_array(API_ENV, ['prod', 'pro']) ) {
                if (in_array($orgRoot1, ["2047TT"])) {
                    $sqlObj->whereNotIn('oil_card_vice_trades.oil_name', ["-20#柴油国六", "-35#柴油国六"]);
                }
            }else{
                if ( in_array($orgRoot1, ["200NW5"]) ) {
                    $sqlObj->whereNotIn('oil_card_vice_trades.oil_name', ["-10#柴油国六", "-30#柴油国六","-35#柴油国六"]);
                }
            }*/

        }

        if (isset($params['orgCodeNow']) && $params['orgCodeNow']) {
            $sqlObj->where('oil_org.orgcode', '=', $params['orgCodeNow']);

            $orgRoot1 = substr($params['orgCodeNow'], 0, 6);
            //G7WALLET-4018
            /*if ( in_array(API_ENV, ['prod', 'pro']) ) {
                if (in_array($orgRoot1, ["2047TT"])) {
                    $sqlObj->whereNotIn('oil_card_vice_trades.oil_name', ["-20#柴油国六", "-35#柴油国六"]);
                }
            }else{
                if ( in_array($orgRoot1, ["200NW5"]) ) {
                    $sqlObj->whereNotIn('oil_card_vice_trades.oil_name', ["-10#柴油国六", "-30#柴油国六","-35#柴油国六"]);
                }
            }*/

            $orgMainCardTradeWhiteList = ReceiptConfig::getOrgTradeForMainCardWhiteList();
            if (in_array($orgRoot1, $orgMainCardTradeWhiteList)) {
                $orgMainCardTradeLimit = false;
            }
        }

        //G7WALLET-1592
        if ($orgMainCardTradeLimit) {
            if (in_array(API_ENV, ['prod', 'pro'])) {
                $sqlObj = $sqlObj->whereRaw("if (oil_card_main.id in (2345), oil_card_vice_trades.trade_place_provice_code = '130000', 1 = 1 )");
            } else {
                $sqlObj = $sqlObj->whereRaw("if (oil_card_main.id in (361), oil_card_vice_trades.trade_place_provice_code = '130000', 1 = 1 )");
            }
        }

        global $app;
        //中石油的加油卡，支持天然气品类的开票,需求号：ENINET-3853
        if (isset($params['oil_type_eq'])) {
            $sqlObj->where('oil_type_no.oil_type', $params['oil_type_eq']);
            if ($params['oil_type_eq'] == OilType::GAS_YOU) {
                $orgRoot = strtoupper(substr($params['orgcode'], 0, 6));
                //因独立核算,使用申请开票的机构
                if (isset($params['orgCodeNow']) && empty($orgRoot)) {
                    $orgRoot = strtoupper(substr($params['orgCodeNow'], 0, 6));
                }
                if (!in_array($orgRoot, $app->config->receipt->gasOrgList)) {
                    $sqlObj->whereRaw("if (oil_card_vice_trades.oil_com = 2,oil_card_vice_trades.createtime >= '2020-08-01', 1 = 1)");
                }
            }
        }

        if (isset($params['org_id_list']) && $params['org_id_list']) {
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $params['org_id_list']);
        }

        if (isset($params['tradetimeLe']) && $params['tradetimeLe']) {
            $sqlObj->where('oil_card_vice_trades.trade_time', '<=', $params['tradetimeLe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        if (isset($params['createtimeLne']) && $params['createtimeLne']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['createtimeLne']);
        }

        if (isset($params['sign_lock_time']) && $params['sign_lock_time']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['sign_lock_time']);
        }

        if (isset($params['orgCodeNowIn']) && count($params['orgCodeNowIn']) > 0){
            $sqlObj->whereIn('oil_org.orgcode', $params['orgCodeNowIn']);
        }

        if (isset($params['notOrgCode']) && $params['notOrgCode']) {
            $sqlObj->where('oil_org.orgcode', '!=', $params['notOrgCode']);
        }

        if (isset($params['oil_type_list'])) {
            $sqlObj->whereIn('oil_type_no.oil_type', $params['oil_type_list']);
        }

        if (isset($params['oil_type_eq'])) {
            $sqlObj->where('oil_type_no.oil_type', $params['oil_type_eq']);
        }

        if (isset($params['oil_comIn']) && !empty($params['oil_comIn'])) {
            $sqlObj->whereIn('oil_card_vice_trades.oil_com', $params['oil_comIn']);
        }

        if( isset($params['receipt_operator_id']) && !empty($params['receipt_operator_id']) ){
            $sqlObj->where('oil_card_vice_trades.org_operator_id', $params['receipt_operator_id']);
        }

        $sqlObj->whereNotExists(function ($query) use ($params) {
            $query = $query->select('oil_card_vice_trades_ext.id')
                ->from('oil_card_vice_trades_ext')
                ->whereRaw('oil_card_vice_trades_ext.trades_id=oil_card_vice_trades.id')
                ->where('can_invoice', CardTradeConf::CAN_INVOICE_NO);
            if( isset($params['receipt_operator_id']) && !empty($params['receipt_operator_id']) ) {
                $query->where('oil_card_vice_trades.org_operator_id', $params['receipt_operator_id']);
            }
        });

        //由于消费撤销，会出现负的开票额度receipt_remain
        /*$sqlObj->where(function($sqlObj){
            $sqlObj->where("oil_card_vice_trades.is_open_invoice","!=",10)
                   ->orWhereNull("oil_card_vice_trades.is_open_invoice");
        });*/

        Log::error('receiptApply-sql' . $sqlObj->toSql(), [$sqlObj->getBindings()], 'receiptApplyTmp');
        return $sqlObj;
    }

    /**
     * @title   获取开票时需要的消费记录（开票专用）
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    public static function getListForOpenReceipt(array $params)
    {
        $data = self::getSqlObjForOpenReceipt($params);

        return $data->take(intval($params['take']))
            ->selectRaw('oil_card_vice_trades.*,oil_org.orgcode,oil_type_no.oil_type')
            ->orderBy('oil_card_vice_trades.id', "asc")
            ->get()
            ->toArray();
    }

    /**
     * @title   独立开票 获取开票时需要的消费记录（开票专用）
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    public static function getListForOpenReceiptForOnly(array $params, $orderField = "oil_card_vice_trades.org_id")
    {
        $data = self::getSqlObjForOpenReceipt($params);

        return $data->take(intval($params['take']))
            ->selectRaw('oil_card_vice_trades.*,oil_org.orgcode,oil_type_no.oil_type')
            ->orderBy($orderField, "asc")
            ->get()
            ->toArray();
    }

    /**
     * 计算消费记录的可开票金额
     *
     * @param array $params
     * @return mixed
     */
    public static function sumReceiptRemainByOrgIdList(array $params)
    {
        Log::error('params' . var_export($params, TRUE), [], 'eeeerrrr');
        $data = self::getSqlObjForOpenReceipt($params);

        Log::error('params-sql' . var_export($data->toSql(), TRUE), [], 'eeeerrrr');
        $data = $data->sum('oil_card_vice_trades.receipt_remain');

        return $data ? $data : 0;
    }

    /**
     * 根据交易时间和机构获取加油流水
     *
     * @param array $params
     * @return mixed
     */
    public static function getTradeList(array $params)
    {
        Log::error('params-sumReceiptRemainByOrgIdListNew' . var_export($params, TRUE), [], 'eeeerrrr');

        $sqlObj = OilCardViceTrades::leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice_trades.org_id');

        if (isset($params['oil_comIn']) && $params['oil_comIn']) {
            $sqlObj->whereIn('oil_card_vice_trades.oil_com', $params['oil_comIn']);
        }

        if (isset($params['orgcode']) && $params['orgcode']) {
            $sqlObj->where('oil_org.orgcode', $params['orgcode']);
        }

        if (isset($params['tradetimeLe']) && $params['tradetimeLe']) {
            $sqlObj->where('oil_card_vice_trades.trade_time', '<=', $params['tradetimeLe']);
        }

        if (isset($params['tradetimeGe']) && $params['tradetimeGe']) {
            $sqlObj->where('oil_card_vice_trades.trade_time', '>=', $params['tradetimeGe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['extIdNeqNull']) && $params['extIdNeqNull'] == 1) {
            $sqlObj->whereNotNUll('oil_card_vice_trades.extID');
        }
        return $sqlObj->get();
    }

    /**
     * 独立开票计算消费记录的可开票金额
     *
     * @param array $params
     * @return mixed
     */
    public static function sumReceiptRemainByOrgIdListNew(array $params)
    {
        Log::error('params-sumReceiptRemainByOrgIdListNew' . var_export($params, TRUE), [], 'eeeerrrr');

        $sqlObj = OilCardViceTrades::leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice_trades.org_id')
            ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no')
            ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
            ->whereRaw("if(oil_card_vice_trades.createtime > '2018-03-01',oil_card_main.is_open_invoice = 10,1=1)")
//                                   ->whereIn('oil_card_vice_trades.trade_type', ['加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油'])//TradesType::getFanLiTradeType()
            ->whereIn('oil_card_vice_trades.trade_type', TradesType::getCashAndSelfOilTradeTypeArr())
            ->whereNotIn('oil_card_vice_trades.oil_com', [6, 7, 8])
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41]);

        if (isset($params['org_id_list']) && $params['org_id_list']) {
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $params['org_id_list']);
        }

        if (isset($params['orgcode']) && $params['orgcode']) {
            $sqlObj->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');
        }

        if (isset($params['oil_type_list'])) {
            $sqlObj->whereIn('oil_type_no.oil_type', $params['oil_type_list']);
        }

        if (isset($params['oil_type_eq'])) {
            $sqlObj->where('oil_type_no.oil_type', $params['oil_type_eq']);
        }

        if (isset($params['tradetimeLe']) && $params['tradetimeLe']) {
            $sqlObj->where('oil_card_vice_trades.trade_time', '<=', $params['tradetimeLe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['is_fanli']) && $params['is_fanli'] == 1) {
            $sqlObj->where('oil_card_vice_trades.is_fanli', 1);
        }

        $total_trade_money = $sqlObj->sum('oil_card_vice_trades.receipt_remain');

        Log::error('params-sql' . var_export($sqlObj->toSql(), TRUE), [], 'eeeerrrr');
        return $total_trade_money ? $total_trade_money : 0;
    }


    public static function sumTradesByOrgIdList(array $params)
    {
        global $app;
        Capsule::connection()->disableQueryLog();
        Capsule::connection()->enableQueryLog();
        Log::error('params-sumTradesByOrgIdList' . var_export($params, TRUE), [], 'eeeerrrr');
        $sqlObj = Capsule::connection('foss_read_TiDb')->table('oil_card_vice_trades')
            ->from(Capsule::raw('`oil_card_vice_trades`'))
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            #->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice_trades.org_id')
            ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no')
            ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
//                         ->leftJoin('oil_card_vice_trades_ext', 'oil_card_vice_trades_ext.trades_id', '=', 'oil_card_vice_trades.id')
            //->whereRaw("if(oil_card_vice_trades.createtime > '2018-03-01',oil_card_main.is_open_invoice = 10,1=1)")
            ->whereRaw("if (oil_card_main.operators_id = 3, oil_card_vice_trades.createtime < '2020-03-02', if (oil_card_vice_trades.createtime > '2018-03-01' , oil_card_main.is_open_invoice = 10 , 1 = 1) )")
//                         ->whereIn('oil_card_vice_trades.trade_type', ['加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油'])//TradesType::getFanLiTradeType()
            ->whereIn('oil_card_vice_trades.trade_type', TradesType::getCashAndSelfOilTradeTypeArr())//TradesType::getFanLiTradeType()
            ->whereNotIn('oil_card_vice_trades.oil_com', [6, 7, 8])
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41])
            ->whereNull("oil_card_vice_trades.cancel_sn");
//                         ->where('can_invoice', CardTradeConf::CAN_INVOICE_YES);


        if (isset($params['org_id_list']) && $params['org_id_list']) {
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $params['org_id_list']);
        }

        $orgMainCardTradeLimit = true;
        if (isset($params['orgcode']) && $params['orgcode']) {
            $orgIds = OilOrg::getByOrgcodeLike($params['orgcode']);
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $orgIds);
            #$sqlObj->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');

            $orgRoot1 = substr($params['orgcode'], 0, 6);
            // G7WALLET-1672
            $orgMainCardTradeWhiteList = ReceiptConfig::getOrgTradeForMainCardWhiteList();
            if (in_array($orgRoot1, $orgMainCardTradeWhiteList)) {
                $orgMainCardTradeLimit = false;
            }

            //G7WALLET-4018
            /*if ( in_array(API_ENV, ['prod', 'pro']) ) {
                if (in_array($orgRoot1, ["2047TT"])) {
                    $sqlObj->whereNotIn('oil_card_vice_trades.oil_name', ["-20#柴油国六", "-35#柴油国六"]);
                }
            }else{
                if ( in_array($orgRoot1, ["200NW5"]) ) {
                    $sqlObj->whereNotIn('oil_card_vice_trades.oil_name', ["-10#柴油国六", "-30#柴油国六","-35#柴油国六"]);
                }
            }*/

        }

        //G7WALLET-1592
        if ($orgMainCardTradeLimit) {
            if (in_array(API_ENV, ['prod', 'pro'])) {
                $sqlObj = $sqlObj->whereRaw("if (oil_card_main.id in (2345), oil_card_vice_trades.trade_place_provice_code = '130000', 1 = 1 )");
            } else {
                $sqlObj = $sqlObj->whereRaw("if (oil_card_main.id in (361), oil_card_vice_trades.trade_place_provice_code = '130000', 1 = 1 )");
            }
        }

        //中石油的加油卡，支持天然气品类的开票,需求号：ENINET-3853
        if (isset($params['oil_type_eq'])) {
            $sqlObj->where('oil_type_no.oil_type', $params['oil_type_eq']);
            if ($params['oil_type_eq'] == OilType::GAS_YOU) {
                $orgRoot = strtoupper(substr($params['orgcode'], 0, 6));
                if (!in_array($orgRoot, $app->config->receipt->gasOrgList)) {
                    $sqlObj->whereRaw("if (oil_card_vice_trades.oil_com = 2,oil_card_vice_trades.createtime >= '2020-08-01', 1 = 1)");
                }
            }
        }

        if (isset($params['oil_type_list'])) {
            $sqlObj->whereIn('oil_type_no.oil_type', $params['oil_type_list']);
        }

        if (isset($params['oil_type_not_in'])) {
            $sqlObj->whereNotIn('oil_type_no.oil_type', $params['oil_type_not_in']);
        }

        if (isset($params['oil_type_neq'])) {
            $sqlObj->where('oil_type_no.oil_type', '!=', $params['oil_type_neq']);
        }

        if (isset($params['oil_comIn']) && !empty($params['oil_comIn'])) {
            $sqlObj->whereIn('oil_card_vice_trades.oil_com', $params['oil_comIn']);
        }

        if (isset($params['tradetimeLe']) && $params['tradetimeLe']) {
            $sqlObj->where('oil_card_vice_trades.trade_time', '<=', $params['tradetimeLe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        if (isset($params['createtimeLne']) && $params['createtimeLne']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['createtimeLne']);
        }

        if (isset($params['sign_lock_time']) && $params['sign_lock_time']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['sign_lock_time']);
        }

        if (isset($params['stat_split_time']) && $params['stat_split_time']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['stat_split_time']);
        }

        if (isset($params['receipt_operator_id']) && $params['receipt_operator_id']) {
            $sqlObj->where('oil_card_vice_trades.org_operator_id', '=', $params['receipt_operator_id']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe']);
        }

        /*if(isset($params['is_open_invoice'])){
            $sqlObj->where('oil_card_main.is_open_invoice',$params['is_open_invoice']);
        }*/

        if (isset($params['is_fanli']) && $params['is_fanli'] == 1) {
            $sqlObj->where('oil_card_vice_trades.is_fanli', 1);
        }

        Log::error('params-sumTradesByOrgIdList-sql' . var_export($sqlObj->toSql(), TRUE), [$sqlObj->getBindings()], 'eeeerrrr');
        $total_trade_money = $sqlObj->sum('oil_card_vice_trades.trade_money');

        Log::info('核销_total', [$sqlObj->toSql(), $sqlObj->getBindings()], 'charge_off');

        // 不可开票的消费额
        $trade_money_cant_invoice = $sqlObj->leftJoin('oil_card_vice_trades_ext', 'oil_card_vice_trades_ext.trades_id', '=', 'oil_card_vice_trades.id')
            ->where('can_invoice', CardTradeConf::CAN_INVOICE_NO)
            ->sum('oil_card_vice_trades.trade_money');

        Log::info('核销_cant_invoice', [$sqlObj->toSql(), $sqlObj->getBindings()], 'charge_off');

//        Log::error(__METHOD__,[$sqlObj->toSql(), $sqlObj->getBindings()],'receiptQuotaDebug2222');

        //$totalUseFanliMoney = $sqlObj->sum('oil_card_vice_trades.use_fanli_money');
        //$totalFanliMoney = $sqlObj->sum('oil_card_vice_trades.fanli_money');

        //Log::error(__METHOD__,[Capsule::connection()->getQueryLog()],'receiptQuotaDebug2222');

        Log::error('result', ['total_trade_money' => $total_trade_money ,'trade_money_cant_invoice'=> $trade_money_cant_invoice], 'eeeerrrr');

        return ['total_trade_money' => $total_trade_money - $trade_money_cant_invoice];
    }

    public static function sumGasTradesUseFanli(array $params)
    {
        $sqlObj = Capsule::connection('foss_read_TiDb')->table('oil_card_vice_trades')
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->from(Capsule::raw('`oil_card_vice_trades`'))
            #->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice_trades.org_id')
            ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no')
            ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
//                         ->leftJoin('oil_card_vice_trades_ext', 'oil_card_vice_trades_ext.trades_id', '=', 'oil_card_vice_trades.id')
            ->whereRaw("if (oil_card_main.operators_id = 3, oil_card_vice_trades.createtime < '2020-03-02', if (oil_card_vice_trades.createtime > '2018-03-01' , oil_card_main.is_open_invoice = 10 , 1 = 1) )")
            #->whereRaw("if(oil_card_vice_trades.createtime > '2018-03-01',oil_card_main.is_open_invoice = 10,1=1)")
//                         ->whereIn('oil_card_vice_trades.trade_type', ['加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油'])//TradesType::getFanLiTradeType()
            ->whereIn('oil_card_vice_trades.trade_type', TradesType::getCashAndSelfOilTradeTypeArr())//TradesType::getFanLiTradeType()
            ->whereNotIn('oil_card_vice_trades.oil_com', [6, 7, 8])
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41])
            ->whereNull("oil_card_vice_trades.cancel_sn");
//                         ->where('can_invoice', CardTradeConf::CAN_INVOICE_YES);

        if (isset($params['orgcode']) && $params['orgcode']) {
            $orgIds = OilOrg::getByOrgcodeLike($params['orgcode']);
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $orgIds);
            //$sqlObj->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');
        }

        if (isset($params['oil_type_list'])) {
            $sqlObj->whereIn('oil_type_no.oil_type', $params['oil_type_list']);
        }

        if (isset($params['oil_type_eq'])) {
            $sqlObj->where('oil_type_no.oil_type', $params['oil_type_eq']);
        }

        if (isset($params['oil_comIn']) && !empty($params['oil_comIn'])) {
            $sqlObj->whereIn('oil_card_vice_trades.oil_com', $params['oil_comIn']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        if (isset($params['createtimeLne']) && $params['createtimeLne']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['createtimeLne']);
        }

        if (isset($params['sign_lock_time']) && $params['sign_lock_time']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['sign_lock_time']);
        }

        if (isset($params['stat_split_time']) && $params['stat_split_time']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['stat_split_time']);
        }

        if (isset($params['receipt_operator_id']) && $params['receipt_operator_id']) {
            $sqlObj->where('oil_card_vice_trades.org_operator_id', '=', $params['receipt_operator_id']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe']);
        }
        $use_fanli_fee = $sqlObj->sum('oil_card_vice_trades.use_fanli_money');

        Log::error('核销_total', [$sqlObj->toSql(), $sqlObj->getBindings()], 'charge_off');

        // 不可开票消费使用的返利
        $use_rebate_can_not_invoice = $sqlObj->leftJoin('oil_card_vice_trades_ext', 'oil_card_vice_trades_ext.trades_id', '=', 'oil_card_vice_trades.id')
            ->where('can_invoice', CardTradeConf::CAN_INVOICE_NO)
            ->sum('oil_card_vice_trades.use_fanli_money');

        Log::error('核销_cant_invoice', [$sqlObj->toSql(), $sqlObj->getBindings(),$params], 'charge_off');

        $use_fanli_fee -= $use_rebate_can_not_invoice;

        return $use_fanli_fee;
    }

    public static function sumUseTradesByOrgIdList(array $params)
    {
        Log::error('params-sumUseTradesByOrgIdList' . var_export($params, TRUE), [], 'eeeerrrr');
        $sqlObj = OilCardViceTrades::leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice_trades.org_id')
            ->leftJoin('oil_card_main', 'oil_card_main.main_no', '=', 'oil_card_vice_trades.main_no')
            ->where('oil_card_vice_trades.is_fanli', 1)
            ->where('oil_card_vice_trades.trade_time', '<=', $params['tradetimeLe'])
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41]);

        if (isset($params['org_id_list']) && $params['org_id_list']) {
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $params['org_id_list']);
        }

        if (isset($params['orgcode']) && $params['orgcode']) {
            $sqlObj->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');
        }

        if (isset($params['oil_type_list'])) {
            $sqlObj->whereIn('oil_type_no.oil_type', $params['oil_type_list']);
        }

        if (isset($params['is_open_invoice'])) {
            $sqlObj->where('oil_card_main.is_open_invoice', $params['is_open_invoice']);
        }

//        Log::error('params-sumUseTradesByOrgIdList-sql' . var_export($sqlObj->toSql(), TRUE), [], 'eeeerrrr');
        $totalTradesMoney = $sqlObj->sum('oil_card_vice_trades.trade_money');
        $totalReceipt_remain = $sqlObj->sum('oil_card_vice_trades.use_fanli_money');

        Log::error('$totalTradesMoney' . var_export($totalTradesMoney, TRUE), [], 'eeeerrrr');
        Log::error('$totalReceipt_remain' . var_export($totalReceipt_remain, TRUE), [], 'eeeerrrr');

        $useTrades = $totalTradesMoney - $totalReceipt_remain;
        return $useTrades ? $useTrades : 0;
    }

    public static function sumFanliByOrgIdList(array $params)
    {
        Log::error('params-sumUseTradesByOrgIdList' . var_export($params, TRUE), [], 'eeeerrrr');
        $sqlObj = OilCardViceTrades::leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice_trades.org_id')
            ->where('oil_card_vice_trades.is_fanli', 1)
            ->where('oil_card_vice_trades.trade_time', '<=', $params['tradetimeLe'])
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41]);

        if (isset($params['org_id_list']) && $params['org_id_list']) {
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $params['org_id_list']);
        }

        if (isset($params['orgcode']) && $params['orgcode']) {
            $sqlObj->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');
        }

        if (isset($params['oil_type_list'])) {
            $sqlObj->whereIn('oil_type_no.oil_type', $params['oil_type_list']);
        }

        Log::error('params-sumFanliByOrgIdList-sql' . var_export($sqlObj->toSql(), TRUE), [], 'eeeerrrr');
        $totalFanliMoney = $sqlObj->sum('oil_card_vice_trades.fanli_money');

        return $totalFanliMoney ? $totalFanliMoney : 0;
    }

    /**
     * 计算消费记录的可开票金额
     *
     * @param array $params
     * @return mixed
     */
    public static function sumSnByOrgIdList(array $params)
    {
        $data = self::getSqlObjForOpenReceipt($params);

        $data = $data->sum('oil_card_vice_trades.sn');

        return $data ? $data : 0;
    }

    public static function sumUseFanLiByOrgIdList(array $params)
    {
        $data = self::getSqlObjForOpenReceipt($params);

        $data = $data->sum('oil_card_vice_trades.use_fanli_money');

        return $data ? $data : 0;
    }

    public static function synchronizeTradesTruckNo($value)
    {
        $result = OilCardViceTrades::where('vice_no', '=', $value['vice_no'])
            ->where('trade_time', '>=', $value['sendtime'])
            ->where('trade_time', '<', $value['backtime'])
            ->where('truck_no', '=', '')
            ->update(['truck_no' => $value['truck_no']]);

        return $result;
    }

    /**
     * 批量新增
     * batchInsert
     *
     * @param array $arr
     * @return bool
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function batchInsert(array $arr)
    {
        return Capsule::connection()->table('oil_card_vice_trades')->insert($arr);
    }

    /**
     * PDO批量添加
     *
     * @param string $tableName
     * @param array $data
     */
    public static function batchInsertByPdo($tableName, $apiData = [])
    {
        //批量入库
        $data = FALSE;
        if ($apiData) {
            $batchInsertSqlArr = [];

            $accountInfo = OilCardViceTrades::getAccountInfo();
            $oilComIn = OilCom::getFanLiCalculate();
            $tradeTypeList = TradesType::getFanLiTradeType();
            $card_fromIn = CardFrom::getFanliCardFrom();
            $condition['oil_com'] = $oilComIn;
            $condition['is_open_invoice'] = 20;
            $noReceiptMainNo = OilCardMain::getMainNo($condition);
            Log::error("不可开票主卡" . json_encode($noReceiptMainNo), [], "FanliDiscountRemain_");

            $logData = [];

            $oilTypeMap = OilTypeNo::oilTypeMap();

            foreach ($apiData as $v) {
                $fieldArr = [];
                $itemLog = $valuesArr = [];
                //todo 来消费后，释放消费记录的开票额度
                if ('oil_card_vice_trades' == $tableName) {
                    $orgRoot = "";
                    global $app;
                    $orgCode = OilOrg::getById(['id' => $v['org_id']]);
                    if ($orgCode) {
                        $orgRoot = substr($orgCode->orgcode, 0, 6);
                    }
                    // 如果是自授信扣费，查询返利动账记录

                    $v['receipt_remain'] = $v['trade_money'];
                    $accountOne = $accountInfo[$v['org_id']];
                    Log::error("标记时，机构账户返利扣减余额,新增时" . json_encode($accountOne), [], "FanliDiscountRemain_");
                    $oil_type = $oilTypeMap[$v['oil_name']];
                    Log::error("value", [$v], "FanliDiscountRemain_");
                    Log::error("oil_type", [$oil_type], "FanliDiscountRemain_");
                    Log::error("oil_type", [$v['oil_com']], "FanliDiscountRemain_");
                    //来一条消费记录标记消费记录的返利扣减余额 2019.2.19 txb modify
                    //返利只标记到天然气消费：G7WALLET-395
                    if (in_array($v['trade_type'], $tradeTypeList) && in_array($v['oil_com'], $oilComIn) && in_array($v['card_from'], $card_fromIn)
                        && $accountOne['fanli_discount_remain'] > 0 && !in_array($v["main_no"], $noReceiptMainNo)
                        && (($oil_type == 2 && !in_array($orgRoot, $app->config->customer->orgCodeGas)) || ($oil_type == 5 && in_array($v['oil_com'], OilCom::getAllFirstList())))
                        && bccomp($v['trade_money'], 10, 2) > 0
                    ) {
                        Log::error("come in", [$oil_type], "FanliDiscountRemain_");
                        //$accountInfo[$v['org_id']]['is_edited'] = 1;
                        $needCalFanli = true;
                        if (isset($v['use_fanli_money'])) {
                            $needCalFanli = false;
                            $use_fanli_money = $v['use_fanli_money'];
                        } else {
                            $xishu = self::getTradeRebate();
                            Log::error("Add:返利系数：" . $xishu . ",交易金额：" . $v['trade_money'] . ":api_id:" . $v['api_id'], [], "FanliDiscount_");
                            //由于数据数据库的use_fanli_money 保留2位，因此减返利扣减也需要保留一致的位数
                            $use_fanli_money = number_format(($v['trade_money'] * $xishu), 2, ".", "");
                        }

                        //ENINET-4833
                        if ('oil_card_vice_trades' == $tableName) {
                            $v['use_fanli_money'] = $needCalFanli ? 0 : $use_fanli_money;
                            $upRes = OilCardViceTrades::updateAccountFanliDiscount($use_fanli_money, $v['org_id'], '-');
                            if ($upRes) {
                                $v['use_fanli_money'] = $use_fanli_money;

                                $itemLog['trade_api_id'] = $v['api_id'];
                                $itemLog['org_id'] = $v['org_id'];
                                $itemLog['type'] = 1;
                                $itemLog['trade_money'] = $v['trade_money'];
                                $itemLog['createtime'] = $itemLog['updatetime'] = \helper::nowTime();
                                $itemLog['mark_fee'] = $use_fanli_money;
                            }
                        }

                        /*if ($accountOne['fanli_discount_remain'] >= $use_fanli_money) {
                            $v['use_fanli_money']                               = $use_fanli_money;
                            //$accountInfo[$v['org_id']]['fanli_discount_remain'] -= $use_fanli_money;
                            $itemLog['mark_fee']                                = $use_fanli_money;
                        } else {
                            $v['use_fanli_money']                               = $accountOne['fanli_discount_remain'];
                            //$accountInfo[$v['org_id']]['fanli_discount_remain'] = 0;
                            $itemLog['mark_fee']                                = $accountOne['fanli_discount_remain'];
                        }*/
                        $logData[] = $itemLog;
                        $v['receipt_remain'] = $v['trade_money'] - $v['use_fanli_money'];
                    }
                }
                Log::error("end in", [$v], "FanliDiscountRemain_");
                foreach ($v as $key => $val) {
                    $fieldArr[] = "`" . $key . "`";
                    $valuesArr[] = "'$val'";

                }
                $batchInsertSqlArr[] = "insert into $tableName (" . implode(",", $fieldArr) . ") values (" . implode(",", $valuesArr) . ")";
            }
            $batchInsertSql = implode(";", $batchInsertSqlArr);
            $data = Capsule::connection()->getPdo()->exec($batchInsertSql);

            //Log::error("标记后，机构账户返利扣减余额" . json_encode($accountInfo), [], "FanliDiscountRemain_");
            //更新机构的返利扣减余额
            //OilCardViceTrades::updateAccountMoney($accountInfo);

            if (count($logData) > 0) {
                //记录返利扣减标记记录
                OilMarkfanliremainLog::batchAdd($logData);
            }
        }

        return $data;
    }

    /*
     * 得到动态油价折扣
     */
    public static function getTradeRebate()
    {
        $rebate = [0.05, 0.06, 0.07, 0.08, 0.09, 0.1];

        $key = array_rand($rebate, 1);

        return $rebate[$key];
    }

    /**
     * PDO批量修改
     *
     * @param       $tableName
     * @param array $apiData
     * @return bool|int
     */
    public static function batchEditByPdo($tableName, $apiData = [])
    {
        //批量入库
        $data = FALSE;
        if ($apiData) {
            $batchSqlArr = [];

            $accountInfo = OilCardViceTrades::getAccountInfo();
            $oilComIn = OilCom::getFanLiCalculate();
            $tradeTypeList = TradesType::getFanLiTradeType();
            $card_fromIn = CardFrom::getFanliCardFrom();

            $logData = [];

            Log::error("标记前，机构账户返利扣减余额,更新" . json_encode($accountInfo), [], "FanliDiscountRemain_");

            foreach ($apiData as $v) {
                if (!isset($v['where'])) {
                    throw new \RuntimeException('where条件缺失', 6);
                }
                if ('oil_card_vice_trades' == $tableName) {

                    //来一条消费记录标记消费记录的返利扣减余额 2019.2.19 txb modify
                    if (in_array($v['trade_type'], $tradeTypeList) && in_array($v['oil_com'], $oilComIn) && in_array($v['card_from'], $card_fromIn)) {
                        //Log::error('$v.'.var_export($v,true),[],'tim');
                        $xishu = OilCardViceTrades::getTradeRebate();
                        Log::error("edit:返利系数：" . $xishu . ",交易金额：" . $v['trade_money'] . ",Api_id:" . $v['api_id'], [], "FanliDiscount_");
                        //由于数据数据库的use_fanli_money 保留2位，因此减返利扣减也需要保留一致的位数
                        $use_fanli_money = number_format(($v['trade_money'] * $xishu), 2, ".", "");
                        // 处理 ENINET-4833
                        if ($v['use_fanli_money'] < $use_fanli_money) {
                            $last_fanli = $v['use_fanli_money'] - $use_fanli_money;
                            $res = OilCardViceTrades::updateAccountFanliDiscount($last_fanli, $v['org_id'], "+");
                            if ($res) {
                                $v['use_fanli_money'] = $use_fanli_money;
                                $itemLog['trade_api_id'] = $v['api_id'];
                                $itemLog['org_id'] = $v['org_id'];
                                $itemLog['type'] = 2;
                                $itemLog['mark_fee'] = $last_fanli;
                                $itemLog['trade_money'] = $v['trade_money'];
                                $itemLog['createtime'] = $itemLog['updatetime'] = \helper::nowTime();
                                $logData[] = $itemLog;
                            }
                        }
                        /*if ($v['use_fanli_money'] < $use_fanli_money) {
                            $accountInfo[$v['org_id']]['is_edited'] = 1;
                            $v['use_fanli_money'] = $use_fanli_money;
                            $accountInfo[$v['org_id']]['fanli_discount_remain'] += ($v['use_fanli_money'] - $use_fanli_money);
                            $itemLog['trade_api_id'] = $v['api_id'];
                            $itemLog['org_id'] = $v['org_id'];
                            $itemLog['type'] = 2;
                            $itemLog['mark_fee'] = $v['use_fanli_money'] - $use_fanli_money;
                            $itemLog['trade_money'] = $v['trade_money'];
                            $itemLog['createtime'] = $itemLog['updatetime'] = \helper::nowTime();
                            $logData[] = $itemLog;
                        }*/
                    }

                }
                $fieldStr = '';
                $where = '';
                foreach ($v as $key => $val) {
                    if ($key != 'where') {
                        if ($key != 'createtime')
                            $fieldStr .= $fieldStr ? ",`" . $key . "`='$val'" : "`" . $key . "`='$val'";
                    } else
                        $where = $val;
                }
                $batchSqlArr[] = "UPDATE $tableName SET $fieldStr WHERE $where";
            }
            $batchSql = implode(";", $batchSqlArr);
            if ($tableName == 'oil_card_vice') {
                Log::info($batchSql, [], 'cardViceUpdateSql');
            }
            $data = Capsule::connection()->getPdo()->exec($batchSql);

            //Log::error("标记后，机构账户返利扣减余额,更新" . json_encode($accountInfo), [], "FanliDiscountRemain_");
            //更新机构的返利扣减余额
            //OilCardViceTrades::updateAccountMoney($accountInfo);

            if (count($logData) > 0) {
                //记录返利扣减标记记录
                OilMarkfanliremainLog::batchAdd($logData);
            }
        }

        return $data;
    }

    /**
     * 校验油站信息
     *
     * @param $ids
     */
    public static function checkTradePlace($ids)
    {
        $oilCom = \Fuel\Defines\OilCom::getFanLiCalculate();
        $oilComStr = implode(",", $oilCom);

        $data = Capsule::connection()->select("SELECT a.vice_no, a.oil_com, a.trade_place, a.trade_time
                FROM oil_card_vice_trades a
                LEFT JOIN oil_station b ON a.trade_place = b.station_name
                WHERE a.id in (" . $ids . ") AND a.is_fanli = 0 AND a.oil_com in (" . $oilComStr . ") AND a.trade_type in (" .
            \Fuel\Defines\TradesType::getFanLiTradeType(TRUE) . ") AND (b.id is null OR b.regions_id is NULL)
                GROUP BY a.oil_com,a.trade_place");
        if ($data) {
            $i = 0;
            $alertStr = '请先维护以下油站信息：<br>';
            foreach ($data as $val) {
                $tradeErr = [
                    'module' => '返利计算',
                    'data' => '未关联出交易地点',
                    'details' => '副卡号：' . $val->vice_no . ',交易时间：' . $val->trade_time . ',交易地点：' . $val->trade_place,
                    'createtime' => \helper::nowTime(),
                ];
                //添加错误日志
                OilErrorLogs::add($tradeErr);

                if ($i < 5) {
                    $oilCom = \Fuel\Defines\OilCom::getById($val->oil_com);
                    $alertStr .= $oilCom['oil_com'] . '--' . $val->trade_place . '<br>';
                }
                if ($i == 5) {
                    $alertStr .= '... ...';
                }
                $i++;
            }

            throw new \RuntimeException('加油站未维护' . $alertStr, 2);
        }
    }

    /**
     * 校验油品类型信息
     *
     * @param $ids
     */
    public static function checkOilType($ids)
    {
        $oilCom = \Fuel\Defines\OilCom::getFanLiCalculate();
        $oilComStr = implode(",", $oilCom);
        $data = Capsule::connection()->select("SELECT a.vice_no, a.trade_time, a.oil_name
                FROM oil_card_vice_trades a
                LEFT JOIN oil_type_no b ON a.oil_name = b.oil_no
                WHERE a.id in (" . $ids . ") AND a.is_fanli = 0 AND a.oil_com in (" . $oilComStr . ") AND a.trade_type in (" .
            \Fuel\Defines\TradesType::getFanLiTradeType(TRUE) . ") AND (b.oil_type is null OR b.oil_type = '')
                GROUP BY a.oil_name");
        if ($data) {
            $i = 0;
            $alertStr = '请先维护以下油品信息：<br>';
            foreach ($data as $val) {
                $typeErr = [
                    'module' => '返利计算',
                    'data' => '未关联出油品类型',
                    'details' => '副卡号：' . $val->vice_no . ',交易时间：' . $val->trade_time . ',油品：' . $val->oil_name,
                    'createtime' => \helper::nowTime(),
                ];
                //添加错误日志
                OilErrorLogs::add($typeErr);

                if ($i < 5) {
                    $alertStr .= $val->oil_name . '<br>';
                }
                if ($i == 5) {
                    $alertStr .= '... ...';
                }
                $i++;
            }

            throw new \RuntimeException('油品未维护' . $alertStr, 2);
        }
    }

    /**
     * 校验消费记录是否匹配多条返利政策
     *
     * @param $data
     */
    public static function checkMorePolicy($data)
    {
        if ($data) {
            $trades = [];
            $moreViceError = [];
            foreach ($data as $v) {
                $trades[$v->id][] = $v;
                if (count($trades[$v->id]) > 1) {
                    $moreViceError[] = $v->vice_no . '--' . $v->trade_time;
                }

                if (count($moreViceError) > 5) {
                    $moreViceError[] = '... ...';
                    break;
                }
            }

            if (count($moreViceError) > 0) {
                $alertStr = implode('<br>', $moreViceError);
                $content[] = "标题：返利计算异常\n";
                $content[] = "环境：" . API_ENV . "\n";
                $content[] = "原因：返利政策重复了\n";
                $content[] = "描述：> " . $alertStr . "\n";

                (new \Framework\DingTalk\DingTalkAlarm())
                    ->alarmToGroup('返利计算异常;', implode("", $content), [], TRUE);
                throw new \RuntimeException($alertStr, 3);
            }
        }
    }


    /**
     * @param $params
     * 消费记录是否强制使用索引
     */
    public static function forceIndex2Trades($params)
    {
        if (((isset($params['orgcode']) && !empty($params['orgcode'])) || (isset($params['vice_no']) && !empty($params['vice_no']))
                || (isset($params['pcode_in']) && !empty($params['pcode_in'])) || (isset($params['is_fanli']) && !empty($params['is_fanli']))
                || (isset($params['main_operators_id']) && !empty($params['main_operators_id']))) &&
            ((isset($params['tradetimeGe']) && !empty($params['tradetimeGe']) && isset($params['tradetimeLe']) && !empty($params['tradetimeLe'])) ||
                (isset($params['createtimeGe']) && !empty($params['createtimeGe']) && isset($params['createtimeLe']) && !empty($params['createtimeLe'])))) {
            return false;
        }
        return true;
    }

    /**
     * 获取返利计算的消费记录
     *
     * @param $ids
     * @return array
     */
    public static function fanliTrades($params)
    {
        $fields = "oil_card_vice_trades.id, oil_card_vice_trades.vice_no, oil_card_vice_trades.trade_price, oil_card_vice_trades.trade_money,
        oil_card_vice_trades.use_fanli_money, oil_card_vice_trades.trade_num, oil_card_vice_trades.trade_time,oil_card_vice_trades.oil_com,
        oil_card_vice_trades.createtime,oil_card_main.is_open_invoice,oil_card_vice.org_id_fanli,oil_card_vice.card_main_id, oil_station.regions_id,oil_type_no.oil_type";
        if (in_array(API_ENV, ['test', 'pro'])) {
            $dbObj = Capsule::connection('online_only_read');
        } else {
            $dbObj = Capsule::connection();
        }
        //$dbObj->enableQueryLog();
        $sqlObj = $dbObj->table('oil_card_vice_trades')
            ->leftJoin('oil_station', function ($query) {
                $query->on('oil_station.station_name', '=', 'oil_card_vice_trades.trade_place')->where('oil_station.is_del', '=', 0);
            })
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_card_main', 'oil_card_main.main_no', '=', 'oil_card_vice_trades.main_no')
            ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no')
            ->select(Capsule::raw($fields));

        $isForce = self::forceIndex2Trades($params); //修复：ENINET-4923

        if ($isForce) {
            //处理任务：ENINET-222,由于选择油品类型时,没用使用时间索引
            if (isset($params['tradetimeGe']) && !empty($params['tradetimeGe']) && isset($params['tradetimeLe']) && !empty($params['tradetimeLe'])) {
                $sqlObj->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`trade_time_dx`)'));
            } elseif (isset($params['createtimeGe']) && !empty($params['createtimeGe']) && isset($params['createtimeLe']) && !empty($params['createtimeLe'])) {
                $sqlObj->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`create_time_dx`)'));
            }
        }

        $oilCardViceTrades = new OilCardViceTrades();
        $sqlObj = $oilCardViceTrades->scopeFilter($sqlObj, $params);
        $data = $sqlObj->orderBy('oil_card_vice_trades.trade_time', 'asc')->get();
        //$sql = $dbObj->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }

    /**
     * 获取消费记录的返利政策
     *
     * @param $ids
     * @return array
     */
    public static function getPolicy($ids)
    {
        $sql = "SELECT ocvt.id, ocvt.vice_no, ocvt.trade_price, ocvt.trade_money,ocvt.use_fanli_money, ocvt.trade_num, ocvt.trade_time,ocvt.oil_com,ocm.is_open_invoice, ocv.org_id_fanli, os.regions_id, ofp.id policy_id, ofp.fanli_type, ofp.fanli_way, ofp.fanli_coe, ofp.fanli_money, ofp.coe_unit, ofp.step_fanli_data, ofp.oil_amount_limit, ofp.oil_money_limit,ofp.fanli_min_money,ofp.add_fanli_edu,ocvt.createtime
                FROM oil_card_vice_trades ocvt
                LEFT JOIN oil_card_vice ocv ON ocvt.vice_no = ocv.vice_no
                LEFT JOIN oil_card_main ocm ON ocv.card_main_id = ocm.id
                LEFT JOIN oil_station os ON ocvt.trade_place = os.station_name
                LEFT JOIN oil_type_no otn ON ocvt.oil_name = otn.oil_no
                LEFT JOIN oil_fanli_policy ofp ON ofp.is_del = 0 AND ofp.policy_object = 2 AND ofp.policy_oil_com = ocvt.oil_com
                AND ofp.oil_type = otn.oil_type AND ofp.org_id = ocv.org_id_fanli AND ofp.regions_id = os.regions_id AND ocvt.createtime >= ofp.start_time
                AND ocvt.createtime <= CONCAT(ofp.end_time, \" 23:59:59\") AND ofp.main_id = ocv.card_main_id
                WHERE ocvt.id in (" . $ids . ") AND ocvt.is_fanli = 0 AND ocvt.trade_type in (" . TradesType::getFanLiTradeType(TRUE) . ") AND ofp.id > 0 ORDER BY ocvt.trade_time ASC";

        $data = Capsule::connection()->select($sql);
//        Log::error('getPolicy--'.$sql,[],'fanliCal');

        return $data;
    }

    /**
     * 获取1号卡消费记录的返利政策
     *
     * @param $ids
     * @return array
     */
    public static function getFirstCardPolicy($ids)
    {
        $sql = "SELECT ocvt.id, ocvt.vice_no, ocvt.trade_price, ocvt.trade_money, ocvt.use_fanli_money,ocvt.trade_num, ocvt.trade_time,ocvt.oil_com,ocm.is_open_invoice, ocv.org_id_fanli, os.regions_id, ofp.id policy_id, ofp.fanli_type, ofp.fanli_way, ofp.fanli_coe, ofp.fanli_money, ofp.coe_unit, ofp.step_fanli_data, ofp.oil_amount_limit, ofp.oil_money_limit,ofp.fanli_min_money,ofp.add_fanli_edu,ocvt.createtime
                FROM oil_card_vice_trades ocvt
                LEFT JOIN oil_card_vice ocv ON ocvt.vice_no = ocv.vice_no
                LEFT JOIN oil_card_main ocm ON ocv.card_main_id = ocm.id
                LEFT JOIN oil_station os ON ocvt.trade_place = os.station_name
                LEFT JOIN oil_type_no otn ON ocvt.oil_name = otn.oil_no
                LEFT JOIN oil_fanli_policy ofp ON ofp.is_del = 0 AND ofp.policy_object = 2 AND ofp.policy_oil_com = ocvt.oil_com
                AND ofp.oil_type = otn.oil_type AND ofp.org_id = ocv.org_id_fanli AND ofp.station_id = os.id AND ocvt.createtime >= ofp.start_time
                AND ocvt.createtime <= CONCAT(ofp.end_time, \" 23:59:59\") AND ofp.main_id = ocv.card_main_id
                WHERE ocvt.id in (" . $ids . ") AND ocvt.is_fanli = 0 AND ocvt.trade_type in (" . TradesType::getFanLiTradeType(TRUE) . ") AND ofp.id > 0 ORDER BY ocvt.trade_time ASC";

        $data = Capsule::connection()->select($sql);

        return $data;
    }

    /**
     * 获取账户信息
     *
     * @return array
     */
    public static function getAccountInfo()
    {
        $accountMoney = OilAccountMoney::getAccountInfo();
        $accountInfo = [];
        foreach ($accountMoney as $v) {
            $accountInfo[$v->org_id]['fanli_discount_remain'] = $v->fanli_discount_remain;
            $accountInfo[$v->org_id]['is_edited'] = FALSE;//是否被修改
        }

        return $accountInfo;
    }

    /**
     * 处理"按金额"类型返利
     *
     * @param $val
     * @param $accountInfo
     * @return array
     */
    public static function fanliTypeByMoney($val, $accountInfo)
    {
        $result = [];
        //返现金
        if ($val->fanli_way == 1) {
            /****************** 处理现金返利循环问题 ************************/
            //如果返利机构资金账户的现金返利扣减余额大于0
            if ($val->is_open_invoice && isset($accountInfo) && $accountInfo && floatval($accountInfo['fanli_discount_remain']) > 0) {
                $accountInfo['is_edited'] = TRUE;
                //消费金额 <= 现金返利扣减余额
                if (floatval($val->trade_money) <= floatval($accountInfo['fanli_discount_remain'])) {
                    $result['use_fanli_money'] = floatval($val->trade_money);
                    $result['fanli_money'] = 0;
                    $accountInfo['fanli_discount_remain'] = floatval($accountInfo['fanli_discount_remain']) - floatval($val->trade_money);
                } else {
                    $result['use_fanli_money'] = floatval($accountInfo['fanli_discount_remain']);
                    $accountInfo['fanli_discount_remain'] = 0;
                    //计算（"消费金额" - "使用返利扣减余额"）这部分的返利
                    $result['fanli_money'] = (floatval($val->trade_money) - $result['use_fanli_money']) * floatval($val->fanli_coe) * 0.01;
                }
            } else {
                $result['fanli_money'] = (floatval($val->trade_money) - floatval($val->use_fanli_money)) * floatval($val->fanli_coe) * 0.01;
                //$result['use_fanli_money'] = 0;
            }

            //如果金额超过了数据库存储要求，则记录错误日志
            if ($result['fanli_money'] > ***********.99) {
                $noErr = [
                    'module' => '返利计算',
                    'data' => '返利金额超出数据库存储要求',
                    'details' => 'id：' . $val->id . '，返利金额:' . $result['fanli_money'],
                    'createtime' => \helper::nowTime(),
                ];
                if (!OilErrorLogs::add($noErr)) {
                    throw new \RuntimeException('计算失败：新增错误日志时失败', 1);
                }
            }
        }
        //返积分
        if ($val->fanli_way == 2) {
            $result['fanli_jifen'] = (floatval($val->trade_money) - floatval($val->use_fanli_money)) * floatval($val->fanli_coe) * 0.01;
            if ($result['fanli_jifen'] > ***********.99) {
                $noErr = [
                    'module' => '返利计算',
                    'data' => '返利积分超出数据库存储要求',
                    'details' => 'id：' . $val->id . '，返利积分:' . $result['fanli_jifen'],
                    'createtime' => \helper::nowTime(),
                ];
                if (!OilErrorLogs::add($noErr)) {
                    throw new \RuntimeException('计算失败：新增错误日志时失败', 1);
                }
            }
        }

        return ['result' => $result, 'accountInfo' => $accountInfo];
    }

    /**
     * 处理"按加油量"类型返利
     *
     * @param $val
     * @return array
     */
    public static function fanliTypeByJifen($val, $accountInfo)
    {
        $result = [];
        $checkStatus = 0;//1现金，2积分，3现金+积分
        //返现金
        if ($val->fanli_way == 1) {
            if ($val->is_open_invoice && isset($accountInfo) && $accountInfo && floatval($accountInfo['fanli_discount_remain']) > 0) {
                $accountInfo['is_edited'] = TRUE;
                //消费金额 <= 现金返利扣减余额
                if (floatval($val->trade_money) <= floatval($accountInfo['fanli_discount_remain'])) {
                    $result['use_fanli_money'] = floatval($val->trade_money);
                    $result['fanli_money'] = 0;
                    $accountInfo['fanli_discount_remain'] = floatval($accountInfo['fanli_discount_remain']) - floatval($val->trade_money);
                } else {
                    $result['use_fanli_money'] = floatval($accountInfo['fanli_discount_remain']);
                    $accountInfo['fanli_discount_remain'] = 0;
                    //计算（"消费金额" - "使用返利扣减余额"）这部分的返利
                    $result['fanli_money'] = (floatval($val->trade_money) - floatval($result['use_fanli_money'])) / floatval($val->trade_price) * floatval($val->fanli_money);
                }
            } else {
                $result['fanli_money'] = (floatval($val->trade_money) - floatval($val->use_fanli_money)) / floatval($val->trade_price) * floatval($val->fanli_money);
                //$result['use_fanli_money'] = 0;
            }

            if ($result['fanli_money'] > ***********.99) {
                $noErr = [
                    'module' => '返利计算',
                    'data' => '返利金额超出数据库存储要求',
                    'details' => 'id：' . $val->id . '，返利金额:' . $result['fanli_money'],
                    'createtime' => \helper::nowTime(),
                ];

                if (!OilErrorLogs::add($noErr)) {
                    throw new \RuntimeException('计算失败：新增错误日志时失败', 1);
                }

                $checkStatus = 1;
            }
        }
        //返积分
        if ($val->fanli_way == 2) {
            $result['fanli_jifen'] = (floatval($val->trade_money) - floatval($val->use_fanli_money)) / floatval($val->trade_price) * floatval($val->fanli_money);
            if ($result['fanli_jifen'] > ***********.99) {
                $noErr = [
                    'module' => '返利计算',
                    'data' => '返利积分超出数据库存储要求',
                    'details' => 'id：' . $val->id . '，返利积分:' . $result['fanli_jifen'],
                    'createtime' => \helper::nowTime(),
                ];

                if (!OilErrorLogs::add($noErr)) {
                    throw new \RuntimeException('计算失败：新增错误日志时失败', 1);
                }

                $checkStatus = $checkStatus == 1 ? 3 : 2;
            }
        }

        if ($checkStatus != 0) {
            throw new \RuntimeException('总额超出数据库存储要求', 1);
        }

        return ['result' => $result, 'accountInfo' => $accountInfo];
    }

    /**
     * 处理"按阶梯"类型返利
     *
     * @param $val
     * @param $accountInfo
     * @return array
     */
    public static function fanliTypeByStep($val, $accountInfo, $tradeData)
    {
        $result = [];
        //返现金
        if ($val->fanli_way == 1) {
            /****************** 处理现金返利循环问题 ************************/
            //如果返利机构资金账户的现金返利扣减余额大于0
            if ($val->is_open_invoice && isset($accountInfo) && $accountInfo && floatval($accountInfo['fanli_discount_remain']) > 0) {
                $accountInfo['is_edited'] = TRUE;//标记资金账户是否有修改
                //消费金额 <= 现金返利扣减余额
                if (floatval($val->trade_money) <= floatval($accountInfo['fanli_discount_remain'])) {
                    $result['use_fanli_money'] = floatval($val->trade_money);
                    $result['fanli_money'] = 0;
                    $accountInfo['fanli_discount_remain'] = floatval($accountInfo['fanli_discount_remain']) - floatval($val->trade_money);
                } else {
                    $result['use_fanli_money'] = floatval($accountInfo['fanli_discount_remain']);
                    $accountInfo['fanli_discount_remain'] = 0;
                    //计算（"消费金额" - "使用返利扣减余额"）这部分的返利
                    $money = floatval($val->trade_money) - $result['use_fanli_money'];
                    $result['fanli_money'] = self::getStepFanliMoney($val, $money, $tradeData);
                }
            } else {
                $result['fanli_money'] = self::getStepFanliMoney($val, ($val->trade_money - $val->use_fanli_money), $tradeData);
                //$result['use_fanli_money'] = 0;
            }

            //如果金额超过了数据库存储要求，则记录错误日志
            if ($result['fanli_money'] > ***********.99) {
                $noErr = [
                    'module' => '返利计算',
                    'data' => '返利金额超出数据库存储要求',
                    'details' => 'id：' . $val->id . '，返利金额:' . $result['fanli_money'],
                    'createtime' => \helper::nowTime(),
                ];
                if (!OilErrorLogs::add($noErr)) {
                    throw new \RuntimeException('计算失败：新增错误日志时失败', 1);
                }
            }
        }
        //返积分
        if ($val->fanli_way == 2) {
            $result['fanli_jifen'] = self::getStepFanliMoney($val, ($val->trade_money - $val->use_fanli_money), $tradeData);
            if ($result['fanli_jifen'] > ***********.99) {
                $noErr = [
                    'module' => '返利计算',
                    'data' => '返利积分超出数据库存储要求',
                    'details' => 'id：' . $val->id . '，返利积分:' . $result['fanli_jifen'],
                    'createtime' => \helper::nowTime(),
                ];
                if (!OilErrorLogs::add($noErr)) {
                    throw new \RuntimeException('计算失败：新增错误日志时失败', 1);
                }
            }
        }

        return ['result' => $result, 'accountInfo' => $accountInfo];
    }

    /**
     * @title   获取指定的季度时间
     * @desc
     * @param string $datetime 指定时间
     * @param bool $flag true 上一季度
     * @return array
     * @returns
     *                         []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     * @package Models
     */
    public static function getSessionTime($datetime, $flag = FALSE)
    {
        $year = substr($datetime, 0, 4);
        $season = ceil((date('n', strtotime($datetime))) / 3);//获取月份的季度
        if ($flag) {//取上一季度
            if ($season == 1) {
                $year = intval($year) - 1;
                $season = 4;
            } else {
                $season -= 1;
            }
        }
        $seasonArr = [
            '1' => [$year . '-01-01', $year . '-03-31 23:59:59'],
            '2' => [$year . '-04-01', $year . '-06-31 23:59:59'],
            '3' => [$year . '-07-01', $year . '-09-31 23:59:59'],
            '4' => [$year . '-10-01', $year . '-12-31 23:59:59'],
        ];

        $time = $seasonArr[$season];

        return $time;
    }

    /**
     * @title  统计阶梯返利消费数据的累计加油量
     * @param $info
     * @param $tradeMoney
     * @return int
     * <AUTHOR>
     */
    static private function getTotalData($start_time, $end_time, $vice_no, $isEq = FALSE, $priceList)
    {
        $tradeInfo = new \stdClass();
        $tradeInfo->trademoneytotal = 0;
        $tradeInfo->tradenumtotal = 0;

        foreach ($priceList as $key => $val) {
            foreach ($val as $timeKey => $price) {
                $tmp = explode("#", $timeKey);
                if (strtotime($tmp[1]) > strtotime($end_time)) {
                    $new_end_time = $end_time;
                } else {
                    $new_end_time = $tmp[1];
                }

                if (strtotime($tmp[0]) > strtotime($start_time)) {
                    $new_start_time = $start_time;
                } else {
                    $new_start_time = $tmp[0];
                }

                $fh = $isEq == TRUE ? "<=" : "<";
                $oneItem = self::where('oil_card_vice_trades.vice_no', $vice_no)
                    ->leftJoin("oil_station", "oil_card_vice_trades.trade_place", '=', 'oil_station.station_name')
                    ->where('oil_card_vice_trades.createtime', '>=', $new_start_time)
                    ->where('oil_card_vice_trades.createtime', $fh, $new_end_time)
                    ->where('oil_station.regions_id', $key)
                    ->where('oil_card_vice_trades.trade_price', '>', $price)
                    ->whereIn('oil_card_vice_trades.trade_type', TradesType::getFanLiTradeType())
                    ->select(Capsule::connection()->raw('SUM(trade_money - use_fanli_money) trademoneytotal,SUM((trade_money-use_fanli_money)/trade_price) tradenumtotal'))
                    ->first();
                $tradeInfo->trademoneytotal += $oneItem->trademoneytotal;
                $tradeInfo->tradenumtotal += $oneItem->tradenumtotal;
            }
        }
        /*$tradeList = OilCardViceTrades::getStepTradeList($vice_no,$start_time,$end_time,$isEq);
        //Log::error("获取累计消费记录".json_encode($tradeList),[],"fanliCal");
        if(count($tradeList) > 0) {
            foreach ($tradeList as $val) {
                if( !array_key_exists($val->regions_id,$priceList) ){
                    $tmpMoney = 0;
                    $tmpNum = 0;
                }else{
                    $timeData = $priceList[$val->regions_id];
                    foreach ($timeData as $timeKey => $miniPrice){
                        $tmp = explode("#",$timeKey);
                        Log::error("累计消费数据:".json_encode($val).",miniTime:".json_encode($tmp).",price:".$miniPrice,[],"fanliCal");
                        if( strtotime($val->createtime) >= strtotime($tmp[0]) && strtotime($val->createtime) <= strtotime($tmp[1]) && bccomp($val->trade_price,$miniPrice) > 0){
                            $tmpMoney = $val->trade_money - $val->use_fanli_money;
                            $tmpNum = $tmpMoney/$val->trade_price;
                        }
                    }
                }
                $tradeInfo->trademoneytotal += $tmpMoney;
                $tradeInfo->tradenumtotal += $tmpNum;
            }
        }*/

        return $tradeInfo;

    }

    /**
     * @title  获取阶梯返利金额
     * @param $info
     * @param $tradeMoney
     * @return int
     * <AUTHOR>
     */
    static private function getStepFanliMoney($info, $tradeMoney, $tradeData)
    {
        if ($tradeMoney == 0) {
            return 0;
        }
        Log::error('开始阶梯返利计算...', [], 'fanliCal');

        $sessionTime = self::getSessionTime($info->trade_time);

        Log::error('$sessionTime--' . var_export($sessionTime, TRUE), [], 'fanliCal');
        Log::error('$tradeMoney--' . $tradeMoney, [], 'fanliCal');
        $stepFanliData = json_decode($info->step_fanli_data, TRUE);


        //统计该条消费记录的累计消费,方案1，按卡累计
//        Capsule::connection()->enableQueryLog();
        $tradeInfo = self::where('vice_no', $info->vice_no)
            ->where('trade_time', '>=', $sessionTime[0])
            ->where('trade_time', '<', $info->trade_time)
            ->whereIn('trade_type', TradesType::getFanLiTradeType())
            ->whereNotNull("mini_trade_price")
            ->whereRaw('trade_price > mini_trade_price')
            ->select(Capsule::connection()->raw('SUM(trade_money - use_fanli_money) trademoneytotal,SUM((trade_money-use_fanli_money)/trade_price) tradenumtotal'))->first();
        //累计加油量计算方法变更，任务：YP-3698
        //$tradeInfo = self::getTotalData($sessionTime[0],$info->createtime,$info->vice_no,false,$priceList);

        Log::error('累计加油量：' . $sessionTime[0] . '-=' . $info->createtime . '#' . $info->vice_no . 'data:' . json_encode($tradeInfo), [], 'fanliCal');


//        Log::error('$tradeInfoSQL--' . var_export(Capsule::connection()->getQueryLog(), TRUE), [], 'fanliCal');
        //Log::error('累计消费信息--' . var_export($tradeInfo->toArray(), TRUE), [], 'fanliCal');

        if ($tradeData) {
            foreach ($tradeData as $v) {
                if ($info->vice_no == $v['vice_no'] && $v['trade_time'] >= $sessionTime[0] && $v['trade_time'] < $info->trade_time) {
                    $tradeInfo->trademoneytotal -= $v['use_fanli_money'];
                    $tradeInfo->tradenumtotal -= $v['use_fanli_money'] / $v['trade_price'];
                }
            }
        }
        $totalTrade = 0;
        if ($info->fanli_type == 3) {//现金阶梯返利（元）
            $totalTrade = $tradeInfo->trademoneytotal + $tradeMoney;
        } elseif ($info->fanli_type == 4) {                //油量阶梯返利（升）
            $tradeMoney = $tradeMoney / $info->trade_price;//转换为加油量
            $totalTrade = $tradeInfo->tradenumtotal + $tradeMoney;
        }

        $fanliMoney = self::getFanliMoneyForStep($info, $tradeMoney, $totalTrade, $stepFanliData);

        Log::error('累计消费--' . var_export($totalTrade, TRUE), [], 'fanliCal');
        Log::error('本次消费--' . var_export($tradeMoney, TRUE), [], 'fanliCal');
        Log::error('阶梯返利数据--' . var_export($stepFanliData, TRUE), [], 'fanliCal');
        Log::error('返利金额--' . var_export($fanliMoney, TRUE), [], 'fanliCal');

        return $fanliMoney;
    }

    /**
     * @title  获取阶梯返利金额
     * @param object $info
     * @param float $tradeMoney 交易金额 OR 加油量
     * @param float $totalTrade
     * @param array $stepFanliData
     * @return int|mixed
     * <AUTHOR>
     */
    static private function getFanliMoneyForStep($info, $tradeMoney, $totalTrade, $stepFanliData)
    {
        //获取阶梯返利数据
        $levelMoney = self::getStepFanli($tradeMoney, $totalTrade, $stepFanliData);
        Log::error('$levelMoney--' . var_export($levelMoney, TRUE), [], 'fanliCal');
        $fanliMoney = 0;
        $logStr = '';//记录日志
        if ($info->coe_unit == 1) {//按现金百分比
            $fanliMoney = ($levelMoney[1] * $stepFanliData['fanli_coe_level1'] + $levelMoney[2] * $stepFanliData['fanli_coe_level2'] + $levelMoney[3] * floatval($stepFanliData['fanli_coe_level3']) + $levelMoney[4] * floatval($stepFanliData['fanli_coe_level4']) + $levelMoney[5] * floatval($stepFanliData['fanli_coe_level5'])) * 0.01;

            $logStr .= '(' . $levelMoney[1] . '*' . $stepFanliData['fanli_coe_level1'] . '+' . $levelMoney[2] . '*' . $stepFanliData['fanli_coe_level2'] . '+' . $levelMoney[3] . '*' . floatval($stepFanliData['fanli_coe_level3']) . '+' . $levelMoney[4] . '*' . floatval($stepFanliData['fanli_coe_level4']) . '+' . $levelMoney[5] . '*' . floatval($stepFanliData['fanli_coe_level5']) . ')*' . 0.01;
            if ($info->fanli_type == 4) {//油量阶梯返利（升）
                $fanliMoney = $fanliMoney * $info->trade_price;
                $logStr .= '*' . $info->trade_price;
            }
        } elseif ($info->coe_unit == 2) {//按每升返利金额
            $fanliMoney = $levelMoney[1] * $stepFanliData['fanli_money_level1'] + $levelMoney[2] * $stepFanliData['fanli_money_level2'] + $levelMoney[3] * $stepFanliData['fanli_money_level3'] + $levelMoney[4] * $stepFanliData['fanli_money_level4'] + $levelMoney[5] * $stepFanliData['fanli_money_level5'];

            $logStr .= "($levelMoney[1] * $stepFanliData[fanli_money_level1] + $levelMoney[2] * $stepFanliData[fanli_money_level2] + $levelMoney[3] * $stepFanliData[fanli_money_level3] + $levelMoney[4] * $stepFanliData[fanli_money_level4] + $levelMoney[5] * $stepFanliData[fanli_money_level5])";
            if ($info->fanli_type == 3) {//现金阶梯返利（元）
                $fanliMoney = $fanliMoney / $info->trade_price;
                $logStr .= '/' . $info->trade_price;
            }
        }


        //若加油量叠加优惠填写了数值，则需要判断这张卡当前消费日期的前一个季度，这张油卡消费总油量是否大于等于2000升；
        //如果大于等于2000升，则需要在原有基础上，多返利一个百分比（或者多返利多少钱）。
        if (isset($info->add_fanli_edu) && $info->add_fanli_edu) {
            $sessionTime = self::getSessionTime($info->trade_time, TRUE);//获取上一季度的时间
            $tradeInfo = self::where('vice_no', $info->vice_no)
                ->where('trade_time', '>=', $sessionTime[0])
                ->where('trade_time', '<=', $sessionTime[1])
                ->whereIn('trade_type', TradesType::getFanLiTradeType())
                ->whereNotNull("mini_trade_price")
                ->whereRaw('trade_price > mini_trade_price')
                ->select(Capsule::connection()->raw('SUM(trade_num) tradenumtotal'))->first();

            //$tradeInfo = self::getTotalData($sessionTime[0],$sessionTime[1],$info->vice_no,true,$priceList);

            Log::error('累计加油量has#add_fanli_edu：' . $sessionTime[0] . '-=' . $sessionTime[1] . '#' . $info->vice_no . 'data:' . json_encode($tradeInfo), [], 'fanliCal');

            if ($tradeInfo->tradenumtotal >= 2000) {
                Log::error('触发返利叠加优惠------', [], 'fanliCal');
                $levelMoneyTotal = array_sum($levelMoney);
                if ($info->coe_unit == 1) {//按现金百分比
                    if ($info->fanli_type == 4) {//油量阶梯返利（升）
                        $levelMoneyTotal = $levelMoneyTotal * $info->trade_price;
                    }
                    $fanliMoney += $levelMoneyTotal * $info->add_fanli_edu * 0.01;
                    $logStr .= " + $levelMoneyTotal * $info->add_fanli_edu * 0.01";
                } elseif ($info->coe_unit == 2) {//按每升返利金额
                    if ($info->fanli_type == 3) {//现金阶梯返利（元）
                        $levelMoneyTotal = $levelMoneyTotal / $info->trade_price;
                    }
                    $fanliMoney += $levelMoneyTotal * $info->add_fanli_edu;
                    $logStr .= " + $levelMoneyTotal * $info->add_fanli_edu";
                }
            }
        }

        Log::error('计算过程--' . $logStr, [], 'fanliCal');

        return $fanliMoney;
    }

    /**
     * @title  现金阶梯返利
     * @param $tradeMoney
     * @param $totalTradeMoney
     * @param $stepFanliData
     * @return array
     * <AUTHOR>
     */
    static private function getStepFanli($tradeMoney, $totalTradeMoney, $stepFanliData)
    {
        $level5Money = $level4Money = $level3Money = $level2Money = $level1Money = 0;
        $level1Diff = $totalTradeMoney - $stepFanliData['fanli_level1_le'];
        $level2Diff = $totalTradeMoney - $stepFanliData['fanli_level2_le'];
        //txb 加上累计加油量不返的条件
        $level0Diff = $totalTradeMoney - $stepFanliData['fanli_level1_gt'];

        if ($level1Diff <= 0 && $level0Diff > 0) {//一级返利
            //$level1Money = min($tradeMoney, $stepFanliData['fanli_level1_le'] - $stepFanliData['fanli_level1_gt']);
            if ($level0Diff < $tradeMoney) {
                //$level0Money = $tradeMoney - $level0Diff;
                $level1Money = $level0Diff;
            } else {
                $level1Money = $tradeMoney;
            }
        } elseif ($level1Diff > 0 && $level2Diff <= 0) {//二级返利
            if ($level1Diff < $tradeMoney) {
                $level1Money = $tradeMoney - $level1Diff;
                $level2Money = $level1Diff;
            } else {
                $level2Money = $tradeMoney;
            }
        } elseif (!empty($stepFanliData['fanli_level3_gt']) && !empty($stepFanliData['fanli_level3_le'])) {//三级返利
            $level3Diff = $totalTradeMoney - $stepFanliData['fanli_level3_le'];
            if ($level2Diff > 0 && $level3Diff <= 0) {
                if ($level1Diff < $tradeMoney) {
                    $level1Money = $tradeMoney - $level1Diff;
                    $level2Money = $stepFanliData['fanli_level2_le'] - $stepFanliData['fanli_level2_gt'];
                    $level3Money = $totalTradeMoney - $stepFanliData['fanli_level3_gt'];
                } elseif ($level1Diff >= $tradeMoney && $level2Diff < $tradeMoney) {
                    $level2Money = $tradeMoney - $level2Diff;
                    $level3Money = $totalTradeMoney - $stepFanliData['fanli_level3_gt'];
                } elseif ($level2Diff >= $tradeMoney) {
                    $level3Money = $tradeMoney;
                }
            }

            if (!empty($stepFanliData['fanli_level4_gt']) && !empty($stepFanliData['fanli_level4_le'])) {//四级返利
                $level4Diff = $totalTradeMoney - $stepFanliData['fanli_level4_le'];
                if ($level3Diff > 0 && $level4Diff <= 0) {
                    if ($level1Diff < $tradeMoney) {
                        $level1Money = $tradeMoney - $level1Diff;
                        $level2Money = $stepFanliData['fanli_level2_le'] - $stepFanliData['fanli_level2_gt'];
                        $level3Money = $stepFanliData['fanli_level3_le'] - $stepFanliData['fanli_level3_gt'];
                        $level4Money = $totalTradeMoney - $stepFanliData['fanli_level4_gt'];
                    } elseif ($level1Diff >= $tradeMoney && $level2Diff < $tradeMoney) {
                        $level2Money = $tradeMoney - $level2Diff;
                        $level3Money = $stepFanliData['fanli_level3_le'] - $stepFanliData['fanli_level3_gt'];
                        $level4Money = $totalTradeMoney - $stepFanliData['fanli_level4_gt'];
                    } elseif ($level2Diff >= $tradeMoney && $level3Diff < $tradeMoney) {
                        $level3Money = $tradeMoney - $level3Diff;
                        $level4Money = $totalTradeMoney - $stepFanliData['fanli_level4_gt'];
                    } elseif ($level3Diff >= $tradeMoney) {
                        $level4Money = $tradeMoney;
                    }
                }

                if (!empty($stepFanliData['fanli_level5_gt']) && !empty($stepFanliData['fanli_level5_le'])) {//五级返利
                    $level5Diff = $totalTradeMoney - $stepFanliData['fanli_level5_le'];
                    if ($level4Diff > 0 && $level5Diff <= 0) {
                        if ($level1Diff < $tradeMoney) {
                            $level1Money = $tradeMoney - $level1Diff;
                            $level2Money = $stepFanliData['fanli_level2_le'] - $stepFanliData['fanli_level2_gt'];
                            $level3Money = $stepFanliData['fanli_level3_le'] - $stepFanliData['fanli_level3_gt'];
                            $level4Money = $stepFanliData['fanli_level4_le'] - $stepFanliData['fanli_level4_gt'];
                            $level5Money = $totalTradeMoney - $stepFanliData['fanli_level5_gt'];
                        } elseif ($level1Diff >= $tradeMoney && $level2Diff < $tradeMoney) {
                            $level2Money = $tradeMoney - $level2Diff;
                            $level3Money = $stepFanliData['fanli_level3_le'] - $stepFanliData['fanli_level3_gt'];
                            $level4Money = $stepFanliData['fanli_level4_le'] - $stepFanliData['fanli_level4_gt'];
                            $level5Money = $totalTradeMoney - $stepFanliData['fanli_level5_gt'];
                        } elseif ($level2Diff >= $tradeMoney && $level3Diff < $tradeMoney) {
                            $level3Money = $tradeMoney - $level3Diff;
                            $level4Money = $stepFanliData['fanli_level4_le'] - $stepFanliData['fanli_level4_gt'];
                            $level5Money = $totalTradeMoney - $stepFanliData['fanli_level5_gt'];
                        } elseif ($level3Diff >= $tradeMoney && $level4Diff < $tradeMoney) {
                            $level4Money = $tradeMoney - $level4Diff;
                            $level5Money = $totalTradeMoney - $stepFanliData['fanli_level5_gt'];
                        } elseif ($level4Diff >= $tradeMoney) {
                            $level5Money = $tradeMoney;
                        }
                    }
                }
            }
        }


        return [
            '1' => $level1Money,
            '2' => $level2Money,
            '3' => $level3Money,
            '4' => $level4Money,
            '5' => $level5Money,
        ];
    }

    /**
     * 获取交易记录数组
     *
     * @param $no
     * @param $val
     * @param $params
     */
    public static function getTradesRecord($no, $val, $params)
    {
        $backData = [
            'id' => $val->id,
            'fanli_no' => $no,
            'fanli_money' => $params['fanli_money'] ? $params['fanli_money'] : 0,
            //'use_fanli_money' => $params['use_fanli_money'] ? $params['use_fanli_money'] : 0,
            'fanli_jifen' => $params['fanli_jifen'] ? $params['fanli_jifen'] : 0,
            'policy_id' => empty($val->policy_id) ? NULL : $val->policy_id,
            'fanli_way' => empty($val->fanli_way) ? NULL : $val->fanli_way,
            'is_fanli' => 2,//已算(未审核)
            //'updatetime'  => \helper::nowTime(), //1105,认为返利计算不应该修改updatetime,特此注释
        ];

        return $backData;
    }

    /**
     * 更新交易记录为已算(未审核)和计算可开票金额
     *
     * @param $no
     * @throws Exception
     */
    public static function updateIsFanli($no, $ids)
    {
        $sql[] = "UPDATE oil_card_vice_trades SET fanli_no='$no',is_fanli=2 WHERE id IN ($ids) AND is_fanli=0";
        #$sql[] = "UPDATE oil_card_vice_trades a
        #    INNER JOIN oil_card_vice b ON a.vice_no = b.vice_no AND  a.is_open_invoice IS NULL
        #    INNER JOIN oil_card_main c ON b.card_main_id = c.id AND c.is_open_invoice = 10
        #    SET a.receipt_remain=a.trade_money - a.use_fanli_money WHERE a.id IN ($ids)";
        Log::info('sql--' . implode(';', $sql), [], 'updateIsFanli');
        $flag = Capsule::connection()->getPdo()->exec(implode(';', $sql));
        if ($flag === FALSE) {
            throw new \RuntimeException('计算失败：回写副卡交易记录时失败', 1);
        }
    }

    public static function updateByFanliNo(array $params)
    {
        \helper::argumentCheck(['fanli_no'], $params);
        self::where('fanli_no', $params['fanli_no'])->update($params);
    }

    public static function updateFanliLevelByStation(array $params)
    {
        \helper::argumentCheck(['station_code'], $params);
        $station_code = $params['station_code'];
        unset($params['station_code']);
        self::where('station_code', $station_code)->whereIn("oil_com", OilCom::getAllFirstList())->update($params);
    }

    /**
     * @title    更新交易记录为已算
     * @desc
     * @param $code
     * @param $name
     * @param $station_name
     * @return bool
     * @returns
     * bool
     * @returns
     * @version  1.0.0
     * <AUTHOR> @package  Models
     * @since
     * @params   type filedName required?
     */
    public static function updateCodeName($code, $name, $station_name)
    {
        $tradesIds = OilCardViceTrades::where('trade_place', $station_name)
            ->where('station_code', '<>', '')
            ->pluck('id')->toArray();

        if (count($tradesIds) > 0) {
            OilCardViceTrades::where('trade_place', $station_name)->update([
                'trade_place_provice_code' => $code,
                'trade_place_provice_name' => $name,
            ]);
            //修改返利计算临时表
            OilUpstreamRebateTradesTemp::where('trade_place', $station_name)
                ->update([
                    'trade_place_provice_code' => $code,
                    'trade_place_provice_name' => $name,
                ]);
            //修改能源看数基础统计表
            OilTrades::where('station_name', $station_name)->update([
                'province_code' => $code,
                'province_name' => $name,
            ]);
            //to gos
            CardViceTradesToGos::sendBatchUpdateTask($tradesIds, 'sync');
        }

        return TRUE;

//        $flag = Capsule::connection()->getPdo()->exec("UPDATE oil_card_vice_trades SET trade_place_provice_code='$code',trade_place_provice_name='$name' WHERE trade_place IN ('$ids')");
//        if ($flag === FALSE) {
//            throw new \RuntimeException('计算失败：回写副卡交易记录时失败', 1);
//        }
//        return $flag;
    }

    /**
     * 回写现金返利扣减余额至机构资金账户表
     *
     * @param $accountInfo
     */
    public static function updateAccountMoney($accountInfo)
    {
        $updateAccountMoneyArr = [];
        foreach ($accountInfo as $k => $v) {
            if ($v['is_edited']) {
                $updateAccountMoneyArr[] = "UPDATE oil_account_money SET fanli_discount_remain=" . $v['fanli_discount_remain'] . " WHERE org_id=" . intval($k);
            }
        }
        if ($updateAccountMoneyArr) {
            $sql = implode(';', $updateAccountMoneyArr);
            Capsule::connection()->getPdo()->exec($sql);
        }
    }

    /**
     * 更新现在账户的返利扣减
     * @param array $remainList
     */
    public static function updateAccountFanliDiscount($discount_fee = 0, $org_id = 0, $operator_type = "-")
    {
        Log::error("updateAccountFanliDiscount-params" . var_export($discount_fee, true), [$org_id], "fanliDiscountRemain_");
        if (empty($org_id)) {
            return false;
        }
        $subStr = "fanli_discount_remain " . $operator_type . $discount_fee;
        $updateAccountMoneySql = "";
        $updateAccountMoneySql = "UPDATE oil_account_money SET fanli_discount_remain = " . $subStr . " WHERE " . $subStr . " >= 0 and  org_id = " . intval($org_id);

        Log::error("updateAccountFanliDiscount-sql" . var_export($updateAccountMoneySql, true), [$org_id], "fanliDiscountRemain_");

        return Capsule::connection()->getPdo()->exec($updateAccountMoneySql);

    }

    /**
     * 缓存返利计算使用的政策
     *
     * @param $no
     * @param $params
     * @param $ids
     * @return string
     */
    public static function cacheFanliPolicy(array $params)
    {
        $policyParams['_export'] = 1;
        $policyParams['is_del'] = '';
        $policyParams['policy_object'] = 2;
        $policyParams['policy_oil_comIn'] = !is_array($params['oil_com_in']) ? explode(",", $params['oil_com_in']) : $params['oil_com_in'];
        $policyParams['policy_end_time'] = date("Y-m-01", strtotime($params['createtimeGe']));
        $policyData = OilFanliPolicy::getPolicy($policyParams);
        $policyList = [];
        if (count($policyData) > 0) {
            foreach ($policyData as $item) {
                //main_id#oil_com#oil_type#org_id_fanli#regions_id
                $_key = $item->main_id . "#" . $item->policy_oil_com . "#" .
                    $item->oil_type . "#" . $item->org_id . "#" . $item->regions_id;
                //"#".$item->start_time."#".$item->end_time." 23:59:59";
                $_val = [
                    "policy_id" => $item->id,
                    "fanli_type" => $item->fanli_type,
                    "fanli_way" => $item->fanli_way,
                    "fanli_coe" => $item->fanli_coe,
                    "fanli_money" => $item->fanli_money,
                    "coe_unit" => $item->coe_unit,
                    "step_fanli_data" => $item->step_fanli_data,
                    "oil_amount_limit" => $item->oil_amount_limit,
                    "oil_money_limit" => $item->oil_money_limit,
                    "fanli_min_money" => $item->fanli_min_money,
                    "add_fanli_edu" => $item->add_fanli_edu,
                    "start_time" => $item->start_time,
                    "end_time" => $item->end_time . " 23:59:59",
                ];
                $cacheKey = "POLICY-" . $_key;
                //Cache::forget($cacheKey);
                //Cache::put($cacheKey, json_encode($_val), 60 * 60 * 24);
                $policyList[$_key][] = $_val;
            }
        }
        return $policyList;
    }

    /**
     * 新方式执行返利计算
     *
     * @param $no
     * @param $params
     * @param $ids
     * @return string
     */
    public static function executeNew($no, $params)
    {
        Log::error('开始返利计算...', [], 'jobOk');
        Log::error('$params04--' . var_export($params, TRUE), [], 'fanliNewCal');

        $policyList = OilCardViceTrades::cacheFanliPolicy($params);
        if (count($policyList) == 0) {
            Log::error('无返利政策-继续标记消费记录', [], 'fanliNewCal');
            //return true;
        }
        Log::error('返利政策条数：' . count($policyList), [], 'fanliNewCal');
        //根据条件取出消费记录
        //unset($params['oil_com_in']);
        $tradeList = OilCardViceTrades::fanliTrades($params);
        Log::error('消费记录条数：' . count($tradeList), [], 'fanliNewCal');
        if (count($tradeList) > 0) {
            $noPolicy_ids = [];
            $all_trade_ids = [];
            $policyCalData = [];
            foreach ($tradeList as $item) {
                $trade_id = $item->id;
                $all_trade_ids[] = $trade_id;
                $policy = "";
                $region_id = $item->regions_id ? $item->regions_id : 0;
                $_key = $item->card_main_id . "#" . $item->oil_com . "#" .
                    $item->oil_type . "#" . $item->org_id_fanli . "#" . $region_id;
                Log::error("key：" . $_key, [$trade_id], "fanliNewCal");
                Log::error("key：" . $_key, [$policyList[$_key]], "fanliNewCal");
                if (array_key_exists($_key, $policyList) && count($policyList[$_key]) > 0) {
                    $policyData = $policyList[$_key];
                    if (count($policyData) > 0) {
                        foreach ($policyData as $_subItem) {
                            if (strtotime($item->createtime) >= strtotime($_subItem['start_time']) &&
                                strtotime($item->createtime) <= strtotime($_subItem['end_time'])) {
                                $policyCalData[$item->id] = array_merge((array)$item, $_subItem);
                            } else {
                                $noPolicy_ids[$trade_id] = $trade_id;
                                //todo 报警
                                //$noTimePolicy[] = $item->vice_no . '--' . $item->trade_time;
                            }
                        }
                    } else {
                        $noPolicy_ids[$trade_id] = $trade_id;
                    }
                } else {
                    $noPolicy_ids[$trade_id] = $trade_id;
                }
                /*$policy = Cache::get("POLICY-" . $_key);
				Log::error('_key' . $_key . ',policy:' . var_export($policy, TRUE), [], 'fanliCal');
				if (empty($policy)) {
					$noPolicy_ids[$trade_id] = $trade_id;
					//todo 报警
					//$noPolicy[] = $item->vice_no . '--' . $item->trade_time;
				} else {
					$policyData = json_decode($policy, true);
					//echo $item->createtime.'-=-'.$policyData['start_time']."====".$policyData['end_time']."\r\n";
					if (strtotime($item->createtime) >= strtotime($policyData['start_time']) &&
						strtotime($item->createtime) <= strtotime($policyData['end_time'])) {
						$policyCalData[$item->id] = array_merge((array)$item, $policyData);
					} else {
						$noPolicy_ids[$trade_id] = $trade_id;
						//todo 报警
						//$noTimePolicy[] = $item->vice_no . '--' . $item->trade_time;
					}
				}*/
            }
            //Log::error("allPolicy:",[$policyCalData],"fanliNewCal");
            //Log::error("allTradesIds:",[$all_trade_ids],"fanliNewCal");
            OilCardViceTrades::beginCalFanli($no, $params, $policyCalData, $noPolicy_ids, $all_trade_ids);
        } else {
            Log::error("无待返利计算的消费记录:", [], "fanliNewCal");
        }
    }

    //根据消费记录和返利政策,开始返利计算
    public static function beginCalFanli($no, $params, $policyData, $noPolicy_ids, $all_trade_ids)
    {
        $ids_str = implode(",", $all_trade_ids);
        Log::error('消费记录及政策' . var_export($policyData, TRUE), [], 'fanliCal');
        //开启事务
        Capsule::connection()->getPdo()->beginTransaction();
        try {
            $accountInfo = OilCardViceTrades::getAccountInfo();

            if ($policyData) {
                //取出所有资金机构账户的现金返利扣减余额
                $data = [];
                $tradeData = [];//阶梯返利的累计消费需减去使用返利金额
                foreach ($policyData as $key => $val) {
                    $val = (object)$val;
                    //unset($lastTrade_id[$val->id]);
                    $result = [];
                    //交易金额大于金额限制且加油量大于油量限制
                    if (floatval($val->trade_money) >= $val->oil_money_limit && floatval($val->trade_num) >= $val->oil_amount_limit) {
                        Log::error('参与返利--' . var_export($val, TRUE), [], 'fanliCal');
                        //按金额返
                        if ($val->fanli_type == 1) {
                            //由于来一条消费标记消费记录的use_fanli_money ,因此去掉这里的标记
                            $accountInfo[$val->org_id_fanli]['fanli_discount_remain'] = 0;
                            $returnResult = OilCardViceTrades::fanliTypeByMoney($val, $accountInfo[$val->org_id_fanli]);
                            $result = $returnResult['result'];
                            $accountInfo[$val->org_id_fanli] = $returnResult['accountInfo'];
                        }
                        //按加油量返
                        if ($val->fanli_type == 2) {
                            $accountInfo[$val->org_id_fanli]['fanli_discount_remain'] = 0;
                            $returnResult = OilCardViceTrades::fanliTypeByJifen($val, $accountInfo[$val->org_id_fanli]);
                            $result = $returnResult['result'];
                            $accountInfo[$val->org_id_fanli] = $returnResult['accountInfo'];
                        }

                        //按阶梯返利返
                        //交易的单价，是否大于免惠最低价；只有大于免惠最低价时，才触发计算返利 txb 修改去掉等于（YP-3698）
                        if (in_array(intval($val->fanli_type), [3, 4]) && $val->trade_price > $val->fanli_min_money) {
                            $accountInfo[$val->org_id_fanli]['fanli_discount_remain'] = 0;
                            $returnResult = OilCardViceTrades::fanliTypeByStep($val, $accountInfo[$val->org_id_fanli], $tradeData);
                            $result = $returnResult['result'];
                            $accountInfo[$val->org_id_fanli] = $returnResult['accountInfo'];
                        }

                        $tradeData[] = [
                            'vice_no' => $val->vice_no,
                            'trade_time' => $val->trade_time,
                            'trade_price' => $val->trade_price,
                            //'use_fanli_money'=>isset($result['use_fanli_money']) ? $result['use_fanli_money'] : 0 //消费记录来了，就标记使用返利
                            'use_fanli_money' => 0,
                        ];
                        //组装交易记录数组
                        $data[] = OilCardViceTrades::getTradesRecord($no, $val, $result);
                    } else {
                        Log::error('未参与返利--' . var_export($val, TRUE), [], 'fanliCal');
                        $data[] = [
                            'id' => $val->id,
                            'policy_id' => $val->policy_id,
                        ];
                    }
                }
                if ($data) {
                    //批量修改交易记录
                    OilCardViceTrades::updateBatch('oil_card_vice_trades', $data);
                }
                //回写现金返利扣减余额至机构资金账户表
                //OilCardViceTrades::updateAccountMoney($accountInfo);

            } else {
                //todo 消费记录没有匹配到返利政策的也需要把相应机构上的返利扣减（fanli_discount_remain）用掉
                if (count($noPolicy_ids) > 0) {
                    Log::error("标记没有返利政策的消费记录" . json_encode($noPolicy_ids), [], 'fanliCal');
                    self::upFanliToTrade(array_keys($noPolicy_ids), $accountInfo);
                }
                Log::error("消费记录无匹配返利政策" . json_encode($policyData), [], 'fanliCal');
            }

            //返利计算时间段内的记录均标识为已算和计算可开票金额(trade_money - use_fanli_money)
            OilCardViceTrades::updateIsFanli($no, $ids_str);
            //写入返利计算表
            OilCardViceTrades::addToFanliCalculate($no, $params, $ids_str);
            //事务提交
            Capsule::connection()->getPdo()->commit();
            Log::error('本次返利计算已完成...', [], 'jobOk');

            return '计算完成';
        } catch (\Exception $e) {
            //事务回滚
            Capsule::connection()->getPdo()->rollBack();
            throw  new \RuntimeException($e->getMessage(), $e->getCode());
        }

    }

    //返利计算报警
    public static function fanliCalAlert()
    {
        $alertStr = implode('<br>', "");
        $content[] = "标题：返利计算异常\n";
        $content[] = "环境：" . API_ENV . "\n";
        $content[] = "原因：返利政策重复了\n";
        $content[] = "描述：> " . $alertStr . "\n";

        (new \Framework\DingTalk\DingTalkAlarm())
            ->alarmToGroup('返利计算异常;', implode("", $content), [], TRUE);
        throw new \RuntimeException($alertStr, 3);
    }

    /**
     * 执行返利计算
     *
     * @param $no
     * @param $params
     * @param $ids
     * @return string
     */
    public static function execute($no, $params, $ids)
    {
        Log::error('开始返利计算...', [], 'jobOk');
        Log::error('$params04--' . var_export($params, TRUE), [], 'fanliCal');

        //获取本次计算的副卡交易计算详情
        if (in_array($params['oil_com'], OilCom::getFirstList())) {
            $policyData = OilCardViceTrades::getFirstCardPolicy($ids);
        } else {
            $policyData = OilCardViceTrades::getPolicy($ids);
        }
        $allTrade_id = explode(",", $ids);
        $lastTrade_id = array_flip($allTrade_id);
        Log::error('$policyData--' . var_export($policyData, TRUE), [], 'fanliCal');
        //校验：是否有关联出多个返利政策的数据
        OilCardViceTrades::checkMorePolicy($policyData);

        //开启事务
        Capsule::connection()->getPdo()->beginTransaction();
        try {
            $accountInfo = OilCardViceTrades::getAccountInfo();

            if ($policyData) {
                //取出所有资金机构账户的现金返利扣减余额
                $data = [];
                $tradeData = [];//阶梯返利的累计消费需减去使用返利金额
                foreach ($policyData as $key => $val) {
                    unset($lastTrade_id[$val->id]);
                    $result = [];
                    //交易金额大于金额限制且加油量大于油量限制
                    if (floatval($val->trade_money) >= $val->oil_money_limit && floatval($val->trade_num) >= $val->oil_amount_limit) {
                        Log::error('参与返利--' . var_export($val, TRUE), [], 'fanliCal');
                        //按金额返
                        if ($val->fanli_type == 1) {
                            //由于来一条消费标记消费记录的use_fanli_money ,因此去掉这里的标记
                            $accountInfo[$val->org_id_fanli]['fanli_discount_remain'] = 0;
                            $returnResult = OilCardViceTrades::fanliTypeByMoney($val, $accountInfo[$val->org_id_fanli]);
                            $result = $returnResult['result'];
                            $accountInfo[$val->org_id_fanli] = $returnResult['accountInfo'];
                        }
                        //按加油量返
                        if ($val->fanli_type == 2) {
                            $accountInfo[$val->org_id_fanli]['fanli_discount_remain'] = 0;
                            $returnResult = OilCardViceTrades::fanliTypeByJifen($val, $accountInfo[$val->org_id_fanli]);
                            $result = $returnResult['result'];
                            $accountInfo[$val->org_id_fanli] = $returnResult['accountInfo'];
                        }

                        //按阶梯返利返
                        //交易的单价，是否大于免惠最低价；只有大于免惠最低价时，才触发计算返利 txb 修改去掉等于（YP-3698）
                        if (in_array(intval($val->fanli_type), [3, 4]) && $val->trade_price > $val->fanli_min_money) {
                            $accountInfo[$val->org_id_fanli]['fanli_discount_remain'] = 0;
                            $returnResult = OilCardViceTrades::fanliTypeByStep($val, $accountInfo[$val->org_id_fanli], $tradeData);
                            $result = $returnResult['result'];
                            $accountInfo[$val->org_id_fanli] = $returnResult['accountInfo'];
                        }

                        $tradeData[] = [
                            'vice_no' => $val->vice_no,
                            'trade_time' => $val->trade_time,
                            'trade_price' => $val->trade_price,
                            //'use_fanli_money'=>isset($result['use_fanli_money']) ? $result['use_fanli_money'] : 0 //消费记录来了，就标记使用返利
                            'use_fanli_money' => 0,
                        ];
                        //组装交易记录数组
                        $data[] = OilCardViceTrades::getTradesRecord($no, $val, $result);
                    } else {
                        Log::error('未参与返利--' . var_export($val, TRUE), [], 'fanliCal');
                        $data[] = [
                            'id' => $val->id,
                            'policy_id' => $val->policy_id,
                        ];
                    }
                }
                if ($data) {
                    //批量修改交易记录
                    OilCardViceTrades::updateBatch('oil_card_vice_trades', $data);
                }
                //回写现金返利扣减余额至机构资金账户表
                //OilCardViceTrades::updateAccountMoney($accountInfo);

            } else {
                //todo 消费记录没有匹配到返利政策的也需要把相应机构上的返利扣减（fanli_discount_remain）用掉
                if (count($lastTrade_id) > 0) {
                    Log::error("标记没有返利政策的消费记录" . json_encode($lastTrade_id), [], 'fanliCal');
                    self::upFanliToTrade(array_keys($lastTrade_id), $accountInfo);
                }
                Log::error("消费记录无匹配返利政策" . json_encode($policyData), [], 'fanliCal');
            }

            //返利计算时间段内的记录均标识为已算和计算可开票金额(trade_money - use_fanli_money)
            OilCardViceTrades::updateIsFanli($no, $ids);
            //写入返利计算表
            OilCardViceTrades::addToFanliCalculate($no, $params, $ids);
            //事务提交
            Capsule::connection()->getPdo()->commit();
            Log::error('本次返利计算已完成...', [], 'jobOk');

            return '计算完成';
        } catch (\Exception $e) {
            //事务回滚
            Capsule::connection()->getPdo()->rollBack();
            throw  new \RuntimeException($e->getMessage(), $e->getCode());
        }

    }

    /**
     * 处理没有匹配返利政策的消费记录
     *
     * @param $ids
     * @param $account
     */
    static function upFanliToTrade(array $lastTrade_id, $accountInfo)
    {
        $tradeInfo = OilCardViceTrades::getInfoByIds(['ids' => $lastTrade_id]);
        if (count($tradeInfo) > 0) {
            foreach ($tradeInfo as $key => $tradeItem) {
                $accountOne = $accountInfo[$tradeItem->org_id];
                $accountOne['fanli_discount_remain'] = 0;
                if (isset($accountOne) && $accountOne && floatval($accountOne['fanli_discount_remain']) > 0) {
                    $accountOne['is_edited'] = TRUE;
                    //消费金额 <= 现金返利扣减余额
                    $trade_money = floatval($tradeItem->trade_money - $tradeItem->use_fanli_money);
                    if ($trade_money <= floatval($accountOne['fanli_discount_remain'])) {
                        $result['use_fanli_money'] = $trade_money;
                        $accountOne['fanli_discount_remain'] = floatval($accountOne['fanli_discount_remain']) - $trade_money;
                    } else {
                        $result['use_fanli_money'] = floatval($accountOne['fanli_discount_remain']);
                        $accountOne['fanli_discount_remain'] = 0;
                    }
                    $accountInfo[$tradeItem->org_id] = $accountOne;
                    $data[] = [
                        'id' => $tradeItem->id,
                        //'use_fanli_money' => $result['use_fanli_money'] ? $result['use_fanli_money'] : 0,
                        'updatetime' => \helper::nowTime(),
                    ];
                }
            }

            if (count($data) > 0) {
                //批量修改交易记录
                OilCardViceTrades::updateBatch('oil_card_vice_trades', $data);
            }

            //回写现金返利扣减余额至机构资金账户表
            //OilCardViceTrades::updateAccountMoney($accountInfo);
        }
    }

    /**
     * 批量修改
     *
     * @param string $tableName
     * @param array $data
     */
    static function updateBatch($tableName = "", $data = [])
    {
        $updateArr = [];
        foreach ($data as $v) {
            $sql = "UPDATE $tableName SET ";
            $fields = "";
            $where = "";
            foreach ($v as $k1 => $v1) {
                if ($k1 == 'id') {
                    $where = " WHERE id = " . intval($v1);
                    continue;
                }
                $fields .= $fields ? ",$k1='$v1'" : "$k1='$v1'";
            }
            $updateArr[] = $sql . $fields . $where;
        }
        $sqlStr = implode(";", $updateArr);
        Capsule::connection()->getPdo()->exec($sqlStr);
    }

    /**
     * 通过单号获取返利单详情
     *
     * @param $no
     * @return mixed
     */
    public static function getPolicyDetailByNo($no, $ids = '')
    {
        $fanLiTypeOneSql = "SELECT ocvt.policy_id, SUM(ocvt.fanli_money) fanli_money, 0 fanli_jifen, NULL main_id, '' main_no
                FROM oil_card_vice_trades ocvt
                LEFT JOIN oil_card_vice ocv ON ocvt.vice_no = ocv.vice_no
                LEFT JOIN oil_card_main ocm ON ocm.id = ocv.card_main_id
                WHERE ocvt.fanli_no = '$no' AND ocvt.fanli_way = 1";
        if ($ids) {
            $fanLiTypeOneSql .= ' AND ocvt.id in (' . $ids . ')';
        }
        $fanLiTypeOneSql .= ' GROUP BY ocvt.policy_id';
        $dataM = Capsule::connection()->select($fanLiTypeOneSql);

        $fanLiTypeTwoSql = "SELECT ocvt.policy_id, 0 fanli_money, SUM(ocvt.fanli_jifen) fanli_jifen, ocm.id main_id, ocm.main_no
                FROM oil_card_vice_trades ocvt
                LEFT JOIN oil_card_vice ocv ON ocvt.vice_no = ocv.vice_no
                LEFT JOIN oil_card_main ocm ON ocm.id = ocv.card_main_id
                WHERE ocvt.fanli_no = '$no' AND ocvt.fanli_way = 2";
        if ($ids) {
            $fanLiTypeTwoSql .= ' AND ocvt.id in (' . $ids . ')';
        }
        $fanLiTypeTwoSql .= ' GROUP BY ocm.id, ocvt.policy_id';
        $dataJ = Capsule::connection()->select($fanLiTypeTwoSql);

        $data = array_merge($dataM, $dataJ);

        return $data;
    }

    /**
     * 获取返利审核的开始和结束时间
     *
     * @param $no
     * @return mixed
     */
    static private function getStartAndEndTime($no)
    {
        $data = Capsule::connection()->select("SELECT MAX(createtime) as max_createtime,MIN(createtime) as min_createtime FROM oil_card_vice_trades
                WHERE fanli_no = '$no'");

        return $data[0];
    }

    /**
     * 写入返利计算表
     *
     * @param $no
     * @param $params
     * @throws Exception
     */
    public static function addToFanliCalculate($no, $params, $ids)
    {
        Log::error('$no--' . var_export($no, TRUE), [], 'addFanliCal');
        Log::error('params--' . var_export($params, TRUE), [], 'addFanliCal');
        Log::error('$ids--' . var_export($ids, TRUE), [], 'addFanliCal');
        //获取本次返利计算汇总后的数据
        $data = OilCardViceTrades::getPolicyDetailByNo($no, $ids);
        if (!$params['createtimeGe'] && !$params['createtimeLe']) {
            $timeInfo = OilCardViceTrades::getStartAndEndTime($no);
            $params['createtimeGe'] = $timeInfo->min_createtime;
            $params['createtimeLe'] = $timeInfo->max_createtime;
        } elseif (!$params['createtimeGe'] && $params['createtimeLe']) {
            $timeInfo = OilCardViceTrades::getStartAndEndTime($no);
            $params['createtimeGe'] = $timeInfo->min_createtime;
        } elseif ($params['createtimeGe'] && !$params['createtimeLe']) {
            $timeInfo = OilCardViceTrades::getStartAndEndTime($no);
            $params['createtimeLe'] = $timeInfo->max_createtime;
        }
        $startTime = substr($params['createtimeGe'], 0, 10) . " 00:00:00";
        $endTime = substr($params['createtimeLe'], 0, 10) . " 23:59:59";
        $nowTime = \helper::nowTime();
        if ($data) {
            $insertArr = [];
            foreach ($data as $_v) {
                if ($_v->policy_id) {
                    $insertArr[] = [
                        'fanli_no' => $no,
                        'policy_id' => $_v->policy_id,
                        'main_id' => $_v->main_id,
                        'fanli_money' => $_v->fanli_money,
                        'fanli_jifen' => $_v->fanli_jifen,
                        'status' => 0,
                        'starttime' => $startTime,
                        'endtime' => $endTime,
                        'createtime' => $nowTime,
                        'creator_id' => isset($params['creator_id']) ? $params['creator_id'] : '',
                        'last_operator' => isset($params['true_name']) ? $params['true_name'] : '',
                    ];
                }
            }

            OilFanliCalculate::batchInsertByPdo($insertArr);
        }
    }

    /**
     * 获取上月的月初和月末
     *
     * @return array
     */
    static function getlastMonthDays()
    {
        $timestamp = time();
        $firstday = date('Y-m-01 00:00:00', strtotime(date('Y', $timestamp) . '-' . (date('m', $timestamp) - 1) . '-01'));
        $lastday = date('Y-m-d 23:59:59', strtotime("$firstday +1 month -1 day"));

        return [$firstday, $lastday];
    }

    /**
     * 获取本月返利
     *
     * @param $params
     * @param $flag
     * @return int
     */
    public static function getFanliByMonth($params, $flag)
    {
        if ($flag == 1) {
            $fields = "SUM(fanli_money) as fanli";
        } elseif ($flag == 2) {
            $fields = "SUM(fanli_jifen) as fanli";
        } elseif ($flag == 3) {
            $fields = "SUM(use_fanli_money) as fanli";
        }
        $data = OilCardViceTrades::Filter($params)->select(Capsule::connection()->raw($fields))->first();

        return $data->fanli ? $data->fanli : 0;
    }

    /**
     * 获取本月现金返利金额
     *
     * @param array $params
     * @return int
     */
    public static function cashFanliMonth(array $params)
    {
        $orgIdList = OilOrg::getByOrgcodeLike($params['orgcode']);
        $currentMonth = [0 => date('Y-m-01') . ' 00:00:00', 1 => date('Y-m-d') . ' 23:59:59'];
        //获取本月已审核的返利单号
        $fanliNo = OilFanliCalculate::getFanliNo($currentMonth);
        if (!$fanliNo) {
            $cashFanli = 0;
        } else {
            //统计本月现金返利（按本月已通过返利审核的消费记录返利）
            $args = ['org_id_list' => $orgIdList, 'fanli_noIn' => $fanliNo];
            $cashFanli = OilCardViceTrades::getFanliByMonth($args, 1);
        }
        //获取本月返利充值的现金返利
        $fanliCharge = OilAccountMoneyCharge::getMonthFanliCharge($currentMonth, $orgIdList);
        $fanliMoney = floatval($cashFanli) + floatval($fanliCharge);

        return $fanliMoney;
    }

    /**
     * 获取本月积分返利金额
     *
     * @param array $params
     * @return int
     */
    public static function jifenFanliMonth(array $params)
    {
        $orgIdList = OilOrg::getByOrgcodeLike($params['orgcode']);
        $currentMonth = [0 => date('Y-m-01') . ' 00:00:00', 1 => date('Y-m-d') . ' 23:59:59'];
        //获取本月已审核的返利单号
        $fanliNo = OilFanliCalculate::getFanliNo($currentMonth);
        if (!$fanliNo) {
            $jifenFanli = 0;
        } else {
            //统计本月积分返利（按本月消费记录返利）
            $args = ['org_id_list' => $orgIdList, 'fanli_noIn' => $fanliNo];
            $jifenFanli = OilCardViceTrades::getFanliByMonth($args, 2);
        }
        //获取本月返利充值的现金返利
        $jifenCharge = OilAccountJifenCharge::getMonthJifenCharge($currentMonth, $orgIdList);
        $jifenFanli = floatval($jifenFanli) + floatval($jifenCharge);

        return $jifenFanli;
    }

    /**
     * 根据卡号获取最新的交易记录一条
     */
    public static function getNewTrade($vice_no)
    {
        return OilCardViceTrades::where('vice_no', '=', $vice_no)->select('trade_time')->orderBy('trade_time', 'desc')->first();
    }

    /**
     * 根据最新两天的消费记录更改副卡的最新时间
     */
    public static function getTwoDayAgo()
    {
        $startTime = date('Y-m-d', strtotime('-2 day', time())) . ' 23:59:59';
        //Capsule::connection()->enableQueryLog();
        $data = OilCardViceTrades::select('vice_no', Capsule::connection()->raw('max(trade_time) trade_time'), Capsule::connection()->raw('max(createtime) createtime'))->where('createtime', '>', $startTime)->groupBy('vice_no')->get();
        //$sql = Capsule::connection()->getQueryLog();
        //Framework\Log::dataLog('sql:'.var_export($sql,true),'tims');
        return $data;
    }

    /**
     * 消费记录同步统计
     *
     * @param array $params
     * @return mixed
     */
    public static function syncStats(array $params)
    {
        $checkDate = isset($params['checkDate']) ? $params['checkDate'] : date("Y-m-d");
        $data = Capsule::connection()->select("(
SELECT
oil_com,count(*) as total,DATE_FORMAT(createtime,'%Y-%m-%d') as sync_date
FROM
oil_card_vice_trades
WHERE
createtime >= '" . $checkDate . " 00:00:00' and createtime <= '" . $checkDate . " 23:59:59' AND oil_com in (1,2,26,3,50,52)
GROUP BY oil_com
)
UNION
(
SELECT
4 AS oil_com,count(*) as total,DATE_FORMAT(createtime,'%Y-%m-%d') as sync_date
FROM
oil_card_vice_trades
WHERE
createtime >= '" . $checkDate . " 00:00:00' and createtime <= '" . $checkDate . " 23:59:59' AND oil_com in (6,7,8)
)");

        $total = OilCardViceTrades::where('createtime', '>=', $checkDate . " 00:00:00")
            ->where('createtime', '<=', $checkDate . " 23:59:59")
            ->count();

        $result = [];
        if ($data && $total > 0) {
            foreach ($data as &$v) {
                $v->totalCount = $total;
                $v->percentage = round($v->total / $total, 2) * 100;
                if ($v->sync_date) {
                    $result[] = $v;
                }
            }
        }

        return $result;
    }

    /**
     * 获取机构上月的消费记录
     */
    public static function getMonthList(array $params)
    {
        $oilTradeType = \Fuel\Defines\TradesType::getTradesType(TRUE);
        $month = isset($params['month']) ? date("Y-m", strtotime($params['month'])) : date('Y-m', strtotime('-1 month'));
        $orgcode = $params['orgcode'];
        $limit = isset($params['limit']) && $params['limit'] ? $params['limit'] : '';
        //$orgcode = '2000BG'; //tmp
        $condition = self::preUpdateTradeAndOilComCondition($month);

        $sql = "SELECT
	oil_card_vice_trades.*,
	oil_type_no.oil_type,
	oil_provinces.province as trade_place_name
FROM
	oil_card_vice_trades
LEFT JOIN
	oil_org ON oil_card_vice_trades.org_id = oil_org.id AND oil_org.is_del = 0
LEFT JOIN
    oil_type_no ON oil_card_vice_trades.oil_name = oil_type_no.oil_no
LEFT JOIN
    oil_station ON oil_station.station_name = oil_card_vice_trades.trade_place
LEFT JOIN
    oil_provinces ON oil_station.regions_id = oil_provinces.id
WHERE
	oil_org.orgcode like '" . $orgcode . "%'
" . $condition . " " . $limit . "
";

        $record = Capsule::connection()->select($sql);

        return $record;
    }

    /**
     * 获取机构上月的消费记录
     */
    public static function getMonthListCount(array $params)
    {
        $oilTradeType = \Fuel\Defines\TradesType::getTradesType(TRUE);
        $month = isset($params['month']) ? date("Y-m", strtotime($params['month'])) : date('Y-m', strtotime('-1 month'));
        $orgcode = $params['orgcode'];
        //$orgcode = '2000BG'; //tmp

        $sql = "SELECT
	count(oil_card_vice_trades.id) as total
FROM
	oil_card_vice_trades
LEFT JOIN
	oil_org ON oil_card_vice_trades.org_id = oil_org.id AND oil_org.is_del = 0
WHERE
	oil_org.orgcode like '" . $orgcode . "%'
AND oil_card_vice_trades.trade_type in (" . $oilTradeType . ")
AND oil_card_vice_trades.createtime like '" . $month . "%'";

        $record = Capsule::connection()->select($sql);

        return $record[0]->total;
    }

    /**
     * getNoTradePlaceData
     *
     * @param $endTime
     * @param $beginTime
     * @return mixed
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function getNoTradePlaceData($endTime, $beginTime)
    {
        $tradeType = \Fuel\Defines\TradesType::getTradesType();
        $data = OilCardViceTrades::whereIn('trade_type', $tradeType)
            ->where('trade_place', '=', '')
            ->whereNotIn('card_from', [40, 41])
            ->where('createtime', '>', $beginTime)
            ->where('createtime', '<', $endTime)
            ->select('oil_com')
            ->first();

        return $data;
    }

    /**
     * getNoStationTrades
     *
     * @param $endTime
     * @param $beginTime
     * @return mixed
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function getNoStationTrades($endTime, $beginTime)
    {
        //Capsule::connection()->enableQueryLog();
        $tradeType = \Fuel\Defines\TradesType::getTradesType();

        $connection = "online_only_read";
        if (API_ENV == 'dev') {
            $connection = "";
        }
         $sqlObj = Capsule::connection($connection)->table('oil_card_vice_trades')
             ->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`idx_create_trade_type`)'));
         $data = $sqlObj->whereIn('oil_card_vice_trades.trade_type', $tradeType)
            ->leftJoin('oil_station', 'oil_station.station_name', '=', 'oil_card_vice_trades.trade_place')
            ->where('oil_card_vice_trades.createtime', '>', $beginTime)
            ->where('oil_card_vice_trades.createtime', '<', $endTime)
            ->whereNull('oil_station.station_name')
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41])
            ->select('oil_card_vice_trades.oil_com', 'oil_card_vice_trades.trade_place', 'oil_station.station_name')
            ->groupBy('oil_card_vice_trades.trade_place')
            ->get();
        //$sql = Capsule::connection()->getQueryLog();

        return $data;
    }

    /**
     * getNoTypeTrades
     *
     * @param $endTime
     * @param $beginTime
     * @return mixed
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function getNoTypeTrades($endTime, $beginTime)
    {
        //Capsule::connection()->enableQueryLog();
        $tradeType = \Fuel\Defines\TradesType::getTradesType();

        $connection = "online_only_read";
        if (API_ENV == 'dev') {
            $connection = "";
        }
        $sqlObj = Capsule::connection($connection)->table('oil_card_vice_trades')
            ->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`idx_create_trade_type`)'));
        $data = $sqlObj->whereIn('oil_card_vice_trades.trade_type', $tradeType)
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->where('oil_card_vice_trades.createtime', '>', $beginTime)
            ->where('oil_card_vice_trades.createtime', '<', $endTime)
            ->whereNull('oil_type_no.oil_no')
            ->select('oil_card_vice_trades.oil_com', 'oil_card_vice_trades.oil_name', 'oil_type_no.oil_no')
            ->groupBy('oil_card_vice_trades.oil_name')
            ->get();

        //$sql = Capsule::connection()->getQueryLog();

        return $data;
    }

    /**
     * getMonthDataGroupByOrg
     *
     * @param $month
     * @return mixed
     * <AUTHOR>
     * @since  ${DATE}
     */
    public static function getMonthDataGroupByOrg($month,$cond_str = '')
    {
        $month = isset($month) ? date("Y-m", strtotime($month)) : date('Y-m', strtotime('-1 month'));
        $condition = self::preUpdateTradeAndOilComCondition($month);
        $cond_str && $condition = $cond_str.$condition;
        $sql = "SELECT
			oil_card_vice_trades.org_id AS org_id,
			sum(
			  case when oil_type_no.oil_type != 3 then oil_card_vice_trades.trade_money else 0 END
			) AS total_trade_money,
			sum(
			    case when oil_type_no.oil_type != 3 then oil_card_vice_trades.trade_num else 0 END
			) AS total_trade_num,
			sum(
			    case when oil_type_no.oil_type != 3 then 1 else 0 END
			) AS counts,
			SUM(
            CASE
            WHEN oil_card_vice_trades.trade_money < 0 THEN
                1
            ELSE
                0
            END
            ) AS refundCount,
            sum(
                    case when oil_type_no.oil_type = 3 then oil_card_vice_trades.trade_money else 0 END
                ) AS no_trades_total,
            sum(
                    case when oil_type_no.oil_type = 3 then 1 else 0 END
                ) AS no_trades_counts
		FROM
			oil_card_vice_trades
		LEFT JOIN oil_type_no ON oil_card_vice_trades.oil_name = oil_type_no.oil_no
		WHERE 1
		" . $condition . "
		group by oil_card_vice_trades.org_id";

        $record = Capsule::connection('online_only_read')->select($sql);

        return $record;
    }

    /**
     * @title   预处理消费月报统计中消费记录相关查询条件(updateTrade & updateOilCom)
     * @desc
     * @param $month
     * @return string
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * <AUTHOR>
     */
    static private function preUpdateTradeAndOilComCondition($month)
    {
        $oilTradeType = \Fuel\Defines\TradesType::getTradesType(TRUE);
        $oilComType = implode(',', OilCom::getNotGssType());
        $cardFrom = CardFrom::getFanliCardFrom();
        $cardFrom = implode(',', $cardFrom);
        $month_end = date('Y-m-t 23:59:59',strtotime($month));
        $month_start = date('Y-m-01 00:00:00',strtotime($month));
        return " AND oil_card_vice_trades.trade_type in (" . $oilTradeType . ")
		AND oil_card_vice_trades.card_from in (" . $cardFrom . ")
		AND oil_card_vice_trades.createtime >= '{$month_start}' AND oil_card_vice_trades.createtime <= '{$month_end}'
		AND oil_card_vice_trades.oil_com in (" . $oilComType . ")";
    }

    /**
     * 根据机构统的消费记录来源类型计月报表
     */
    public static function getSingleOrgMonthOilcomMap($month,$cond_str = '')
    {
        $month = isset($month) ? date("Y-m", strtotime($month)) : date('Y-m', strtotime('-1 month'));
        $condition = self::preUpdateTradeAndOilComCondition($month);
        $cond_str && $condition = $cond_str . $condition;
        $sql = "SELECT
            oil_card_vice_trades.org_id,
            oil_card_vice_trades.oil_com,
			oil_card_vice_trades.regions_name,
			sum(oil_card_vice_trades.trade_money) as trade_money,
			count(*) as counts,
            sum(
                CASE
                WHEN oil_type_no.oil_type != 3 AND oil_card_vice_trades.trade_type != '积分加油' THEN
                    oil_card_vice_trades.trade_money
                ELSE
                    0
                END
            ) AS cash_trade_money,
            sum(
                        case when oil_type_no.oil_type = 3 AND oil_card_vice_trades.trade_type != '积分加油' then oil_card_vice_trades.trade_money else 0 END
                    ) AS no_trade_money,
            sum(
                        case when oil_card_vice_trades.trade_type = '积分加油' then oil_card_vice_trades.trade_money else 0 END
                    ) AS jifen_trade_money
		FROM
			oil_card_vice_trades
		LEFT JOIN oil_org ON oil_card_vice_trades.org_id = oil_org.id
		LEFT JOIN oil_account_money ON oil_card_vice_trades.org_id = oil_account_money.id
		LEFT JOIN oil_type_no ON oil_card_vice_trades.oil_name = oil_type_no.oil_no
		WHERE 1
		" . $condition . "
        GROUP BY oil_card_vice_trades.org_id,oil_card_vice_trades.oil_com,oil_card_vice_trades.regions_name";

        $record = Capsule::connection('online_only_read')->select($sql);

        return $record;
    }

    /**
     * 获取最大的api_id（同步DSP消费记录专用)
     *
     * @return mixed
     * <AUTHOR>
     */
    public static function getMaxApiId()
    {
        $info = OilCardViceTrades::select(Capsule::connection()->raw('MAX(api_id + 0) max_api_id'))
            ->whereIn('oil_com', OilCom::getAutoAssignType())
            ->whereRaw('length(api_id) <> 32')
            ->whereNotIn('card_from', [40, 41])
            ->where('createtime', '>', date("Y-m-d", strtotime("-10 day")))
            ->first();

        if (!$info->max_api_id) {
            (new DingTalkAlarm())->alarmToGroup('消费记录增量同步异常', 'max_api_id：' . $info->max_api_id, [], TRUE);
        }

        return $info->max_api_id;
    }

    /**
     * 获取最大的dataUpdated（同步DSP消费记录专用)
     *
     * @return mixed
     * <AUTHOR>
     */
    public static function getMaxDataUpdated()
    {
        return OilCardViceTrades::whereIn('oil_com', TradesType::getTypeForDsp())
            ->select('api_id', 'data_updated')->orderBy('data_updated', 'desc')->first();
    }

    /**
     * 根据油站获取机构号和车牌号(油站定位信息专用)
     *
     * @param $params
     * @return mixed
     * <AUTHOR>
     */
    public static function getNewRecordByStation($params)
    {
        return OilCardViceTrades::leftJoin('oil_card_vice as ocv', 'oil_card_vice_trades.vice_no', '=', 'ocv.vice_no')
            ->leftJoin('oil_org as oo', 'oo.id', '=', 'ocv.org_id')
            ->where('oil_card_vice_trades.trade_place', $params['station_name'])
            ->where('oil_card_vice_trades.trade_time', '>=', '2016-01-01')
            ->whereNotNull('oil_card_vice_trades.truck_no')
            ->where('oil_card_vice_trades.id', '>=', intval($params['last_trade_id']))
            ->select('oil_card_vice_trades.id', 'oil_card_vice_trades.trade_time', 'oo.orgcode', 'oil_card_vice_trades.truck_no')
            ->take(5)->get()->toArray();
    }

    /**
     * @title    修正消费记录中油站省份为空的数据
     * @desc
     * @param null $date
     * @return array
     * @returns
     * array
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function updateTradeProvince($date = \NULL)
    {
        $dateParams = !$date ? date("Y-m-d", strtotime("-2 days")) : $date;
        $sql = "SELECT
station.station_name,station.regions_id,oil_provinces.`code`,oil_provinces.province
FROM
oil_station station
LEFT JOIN oil_provinces on oil_provinces.id = station.regions_id
INNER JOIN
(
SELECT
trade_place
FROM
oil_card_vice_trades
where trade_place_provice_code is NULL and trade_place  != '' AND oil_card_vice_trades.createtime > '" . $dateParams . "'
GROUP BY trade_place
) trades on trades.trade_place = station.station_name
where station.regions_id is not NULL AND station.station_name != ''";
        $result = Capsule::connection()->select($sql);

        $data = [];
        //特殊处理的市级油站
        $tmpProvince = [
            '37' => '广东',
            '39' => '江苏',
        ];
        if ($result) {
            Capsule::connection()->enableQueryLog();
            foreach ($result as $v) {
                if (in_array($v->regions_id, \array_keys($tmpProvince))) {
                    $trade_place_provice_code = \substr($v->code, 0, 2) . '0000';
                    $trade_place_provice_name = $tmpProvince[$v->regions_id];
                } else {
                    $trade_place_provice_code = $v->code;
                    $trade_place_provice_name = $v->province;
                }
                if ($v->station_name) {
                    $data[] = self::where('trade_place', $v->station_name)
                        ->update(
                            [
                                'trade_place_provice_code' => $trade_place_provice_code,
                                'trade_place_provice_name' => $trade_place_provice_name,
                            ]
                        );
                    //修改返利计算临时表
                    OilUpstreamRebateTradesTemp::where('trade_place', $v->station_name)
                        ->update([
                            'trade_place_provice_code' => $trade_place_provice_code,
                            'trade_place_provice_name' => $trade_place_provice_name,
                        ]);
                    //修改能源看数基础统计表
                    OilTrades::where('station_name', $v->station_name)->update([
                        'province_code' => $trade_place_provice_code,
                        'province_name' => $trade_place_provice_name,
                    ]);

                }
            }
            var_dump(Capsule::connection()->getQueryLog());exit;
        }

        return $data;
    }

    /**
     * @title   for gos sync
     * @desc
     * @param $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * <AUTHOR>
     */
    public static function getListByUpdatetime($params)
    {
        Capsule::connection()->enableQueryLog();
        $sqlobj = OilCardViceTrades::
        leftJoin('oil_provinces as activeProvince', 'activeProvince.id', '=', 'oil_card_vice_trades.regions_id')
            ->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id')->where('oil_org.is_del', '=', 0);
            })
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_station', 'oil_station.station_name', '=', 'oil_card_vice_trades.trade_place')
            ->select(Capsule::raw('oil_station.province_code as trade_place_provice_code,oil_station.province_name as trade_place_provice_name,
            oil_card_vice_trades.id,oil_card_vice_trades.api_id,oil_card_vice_trades.imgurl,oil_card_vice_trades.vice_no,
            oil_card_vice_trades.trade_time,oil_card_vice_trades.trade_type,oil_card_vice_trades.oil_name,oil_card_vice_trades.trade_money,
            oil_card_vice_trades.unit,oil_card_vice_trades.use_fanli_money,oil_card_vice_trades.trade_price,oil_card_vice_trades.trade_num,
            oil_card_vice_trades.trade_jifen,oil_card_vice_trades.balance,oil_card_vice_trades.receipt_remain,oil_card_vice_trades.trade_place,
            oil_card_vice_trades.regions_id,oil_card_vice_trades.regions_name,oil_card_vice_trades.fetch_time,oil_card_vice_trades.is_fanli,
            oil_card_vice_trades.main_no,oil_card_vice_trades.card_owner,oil_card_vice_trades.oil_com,oil_card_vice_trades.active_region,
            oil_card_vice_trades.org_id,oil_card_vice_trades.org_name,oil_card_vice_trades.consume_type,oil_card_vice_trades.truck_no,
            oil_card_vice_trades.fanli_no,oil_card_vice_trades.fanli_money,oil_card_vice_trades.fanli_jifen,oil_card_vice_trades.policy_id,
            oil_card_vice_trades.fanli_way,oil_card_vice_trades.qz_drivername,oil_card_vice_trades.qz_drivertel,oil_card_vice_trades.createtime,
            oil_card_vice_trades.updatetime,oil_card_vice_trades.data_updated,oil_card_vice_trades.trade_place_city_code,oil_card_vice_trades.trade_place_city_name,
            activeProvince.code as regions_code,oil_org.orgcode,oil_type_no.oil_type,oil_org.is_test,oil_card_vice_trades.fanli_no,oil_card_vice_trades.fanli_no,oil_card_vice_trades.fanli_money,
            oil_card_vice_trades.fanli_jifen,oil_card_vice_trades.mileage'))
            ->whereIn('oil_card_vice_trades.oil_com', OilCom::getNotGssType())
            ->whereIn('oil_card_vice_trades.consume_type', [0, 1, 2, 3])
            ->where('oil_card_vice_trades.createtime', '>=', '2019-04-01 00:00:00')
            ->orderBy('oil_card_vice_trades.id', 'desc');
        if (isset($params['id']) && $params['id']) {
            $sqlobj->where('oil_card_vice_trades.id', '<', $params['id']);
            //$sqlobj->where('oil_card_vice_trades.is_fanli', 1);
            //$sqlobj->whereNotNull('oil_card_vice_trades.fanli_no');
        }
        //oil_provinces.code trade_place_provice_code,oil_provinces.province trade_place_provice_name,

        $data = $sqlobj->limit(200)->get();
        Log::debug('getListByUpdatetime' . \var_export(Capsule::connection()->getQueryLog(), TRUE), [], 'getListByUpdatetime');

//        return $sqlobj->offset(0)->limit(10)->get();
        return ['total' => 1719395, 'data' => $data];

    }

    /**
     * @title   for gos sync
     * @desc
     * @param $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * <AUTHOR>
     */
    public static function getIncrementListById($params)
    {
//        Capsule::connection()->enableQueryLog();
        $sqlObj = OilCardViceTrades::leftJoin('oil_provinces as activeProvince', 'activeProvince.id', '=', 'oil_card_vice_trades.regions_id')
            ->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id')->where('oil_org.is_del', '=', 0);
            })
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_station', 'oil_station.station_name', '=', 'oil_card_vice_trades.trade_place')
            ->select(Capsule::raw('oil_station.province_code as trade_place_provice_code,oil_station.province_name as trade_place_provice_name,
            oil_card_vice_trades.id,oil_card_vice_trades.api_id,oil_card_vice_trades.imgurl,oil_card_vice_trades.vice_no,oil_card_vice_trades.cancel_sn,
            oil_card_vice_trades.trade_time,oil_card_vice_trades.trade_type,oil_card_vice_trades.oil_name,oil_card_vice_trades.trade_money,
            oil_card_vice_trades.unit,oil_card_vice_trades.use_fanli_money,oil_card_vice_trades.trade_price,oil_card_vice_trades.trade_num,
            oil_card_vice_trades.trade_jifen,oil_card_vice_trades.balance,oil_card_vice_trades.receipt_remain,oil_card_vice_trades.trade_place,
            oil_card_vice_trades.regions_id,oil_card_vice_trades.regions_name,oil_card_vice_trades.fetch_time,oil_card_vice_trades.is_fanli,
            oil_card_vice_trades.main_no,oil_card_vice_trades.card_owner,oil_card_vice_trades.oil_com,oil_card_vice_trades.active_region,
            oil_card_vice_trades.org_id,oil_card_vice_trades.org_name,oil_card_vice_trades.consume_type,oil_card_vice_trades.truck_no,
            oil_card_vice_trades.card_from,oil_card_vice_trades.fanli_no,oil_card_vice_trades.fanli_money,oil_card_vice_trades.fanli_jifen,oil_card_vice_trades.policy_id,
            oil_card_vice_trades.fanli_way,oil_card_vice_trades.qz_drivername,oil_card_vice_trades.qz_drivertel,oil_card_vice_trades.createtime,
            oil_card_vice_trades.updatetime,oil_card_vice_trades.data_updated,oil_card_vice_trades.trade_place_city_code,oil_card_vice_trades.trade_place_city_name,oil_card_vice_trades.mileage,
            activeProvince.code as regions_code,oil_org.orgcode,oil_type_no.oil_type,oil_org.is_test,oil_card_vice_trades.fanli_no,oil_card_vice_trades.fanli_no,oil_card_vice_trades.fanli_money,
            oil_card_vice_trades.fanli_jifen,oil_card_vice_trades.mileage'))
            ->whereIn('oil_card_vice_trades.oil_com', OilCom::getNotGssType())
            ->whereIn('oil_card_vice_trades.consume_type', [0, 1, 2, 3])
            ->orderBy('oil_card_vice_trades.id', 'asc');
        if (isset($params['id']) && $params['id']) {
            $sqlObj->where('oil_card_vice_trades.id', '>', $params['id']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $sqlObj->where('oil_org.orgcode', 'like', $params['orgcode'] . '%');
        }

        if (isset($params['one_card']) && $params['one_card'] == 1) {
            $sqlObj->whereIn('oil_card_vice_trades.oil_com', OilCom::getAllFirstList());
        }

        if (isset($params['is_fanli']) && $params['is_fanli'] == 1) {
            $sqlObj->where('oil_card_vice_trades.is_fanli', 1)
                ->where(function ($query) {
                    $query->where('fanli_money', '>', 0)->orWhere('fanli_jifen', '>', 0);
                });
        }

        $data = $sqlObj->limit(300)->get();
//        Log::error('getListByUpdatetime' . \var_export(Capsule::connection()->getQueryLog(), TRUE), [], 'getListByUpdatetime');

        return ['data' => $data];
    }

    /**
     * @title    判断交易重复
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * mixed
     * @returns
     * @package  Models
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    public static function checkExistByOne(array $params)
    {
        $data = OilCardViceTrades::where('vice_no', $params['vice_no'])
            ->where('trade_type', $params['trade_type'])
            ->where('trade_time', $params['trade_time'])
            ->where('oil_name', $params['oil_name'])
            ->where('trade_money', $params['trade_money'])
            ->where('trade_place', $params['trade_place'])
            ->get();

        return $data;
    }

    /**
     * @title   阶梯返利获取消费记录
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>

    public static function getStepTradeList($start_time, $end_time, $table = "oil_card_vice_trades", $_key = 'create_time_dx')
    {
        if (API_ENV == 'dev') {
            $connection = "";
        } else {
            $connection = "online_only_read";
        }

        //Capsule::connection($connection)->enableQueryLog();
        $sqlObj = Capsule::connection($connection)
            #->table('oil_card_vice_trades')
            ->table($table)
            ->from(Capsule::raw('`' . $table . '` as oil_card_vice_trades FORCE INDEX (`' . $_key . '`)'));
        #->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`create_time_dx`)'));

        $result = $sqlObj->leftJoin("oil_station", "oil_card_vice_trades.trade_place", '=', 'oil_station.station_name')
            ->where('oil_card_vice_trades.createtime', '>=', $start_time)
            ->where('oil_card_vice_trades.createtime', '<=', $end_time)
            ->whereIn("oil_card_vice_trades.oil_com", OilCom::getFanLiCalculate())
            ->whereIn("oil_card_vice_trades.card_from", CardFrom::getFanliCardFrom())
            ->whereIn('oil_card_vice_trades.trade_type', TradesType::getFanLiTradeType())
            ->select("oil_card_vice_trades.id", "oil_card_vice_trades.mini_trade_price", "oil_card_vice_trades.trade_price",
                "oil_station.regions_id", "oil_station.province_code", "oil_card_vice_trades.createtime")
            ->get();
        //$ss     = Capsule::connection($connection)->getQueryLog();
        //print_r($ss);exit;
        return $result;
    }

    public static function getStepTradeListFromTemp($start_time, $end_time,$table = "oil_card_vice_trades",$_key = 'create_time_dx')
    {
        if (API_ENV == 'dev') {
            $connection = "";
        } else {
            $connection = "online_only_read";
        }

        //Capsule::connection($connection)->enableQueryLog();
        $sqlObj = Capsule::connection($connection)
            #->table('oil_card_vice_trades')
            ->table($table)
            ->from(Capsule::raw('`'.$table.'` as oil_card_vice_trades FORCE INDEX (`'.$_key.'`)'));
        #->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`create_time_dx`)'));

        $result = $sqlObj->leftJoin("oil_station", "oil_card_vice_trades.trade_place", '=', 'oil_station.station_name')
            ->where('oil_card_vice_trades.trade_create_time', '>=', $start_time)
            ->where('oil_card_vice_trades.trade_create_time', '<=', $end_time)
            ->whereIn("oil_card_vice_trades.oil_com", OilCom::getFanLiCalculate())
//            ->whereIn("oil_card_vice_trades.card_from", CardFrom::getFanliCardFrom())
            ->whereIn('oil_card_vice_trades.trade_type', TradesType::getFanLiTradeType())
            ->select("oil_card_vice_trades.id", "oil_card_vice_trades.mini_trade_price","oil_card_vice_trades.trade_price",
                "oil_station.regions_id","oil_station.province_code", "oil_card_vice_trades.trade_create_time")
            ->get();
        //$ss     = Capsule::connection($connection)->getQueryLog();
        //print_r($ss);exit;
        return $result;
    }

    /**
     * @title   代理商返佣获取累计消费
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
    public static function getSumTradeMoney(array $params)
    {
        Capsule::connection()->enableQueryLog();
        $data = self::Filter($params)
            ->leftJoin("oil_org", "oil_org.id", '=', "oil_card_vice_trades.org_id")
            ->sum("trade_money");
        $sql = Capsule::connection()->getQueryLog();
        Log::error("SumTotalSql" . json_encode($sql), [], "getSumTradeMoney_");
        return $data;
    }

    /**
     * @title   1号卡获取累计加油量
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
    public static function getSumTradeNum(array $params)
    {
        Capsule::connection()->enableQueryLog();
        $data = self::Filter($params)
            ->sum("trade_num");
        $sql = Capsule::connection()->getQueryLog();
        Log::error("SumTotalSql" . json_encode($sql), [], "getSumTradeMoney_");
        return $data;
    }

    /**
     * @title
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
    public static function getAllIdByFanliNo($no)
    {
        return self::where("fanli_no", $no)->pluck("id");
    }


    /**
     * @title   供gsp分析油卡消费数据用
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
    public static function getTradesStatisticForGsp(array $params)
    {
        if (isset($params['per_page']) && intval($params['per_page'])) {
            if (intval($params['per_page']) > 1000) {
                throw new \RuntimeException('每页最大条数不能超过1000', 2);
            }
            $params['limit'] = intval($params['per_page']) > 1000 ? 1000 : intval($params['per_page']);
            $params['page'] = isset($params['page_no']) ? intval($params['page_no']) : 1;
        } else {
            $params['limit'] = 50;
            $params['page'] = 1;
        }

        $sqlObj = self::select(Capsule::connection()->raw("oil_type_no.oil_type,left(oil_org.orgcode,6) as orgroot,oil_org.orgcode,oil_card_vice_trades.trade_type,
        sum(oil_card_vice_trades.trade_money) as total,count(oil_card_vice_trades.id) as total_count"))
            ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice_trades.org_id')
            ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no')
            ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->whereNotIn('oil_card_main.card_from', [40, 41])
            ->where('oil_org.is_del', 0)
            ->whereIn('oil_card_vice_trades.oil_com', OilCom::getFanLiCalculate());

        if (isset($params['orgcodes']) && $params['orgcodes']) {
            $sqlObj->whereIn('oil_org.orgcode', explode(',', trim($params['orgcodes'])));
        } elseif (isset($params['orgcode_like']) && $params['orgcode_like']) {
            $sqlObj->where('oil_org.orgcode', 'like', $params['orgcode_like'] . '%');
        } elseif (isset($params['orgname_like']) && $params['orgname_like']) {
            $sqlObj->where('oil_org.org_name', 'like', '%' . $params['orgname_like'] . '%');
        }

        if (isset($params['oil_type']) && $params['oil_type']) {
            $oilTypeIn = explode(",", trim($params['oil_type']));
            $sqlObj->whereIn("oil_type_no.oil_type", $oilTypeIn);
        }

        if (isset($params['trades_type']) && $params['trades_type']) {
            if (trim($params['trades_type']) == '现金加油') {
//                $sqlObj->whereIn('oil_card_vice_trades.trade_type', ['现金加油', 'IC卡消费', '优惠油品脱机消费', '加油', '油品联机消费', '油品脱机消费', '撬装加油']);
                $sqlObj->whereIn('oil_card_vice_trades.trade_type', TradesType::getCashAndSelfOilTradeTypeArr());
            } elseif (trim($params['trades_type']) == '积分加油') {
//                $sqlObj->where('oil_card_vice_trades.trade_type', '积分加油');
                $sqlObj->whereIn('oil_card_vice_trades.trade_type', TradesType::getJiFenTradeTypeArr());
            }
        } else {
//            $sqlObj->whereIn('oil_card_vice_trades.trade_type', ['现金加油', 'IC卡消费', '优惠油品脱机消费', '加油', '油品联机消费', '油品脱机消费', '积分加油', '撬装加油']);
            $sqlObj->whereIn('oil_card_vice_trades.trade_type', TradesType::getJiFenAndCashSelfTradeTypeArr());
        }

        if (isset($params['start_time']) && $params['start_time']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['start_time']);
        }

        if (isset($params['end_time']) && $params['end_time']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['end_time']);
        }

        if (!isset($params['group_key']) && $params['group_key'] != 1) {
            $sqlObj->groupBy('oil_card_vice_trades.org_id', 'oil_card_vice_trades.trade_type', 'oil_type_no.oil_type');
        } else {
            if (!isset($params['orgcode_like']) && !$params['orgcode_like']) {
                throw new \RuntimeException('orgcode_like必填', 2);
            }
        }

        $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);

        return $data;
    }


    /**
     * 请求账户中心，进行卡消费扣款
     *
     * @param array $params
     * @return mixed
     */
    public static function trades2G7Pay($insertArr, $accountInfo, $cardAccountInfo = NULL, $orgInfo)
    {
        Log::error('进入账户中心卡消费扣款：', [], 'gasDeductMoney');
        try {
            global $app;
            //$money = $insertArr->trade_money * 100;
            $money = bcmul($insertArr->trade_money, 100, 0);
            $postData = [];
            $postData['amount'] = intval($money);
            $postData['carNumber'] = $insertArr->truck_no ? $insertArr->truck_no : '未知';
            $postData['cardNo'] = $insertArr->vice_no;
            $postData['consumeArea'] = $insertArr->trade_address ? $insertArr->trade_address : '未知';
            $postData['consumePoint'] = $insertArr->trade_place ? $insertArr->trade_place : "5";
            $postData['consumeTime'] = date('Y-m-d\TH:i:s.0000+08:00', strtotime($insertArr->trade_time));
            $postData['consumeType'] = 'G7_FACTORING_CREDIT'; //'CHANNEL_CREDIT' ZBANK_CREDIT;
            $postData['extID'] = $insertArr->extID;
            $postData['oilType'] = $insertArr->oil_name ? $insertArr->oil_name : '0#柴油';
            $postData['orgCode'] = substr($orgInfo->orgcode, 0, 6);
            $postData['channelSubAccountID'] = $accountInfo['credit_account_info']->subAccountID;
            if ($insertArr->oil_com == OilCom::GAS_FIRST_CHARGE && $cardAccountInfo) {
                Log::error('充值卡授信账户消费：', [$cardAccountInfo->cardSubAccountID], 'gasDeductMoney');
                $postData['subAccountID'] = $cardAccountInfo->subAccountID;
                $balance = ($cardAccountInfo->restCreditAmount - $postData['amount']) / 100;
            } else {
                Log::error('共享卡授信账户消费：', [$accountInfo['credit_account_info']->subAccountID], 'gasDeductMoney');
                $postData['subAccountID'] = $accountInfo['credit_account_info']->subAccountID;
                $balance = ($accountInfo['creditInfo']->restCreditAmount - $postData['amount']) / 100;
            }
            if ($app->config->api_env == "pro") {
                $domain = 'http://zqx.chinawayltd.com/';
                $postData['oilCompanyName'] = '汇通天下石油化工（大连）有限公司';
                $postData['unifiedSocialCreditCode'] = '91210244MA0QE1K31X';
            } elseif ($app->config->api_env == 'test') {
                $domain = 'http://test.zqx.chinawayltd.com/';
                $postData['oilCompanyName'] = '洛阳嘉驰运输有限公司';
                $postData['unifiedSocialCreditCode'] = '914103003450408927';
            } elseif ($app->config->api_env == 'demo') {
                $domain = 'http://demo.zqx.chinawayltd.com/';
                $postData['oilCompanyName'] = '洛阳嘉驰运输有限公司';
                $postData['unifiedSocialCreditCode'] = '914103003450408927';
            } else {
                $domain = 'http://dev.zqx.chinawayltd.com/';
                $postData['oilCompanyName'] = '洛阳嘉驰运输有限公司';
                $postData['unifiedSocialCreditCode'] = '914103003450408927';
            }
            $postData['companyCode'] = 'G7_DALIAN';
            $postData['notifyUrl'] = $domain . "api.php?method=zbank.consume.pushCardConsume";
            Log::error('请求账户中心扣款参数：', [$postData], 'gasDeductMoney');
            $result = (new AccountService())->createCardConsume($postData);
            Log::error('请求账户中心扣款结果：', [$result], 'gasDeductMoney');
            if (!$result) {
                throw new \RuntimeException('支付消费失败', 2);
            }

            //采取同步方案-请求消费，在请求查询，5秒内如果不成功就失败
            $is_pay = NULL;

            for ($i = 1; $i <= 10; $i++) {
                Log::error('查询账户中心扣款：', [['extID' => $postData['extID']]], 'gasDeductMoney');
                $res = (new AccountService())->getCardConsume(['extID' => $postData['extID']]);
                if ($res && $res->consumeResult == 'SUCCESS') {
                    Log::error('查询账户中心扣款结果SUCCESS：', [$res], 'gasDeductMoney');
                    $is_pay = 1;
                    break;
                } elseif ($res && $res->consumeResult == 'FAILED') {
                    Log::error('查询账户中心扣款结果FAILED：', [$res], 'gasDeductMoney');
                    $is_pay = 3;
                    break;
                }

                sleep(1);
            }

            if (!$is_pay) {
                Log::error('连续10次未得到结果：', [$postData], 'gasDeductMoney');
                throw new \RuntimeException("支付未得到结果", 2);
            } else {
                OilCardViceTradesZBank::where('id', $insertArr->id)->update(['is_pay' => $is_pay, 'billID' => $res->billID, "balance" => $balance]);
            }

            if ($is_pay == 3) {
                throw new \RuntimeException("账户中心扣款失败", 2);
            }

        } catch (\Exception $e) {
            Log::error('error:', [$e->getMessage(), $e->getCode()], 'gasDeductMoney');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $result;
    }

    public static function trades2G7Pay_bak($insertArr, $accountInfo, $cardAccountInfo, $orgInfo)
    {
        try {
            global $app;
            $money = $insertArr->trade_money * 100;
            $postData = [];
            $postData['amount'] = intval($money);
            $postData['carNumber'] = $insertArr->truck_no;
            $postData['cardNo'] = $insertArr->vice_no;
            $postData['consumeArea'] = $insertArr->trade_address;
            $postData['consumePoint'] = $insertArr->trade_place;
            $postData['consumeTime'] = date('Y-m-d\TH:i:s.0000+08:00', strtotime($insertArr->trade_time));
            $postData['consumeType'] = 'CHANNEL_CREDIT';
            $postData['extID'] = $insertArr->extID;
            $postData['oilType'] = $insertArr->oil_name;
            $postData['orgCode'] = $orgInfo->orgcode;
            $postData['channelSubAccountID'] = $accountInfo['info']->subAccountID;
            if ($insertArr->oil_com == OilCom::GAS_FIRST_ZBANK_CHARGE) {
                $postData['subAccountID'] = $cardAccountInfo['accountInfo']->cardSubAccountID;
            } else {
                $postData['subAccountID'] = $accountInfo['info']->subAccountID;
            }
            if ($app->config->api_env == "pro") {
                $domain = 'http://zqx.chinawayltd.com/';
                $postData['oilCompanyName'] = '汇通天下石油化工（大连）有限公司';
                $postData['unifiedSocialCreditCode'] = '91210244MA0QE1K31X';
            } elseif ($app->config->api_env == 'test') {
                $domain = 'http://test.zqx.chinawayltd.com/';
                $postData['oilCompanyName'] = '洛阳嘉驰运输有限公司';
                $postData['unifiedSocialCreditCode'] = '914103003450408927';
            } elseif ($app->config->api_env == 'demo') {
                $domain = 'http://demo.zqx.chinawayltd.com/';
                $postData['oilCompanyName'] = '洛阳嘉驰运输有限公司';
                $postData['unifiedSocialCreditCode'] = '914103003450408927';
            } else {
                $domain = 'http://dev.zqx.chinawayltd.com/';
                $postData['oilCompanyName'] = '洛阳嘉驰运输有限公司';
                $postData['unifiedSocialCreditCode'] = '914103003450408927';
            }
            $postData['notifyUrl'] = $domain . "api.php?method=zbank.consume.pushCardConsume";
            $result = (new AccountService())->createCardConsume($postData);
            return $result;
        } catch (\Exception $e) {
            Log::error('msg', [$e->getMessage(), $e->getCode()], 'trades2G7Pay_');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 支付成功后，处理方式
     *
     * @param array $params
     * @return mixed
     */
    public static function paySuccess($info, $state = 3)
    {
        Capsule::connection()->beginTransaction();
        try {
            $upData['is_pay'] = $state;
            $upData['id'] = $info->id;
            OilCardViceTradesZBank::edit($upData);

            //todo 充值卡扣卡余额
            if ($info->oil_com == OilCom::GAS_FIRST_ZBANK_CHARGE && $state == 1) {
                $cardInfo = OilCardVice::getByViceNoForLock(['vice_no' => $info->vice_no]);
                if (bccomp($info->trade_money, $cardInfo->card_remain) > 0) {
                    throw new \RuntimeException('余额不足，子账户账号：' . $cardInfo->vice_no, 2);
                }
                $upCard['id'] = $cardInfo->id;
                $upCard['card_remain'] = $cardInfo->card_remain - $info->trade_money;
                \Models\OilCardVice::edit($upCard);
            }

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollback();
            Log::error('msg', [$e->getMessage(), $e->getCode()], 'G7PayRecord_');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        /*//推送Gos
        $lastId = OilCardViceTrades::getByApiId( array("api_id" => $insertData['api_id']) );
        CardViceTradesToGos::sendBatchCreateTask([ $lastId->id ],'sync');*/
        //推送卡余额

        return TRUE;
    }

    /**
     * 支付成功后，推送gos和gas
     *
     * @param array $params
     * @return mixed
     */
    public static function sendPayRes($info, $state)
    {
        \Fuel\Service\CardViceToGos::batchUpdateToGos([$info->vice_no], "sync");

        /*Log::error("推送GasBegin:".var_export($info,true),[],"G7PayRecord_");
        $params['extId'] = $info->extID;
        $params['state'] = $state;ice
        //todo 通知gas系统 需要gas提供
        $gasRes = GasClient::post(
            [
                'method' => 'gas.api.setTradeStatus',
                'data'   => $params,
            ]
        );

        Log::error("推送Gas返回值:".var_export($gasRes,true),[$params],"G7PayRecord_");
        if($gasRes && $gasRes->history_id && $state == 1){
            $upTrade['id']  = $info->id;
            $upTrade['trade_api_id'] = $gasRes->history_id;
            OilCardViceTradesZBank::edit($upTrade);
        }*/
    }

    /**
     * @title   针对1号记账卡增加消费记录
     * @desc
     * @param array $params
     * @returns
     * []
     * @returns
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     */
    public static function addGasTrades(array $params)
    {
        $isExist = OilCardViceTrades::getByApiId(['api_id' => $params['id']]);

        $orgInfo = \Models\OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('机构不存在', 2);
        }
        $addData['org_id'] = $orgInfo->id;
        $addData['org_name'] = $orgInfo->org_name;
        if (!$isExist || !$isExist->id) {
            if (!isset($params['isdel']) || $params['isdel'] !== 1) {
                $cardViceInfo = OilCardVice::getViceDetail(['vice_no' => $params['card_no'], 'oil_comIn' => \Fuel\Defines\OilCom::getFisrtType()]);
                if (!$params['card_no'] || !$cardViceInfo) {
                    throw new \RuntimeException('消费记录不存在子账户账号', 2);
                }

                $addData['regions_id'] = isset($cardViceInfo->CardMain) ? $cardViceInfo->CardMain->fanli_region : NULL;
                $addData['regions_name'] = isset($cardViceInfo->CardMain->FanLiRegion) ? $cardViceInfo->CardMain->FanLiRegion->province : NULL;
                $addData['active_region'] = isset($cardViceInfo->CardMain) ? $cardViceInfo->CardMain->active_region : NULL;
                $addData['card_owner'] = $cardViceInfo->card_owner;
                $addData['main_no'] = $cardViceInfo->CardMain->main_no;
                $addData['oil_com'] = $cardViceInfo->CardMain->oil_com;
                $addData['card_from'] = $cardViceInfo->CardMain->card_from;

                if ($cardViceInfo->unit == 2) {
                    $viceNoArr[$params['card_no']] = $params['oil_balance'];
                    $addData['balance'] = $params['oil_balance'];
                } elseif ($cardViceInfo->unit == 1) {
                    $viceNoArr[$params['card_no']] = $params['balance'];
                    $addData['balance'] = $params['balance'];
                }

                if (in_array($addData['oil_com'], [7, 8])) {//撬装记账卡、加油员卡，卡余额显示0.00
                    $addData['balance'] = 0.00;
                }

                if ($params['sale_type'] == '现金消费') {
                    $addData['consume_type'] = '1';
                } elseif ($params['sale_type'] == '透支消费') {
                    $addData['consume_type'] = '3';
                }

                $addData['fetch_time'] = $params['createtime'];
                $addData['api_id'] = $params['id'];
                $addData['vice_no'] = $params['card_no'];
                $addData['trade_time'] = $params['createtime'];
                $addData['trade_type'] = '撬装加油';//$v->log_type;
                $addData['imgurl'] = $params['imgurl'];
                $addData['oil_name'] = $params['oil_name'];
                $addData['trade_money'] = $params['money'];
                $addData['trade_price'] = $params['price'];
                $addData['trade_num'] = $params['oil_num'];
                $addData['trade_place'] = $params['station_name'];
                $addData['truck_no'] = $params['truck_no'];
                $addData['oil_balance'] = $params['oil_balance'];
                $addData['trade_place_provice_code'] = substr($params['provice_code'], 0, 6);
                $addData['trade_place_provice_name'] = $params['provice_name'];
                $addData['trade_place_city_code'] = substr($params['city_code'], 0, 6);
                $addData['trade_place_city_name'] = $params['city_name'];
                $addData['unit'] = $params['unit'];
                $addData['createtime'] = \helper::nowTime();
                $addData['updatetime'] = date("Y-m-d H:i:s", strtotime($params['updatetime']));
                $addData['mileage'] = $params['mileage'];
                isset($params['consumer_name']) ? $addData['consumer_name'] = $params['consumer_name'] : '';//消费单位名称（针对撬装）

                Log::error('addTrades--' . var_export($addData, TRUE), [], 'gasDeductMoney');

                OilCardViceTrades::add($addData);
            }
        }
    }

    /**
     * @title   根据返利单号获取返利规则id
     * @desc
     * @param array $params
     * @returns
     * []
     * @returns
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     */
    public static function getPolicyId($fanli_no, $field = "")
    {
        return self::where("fanli_no", $fanli_no)->where("is_fanli", '!=', 0)->pluck("policy_id");
    }

    /**
     * @title   驳回返利计算工单
     * @desc
     * @param array $params
     * @returns
     * []
     * @returns
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     */
    public static function rejectCalData($sql)
    {
        $result = Capsule::connection()->select($sql);
        return $result;
    }

    /**
     * @title   新版返利查询
     * @desc
     * @param array $params
     * @returns
     * []
     * @returns
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     */
    public static function getRuleStep($vice_no, $startTime, $endTime, $field, $isEq = FALSE)
    {
        if ($isEq) {
            $fh = "<=";
        } else {
            $fh = "<";
        }
        //Capsule::connection()->enableQueryLog();
        $data = self::where('vice_no', $vice_no)
            ->where('createtime', '>=', $startTime)
            ->where('createtime', $fh, $endTime)
            ->whereIn('trade_type', TradesType::getFanLiTradeType())
            ->whereNotNull('mini_trade_price')
            ->whereRaw('trade_price>mini_trade_price')
            ->select(Capsule::connection()->raw($field))->first();
        //$ss = Capsule::connection()->getQueryLog();
        //print_r($ss);
        return $data;
    }

    /**
     * @title   新版1号卡特殊返利
     * @desc
     * @param array $params
     * @returns
     * []
     * @returns
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     */
    public static function getSpecialRuleStep($org_id, $startTime, $endTime, $field)
    {
        //Capsule::connection()->enableQueryLog();
        $data = self::where('org_id', $org_id)
            ->where('createtime', '>=', $startTime)
            ->where('createtime', '<=', $endTime)
            ->whereIn('trade_type', TradesType::getFanLiTradeType())
            ->select(Capsule::connection()->raw($field))->first();
        //$ss = Capsule::connection()->getQueryLog();
        //print_r($ss);
        return $data;
    }

    /**
     * 更新交易记录为已算(未审核)和计算可开票金额
     *
     * @param $no
     * @throws Exception
     */
    public static function updateIsFanliRule($no, $ids)
    {
        //todo 一定要修改为消费记录表和消费sql
        $sql[] = "UPDATE oil_card_vice_trades SET fanli_no='$no',is_fanli=2 WHERE id IN ($ids) AND is_fanli=0";

        $sql[] = "UPDATE oil_card_vice_trades a
            INNER JOIN oil_card_vice b ON a.vice_no = b.vice_no AND  a.is_open_invoice IS NULL
            INNER JOIN oil_card_main c ON b.card_main_id = c.id AND c.is_open_invoice = 10
            SET a.receipt_remain=a.trade_money - a.use_fanli_money WHERE a.id IN ($ids)";

        /*$sql[] = "UPDATE oil_card_vice_trades a
            SET a.receipt_remain=a.trade_money - a.use_fanli_money WHERE a.id IN ($ids)";*/

        Log::info('sql--' . implode(';', $sql), [], 'updateIsFanli');
        $flag = Capsule::connection()->getPdo()->exec(implode(';', $sql));
        if ($flag === FALSE) {
            throw new \RuntimeException('计算失败：回写副卡交易记录时失败', 1);
        }
    }

    /**
     * @title   统计昨日机构消费记录消费总和
     * @desc
     * @return mixed
     * @returns
     * []
     * @returns
     * <AUTHOR>
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     */
    public static function getOrgTradeTotalForYestoday_bak($params)
    {
        $beginTime = isset($params['begin_date']) && $params['begin_date'] ? $params['begin_date'] . " 00:00:00" : date("Y-m-d", strtotime("-1 day")) . " 00:00:00";
        $endTime = isset($params['end_date']) && $params['end_date'] ? $params['end_date'] . " 23:59:59" : date("Y-m-d", strtotime("-1 day")) . " 23:59:59";

        $sql = "SELECT
	LEFT (b.orgcode, 6) AS orgroot,
	sum(a.trade_money) AS totalMoney,
	a.createtime,
	(CASE
WHEN a.trade_type = '撬装加油' THEN
	'电子卡'
ELSE
	'传统卡'  END) as card_type
FROM
	oil_card_vice_trades a
LEFT JOIN oil_org b ON a.org_id = b.id
WHERE
	a.trade_type IN (" . TradesType::getCashAndSelfOilTradeTypeArr(true) . ")
AND a.createtime > '" . $beginTime . "'
AND a.createtime <= '" . $endTime . "'
AND a.card_from not in (40,41)
GROUP BY
orgroot,card_type";

//        '加油',
//		'IC卡消费',
//		'油品脱机消费',
//		'油品联机消费',
//		'现金加油',
//		'撬装加油'
        $result = Capsule::connection('online_only_read')->select($sql);

        return $result;

    }

    public static function getOrgTradeTotalForYestoday($params)
    {
        $beginTime = isset($params['begin_date']) && $params['begin_date'] ? $params['begin_date'] . " 00:00:00" : date("Y-m-d", strtotime("-1 day")) . " 00:00:00";
        $endTime = isset($params['end_date']) && $params['end_date'] ? $params['end_date'] . " 23:59:59" : date("Y-m-d", strtotime("-1 day")) . " 23:59:59";

        $sql = "SELECT h.*,j.product_code FROM (
SELECT
	orgroot,
	sum(totalMoney) totalMoney,
	createtime,
	card_type,
	account_name,
	account_no
FROM
	(
		SELECT
			LEFT (b.orgcode, 6) AS orgroot,
			sum(a.trade_money) AS totalMoney,
			a.createtime,
			(
				CASE
				WHEN a.trade_type = '撬装加油' THEN
					'电子卡'
				ELSE
					'传统卡'
				END
			) AS card_type,
			(
				CASE
				WHEN a.account_name IS NULL THEN
					'现金账户'
				WHEN a.account_name = '' THEN
					'现金账户'
				WHEN a.account_name = 'G7保理' THEN
					'G7保理(1338M)'
					WHEN a.account_name = 'G7免息' THEN
					'G7免息(0038M)'
				ELSE
					account_name
				END
			) AS account_name,
			(
				CASE
				WHEN a.account_no IS NULL THEN
					''
				ELSE
					account_no
				END
			) AS account_no
		FROM
			oil_card_vice_trades a
		LEFT JOIN oil_org b ON a.org_id = b.id
		WHERE
			a.trade_type IN (" . TradesType::getCashAndSelfOilTradeTypeArr(true) . ")
		AND a.createtime >= '" . $beginTime . "'
		AND a.createtime <= '" . $endTime . "'
		AND a.card_from NOT IN (40, 41)
		#AND a.account_no != ''
		GROUP BY
			orgroot,
			card_type,
			account_name
	) z
GROUP BY
	orgroot,
	card_type,
	account_name ) h
LEFT JOIN oil_credit_provider j on h.account_name = j.name";
//        '加油',
//				'IC卡消费',
//				'油品脱机消费',
//				'油品联机消费',
//				'现金加油',
//				'撬装加油'

        $result = Capsule::connection('online_only_read')->select($sql);

        return $result;

    }

    /**
     * 探测对天然气、尿素标记是否异常
     *
     * @return array
     */
    public static function checkUseFanLiMoneyOfTrades($params)
    {
        $sql = "select count(*) as total
from
oil_card_vice_trades a
left join oil_type_no b on a.oil_name = b.oil_no
where b.oil_type in (5,6) and a.createtime > '" . $params['start_time'] . "' and a.use_fanli_money > 0";
        $result = Capsule::connection('online_only_read')->select($sql);

        return $result;
    }

    /**
     * 获取指定周期内各油品的最高价格
     *
     * @param $startTime
     * @param $endTime
     * @return mixed
     */
    public static function getMaxPriceGroupByOilName($startTime, $endTime)
    {
        $cacheName = 'receipt_stock_V1_' . md5(var_export(__METHOD__ . __FUNCTION__ . $startTime . $endTime, TRUE));
        $result = Cache::get($cacheName);
        if (!$result) {
            $result = [];
            $_result = self::selectRaw('oil_type_no.oil_type,oil_card_vice_trades.oil_name,max( oil_card_vice_trades.trade_price ) AS max_price')
                ->leftJoin("oil_type_no", "oil_type_no.oil_no", "=", "oil_card_vice_trades.oil_name")
                ->leftJoin("oil_card_vice", "oil_card_vice.vice_no", "=", "oil_card_vice_trades.vice_no")
                ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
                ->where('oil_card_vice_trades.createtime', '>=', $startTime)
                ->where('oil_card_vice_trades.createtime', '<', $endTime)
                ->where('oil_card_vice_trades.trade_price', '>', 3)
                ->where('oil_card_vice_trades.trade_price', '<=', 6)
                ->where('oil_type_no.oil_type', 2)
                ->whereNotIn('oil_card_vice_trades.card_from', [40, 41])
                ->whereNotIn('oil_card_vice_trades.oil_com', [6, 7, 8])
//                           ->whereIn('oil_card_vice_trades.trade_type', ['加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油'])
                ->whereIn('oil_card_vice_trades.trade_type', TradesType::getCashAndSelfOilTradeTypeArr())
                ->where('oil_card_vice_trades.trade_money', '>', 0)
                ->groupBy('oil_card_vice_trades.oil_name')
                ->get();
            if ($_result) {
                foreach ($_result as $v) {
                    $result[$v->oil_name] = $v->max_price;
                }
                Cache::put($cacheName, $result, 86400 * 7);
            }
        }

        return $result;
    }

    /**
     * 获取指定周期内各油品的阶梯最高价
     *
     * @param $startTime
     * @param $endTime
     * @return mixed
     */
    public static function getStepMaxPriceGroupByOilName($startTime, $endTime)
    {
        $cacheName = 'receipt_stock_V2_' . md5(var_export(__METHOD__ . __FUNCTION__ . $startTime . $endTime, TRUE));
        $result = Cache::get($cacheName);
        if (!$result) {
            $result = [];
            $sql = "SELECT
	oil_type_no.oil_type,
	oil_card_vice_trades.oil_name,
	SUBSTR((oil_card_vice_trades.trade_price-0.01),1,1) as int_price,
	if (oil_card_vice_trades.trade_price <= 5.00, MAX( oil_card_vice_trades.trade_price ),0) AS one_max_price,
	if (oil_card_vice_trades.trade_price > 5.00 and oil_card_vice_trades.trade_price <= 6.00, max( oil_card_vice_trades.trade_price ),0) AS second_max_price,
	if (oil_card_vice_trades.trade_price > 6.00 and oil_card_vice_trades.trade_price <= 7.00, max( oil_card_vice_trades.trade_price ),0) AS third_max_price
FROM
	`oil_card_vice_trades`
	LEFT JOIN `oil_type_no` ON `oil_type_no`.`oil_no` = `oil_card_vice_trades`.`oil_name`
	LEFT JOIN `oil_card_vice` ON `oil_card_vice`.`vice_no` = `oil_card_vice_trades`.`vice_no`
	LEFT JOIN `oil_card_main` ON `oil_card_main`.`id` = `oil_card_vice`.`card_main_id`
WHERE
	`oil_card_vice_trades`.`createtime` >= '" . $startTime . "'
	AND `oil_card_vice_trades`.`createtime` < '" . $endTime . "'
	AND `oil_card_vice_trades`.`trade_price` > 3
	AND `oil_card_vice_trades`.`trade_price` <= 7
	AND `oil_type_no`.`oil_type` = 2
	AND `oil_card_vice_trades`.`card_from` NOT IN (40,41)
	AND `oil_card_vice_trades`.`oil_com` NOT IN (6,7,8)
	AND `oil_card_vice_trades`.`trade_type` IN (" . TradesType::getCashAndSelfOilTradeTypeArr(true) . ")
	AND `oil_card_vice_trades`.`trade_money` > 0
	group BY `oil_card_vice_trades`.`oil_name`, SUBSTR((oil_card_vice_trades.trade_price-0.01),1,1) HAVING int_price > 3 ";
            //'加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油'
            $_result = Capsule::connection('online_only_read')->select($sql);
            if ($_result) {
                foreach ($_result as $v) {
                    if ($v->one_max_price > 0 && $v->one_max_price > $result[$v->oil_name]['step_one_price']) {
                        $result[$v->oil_name]['step_one_price'] = $v->one_max_price;
                    }
                    if ($v->second_max_price > 0 && $v->second_max_price > $result[$v->oil_name]['step_two_price']) {
                        $result[$v->oil_name]['step_two_price'] = $v->second_max_price;
                    }
                    if ($v->third_max_price > 0 && $v->third_max_price > $result[$v->oil_name]['step_three_price']) {
                        $result[$v->oil_name]['step_three_price'] = $v->third_max_price;
                    }
                }
                Cache::put($cacheName, $result, 86400 * 7);
            }
        }

        return $result;
    }

    /**
     * @param $startTime
     * @param $endTime
     * @return array|mixed
     * 库存节省的阶梯变更
     */

    public static function getNewStepMaxPriceGroup($startTime, $endTime)
    {
        $cacheName = 'receipt_stock_V3-3_' . md5(var_export(__METHOD__ . __FUNCTION__ . $startTime . $endTime, TRUE));
        $result = Cache::get($cacheName);
        if (!$result) {
            $result = [];
            // G7WALLET-1005 变更为 柴油品类（不区分规格型号），按照单价段划分为两档：<8；≥8
            $sql = "SELECT
	oil_type_no.oil_type,
	oil_card_vice_trades.oil_name,
	GROUP_CONCAT(DISTINCT(oil_card_vice_trades.trade_price) ORDER BY oil_card_vice_trades.trade_price desc),
	SUBSTR((oil_card_vice_trades.trade_price),1,1) as int_price,
	if (oil_card_vice_trades.trade_price < 8.00, MAX( oil_card_vice_trades.trade_price ),0) AS one_max_price,
	if (oil_card_vice_trades.trade_price >= 8.00, max( oil_card_vice_trades.trade_price ),0) AS second_max_price
FROM
	`oil_card_vice_trades` FORCE INDEX ( `create_time_dx` )
	LEFT JOIN `oil_type_no` ON `oil_type_no`.`oil_no` = `oil_card_vice_trades`.`oil_name`
WHERE
	`oil_card_vice_trades`.`createtime` >= '" . $startTime . "'
	AND `oil_card_vice_trades`.`createtime` < '" . $endTime . "'
	AND `oil_card_vice_trades`.`trade_price` > 4
	AND `oil_card_vice_trades`.`trade_price` <= 11
	AND `oil_type_no`.`oil_type` = 2
	AND `oil_card_vice_trades`.`card_from` NOT IN (40,41)
	AND `oil_card_vice_trades`.`oil_com` NOT IN (6,7,8)
	AND `oil_card_vice_trades`.`trade_type` IN (" . TradesType::getCashAndSelfOilTradeTypeArr(true) . ")
	AND `oil_card_vice_trades`.`trade_money` > 0
	group BY SUBSTR((oil_card_vice_trades.trade_price),1,1) HAVING int_price > 4";
            //'加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油'
            $_result = Capsule::connection('online_only_read')->select($sql);
            if ($_result) {
                foreach ($_result as $v) {
                    if ($v->one_max_price > 0 && $v->one_max_price > $result[$v->oil_type]['step_one_price']) {
                        $result[$v->oil_type]['step_one_price'] = $v->one_max_price;
                    }
                    if ($v->second_max_price > 0 && $v->second_max_price > $result[$v->oil_type]['step_two_price']) {
                        $result[$v->oil_type]['step_two_price'] = $v->second_max_price;
                    }
                }
                Cache::put($cacheName, $result, 86400 * 7);
            }
        }

        return $result;
    }

    /**
     * @title   G7授信，回款单对账专用
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public static function getCreditTrades($orgcode, $start_time, $end_time, $product_name)
    {
        //Capsule::connection()->enableQueryLog();
        $field = "oil_card_vice_trades.id,oil_card_vice_trades.vice_no,
        oil_card_vice_trades.trade_money,oil_card_vice_trades.api_id,
        oil_card_vice_trades.createtime,oil_card_vice_trades.account_no,oil_card_vice_trades.account_name,
        zbank.extID,zbank.BillID,oil_org.orgcode,oil_org.org_name,oil_card_vice_trades.service_money,oil_card_vice_trades.trade_num";
        $sqlObj = OilCardViceTrades::selectRaw($field)
            ->leftJoin('oil_card_vice_trades_zbank as zbank', 'zbank.trade_api_id', '=', 'oil_card_vice_trades.api_id')
            ->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id');
            })
            ->whereIn("oil_card_vice_trades.oil_com", OilCom::getAllFirstList())
            ->whereNull("oil_card_vice_trades.cancel_sn")
            ->whereNotNull("oil_card_vice_trades.account_no")
            ->where("oil_card_vice_trades.account_no", "!=", '')
            ->whereNotIn("oil_card_vice_trades.account_name", ["储值账户", "现金账户"])
            ->where("oil_org.orgcode", "like", $orgcode . "%")
            ->where("oil_card_vice_trades.trade_time", ">=", $start_time)
            ->where("oil_card_vice_trades.trade_time", "<=", $end_time);
        if (!empty($product_name)) {
            $sqlObj->where("oil_card_vice_trades.account_name", $product_name);
        }
        $data = $sqlObj->get();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }

    /**
     * @title   G7授信，回款单对账专用
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public static function checkIsFanli($params)
    {
        return self::Filter($params)->count();
    }

    public static function tradeDayReport($params)
    {
        $startTime = isset($params['start_time']) && $params['start_time'] ? $params['start_time'] . " 00:00:00" : date("Y-m-d", strtotime("-1 day")) . " 00:00:00";
        $endTime = isset($params['end_time']) && $params['end_time'] ? $params['end_time'] . " 23:59:59" : date("Y-m-d", strtotime("-1 day")) . " 23:59:59";
        $firstCardStr = implode(",", OilCom::getAllFirstList());
        $sql = "SELECT
		trades.org_id,
		trades.orgroot as orgcode,
		trades.org_name as orgname,
		trades.crm_id as cdcid,
		trades.day,
		sum( trades.total_money ) AS total_amount,
		sum( trades.total_num ) AS total_num,
		sum( trades.total_count ) AS total_count,
		sum( trades.ecard_total_amount ) AS ecard_total_amount,
		sum( trades.ecard_total_num ) AS ecard_total_num,
		sum( trades.ecard_total_count ) AS ecard_total_count,
		sum( trades.pcard_total_amount ) AS pcard_total_amount,
		sum( trades.pcard_total_num ) AS pcard_total_num,
		sum( trades.pcard_total_count ) AS pcard_total_count
	FROM
		(
		SELECT
			sum(a.trade_money) as total_money,
			sum(a.trade_num) as total_num,
			count(*) as total_count,
			sum( (case when a.oil_com in (" . $firstCardStr . ") then a.trade_money else 0 end )) AS ecard_total_amount,
			sum( (case when a.oil_com in (" . $firstCardStr . ") then a.trade_num else 0 end )) AS ecard_total_num,
			sum( (case when a.oil_com in (" . $firstCardStr . ") then 1 else 0 end )) AS ecard_total_count,
			sum( (case when a.oil_com not in (" . $firstCardStr . ") then a.trade_money else 0 end )) AS pcard_total_amount,
			sum( (case when a.oil_com not in (" . $firstCardStr . ") then a.trade_num else 0 end )) AS pcard_total_num,
			sum( (case when a.oil_com not in (" . $firstCardStr . ") then 1 else 0 end )) AS pcard_total_count,
			( CASE WHEN a.oil_com IN ( " . $firstCardStr . " ) THEN '电子卡' ELSE '实体卡' END ) AS oil_com_type,
			date_format(a.createtime,'%Y-%m-%d') as day,
			b.orgcode,
			substr( b.orgcode FROM 1 FOR 6 ) AS orgroot,
			b.org_name,
			a.oil_com,
			a.org_id,
			b.crm_id
		FROM
			oil_card_vice_trades a
			LEFT JOIN oil_org b ON a.org_id = b.id
		WHERE
			a.trade_type IN ( " . TradesType::getTotalTradeTypeArr(true) . " )
			AND a.card_from NOT IN ( 40, 41 )
			AND a.createtime >= '" . $startTime . "' and a.createtime < '" . $endTime . "' and b.is_test = 1
		GROUP BY
			date_format(a.createtime,'%Y-%m-%d'),
			a.org_id,
			a.oil_com
		) trades
	GROUP BY
		trades.orgroot,
		trades.oil_com_type ";
        //'IC卡消费', '优惠油品脱机消费', '加油', '撬装加油', '油品联机消费', '油品脱机消费', '现金加油', '积分加油', '非油脱机消费'

        return Capsule::connection('online_only_read')->select($sql);
    }

    public static function getOrgTradeMonthData($params)
    {
        $startTime = $params['start_time'];
        $endTime = $params['end_time'];
        $firstCardStr = implode(",", OilCom::getAllFirstList());
        $firstCardStr = $firstCardStr . ',2';//中石油的加油卡，支持天然气品类的开票

        $sql = "SELECT
	a.org_id,
	b.orgcode,
	b.org_name,
 	sum( a.trade_money ) AS total_amount,
	sum( a.use_fanli_money ) AS total_use_fanli,
	c.oil_type,
	DATE_FORMAT( a.createtime, '%Y%m' ) AS month
FROM
	oil_card_vice_trades a
	LEFT JOIN oil_org b ON a.org_id = b.id
	LEFT JOIN oil_type_no c ON c.oil_no = a.oil_name
	LEFT JOIN oil_card_vice ocv on ocv.vice_no = a.vice_no
	LEFT JOIN oil_card_main	d on d.id = ocv.card_main_id
WHERE
	if(d.operators_id =3, a.createtime < '2020-03-02',
    if (a.createtime > '2018-03-01' , d.is_open_invoice = 10 , 1 = 1))
  AND
	if(a.oil_com not in (" . $firstCardStr . ") and c.oil_type in (5,6),c.oil_type not in (5,6),1=1)
  AND
	if(left(b.orgcode,6) not in ('2000ZH','2034H2','201MKB') and a.oil_com = 2 and c.oil_type = 5,a.createtime > '2020-08-01',1=1)
	AND a.cancel_sn is NULL
	AND a.trade_type IN ( " . TradesType::getTotalTradeTypeArr(true) . " )
	AND a.card_from not in (40,41)
	AND a.oil_com not in (6,7,8)
	AND a.createtime >= '" . $startTime . "'
	AND a.createtime < '" . $endTime . "'
	AND b.is_del = 0
GROUP BY
	a.org_id,
	c.oil_type,
	DATE_FORMAT( a.createtime, '%Y-%m' )";
        //'加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油', '优惠油品脱机消费', '非油脱机消费'
        return Capsule::connection('online_only_read')->select($sql);
    }

    /**
     * 大屏取最新50条
     *
     * @param $params
     * @return array|\Illuminate\Database\Query\Builder[]
     */
    public static function getLatestTrade($params)
    {
        $take = 50;
//        $trade_type = ['IC卡消费', '优惠油品脱机消费', '加油', '撬装加油', '油品联机消费', '油品脱机消费', '现金加油', '积分加油', '非油脱机消费'];
        $trade_type = TradesType::getTotalTradeTypeArr();

        $data = Capsule::connection('slave')
            ->table('oil_card_vice_trades')
            ->leftJoin('oil_station', 'oil_station.station_name', '=', 'oil_card_vice_trades.trade_place')
            ->whereNotNull('oil_card_vice_trades.truck_no')
            ->where('oil_card_vice_trades.truck_no', '!=', '')
            ->whereNotNull('oil_station.lat')
            ->where('oil_station.lat', '!=', '')
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41])
            ->whereIn('oil_card_vice_trades.trade_type', $trade_type)
            ->whereNull('oil_card_vice_trades.cancel_sn')
            ->whereNotIn("oil_card_vice_trades.oil_com", [1, 50, 52])
            ->where("oil_card_vice_trades.createtime", ">=", date("Y-m-d") . " 00:00:00")
            ->select('oil_card_vice_trades.id', 'oil_card_vice_trades.trade_time',
                'oil_card_vice_trades.truck_no', 'oil_card_vice_trades.trade_money', 'oil_card_vice_trades.createtime',
                'oil_card_vice_trades.trade_place', 'oil_station.lat', 'oil_station.lng')
            ->orderBy('oil_card_vice_trades.createtime', 'desc')
            ->take($take)
            ->get();

        return $data;
    }

    /**
     * @param $params
     * @return array
     */
    public static function getCurrentDayTotalTrade($params)
    {
        $createtime_ge = isset($params['trade_time_ge']) && $params['trade_time_ge'] ? $params['trade_time_ge'] : date("Y-m-d") . ' 00:00:00';
        $createtime_le = isset($params['trade_time_le']) && $params['trade_time_le'] ? $params['trade_time_le'] : date("Y-m-d") . ' 23:59:59';
        $sql = "select
date_format(a.createtime,'%Y-%m-%d') as trade_date,sum(a.trade_money) as trade_money_total
from
oil_card_vice_trades a FORCE INDEX (`create_time_dx`)
left join oil_station b on a.trade_place = b.station_name
where a.truck_no != '' and b.lat is not null and a.cancel_sn is null
and a.card_from not in (40,41) and a.trade_type in (" . TradesType::getTotalTradeTypeArr(true) . ")
and a.createtime >= '" . $createtime_ge . "' and a.createtime <= '" . $createtime_le . "'
group by date_format(a.createtime,'%Y-%m-%d')";
        //'IC卡消费', '优惠油品脱机消费', '加油', '撬装加油', '油品联机消费', '油品脱机消费', '现金加油', '积分加油', '非油脱机消费'
        $data = Capsule::connection('slave')
            ->select($sql);

        return $data;
    }

    /*
     * 获取无车特殊油站都加油记录
     */
    static public function getNtoccStaionTrades(array $params)
    {
        //Capsule::connection()->enableQueryLog();
        $data = OilCardViceTrades::leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_vice_trades.org_id')
            ->where('oil_card_vice_trades.pcode', $params['pcode'])
            //->where('oil_org.orgcode','like',CreditProvider::getNtoccOrgcode().'%')
            //->whereIn('oil_org.id',$params['org_ids']) // 授信账户充值到所有机构含子集到机构
            ->where('oil_card_vice_trades.createtime', '>=', $params['start_time'])
            ->where('oil_card_vice_trades.createtime', '<=', $params['end_time'])
            ->where('oil_card_vice_trades.card_from', '!=', CardFrom::FREE_CARRIER_CARD)
            ->selectRaw('oil_card_vice_trades.id,oil_card_vice_trades.vice_no,oil_card_vice_trades.createtime,oil_card_vice_trades.trade_money')
            ->get();
        //$sql = Capsule::connection()->getQueryLog();

        //Log::error('sql',[\helper::getRealQuerySql($sql)],'createBillByCharge');

        return $data;

    }

    public static function getIdsByCreatetime($params)
    {
        $start_time = isset($params['start_time']) && $params['start_time'] ? $params['start_time'] : date("Y-m-d") . " 00:00:00";
        $end_time = isset($params['end_time']) && $params['end_time'] ? $params['end_time'] : date("Y-m-d") . " 23:59:59";
        return Capsule::connection('online_only_read')->table('oil_card_vice_trades')->where('createtime', '>=', $start_time)->where('createtime', '<=', $end_time)->pluck('id');
    }

    public static function getByIdsForSync($ids)
    {

        $result = OilCardViceTrades::leftJoin('oil_provinces as activeProvince', 'activeProvince.id', '=', 'oil_card_vice_trades.regions_id')
            ->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id')->where('oil_org.is_del', '=', 0);
            })
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_station', 'oil_station.station_name', '=', 'oil_card_vice_trades.trade_place')
            ->leftJoin('oil_card_vice_trades_ext', 'oil_card_vice_trades_ext.trades_id', '=', 'oil_card_vice_trades.id')
            ->select(Capsule::raw('oil_station.province_code as trade_place_provice_code,oil_station.province_name as trade_place_provice_name,
            oil_card_vice_trades.id,oil_card_vice_trades.api_id,oil_card_vice_trades.imgurl,oil_card_vice_trades.vice_no,oil_card_vice_trades.cancel_sn,
            oil_card_vice_trades.trade_time,oil_card_vice_trades.trade_type,oil_card_vice_trades.oil_name,oil_card_vice_trades.trade_money,
            oil_card_vice_trades.unit,oil_card_vice_trades.use_fanli_money,oil_card_vice_trades.trade_price,oil_card_vice_trades.trade_num,
            oil_card_vice_trades.trade_jifen,oil_card_vice_trades.balance,oil_card_vice_trades.receipt_remain,oil_card_vice_trades.trade_place,
            oil_card_vice_trades.regions_id,oil_card_vice_trades.regions_name,oil_card_vice_trades.fetch_time,oil_card_vice_trades.is_fanli,
            oil_card_vice_trades.main_no,oil_card_vice_trades.card_owner,oil_card_vice_trades.oil_com,oil_card_vice_trades.active_region,
            oil_card_vice_trades.org_id,oil_card_vice_trades.org_name,oil_card_vice_trades.consume_type,oil_card_vice_trades.truck_no,
            oil_card_vice_trades.card_from,oil_card_vice_trades.fanli_no,oil_card_vice_trades.fanli_money,oil_card_vice_trades.fanli_jifen,oil_card_vice_trades.policy_id,
            oil_card_vice_trades.fanli_way,oil_card_vice_trades.qz_drivername,oil_card_vice_trades.qz_drivertel,oil_card_vice_trades.createtime,
            oil_card_vice_trades.updatetime,oil_card_vice_trades.data_updated,oil_card_vice_trades.trade_place_city_code,oil_card_vice_trades.trade_place_city_name,oil_card_vice_trades.mileage,
            activeProvince.code as regions_code,oil_org.orgcode,oil_type_no.oil_type,oil_org.is_test,oil_card_vice_trades.fanli_no,oil_card_vice_trades.fanli_no,oil_card_vice_trades.fanli_money,
            oil_card_vice_trades.fanli_jifen,oil_card_vice_trades.mileage,oil_card_vice_trades_ext.document_type,
            oil_card_vice_trades_ext.original_order_id'))
            ->whereIn('oil_card_vice_trades.id', $ids)
            ->orderBy('oil_card_vice_trades.id', 'asc')
            ->get();

        if (count($result) > 0) {
            foreach ($result as &$v) {
                $v->org_id = $v->orgcode;
                $v->trade_type_name = $v->trade_type;
                $v->trade_type = TradesType::getGosTrade($v->trade_type);
                $v->unit = 1;
            }
        }
        return $result;
    }

    /**
     * 电子卡返利探测
     */
    public static function policyCheckTrades($params)
    {
        $connection = "slave";
        if (API_ENV == 'dev') {
            $connection = "";
        }
        $fields = 'oil_card_vice_trades.id,oil_card_vice_trades.fanli_level,oil_card_vice_trades.org_id,oil_card_vice_trades.vice_no,oil_card_vice_trades.pcode,
        oil_card_vice_trades.station_code,oil_card_vice_trades.trade_time,oil_card_vice_trades.createtime,oil_card_vice_trades.trade_place,
        oil_org.is_test,oil_org.orgcode,oil_org.org_name,org_fanli.orgcode as fanli_orgcode,org_fanli.org_name as fanli_orgname';
        $sqlObj = Capsule::connection($connection)
            ->table('oil_card_vice_trades as oil_card_vice_trades')
            ->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id')->where('oil_org.is_del', '=', 0);
            })
            ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no')
            ->leftJoin('oil_org as org_fanli', 'org_fanli.id', '=', 'oil_card_vice.org_id_fanli')
            ->select(Capsule::raw($fields));

        $sqlObj->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`create_time_dx`)'));

        $oilCardViceTrades = new OilCardViceTrades();
        $sqlObj = $oilCardViceTrades->scopeFilter($sqlObj, $params);
        $data = $sqlObj->orderby("oil_card_vice_trades.createtime", "asc")->get();
        if (count($data) > 0) {
            $operator = OilStationOperators::getOperatorMap([], "operators_code", "operators_name");
            $topOrgCodeArr = [];
            foreach ($data as &$v) {
                $v->top_org_name = '';
                $topOrgCode = substr($v->orgcode, 0, 6);
                $v->top_orgcode = $topOrgCode;
                $topOrgCodeArr[$topOrgCode] = $topOrgCode;
                $v->station_operators = isset($operator[$v->pcode]) ? $operator[$v->pcode] : '';
            }
            if ($topOrgCodeArr) {
                $topOrgRecords = OilOrg::getByOrgCodesMap(array_values($topOrgCodeArr));
                if ($topOrgRecords) {
                    foreach ($data as &$v) {
                        if (isset($topOrgRecords[$v->top_orgcode])) {
                            $v->top_org_name = $topOrgRecords[$v->top_orgcode];
                        }
                    }
                }
            }
        }
        return $data;
    }

    /**
     * 电子卡返利充值时，检查消费是否已计算返利
     */
    static public function getTradesCount(array $params)
    {
        $connection = "online_only_read";
        if (API_ENV == 'dev') {
            $connection = "";
        }
        //Capsule::connection($connection)->enableQueryLog();
        $sqlObj = Capsule::connection($connection)
            ->table('oil_card_vice_trades as oil_card_vice_trades')
            ->leftJoin('oil_org', function ($query) {
                $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id')->where('oil_org.is_del', '=', 0);
            });

        $sqlObj->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`create_time_dx`)'));

        $oilCardViceTrades = new OilCardViceTrades();
        $sqlObj = $oilCardViceTrades->scopeFilter($sqlObj, $params);
        $data = $sqlObj->count();
        //$sql = Capsule::connection($connection)->getQueryLog();
        //print_r($sql);exit;
        return $data;
    }

    /**
     * 获取手机号某天大于固定金额的消费数量
     * @param $mobile
     * @param $money
     * @param string $day
     * @return array|int
     */
    public static function getActTradeNum($mobile, $money, $day = '')
    {
        if (!$mobile || !$money) {
            return 0;
        }
        $day = empty($day) ? date('Y-m-d') : $day;
        $dayEnd = $day . ' 23:59:59';
        $data = OilCardViceTrades::where('qz_drivertel', '=', $mobile)
            ->where('createtime', '>=', $day)
            ->where('createtime', '<=', $dayEnd)
            ->whereIn('trade_type', TradesType::getTradesType())
            ->where('trade_money', '>=', $money)
            ->whereNull('cancel_sn')
            ->count();

        return $data;
    }

    public static function getYesterdayTradeByAccountNoAndOrgId(array $account_no, array $org_id)
    {
        $trade_data = OilCardViceTrades::whereIn('org_id', $org_id)
            ->where('createtime', '>=', date('Y-m-d 00:00:00',
                strtotime('-1 day')))
            ->where('createtime', '<=', date('Y-m-d 23:59:59',
                strtotime('-1 day')))
            ->whereIn("trade_type",TradesType::getFanLiTradeType())
            ->get([
                'account_no',
                'id',
                'sn',
                'cancel_sn',
                'trade_money',
                'use_fanli_money',
                'trade_num',
            ])
            ->toArray();
        if ($trade_data) {
            $trade_data_mapping = [];
            foreach ($trade_data as $v) {
                $trade_data_mapping[$v['account_no']][] = $v;
            }
            $return_data = [];
            foreach ($account_no as $v) {
                $return_data[$v] = $trade_data_mapping[$v] ?: [];
            }
            return $return_data;
        }
        return [];
    }

    static public function getByBillId($billId)
    {
        if (empty($billId) or !is_numeric($billId)) {
            return [];
        }
        return OilCardViceTrades::select('oil_card_vice_trades.*')
            ->rightJoin('oil_credit_bill_details', 'oil_credit_bill_details.no_id', '=', 'oil_card_vice_trades.id')
            ->where('oil_credit_bill_details.bill_id', '=', $billId)
            ->get();
    }

    /**
     * 批量修改新的消费类型数据创建时间脚本期初
     * @param array $params
     * @param array $data
     * @return mixed
     */
    static public function batchEdit(array $params, $data = [])
    {
        return OilCardViceTrades::Filter($params)->update($data);

    }

    /**
      *  gms查询副卡返利等信息
      *  <AUTHOR> yanglei <<EMAIL>>
      *  @since  :2022年8月11日
      *
     */
    static public function getViceInfoByOrderIds($orderIds)
    {
        $fields = 'oil_card_vice_trades.api_id,oil_card_vice_trades.createtime,oil_card_vice_trades.is_fanli,
                oil_card_vice_trades.fanli_money,oil_card_vice_trades.fanli_way,oil_card_vice_trades.fanli_jifen,
                oil_card_vice_trades.xpcode_pay_money,
                oil_card_vice_trades.total_money,oil_card_vice_trades.trade_money,
                ext.real_oil_num, ext.mac_price, ext.mac_amount,
                ext.supplier_id,ext.area_id,ext.operator_id,
                rebate.mark_rebate, rebate.is_share, rebate.straight_down_rebate,rebate.final_straight_down_rebate,
                rebate.after_rebate,rebate.final_after_rebate,
                rebate.is_final,rebate.down_cal_rebate,rebate.down_fanli_way,
                rebate.down_is_final';

        $results = Capsule::connection('online_only_read')->table('oil_card_vice_trades as oil_card_vice_trades')
        ->leftJoin('oil_card_vice_trades_ext as ext', 'ext.trades_id', '=', 'oil_card_vice_trades.id')
        ->leftJoin('oil_card_vice_trade_rebate as rebate','rebate.trade_id','=','oil_card_vice_trades.id')
        ->whereIn('oil_card_vice_trades.api_id', $orderIds)
        ->select(Capsule::raw($fields))
        ->get();

        $dataArr = [];
        foreach($results as $result) {
            self::prepareProfitData($result);

            $dataArr[$result->api_id] = $result;
        }

        return $dataArr;
    }

    //消费数据For换签
    static public function getTradesData($params = [])
    {
        $len = isset($params['group_len']) ? $params['group_len'] : 6;
        $sql = "SELECT
	count( t.id ) as trade_num,
	sum( t.trade_money ) as fee,
	sum( t.trade_num ) as trade_sl,
	LEFT ( o.orgcode, ".$len." ) as orgroot,
	group_concat( DISTINCT IF ( t.org_operator_id IS NULL OR t.org_operator_id = 0, '空', t.org_operator_id ) ) AS operator_txt,
	substring_index( GROUP_CONCAT( t.id, '#', t.createtime ORDER BY t.id DESC ), ',', 3 ) AS trades_id_str  
FROM
	oil_card_vice_trades AS t FORCE INDEX ( `idx_org_id_createtime` )
	LEFT JOIN oil_org AS o on t.org_id = o.id 
WHERE
	t.card_from NOT IN ( 40, 41 ) 
	AND t.oil_com NOT IN ( 6, 7, 8 ) 
	AND t.cancel_sn is NULL
	AND t.trade_type IN (".TradesType::getCashAndSelfOilTradeTypeArr(true).") ";

        if(isset($params['begin_time']) && $params['begin_time'] != ""){
            $sql .= " and t.createtime >= '".$params['begin_time']."'";
        }
        if(isset($params['end_time']) && $params['end_time'] != ""){
            $sql .= " and t.createtime < '".$params['end_time']."'";
        }

        if( isset($params['trade_id_lt']) && !empty($params['trade_id_lt']) ){
            $sql .= " and t.id < ".$params['trade_id_lt'];
        }

        if( isset($params['trade_id_eq']) && !empty($params['trade_id_eq']) ){
            $sql .= " and t.id <= ".$params['trade_id_eq'];
        }

        if( isset($params['orgcode']) && !empty($params['orgcode']) ){
            $sql .= " and o.orgcode like '".$params['orgcode']."%'";
        }

        if( isset($params['org_operator_id']) && !empty($params['org_operator_id']) ){
            $sql .= " and t.org_operator_id = ".$params['org_operator_id'];
        }

        if( isset($params['orgRootIn']) && !empty($params['orgRootIn']) ){
            $sql .= " and left(o.orgcode,6) in ('".implode("','",$params['orgRootIn'])."')";
        }

        $sql .= " GROUP BY LEFT (o.orgcode,".$len." );";
        $list = Capsule::connection("slave")->select($sql);
        $map = [];
        foreach ($list as $_val){
            $map[$_val->orgroot] = [
                "operator_id" => $_val->operator_txt,
                "trades_id" => $_val->trades_id_str,
                "trade_num" => $_val->trade_sl,
                "trade_count" => $_val->trade_num,
                'money' => $_val->fee,
            ];
        }
        return $map;
    }



     //换签成功查询最后一条相近的trade_id
    static public function getTradesDataByTradesId($params = [])
    {
        $sql = "SELECT
	t.id,
	t.trade_money,
	t.trade_num,
	group_concat( DISTINCT IF ( t.org_operator_id IS NULL OR t.org_operator_id = 0, '空', t.org_operator_id ) ) AS operator_txt 
FROM
	oil_card_vice_trades AS t FORCE INDEX ( `idx_org_id_createtime` )
	LEFT JOIN oil_org AS o on t.org_id = o.id 
WHERE
	t.card_from NOT IN ( 40, 41 ) 
	AND t.oil_com NOT IN ( 6, 7, 8 ) 
	AND t.trade_type IN (".TradesType::getCashAndSelfOilTradeTypeArr(true).") ";

        if(isset($params['sign_end_time']) && $params['sign_end_time'] != ""){
            $sql .= " and t.createtime > '".$params['sign_end_time']."'";
        }

        if( isset($params['trade_id_gt']) && !empty($params['trade_id_gt']) ){
            $sql .= " and t.id > ".$params['trade_id_gt'];
        }

        if( isset($params['orgcode']) && !empty($params['orgcode']) ){
            $sql .= " and o.orgcode like '".$params['orgcode']."%'";
        }

        if( isset($params['org_operator_id']) && !empty($params['org_operator_id']) ){
            $sql .= " and t.org_operator_id = ".$params['org_operator_id'];
        }

        $sql .= " AND t.cancel_sn != NULL LIMIT 1";
        Log::info('getTradesDataByTradesId====sql--'.var_export($sql,true),[],'getOrgSignDetails_');
        return Capsule::connection("slave")->select($sql);
    }

    static public function getTradeWithExt($id = 0)
    {
        $fields = "oil_card_vice_trades.id,oil_card_vice_trades.trade_type,oil_card_vice_trades.createtime,
        oil_card_vice_trades.org_operator_id,oil_card_vice_trades.org_id,ext.operator_id,t.oil_base_id";
        $data = Capsule::connection("slave")
            ->table('oil_card_vice_trades as oil_card_vice_trades')
            ->leftJoin('oil_card_vice_trades_ext as ext', 'ext.trades_id', '=', 'oil_card_vice_trades.id')
            ->leftJoin('oil_type_no as t', 't.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->where('oil_card_vice_trades.id',$id)
            ->select(Capsule::raw($fields))
            ->first();
        return $data;
    }


    /**
     * 根据查询条件拼接关联的表
     */
    public static function joinTables($condition, $sqlObj) {
            $columPrefixArr = array_column($condition, 'column');
            $tablePrexixArr = [];
            foreach ($columPrefixArr as $colomn) {
                $tablePrexixArr[] = substr($colomn, 0, strpos($colomn, '.'));
            }
            if(in_array('oil_org', $tablePrexixArr)) {
                $sqlObj->leftJoin('oil_org', function ($query) {
                    $query->on('oil_org.id', '=', 'oil_card_vice_trades.org_id')->where('oil_org.is_del', '=', 0);
                });
            }
            if(in_array('oil_station', $tablePrexixArr)) {
                $sqlObj->leftJoin('oil_station', function ($query) {
                    $query->on('oil_station.station_name', '=', 'oil_card_vice_trades.trade_place')->where('oil_station.is_del', '=', 0);
                });
            }
            if(in_array('oil_type_no', $tablePrexixArr)) {
                $sqlObj->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name');
            }
            if(in_array('oil_card_main', $tablePrexixArr)) {
                $sqlObj->leftJoin('oil_card_main', 'oil_card_main.main_no', '=', 'oil_card_vice_trades.main_no');
            }
            if(in_array('oil_card_vice', $tablePrexixArr)) {
                $sqlObj->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no');
            }
            if(in_array('org_fanli', $tablePrexixArr)) {
                $sqlObj->leftJoin('oil_org as org_fanli', 'org_fanli.id', '=', 'oil_card_vice.org_id_fanli');
            }
            if(in_array('ext', $tablePrexixArr)) {
                $sqlObj->leftJoin('oil_card_vice_trades_ext as ext', 'ext.trades_id', '=', 'oil_card_vice_trades.id');
            }
            if(in_array('ssp', $tablePrexixArr)) {
                $sqlObj->leftJoin('oil_station_supplier as ssp', 'ssp.id', '=', 'ext.supplier_id');
            }
            if(in_array('mark', $tablePrexixArr)) {
                $sqlObj->leftJoin('oil_card_vice_trade_mark as mark', 'mark.vice_trade_id', '=', 'oil_card_vice_trades.id');
            }
            return $sqlObj;

    }

    public static function sumTradesByOrgIdListForWhite(array $params)
    {
        Log::error('params-sumTradesByOrgIdList' . var_export($params, TRUE), [], 'white_trades_');
        $sqlObj = Capsule::connection('online_only_read')->table('oil_card_vice_trades')
            ->from(Capsule::raw('`oil_card_vice_trades` FORCE INDEX (`idx_org_id_createtime`)'))
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->leftJoin('oil_card_vice', 'oil_card_vice.vice_no', '=', 'oil_card_vice_trades.vice_no')
            ->leftJoin('oil_card_main', 'oil_card_main.id', '=', 'oil_card_vice.card_main_id')
            ->whereIn('oil_card_vice_trades.trade_type', TradesType::getCashAndSelfOilTradeTypeArr())//TradesType::getFanLiTradeType()
            ->whereNotIn('oil_card_vice_trades.oil_com', [6, 7, 8])
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41]);

        if(isset($params['open_invoice_null']) && $params['open_invoice_null'] == 1){
            $sqlObj->whereNull('oil_card_vice_trades.is_open_invoice');
        }

        if (isset($params['org_id_list']) && $params['org_id_list']) {
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $params['org_id_list']);
        }

        if (isset($params['orgcode']) && $params['orgcode']) {
            $orgIds = OilOrg::getByOrgcodeLike($params['orgcode']);
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $orgIds);
        }

        if (isset($params['oil_type_list'])) {
            $sqlObj->whereIn('oil_type_no.oil_type', $params['oil_type_list']);
        }

        if (isset($params['oil_type_eq'])) {
            $sqlObj->where('oil_type_no.oil_type', $params['oil_type_eq']);
        }

        if (isset($params['oil_type_not_in'])) {
            $sqlObj->whereNotIn('oil_type_no.oil_type', $params['oil_type_not_in']);
        }

        if (isset($params['oil_type_neq'])) {
            $sqlObj->where('oil_type_no.oil_type', '!=', $params['oil_type_neq']);
        }

        if(isset($params['oil_2_level']) && count($params['oil_2_level']) > 0){
            $sqlObj->whereIn('oil_type_no.oil_base_id', $params['oil_2_level']);
        }

        if (isset($params['oil_comIn']) && !empty($params['oil_comIn'])) {
            $sqlObj->whereIn('oil_card_vice_trades.oil_com', $params['oil_comIn']);
        }

        if (isset($params['tradetimeLe']) && $params['tradetimeLe']) {
            $sqlObj->where('oil_card_vice_trades.trade_time', '<=', $params['tradetimeLe']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        if (isset($params['createtimeLne']) && $params['createtimeLne']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['createtimeLne']);
        }

        if (isset($params['sign_lock_time']) && $params['sign_lock_time']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['sign_lock_time']);
        }

        if (isset($params['receipt_operator_id']) && $params['receipt_operator_id']) {
            $sqlObj->where('oil_card_vice_trades.org_operator_id', '=', $params['receipt_operator_id']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['pay_company_id']) && $params['pay_company_id']) {
            $sqlObj->where('oil_card_vice_trades.pay_company_id', '=', $params['pay_company_id']);
        }

        if(isset($params['white_flag_field']) && $params['white_flag_field']){
            $sqlObj = $sqlObj->selectRaw('min(oil_card_vice_trades.createtime) as min_time');
            Log::error('get_min_time', [$sqlObj->toSql(), $sqlObj->getBindings()], 'white_trades_');
            return $sqlObj->first();
        }

        if (isset($params['skip']) && isset($params['take'])) {
            $sqlObj = $sqlObj->selectRaw('oil_card_vice_trades.*');
            $sqlObj->whereNotExists(function ($query) use ($params) {
                $query = $query->select('oil_card_vice_trades_ext.id')
                    ->from('oil_card_vice_trades_ext')
                    ->whereRaw('oil_card_vice_trades_ext.trades_id=oil_card_vice_trades.id')
                    ->where('can_invoice', CardTradeConf::CAN_INVOICE_NO);
            });
            $result = $sqlObj->orderBy('oil_card_vice_trades.id', 'asc')->skip(intval($params['skip']))->take(intval($params['take']))->get();
            return $result;
        }

        Log::error('params-sumTradesByOrgIdList-sql' . var_export($sqlObj->toSql(), TRUE), [$sqlObj->getBindings()], 'white_trades_');
        $total_trade_money = $sqlObj->sum('oil_card_vice_trades.trade_money');
        $total_fanli = $sqlObj->sum('oil_card_vice_trades.use_fanli_money');

        Log::error('money&fanli', [$sqlObj->toSql(), $sqlObj->getBindings()], 'white_trades_');

        // 不可开票的消费额
        $noObj = $sqlObj->leftJoin('oil_card_vice_trades_ext', 'oil_card_vice_trades_ext.trades_id', '=', 'oil_card_vice_trades.id')
            ->where('can_invoice', CardTradeConf::CAN_INVOICE_NO);

        $trade_money_cant_invoice = $noObj->sum('oil_card_vice_trades.trade_money');

        Log::error('核销_cant_invoice', [$sqlObj->toSql(), $sqlObj->getBindings()], 'white_trades_');

        Log::error('result', [
            'total_trade_money' => $total_trade_money ,
            'trade_money_cant_invoice'=> $trade_money_cant_invoice,
            'total_fanli' => $total_fanli
        ], 'white_trades_');

        return ['total_trade_money' => $total_trade_money - $trade_money_cant_invoice,'total_fanli'=>$total_fanli];
    }

     //山东高速的拆单统计
    static public function sumTradeMoneyForSG($params = [])
    {
        Log::error('params-sumTradesByOrgIdList-params' , [$params], 'ssseeeerrrrggg');
        $sqlObj = Capsule::connection('online_only_read')->table('oil_card_vice_sub_trades')
            ->leftJoin('oil_card_vice_trades', 'oil_card_vice_trades.id', '=', 'oil_card_vice_sub_trades.trades_id')
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->whereIn('oil_card_vice_trades.trade_type', TradesType::getCashAndSelfOilTradeTypeArr())
            ->whereNotIn('oil_card_vice_trades.oil_com', [6, 7, 8])
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41]);

        if (isset($params['org_id_list']) && $params['org_id_list']) {
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $params['org_id_list']);
        }

        if (isset($params['orgcode']) && $params['orgcode']) {
            $orgIds = OilOrg::getByOrgcodeLike($params['orgcode']);
            $sqlObj->whereIn('oil_card_vice_trades.org_id', $orgIds);
        }

        //中石油的加油卡，支持天然气品类的开票,需求号：ENINET-3853
        if (isset($params['oil_type_eq'])) {
            $sqlObj->where('oil_type_no.oil_type', $params['oil_type_eq']);
        }

        if (isset($params['oil_type_list'])) {
            $sqlObj->whereIn('oil_type_no.oil_type', $params['oil_type_list']);
        }

        if (isset($params['oil_type_not_in'])) {
            $sqlObj->whereNotIn('oil_type_no.oil_type', $params['oil_type_not_in']);
        }

        if (isset($params['oil_type_neq'])) {
            $sqlObj->where('oil_type_no.oil_type', '!=', $params['oil_type_neq']);
        }

        if (isset($params['oil_comIn']) && !empty($params['oil_comIn'])) {
            $sqlObj->whereIn('oil_card_vice_trades.oil_com', $params['oil_comIn']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['createtimeLe']);
        }

        if (isset($params['createtimeLne']) && $params['createtimeLne']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<', $params['createtimeLne']);
        }

        if (isset($params['receipt_operator_id']) && $params['receipt_operator_id']) {
            $sqlObj->where('oil_card_vice_trades.org_operator_id', '=', $params['receipt_operator_id']);
        }

        if (isset($params['trade_company_id']) && !empty($params['trade_company_id']) ) {
            $sqlObj->where('oil_card_vice_sub_trades.company_id', '=', $params['trade_company_id']);
        }

        Log::error('params-sumTradesByOrgIdList-sql' . var_export($sqlObj->toSql(), TRUE), [$sqlObj->getBindings()], 'ssseeeerrrrggg');
        $total_trade_money = $sqlObj->sum('oil_card_vice_sub_trades.trade_money');

        return ['total_trade_money' => $total_trade_money];
    }
}
