<?php
/**
 * 油站供应商账户快照表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/09/14
 * Time: 18:20:04
 */
namespace Models;
use Fuel\Defines\CooperationType;
use Fuel\Defines\SupplierAccountConf;
use Fuel\Service\SupplierAccount;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSupplierAccountSnapshot extends \Framework\Database\Model
{
    protected $table = 'oil_supplier_account_snapshot';

    protected $guarded = ["id"];
    protected $fillable = ['snapshot_date','account_no','supplier_id','res_type','res_id','pid','balance','card_balance','rebate_total','recharge_total','trade_total','tranfer_total','adjust_total','clearing_total','assign_total','account_type','status','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By snapshot_date
        if (isset($params['snapshot_date']) && $params['snapshot_date'] != '') {
            $query->where('snapshot_date', '=', $params['snapshot_date']);
        }

        if (isset($params['ids']) && is_array($params['ids']) && !empty($params['ids']))
            $query->whereIn('id', $params['ids']);

        //Search By account_no
        if (isset($params['account_no']) && $params['account_no'] != '') {
            $query->where('account_no', '=', $params['account_no']);
        }

        //Search By supplier_id
        if (isset($params['supplier_id']) && $params['supplier_id'] != '') {
            $query->where('oil_supplier_account_snapshot.supplier_id', '=', $params['supplier_id']);
        }

        //Search By res_type
        if (isset($params['res_type']) && $params['res_type'] != '') {
            if (is_array($params['res_type'])) {

                $query->whereIn('res_type', $params['res_type']);
            } else {

                $query->where('res_type', '=', $params['res_type']);
            }
        }

        //Search By res_id
        if (isset($params['res_id']) && $params['res_id'] != '') {
            $query->where('res_id', '=', $params['res_id']);
        }

        if (isset($params['res_idIn']) && $params['res_idIn'] != '') {
            $query->whereIn('res_id', $params['res_idIn']);
        }

        //Search By pid
        if (isset($params['pid']) && $params['pid'] != '') {
            $query->where('pid', '=', $params['pid']);
        }

        //Search By balance
        if (isset($params['balance']) && $params['balance'] != '') {
            $query->where('balance', '=', $params['balance']);
        }

        //Search By card_balance
        if (isset($params['card_balance']) && $params['card_balance'] != '') {
            $query->where('card_balance', '=', $params['card_balance']);
        }

        //Search By rebate_total
        if (isset($params['rebate_total']) && $params['rebate_total'] != '') {
            $query->where('rebate_total', '=', $params['rebate_total']);
        }

        //Search By recharge_total
        if (isset($params['recharge_total']) && $params['recharge_total'] != '') {
            $query->where('recharge_total', '=', $params['recharge_total']);
        }

        //Search By trade_total
        if (isset($params['trade_total']) && $params['trade_total'] != '') {
            $query->where('trade_total', '=', $params['trade_total']);
        }

        //Search By tranfer_total
        if (isset($params['tranfer_total']) && $params['tranfer_total'] != '') {
            $query->where('tranfer_total', '=', $params['tranfer_total']);
        }

        //Search By adjust_total
        if (isset($params['adjust_total']) && $params['adjust_total'] != '') {
            $query->where('adjust_total', '=', $params['adjust_total']);
        }

        //Search By clearing_total
        if (isset($params['clearing_total']) && $params['clearing_total'] != '') {
            $query->where('clearing_total', '=', $params['clearing_total']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('oil_supplier_account_snapshot.status', '=', $params['status']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        // 合作类型
        if (isset($params['cooperation_type']) && !empty($params['cooperation_type'])) {
            $query->where('oil_station_supplier.cooperation_type', '=', $params['cooperation_type']);
        }

        // 核算主体
        if (isset($params['settle_obj']) && !empty($params['settle_obj'])) {
            $query->where('oil_station_supplier.settle_obj', '=', $params['settle_obj']);
        }

        // 供应商关键字
        if (isset($params['supplier_keyword']) && !empty($params['supplier_keyword'])) {
            $query->where('oil_station_supplier.supplier_name', 'like', sprintf("%%%s%%", $params['supplier_keyword']));
        }

        // 服务区关键字
        if (isset($params['area_keyword']) && !empty($params['area_keyword'])) {
            $query->where('oil_station_area.name', 'like', sprintf("%%%s%%", $params['area_keyword']));
        }

        // 合作状态
        if (isset($params['cooperation_status']) && !empty($params['cooperation_status'])) {
            $query->where('oil_station_supplier.cooperation_status', '=', $params['cooperation_status']);
        }

        // 服务区状态
        if (isset($params['area_status']) && !empty($params['area_status'])) {
            $query->where('oil_station_area.status', '=', $params['area_status']);
        }

        // 主卡状态 - 账户状态
        if (isset($params['account_status']) && !empty($params['account_status'])) {
            $query->where('oil_card_main.account_status', '=', $params['account_status']);
        }
        // 主卡状态 - 普通主卡
        if (isset($params['is_jifen'])) {
            $query->where('oil_card_main.is_jifen', '=', $params['is_jifen']);
        }

        return $query;
    }

    /**
     * 油站供应商账户快照表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        // 账户等级
        if (!empty($params['account_level'])) {
            if ($params['account_level'] == 10) {
                $params['res_type'] = SupplierAccountConf::RES_TYPE_SUPPLIER;
            } else {
                $params['res_type'] = [SupplierAccountConf::RES_TYPE_AREA,SupplierAccountConf::RES_TYPE_MAIN_CARD,SupplierAccountConf::RES_TYPE_STATION_OPERATORS];
            }
            unset($params['account_level']);
        }
        $sqlObj = OilSupplierAccountSnapshot::Filter($params)
            ->leftJoin('oil_station_supplier', 'oil_station_supplier.id', '=', 'oil_supplier_account_snapshot.supplier_id')
            // 临时限制不展示主卡账户
            ->whereIn('oil_station_supplier.cooperation_type', [CooperationType::COOPERATION_TYPE_ZD,CooperationType::COOPERATION_TYPE_PT])
            ->select(Capsule::connection()
                ->raw('oil_supplier_account_snapshot.*,
                             oil_station_supplier.supplier_name,
                             oil_station_supplier.cooperation_type,
                             oil_station_supplier.settle_obj,
                             oil_station_supplier.cooperation_status'
                )
            );
        if(isset($params['count']) && $params['count'] == 1){
            $data = $sqlObj->count();
        }elseif(isset($params['take']) && isset($params['skip'])){
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->orderBy('createtime', 'desc')->get();
        }elseif(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->orderBy('id', 'asc')->orderBy('createtime', 'asc')->get();
        }else{
            $data = $sqlObj->orderBy('id', 'asc')->orderBy('createtime', 'asc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        if (count($data) > 0) {
            $data = self::formatData($data);
        }

        return $data;
    }

    /**
     * 格式化列表数据
     * @param $data
     */
    public static function formatData($data)
    {
        foreach ($data as &$val) {
            $val = self::formatInfo($val);
        }

        return $data;
    }

    /**
     * 格式化信息
     * @param $info
     * @param $formatDegree
     * @return mixed
     */
    public static function formatInfo($info, $formatDegree = 1)
    {
        if (empty($info)) {
            return [];
        }

        $ret = $info;
        if (empty($info['cooperation_type'])
            || empty($info['settle_obj'])
            || empty($info['supplier_name'])) {
            $fields = ['supplier_name','cooperation_type','cooperation_status','settle_obj'];
            $supplierInfo                  = OilStationSupplier::getInfoByFilter(['id'=>$info['supplier_id']], $fields);
            $ret['cooperation_type']       = $supplierInfo['cooperation_type'];
            $ret['supplier_name']          = $supplierInfo['supplier_name'];
            $ret['settle_obj']             = $supplierInfo['settle_obj'];
        }

        // 配置值
        $ret['cooperation_type_value'] = CooperationType::$list[$ret['cooperation_type']];
        $ret['res_type_value']         = SupplierAccountConf::$res_type[$info['res_type']];
        $ret['status_value']           = SupplierAccountConf::$account_status[$info['status']];
        $ret['settle_obj_value']       = (new SupplierAccount())->getSettleObj($ret['settle_obj'], $ret['cooperation_type'])['value'];

        // resInfo 根据不同资源取不同信息
        switch ($info['res_type']) {
            case SupplierAccountConf::RES_TYPE_SUPPLIER:
                $ret['account_name'] = $ret['supplier_name'];
                $ret['account_level'] = 10;
                $ret['account_level_value'] = '1级';
                break;
            case SupplierAccountConf::RES_TYPE_AREA:
                $ret['account_name'] = OilStationArea::getResField(['id'=>$info['res_id']], 'name');
                $ret['account_level'] = 20;
                $ret['account_level_value'] = '2级';
                break;
            case SupplierAccountConf::RES_TYPE_MAIN_CARD:
                $ret['account_name'] = OilCardMain::getResField(['id'=>$info['res_id']], 'main_no');
                $ret['account_level'] = 20;
                $ret['account_level_value'] = '2级';
                break;
            case SupplierAccountConf::RES_TYPE_STATION_OPERATORS:
                $ret['account_name'] = OilStationOperators::getResField(['id'=>$info['res_id']], 'operators_name');
                $ret['account_level'] = 20;
                $ret['account_level_value'] = '2级';
                break;
            default:
                $ret['account_name'] = '';
                $ret['account_level'] = 0;
                $ret['account_level_value'] = '';
        }

        // 卡内余额 @todo
        $ret['card_main_balance'] = 0;
        unset($ret['account_type']);

        // 累计消费及清分用绝对值
        $ret['total_trade']    = abs($info['total_trade']);
        $ret['total_clearing'] = abs($info['total_clearing']);

        return $ret;
    }

    /**
     * 油站供应商账户快照表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierAccountSnapshot::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierAccountSnapshot::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 油站供应商账户快照表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSupplierAccountSnapshot::create($params);
    }

    /**
     * 油站供应商账户快照表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierAccountSnapshot::find($params['id'])->update($params);
    }

    /**
     * 油站供应商账户快照表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSupplierAccountSnapshot::destroy($params['ids']);
    }




}