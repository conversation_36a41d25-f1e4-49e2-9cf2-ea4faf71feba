<?php
/**
 * 上游返利记录充值返利扩展表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/11/10
 * Time: 17:31:57
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSupplierFanliRechargeExt extends \Framework\Database\Model
{
    protected $table = 'oil_supplier_fanli_recharge_ext';

    protected $guarded = ["id"];
    protected $fillable = ['fanli_id','payment_id','payment_no','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By fanli_id
        if (isset($params['fanli_id'])) {
            if (is_array($params['fanli_id']) && !empty($params['fanli_id'])) {
                $query->whereIn('fanli_id', $params['fanli_id']);
            } elseif ($params['fanli_id'] != '') {
                $query->where('fanli_id', '=', $params['fanli_id']);
            }
        }

        //Search By payment_id
        if (isset($params['payment_id']) && $params['payment_id'] != '') {
            $query->where('payment_id', '=', $params['payment_id']);
        }

        //Search By payment_no
        if (isset($params['payment_no']) && $params['payment_no'] != '') {
            $query->where('payment_no', '=', $params['payment_no']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 上游返利记录充值返利扩展表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilSupplierFanliRechargeExt::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 上游返利记录充值返利扩展表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierFanliRechargeExt::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierFanliRechargeExt::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 上游返利记录充值返利扩展表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSupplierFanliRechargeExt::create($params);
    }

    /**
     * 上游返利记录充值返利扩展表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierFanliRechargeExt::find($params['id'])->update($params);
    }

    /**
     * 上游返利记录充值返利扩展表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSupplierFanliRechargeExt::destroy($params['ids']);
    }

    static public function removeByFanLiIds(array $ids)
    {
        if (empty($ids))
            return;

        return OilSupplierFanliRechargeExt::whereIn('fanli_id', $ids)->delete();
    }






}