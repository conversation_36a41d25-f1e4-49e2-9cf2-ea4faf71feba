<?php
/**
 * 运营商开户行
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/08/15
 * Time: 12:23:40
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOperatorsBank extends \Framework\Database\Model
{
    protected $table = 'oil_operators_bank';

    protected $guarded = ["id"];
    protected $fillable = ['operators_id','bank','card_no','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By operators_id
        if (isset($params['operators_id']) && $params['operators_id'] != '') {
            $query->where('operators_id', '=', $params['operators_id']);
        }

        //Search By bank
        if (isset($params['bank']) && $params['bank'] != '') {
            $query->where('bank', '=', $params['bank']);
        }

        //Search By card_no
        if (isset($params['card_no']) && $params['card_no'] != '') {
            $query->where('card_no', '=', $params['card_no']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 运营商开户行 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOperatorsBank::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 运营商开户行 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOperatorsBank::find($params['id']);
    }

    /**
     * 运营商开户行 详情查询
     */
    static public function getByOperatorsId($operators_id)
    {
        return OilOperatorsBank::where('operators_id','=',$operators_id)->get()->toArray();
    }

    /**
     * 运营商开户行 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOperatorsBank::create($params);
    }

    /**
     * 运营商开户行 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOperatorsBank::find($params['id'])->update($params);
    }

    /**
     * 运营商开户行 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOperatorsBank::destroy($params['ids']);
    }




}