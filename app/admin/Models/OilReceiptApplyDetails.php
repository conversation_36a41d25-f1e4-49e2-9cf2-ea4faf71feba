<?php
/**
 * oil_receipt_apply_details
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:19
 */

namespace Models;

use Framework\Helper;
use Framework\Log;
use Fuel\Defines\OilType;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptApplyDetails extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_apply_details';

    protected $guarded = ["id"];

    protected $fillable = [
        'receipt_apply_id', 'trade_num', 'origin_trade_num', 'receipt_money', 'trade_money', 'fanli_money', 'trades_id',
        'use_fanli_money', 'creator_id', 'createtime', 'other_creator_id', 'other_creator', 'last_operator', 'updatetime', 'remark'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public function ReceiptTrades()
    {
        return $this->belongsTo('Models\OilCardViceTrades', 'trades_id', 'id');
    }


    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By receipt_apply_id
        if (isset($params['receipt_apply_id']) && $params['receipt_apply_id'] != '') {
            $query->where('oil_receipt_apply_details.receipt_apply_id', '=', $params['receipt_apply_id']);
        }

        //Search By trade_num
        if (isset($params['trade_num']) && $params['trade_num'] != '') {
            $query->where('oil_receipt_apply_details.trade_num', '=', $params['trade_num']);
        }

        //Search By trade_money
        if (isset($params['trade_money']) && $params['trade_money'] != '') {
            $query->where('oil_receipt_apply_details.trade_money', '=', $params['trade_money']);
        }

        //Search By fanli_money
        if (isset($params['fanli_money']) && $params['fanli_money'] != '') {
            $query->where('oil_receipt_apply_details.fanli_money', '=', $params['fanli_money']);
        }

        //Search By trades_id
        if (isset($params['trades_id']) && $params['trades_id'] != '') {
            $query->where('oil_receipt_apply_details.trades_id', '=', $params['trades_id']);
        }

        //Search By creator_id
        if (isset($params['creator_id']) && $params['creator_id'] != '') {
            $query->where('oil_receipt_apply_details.creator_id', '=', $params['creator_id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('oil_receipt_apply_details.createtime', '=', $params['createtime']);
        }

        //Search By other_creator_id
        if (isset($params['other_creator_id']) && $params['other_creator_id'] != '') {
            $query->where('oil_receipt_apply_details.other_creator_id', '=', $params['other_creator_id']);
        }

        //Search By other_creator
        if (isset($params['other_creator']) && $params['other_creator'] != '') {
            $query->where('oil_receipt_apply_details.other_creator', '=', $params['other_creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('oil_receipt_apply_details.last_operator', '=', $params['last_operator']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_receipt_apply_details.updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_receipt_apply_details 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        Capsule::connection("slave")->enableQueryLog();
        $sqlObj = Capsule::connection("slave")->table('oil_receipt_apply_details');

        $field = 'oil_receipt_apply_details.*,oil_card_vice_trades.oil_name,oil_type_no.oil_type,oil_type_no.oil_sec_type,
            oil_type_no.product_name,oil_type_no.unit,oil_type_no.receipt_oil_name,
            sum(oil_receipt_apply_details.trade_num) as trade_num_sum,
            sum(oil_receipt_apply_details.receipt_money) as receipt_money_sum,
            sum(oil_receipt_apply_details.fanli_money) as fanli_money_sum,
            sum(oil_receipt_apply_details.use_fanli_money) as use_fanli_money_sum,
            sum(oil_receipt_apply_details.trade_money) as trade_money_sum
        ';

        if (isset($params['oil_name_group_for_split']) && $params['oil_name_group_for_split']) {
            $field = 'oil_receipt_apply_details.*,oil_type_no.oil_type,oil_type_no.oil_sec_type,
            (case when `oil_type_no`.receipt_oil_name is NULL or `oil_type_no`.receipt_oil_name = \'\' THEN `oil_type_no`.`oil_no` else `oil_type_no`.receipt_oil_name end)	as oil_receipt_name,
                oil_type_no.product_name,oil_type_no.unit,
                sum(oil_receipt_apply_details.trade_num) as trade_num_sum,
                sum(oil_receipt_apply_details.receipt_money) as receipt_money_sum,
                sum(oil_receipt_apply_details.fanli_money) as fanli_money_sum,
                sum(oil_receipt_apply_details.use_fanli_money) as use_fanli_money_sum,
                sum(oil_receipt_apply_details.trade_money) as trade_money_sum
            ';
        }

        $sqlObj->selectRaw($field);

//        $sqlObj->select('oil_receipt_apply_details.*', 'oil_card_vice_trades.oil_name', 'oil_type_no.oil_type',
//            'oil_type_no.oil_sec_type', 'oil_type_no.product_name', 'oil_type_no.unit',
//            Capsule::connection()->raw("sum(oil_receipt_apply_details.trade_num) as trade_num_sum,
//            sum(oil_receipt_apply_details.receipt_money) as receipt_money_sum,sum(oil_receipt_apply_details.fanli_money) as fanli_money_sum,
//            sum(oil_receipt_apply_details.use_fanli_money) as use_fanli_money_sum,
//            sum(oil_receipt_apply_details.trade_money) as trade_money_sum"));
        $sqlObj
            ->leftJoin('oil_card_vice_trades', 'oil_card_vice_trades.id', '=', 'oil_receipt_apply_details.trades_id')
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_card_vice_trades.oil_name')
            ->orderBy('trade_time', 'asc');

        if (isset($params['receipt_apply_id']) && $params['receipt_apply_id']) {
            $sqlObj->where('oil_receipt_apply_details.receipt_apply_id', '=', $params['receipt_apply_id']);
        }

        if (isset($params['receipt_apply_idIn']) && $params['receipt_apply_idIn']) {
            $sqlObj->whereIn('oil_receipt_apply_details.receipt_apply_id',  $params['receipt_apply_idIn']);
        }

        if (isset($params['check_oil_sec_type']) && $params['check_oil_sec_type']) {
            $sqlObj->where(function ($sqlObj) use ($params) {
                $sqlObj->whereNull('oil_type_no.oil_sec_type');
                $sqlObj->orWhere('oil_type_no.oil_sec_type', '');
            });
        }

        if (isset($params['order_id']) && $params['order_id'] != "") {
            $_id = $params['order_id'];
            if (strlen($_id) > 10) {
                $sqlObj->where("oil_card_vice_trades.api_id", strval($_id));
            } else {
                $sqlObj->where("oil_card_vice_trades.id", $_id);
            }
        }

        $sqlObj->groupBy('oil_receipt_apply_details.receipt_apply_id');

        if (isset($params['type_no_group']) && $params['type_no_group']) {
            $sqlObj->groupBy('oil_type_no.oil_type');
        }
        if (isset($params['oil_name_group']) && $params['oil_name_group']) {
            $sqlObj->groupBy('oil_card_vice_trades.oil_name');
        }

        if (isset($params['oil_name_group_for_split']) && $params['oil_name_group_for_split']) {
            $sqlObj->groupBy('oil_receipt_name');
        }

        if (!empty($params['oil_sec_type_group'])) {
            $sqlObj->groupBy('oil_type_no.oil_sec_type');
        }

        if(isset($params['receipt_apply_id_group']) && $params['receipt_apply_id_group']) {
            $sqlObj->groupBy('oil_receipt_apply_details.receipt_apply_id');
        }

        if (!((isset($params['type_no_group']) && $params['type_no_group']) ||
            (isset($params['oil_name_group']) && $params['oil_name_group']) ||
            !empty($params['oil_sec_type_group']))) {
            $sqlObj->groupBy('oil_type_no.product_name');
        }

        $data = $sqlObj->get();
        $sql = Capsule::connection("slave")->getQueryLog();
        //print_r($sql);exit;
        Log::error('receiptSplit-sql-2:',[$sql],'receiptSplit');

        if(isset($params['is_direct_return']) && $params['is_direct_return'] == 1){
            return $data;
        }

        if (count($data) > 0) {
            foreach ($data as &$v) {
                if(!isset($params['oil_name_group_for_split']) || !$params['oil_name_group_for_split']){

                    if ((isset($params['type_no_group']) && $params['type_no_group']) && (!isset($params['oil_name_group']) || !$params['oil_name_group'])) {
                        $v->oil_name = '';
                        $v->unit = '';
                    }

                    if ((!isset($params['type_no_group']) || !$params['type_no_group']) && (!isset($params['oil_name_group']) || !$params['oil_name_group'])) {
                        $v->oil_name = '';
                        $v->oil_type = '';
                        $v->unit = '';
                    }

                }else{
                    $v->oil_name = $v->oil_receipt_name;
                }
            }
        }

        return $data;
    }

    /**
     * oil_receipt_apply_details 列表查询
     * @param array $params
     * @return array
     */
    static public function getListForInternal(array $params)
    {
        Capsule::connection()->enableQueryLog();
        $sqlObj = Capsule::connection()->table('oil_receipt_apply_details');

        $field = 'oil_receipt_apply_details.*,oil_trades.oil_name,oil_trades.supplier_id,oil_type_no.oil_type,oil_type_no.oil_sec_type,
        oil_type_no.product_name,oil_type_no.unit,
        sum(oil_receipt_apply_details.trade_num) as trade_num_sum,
        sum(oil_receipt_apply_details.receipt_money) as receipt_money_sum,
        sum(oil_receipt_apply_details.fanli_money) as fanli_money_sum,
        sum(oil_receipt_apply_details.use_fanli_money) as use_fanli_money_sum,
        sum(oil_receipt_apply_details.trade_money) as trade_money_sum
        ';

        if (isset($params['oil_name_group_for_split']) && $params['oil_name_group_for_split']) {
            $field = 'oil_receipt_apply_details.*,oil_trades.supplier_id,oil_type_no.oil_type,oil_type_no.oil_sec_type,
            (case when `oil_type_no`.receipt_oil_name is NULL or `oil_type_no`.receipt_oil_name = \'\' THEN `oil_type_no`.`oil_no` else `oil_type_no`.receipt_oil_name end)	as oil_receipt_name,
            oil_type_no.product_name,oil_type_no.unit,
            sum(oil_receipt_apply_details.trade_num) as trade_num_sum,
            sum(oil_receipt_apply_details.receipt_money) as receipt_money_sum,
            sum(oil_receipt_apply_details.fanli_money) as fanli_money_sum,
            sum(oil_receipt_apply_details.use_fanli_money) as use_fanli_money_sum,
            sum(oil_receipt_apply_details.trade_money) as trade_money_sum';
        }

        $sqlObj->selectRaw($field);

//        $sqlObj->select('oil_receipt_apply_details.*', 'oil_trades.oil_name', 'oil_trades.supplier_id', 'oil_type_no.oil_type',
//            'oil_type_no.oil_sec_type', 'oil_type_no.product_name', 'oil_type_no.unit',
//            Capsule::connection()->raw("sum(oil_receipt_apply_details.trade_num) as trade_num_sum,
//            sum(oil_receipt_apply_details.receipt_money) as receipt_money_sum,sum(oil_receipt_apply_details.fanli_money) as fanli_money_sum,
//            sum(oil_receipt_apply_details.use_fanli_money) as use_fanli_money_sum,
//            (case when `oil_type_no`.receipt_oil_name is NULL or `oil_type_no`.receipt_oil_name = '' THEN `oil_type_no`.`oil_no` else `oil_type_no`.receipt_oil_name end)	as oil_name,
//            sum(oil_receipt_apply_details.trade_money) as trade_money_sum"));
        $sqlObj
            ->leftJoin('oil_trades', 'oil_trades.trades_id', '=', 'oil_receipt_apply_details.trades_id')
            ->leftJoin('oil_type_no', 'oil_type_no.oil_no', '=', 'oil_trades.oil_name')
            ->orderBy('trade_createtime', 'asc');

        if (isset($params['receipt_apply_id']) && $params['receipt_apply_id']) {
            $sqlObj->where('oil_receipt_apply_details.receipt_apply_id', '=', $params['receipt_apply_id']);
        }

        if (isset($params['order_id']) && $params['order_id'] != "") {
            $_id = $params['order_id'];
            if (strlen($_id) > 10) {
                //$sqlObj->where("oil_trades.api_id", strval($_id));
            } else {
                $sqlObj->where("oil_trades.trades_id", $_id);
            }
        }

        $sqlObj->whereNotNull('oil_trades.supplier_id'); //temp

        $sqlObj->groupBy('oil_receipt_apply_details.receipt_apply_id');

        if (isset($params['type_no_group']) && $params['type_no_group']) {
            $sqlObj->groupBy('oil_type_no.oil_type');
        }
        if (isset($params['oil_name_group']) && $params['oil_name_group']) {
            $sqlObj->groupBy('oil_trades.oil_name');
            $sqlObj->groupBy('oil_trades.supplier_id');
        }

        if (isset($params['oil_name_group_for_split']) && $params['oil_name_group_for_split']) {
            $sqlObj->groupBy('oil_receipt_name');
            $sqlObj->groupBy('oil_trades.supplier_id');
        }

        if (!empty($params['oil_sec_type_group'])) {
            //$sqlObj->groupBy('oil_type_no.oil_sec_type');
            $sqlObj->groupBy('oil_trades.supplier_id');
        }

        if (!((isset($params['type_no_group']) && $params['type_no_group']) ||
            (isset($params['oil_name_group']) && $params['oil_name_group']) ||
            !empty($params['oil_sec_type_group']))) {
            $sqlObj->groupBy('oil_type_no.product_name');
        }

        $data = $sqlObj->get();
        $sql = Capsule::connection()->getQueryLog();
        //print_r($sql[0]['query']);exit;
        Log::error('receiptSplit-sql:',[$sql],'receiptSplit');

        if (count($data) > 0) {
            foreach ($data as &$v) {
                if(!$v->supplier_id){
                    throw new \RuntimeException($v->trades_id.'交易没有归属供应商',2);
                }
//                $v->oil_sec_type_name = OilType::$oil_sec_type[$v->oil_sec_type]['title'];
                $v->oil_sec_type_name = OilType::$internalOilTypeMap[$params['oil_type']] ?? '';

                if (isset($params['oil_name_group_for_split']) && $params['oil_name_group_for_split']) {
                    $v->oil_name = $v->oil_receipt_name;
                }
            }
        }

        return $data;
    }

    static public function get_list_for_detail(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $sqlObj = Capsule::connection()->table('oil_receipt_apply_details');
        $sqlObj->select('oil_receipt_apply_details.trade_money',
            'oil_receipt_apply_details.receipt_money',
            'oil_receipt_apply_details.use_fanli_money',
            'oil_receipt_apply_details.id',
            'oil_receipt_apply_details.trades_id',
            'oil_card_vice_trades.trade_time',
            'oil_card_vice_trades.createtime',
            'oil_card_vice_trades.oil_name');

        $sqlObj
            ->leftJoin('oil_card_vice_trades', 'oil_card_vice_trades.id', '=', 'oil_receipt_apply_details.trades_id')
            ->orderBy('oil_receipt_apply_details.trades_id', 'desc');

        if (isset($params['receipt_apply_id']) && $params['receipt_apply_id']) {
            $sqlObj->where('oil_receipt_apply_details.receipt_apply_id', '=', $params['receipt_apply_id']);
        }

        if (isset($params['trades_id']) && $params['trades_id']) {
            $sqlObj->where('oil_receipt_apply_details.trades_id', '=', $params['trades_id']);
        }

        if (isset($params['createtime_le']) && $params['createtime_le']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '<=', $params['createtime_le']);
        }

        if (isset($params['createtime_ge']) && $params['createtime_ge']) {
            $sqlObj->where('oil_card_vice_trades.createtime', '>=', $params['createtime_ge']);
        }

        if (isset($params['use_fanli_money_gt']) && $params['use_fanli_money_gt']) {
            $sqlObj->where('oil_receipt_apply_details.use_fanli_money', '>', $params['use_fanli_money_gt']);
        }

        if (isset($params['use_fanli_money_lt']) && $params['use_fanli_money_lt']) {
            $sqlObj->where('oil_receipt_apply_details.use_fanli_money', '<', $params['use_fanli_money_lt']);
        }

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    static public function arraySort($array, $keys, $sort = SORT_DESC)
    {
        $keysValue = [];
        foreach ($array as $k => $v) {
            $keysValue[$k] = $v->$keys;
        }
        array_multisort($keysValue, $sort, $array);
        return $array;
    }

    static public function getApplyDetails(array $params)
    {
        return self::Filter($params)->get();
    }

    static public function getApplyDetailsNum(array $params)
    {
        return self::Filter($params)->count();
    }

    //统计某字段的累计金额
    static public function sumFieldMoney(array $params, $field = "use_fanli_money")
    {
        return self::Filter($params)
            ->leftJoin('oil_receipt_apply', "oil_receipt_apply.id", '=', 'oil_receipt_apply_details.receipt_apply_id')
            ->where("oil_receipt_apply.receipt_status", ">=", 0)
            ->where("oil_receipt_apply.is_internal", "=", 2)
            //->sum($field)
            ->selectRaw("sum(". $field . ") as ".$field.",sum(trade_num) as num,sum(trade_money) as money")
            ->first();
    }

    /**
     * oil_receipt_apply_details 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilReceiptApplyDetails::find($params['id']);
    }

    /**
     * oil_receipt_apply_details 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptApplyDetails::create($params);
    }

    /**
     * oil_receipt_apply_details 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilReceiptApplyDetails::find($params['id'])->update($params);
    }

    /**
     * oil_receipt_apply_details 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilReceiptApplyDetails::destroy($params['ids']);
    }

    /**
     * @title 根据$receiptApplyId删除明细
     * @desc
     * @param $receiptApplyId
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Models
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     */
    static public function removeByReceiptApplyId($receiptApplyId)
    {
        return self::where('receipt_apply_id', $receiptApplyId)->delete();
    }

    /**
     * 查询库存节省
     * @param array $params
     * @return array
     */
    public static function checkSaveStock($params = [])
    {
        $condition = ' 1 ';
        if (isset($params['start_time']) && $params['start_time'] && isset($params['end_time']) && $params['end_time']) {
            $condition = ' and d.createtime >= "' . $params['start_time'] . '" and d.createtime <= "' . $params['end_time'] . '" ';
        }

        if (isset($params['orgcode']) && $params['orgcode']) {
            $condition = ' and c.orgcode like "' . $params['orgcode'] . '%" ';
        }

        $sql = "SELECT b.org_name,a.* FROM (
SELECT
  left(c.orgcode,6) orgcode,
  sum(a.trade_num) shi_kai,
  sum(a.origin_trade_num) ying_kai,
  sum(a.origin_trade_num - a.trade_num) jieyue
FROM
  oil_receipt_apply_details a
LEFT JOIN oil_card_vice_trades b ON b.id = a.trades_id
LEFT JOIN oil_org c on b.org_id = c.id
LEFT JOIN oil_receipt_apply d on d.id = a.receipt_apply_id 
WHERE
  a.origin_trade_num > a.trade_num
and d.receipt_status > -1 and " . $condition . "
GROUP BY
left(c.orgcode,6) ) a LEFT JOIN oil_org b on a.orgcode = b.orgcode";

        $result = Capsule::connection()->select($sql);

        return $result;
    }

    /**
     * 发票申请，导出占用的消费数据
     * @param array $params
     * @return array
     */
    public static function exportTradesDetails($params)
    {
        $field = "oil_card_vice_trades.id,oil_card_vice_trades.vice_no,oil_card_vice_trades.org_name,oil_card_vice_trades.trade_place,oil_card_vice_trades.trade_place_provice_name,oil_card_vice_trades.oil_name
        ,oil_card_vice_trades.trade_price,oil_card_vice_trades.trade_num,oil_card_vice_trades.trade_money,oil_card_vice_trades.use_fanli_money,oil_receipt_apply_details.receipt_money,oil_card_vice_trades.fanli_money,
        oil_card_vice_trades.fanli_jifen,oil_card_vice_trades.trade_time,oil_card_vice_trades.createtime,oil_type_no.receipt_oil_name";
        $sqlObj = self::select(Capsule::connection()->raw($field))
            ->leftJoin('oil_receipt_apply', 'oil_receipt_apply.id', '=', 'oil_receipt_apply_details.receipt_apply_id')
            ->leftJoin('oil_card_vice_trades', 'oil_card_vice_trades.id', '=', 'oil_receipt_apply_details.trades_id')
            ->leftJoin('oil_type_no', 'oil_card_vice_trades.oil_name', '=', 'oil_type_no.oil_no');

        if(isset($params['receipt_apply_id']) && $params['receipt_apply_id']) {
            $sqlObj = $sqlObj->where('oil_receipt_apply.id', $params['receipt_apply_id']);
        }

        if(isset($params['receipt_apply_idIn']) && count($params['receipt_apply_idIn']) > 0 ) {
            $sqlObj = $sqlObj->whereIn('oil_receipt_apply.id', $params['receipt_apply_idIn']);
        }

        $sqlObj = $sqlObj->orderBy('oil_card_vice_trades.createtime', 'desc');

        if (isset($params['count']) && $params['count']) {
            return $sqlObj->count();
        } elseif (isset($params['skip']) && isset($params['take'])) {
            return $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        } elseif (isset($params['_export']) && $params['_export'] == 1) {
            return $sqlObj->get();
        } else {
            return $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
    }

    public static function getApplyByTrade($params = [])
    {
        $sqlObj = self::Filter($params);
        $sqlObj->select('oil_receipt_apply_details.id as detail_id', 'oil_card_vice_trades.oil_name',
            'oil_receipt_apply_details.receipt_money','oil_receipt_apply_details.trade_num', "oil_card_vice_trades.org_id",
            "oil_receipt_apply.id", "oil_receipt_apply.no", "oil_receipt_apply_details.trade_money","oil_receipt_apply.is_internal",
            "oil_receipt_apply_details.use_fanli_money","oil_receipt_apply.receipt_amount","oil_receipt_apply.receipt_status","oil_receipt_apply.no",
            'oil_card_vice_trades.trade_money as o_money','oil_card_vice_trades.trade_num as o_num','oil_card_vice_trades.trade_price'
        );
        $sqlObj
            ->leftJoin('oil_card_vice_trades', 'oil_card_vice_trades.id', '=', 'oil_receipt_apply_details.trades_id')
            ->leftJoin('oil_receipt_apply', 'oil_receipt_apply.id', '=', 'oil_receipt_apply_details.receipt_apply_id')
            ->where('oil_receipt_apply.receipt_status', '!=', -1)
            ->orderBy('trade_time', 'asc');

        if(isset($params['is_internal']) && $params['is_internal'] != ''){
            $sqlObj = $sqlObj->where("oil_receipt_apply.is_internal",$params['is_internal']);
        }

        if(isset($params['receipt_statusIn']) && count($params['receipt_statusIn']) > 0){
            $sqlObj = $sqlObj->whereIn("oil_receipt_apply.receipt_status",$params['receipt_statusIn']);
        }

        if(isset($params['api_id']) && $params['api_id'] != ''){
            $sqlObj = $sqlObj->where("oil_card_vice_trades.api_id",$params['api_id']);
        }

        return $sqlObj->get()->toArray();
    }

    public static function getUnSetReceiptOilName($applyId)
    {
        $sql = "select  distinct(oil_type_no.oil_no)  from  oil_receipt_apply_details 
left join oil_trades on oil_trades.trades_id = oil_receipt_apply_details.trades_id 
left join oil_type_no on oil_type_no.oil_no = oil_trades.oil_name
where  (oil_type_no.receipt_oil_name is null or oil_type_no.receipt_oil_name = '') 
and oil_receipt_apply_details.receipt_apply_id = " . $applyId;
        return Capsule::connection()->select($sql);
    }

    public static function batchAdd($data = [])
    {
        return OilReceiptApplyDetails::insert($data);
    }

    public static function checkAndUpdateApplyToFetchAmountDiffByApplyId($receiptApplyId, $receiptAmount)
    {
        $applyAndFetchDiff = 1;
        if (bccomp(
                $receiptAmount,
                self::Filter([
                    'receipt_apply_id' => $receiptApplyId
                ])->sum('receipt_money'),
                2
            ) !== 0) {
            $applyAndFetchDiff = 2;
        }
        Log::error("申请与捞取明细金额对比结果", [
            'applyAndFetchDiff' => $applyAndFetchDiff,
            'receiptApplyId'    => $receiptApplyId,
            'receiptAmount'     => $receiptAmount,
        ], 'receiptApply');
        OilReceiptApply::edit([
            'id'                          => $receiptApplyId,
            'apply_fetch_amount_diff' => $applyAndFetchDiff,
        ]);
    }
}