<?php
/**
 * oil_receipt_apply_log
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/29
 * Time: 20:20:12
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilReceiptApplyLog extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_apply_log';

    protected $guarded = ["id"];

    protected $fillable = ['app_id','status','last_operator','last_operator_id','createtime','updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By app_id
        if (isset($params['app_id']) && $params['app_id'] != '') {
            $query->where('app_id', '=', $params['app_id']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By last_operator_id
        if (isset($params['last_operator_id']) && $params['last_operator_id'] != '') {
            $query->where('last_operator_id', '=', $params['last_operator_id']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        return $query;
    }

    /**
     * oil_receipt_apply_log 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptApplyLog::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_receipt_apply_log 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptApplyLog::find($params['id']);
    }

    /**
     * oil_receipt_apply_log 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptApplyLog::create($params);
    }

    /**
     * oil_receipt_apply_log 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptApplyLog::find($params['id'])->update($params);
    }

    /**
     * oil_receipt_apply_log 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptApplyLog::destroy($params['ids']);
    }

    /**
     * 发票申请单进度详情查询
     * @param array $params
     * @return object
     */
    static public function getProgressByAppId(array $params)
    {
        \helper::argumentCheck(['app_id'],$params);

        return OilReceiptApplyLog::where('app_id','=',$params['app_id'])->get();
    }

    static public function removeByAppId($appId)
    {
        return self::where('app_id',$appId)->delete();
    }


}