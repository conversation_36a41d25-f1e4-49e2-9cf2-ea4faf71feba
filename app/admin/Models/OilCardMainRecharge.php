<?php
/**
 * 油品主卡充值记录表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:18
 */

namespace Models;

use Framework\Log;
use Framework\SDK\Sign\OilAgent;
use Fuel\Defines\CardFrom;
use Fuel\Defines\OilCom;
use Fuel\Request\dspClient;
use Fuel\Request\OilAgentClient;
use G7Pay\Core\Card;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardMainRecharge extends \Framework\Database\Model
{
    protected $table = 'oil_card_main_recharge';

    protected $guarded = ["id"];

    protected $fillable = [
        'api_id', 'channel', 'card_main_id', 'recharge_time', 'trade_place', 'remit_type', 'recharge_total',
        'recharge_status', 'recharge_vice_no', 'createtime', 'updatetime', 'fetch_time', 'account_type'
    ];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     *
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By api_id
        if (isset($params['api_id']) && $params['api_id'] != '') {
            $query->where('api_id', '=', $params['api_id']);
        }

        //Search By channel
        if (isset($params['channel']) && $params['channel'] != '') {
            $query->where('channel', '=', $params['channel']);
        }

        if (isset($params['account_type']) && $params['account_type'] != '') {
            $query->where('account_type', '=', $params['account_type']);
        }

        //Search By card_main_id
        if (isset($params['card_main_id']) && $params['card_main_id'] != '') {
            $query->where('card_main_id', '=', $params['card_main_id']);
        }

        //Search By recharge_time
        if (isset($params['recharge_time']) && $params['recharge_time'] != '') {
            $query->where('recharge_time', '=', $params['recharge_time']);
        }

        //Search By recharge_time
        if (isset($params['recharge_timeGe']) && $params['recharge_timeGe'] != '') {
            $query->where('oil_card_main_recharge.recharge_time', '>=', $params['recharge_timeGe']);
        }

        //Search By recharge_timeLe
        if (isset($params['recharge_timeLe']) && $params['recharge_timeLe'] != '') {
            $recharge_timeLe = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $params['recharge_timeLe'], $matches) ? $params['recharge_timeLe'] . ' 23:59:59' : $params['recharge_timeLe'];
            $query->where('oil_card_main_recharge.recharge_time', '<=', $recharge_timeLe);
        }

        //Search By recharge_timeGt
        if (isset($params['recharge_timeGt']) && $params['recharge_timeGt'] != '') {
            $query->where('oil_card_main_recharge.recharge_time', '>', $params['recharge_timeGt']);
        }

        //Search By recharge_timeLe
        if (isset($params['recharge_timeLt']) && $params['recharge_timeLt'] != '') {
            $query->where('oil_card_main_recharge.recharge_time', '<', $params['recharge_timeLt']);
        }

        //Search By trade_place
        if (isset($params['trade_place']) && $params['trade_place'] != '') {
            $query->where('trade_place', '=', $params['trade_place']);
        }

        //Search By remit_type
        if (isset($params['remit_type']) && $params['remit_type'] != '') {
            $query->where('remit_type', '=', $params['remit_type']);
        }

        //Search By recharge_total
        if (isset($params['recharge_total']) && $params['recharge_total'] != '') {
            $query->where('recharge_total', '=', $params['recharge_total']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $createtimeLe = preg_match('/^\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}$/', $params['createtimeLe'], $matches) ? $params['createtimeLe'] . ' 23:59:59' : $params['createtimeLe'];
            $query->where('oil_card_main_recharge.createtime', '<=', $createtimeLe);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_card_main_recharge.createtime', '>=', $params['createtimeGe']);
        }

        if (isset($params['createtimeLt']) && $params['createtimeLt'] != '') {
            $query->where('oil_card_main_recharge.createtime', '<', $params['createtimeLt']);
        }

        if (isset($params['createtimeGt']) && $params['createtimeGt'] != '') {
            $query->where('oil_card_main_recharge.createtime', '>', $params['createtimeGt']);
        }

        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $viceNoArr = explode('|', $params['vice_no']);
            if (count($viceNoArr) == 1) {
                $query->where('oil_card_main_recharge.recharge_vice_no', '=', $params['vice_no']);
            } else {
                $query->whereIn('oil_card_main_recharge.recharge_vice_no', $viceNoArr);
            }
        }

        //Search By oil_comIn
        if (isset($params['oil_comIn']) && $params['oil_comIn'] != '') {
            $oilComIn = explode(",", $params['oil_comIn']);
            $query->whereIn('oil_card_main.oil_com', $oilComIn);
        }

        //Search By account_nameIn
        if (isset($params['account_nameIn']) && $params['account_nameIn'] != '') {
            $query->whereIn('oil_card_main.account_name', $params['account_nameIn']);
        }

        return $query;
    }

    /**
     * 油品主卡充值记录表 列表查询
     *
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data            = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page']  = isset($params['page']) ? $params['page'] : 1;
        //设置默认值
        if( !isset($params['channel']) || empty($params['channel']) ){
            $params['channel'] = 10;
        }
        if (API_ENV == 'dev') {
            $connection = "";
        } else {
            $connection = "online_only_read";
        }
        $accountList = [];
        if (isset($params['orgcodeLk']) && $params['orgcodeLk'] != '') {
            $accountList = OilCustomerCard::getAccountList(['is_parent' => 1, "orgcode" => $params['orgcodeLk']]);
        }

        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $accountList = OilCustomerCard::getAccountList(["orgcode" => $params['orgcode']]);
        }

        if (count($accountList) > 0) {
            $params['account_nameIn'] = $accountList->toArray();
        } else {
            $emptyData['total']         = 0;
            $emptyData['per_page']      = 50;
            $emptyData['current_page']  = 1;
            $emptyData['last_page']     = 0;
            $emptyData['next_page_url'] = NULL;
            $emptyData['prev_page_url'] = NULL;
            $emptyData['from']          = NULL;
            $emptyData['to']            = NULL;
            $emptyData['data']          = [];
            return $emptyData;
        }

        //Capsule::connection($connection)->enableQueryLog();
        $sqlObj = Capsule::connection($connection)->table("oil_card_main_recharge")
                         ->leftJoin('oil_card_main', 'oil_card_main_recharge.recharge_vice_no', '=', 'oil_card_main.main_no')
                         ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_card_main.org_id')
                         ->selectRaw('oil_card_main.oil_com,oil_card_main_recharge.id,oil_card_main_recharge.recharge_time,
            oil_card_main_recharge.trade_place,oil_card_main_recharge.remit_type,oil_card_main_recharge.recharge_total,
            oil_card_main_recharge.recharge_status,oil_card_main_recharge.recharge_vice_no,oil_card_main_recharge.createtime,
            oil_card_main_recharge.updatetime,oil_org.orgcode,oil_org.org_name,oil_card_main_recharge.channel,oil_card_main_recharge.account_type')
                         ->orderBy('oil_card_main_recharge.createtime', 'desc');
        $obj    = new OilCardMainRecharge();
        $sqlObj = $obj->scopeFilter($sqlObj, $params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        //$sql = Capsule::connection($connection)->getQueryLog();
        //print_r($sql);exit;
        $data = self::formatData($data, $params);
        return $data;
    }

    static public function formatData($data, $params)
    {
        foreach ($data as &$_item) {
            if (isset($params['_export']) && $params['_export'] == 1) {
                $_item->recharge_vice_no = $_item->recharge_vice_no . "\t";
            }
            $_item->card_type_txt = $_item->isMain = "";
            if (in_array($_item->oil_com, [OilCom::ZSH, OilCom::ZCW_ZSH_CYZYK])) {
                $_item->card_type_txt = "中石化";
            }
            if ($_item->oil_com == OilCom::ZSY) {
                $_item->card_type_txt = "中石油";
            }
            $_item->isMain = "是";

            //机构名称显示机构层级关系处理（临时取消）
            /*if(isset($params['curent_orgcode']) && $params['curent_orgcode']){
                $_item->org_name = \Models\OilOrg::getOrgTreePart($params['curent_orgcode'],$_item->orgcode);
            }*/
        }
        return $data;
    }

    /**
     * 油品主卡充值记录表 详情查询
     *
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {

        \helper::argumentCheck(['id'], $params);

        return OilCardMainRecharge::find($params['id']);
    }

    static public function getByApiId(array $params)
    {
        return OilCardMainRecharge::Filter($params)->lockForUpdate()->first();
    }

    /**
     * 油品主卡充值记录表 新增
     *
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilCardMainRecharge::create($params);
    }

    /**
     * 油品主卡充值记录表 编辑
     *
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardMainRecharge::find($params['id'])->update($params);
    }

    /**
     * 油品主卡充值记录表 根据ids删除或批量删除
     *
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardMainRecharge::destroy($params['ids']);
    }

    /**
     * 批量插入
     *
     * @param array $params
     * @return int
     */
    static public function batchAdd(array $params)
    {
        return OilCardMainRecharge::insert($params);
    }

    /**
     * search data from oilAgent
     *
     * @return mixed
     */
    static public function syncChargeData(array $params)
    {
        try {
            $_beginTime = $params['start_time'];
            $_endTime   = $params['end_time'];
            //$condition['card_fromIn'] = [CardFrom::CUSTOMER_CARD, CardFrom::CUSTOMER_CARD_SIMPLE];
            $condition['oil_comIn']      = [OilCom::ZSH, OilCom::ZCW_ZSH_CYZYK, OilCom::ZSY];
            $condition['account_status'] = 10;
            $mainList                    = OilCardMain::getMainData($condition);
            if (count($mainList) > 0) {
                $clientEnv = OilConfigure::getBySysKey('client_env');
                foreach ($mainList as $_item) {
                    if (empty($_item->account_password)) {
                        Log::error('主卡号-' . $_item->main_no . "密码为空", [], 'syncChargeData');
                        continue;
                    }
                    $checkArr['cardList']   = [];
                    $checkArr['parentcard'] = $_item->main_no;
                    $checkArr['account']    = ($clientEnv && $_item->oil_com == OilCom::ZSY) ? $_item->main_no : $_item->account_name;
                    $checkArr['password']   = $_item->account_password;
                    $checkArr['level']      = 1; //任务级别(0:定时任务|1:实时任务)
                    //判断任务类型
                    if (in_array($_item->oil_com, [OilCom::ZSH, OilCom::ZCW_ZSH_CYZYK])) {
                        $checkArr['taskType']  = 'zshChargeDetailCrawler';
                        $checkArr['cardType']  = 'zsh';
                        $checkArr['beginDate'] = $_beginTime;
                        $checkArr['endDate']   = $_endTime;
                    }
                    if (in_array($_item->oil_com, [OilCom::ZSY])) {
                        $checkArr['cardList'][] = $_item->main_no;
                        $checkArr['cardType']   = 'zsy';
                        $checkArr['taskType']   = 'zsyChargeDetailCrawler';
                        $checkArr['beginDate']  = $_beginTime;
                        $checkArr['endDate']    = $_endTime;
                    }
                    $apiParams = [
                        'method' => 'crawl-provider.fuelCardService.query',
                        'data'   => [
                            'cardtype' => $checkArr['cardType'],
                            'account'  => $checkArr['account'],
                            'level'    => 0,
                            'callback' => '',
                            'params'   => json_encode($checkArr),
                        ],
                    ];

                    Log::error('发送参数:$apiParams-' . var_export($apiParams, TRUE), [], 'syncChargeData');
                    $taskId = OilAgentClient::post($apiParams);
                    Log::error('请求代理结果:$taskId-' . $taskId, [], 'syncChargeData');
                }
            } else {
                Log::error('主卡信息为空-' . var_export($condition, true), [], 'syncChargeData');
            }
        } catch (\Exception $e) {
            Log::error('checkAssignError:' . strval($e), [$params], 'syncChargeData');
        }
    }

}