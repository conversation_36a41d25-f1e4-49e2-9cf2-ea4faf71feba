<?php
/**
 * oil_trades
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/08/28
 * Time: 11:57:11
 */
namespace Models;
use Fuel\Defines\ReceiptApplyDefine;
use Fuel\Defines\TradesType;
use Illuminate\Database\Capsule\Manager as Capsule;
use Jobs\ExportReportDataJob;
use Kafka\Log;

class OilTrades extends \Framework\Database\Model
{
    protected $table = 'oil_trades';

    protected $guarded = ["id"];
    protected $fillable = ['trades_id','org_id','orgcode','supplier_id','area_id','station_code','station_name','pcode',
        'vice_no','trade_price','trade_money','trade_num','mac_price','mac_amount','use_fanli_money','xpcode_pay_money',
        'real_oil_num','province_code','province_name',
        'city_code','city_name','oil_name','trade_type','consume_type','oil_com','top_org_id','top_orgcode',
        'trade_createtime','createtime','updatetime','up_operator_id','down_operator_id','is_lock_operator','invoice_identy'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By trades_id
        if (isset($params['trades_id']) && $params['trades_id'] != '') {
            $query->where('trades_id', '=', $params['trades_id']);
        }

        if (isset($params['trades_idIn']) && $params['trades_idIn'] != '') {
            $query->whereIn('trades_id', $params['trades_idIn']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By top_org_id
        if (isset($params['top_org_id']) && $params['top_org_id'] != '') {
            $query->where('top_org_id', '=', $params['top_org_id']);
        }

        //Search By top_orgcode
        if (isset($params['top_orgcode']) && $params['top_orgcode'] != '') {
            $query->where('top_orgcode', '=', $params['top_orgcode']);
        }

        //Search By supplier_id
        if (isset($params['supplier_id']) && $params['supplier_id'] != '') {
            $query->where('supplier_id', '=', $params['supplier_id']);
        }

        //Search By area_id
        if (isset($params['area_id']) && $params['area_id'] != '') {
            $query->where('area_id', '=', $params['area_id']);
        }

        //Search By station_code
        if (isset($params['station_code']) && $params['station_code'] != '') {
            $query->where('station_code', '=', $params['station_code']);
        }

        //Search By station_name
        if (isset($params['station_name']) && $params['station_name'] != '') {
            $query->where('station_name', '=', $params['station_name']);
        }

        //Search By pcode
        if (isset($params['pcode']) && $params['pcode'] != '') {
            $query->where('pcode', '=', $params['pcode']);
        }

        //Search By oil_com
        if (isset($params['oil_com']) && $params['oil_com'] != '') {
            $query->where('oil_com', '=', $params['oil_com']);
        }

        //Search By vice_no
        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->where('vice_no', '=', $params['vice_no']);
        }

        //Search By trade_price
        if (isset($params['trade_price']) && $params['trade_price'] != '') {
            $query->where('trade_price', '=', $params['trade_price']);
        }

        //Search By trade_money
        if (isset($params['trade_money']) && $params['trade_money'] != '') {
            $query->where('trade_money', '=', $params['trade_money']);
        }

        //Search By trade_num
        if (isset($params['trade_num']) && $params['trade_num'] != '') {
            $query->where('trade_num', '=', $params['trade_num']);
        }

        //Search By mac_price
        if (isset($params['mac_price']) && $params['mac_price'] != '') {
            $query->where('mac_price', '=', $params['mac_price']);
        }

        //Search By mac_amount
        if (isset($params['mac_amount']) && $params['mac_amount'] != '') {
            $query->where('mac_amount', '=', $params['mac_amount']);
        }

        //Search By use_fanli_money
        if (isset($params['use_fanli_money']) && $params['use_fanli_money'] != '') {
            $query->where('use_fanli_money', '=', $params['use_fanli_money']);
        }

        //Search By xpcode_pay_money
        if (isset($params['xpcode_pay_money']) && $params['xpcode_pay_money'] != '') {
            $query->where('xpcode_pay_money', '=', $params['xpcode_pay_money']);
        }

        //Search By real_oil_num
        if (isset($params['real_oil_num']) && $params['real_oil_num'] != '') {
            $query->where('real_oil_num', '=', $params['real_oil_num']);
        }

        //Search By province_code
        if (isset($params['province_code']) && $params['province_code'] != '') {
            $query->where('province_code', '=', $params['province_code']);
        }

        //Search By city_code
        if (isset($params['city_code']) && $params['city_code'] != '') {
            $query->where('city_code', '=', $params['city_code']);
        }

        //Search By oil_name
        if (isset($params['oil_name']) && $params['oil_name'] != '') {
            $query->where('oil_name', '=', $params['oil_name']);
        }

        //Search By trade_type
        if (isset($params['trade_type']) && $params['trade_type'] != '') {
            $query->where('trade_type', '=', $params['trade_type']);
        }

        //Search By consume_type
        if (isset($params['consume_type']) && $params['consume_type'] != '') {
            $query->where('consume_type', '=', $params['consume_type']);
        }

        //Search By trade_createtime
        if (isset($params['trade_createtime']) && $params['trade_createtime'] != '') {
            $query->where('trade_createtime', '=', $params['trade_createtime']);
        }

        if (isset($params['trade_createtime_ge']) && $params['trade_createtime_ge'] != '') {
            $query->where('trade_createtime', '>=', $params['trade_createtime_ge']);
        }

        if (isset($params['trade_createtime_lt']) && $params['trade_createtime_lt'] != '') {
            $query->where('trade_createtime', '<', $params['trade_createtime_lt']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        if (isset($params['up_operator_id']) && $params['up_operator_id'] != '') {
            $query->where('up_operator_id', '=', $params['up_operator_id']);
        }

        if (isset($params['down_operator_id']) && $params['down_operator_id'] != '') {
            $query->where('down_operator_id', '=', $params['down_operator_id']);
        }

        return $query;
    }

    /**
     * oil_trades 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilTrades::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_trades 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTrades::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTrades::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_trades 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilTrades::create($params);
    }

    /**
     * oil_trades 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilTrades::find($params['id'])->update($params);
    }

    static public function editByFilter(array $params,$data = [])
    {
        return OilTrades::Filter($params)->update($data);
    }

    /**
     * oil_trades 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilTrades::destroy($params['ids']);
    }

    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return self::Filter($params)->count();
    }

    /**
     * 根据条件取信息
     * @param array $params
     * @param mixed $field
     * @return OilSupplierAccount
     */
    static public function getInfoByFilter(array $params,$field = "*")
    {
        return self::Filter($params)->select($field)->first();
    }

    static public function getSumMoney($params = [])
    {
        //Capsule::connection()->enableQueryLog();
        $trades = new OilTrades();
        $sqlObj = Capsule::connection("")
            ->table('oil_trades as oil_trades')
            ->from(Capsule::raw('`oil_trades` FORCE INDEX (`idx_createtime_operator_id`)'));
        $data = $trades->scopeFilter($sqlObj,$params)->sum('trade_money');
        //\Framework\Log::error('getSumMoney -=- SQL:'.\var_export(Capsule::connection()->getQueryLog(), TRUE), [$params], 'Inner_receipt_');
        return $data;
    }

    /**
     * Desc: 获取trade_list
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 5/12/22 上午11:39
     * @param array $params
     * @return mixed
     */
    static public function getReceiptInternal($params = [])
    {
        $profit_val = isset($params['profit_val']) ? $params['profit_val'] : 0.2;
        //\Framework\Log::error('$params=========' . var_export(['sql'=>$params], TRUE), [], 'getReceiptInternal_');
        Capsule::connection()->enableQueryLog();
        $fields = 'oil_trades.*,
                rebate.mark_rebate, 
                rebate.is_share, 
                rebate.straight_down_rebate,
                rebate.final_straight_down_rebate,
                rebate.after_rebate,
                rebate.final_after_rebate,
                rebate.is_final,
                rebate.down_cal_rebate,
                rebate.down_fanli_way,
                rebate.down_is_final,
                ( rebate.final_straight_down_rebate + rebate.final_after_rebate + rebate.mark_rebate ) AS up_fanli,
                round ((((oil_trades.trade_money - rebate.down_cal_rebate) - (oil_trades.mac_amount - (rebate.final_straight_down_rebate + rebate.final_after_rebate + rebate.mark_rebate))) * IF(
          oil_trades.up_operator_id = 19,
          IF( oil_trades.trade_createtime < \'2024-09-01 00:00:00\',0.8,0.95 ),
          IF(
            oil_trades.trade_createtime < \'2023-11-01 00:00:00\',
            0.6,
            If(
              oil_trades.trade_createtime >= \'2023-11-01 00:00:00\'
              and oil_trades.trade_createtime < \'2024-09-01 00:00:00\',
              0.2,
              0.6
            )
          )
        ) + rebate.down_cal_rebate), 2) AS discount';
        $sqlObj = OilTrades::select(Capsule::raw($fields));

        $sqlObj->leftJoin('oil_card_vice_trade_rebate as rebate', 'rebate.trade_id', '=', 'oil_trades.trades_id')
            ->leftJoin('oil_type_no', 'oil_trades.oil_name', '=', 'oil_type_no.oil_no');
        // ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_trades.org_id');

        if (isset($params['trade_create_Ge']) && $params['trade_create_Ge']) {
            $sqlObj->where('oil_trades.trade_createtime', '>=', $params['trade_create_Ge']);
        }

        if (isset($params['trade_create_Lt']) && $params['trade_create_Lt']) {
            $sqlObj->where('oil_trades.trade_createtime', '<=', $params['trade_create_Lt']);
        }

        if (isset($params['trade_create_NLt']) && $params['trade_create_NLt']) {
            $sqlObj->where('oil_trades.trade_createtime', '<', $params['trade_create_NLt']);
        }

        if (isset($params['down_operator_id']) && $params['down_operator_id']) {
            $sqlObj->where('oil_trades.down_operator_id', $params['down_operator_id']);
        }

        if (isset($params['up_operator_id']) && $params['up_operator_id']) {
            $sqlObj->where('oil_trades.up_operator_id',$params['up_operator_id']);
        }

        if (isset($params['oil_base_id']) && $params['oil_base_id']) {
            $sqlObj->whereIn('oil_type_no.oil_base_id',$params['oil_base_id']);
        }

        if(isset($params['org_root_in']) && count($params['org_root_in']) > 0){
            $sqlObj->whereIn('oil_trades.top_orgcode', $params['org_root_in']);
        }

        if (isset($params['is_lock_operator']) && $params['is_lock_operator']) {
            $sqlObj->where('oil_trades.is_lock_operator',$params['is_lock_operator']);
        }

        if (isset($params['trades_id']) && $params['trades_id']) {
            $sqlObj->where('oil_trades.trades_id',$params['trades_id']);
        }

        //排除以下清单机构的消费
        $exceipt = (new ExportReportDataJob())->exceiptOrg;
        $sqlObj->whereNotIn('oil_trades.top_orgcode',$exceipt);
        //G7WALLET-5042
        $sqlObj->whereNotIn('oil_trades.supplier_id',ReceiptApplyDefine::getExceptSupplierId());

        $sqlObj->whereIn('oil_trades.trade_type',TradesType::getCashAndSelfOilTradeTypeArr(false,true));

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 1000;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $data = $sqlObj->orderBy('oil_trades.trade_createtime', 'asc')->paginate($params['limit'], ['*'], 'page', $params['page']);

        $sql = Capsule::connection()->getQueryLog();

        \Framework\Log::error('$sql======' . var_export(['sql'=>$sql], TRUE), [$params], 'addReceiptApplyINnnnnDetails');
        return $data;
    }


    /**
     * Desc: 获取trade_list
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 5/12/22 上午11:39
     * @param array $params
     * @return mixed
     */
    static public function getReceiptInternalTradeNum($params = [])
    {
        //\Framework\Log::error('$params=========' . var_export(['sql'=>$params], TRUE), [], 'getReceiptInternal_');
        //Capsule::connection()->enableQueryLog();
        $fields = 'sum(oil_trades.trade_num) as receipt_num';
        $sqlObj = OilTrades::select(Capsule::raw($fields));

        $sqlObj->leftJoin('oil_card_vice_trade_rebate as rebate', 'rebate.trade_id', '=', 'oil_trades.trades_id')
            ->leftJoin('oil_type_no', 'oil_trades.oil_name', '=', 'oil_type_no.oil_no');
        // ->leftJoin('oil_org', 'oil_org.id', '=', 'oil_trades.org_id');

        if (isset($params['trade_create_Ge']) && $params['trade_create_Ge']) {
            $sqlObj->where('oil_trades.trade_createtime', '>=', $params['trade_create_Ge']);
        }

        if (isset($params['trade_create_Lt']) && $params['trade_create_Lt']) {
            $sqlObj->where('oil_trades.trade_createtime', '<=', $params['trade_create_Lt']);
        }

        if (isset($params['down_operator_id']) && $params['down_operator_id']) {
            $sqlObj->where('oil_trades.down_operator_id', $params['down_operator_id']);
        }

        if (isset($params['up_operator_id']) && $params['up_operator_id']) {
            $sqlObj->where('oil_trades.up_operator_id',$params['up_operator_id']);
        }

        if (isset($params['oil_base_id']) && $params['oil_base_id']) {
            //\Framework\Log::error('$params======oil_base_id' . var_export(['sql'=>'oil_base_id'], TRUE), [], 'getReceiptInternal_');
            $sqlObj->whereIn('oil_type_no.oil_base_id',$params['oil_base_id']);
        }

        //排除以下清单机构的消费
        $exceipt = (new ExportReportDataJob())->exceiptOrg;
        $sqlObj->whereNotIn('oil_trades.top_orgcode',$exceipt);

        $sqlObj->whereIn('oil_trades.trade_type',TradesType::getCashAndSelfOilTradeTypeArr(false,true));

        $data = $sqlObj->get();

        //$sql = Capsule::connection()->getQueryLog();

        //\Framework\Log::error('SumTrade_num$sql=========' . var_export(['sql'=>$sql], TRUE), [], 'addReceiptApplyINnnnnDetails');
        return $data;
    }

    static public function countUnSetUpOperatorIdAndSupplierId($params, $countTotal = true, $limitSize = 100, $limitPage = 1)
    {
        Capsule::connection()->enableQueryLog();
        $sqlObj = OilTrades::select('oil_trades.trades_id');
        $sqlObj = $sqlObj->where(function ($query) {
            $query->where('oil_trades.up_operator_id', 0)
                ->orWhereNull('oil_trades.up_operator_id')
                ->orWhereNull('oil_trades.supplier_id')
                ->orWhere('oil_trades.supplier_id', 0);
        });

        $sqlObj = $sqlObj->where('oil_trades.is_lock_operator', 2);
        //排除以下清单机构的消费
        $exceipt = (new ExportReportDataJob())->exceiptOrg;
        $sqlObj->whereNotIn('oil_trades.top_orgcode', $exceipt);

        $sqlObj->leftJoin('oil_card_vice_trade_rebate as rebate', 'rebate.trade_id', '=', 'oil_trades.trades_id')
            ->leftJoin('oil_type_no', 'oil_trades.oil_name', '=', 'oil_type_no.oil_no');

        if (isset($params['trade_create_Ge']) && $params['trade_create_Ge']) {
            $sqlObj->where('oil_trades.trade_createtime', '>=', $params['trade_create_Ge']);
        }

        if (isset($params['trade_create_Lt']) && $params['trade_create_Lt']) {
            $sqlObj->where('oil_trades.trade_createtime', '<=', $params['trade_create_Lt']);
        }

        if (isset($params['oil_base_id']) && $params['oil_base_id']) {
            $sqlObj->whereIn('oil_type_no.oil_base_id',$params['oil_base_id']);
        }
        if ($countTotal) {
            $countResult = $sqlObj->count();
            $sql = Capsule::connection()->getQueryLog();
            \Framework\Log::error('$countN=========' . var_export(['sql'=>$sql], TRUE), [], 'countUnSetUpOperatorIdAndSupplierId');
            return $countResult;
        }

        $result = $sqlObj->paginate($limitSize, ['*'], 'page', $limitPage)->toArray();
        return $result['data'];
    }

    //校验单笔交易10万
    static public function checkMaxAmountTrade($params,$isMonth = 2)
    {
        \Framework\Log::error('checkMaxAmountTrade-params',$params,'checkMaxAmountTrade');
        \Framework\Log::error('checkMaxAmountTrade-isMonth',[$isMonth],'checkMaxAmountTrade');
        $roots = [];
        $m_end_time = "";
        $m_start_time = \helper::nowTime();
        if($isMonth == 1) {
            $monthData = OilOrgOperatorDayTrades::getList([
                'up_operator_id' => $params['up_operator_id'],
                'down_operator_id' => $params['down_operator_id'],
                'monthIn' => $params['month_time'],
                'oil_base_id_in' => $params['oil_base_idIn'],
                '_export' => 1,
            ]);

            if (count($monthData) > 0) {
                foreach ($monthData as $_one){
                    $roots[$_one->org_root] = $_one->org_root;
                    $m_end_time = $_one->end_time;
                    if(strtotime($m_start_time) > strtotime($_one->start_time)){
                        $m_start_time = $_one->start_time;
                    }
                }
            }else{
                throw new \RuntimeException("没有找到锁定机构", 40019);
            }
        }
        $condition = [
            "up_operator_id" => $params['up_operator_id'],//供给公司
            'down_operator_id' => $params['down_operator_id'], //销售公司
            'trade_create_Ge' => $params['trade_start_time'],//开始时间
            'trade_create_Lt' => $params['trade_end_time'], //结束时间,
            'oil_base_id' => $params['oil_base_idIn'],
            'is_lock_operator' => 2,
        ];

        if($isMonth == 1){
            unset($condition['trade_create_Lt']);
            $condition['org_root_in'] = array_values($roots);
            $condition['trade_create_NLt'] = !empty($m_end_time) ? $m_end_time : date("Y-m-d",time())." 00:00:00";
            $condition['trade_create_Ge'] = $m_start_time;
            $condition['is_lock_operator'] = 1;
        }
        \Framework\Log::error('checkMaxAmountTrade-condition',$condition,'checkMaxAmountTrade');
        Capsule::connection()->enableQueryLog();
        $sqlObj = OilTrades::select('oil_trades.trades_id')
            ->where('oil_trades.is_lock_operator', $isMonth);

        //排除以下清单机构的消费
        $exceipt = (new ExportReportDataJob())->exceiptOrg;
        $sqlObj->whereNotIn('oil_trades.top_orgcode', $exceipt);

        $sqlObj->leftJoin('oil_card_vice_trade_rebate as rebate', 'rebate.trade_id', '=', 'oil_trades.trades_id')
            ->leftJoin('oil_type_no', 'oil_trades.oil_name', '=', 'oil_type_no.oil_no');

        if (isset($condition['trade_create_Ge']) && $condition['trade_create_Ge']) {
            $sqlObj->where('oil_trades.trade_createtime', '>=', $condition['trade_create_Ge']);
        }

        if (isset($condition['trade_create_Lt']) && $condition['trade_create_Lt']) {
            $sqlObj->where('oil_trades.trade_createtime', '<=', $condition['trade_create_Lt']);
        }

        if (isset($condition['trade_create_NLt']) && $condition['trade_create_NLt']) {
            $sqlObj->where('oil_trades.trade_createtime', '<', $condition['trade_create_NLt']);
        }

        if (isset($condition['down_operator_id']) && $condition['down_operator_id']) {
            $sqlObj->where('oil_trades.down_operator_id', $condition['down_operator_id']);
        }

        if (isset($condition['up_operator_id']) && $condition['up_operator_id']) {
            $sqlObj->where('oil_trades.up_operator_id',$condition['up_operator_id']);
        }

        if (isset($condition['oil_base_id']) && $condition['oil_base_id']) {
            $sqlObj->whereIn('oil_type_no.oil_base_id',$condition['oil_base_id']);
        }

        if(isset($condition['org_root_in']) && count($condition['org_root_in']) > 0){
            $sqlObj->whereIn('oil_trades.top_orgcode', $condition['org_root_in']);
        }

        $sqlObj->where('oil_trades.trade_money', '>=', 100000);
        //$sqlObj->whereRaw(round ((((oil_trades.trade_money - rebate.down_cal_rebate) - (oil_trades.mac_amount - (rebate.final_straight_down_rebate + rebate.final_after_rebate + rebate.mark_rebate))) * '.$profit_val.' + rebate.down_cal_rebate), 2) > 100000);

        $data = $sqlObj->orderBy('oil_trades.trade_money','desc')->limit(1)->first();
        $sql = Capsule::connection()->getQueryLog();
        \Framework\Log::error('checkMaxAmountTrade-sql',$sql,'checkMaxAmountTrade');

        return $data;
    }


}