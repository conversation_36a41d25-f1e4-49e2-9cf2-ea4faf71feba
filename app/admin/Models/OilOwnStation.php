<?php
/**
 * G7能源自营站
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/06/16
 * Time: 18:05:18
 */

namespace Models;

use Fuel\Defines\CardViceConf;
use Fuel\Defines\StationArea;
use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class OilOwnStation
 * @package Models
 * @property $area_id
 * @property $supplier_id
 * @property $cooperation_type
 * @property $is_proxy
 * @property $area_code
 * @property $operator_id
 */
class OilOwnStation extends \Framework\Database\Model
{
    public $timestamps = false;
    const WAIT_AUDIT = 1;
    const AUDIT_PASS = 5;
    const AUDIT_REFUSE = 10;
    protected $table = 'oil_own_station';
    protected $guarded = ["id"];
    protected $fillable = ['station_code', 'station_name', 'pcode', 'pname', 'area_id', 'status', 'contact', 'contact_phone',
        'admin_remark', 'audit_time', 'auditor', 'creator', 'modifier', 'createtime', 'updatetime','is_proxy','operator_id',
        'card_assign_method',];

    public function getFillAble()
    {
        return $this->fillable;
    }

    public static $STATUS_ENUM = [
        self::WAIT_AUDIT   => '待审核',
        self::AUDIT_PASS   => '已审核',
        self::AUDIT_REFUSE => '已驳回',
    ];

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            if (is_array($params['id'])) {

                $query->whereIn('oil_own_station.id', $params['id']);
            } else {

                $query->where('oil_own_station.id', '=', $params['id']);
            }
        }

        //Search By station_code
        if (isset($params['station_code']) && $params['station_code'] != '') {
            if (is_array($params['station_code'])) {

                $query->whereIn('oil_own_station.station_code', $params['station_code']);
            } else {

                $query->where('oil_own_station.station_code', '=', $params['station_code']);
            }
        }

        if (isset($params['codeLike']) && $params['codeLike'] != '') {
            $query->where('oil_own_station.station_code', 'like', '%'. $params['codeLike'] .'%');
        }

        //Search By station_name
        if (isset($params['station_name']) && $params['station_name'] != '') {
            $query->where('oil_own_station.station_name', '=', $params['station_name']);
        }

        //Search By seller_nameLike
        if (isset($params['station_nameLike']) && $params['station_nameLike'] != '') {
            $query->where('station_name', 'like', '%' . $params['station_nameLike'] . '%');
        }

        if (isset($params['stationCodeNotIn']) && count($params['stationCodeNotIn']) > 0 ) {
            $query->whereNotIn('oil_own_station.station_code', $params['stationCodeNotIn']);
        }

        //Search By pcode
        if (isset($params['pcode']) && $params['pcode'] != '') {
            $query->where('oil_own_station.pcode', '=', $params['pcode']);
        }

        //Search By pname
        if (isset($params['pname']) && $params['pname'] != '') {
            $query->where('oil_own_station.pname', '=', $params['pname']);
        }

        //Search By area_id
        if (isset($params['area_id']) && $params['area_id'] != '') {
            if (is_array($params['area_id'])) {

                $query->whereIn('oil_own_station.area_id', $params['area_id']);
            } else {

                $query->where('oil_own_station.area_id', '=', $params['area_id']);
            }
        }

        if (isset($params['area_idIn']) && $params['area_idIn'] != '') {
            $query->whereIn('oil_own_station.area_id', $params['area_idIn']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            if (is_array($params['status'])) {

                $query->whereIn('oil_own_station.status', $params['status']);
            } else {

                $query->where('oil_own_station.status', '=', $params['status']);
            }
        }
        if (isset($params['oil_own_station_status']) && $params['oil_own_station_status'] != '') {
            if (is_array($params['oil_own_station_status'])) {

                $query->whereIn('oil_own_station.status', $params['oil_own_station_status']);
            } else {

                $query->where('oil_own_station.status', '=', $params['oil_own_station_status']);
            }
        }

        //Search By admin_remark
        if (isset($params['admin_remark']) && $params['admin_remark'] != '') {
            $query->where('oil_own_station.admin_remark', '=', $params['admin_remark']);
        }

        //Search By audit_time
        if (isset($params['audit_time']) && $params['audit_time'] != '') {
            $query->where('oil_own_station.audit_time', '=', $params['audit_time']);
        }

        //Search By auditor
        if (isset($params['auditor']) && $params['auditor'] != '') {
            $query->where('oil_own_station.auditor', '=', $params['auditor']);
        }

        //Search By modifier
        if (isset($params['modifier']) && $params['modifier'] != '') {
            $query->where('oil_own_station.modifier', '=', $params['modifier']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('oil_own_station.createtime', '=', $params['createtime']);
        }

        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('oil_own_station.createtime', '>=', $params['createtimeGe']);
        }
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('oil_own_station.createtime', '<=', $params['createtimeLe']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('oil_own_station.updatetime', '=', $params['updatetime']);
        }
        if (isset($params['updatetimeGe']) && $params['updatetimeGe'] != '') {
            $query->where('oil_own_station.updatetime', '>=', $params['updatetimeGe']);
        }
        if (isset($params['updatetimeLe']) && $params['updatetimeLe'] != '') {
            $query->where('oil_own_station.updatetime', '<=', $params['updatetimeLe']);
        }

        if (!empty($params['supplier_id'])) {
            $query->where("oil_station_supplier.id", '=', $params['supplier_id']);
        }

        if (isset($params['code_nameLike']) && $params['code_nameLike'] != '') {
            $like = '%' . $params['code_nameLike'] . '%';
            $query->where(function ($query)use($like){
                $query->where('station_code', 'like', $like)->orWhere('station_name', 'like', $like);
            });
        }

        if (isset($params['vice_no']) && $params['vice_no'] != '') {
            $query->whereExists(function ($query) use ($params) {
                $query->select('id')
                    ->from('oil_station_card')
                    ->where('vice_no', $params['vice_no'])
                    ->whereRaw('oil_station_card.code = oil_own_station.station_code');
            });
        }


        if (isset($params['is_proxy']) && $params['is_proxy'] != '') {
            $query->where('oil_own_station.is_proxy', '=', $params['is_proxy']);
        }

        if (isset($params['card_main_id']) && $params['card_main_id'] != '') {
            $query->where('oil_station_card.card_main_id', '=', $params['card_main_id']);
        }


        return $query;
    }

    /**
     * G7能源自营站 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        //Capsule::connection()->enableQueryLog();
        $sqlObj = OilOwnStation::Filter($params)->where("oil_supplier_relation.is_area", '=', 1)
            ->leftJoin('oil_station_area', 'oil_own_station.area_id', '=', 'oil_station_area.id')
            ->leftJoin('oil_supplier_relation', 'oil_supplier_relation.area_id', '=', 'oil_own_station.area_id')
            ->leftJoin('oil_station_supplier', 'oil_supplier_relation.supplier_id', '=', 'oil_station_supplier.id')
            ->leftJoin('oil_station_card', 'oil_own_station.station_code', '=', 'oil_station_card.code')
            ->selectRaw('oil_own_station.*,oil_station_area.name as area_name,oil_station_supplier.id as supplier_id,
                               oil_station_area.statistics_type,oil_station_supplier.supplier_name,count(oil_station_card.id) as card_count,
                               oil_station_area.code as area_code,GROUP_CONCAT(oil_station_card.property) as str_perty')
            ->groupBy("oil_own_station.id")
            ->dataRange();

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->orderBy('createtime', 'desc')->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        $dataArr = $data->toArray();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        if (isset($params['_export']) && $params['_export'] == 1) {
            $realData = &$dataArr;
        } else {
            $realData = &$dataArr['data'];
        }
        if (!empty($realData)) {
            foreach ($realData as &$v) {
                $v['status_name'] = self::$STATUS_ENUM[$v['status']];
                $v['service_area_statistics_type_name'] = StationArea::$STATISTICS_TYPE_DES_ENUM[$v['statistics_type']];
                $v['full_name'] = trim($v['station_code'])." ".$v['station_name'];
                $v['proxy_txt'] = $v['is_proxy'] == 1 ? '是' : '否';
                $v['is_edit'] = 2;
                //站点合作方式，油卡模式，存在副卡,且 站点下不能有：站点托管卡
                if( $v['statistics_type'] == StationArea::OIL_CARD &&
                    $v['card_count'] > 0 && stripos($v['str_perty'],strval(CardViceConf::CARD_PROPERTY_STATION_CUSTODY)) === false ) {
                    $v['is_edit'] = 1;
                }
            }
        }
        return $dataArr;
    }

    /*
     * 根据filter查列表
     */
    static public function getListByFilter($params)
    {
        return self::Filter($params)->get();
    }

    /**
     * G7能源自营站 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilOwnStation::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @param bool $only
     * @return object|null
     */
    static public function getByLock(array $params, $only = true)
    {
        if ($only) {

            return OilOwnStation::Filter($params)->lockForUpdate()->first();
        }
        return OilOwnStation::Filter($params)->lockForUpdate()->get();
    }

    /**
     * G7能源自营站 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOwnStation::create($params);
    }

    /**
     * G7能源自营站 编辑
     * @param array $where
     * @param array $data
     * @return mixed
     */
    static public function edit(array $where, array $data)
    {
        return OilOwnStation::Filter($where)->update($data);
    }

    /**
     * G7能源自营站 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilOwnStation::destroy($params['ids']);
    }

    /**
     * @param array $params
     * @return Collection|null
     */
    static public function getByStationCodes(array $params)
    {
        \helper::argumentCheck(['station_code'], $params);
        $data = OilOwnStation::Filter($params)
            ->where("oil_supplier_relation.is_area", '=', 1)
            ->leftJoin('oil_station_area', 'oil_own_station.area_id', '=', 'oil_station_area.id')
            ->leftJoin('oil_supplier_relation', 'oil_supplier_relation.area_id', '=', 'oil_own_station.area_id')
            ->leftJoin('oil_station_supplier', 'oil_supplier_relation.supplier_id', '=', 'oil_station_supplier.id')
            ->selectRaw('oil_own_station.*,oil_station_area.name as area_name,oil_station_area.statistics_type,oil_station_supplier.id as supplier_id,oil_station_supplier.supplier_name')
            ->get()
            ->toArray();
        if (!empty($data)) {
            foreach ($data as &$v) {
                $v['status_name'] = self::$STATUS_ENUM[$v['status']];
            }
        }
        return $data;
    }

    static public function getNum(array $params)
    {
        return OilOwnStation::Filter($params)->count();
    }

    /**
     * 根据条件取信息
     * @param array $params
     * @param mixed $field
     * @return OilOwnStation
     */
    static public function getInfoByFilter(array $params,$field = "*")
    {
        return self::Filter($params)->select($field)->first();
    }

    /**
     * 取字段值
     * @param array $params
     * @param $pluckField
     * @return array
     */
    static public function getPluckFields(array $params, $pluckField)
    {
        $res =  self::Filter($params)->pluck($pluckField);
        return !$res ? [] : $res->toArray();
    }

    /**
     * 获取绑定服务区
     * @param $params
     * @return mixed
     */
    public static function getStationBindArea($params)
    {
        return self::Filter($params)
            ->selectRaw('oil_own_station.*,oil_station_area.name as area_name,oil_station_area.code as area_code,oil_station_area.status as area_status,oil_station_area.statistics_type,
                   oil_supplier_relation.supplier_id,oil_station_supplier.supplier_name,oil_station_supplier.cooperation_type
            ')
            ->leftjoin("oil_station_area", 'oil_station_area.id', '=', 'oil_own_station.area_id')
            ->leftjoin("oil_supplier_relation", 'oil_supplier_relation.area_id', '=', 'oil_own_station.area_id')
            ->leftjoin("oil_station_supplier", 'oil_station_supplier.id', '=', 'oil_supplier_relation.supplier_id')
            ->where("oil_station_area.status", '<>', 30)
            ->where("oil_supplier_relation.is_area",1)
            ->where("oil_station_supplier.cooperation_type",20)
            ->first();
    }

    /**
     * 列表头部搜索
     * @param array $params
     * @return mixed
     */
    static public function getTotalList(array $params)
    {
        $data=OilOwnStation::Filter($params)->dataRange()->selectRaw('id,station_code,station_name')->get();
        foreach ($data as &$_item){
            $_item->full_name = $_item->station_code." ".$_item->station_name;
        }
        return $data;
    }
}
