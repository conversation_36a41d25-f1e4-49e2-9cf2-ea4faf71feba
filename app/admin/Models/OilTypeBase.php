<?php
/**
 * 油品种类
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/10/24
 * Time: 12:37:25
 */

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class OilTypeBase extends \Framework\Database\Model
{
    protected $table = 'oil_type_base';

    protected $guarded = ["id"];
    protected $fillable = ['name', 'unit', 'tax', 'tax_no', 'tag', 'mode', 'status', 'creator', 'last_operator', 'createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['idIn']) && $params['idIn'] != '') {
            $query->whereIn('id', $params['idIn']);
        }

        //Search By name
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('name', '=', $params['name']);
        }

        //Search By name
        if (isset($params['nameLk']) && $params['nameLk'] != '') {
            $query->where('name', 'like', "%" . $params['nameLk'] . "%");
        }

        //Search By unit
        if (isset($params['unit']) && $params['unit'] != '') {
            $query->where('unit', '=', $params['unit']);
        }

        //Search By tax
        if (isset($params['tax']) && $params['tax'] != '') {
            $query->where('tax', '=', $params['tax']);
        }

        //Search By tax_no
        if (isset($params['tax_no']) && $params['tax_no'] != '') {
            $query->where('tax_no', '=', $params['tax_no']);
        }

        //Search By tag
        if (isset($params['tag']) && $params['tag'] != '') {
            $query->where('tag', '=', $params['tag']);
        }

        //Search By mode
        if (isset($params['mode']) && $params['mode'] != '') {
            $query->where('mode', '=', $params['mode']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 油品种类 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilTypeBase::Filter($params);
        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        } else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        return $data;
    }

    /**
     * 油品种类 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilTypeBase::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilTypeBase::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 油品种类 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilTypeBase::create($params);
    }

    /**
     * 油品种类 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilTypeBase::find($params['id'])->update($params);
    }

    /**
     * 油品种类 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilTypeBase::destroy($params['ids']);
    }

    /**
     * 油品id  name map
     * @return array
     */
    static public function getMap()
    {
        $data = [];
        $result = OilTypeBase::all();
        foreach ($result as $item) {
            $data[$item['id']] = $item['name'];
        }
        return $data;
    }

    /**
     * 油品单位关系配置调用
     * @param array $params
     * @return mixed
     */
    static public function pluckByFilter(array $params)
    {
        return OilTypeBase::Filter($params)->pluck("id");
    }


}