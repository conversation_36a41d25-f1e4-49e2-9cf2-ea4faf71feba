<?php
/**
 * oil_supplier_fanli_account_statement
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2022/02/21
 * Time: 14:39:55
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilSupplierFanliAccountStatement extends \Framework\Database\Model
{
    protected $table = 'oil_supplier_fanli_account_statement';

    protected $guarded = ["id"];
    protected $fillable = ['account_id','res_type','res_id','oil_id','collect_company_id','business_no','amount','account_balance','summary','creator','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By account_id
        if (isset($params['account_id']) && $params['account_id'] != '') {
            $query->where('account_id', '=', $params['account_id']);
        }

        //Search By resource
        if (isset($params['res_type']) && $params['res_type'] != '') {
            $query->where('res_type', '=', $params['res_type']);
        }

        //Search By res_id
        if (isset($params['res_id']) && $params['res_id'] != '') {
            $query->where('res_id', '=', $params['res_id']);
        }

        //Search By business_no
        if (isset($params['business_no']) && $params['business_no'] != '') {
            $query->where('business_no', '=', $params['business_no']);
        }

        //Search By amount
        if (isset($params['amount']) && $params['amount'] != '') {
            $query->where('amount', '=', $params['amount']);
        }

        //Search By account_balance
        if (isset($params['account_balance']) && $params['account_balance'] != '') {
            $query->where('account_balance', '=', $params['account_balance']);
        }

        //Search By summary
        if (isset($params['summary']) && $params['summary'] != '') {
            $query->where('summary', '=', $params['summary']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * oil_supplier_fanli_account_statement 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilSupplierFanliAccountStatement::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * oil_supplier_fanli_account_statement 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierFanliAccountStatement::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierFanliAccountStatement::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_supplier_fanli_account_statement 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilSupplierFanliAccountStatement::create($params);
    }

    /**
     * oil_supplier_fanli_account_statement 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilSupplierFanliAccountStatement::find($params['id'])->update($params);
    }

    /**
     * oil_supplier_fanli_account_statement 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilSupplierFanliAccountStatement::destroy($params['ids']);
    }

    static public function getInfoByFilter(array $params){
        return OilSupplierFanliAccountStatement::where($params)->first();
    }

    /**
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return OilSupplierFanliAccountStatement::insert($params);
    }
}