<?php
/**
 * gas油站表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:19
 */

namespace Models;

use App\Models\MySQL\Station;
use Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;

class GosStation extends \Framework\Database\Model
{
    protected $connection = "gos_online";

    protected $table = 'gos_station';

	protected $guarded = ["id"];

	protected $fillable = [
        'id', 'third_id', 'sys_id', 'station_name', 'station_code', 'remark_name', 'station_type', 'is_highway', 'trade_type',
        'card_classify', 'activity', 'lng', 'lat', 'pcode_name', 'pcode', 'city_code', 'provice_code', 'city_name', 'provice_name',
        'address', 'navigation', 'contact', 'contact_phone', 'intro_img', 'detail_img', 'saled_num', 'share_tpl', 'star_rating',
        'score', 'creator', 'modifier', 'station_brand', 'station_brand_name', 'createtime', 'updatetime', 'created_at', 'updated_at', 'deleted_at',
        'rebate_grade', 'business_hours','station_oil_unit','operator_id'
	];

	public function getFillAble()
	{
		return $this->fillable;
	}

    public function getConnection()
    {
        return $this->connection;
    }

	/**
	 * 聚集查询
	 * @param $query
	 * @param $params
	 * @return $query
	 */
	public function scopeFilter($query, $params)
	{

        //Search By id
		if (isset($params['id']) && $params['id'] != '') {
			$query->where('id', '=', $params['id']);
		}

        //Search By third_id
        if (isset($params['third_id'])) {
            $thirdId = is_array($params['third_id']) ? array_unique($params['third_id']) : array_map('strval', explode(',', $params['third_id']));
            $query->whereIn('third_id', $thirdId);
        }

        if (isset($params['third_id_in']) && $params['third_id_in']) {
            $query->whereIn('third_id', $params['third_id_in']);
        }

        //Search By station_code
        if (isset($params['station_code']) && $params['station_code'] != '') {

            if (is_array($params['station_code'])) {

                $query->whereIn('station_code', $params['station_code']);
            } else {

                $query->where('station_code', '=', $params['station_code']);
            }
        }

        //Search By station_brand
        if (isset($params['station_brand']) && $params['station_brand'] != '') {

            if (is_array($params['station_brand'])) {

                $query->whereIn('station_brand', $params['station_brand']);
            } else {

                $query->where('station_brand', '=', $params['station_brand']);
            }
        }

        //Search By rebate_grade
        if (isset($params['rebate_grade']) && $params['rebate_grade'] != '') {

            if (is_array($params['rebate_grade'])) {

                $query->whereIn('rebate_grade', $params['rebate_grade']);
            } else {
                if (stripos($params['rebate_grade'], ",") !== false) {
                    $query->whereIn('rebate_grade', explode(',', $params['rebate_grade']));
                } else {
                    $query->where('rebate_grade', '=', $params['rebate_grade']);
                }
            }
        }

        //Search By pcode
        if (isset($params['pcode']) && $params['pcode'] != '') {
            if (is_array($params['pcode'])) {
                $query->whereIn('pcode', $params['pcode']);
            } else {
                $query->where('pcode', '=', $params['pcode']);
            }
        }

		return $query;
	}

	/**
	 * 油站表 详情查询
	 * @param array $params
	 * @return object
	 */
	static public function getById(array $params)
	{
		\helper::argumentCheck(['id'], $params);

		return self::find($params['id']);
	}

	/**
	 * 油站表 新增
	 * @param array $params
	 * @return mixed
	 */
	static public function add(array $params)
	{
		return self::create($params);
	}

	/**
	 * 油站表 编辑
	 * @param array $params
	 * @return mixed
	 */
	static public function edit(array $params)
	{
		\helper::argumentCheck(['id'], $params);

		return self::find($params['id'])->update($params);
	}

	/**
	 * 油站表 根据ids删除或批量删除
	 * @param array $params
	 * @return int
	 */
	static public function remove(array $params)
	{
		\helper::argumentCheck(['ids'], $params);

		return self::destroy($params['ids']);
	}

    public static function getOneField(array $params, $field = "remark_name", $group = "pcode")
    {
        $obj = Capsule::connection("gos_online")->table((new GosStation())->getTable());
        return (new GosStation())->scopeFilter($obj,$params)->groupby($group)->pluck($field);
    }

}