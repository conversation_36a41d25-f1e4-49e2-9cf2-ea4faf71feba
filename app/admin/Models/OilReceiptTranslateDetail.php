<?php
/**
 * oil_receipt_translate_detail
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2021/08/10
 * Time: 17:06:32
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;
use Fuel\Defines\ReceiptTranslateDetail;

/**
 * Class OilReceiptTranslateDetail
 * @package Models
 * @property $id
 * @property $receipt_return_id
 */
class OilReceiptTranslateDetail extends \Framework\Database\Model
{
    protected $table = 'oil_receipt_translate_detail';

    protected $guarded = ["id"];

    protected $fillable = ['receipt_return_detail_id','receipt_return_id','receipt_no','receipt_code','in_sku',
    'in_second_oil_id','select_status','in_num','in_unit','translate_status','is_return','in_status','in_mode',
    'return_reason','creator','last_operator','createtime','updatetime','progress',
        'stock_reason','transform','money','tax_money','money_tax','origin_name'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        if (isset($params['id']) && $params['id'] != '') {
            if(is_array($params['id'])){
                $query->whereIn('id',$params['id']);
            }else{
                $query->where('id', '=', $params['id']);
            }
        }

        //Search By receipt_return_detail_id
        if (isset($params['receipt_return_detail_id']) && $params['receipt_return_detail_id'] != '') {
            if(is_array($params['receipt_return_detail_id'])){
                $query->whereIn('receipt_return_detail_id',$params['receipt_return_detail_id']);
            }else{
                $query->where('receipt_return_detail_id', '=', $params['receipt_return_detail_id']);
            }
        }

        //Search By receipt_no
        if (isset($params['receipt_no']) && $params['receipt_no'] != '') {
            $query->where('receipt_no', '=', $params['receipt_no']);
        }

        //Search By receipt_code
        if (isset($params['receipt_code']) && $params['receipt_code'] != '') {
            $query->where('receipt_code', '=', $params['receipt_code']);
        }

        //Search By in_second_oil
        if (isset($params['in_second_oil_id']) && $params['in_second_oil_id'] != '') {
            $query->where('in_second_oil_id', '=', $params['in_second_oil_id']);
        }

        //Search By second_oil_type
        if (isset($params['select_status']) && $params['select_status'] != '') {
            $query->where('select_status', '=', $params['select_status']);
        }

        //Search By translate_status
        if (isset($params['translate_status']) && $params['translate_status'] != '') {
            $query->where('translate_status', '=', $params['translate_status']);
        }

        //Search By is_return
        if (isset($params['is_return']) && $params['is_return'] != '') {
            $query->where('is_return', '=', $params['is_return']);
        }

        //Search By in_sku
        if (isset($params['in_sku']) && $params['in_sku'] != '') {
            $query->where('in_sku', 'like', $params['in_sku'].'%');
        }

        //Search By createtime
        if (isset($params['create_time_from']) && $params['create_time_from'] != '') {
            $query->where('oil_receipt_translate_detail.createtime', '>=', $params['create_time_from']);
        }
        if (isset($params['create_time_to']) && $params['create_time_to'] != '') {
            $query->where('oil_receipt_translate_detail.createtime', '<=', $params['create_time_to']);
        }
        //Search By updatetime
        if (isset($params['last_time_from']) && $params['last_time_from'] != '') {
            $query->where('oil_receipt_translate_detail.updatetime', '>=', $params['last_time_from']);
        }
        if (isset($params['last_time_to']) && $params['last_time_to'] != '') {
            $query->where('oil_receipt_translate_detail.updatetime', '<=', $params['last_time_to']);
        }
        //Search By in_sku
        if (isset($params['progress']) && $params['progress'] != '') {
            $query->where('progress', '=', $params['progress']);
        }

        //Search By name 货物名称查询
        if (isset($params['name']) && $params['name'] != '') {
            $query->where('t1.name', 'like', '%'.$params['name'].'%');
        }

        //Search By in_sku
        if (isset($params['in_status']) && $params['in_status'] != '') {
            $query->where('in_status', '=', $params['in_status']);
        }

        //Search By transform
        if (isset($params['transform']) && $params['transform'] != '') {
            $query->where('transform', '=', $params['transform']);
        }

        if (isset($params['money_tax_gt'])) {
            $query->where('money_tax', '>', $params['money_tax_gt']);
        }
        if (isset($params['money_tax_lt'])) {
            $query->where('money_tax', '<', $params['money_tax_lt']);
        }

        if (! empty($params['receipt_return_id'])) {
            $query->where('receipt_return_id', '=', $params['receipt_return_id']);
        }

        if (! empty($params['receipt_apply_id'])) {
            $query->whereIn('oil_receipt_translate_detail.receipt_return_id', function ($query) use ($params) {
                $query->select('oil_receipt_apply_internal_details.receipt_return_id')
                    ->from('oil_receipt_apply_internal_details')
                    ->where('oil_receipt_apply_internal_details.receipt_apply_id', '=', $params['receipt_apply_id']);
            });
        }

        if (isset($params['in_status_neq'])) {
            $query->where('in_status', '!=', $params['in_status_neq']);
        }

        return $query;
    }

    /**
     * oil_receipt_translate_detail 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilReceiptTranslateDetail::Filter($params)->selectRaw('t1.name,t1.sku,t1.tax_rate as tax,
            t1.unit,t1.num,t2.name as oil_name,oil_receipt_translate_detail.*,t3.sys_value as in_unit_val')
        ->leftJoin('oil_receipt_return_detail as t1','t1.id','=','oil_receipt_translate_detail.receipt_return_detail_id')
        ->leftJoin('oil_type_base as t2','oil_receipt_translate_detail.in_second_oil_id','=','t2.id')
        ->leftJoin('oil_configure as t3','oil_receipt_translate_detail.in_unit','=','t3.id');
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else if(isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->orderBy('oil_receipt_translate_detail.createtime', 'desc')
                ->skip(intval($params['skip']))
                ->take(intval($params['take']))
                ->get();
        }else if(isset($params['count']) && $params['count'] == 1) {
            $data = $sqlObj->count();
        }else{
            $data = $sqlObj->orderBy('oil_receipt_translate_detail.createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }
        return $data;
    }
    /**
     * 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptTranslateDetail::find($params['id']);
    }

    /**
     * @param array $params
     * @return mixed
     */
    static public function getInfoByFilter(array $params)
    {
        return OilReceiptTranslateDetail::Filter($params)->first();
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilReceiptTranslateDetail::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * oil_receipt_translate_detail 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilReceiptTranslateDetail::create($params);
    }

    /**
     * oil_receipt_translate_detail 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);
        return OilReceiptTranslateDetail::find($params['id'])->update($params);
    }

    /**
     * oil_receipt_translate_detail 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilReceiptTranslateDetail::destroy($params['ids']);
    }

    /**
     * @param int $id
     * @return mixed
     */
    static public function getReturnDetail($id = 0)
    {
        return OilReceiptTranslateDetail::selectRaw('oil_receipt_return_detail.tax_rate,oil_receipt_return_detail.id,
            oil_receipt_translate_detail.in_num,oil_receipt_translate_detail.in_second_oil_id')
            ->leftJoin('oil_receipt_return_detail','oil_receipt_return_detail.id','=','oil_receipt_translate_detail.receipt_return_detail_id')
            ->where("oil_receipt_translate_detail.id","=",$id)
            ->first();
    }


    /**
     * @param $params
     * @param $pluckField
     * @return mixed
     */
    static public function getFilterByPluckFiled($params,$pluckField){
        return OilReceiptTranslateDetail::Filter($params)->pluck($pluckField)->toArray();
    }
    /**
     * 获取手动翻译列表
     *  @param $params
     * @return mixed
     */
    static public function getManualTranslateList($params)
    {
//        return  OilReceiptTranslateDetail::Filter($params)->get()->toArray();
        return OilReceiptTranslateDetail::Filter($params)
            ->selectRaw('oil_receipt_translate_detail.*,t1.unit')
            ->leftJoin('oil_receipt_return_detail as t1','t1.id','=','oil_receipt_translate_detail.receipt_return_detail_id')
            ->get()
            ->toArray();
    }

    /**
     * 分组获取规格型号
     * @return mixed
     */
    static function getListGroupBySku(){
        return OilReceiptTranslateDetail::where('in_sku','!=','')->where('select_status','=',2)
            ->groupby("in_sku")->pluck("in_sku")->toArray();
    }
    /**
     * 获取手动翻译未翻译完成导出列表
     * @param array $ids
     * @return mixed
     */
    static public function getExportList(array  $ids){
        $data=OilReceiptTranslateDetail::whereIn('oil_receipt_translate_detail.id',$ids)->selectRaw('t1.name,t1.sku,
            t1.unit,t1.num,t2.name as oil_name,t1.tax_rate,t2.tax,oil_receipt_translate_detail.*,t3.sys_key as in_unit_val')
            ->leftJoin('oil_receipt_return_detail as t1','t1.id','=','oil_receipt_translate_detail.receipt_return_detail_id')
            ->leftJoin('oil_type_base as t2','oil_receipt_translate_detail.in_second_oil_id','=','t2.id')
            ->leftJoin('oil_configure as t3','oil_receipt_translate_detail.in_unit','=','t3.id')->get()->toArray();

        return self::formData($data);
    }

    static public function formData($data){
        foreach ($data as $k=>$item){
            $data[$k]['translate_status_val']=ReceiptTranslateDetail::getTranslateStatusById($data[$k]['translate_status']);
            $data[$k]['is_return_val']=ReceiptTranslateDetail::getIsReturnStatusById($data[$k]['is_return']);
            $data[$k]['in_status_val']=ReceiptTranslateDetail::getInStatusById($data[$k]['in_status']);
            $data[$k]['in_mode_val']=ReceiptTranslateDetail::getInModeStatusById($data[$k]['in_mode']);
            $data[$k]['select_status_val']=ReceiptTranslateDetail::getSecondOilSelectStatusById($data[$k]['select_status']);
            $data[$k]['num']=$data[$k]['num']?sprintf("%.6f", $data[$k]['num']):null;
            $data[$k]['in_num']=$data[$k]['in_num']?sprintf("%.6f", $data[$k]['in_num']):null;
            $data[$k]['tax']=$item->tax?$item->tax.'%':null;
        }
        return $data;
    }

    /**
     * 批量更新
     * @param array $ids
     * @param array $updateData
     * @param array $params
     * @return mixed
     */
    static public function batchEdit(array $ids,array $updateData,array $params=[])
    {
        return OilReceiptTranslateDetail::Filter($params)->whereIn('id',$ids)->update($updateData);
    }
    /**
     * @param $params
     * @param $lock
     * @return mixed
     */
    static public function getListByFilter($params, $lock=false)
    {
        $obj = self::Filter($params);

        if ($lock) {
            $obj->lockForUpdate();
        }

        if (! empty($params['fields'])) {
            $obj->selectRaw($params['fields']);
        }

        if (! empty($params['groupBy'])) {
            foreach ($params['groupBy'] as $key) {
                $obj->groupBy($key);
            }
        }

        if (! empty($params['orderBy'])) {
            foreach ($params['orderBy'] as $k => $v) {
                $obj->orderBy($k, $v);
            }
        }

        return $obj->get()->toArray();
    }
    /**
     * 批量增加
     * @param array $params
     * @return mixed
     */
    static public function batchAdd(array $params)
    {
        return OilReceiptTranslateDetail::insert($params);
    }

    static public function editByFilter(array $params, $data)
    {
        if (empty($params))
            return true;

        return self::Filter($params)->update($data);
    }
}