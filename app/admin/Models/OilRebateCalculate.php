<?php
/**
 * 返利计算表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2020/09/25
 * Time: 16:16:22
 */

namespace Models;

use Fuel\Defines\RebateFormula;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilRebateCalculate extends \Framework\Database\Model
{
    protected $table = 'oil_rebate_calculate';

    protected $guarded = ["id"];
    protected $fillable = ['no', 'policy_id', 'policy_name', 'policy_level', 'suit_obj', 'suit_oil_type', 'cal_object',
        'cal_object_value', 'cal_count', 'rebate_type', 'cal_method', 'rebate_unit', 'fanli_val', 'fanli_money',
        'start_time', 'end_time', 'status', 'is_del', 'task_nums', 'creator', 'last_operator', 'createtime',
        'updatetime','min_amount','policy_type','card_type', 'is_show', 'cal_start_time', 'cal_end_time', 'final_status',
        'stable_operator', 'stable_time'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        if (isset($params['ids']) && $params['ids'] != '') {
            $ids = explode(",", $params['ids']);
            $query->whereIn('id', $ids);
        }

        //Search By no
        if (isset($params['no'])) {
            if (is_array($params['no'])) {
                $query->whereIn('no', $params['no']);
            } elseif ($params['no'] != '') {
                $query->where('no', '=', $params['no']);
            }
        }

        if (isset($params['nos']) && $params['nos'] != '') {
            $nos = explode(",", $params['nos']);
            $query->whereIn('no', $nos);
        }

        //Search By policy_id
        if (!empty($params['policy_id'])) {
            if (is_array($params['policy_id'])) {
                $query->whereIn('policy_id', $params['policy_id']);
            } else {
                $query->where('policy_id', '=', $params['policy_id']);
            }
        }

        //Search By policy_name
        if (isset($params['policy_name']) && $params['policy_name'] != '') {
            $query->where('policy_name', '=', $params['policy_name']);
        }

        if (isset($params['policy_name_lk']) && $params['policy_name_lk'] != '') {
            $query->where('policy_name', 'like', '%'.$params['policy_name_lk'].'%');
        }

        //Search By policy_type
        if (isset($params['policy_type']) && $params['policy_type'] != '') {
            $query->where('policy_type', '=', $params['policy_type']);
        }

        //Search By card_type
        if (isset($params['card_type']) && $params['card_type'] != '') {
            $query->where('card_type', '=', $params['card_type']);
        }

        //Search By policy_level
        if (isset($params['policy_level']) && $params['policy_level'] != '') {
            $query->where('policy_level', '=', $params['policy_level']);
        }

        //Search By suit_obj
        if (isset($params['suit_obj']) && $params['suit_obj'] != '') {
            $query->where('suit_obj', '=', $params['suit_obj']);
        }

        //Search By cal_object
        if (isset($params['cal_object']) && $params['cal_object'] != '') {
            $query->where('cal_object', '=', $params['cal_object']);
        }

        //Search By cal_object_value
        if (isset($params['cal_object_value']) && $params['cal_object_value'] != '') {
            $query->where('cal_object_value', '=', $params['cal_object_value']);
        }

        //Search By cal_count
        if (isset($params['cal_count']) && $params['cal_count'] != '') {
            $query->where('cal_count', '=', $params['cal_count']);
        }

        //Search By rebate_type
        if (isset($params['rebate_type']) && $params['rebate_type'] != '') {
            $query->where('rebate_type', '=', $params['rebate_type']);
        }

        //Search By cal_method
        if (isset($params['cal_method']) && $params['cal_method'] != '') {
            $query->where('cal_method', '=', $params['cal_method']);
        }

        //Search By rebate_unit
        if (isset($params['rebate_unit']) && $params['rebate_unit'] != '') {
            $query->where('rebate_unit', '=', $params['rebate_unit']);
        }

        //Search By fanli_val
        if (isset($params['fanli_val']) && $params['fanli_val'] != '') {
            $query->where('fanli_val', '=', $params['fanli_val']);
        }

        //Search By fanli_money
        if (isset($params['fanli_money']) && $params['fanli_money'] != '') {
            $query->where('fanli_money', '=', $params['fanli_money']);
        }

        //Search By start_time
        if (isset($params['start_time']) && $params['start_time'] != '') {
            $query->where('start_time', '=', $params['start_time']);
        }

        //Search By end_time
        if (isset($params['end_time']) && $params['end_time'] != '') {
            $query->where('end_time', '=', $params['end_time']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $statusIn = explode(",", $params['status']);
            if (count($statusIn) > 1) {
                $query->whereIn('status', $statusIn);
            } else {
                $query->where('status', '=', $params['status']);
            }
        }

        //Search By statusNotIn
        if (isset($params['statusNotIn']) && $params['statusNotIn'] != '') {
            $statusNotIn = explode(",", $params['statusNotIn']);
            if (count($statusNotIn) > 1) {
                $query->whereNotIn('status', $statusNotIn);
            } else {
                $query->where('status', '!=', $params['statusNotIn']);
            }
        }

        //Search By is_del
        if (isset($params['is_del']) && $params['is_del'] != '') {
            $query->where('is_del', '=', $params['is_del']);
        }

        //Search By task_id
        if (isset($params['task_nums']) && $params['task_nums'] != '') {
            $query->where('task_nums', '=', $params['task_nums']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By last_operator
        if (isset($params['last_operator']) && $params['last_operator'] != '') {
            $query->where('last_operator', '=', $params['last_operator']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By createtime
        if (isset($params['createtimeGe']) && $params['createtimeGe'] != '') {
            $query->where('createtime', '>=', $params['createtimeGe']);
        }

        //Search By createtime
        if (isset($params['createtimeLe']) && $params['createtimeLe'] != '') {
            $query->where('createtime', '<=', $params['createtimeLe']);
        }

        if (!empty($params['cal_end_time_lt'])) {
            $query->where('cal_end_time', '<', $params['cal_end_time_lt']);
        }

        if (!empty($params['cal_start_time_neq_null'])) {
            $query->whereRaw('cal_end_time is not null');
        }

        if (!empty($params['cal_start_time_is_null'])) {
            $query->whereRaw('cal_end_time is null');
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        if (! empty($params['is_show'])) {
            $query->where('is_show', '=', $params['is_show']);
        }

        if (isset($params['final_status_in'])) {
            $query->whereIn('final_status', $params['final_status_in']);
        }

        if (isset($params['final_status'])) {
            $query->where('final_status', $params['final_status']);
        }

        if (isset($params['final_status_neq'])) {
            $query->where('final_status', '!=', $params['final_status_neq']);
        }

        if (isset($params['final_status_not_in'])) {
            $query->whereNotIn('final_status', $params['final_status_not_in']);
        }

        if (! empty($params['month'])) {
            $calStart = strtotime($params['month']."-01 00:00:00");
            $calEnd = strtotime('+1 month', $calStart) - 1;
            $query->where('cal_start_time', '>=', date('Y-m-d H:i:s', $calStart))
                ->where('cal_end_time', '<=', date('Y-m-d H:i:s', $calEnd));
        }

        return $query;
    }

    /**
     * 返利计算表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilRebateCalculate::Filter($params);

        if (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->orderBy('id', 'desc')->get();
        }else if(isset($params['skip']) && isset($params['take'])) {
            $data = $sqlObj->orderBy('id', 'desc')->skip(intval($params['skip']))->take(intval($params['take']))->get();
        } else {
            $data = $sqlObj->orderBy('id', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }

        if (count($data) == 0) {
            return $data;
        }

        $data = self::formatData($data);

        return $data;
    }

    static public function formatData($data)
    {
        $calObject = RebateFormula::$caObj;
        $method = RebateFormula::$caMethod;
        $unit = RebateFormula::$rebateUnit;
        foreach ($data as &$v) {
            $v->cal_object_txt = isset($calObject[$v->cal_object]) ? $calObject[$v->cal_object] : '';
            $v->total_money = number_format($v->cal_object_money, 2, ".", "");
            $v->total_nums = number_format($v->cal_object_num, 2, ".", "");
            $v->total_fanli = number_format($v->fanli_money, 2, ".", "");
            $v->min_amount = number_format($v->min_amount, 2, ".", "");

            switch ($v->status) {
                case 15:
                    $v->status_txt = "系统处理中";
                    break;
                case 20:
                    $v->status_txt = "待审核";
                    break;
                case 30:
                    $v->status_txt = "已审核";
                    break;
                case 40:
                    $v->status_txt = "已驳回";
                    break;
                case 50:
                    $v->status_txt = "未捞到数据，无需审核";
                    break;
                default:
                    $v->status_txt = "系统处理中";
            }
            $v->rebate_type_txt = $v->rebate_type == 20 ? "积分" : '现金';
            $v->method_txt = isset($method[$v->cal_method]) ? $method[$v->cal_method] : '';
            $v->unit_txt = isset($unit[$v->rebate_unit]) ? $unit[$v->rebate_unit] : '';
            $v->cal_day = round((strtotime($v->cal_end_time) - strtotime($v->cal_start_time)) / 86400, 0);
        }
        return $data;
    }

    /**
     * 返利计算表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilRebateCalculate::find($params['id']);
    }

    static public function getInfoByFilter(array $params)
    {
        return OilRebateCalculate::Filter($params)->first();
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilRebateCalculate::lockForUpdate()->where('id', $params['id'])->first();
    }

    /**
     * 返利计算表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilRebateCalculate::create($params);
    }

    static public function batchAdd(array $params)
    {
        return self::insert($params);
    }

    /**
     * 返利计算表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilRebateCalculate::find($params['id'])->update($params);
    }

    static public function editByNo($no = "", array $updateData)
    {
        if (empty($no)) {
            return false;
        }
        return OilRebateCalculate::where("no", $no)->update($updateData);
    }

    /**
     * 返利计算表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilRebateCalculate::destroy($params['ids']);
    }

    /**
     * @param array $params
     * @return string
     * 生成返利计算工单号
     */
    static public function createFanliNo()
    {
        $microTime = microtime();
        $microArr = explode(" ", $microTime);
        $str = "FL" . date("ymds", time());
        $str .= substr($microArr[0], 3, 3);
        $str .= sprintf('%02d', rand(0, 99));

        $exist = self::where("no", "=", $str)->lockForUpdate()->first();
        if ($exist) {
            self::createFanliNo();
        }

        return $str;
    }
    /**
     * 取总数
     * @param array $params
     * @return mixed
     */
    static public function getTotal(array $params)
    {
        return self::Filter($params)->count();
    }

}