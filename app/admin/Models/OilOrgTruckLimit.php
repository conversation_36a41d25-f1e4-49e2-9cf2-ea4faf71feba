<?php
/**
 * 机构车牌限制
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2024/07/04
 * Time: 14:44:18
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilOrgTruckLimit extends \Framework\Database\Model
{
    protected $table = 'oil_org_truck_limit';

    protected $guarded = ["id"];
    protected $fillable = ['orgcode','is_open','car_limit_type','createtime','updatetime','creator','modifier'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By is_open
        if (isset($params['is_open']) && $params['is_open'] != '') {
            $query->where('is_open', '=', $params['is_open']);
        }

        //Search By car_limit_type
        if (isset($params['car_limit_type']) && $params['car_limit_type'] != '') {
            $query->where('car_limit_type', '=', $params['car_limit_type']);
        }

        //Search By create_time
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By update_time
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        //Search By creator
        if (isset($params['creator']) && $params['creator'] != '') {
            $query->where('creator', '=', $params['creator']);
        }

        //Search By modifier
        if (isset($params['modifier']) && $params['modifier'] != '') {
            $query->where('modifier', '=', $params['modifier']);
        }

        return $query;
    }

    /**
     * 机构车牌限制 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilOrgTruckLimit::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 机构车牌限制 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgTruckLimit::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgTruckLimit::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 机构车牌限制 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilOrgTruckLimit::create($params);
    }

    /**
     * 机构车牌限制 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilOrgTruckLimit::find($params['id'])->update($params);
    }

    /**
     * 机构车牌限制 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilOrgTruckLimit::destroy($params['ids']);
    }




}