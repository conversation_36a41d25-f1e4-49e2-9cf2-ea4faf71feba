<?php
/**
 * 机构资金账户表快照表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2018/01/12
 * Time: 17:51:09
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilAccountMoneySnapshot extends \Framework\Database\Model
{
    protected $table = 'oil_account_money_snapshot';

    protected $guarded = ["id"];
    protected $fillable = ['subAccountID','backSubAccountID','account_no','org_id','money','cash_fanli_remain','fanli_discount_remain','charge_total','assign_total','fanli_total','total_transfer_in','total_transfer_out','exec_time','status','last_charge_time','createtime','updatetime'];
    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        
        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }

        //Search By subAccountID
        if (isset($params['subAccountID']) && $params['subAccountID'] != '') {
            $query->where('subAccountID', '=', $params['subAccountID']);
        }

        //Search By backSubAccountID
        if (isset($params['backSubAccountID']) && $params['backSubAccountID'] != '') {
            $query->where('backSubAccountID', '=', $params['backSubAccountID']);
        }

        //Search By account_no
        if (isset($params['account_no']) && $params['account_no'] != '') {
            $query->where('account_no', '=', $params['account_no']);
        }

        //Search By org_id
        if (isset($params['org_id']) && $params['org_id'] != '') {
            $query->where('org_id', '=', $params['org_id']);
        }

        //Search By money
        if (isset($params['money']) && $params['money'] != '') {
            $query->where('money', '=', $params['money']);
        }

        //Search By cash_fanli_remain
        if (isset($params['cash_fanli_remain']) && $params['cash_fanli_remain'] != '') {
            $query->where('cash_fanli_remain', '=', $params['cash_fanli_remain']);
        }

        //Search By fanli_discount_remain
        if (isset($params['fanli_discount_remain']) && $params['fanli_discount_remain'] != '') {
            $query->where('fanli_discount_remain', '=', $params['fanli_discount_remain']);
        }

        //Search By charge_total
        if (isset($params['charge_total']) && $params['charge_total'] != '') {
            $query->where('charge_total', '=', $params['charge_total']);
        }

        //Search By assign_total
        if (isset($params['assign_total']) && $params['assign_total'] != '') {
            $query->where('assign_total', '=', $params['assign_total']);
        }

        //Search By fanli_total
        if (isset($params['fanli_total']) && $params['fanli_total'] != '') {
            $query->where('fanli_total', '=', $params['fanli_total']);
        }

        //Search By total_transfer_in
        if (isset($params['total_transfer_in']) && $params['total_transfer_in'] != '') {
            $query->where('total_transfer_in', '=', $params['total_transfer_in']);
        }

        //Search By total_transfer_out
        if (isset($params['total_transfer_out']) && $params['total_transfer_out'] != '') {
            $query->where('total_transfer_out', '=', $params['total_transfer_out']);
        }

        //Search By exec_time
        if (isset($params['exec_time']) && $params['exec_time'] != '') {
            $query->where('exec_time', '=', $params['exec_time']);
        }

        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By last_charge_time
        if (isset($params['last_charge_time']) && $params['last_charge_time'] != '') {
            $query->where('last_charge_time', '=', $params['last_charge_time']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }

        return $query;
    }

    /**
     * 机构资金账户表快照表 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = OilAccountMoneySnapshot::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * 机构资金账户表快照表 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountMoneySnapshot::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountMoneySnapshot::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 机构资金账户表快照表 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return OilAccountMoneySnapshot::create($params);
    }

    /**
     * 机构资金账户表快照表 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return OilAccountMoneySnapshot::find($params['id'])->update($params);
    }

    /**
     * 机构资金账户表快照表 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return OilAccountMoneySnapshot::destroy($params['ids']);
    }




}