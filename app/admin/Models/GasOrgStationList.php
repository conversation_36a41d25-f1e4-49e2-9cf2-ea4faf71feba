<?php
/**
 * gas油站表
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2016/03/11
 * Time: 08:01:19
 */

namespace Models;

use Illuminate\Database\Capsule\Manager as Capsule;

class GasOrgStationList extends \Framework\Database\Model
{
    protected $connection = "gas_online";

    protected $table = 'gas_org_station_list';

	protected $guarded = [];

	protected $fillable = [];

	public function getFillAble()
	{
		return $this->fillable;
	}

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return mixed $query
     */
	public function scopeFilter($query, $params)
	{
        if (isset($params['station_id'])) {
            if (is_array($params['station_id'])) {
                $query->whereIn('station_id', $params['station_id']);
            } else {
                $query->where('station_id', '=', $params['station_id']);
            }
        }

        if (isset($params['orgcode'])) {
            if (is_array($params['orgcode'])) {
                $query->whereIn('orgcode', $params['orgcode']);
            } else {
                $query->where('orgcode', '=', $params['orgcode']);
            }
        }
		return $query;
	}

    public static function getInstitutionSpecificStation($orgCode, $stationId)
    {
        //如果查询到油站配置规则且包含机构编码，则返回1(可用)，否则返回2(不可用)
        return (new self)->Filter([
            "station_id" => $stationId,
        ])->select([
            Capsule::raw("group_concat(distinct if(orgcode = '$orgCode', 1, 2)) as available")
        ]);
    }
}