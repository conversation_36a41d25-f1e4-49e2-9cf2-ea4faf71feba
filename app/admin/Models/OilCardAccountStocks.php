<?php
/**
 * 卡账户库存
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: 2017/12/12
 * Time: 18:32:04
 */

namespace Models;

use Framework\Helper;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Capsule\Manager as Capsule;

class OilCardAccountStocks extends \Framework\Database\Model
{
    protected $table = 'oil_card_account_stocks';

    protected $fillable = ['id', 'orgcode', 'common_account_no', 'subAccountType', 'cardSubAccountID','unique_id','status', 'createtime', 'updatetime'];

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {

        //Search By id
        if (isset($params['id']) && $params['id'] != '') {
            $query->where('id', '=', $params['id']);
        }


        //Search By status
        if (isset($params['status']) && $params['status'] != '') {
            $query->where('status', '=', $params['status']);
        }

        //Search By orgcode
        if (isset($params['orgcode']) && $params['orgcode'] != '') {
            $query->where('orgcode', '=', $params['orgcode']);
        }

        //Search By createtime
        if (isset($params['createtime']) && $params['createtime'] != '') {
            $query->where('createtime', '=', $params['createtime']);
        }

        //Search By updatetime
        if (isset($params['updatetime']) && $params['updatetime'] != '') {
            $query->where('updatetime', '=', $params['updatetime']);
        }


        //Search By statusIn
        if (isset($params['statusIn']) && $params['statusIn']) {
            $query->whereIn('oil_card_vice.status', $params['statusIn']);
        }

        return $query;
    }

    /**
     * 卡账户 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        //Capsule::connection()->enableQueryLog();
        $sqlObj = OilCardAccountStocks::Filter($params);

        if (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        }elseif(isset($params['skip']) && isset($params['take'])){
            $data = $sqlObj->skip(intval($params['skip']))->take(intval($params['take']))->get();
        }elseif (isset($params['_export']) && $params['_export'] == 1) {
            $data = $sqlObj->get();
        }else {
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'], ['*'], 'page', $params['page']);
        }
        //$ssl = Capsule::connection()->getQueryLog();
        //print_r($ssl);exit;

        return $data;
    }


    /**
     * 卡账户 列表查询
     * @param array $params
     * @return array
     */
    static public function getStockList(array $params)
    {
        $sqlObj = OilCardAccountStocks::Filter($params);

        $data = $sqlObj->groupby('subAccountType','common_account_no')->get();


        return $data;

    }

    /**
     * 卡账户 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardAccountStocks::find($params['id']);
    }

    /**
     * 悲观锁查询
     * @param array $params
     * @return object
     */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardAccountStocks::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * 悲观锁查询账户数量
     * @param array $params
     * @return object
     */
    static public function getAccountNum(array $params)
    {
        \helper::argumentCheck(['orgcode','accounttype'], $params);
        //Capsule::connection()->enableQueryLog();
        $data = OilCardAccountStocks::lockForUpdate()
            ->where('orgcode',$params['orgcode'])
            ->where('subAccountType',$params['accounttype'])
            ->where('common_account_no',$params['account_no'])
            ->where('status',2)
            ->count();
        //$sssl = Capsule::connection()->getQueryLog();
        return $data;
    }


    /**
     * 悲观锁查询账户数量
     * @param array $params
     * @return object
     */
    static public function getAccountTypeNum(array $params,$field="")
    {
        \helper::argumentCheck(['orgcode'], $params);
        //Capsule::connection()->enableQueryLog();
        $data = OilCardAccountStocks::lockForUpdate()
            ->where('orgcode',$params['orgcode'])
            ->where('status',2)
            ->select(Capsule::Raw($field))
            ->groupby("subAccountType")
            ->orderby("createtime","asc")
            ->get();
        //$sssl = Capsule::connection()->getQueryLog();
        return $data;
    }


    /**
     * 插入时检查是否存在
     * @param array $params
     * @return object
     */
    static public function getByUniqueLock(array $params)
    {
        return OilCardAccountStocks::Filter($params)->first();
    }

    /**
     * 获取卡账户不全的Vice_id
     * @param array $params
     * @return object
     */
    static public function getNoAccountViceId()
    {
        return OilCardAccountStocks::select(Capsule::Raw("count(id) as num,vice_id"))->where("cardID",'not like',"%_1")->groupBy("cardID")->having("num",'=',1)->get();
    }

    /**
     * 卡账户 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        $params['updatetime'] = $params['createtime'] = date("Y-m-d H:i:s");

        return OilCardAccountStocks::create($params);
    }

    /**
     * 卡账户 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        return OilCardAccountStocks::find($params['id'])->update($params);
    }

    /**
     * 卡账户 编辑
     * @param array $params
     * @return mixed
     */
    static public function editByIds(array $params,$data)
    {
        return OilCardAccountStocks::whereIn("id",$params)->update($data);
    }

    /**
     * 卡账户 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'], $params);

        return OilCardAccountStocks::destroy($params['ids']);
    }

    /**
     * 卡账户 根据卡id获取卡账户
     * @param array $params
     * @return int
     */
    static public function getCardAccount(array $params)
    {
        \helper::argumentCheck(['vice_id'], $params);
        //Capsule::connection()->enableQueryLog();
        return OilCardAccountStocks::where("vice_id",$params['vice_id'])->where("subAccountType","CREDIT")->first();
        //print_r(Capsule::connection()->getQueryLog());
    }

    /**
     * @title   根据cardIDs删除卡账户
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Models
     * @since
     * @params  type filedName required?
     * @param array $cardIds
     * @return mixed
     * @returns
     * []
     * @returns
     */
    static public function removeByCardIds(array $cardIds)
    {
        return self::whereIn('cardId',$cardIds)->delete();
    }

}