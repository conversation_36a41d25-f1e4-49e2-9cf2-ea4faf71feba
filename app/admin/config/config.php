<?php

/**
 * The config file of ZenTaoPHP.
 *
 * ZenTaoPHP is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.

 * ZenTaoPHP is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with ZenTaoPHP.  If not, see <http://www.gnu.org/licenses/>.
 *
 * @copyright   Copyright 2009-2010 青岛易软天创网络科技有限公司(www.cnezsoft.com)
 * <AUTHOR> <<EMAIL>>
 * @package     ZenTaoPHP
 * @version     $Id: config.php 109 2010-05-02 15:42:08Z wwccss $
 * @link        http://www.zentaoms.com
 */
$config = new stdClass();
$config->version = '1.0.STABLE.090620'; // 版本号，切勿修改。
$config->debug = TRUE;              // 是否打开debug功能。
$config->webRoot = '/';               // web网站的根目录。
$config->encoding = 'UTF-8';           // 网站的编码。
$config->cookiePath = '/';               // cookie的有效路径。
$config->cookieLife = time() + 2592000;  // cookie的生命周期。
$config->cookiepre = 'gsp_admin'; //cookie前缀

$config->api_env = 'dev';

$config->strictParams = true;              // 参数按名称精确匹配
$config->requestType = 'GET';             // 如何获取当前请求的信息，可选值：PATH_INFO|GET
$config->pathType = 'clean';           // requestType=PATH_INFO: 请求url的格式，可选值为full|clean，full格式会带有参数名称，clean则只有取值。
$config->requestFix = '/';               // requestType=PATH_INFO: 请求url的分隔符，可选值为斜线、下划线、减号。后面两种形式有助于SEO。
$config->moduleVar = 'm';               // requestType=GET: 模块变量名。
$config->methodVar = 'f';               // requestType=GET: 方法变量名。
$config->viewVar = 't';               // requestType=GET: 模板变量名。
$config->sessionVar = 's';

$config->views = ',html,xml,json,txt,csv,doc,pdf,'; // 支持的视图列表。
$config->langs = 'zh-cn,zh-tw,zh-hk,en';            // 支持的语言列表。
$config->themes = 'default';                         // 支持的主题列表。

$config->default = new stdClass();
$config->default->view = 'json';                      // 默认的视图格式，可选值：html|blz|tpl|json
$config->default->lang = 'zh-cn';                     // 默认的语言。
$config->default->theme = 'default';                   // 默认的主题。
$config->default->module = 'index';                     // 默认的模块。当请求中没有指定模块时，加载该模块。
$config->default->method = 'index';                     // 默认的方法。当请求中没有指定方法或者指定的方法不存在时，调用该方法。

$config->db = new stdClass();
$config->db->errorMode = PDO::ERRMODE_WARNING;         // PDO的错误模式: PDO::ERRMODE_SILENT|PDO::ERRMODE_WARNING|PDO::ERRMODE_EXCEPTION
$config->db->persistant = false;                        // 是否打开持久连接。

//mail连接//Story #41510
$config->mail = new stdClass();
$config->mail->host = 'smtp.partner.outlook.cn';
$config->mail->port = '587';
$config->mail->user = '<EMAIL>';
$config->mail->password = 'Ruco6310';

/**暂时
$config->db->driver     = 'mysql';                      // pdo的驱动类型，目前暂时只支持mysql。
$config->db->host       = '************';//'*************';  //'***********';                  // mysql主机。
$config->db->port       = '3306';                       // mysql主机端口号。
$config->db->name       = 'cw_service';                     // 数据库名称。
$config->db->user       = 'root';                       // 数据库用户名。
$config->db->password   = '123456';                           // 密码。
$config->db->encoding   = 'UTF8';                       // 数据库的编码。
 */
//
 $config->db->driver     = 'mysql';                      // pdo的驱动类型，目前暂时只支持mysql。
 $config->db->host       = '*************';//'*************';  //'***********';                  // mysql主机。
 $config->db->port       = '3306';                       // mysql主机端口号。
 $config->db->name       = 'gsp_fuel';                     // 数据库名称。
 $config->db->user       = 'cat';                       // 数据库用户名。
 $config->db->password   = 'cat@2021';                           // 密码。

 $config->db->encoding   = 'UTF8';                       // 数据库的编码。
//$config->db->driver     = 'mysql';                      // pdo的驱动类型，目前暂时只支持mysql。
//$config->db->host       = '***********';//'*************';  //'***********';                  // mysql主机。
//$config->db->port       = '3306';                       // mysql主机端口号。
//$config->db->name       = 'gsp_fuel';                     // 数据库名称。
//$config->db->user       = 'cat';                       // 数据库用户名。
//$config->db->password   = 'cathyr';                           // 密码。
//$config->db->encoding   = 'UTF8';                       // 数据库的编码。
 $config->db->mulitDB = [
    //财务官报
    'finance'=>[
         'driver'    => 'mysql',
         'host'      => 'rm-bp17v75l42b5e1vvk.mysql.rds.aliyuncs.com',//'***********',
         'database'  => 'oil',//'gos_dev',
         'username'  => 'oil_rw',//'cat',
         'password'  => '0AWVsk37kpmaDovB',//'cathyr',
         'charset'   => 'utf8',
         'collation' => 'utf8_general_ci',
         'prefix'    => '',
    ],
    //foss线上只读
    'online_only_read' => [
        'driver'    => 'mysql',
        'host'      => '*************',//'***********',
        'database'  => 'gsp_fuel',//'gos_dev',
        'username'  => 'cat',//'cat',
        'password'  => 'cat@2021',//'cathyr',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => '',
     ],
    //gas线上库 仅用于圆通流水统计
    'gas_online' => [
        'driver'    => 'mysql',
        'host'      => 'c0a747af9fa343b98ef370b6177aeaf4in01.internal.cn-north-4.mysql.rds.myhuaweicloud.com',//'***********',
        'database'  => 'cat_gas_test',//'gos_dev',
        'username'  => 'cat_gas_test',//'cat',
        'password'  => '99Rl1ZAkq',//'cathyr',
        'charset'   => 'utf8',
        'collation' => 'utf8_general_ci',
        'prefix'    => '',
   ]
 ];


// $config->db->driver     = 'mysql';                      // pdo的驱动类型，目前暂时只支持mysql。
// $config->db->host       = '*************';//'*************';  //'***********';                  // mysql主机。
// $config->db->port       = '33120';                       // mysql主机端口号。
// $config->db->name       = 'gsp';                     // 数据库名称。
// $config->db->user       = 'gsp_user_r';                       // 数据库用户名。
// $config->db->password   = 'axziPey7';                           // 密码。
// $config->db->encoding   = 'UTF8';                       // 数据库的编码。

//$config->db->driver     = 'mysql';                      // pdo的驱动类型，目前暂时只支持mysql。
//$config->db->host       = '***********';//'*************';  //'***********';                  // mysql主机。
//$config->db->port       = '3306';                       // mysql主机端口号。
//$config->db->name       = 'gsp';                     // 数据库名称。
//$config->db->user       = 'admin';                       // 数据库用户名。
//$config->db->password   = 'vfr4%TGBnhy6&UJM';                           // 密码。
//$config->db->encoding   = 'UTF8';                       // 数据库的编码。


$config->dbmssql = new stdClass();

$config->dbmssql->driver     = 'mssql';                      // pdo的驱动类型，目前暂时只支持mysql。
$config->dbmssql->host       = '************';//'*************';  //'***********';
$config->dbmssql->name       = 'AIS20140718202428';                     // 数据库名称。
$config->dbmssql->user       = 'sa';                       // 数据库用户名。
$config->dbmssql->password   = 'kingdee@httx';                           // 密码。

//K3中间库 链接//Story #19017
$config->dbmssql_K3BZJK = new stdClass();
$config->dbmssql_K3BZJK->driver = 'mssql';                      //pdo的驱动类型，目前暂时只支持mysql。
$config->dbmssql_K3BZJK->host = '************';                 //************
$config->dbmssql_K3BZJK->name = 'K3BZJK_Version_2.0';           //数据库名称。
$config->dbmssql_K3BZJK->user = 'sa';                           //数据库用户名。
$config->dbmssql_K3BZJK->password = 'kingdee@httx';             //密码。

$config->ldap = new stdClass();
$config->ldap->host = '************';  //ldap host
$config->ldap->port = '389';  //ldap端口号
$config->ldap->user = 'cn=root,dc=huoyunren,dc=com';   //DN
$config->ldap->password = 'huoyunren';     //密码
$config->ldap->initpwd = 'Tianxia800';     //初始密码//Story #41395 zzg

$config->gps = new stdClass();
$config->gps->table = 'lct_gps_card';            // 默认设备表


// email
$config->email = new stdClass();
/*$config->email->host = 'smtp.partner.outlook.cn';
$config->email->username = '<EMAIL>';
$config->email->password = 'norepay_605@gas*';
$config->email->from = '<EMAIL>';*/
$config->email->host = 'smtp.feishu.cn';
$config->email->username = '<EMAIL>';
$config->email->password = 'hCr1XvFCu8mnIvEW';
$config->email->from = '<EMAIL>';
$config->email->SMTPSecure = "tls";
$config->email->Port = '587';
$config->email->SMTPAuth = true;
$config->email->fromName = '油品管理';

$config->oss_dir = 'upload_local';  //本地环境

//钉钉
$config->dingTalk = new stdClass();
$config->dingTalk->app_key = '112445';
$config->dingTalk->app_secret = '8f5271';

//1号卡余额变动，模板消息id
$config->gas = new stdClass();
$config->gas->templateID = 'LcRHphaAyE5Dk94dXGqSbD0kNeo6WLlIfsTkHV6KAVM';
$config->gas->templateDelID = '7RivlwVONHwPvMT1Se3C1Wl-_RS1r3QpWvLP-NKkHcM';
$config->gas->is_expire = 0; //上线众邦卡，关闭更新卡余额 1：关闭 0：开启
$config->gas->isOpenStocks = 0; //上线G7保理，开卡时，是否使用卡库存，1：使用，0：关闭

//开票优化,消费月报
$config->receipt = new stdClass();
$config->receipt->isOpen = 2; // 1:开启,2:关闭
$config->receipt->orgList = []; //灰度机构白名单
$config->receipt->month = 5; //获取几个月消费
//中石油卡,开具天然气发票
$config->receipt->gasOrgList = ["2000ZH","2034H2","201MKB"];
//针对201E6U 长久物流集团总部，开票方式由集中更改为独立
$config->receipt->specialChargeOrg = ["20002101","201E6U01","201E6U06"];
$config->receipt->specialCompany = ["同福客栈","天津长久智运科技有限公司"];
//由集中该独立开票后，占用其他机构消费（key:开票机构，val:占用机构）
$config->receipt->modeSwitch = 1;
$config->receipt->modeOrgList = ["200D8X0C"=>"200D8X02","20002101"=>"20002102","201UHB04"=>"201UHB","203IIG08"=>"203IIG01"];
//库存节省开关,修改后，需要重启supervisord
$config->receipt->openSave = 2;//1:开，2：关
//库存节省黑名单,新增后，需要重启supervisord
$config->receipt->orgBlackList = [
    '200MNR',//义米能源科技（上海）有限公司
    '200P0I',//盈科世名（厦门）石化有限公司
    '200UOZ',//上海盛海石油化工有限公司
    '200V12',//湖北盛宇源化工有限公司
    '20108J',//厦门永新城石油制品有限公司
    '201GK7',//江苏优联可能源科技有限公司
    '201H1S',//宁夏九鼎物流
    '200Q4S',//福佑（大连）能源有限公司
    '200WKE',//四川森能天然气销售有限公司
    '201UTS',//浙江舟山明文石化有限公司
    '201MS8',//武汉鑫强飞石油化工有限公司
    '201RWX',//宁夏中鸿源化工有限公司
    '201VOK',//黑龙江华盛宏图能源贸易有限公司
    '201XGB',//四川德盛巨能石油化工有限公司
    '2025ZE',//刘洪姣
    '201W6O',//山东坤瑞能源有限公司
    '20272M',//重庆国通石油
    '202AM1',//卓达能源（深圳）有限公司
    '202AQL',//物流宝兑通
    '2026QS',//陕西恒通包装物流有限公司
    '202F0G',//舟山陆泰石油化工有限公司
    '202F0R',//江苏卡满行物联科技有限公司-油品
    '202FMY',//无锡壹卡车网络科技有限公司
    '202GSP',//张海代理商
    '202VO7',//铁铁智慧物流（天津）有限公司
    '20391H',//广东汉森能源科技有限公司1
    '2034BK',//舟山励拓石油化工有限公司
    '2037V6',//上海亥亚石油化工有限公司
    '2016F3',//内蒙古金谷石化有限责任公司
    '202D6S',//河南科益气体股份有限公司
    '2018AA',//广东华特气体股份有限公司
    '2032DM',//深圳市候鸟互联网有限公司1
    '2038RE',//延边东北亚新能源开发有限公司天津分公司
    '200T9G',//壹米滴答
    '2038U8',//武汉海云丰物流有限公司-油品
    '203HQV',//深圳市佳润石油有限公司
    '203HN2',//大连港万通物流有限公司
    '203H9P',//浙江自贸区仲德能源有限公司
    '202LC1',//黑龙江中阔物流有限公司-油品代理
    '203IAI',//森荣（大连）能源有限公司 
    '203JFY',//上海虹鲁石油化工有限公司 
    '203JG8',//江苏安苏化工产品有限公司 
    '203KRK',//大连龙门石油化工有限公司 
    '203LZY',//河北跨省业务
    '203MTI',//宏智控股有限公司
    '203LN7',//辽宁启恒供应链管理有限公司
    '203NAF',//辽宁星斗生物科技有限公司
    '203MH9',//重庆油圈共享有限公司
    '203OYN',//浙江跨省业务销售有限公司
    '203FJ3',//辽宁阿拉丁供应链管理有限公司
    '202VK6',//济南捷安冷藏物流有限公司-1号卡
    '203RS4',//黑龙江天狼星物流有限公司
    '203RIB',//哈尔滨跃兴物流有限公司
    '203SCU',//哈尔滨龙鹏物流有限责任公司
    '203SG8',//海城云仓物流有限公司
    '203TC6',//浙江奥宸石化有限公司-油品
    '203SSE',//哈尔滨瑞锦鑫运输有限公司
    '203SSH',//黑龙江南翔国际物流有限公司
    '203SSL',//	海城飞扬物流有限公司
    '203SZT',//	天津市飞腾物流有限公司
    '203TKB',//天津市大通物流有限公司
    '203TQF',//	辽宁物流有限公司
    '203SHS',//四川李俊锋石油化工有限公司
    '203V29',//周来福商贸有限公司
    '203SOG',//山东启源石油化工有限公司
    '203NCY',//营口盛鑫隆泰物资有限公司
];

//开票优化,消费月报
$config->customer = new stdClass();
$config->customer->orgcode = "200133"; //灰度机构白名单
$config->customer->orgCodeGas = ["202N7A","200NW5"]; //返利只标记天然气的机构
$config->customer->transferOrgCode = ['201E6U','200021']; //G7WALLET-399
$config->customer->transferUserIds = [1,1001740,1001616,1001285,1001705];//独立机构转账用户黑名单


//自助发卡,自动审核发卡申请
$config->preCardAppley = new stdClass();
$config->preCardAppley->orgcode = ['200EOC','200021'];

// 停扣费
$config->trade = new stdClass();
$config->trade->is_stop = 2; //上线卡车宝贝 是否停扣费 1是 2否
$config->trade->gray_org = ['200NYJ']; //灰度机构

//Foss日志，排查的接口名称
$config->fossLogs = new stdClass();
$config->fossLogs->exceiptList = ['getList','search','searchAll','getOrg','countTotalData','getConfig','getOilOrg','getBindArea','getOilSupplier','getOilSupplierStations'];

//Foss数据导出配置
$config->exportJob = new stdClass();
$config->exportJob->exportToken = "1917-61.MA0GXGOOa0RsHWrGprAkOGBT";
$config->exportJob->switch = 2; // 导出开关 1：开，2:关
$config->exportJob->whiteList = ['ExportSupplierAccountJob','oil_receipt_apply.exportlist'];

$config->scheduler = new stdClass();
//容器
//$config->scheduler->addr = 'http://hyr-desp-scheduler-service:8012';
//测试
$config->scheduler->addr = 'https://desp-api.test.chinawayltd.com';
//线上
//$config->scheduler->addr = 'https://desp-api.huoyunren.com';
$config->scheduler->taskType = 2; //下发方式 2:网关，1:服务名
$config->scheduler->switch = 2; // 导出开关 1：开，2:关
$config->scheduler->goQueueWhite = ['ExportSupplierAccountJob','SubmitDownFileJob','ExportIssueJob','ReceiptApplyJob','SupplierChargeJob','ExportReceiptReturnJob'];

//define('IMAGE_TMP_PATH', '../tmp/data/temp/');    //图片临时路径  //Story #43067

//oss 信息配置
$config->oss = new stdClass();
$config->oss->bucket_name = "g7s-fs";
$config->oss->url = "oss.aliyuncs.com";
$config->oss->host = "https://g7s-fs.oss-cn-hangzhou.aliyuncs.com/";
$config->oss->id = "LTAICgZCNKtJ9f9F";
$config->oss->key = "******************************";
$config->oss->fileName = "foss-base-service/dev";

//新
/*$config->oss->bucket_name = "gsp-fuel-prod";
$config->oss->url = "oss-cn-hangzhou.aliyuncs.com"exite
$config->oss->host = "https://gsp-fuel-prod.oss-cn-hangzhou.aliyuncs.com/";
$config->oss->id = "LTAI5tQu75wmxRdAhQk7siht";
$config->oss->key = "******************************";*/


//已开票的消费允许退款撤回机构配置
$config->receipt->cancelOrgList = ["200NW5"];