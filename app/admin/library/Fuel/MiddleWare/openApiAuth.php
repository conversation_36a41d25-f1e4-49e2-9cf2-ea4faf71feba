<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/15/015
 * Time: 19:43
 */

namespace Fuel\MiddleWare;

class openApiAuth extends BaseAuth
{
    /**
     * 本次请求的所有参数
     * @var null
     */
    protected $params = null;

    /**
     * 用于计算sign的token
     * @var null
     */
    protected $token = NULL;

    /**
     * 是否校验sign
     * @var bool
     */
    protected $checkSign = false;//true校验，false不校验

    /**
     * 必须有的参数
     * @var array
     */
    protected $mustParams = ['app_key','method','sign','data','timestamp'];

    public function __construct()
    {
        global $app;
        $app->data_from = 6;

        $this->params = $_REQUEST;

        parent::__construct();
    }

    public function __destruct()
    {
        return parent::__destruct(); // TODO: Change the autogenerated stub
    }

    public function handle()
    {
        foreach($this->mustParams as $v){
            if(!isset($this->params[$v])){
                throw new \RuntimeException($v.'参数缺失',2);
            }
        }

        $this->checkSign();
    }

    /**
     * 检测签名值是否正确
     * @return bool
     */
    public function checkSign()
    {
        $_sign = $this->params['sign'];
        unset($this->params['sign']);

        $sign = $this->createSign($this->params);

        if($this->checkSign && $_sign !== $sign){
            throw new \RuntimeException('签名值不匹配',2);
        }

        return true;
    }

    /**
     * 生成签名
     * @param $paramArr
     * @return string
     */
    public function createSign ($paramArr) {
        //@todo 需向ucenter获取
        $token = trim($this->token);
        $sign = $token;
        ksort($paramArr);
        foreach ($paramArr as $key => $val) {
            if ($key !='' && !is_null($val)) {
                $sign .= $key.$val;
            }
        }
        $sign .= $token;
        $sign = strtoupper(md5($sign));
        return $sign;
    }
}