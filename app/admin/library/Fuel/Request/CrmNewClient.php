<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2015/10/13/013
 * Time: 11:18
 */

namespace Fuel\Request;

use Framework\Log;
use \Framework\Mailer\MailSender;
use \Framework\Config;
use GuzzleHttp\Client;

class CrmNewClient
{
    protected $config = null;

    public function __construct()
    {
        $this->config = Config::get('crm_new');
    }

    protected function formatParams($data)
    {
        $config = $this->config;

        $data = \GuzzleHttp\json_encode($data);

        $timestamp = time();

        $sign = strtoupper(md5($config['app_secret'].$config['app_key'].$data.$timestamp.$config['app_secret']));

        $params = [
            'app_key'=>'foss',
            'data'=>$data,
            'sign'=>$sign,
            'timestamp'=>$timestamp,
        ];

        return $params;
    }
    /*
     * 执行
     */
    public function send(array $args)
    {
        \helper::argumentCheck(['method','data','path'], $args);
        $params = (array)$args;

        $config = $this->config;

        $httpRequest = new Client();

        $url = $config['apiUrl'].$params['path'];

        $sendParams = $this->formatParams($params['data']);

        $response = $httpRequest->request($params['method'], $url, [
            'form_params' => $sendParams
        ]);

        Log::error('发送url####'. \var_export($url, TRUE), [], 'crmNewClient');
        Log::error('发送数据####'.var_export($sendParams,true), [], 'crmNewClient');

        if($response->getStatusCode() != 200){
            throw new \RuntimeException($response->getBody()->getContents(), $response->getStatusCode());
        }

        try {
            $res = $response->getBody()->getContents();

            Log::error('返回---|'.var_export($res,TRUE), [], 'crmNewClient');
            $data = \GuzzleHttp\json_decode($res);
        }catch (\Exception $e){
            //Log::error('json_error---|'.strval($e), [], 'SubscribePushTaskJob');
            throw new \RuntimeException($e->getMessage(), 3);
        }

        if($data->status != 0){
            $msg = isset($data->message) ? $data->message : $data->msg;
            throw new \RuntimeException($msg,$data->code);
        }

        return $data->vo;
    }

    /**
     * 发送报警邮件
     * @param $error
     * @param string $title
     */
    private static function sendEmail($error, $title = '油品请求GSP异常报警')
    {
        if (Config::get('alarmEmailList.sendEmail') && Config::get('gas.errorAlarmEmail')) {
            MailSender::sendEmail($title, $error,Config::get('alarmEmailList.list'));
        }
    }
}