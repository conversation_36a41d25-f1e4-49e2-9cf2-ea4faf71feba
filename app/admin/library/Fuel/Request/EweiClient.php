<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2015/10/13/013
 * Time: 11:18
 */

namespace Fuel\Request;

use helper;
use \Framework\Request\Client as RequestClient;
use \Framework\Mailer\MailSender;
use \Framework\Config;
use \Fuel\Defines\AlarmMailList;
use Framework\Request\ApiClient;

class EweiClient
{
    /**
     * 接口获取操作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function get($args, callable $callback = NULL)
    {
        $record = self::exec('get', $args);

        return $callback != NULL ? $callback($record) : $record;
    }

    /**
     * 接口更新操作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function put($args, callable $callback = NULL)
    {
        $data = self::exec('put', $args);

        return $callback != NULL ? $callback($data) : $data;
    }

    /**
     * 接口新增操作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function post($args, callable $callback = NULL)
    {
        $data = self::exec('post', $args);

        return $callback != NULL ? $callback($data) : $data;
    }

    /**
     * 接口删除动作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function delete($args, callable $callback = NULL)
    {
        $data = self::exec('delete', $args);
        if (isset($data->code) && isset($data->data)) {
            $data = $data->data;
        }

        return $callback != NULL ? $callback($data) : $data;
    }


    /**
     * 执行
     * @param $type
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static protected function exec($type = NULL, $args, callable $callback = NULL)
    {

        $result = NULL;
        \helper::argumentCheck(['method', 'data'], $args);
        $params = (array)$args;

        $app_key = Config::get('ewei.app_key_gsp');
        $app_secret = Config::get('ewei.app_secret_gsp');

        if(isset($params['data_from']) && $params['data_from']){
            if($params['data_from'] == 1){
                $app_key = Config::get('ewei.app_key_gsp');
                $app_secret = Config::get('ewei.app_secret_gsp');
            }elseif($params['data_from'] == 2){
                $app_key = Config::get('ewei.app_key_web');
                $app_secret = Config::get('ewei.app_secret_web');
            }elseif($params['data_from'] == 4){
                $app_key = Config::get('ewei.app_key_wechat');
                $app_secret = Config::get('ewei.app_secret_wechat');
            }
        }

        $params['app_key'] = $app_key;
        $params['app_secret'] = $app_secret;

        \Framework\Log::dataLog('$params---'.var_export($params,true),'eWei');

        $_params = \Framework\SDK\Sign\Ewei::createSign($params);
        $header = [
            'Content-Type: application/json;charset=UTF-8',
            '_app_key:'.$_params['_app_key'],
            '_timestamp:'.$_params['_timestamp'],
            '_sign:'.$_params['_sign']
        ];
        unset($_params['_app_key'],$_params['_timestamp'],$_params['_sign'],$_params['app_secret']);

        try {
            $apiClient = new ApiClient();
            $result = $apiClient
                ->setAppKey($app_key)
                ->setAppSecret($app_secret)
                ->setApiUrl(Config::get('ewei.apiUrl').$params['method'].'.json')
                ->setPostByString(false)
                ->setMethod($params['method'])
                ->setRequestType(strtolower($type))
                ->setData(
                    function() use($_params){
                        return $_params;
                    }
                )
                ->setHeader($header)
                ->restFullSend();

            //接口code异常时
            if (!$result || !isset($result->status) || $result->status != 0) {
                \Framework\Log::error($params['method'], ['result'=>$result],'eweiClient');
                throw new \RuntimeException($result->result->error ? $result->result->error.'-'.$result->result->error_description : '操作异常请联系管理员'.var_export($result,true), $result->status);
            }else{
                \Framework\Log::info($params['method'], ['result'=>$result],'eweiClient');
            }

        } catch (\Exception $e) {
            if(strpos($e->getMessage(),'和现有客服相同') === FALSE){
                global $app;
                self::sendEmail('$params--'.var_export($params,true),'Env:'.$app->config->api_env.',CODE:'.$e->getCode()
                .'--'
                .$e->getMessage());
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }else{
                \Framework\Log::error('易维请求异常 | '.$e->getMessage(), ['exception'=>strval($e)],'eweiClient');
            }
        }

        return $callback != NULL ? $callback($result) : $result;
    }

    /**
     * 发送报警邮件
     * @param $error
     * @param string $title
     */
    private static function sendEmail($error, $title = '油品请求EWei异常报警')
    {
        if (Config::get('alarmEmailList.sendEmail') && Config::get('ewei.errorAlarmEmail')) {
            MailSender::sendEmail($title, $error.',SERVER_INFO:'.var_export($_SERVER,TRUE),Config::get('alarmEmailList.list'));
        }
    }
}