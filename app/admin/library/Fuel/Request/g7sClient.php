<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2015/10/13/013
 * Time: 11:18
 */

namespace Fuel\Request;

use Framework\Sms\NewSms;
use Framework\Sms\NoticeSender;
use helper;
use \Framework\Request\Client as RequestClient;
use \Framework\Mailer\MailSender as MailSender;
use \Framework\Config as Config;
use Framework\Request\ApiClient;
use \Framework\SDK\Sign\g7s;
use \Framework\Log;

class g7sClient
{
    /**
     * 接口获取操作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function get($args, callable $callback = NULL)
    {
        $record = self::exec('get', $args);

        return $callback != NULL ? $callback($record) : $record;
    }

    /**
     * 接口更新操作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function put($args, callable $callback = NULL)
    {
        $data = self::exec('put', $args);

        return $callback != NULL ? $callback($data) : $data;
    }

    /**
     * 接口新增操作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function post($args, callable $callback = NULL)
    {
        $data = self::exec('post', $args);

        return $callback != NULL ? $callback($data) : $data;
    }

    /**
     * 接口删除动作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function delete($args, callable $callback = NULL)
    {
        $data = self::exec('delete', $args);
        if(isset($data->code) && isset($data->data)){
            $data = $data->data;
        }

        return $callback != NULL ? $callback($data) : $data;
    }

    static private function exec($type = NULL, $args, callable $callback = NULL)
    {
        $params = (array)$args;
        $config = Config::get('g7s')[$params['apiPlatForm']];
        try {
            $apiClient = new ApiClient();

            $data = $apiClient
                ->setAppKey($config['app_key'])
                ->setAppSecret($config['app_secret'])
                ->setApiUrl($config['apiurl'])
                ->setMethod($params['method'])
                ->setRequestType($type)
                ->setData(
                    function() use($params){
                        $_params = g7s::createSign($params);
                        Log::info($params['method'], $_params,'g7sClient');
                        return $_params;
                    }
                )
                ->send();

            //接口code异常时
            if (!$data || !isset($data->code) || ($data->code != 0 && $data->code != 404)) {
                Log::error($params['method'], ['result'=>$data],'g7sClient');
                throw new \RuntimeException($data->msg ? $data->msg : '操作异常请联系管理员'.var_export($data,true), 2);
            }else{
                Log::info($params['method'], ['result'=>$data],'g7sClient');
            }
            return isset($data->data) ? $data->data : $data;
        } catch (\Exception $e) {
            Log::error($params['method'], ['result'=>strval($e)],'g7sClient');
            self::sendEmail('code--'.$e->getCode().strval($e));
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 发送报警邮件
     * @param $error
     * @param string $title
     */
    public static function sendEmail($error, $title = '油品代理Api异常')
    {
        if (Config::get('alarmEmailList.sendEmail') && Config::get('dsp.errorAlarmEmail')) {
            MailSender::sendEmail($title, $error,Config::get('alarmEmailList.alertList'));
        }
    }

    //请求用户中心
    static function requestUcenter($postData,$path = "organ/createSubOrgan")
    {
        $config = Config::get('uCenter')['VEGA_CONF'];
        $_time = explode(' ', microtime());
        $msectime = (float)sprintf('%.0f', (floatval($_time[1]) + floatval($_time[0])) * 1000);
        $postData['g7UCAppTimestamp'] = $msectime;
        $postData['g7UCAppKey'] = $config['appKey'];
        ksort($postData);
        $originStr = \helper::createLinkString($postData);
        $appSecret = $config['appSecret'];
        $paramsStr = $appSecret . $originStr . $appSecret;
        $postData['g7UCAppSign'] = strtoupper(md5($paramsStr));
        $postData['path'] = "/v1/ucenter/v2/".$path;
        $postData['is_form'] = 1;
        $result = NewSms::vegatHttpRequest($postData);
        if(!isset($result->data) || empty($result->data)){
            throw new \RuntimeException("用户中心，结果格式异常", 2);
        }
        return $result->data;
    }

    public function sendAlarmDingTalk($exception)
    {
//        if (Config::get('alarmEmailList.sendEmail') && !in_array($exception->getCode(), [2, 403])) {
//            $contentArr = explode(" ", strval($exception));
//            $alarmParams = [
//                'message'   =>  '标题：'.$exception->getMessage()."\n代码：".$exception->getCode()."\n描述：".(isset($contentArr[1]) ? $contentArr[1] : $contentArr[0])."\n错误文件：".$exception->getFile()."\n错误行：".$exception->getLine(),
//            ];
//            NoticeSender::sendAlarmDintTalk($alarmParams);
//        }
    }
}
