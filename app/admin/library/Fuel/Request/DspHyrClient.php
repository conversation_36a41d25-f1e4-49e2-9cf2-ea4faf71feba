<?php
namespace Fuel\Request;

use \Framework\Mailer\MailSender;
use \Framework\Config;
use Framework\Request\ApiClient;

class DspHyrClient
{
    /**
     * 接口获取操作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function get($args, callable $callback = NULL)
    {
        $record = self::exec('get', $args);

        return $callback != NULL ? $callback($record) : $record;
    }

    /**
     * 接口更新操作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function put($args, callable $callback = NULL)
    {
        $data = self::exec('put', $args);

        return $callback != NULL ? $callback($data) : $data;
    }

    /**
     * 接口新增操作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function post($args, callable $callback = NULL)
    {
        $data = self::exec('post', $args);

        return $callback != NULL ? $callback($data) : $data;
    }

    /**
     * 接口删除动作
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static public function delete($args, callable $callback = NULL)
    {
        $data = self::exec('delete', $args);
        if (isset($data->code) && isset($data->data)) {
            $data = $data->data;
        }

        return $callback != NULL ? $callback($data) : $data;
    }


    /**
     * 执行
     * @param $type
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    static protected function exec($type = NULL, $args, callable $callback = NULL)
    {
        \helper::argumentCheck(['method', 'data'], $args);
        $params = (array)$args;

        try {
            $apiClient = new ApiClient();
            $data = $apiClient
                ->setAppKey(Config::get('dsp_hyr.app_key'))
                ->setAppSecret(Config::get('dsp_hyr.app_secret'))
                ->setApiUrl(Config::get('dsp_hyr.apiUrl'))
                ->setPostByString(true)
                ->setMethod($params['method'])
                ->setData(
                    function() use($params){
                        $_params = \Framework\SDK\Sign\DspHyr::createSign($params);
                        \Framework\Log::info($params['method'], $_params,'DspHyrClient');

                        return $_params;
                    }
                )
                ->send();
            //接口code异常时
            if (!$data || !isset($data->code) || $data->code != 0) {
                \Framework\Log::error($params['method'], ['result'=>$data],'DspHyrClient');
                throw new \RuntimeException($data->msg ? $data->msg : '操作异常请联系管理员'.var_export($data,true), 2);
            }else{
                \Framework\Log::info($params['method'], ['result'=>$data],'DspHyrClient');
            }
            $result = isset($data->data) ? $data->data : $data;

            return $callback != NULL ? $callback($result) : $result;
        } catch (\Exception $e) {
            \Framework\Log::error($params['method'], ['result'=>strval($e)],'DspHyrClient');
            if(strpos($e->getMessage(), 'subscribe') != FALSE && isset($params['data']['openIds'])){

            }else{
                self::sendEmail(strval($e));
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }
        }
    }

    /**
     * 发送报警邮件
     * @param $error
     * @param string $title
     */
    private static function sendEmail($error, $title = '油品请求GSP异常报警')
    {
        if (Config::get('alarmEmailList.sendEmail') && Config::get('dsp_hyr.errorAlarmEmail')) {
            MailSender::sendEmail($title, $error,Config::get('alarmEmailList.list'));
        }
    }
}