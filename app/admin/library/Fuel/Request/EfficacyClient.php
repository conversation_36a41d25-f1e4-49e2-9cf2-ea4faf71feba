<?php

namespace Fuel\Request;

use Framework\Cache;
use Framework\Config;
use Framework\DingTalk\DingTalkAlarm;
use Framework\Log;
use GuzzleHttp\Exception\GuzzleException;
use RuntimeException;

class EfficacyClient
{
    private $config;
    private $jsonRequest = true;
    private $jsonRule    = 512;
    private $errorThrow  = true;

    public function __construct()
    {
        $this->config = Config::get('efficacy');
    }

    public function setJsonRule($jsonRule): EfficacyClient
    {
        $this->jsonRule = $jsonRule;
        return $this;
    }

    public function setErrorThrow($throw): EfficacyClient
    {
        $this->errorThrow = $throw;
        return $this;
    }

    public function auth()
    {
        if ($returnData = Cache::getInstance()->get("efficacy_auth_token")) {
            return $returnData;
        }
        $this->jsonRequest = false;
        $responseData = $this->request(
            'oauth/oauth/token',
            'post',
            $this->config['auth_config'],
            [],
            5,
            false
        );
        if (!isset($responseData['parseResponseData']['access_token']) ||
            !isset($responseData['parseResponseData']['token_type']) ||
            !isset($responseData['parseResponseData']['expires_in'])) {
            throw new RuntimeException("效能获取授权接口发生异常");
        }
        Cache::getInstance()->set(
            "efficacy_auth_token",
            $responseData['parseResponseData']['token_type'] . " " .
            $responseData['parseResponseData']['access_token']
        );
        Cache::getInstance()->expire("efficacy_auth_token", $responseData['parseResponseData']['expires_in'] - 1);
        return $responseData['parseResponseData']['token_type'] . " " .
               $responseData['parseResponseData']['access_token'];
    }

    /**
     * @param string $path 接口path
     * @param string $method 请求方式
     * @param mixed $data 请求数据
     * @param array $header 请求header头
     * @param int $timeout 请求超时时间
     * @param bool $checkAuth
     * @return array
     * @throws GuzzleException
     */
    public function request(
        string $path,
        string $method,
        $data = [],
        array $header = [],
        int $timeout = 10,
        bool $checkAuth = true
    ): array {
        Log::debug(
            '请求效能接口输入参数',
            $data,
            'efficacyClient'
        );
        $jsonRequest = $this->jsonRequest;
        $url = $this->config['domain'] . ($checkAuth ? $this->config['routePrefix'] : '') . $path;
        $ch = curl_init();
        switch (strtolower($method)) {
            case "get":
                $data = http_build_query($data);
                $url .= "?" . $data;
                break;
            default:
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper($method));
                if ($this->jsonRequest) {
                    $data = json_encode($data, $this->jsonRule);
                }
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }

        //如果json请求那么增加json头
        if ($jsonRequest) {
            if (empty(
            array_filter($header, function ($value) {
                return strpos(strtolower($value), 'content-type') !== false;
            })
            )) {
                $header[] = 'Content-Type:application/json;charset=utf-8';
            }
        }

        if (strpos($url, 'https://') !== false) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);  // 跳过证书检查
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);  // 从证书中检查SSL加密算法是否存在
        }

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, ceil($timeout / 3));
        curl_setopt($ch, CURLINFO_HEADER_OUT, true);
        $header[] = "Expect:";
        if ($checkAuth) {
            $header[] = "Authorization:" . $this->auth();
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        $output = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $requestHeaderInfo = curl_getinfo($ch, CURLINFO_HEADER_OUT);
        $responseHeaderInfo = substr($output, 0, $headerSize);
        $responseBody = strlen($output) > $headerSize ? substr($output, $headerSize) : $output;
        $curlError = curl_error($ch);
        curl_close($ch);

        $returnData = [
            'requestHeaderInfo'  => $requestHeaderInfo,
            'responseHeaderInfo' => $responseHeaderInfo,
            'responseRawData'    => $responseBody,
            'parseResponseData'  => json_decode($responseBody, true),
        ];
        $returnData['parseResponseData']['payload'] = json_decode(
                                                          $returnData['parseResponseData']['payload'] ?? '',
                                                          true
                                                      ) ?? [];
        Log::debug(
            '请求效能接口输出参数',
            $returnData,
            'efficacyClient'
        );
        if ($httpCode != 200 or !empty($curlError)) {
            Log::error('请求效能服务发生异常', [
                'curlError' => $curlError,
            ], 'efficacyClient');
            $content_err[] = "接口路径：" . $path;
            $content_err[] = "输入数据：" . $data;
            $content_err[] = "响应状态：" . $httpCode;
            $content_err[] = "时间：" . \helper::nowTime();
            $content_err[] = "输出数据：" . $responseBody;
            $content_err[] = "CURL错误：" . $curlError;
            (new DingTalkAlarm())->alarmToGroup('请求效能服务发生异常', implode("\n", $content_err), [],true, false);
            throw new RuntimeException("请求效能服务发生异常");
        }
        if ($checkAuth and ((!isset($returnData['parseResponseData']['status']) or
                             $returnData['parseResponseData']['status'] != 200) or
                            (isset($returnData['parseResponseData']['failed']) and
                             $returnData['parseResponseData']['failed']))) {
            $content_err = [];
            $content_err[] = "接口路径：" . $path;
            $content_err[] = "输入数据：" . $data;
            $content_err[] = "响应状态：" . $httpCode;
            $content_err[] = "时间：" . \helper::nowTime();
            $content_err[] = "输出数据：" . $responseBody;
            $content_err[] = "CURL错误：" . $curlError;
            (new DingTalkAlarm())->alarmToGroup('请求效能服务发生异常', implode("\n", $content_err), [],true, false);
            throw new RuntimeException("请求效能服务发生异常");
        }
        if (isset($returnData['parseResponseData']['payload']['code']) and
            ($returnData['parseResponseData']['payload']['code'] != 'K0000' and
             $returnData['parseResponseData']['payload']['code'] != 'E0000') and $this->errorThrow) {
            $content_err[] = "接口路径：" . $path;
            $content_err[] = "输入数据：" . $data;
            $content_err[] = "响应状态：" . $httpCode;
            $content_err[] = "时间：" . \helper::nowTime();
            $content_err[] = "输出数据：" . $responseBody;
            $content_err[] = "CURL错误：" . $curlError;
            (new DingTalkAlarm())->alarmToGroup('请求开灵服务发生异常', implode("\n", $content_err), [],true, false);
            throw new RuntimeException(
                $returnData['parseResponseData']['payload']['describe'] ??
                "请求开灵服务发生异常"
            );
        }
        return $returnData;
    }
}