/**
 * <?php echo $tbComment."\r\n"; ?>
 * Created by PhpStorm.
 * User: 小微事业部
 * Date: <?php echo $currentDate."\r\n"; ?>
 * Time: <?php echo $currentTime."\r\n"; ?>
 */
namespace Models;
use Illuminate\Database\Capsule\Manager as Capsule;

class <?php echo $modelName; ?> extends \Framework\Database\Model
{
    protected $table = '<?php echo $tbName; ?>';

    <?php echo $guardStr; ?>

    <?php echo $fillFields; ?>

    public function getFillAble()
    {
        return $this->fillable;
    }

    /**
     * 聚集查询
     * @param $query
     * @param $params
     * @return $query
     */
    public function scopeFilter($query, $params)
    {
        <?php echo $scopeFilterStr; ?>

        return $query;
    }

    /**
     * <?php echo $tbComment; ?> 列表查询
     * @param array $params
     * @return array
     */
    static public function getList(array $params)
    {
        $data = [];

        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;
        $sqlObj = <?php echo $modelName; ?>::Filter($params);
        if(isset($params['_export']) && $params['_export'] == 1){
            $data = $sqlObj->get();
        }else{
            $data = $sqlObj->orderBy('createtime', 'desc')->paginate($params['limit'],['*'],'page',$params['page']);
        }

        return $data;
    }

    /**
     * <?php echo $tbComment; ?> 详情查询
     * @param array $params
     * @return object
     */
    static public function getById(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return <?php echo $modelName; ?>::find($params['id']);
    }

    /**
    * 悲观锁查询
    * @param array $params
    * @return object
    */
    static public function getByIdLock(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return <?php echo $modelName; ?>::lockForUpdate()->where('id',$params['id'])->first();
    }

    /**
     * <?php echo $tbComment; ?> 新增
     * @param array $params
     * @return mixed
     */
    static public function add(array $params)
    {
        return <?php echo $modelName; ?>::create($params);
    }

    /**
     * <?php echo $tbComment; ?> 编辑
     * @param array $params
     * @return mixed
     */
    static public function edit(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        return <?php echo $modelName; ?>::find($params['id'])->update($params);
    }

    /**
     * <?php echo $tbComment; ?> 根据ids删除或批量删除
     * @param array $params
     * @return int
     */
    static public function remove(array $params)
    {
        \helper::argumentCheck(['ids'],$params);

        return <?php echo $modelName; ?>::destroy($params['ids']);
    }




}