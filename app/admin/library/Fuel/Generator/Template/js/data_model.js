/*********************************
 *  数据中心
 *********************************/

var controlName = '<?php echo $tbName; ?>';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            <?php
            foreach($fieldsInfo as $v){
                echo '"'.$v->COLUMN_NAME.'",';
            }
            ?>
        ]
    ),
});