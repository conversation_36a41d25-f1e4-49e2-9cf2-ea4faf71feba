<?php


namespace Fuel\Defines;


class OilAccountAdjustment
{
    const ACCOUNT_TYPE_OILSUPPLIER   = 1;
    const ACCOUNT_TYPE_AREA          = 2;
    const ACCOUNT_TYPE_MAINCARD      = 5;

    const ACCOUNT_GRADE_ONE      = 10;
    const ACCOUNT_GRADE_SECOND   = 20;


    const REVIEW_STATUS_UNREVIEW      = 1;
    const REVIEW_STATUS_REVIEWED      = 2;
    const REVIEW_STATUS_REJECT        = 3;

    const STATISTICAL_METHOD_OIl_CARD      = 10;
    const STATISTICAL_METHOD_KEEPACCOUNT   = 20;
    const STATISTICAL_METHOD_MIXED         = 30;

    const COOPERATION_TYPE_PLATFORM      = 10;
    const COOPERATION_TYPE_SITE          = 20;
    const COOPERATION_TYPE_ZK            = 30;

    const TRADE_DIFF = 1;
    const TRADE_JF = 2;
    const OTHER = 3;
    const DIRECT_REBATE_DIFF = 4;


    static public $account_type_list = [
        self::ACCOUNT_TYPE_OILSUPPLIER   => '油站供应商',
        self::ACCOUNT_TYPE_AREA          => '服务区',
        self::ACCOUNT_TYPE_MAINCARD      => '主卡',
    ];

    static public $account_grade_list = [
        self::ACCOUNT_GRADE_ONE      => '1级',
        self::ACCOUNT_GRADE_SECOND   => '2级',
    ];


    static public $review_status_list = [
        self::REVIEW_STATUS_UNREVIEW      => '待审核',
        self::REVIEW_STATUS_REVIEWED      => '已审核',
        self::REVIEW_STATUS_REJECT  => '已驳回',
    ];



    static public  $statistical_method_list = [
        self::STATISTICAL_METHOD_KEEPACCOUNT   => '记账',
        self::STATISTICAL_METHOD_OIl_CARD      => '油卡',
        self::STATISTICAL_METHOD_MIXED         => '混合',
    ];

    static public  $cooperation_type_list = [
        self::COOPERATION_TYPE_PLATFORM      => '平台',
        self::COOPERATION_TYPE_SITE          => '站点',
        self::COOPERATION_TYPE_ZK            => '主卡',
    ];

    static public $classify_type = [
        self::DIRECT_REBATE_DIFF => "直降返利差异",
        self::TRADE_DIFF => "消费流水差异",
        self::TRADE_JF => "积分消费",
        self::OTHER => "其他",
    ];


    static public function getAccountTypeById($id)
    {
        return isset(self::$account_type_list[$id]) ? self::$account_type_list[$id] : NULL;
    }

    static public function getAccountTypeAll()
    {
        return self::$account_type_list;
    }


    static public function getAccountGradeById($id)
    {
        return isset(self::$account_grade_list[$id]) ? self::$account_grade_list[$id] : NULL;
    }

    static public function getAccountGradeAll()
    {
        return self::$account_grade_list;
    }

    static public function getReviewStatusById($id)
    {
        return isset(self::$review_status_list[$id]) ? self::$review_status_list[$id] : NULL;
    }

    static public function getReviewStatusAll()
    {
        return self::$review_status_list;
    }

    static public function getCooperationTypeById($id)
    {
        return isset(self::$cooperation_type_list[$id]) ? self::$cooperation_type_list[$id] : NULL;
    }

    static public function getCooperationTypeAll()
    {
        return self::$cooperation_type_list;
    }

    static public function getStatisticalMethodById($id)
    {
        return isset(self::$statistical_method_list[$id]) ? self::$statistical_method_list[$id] : NULL;
    }

    static public function getStatisticalMethodAll()
    {
        return self::$statistical_method_list;
    }

    static public function getClassify($id = 0){
        return isset(self::$classify_type[$id]) ? self::$classify_type[$id] : "";
    }
}