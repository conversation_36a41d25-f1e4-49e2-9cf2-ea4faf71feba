<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class AccountMoneyTransferStatus
{
    static public $statusList = [
        '0' => ['id' => '-1', 'status' => '未审核'],
        '-1' => ['id' => '-1', 'status' => '已驳回'],
        '1'  => ['id' => '1', 'status' => '已审核'],
        '-2' => ['id' => '-2', 'status' => '已删除']
    ];

    static public $payStatusList = [
        '-1' => ['id' => '-1', 'status' => '未支付'],
        '0' => ['id' => '0', 'status' => '成功'],
        '1'  => ['id' => '1', 'status' => '失败'],
        '2' => ['id' => '2', 'status' => '处理中']
    ];

    static public function getById($id)
    {
        return isset(self::$statusList[$id]) ? self::$statusList[$id] : NULL;
    }

    static public function getPayStatusById($id)
    {
        return isset(self::$payStatusList[$id]) ? self::$payStatusList[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$statusList;
    }

    static public function getCompanyOrgMap()
    {
        if(in_array(API_ENV, ['pro', 'prod'])){
            return [
                7145 => '205C8M01',
            ];
        }else{ 
            return [
                1326 => '200NYE01', 
            ];
        }
    }
    static public function getTransferOrgMap()
    {
        if(in_array(API_ENV, ['pro', 'prod'])){
            return [
                '205C8M01' => [
                    '201XW32NKF'=>'205C8M0108',
                    '201XW32NN3'=>'205C8M0107',
                    '201XW32P5S'=>'205C8M0106',
                    '201XW32P5R'=>'205C8M0105',
                    '201XW32P5Q'=>'205C8M0104',
                    '201XW32P5P'=>'205C8M0103',
                    '201XW32P5O'=>'205C8M0102',
                    '201XW32P5N'=>'205C8M0101',
                ],
            ];
        }else{
            return [
               '200NYE01' =>  [
                    '201XW39001' => '200NYE0101',
                    '201XW39002' => '200NYE0102'
               ],
            ];
        }
    }
}