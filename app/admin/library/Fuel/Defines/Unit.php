<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2022/1/17
 * Time: 11:16 AM
 */

namespace Fuel\Defines;


class Unit
{
    CONST INVOICE_TON = 2;

    CONST INVOICE_L_KG = 1;

    CONST DIESEL_TON_2_L = 1176;

    CONST GASOLINE_TON_2_L = 1388;

    CONST NATURAL_GAS_TON_2_KG = 1000;

    static public function getLId()
    {
        if (API_ENV == 'dev' || API_ENV == 'test' ) {
            return 15;
        }

        return 28;
    }

    static public function getTonValue()
    {
        return '吨';
    }

    static public function getTonId()
    {
        if (API_ENV == 'dev' || API_ENV == 'test' ) {
            return 18;
        }

        return 31;
    }

    static public function getKgId()
    {
        if (API_ENV == 'dev' || API_ENV == 'test' ) {
            return 16;
        }

        return 29;
    }

    static public function transform($type, $src, $des, $num)
    {
        $res = 0;

        switch ($type) {
            case OilTypeBase::SEC_DIESEL:
                if ($src == self::getLId() && $des == self::getTonId()) {
                    $res =  bcdiv($num, self::DIESEL_TON_2_L, 10);
                }
                break;

            case OilTypeBase::SEC_GASQI://汽油类
            case OilTypeBase::SEC_GASOLINE:
            case OilTypeBase::SEC_METHANOL_GASOLINE:
            case OilTypeBase::SEC_ETHANOL_GASOLINE:
                if ($src == self::getLId() && $des == self::getTonId()) {
                    $res =  bcdiv($num, self::GASOLINE_TON_2_L, 10);
                }
                break;
        }

        if (in_array($type, [OilTypeBase::getTopNaturalGasId(), OilTypeBase::SEC_NATURAL_GAS, OilTypeBase::SEC_LIQUEFIED_NATURAL_GAS])) {
            if ($src == self::getKgId() && $des == self::getTonId()) {
                $res =  bcdiv($num, self::NATURAL_GAS_TON_2_KG, 10);
            }
        }

        $res = rtrim(sprintf('%0.6f', round($res, 6)), '0.');

        return $res;
    }

    static public function isTriggerLockTaxDisc($secondOilType, $type, $price)
    {
        $oil = [
            OilTypeBase::SEC_DIESEL,
            OilTypeBase::SEC_GASOLINE,
            OilTypeBase::SEC_METHANOL_GASOLINE,
            OilTypeBase::SEC_ETHANOL_GASOLINE,
        ];

        if (in_array($secondOilType, $oil) && $type == self::getTonId() && bccomp($price, 2500, 10) < 0) {
            return true;
        }

        return false;
    }
}