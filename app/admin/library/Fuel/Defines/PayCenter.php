<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class PayCenter
{
    const STATUS_UNAUDIT = 10;
    const STATUS_AUDITING = 15;
    const STATUS_REJECT = -10;
    const STATUS_PAYING = 20;
    const STATUS_PAY_FAIL = -20;
    const STATUS_PAY_SUCCESS = 30;
    const STATUS_ENTER_ACCOUNT = 50;
    const STATUS_REFUND = -30; // 退款
    static public $status = [
        self::STATUS_UNAUDIT => '待审核',
        self::STATUS_AUDITING => '审核中',
        self::STATUS_REJECT => '已驳回',
        self::STATUS_PAYING  => '支付中',
        self::STATUS_PAY_FAIL  => '支付失败',
        self::STATUS_PAY_SUCCESS  => '支付成功',
        self::STATUS_REFUND  => '已退款',
        self::STATUS_ENTER_ACCOUNT  => '已入账',
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }

    /**
     * get bind_status_id by bind name
     * @param $name
     * @return null
     */
    static public function getByIdByName($name)
    {
        $_list = array_flip(self::$list);
        return isset($_list[$name]) ? $_list[$name] : NULL;
    }
}