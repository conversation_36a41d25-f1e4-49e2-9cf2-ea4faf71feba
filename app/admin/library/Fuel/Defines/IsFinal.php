<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2022/1/11
 * Time: 12:05 AM
 */

namespace Fuel\Defines;


class IsFinal
{
    /**
     * 无需测算
     */
    CONST NO_NEED_CAL = 0;

    /**
     * 未测算
     */
    CONST NO_CAL = 100;

    /**
     * 已测算
     */
    CONST CAL = 111;

    /**
     * 定版
     */
    CONST STABLE = 200;

    public static function isStable($v)
    {
        return $v >= self::STABLE;
    }

    public static function isNoNeedCal($v)
    {
        return $v == self::NO_NEED_CAL;
    }

    public static function isDownRebateCalculated($v)
    {
        return ($v & 101) == 101;
    }

    public static function isLaterRebateCalculated($v)
    {
        return ($v & 110) == 110;
    }

    public static function isRebateCalculated($v)
    {
        return $v == self::CAL || $v == self::STABLE;
    }
}