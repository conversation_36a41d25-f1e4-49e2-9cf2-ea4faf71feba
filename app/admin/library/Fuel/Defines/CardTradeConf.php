<?php
/**
 *  CardTradeConf.php
 * $Author: 刘培俊 (liu<PERSON><PERSON><PERSON>@g7.com.cn) $
 * $Date: 2020/9/2 16:24 $
 * CreateBy Phpstorm
 */

namespace Fuel\Defines;

class CardTradeConf
{
    /**
     * 支付状态
     */
    const PAY_SUCCESS = 1;
    const PAY_FAIL    = 3;
    const PAY_ING     = 2;
    const PAY_CANCEL  = 4;

    static public $is_pay = [
        self::PAY_SUCCESS => '支付成功',
        self::PAY_FAIL    => '支付失败',
        self::PAY_ING     => '支付中',
        self::PAY_CANCEL  => '支付取消',
    ];

    /**
     * 扣款类型
     */
    const DEDUCT_TYPE_CARD_CASH       = 10;
    const DEDUCT_TYPE_CARD_CREDIT     = 20;
    const DEDUCT_TYPE_ORG_CASH        = 30;
    const DEDUCT_TYPE_ORG_CREDIT      = 40;
    const DEDUCT_TYPE_ORG_SELF_CREDIT = 50;

    static public $deduct_type = [
        self::DEDUCT_TYPE_CARD_CASH       => '卡现金支付',
        self::DEDUCT_TYPE_CARD_CREDIT     => '卡授信账户支付',
        self::DEDUCT_TYPE_ORG_CASH        => '机构现金账户支付',
        self::DEDUCT_TYPE_ORG_CREDIT      => '机构授信账户支付',
        self::DEDUCT_TYPE_ORG_SELF_CREDIT => '机构自授信账户支付',
    ];

    /**
     * 交易来源
     */
    const WECHAT_PAY_CODE = 101;
    const WECHAT_ON_LINE  = 102;
    const WECHAT_TICKET   = 103;
    const WMP_PAY_CODE    = 201;
    const WMP_ON_LINE     = 202;
    const WMP_TICKET      = 203;
    const H5_PAY_CODE     = 301;
    const H5_ON_LINE      = 302;
    const H5_TICKET       = 303;
    const GAS_PATCH       = 403;
    const GAS_PAD_PATCH   = 402;
    const GAS_EXCEPTION   = 401;
    #const GMS_PATCH = 503;
    const GMS_PAD_PATCH       = 502;
    const GMS_EXCEPTION       = 501;
    const AFTERWARDS_ADD      = 503;
    const CROSS_PLATFORM      = 601;
    const THIRD_DOWN_PAY_CODE = 701;
    const OPEN_API_LINE       = 801;

    static public $trade_from = [
        self::WECHAT_PAY_CODE     => 'WeChat-付款码',
        self::WECHAT_ON_LINE      => 'WeChat-主动付款',
        self::WECHAT_TICKET       => 'WeChat-小票机',
        self::WMP_PAY_CODE        => 'WMP-付款码',
        self::WMP_ON_LINE         => 'WMP-主动付款',
        self::WMP_TICKET          => 'WMP-小票机',
        self::H5_PAY_CODE         => 'H5-付款码',
        self::H5_ON_LINE          => 'H5-主动付款',
        self::H5_TICKET           => 'H5-小票机',
        self::GAS_EXCEPTION       => 'GAS-异常修改',
        self::GAS_PAD_PATCH       => 'GAS-PDA-补录',
        self::GAS_PATCH           => 'GAS-补录',
        #self::GMS_PATCH => 'GMS-补录',
        self::GMS_PAD_PATCH       => 'GMS-PDA-补录',
        self::GMS_EXCEPTION       => 'GMS-异常修改',
        self::AFTERWARDS_ADD      => '补录',
        self::CROSS_PLATFORM      => '跨平台',
        self::THIRD_DOWN_PAY_CODE => 'PDA扫下游付款码',
        self::OPEN_API_LINE       => '开放平台',
    ];

    /**
     * 是否可开票 - 可
     */
    const CAN_INVOICE_YES = 1;

    /**
     * 是否可开票 - 否
     */
    const CAN_INVOICE_NO = 2;

    const Trade_Sale_Need      = 10;
    const Trade_Sale_No_Need   = 20;
    const Trade_Sale_Exception = 30;
    static public $trade_for_sale = [
        self::Trade_Sale_Need      => '必须',
        self::Trade_Sale_No_Need   => '非必须',
        self::Trade_Sale_Exception => '异常',
    ];

    const Trade_Sale_Relation    = 30;
    const Trade_Sale_No_Relation = 20;
    const Trade_Sale_Normal      = 10;
    static public $trade_sale_relation = [
        self::Trade_Sale_No_Relation => '未关联',
        self::Trade_Sale_Relation    => '已关联',
        self::Trade_Sale_Normal      => '正常发票申请',
    ];

    const Trade_Sale_From_Add       = 10;
    const Trade_Sale_From_Refund    = 20;
    const Trade_Sale_From_Exception = 30;

    static public $trade_sale_from = [
        self::Trade_Sale_From_Add       => "补录",
        self::Trade_Sale_From_Refund    => "退款",
        self::Trade_Sale_From_Exception => "异常修改",
    ];

    const DOCUMENT_TYPE_CONSUMPTION_NORMAL               = 10;
    const DOCUMENT_TYPE_CONSUMPTION_SUPPLEMENTARY_RECORD = 20;
    const DOCUMENT_TYPE_CONSUMPTION_MODIFY               = 30;
    const DOCUMENT_TYPE_REFUND_NORMAL                    = 40;
    const DOCUMENT_TYPE_REFUND_MODIFY                    = 50;
    const DOCUMENT_TYPE_CASH_LOAD                        = 60;
    const DOCUMENT_TYPE_CASH_LOOP                        = 70;
    const DOCUMENT_TYPE_POINTS_LOAD                      = 80;
    const DOCUMENT_TYPE_RESERVE                          = 201;
    const DOCUMENT_TYPE_RESERVE_CANCEL                   = 202;
    const DOCUMENT_TYPE_DESC                             = [
        self::DOCUMENT_TYPE_CONSUMPTION_NORMAL               => '消费-正常',
        self::DOCUMENT_TYPE_CONSUMPTION_SUPPLEMENTARY_RECORD => '消费-补录',
        self::DOCUMENT_TYPE_CONSUMPTION_MODIFY               => '消费-修改',
        self::DOCUMENT_TYPE_REFUND_NORMAL                    => '退款-正常',
        self::DOCUMENT_TYPE_REFUND_MODIFY                    => '退款–修改',
        self::DOCUMENT_TYPE_CASH_LOAD                        => '现金-圈存',
        self::DOCUMENT_TYPE_CASH_LOOP                        => '现金-圈提',
        self::DOCUMENT_TYPE_POINTS_LOAD                      => '积分-圈存',
        self::DOCUMENT_TYPE_RESERVE                          => '消费-预扣',
        self::DOCUMENT_TYPE_RESERVE_CANCEL                   => '退款-预扣',
    ];

    const ELECTRON_TRADE_TYPE = "撬装加油";
    const ELECTRON_CANCEL_TRADE_TYPE = "撬装加油撤销";
    const RESERVE_TRADE_TYPE = "预约加油";
    const RESERVE_CANCEL_TRADE_TYPE = "预约撤销";

    const RESERVE_ORDER = 20;
    const TRADE_ORDER = 10;
}