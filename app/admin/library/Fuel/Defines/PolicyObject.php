<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class PolicyObject
{
    static public $list = [
        '1' => '主卡',
        '2'  => '副卡',
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }

    /**
     * get bind_status_id by bind name
     * @param $name
     * @return null
     */
    static public function getByIdByName($name)
    {
        $_list = array_flip(self::$list);
        return isset($_list[$name]) ? $_list[$name] : NULL;
    }
}