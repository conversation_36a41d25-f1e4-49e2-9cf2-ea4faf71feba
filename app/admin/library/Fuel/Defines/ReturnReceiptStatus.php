<?php
/**
 * 回票状态 （与航信一致）
*  ReturnReceiptStatus.php
* $Author: 刘培俊 (l<PERSON><PERSON><PERSON><PERSON>@g7.com.cn) $
* $Date: 2019/8/26 09:21 $
* CreateBy Phpstorm
*/

namespace Fuel\Defines;


class ReturnReceiptStatus
{
    const STATUS_NORMAL = 0; // 正常
    const STATUS_LOSS_CONTROL = 1; // 失控
    const STATUS_CANCEL = 2; // 作废
    const STATUS_CREDIT_NOTE = 3; // 红冲
    const STATUS_EXCEPTION = 4; // 异常

    static public $allList = [
        self::STATUS_NORMAL => '正常',
        self::STATUS_LOSS_CONTROL => '失控',
        self::STATUS_CANCEL => '作废',
        self::STATUS_CREDIT_NOTE => '红冲',
        self::STATUS_EXCEPTION => '异常',
    ];

    static public function getByKey($key)
    {
        return isset(self::$allList[$key]) ? self::$allList[$key] : null;
    }

    static public function getAll()
    {
        return self::$allList;
    }
}