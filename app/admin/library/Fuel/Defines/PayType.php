<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class PayType
{
    static public $list = [
        '10' => '对公转账',
        '20'  => 'POS转账-借记卡',
        '30'  => 'POS转账-贷记卡',
        '40'  => '微信支付',
        '50'  => '其他',
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }

    /**
     * get id by name
     * @param $name
     * @return null
     */
    static public function getByIdByName($name)
    {
        $_list = array_flip(self::$list);
        return isset($_list[$name]) ? $_list[$name] : NULL;
    }
}