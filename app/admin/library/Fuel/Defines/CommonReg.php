<?php
/**
* 通用正则表达式 CommonReg.php
* $Author: 刘培俊 (l<PERSON><PERSON><PERSON><PERSON>@g7.com.cn) $
* $Date: 2019/9/18 16:20 $
* CreateBy Phpstorm
*/

namespace Fuel\Defines;


class CommonReg
{
    const REG_TRUCK_NO = '/(^[\x{4e00}-\x{9fa5}]{1}[A-Z0-9]{6,9}$)|(^[A-Z]{2}[A-Z0-9]{2}[A-Z0-9\x{4e00}-\x{9fa5}]{1}[A-Z0-9]{4,5}$)|(^[\x{4e00}-\x{9fa5}]{1}[A-Z0-9]{5,8}[挂学警军港澳]{1}$)|(^[A-Z]{2}[0-9]{5,8}$)|(^(08|38){1}[A-Z0-9]{4,8}[A-Z0-9挂学警军港澳]{1}$)|(^[民]{1}[航]{1}([A-Za-z0-9]|[-s]){6,8}$)/u';
//    const REG_TRUCK_NO = '/^([\x{4e00}-\x{9fa5}]{1}[A-Z]{1}[A-Z_0-9]{4}(:?([A-Z_0-9]{1})|([\x{4e00}-\x{9fa5}]{1})))|民航[A-Z0-9]{5}|([\x{4e00}-\x{9fa5}]{2}([A-Za-z0-9]|[^\x{4e00}-\x{9fa5}\*\&\@\+\%\#\^\!]){6}$)/u';
    
    const REG_STATION_CODE = '/^[a-zA-Z0-9][a-zA-Z0-9_]{4,30}$/';
}