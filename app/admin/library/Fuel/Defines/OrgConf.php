<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;

class OrgConf
{
    /**
     * 机构配置类型
     */
    const CONFIG_TYPE_REBATE_MARK          = 10;
    const CONFIG_TYPE_CREDIT_USELIMIT      = 20;
    const CONFIG_TYPE_PAYCOMPANY           = 30;
    const CONFIG_TYPE_OPEN_CARD            = 40;
    const CONFIG_TYPE_CONSUME_WARNING      = 50;
    const CONFIG_TYPE_CLAIM                = 60;
    const CONFIG_TYPE_CARD_CONSUME_ACCOUNT = 70;
    const CONFIG_TYPE_PRE_OPEN_RECEIPT     = 80;
    const CONFIG_TYPE_TON_INVOICE_WHITE    = 90;
    const CONFIG_TYPE_CUSTOM_ORDER         = 101;
    const CONFIG_TYPE_CASH_WARNING         = 102;
    const TRUCK_NO_RECEIVE_TYPE            = 103;
    const CONFIG_TYPE_PAY_COMPANY_UNIQ         = 105;

    public static $config_type = [
        self::CONFIG_TYPE_REBATE_MARK          => '标用一体',
        self::CONFIG_TYPE_CREDIT_USELIMIT      => '授信使用总上限',
        self::CONFIG_TYPE_PAYCOMPANY           => '来款唯一挂靠',
        self::CONFIG_TYPE_OPEN_CARD            => '电子卡创建限制',
        self::CONFIG_TYPE_CONSUME_WARNING      => '授信余额提醒',
        self::CONFIG_TYPE_CLAIM                => '来款认领限制',
        self::CONFIG_TYPE_CARD_CONSUME_ACCOUNT => '卡扣款账户设置',
        self::CONFIG_TYPE_PRE_OPEN_RECEIPT     => '大客户先开后结',
        self::CONFIG_TYPE_TON_INVOICE_WHITE    => '吨票白名单',
        self::CONFIG_TYPE_CUSTOM_ORDER         => '客户后台下单',
        self::CONFIG_TYPE_CASH_WARNING         => '现金余额提醒',
        self::TRUCK_NO_RECEIVE_TYPE            => '车辆卡领卡方式',
        self::CONFIG_TYPE_PAY_COMPANY_UNIQ     => '付款公司白名单',
    ];

    /**
     * 返利标记百分比范围
     * @var array
     */
    public static $rebate_mark_percent = [
        'min' => '5',
        'max' => '10',
    ];

    /**
     * 适用范围
     */
    const SCOPE_OWN_AND_BELOW = 1;
    const SCOPE_OWN = 5;
    public static $scope = [
        self::SCOPE_OWN_AND_BELOW => '本级及以下',
        self::SCOPE_OWN           => '本级',
    ];

    /**
     * 启用状态
     */
    const STATUS_OPEN = 1;
    const STATUS_STOP = 2;
    public static $status = [
        self::STATUS_OPEN => '启用',
        self::STATUS_STOP => '禁用',
    ];

    protected static $testOrg = [
        1 => [
            'id' => 1,
            'name' => '油品测试',
            'code' => '200I1A'
        ],
        89 => [
            'id' => 89,
            'name' => 'G7-YP',
            'code' => '200IQP'
        ],
        94 => [
            'id' => 94,
            'name' => 'L林氏集团',
            'code' => '200IWX'
        ],
        107 => [
            'id' => 107,
            'name' => '张硕测试',
            'code' => '200021'
        ],
        663 => [
            'id' => 663,
            'name' => '北京汇通天下物联科技有限公司',
            'code' => '200000'
        ],
        670 => [
            'id' => 670,
            'name' => 'G7 smart',
            'code' => '200CGK'
        ],
        1612 => [
            'id' => 1612,
            'name' => '张硕测试',
            'code' => '200021'
        ],
        2110 => [
            'id' => 2110,
            'name' => '张硕测试',
            'code' => '200021'
        ],
        3260 => [
            'id' => 3260,
            'name' => '張碩測試',
            'code' => '201CGH'
        ],
        3265 => [
            'id' => 3265,
            'name' => '張氏集團',
            'code' => '201CGI'
        ],
        3768 => [
            'id' => 3768,
            'name' => 'G油品演示机构',
            'code' => '201GNE'
        ],
        4719 => [
            'id' => 4719,
            'name' => '山东中远油撬石油化工有限公司',
            'code' => '201JM6'
        ],
        5060 => [
            'id' => 5060,
            'name' => '春光石化（江苏优联可）',
            'code' => '201KMM'
        ],
        5157 => [
            'id' => 5157,
            'name' => '江苏优联可（重庆中石油）',
            'code' => '201LBN'
        ],
        6227 => [
            'id' => 6227,
            'name' => '主站圈回',
            'code' => '201Q4V'
        ],
        6683 => [
            'id' => 6683,
            'name' => '迁移专业户',
            'code' => '201SXZ'
        ],
        8087 => [
            'id' => 8087,
            'name' => '大连润途石油化工有限公司-导流',
            'code' => '201YWU'
        ],
        10043 => [
            'id' => 10043,
            'name' => '油品pro测试',
            'code' => '2025NI'
        ],
        10362 => [
            'id' => 10362,
            'name' => '林氏集团(北京)有限公司',
            'code' => '2026BD'
        ],
        10364 => [
            'id' => 10364,
            'name' => '林氏集团(天津)有限公司',
            'code' => '2026BV'
        ],
        10401 => [
            'id' => 10401,
            'name' => '林氏集团(南京)有限公司',
            'code' => '2026FO'
        ],
        10434 => [
            'id' => 10434,
            'name' => '林氏集团(无锡)有限公司',
            'code' => '2026JW'
        ],
        13119 => [
            'id' => 13119,
            'name' => 'G7小雪标准版测试机构',
            'code' => '202COT'
        ],
        15518 => [
            'id' => 15518,
            'name' => '中远石油化工（大连）有限公司',
            'code' => '202L1J'
        ]
    ];

    protected static $testOrgInTestEnv = [
        6513 => [
            'id' => 6513,
            'name' => '包氏集团10',
            'code' => '200NYE'
        ]
    ];

    public static function getTopOrgCodeAccordingConf($orgCode, $topOrgConf)
    {
        $top = substr($orgCode, 0, 6);
        if (isset($topOrgConf[$top])) {
            $top = substr($orgCode,0, $topOrgConf[$top]);
        }

        return $top;
    }

    public static function getTestOrgId()
    {
        if (API_ENV == 'dev' || API_ENV == 'test' ) {
            $res = self::$testOrgInTestEnv;
        } else {
            $res = self::$testOrg;
        }

        return array_keys($res);
    }

    public static function getTestOrgCode()
    {
        if (API_ENV == 'dev' || API_ENV == 'test' ) {
            $list = self::$testOrgInTestEnv;
        } else {
            $list = self::$testOrg;
        }

        $res = [];
        foreach ($list as $v) {
            $res[] = $v['code'];
        }

        return $res;
    }

    public static function getGrayLevelTestOrg($flag)
    {
        if (API_ENV == 'dev' || API_ENV == 'test' ) {

            $preOnline = 0;

            $org = [
                [
                    'id'        => 6517,
                    'orgcode'   => '200NYJ',
                    'org_name'  => '包氏集团14',
                ],
            ];
        } else {
            $org = [
            ];

            $preOnline = 0;
        }

        if (!$preOnline)
            return [];

        $res = [];

        foreach ($org as $v) {
            if ($flag == 'id') {

                $res[] = $v['id'];

            } elseif ($flag == 'code') {

                $res[] = $v['orgcode'];

            }
        }

        return $res;
    }
}