<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class AccountType
{
    const CASH = 10;
    const CREDIT = 20;
    const GAS = 30;
    const SCORE = 50;
    const NONE = 40;
    const CREDIT_FREE = 'G7_free';
    const CREDIT_RATE = 'G7_rate';
    const CREDIT_GLP = 'G7_glp';

    const ACCOUNT_CENTER_CASH = 'CASH';
    const ACCOUNT_CENTER_CASH_BACK = 'CASHBACK';
    const ACCOUNT_CENTER_CREDIT = 'CREDIT';
    const ACCOUNT_CENTER_CREDIT_GLP = 'CREDIT_GLP';
    const ACCOUNT_CENTER_CREDIT_ZBANK = 'CREDIT_ZBANK';

    const ACCOUNT_CREDIT_G7_FREE = 'G7免息';
    const ACCOUNT_CREDIT_G7_RATE = 'G7保理';
    const ACCOUNT_CREDIT_G7_GLP = '动力宝';


    const ACCOUNT_CENTER_POINT = 'POINT';

    static public $list = [
        self::CASH   => '现金账户',
        self::CREDIT => '授信账户',
        self::GAS    => '撬装账户',
        self::SCORE  => '积分账户',
        self::NONE   => '托管主卡账户',
    ];

    static public $newList = [
        self::CASH   => '现金账户',
        #self::CREDIT_FREE => self::ACCOUNT_CREDIT_G7_FREE,
        #self::CREDIT_RATE => self::ACCOUNT_CREDIT_G7_RATE,
        #self::CREDIT_GLP => self::ACCOUNT_CREDIT_G7_GLP,
        self::GAS    => '撬装账户',
        self::SCORE  => '积分账户',
        self::NONE   => '托管主卡账户',
    ];

    /**
     * 与账户中心映射
     * @var array
     */
    static public $accountCenterType = [
        self::CASH   => self::ACCOUNT_CENTER_CASH,
        self::CREDIT => self::ACCOUNT_CENTER_CREDIT,
        self::SCORE  => self::ACCOUNT_CENTER_POINT,
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$newList;
    }

    static public function creditTypeMap($id)
    {
        return isset(self::$newList[$id]) ? self::$newList[$id] : NULL;
    }

    /**
     * get bind_status_id by bind name
     * @param $name
     * @return null
     */
    static public function getByIdByName($name)
    {
        $_list = array_flip(self::$list);

        return isset($_list[$name]) ? $_list[$name] : NULL;
    }

    /**
     * @title  可以分配资金的账户类型
     * <AUTHOR>
     * @return array
     */
    static public function getAccountTypeForAssignMoney()
    {
        return [10, 20, 40];
    }

    /*******************根据帐号获取账户类型****************/
    static private $accountType = [
        '108' => '10',
        '208' => '20',
        '308' => '40',
        '106' => '50',
    ];

    static public function getAccountTypeByAccountNo($accountNo)
    {
        $accountNo = substr($accountNo, 0, 3);

        return isset(self::$accountType[$accountNo]) ? self::$accountType[$accountNo] : NULL;
    }
}