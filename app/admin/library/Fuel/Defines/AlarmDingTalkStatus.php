<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace Fuel\Defines;


class AlarmDingTalkStatus
{
    static public $list = [
        '1'    =>  '可用',
        '2'    =>  '禁用',
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }

    static public function getAll()
    {
        return self::$list;
    }
}