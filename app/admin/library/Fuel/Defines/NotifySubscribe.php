<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/5/25
 * Time: 上午11:26
 */

namespace Fuel\Defines;


class NotifySubscribe
{
    const CARD_APPLY = '10';
    
    const ACCOUNT_MONEY_CHARGE = '20';
    
    const ACCOUNT_ASSIGN = '30';
    
    const INCOME = '40';
    
    static public $list = [
        self::CARD_APPLY           => '开卡申请单',
        self::ACCOUNT_MONEY_CHARGE => '充值单',
        self::ACCOUNT_ASSIGN       => '分配单',
        self::INCOME               => '收款',
    ];
    
    static public function getAll()
    {
        return self::$list;
    }
    
    
    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }
}