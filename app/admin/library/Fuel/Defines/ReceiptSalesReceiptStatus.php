<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/5/25
 * Time: 上午11:26
 */

namespace Fuel\Defines;


class ReceiptSalesReceiptStatus
{
    const PRE_RECEIPT = 10;
    const RECEIPTED   = 20;
    const PRE_CANCEL  = 30;
    const CANCELED    = 40;
    
    static public $list = [
        self::PRE_RECEIPT => '待开',
        self::RECEIPTED   => '已开',
        self::PRE_CANCEL  => '待废',
        self::CANCELED    => '已废',
    ];

    const SUBMITTED_OPEN_RECEIPT = 1;
    const NOT_SUBMITTED_OPEN_RECEIPT = 2;

    static public $SUBMIT_OPEN_RECEIPT_DESC = [
        self::SUBMITTED_OPEN_RECEIPT => '是',
        self::NOT_SUBMITTED_OPEN_RECEIPT   => '否',
    ];

    static public function getAll()
    {
        return self::$list;
    }
    
    
    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }
}