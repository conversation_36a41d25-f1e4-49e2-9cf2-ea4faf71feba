<?php

namespace Fuel\Service;

use Framework\Job;
use Framework\Log;
use Fuel\Defines\BindStatus;
use Fuel\Defines\CardFrom;
use Fuel\Defines\OilCom;
use GosSDK\Defines\Methods\Tags;
use GosSDK\Gos;
use Kafka\Exception;
use Models\OilCardMain;
use Models\OilCardVice;
use Models\OilOrg;
use Models\OilTags;

class TagsToGos
{
    /**
     * @title 初始化数据到Gos
     * <AUTHOR>
     */
    static public function batchAddToGos()
    {
        $pageSize = 200;
        for($page=1; $page < 10000; $page++){
            $data = OilTags::orderBy('id', 'asc')
                ->offset(($page- 1) * $pageSize)
                ->limit($pageSize)
                ->get()->toArray();

            if (!$data) {
                echo 'no data';
                break;
            }

            try{
                (new Gos())
                    ->setMethod(Tags::CREATE_BATCH)
                    ->setParams($data)
                    ->sync();
            }catch(Exception $e){
                Log::info('batchAddToGos--'.$e->getCode() . '--' . $e->getMessage(), [], 'TagsToGos');
            }

        }
    }

    /**
     * @title 预处理数据
     * <AUTHOR>
     * @param $data
     * @return mixed
     */
    static private function handleData(&$data)
    {
        foreach($data as &$v){
            $v = self::handleOneData($v);
        }

        return $data;
    }

    static public function handleOneData($v)
    {
        $v['_oil_com'] = OilCom::getOilComById($v['oil_com']);
        $v['_card_from'] = CardFrom::getByIdFromG7s($v['card_from']);
        $orgInfo = OilOrg::getById(['id'=>$v['org_id']]);
        $v['orgcode'] = $orgInfo->orgcode;
        $v['org_name'] = $orgInfo->org_name;
        $v['_bind_status'] = BindStatus::getById($v['bind_status']);
        $cardMainInfo = OilCardMain::getInfoById(['id'=>$v['card_main_id']]);
        $v['fanli_region_code'] = isset($cardMainInfo->FanLiRegion) ? $cardMainInfo->FanLiRegion->code : '';
        $v['fanli_region_name'] = isset($cardMainInfo->FanLiRegion) ? $cardMainInfo->FanLiRegion->province : '';

        return $v;
    }

    /**
     * @title 修改副卡到Gos
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @param array $id
     */
    static public function updateToGos(array $id)
    {
        (new Job())
            ->setTaskName('修改副卡')
            ->pushTask(function () use ($id) {
                $data   = OilCardVice::getById($id)->toArray();
                //预处理
                $data = CardViceToGos::handleOneData($data);

                Log::info('$data--'.var_export($data,true), [], 'pushQueue');
                $result = (new Gos())
                    ->setMethod(Tags::UPDATE)
                    ->setParams($data)
                    ->async();
                Log::info('pushQueue-update', [$result], 'pushQueue');
            })
            ->channel('updateToGos')
            ->exec();
    }

    /**
     * @title push批量修改至Gos
     * <AUTHOR>
     * @param array $viceNos
     */
    static public function batchUpdateToGos(array $viceNos)
    {
        $data   = OilCardVice::getByViceNos($viceNos)->toArray();
        //预处理
        $data = self::handleData($data);

        Log::info('batchUpdateToGos--'.var_export($data,true), [], 'pushQueue');
        $result = (new Gos())
            ->setMethod(Tags::UPDATE_BATCH)
            ->setParams($data)
            ->async();
        Log::info('pushQueue-update', [$result], 'pushQueue');
    }

    /**
     * @title 根据油站code,获取油站返利档位
     * <AUTHOR>
     * @param array $station_codes
     */
    static public function getFanliLevel(array $params)
    {
        return (new Gos())
            ->setMethod('v1/station/getTags')
            ->setParams($params)
            ->sync();
    }
}