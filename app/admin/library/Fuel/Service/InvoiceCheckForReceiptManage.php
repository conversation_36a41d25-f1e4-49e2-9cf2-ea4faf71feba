<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/5/29
 * Time: 下午3:46
 */

namespace Fuel\Service;


use Framework\Excel\ExcelReader;
use Framework\Log;
use Fuel\Defines\OilType;
use Fuel\Defines\ReceiptManageStatus;
use Fuel\Defines\ReceiptSalesReceiptStatus;
use Fuel\Service\ReceiptTranslate\EngineFactory;
use function GuzzleHttp\Psr7\str;
use Kafka\Exception;
use Models\OilReceiptApply;
use Models\OilReceiptDetails;
use Models\OilReceiptDetailsTranslate;
use Models\OilReceiptManage;
use Models\OilReceiptSalesDetails;
use Illuminate\Database\Capsule\Manager as Capsule;
use \Fuel\Defines\ReceiptSalesCheckStatus;
use Models\OilReceiptSalesDetailsRelation;
use Models\OilTypeCategory;
use Symfony\Component\Translation\Translator;


class InvoiceCheckForReceiptManage
{
    
    static public $receiptTitleArr = [
        "发票代码"   => "receipt_code",
        "发票号码"   => "receipt_no",
        "购方企业名称"   => "receipt_title",
        "购方税号"   => "taxpayer_no",
        "银行账号" => "bank_account",
        "地址电话" => "addr_tel",
        "开票日期"   => "receipt_time",
        "商品编码版本号"=> "oil_vs",
        "单据号"   => "receipt_order",
        "商品名称" => "oil_type",
        "规格"   => "sku",
        "单位"   => "unit",
        "数量"   => "num",
        "单价"   => "unit_price",
        "金额"   => "amount",
        "税率"   => "tax_rate",
        "税额"   => "tax",
        "税收分类编码" => "order",
    ];
    /**
     * @title   金税发票导入
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @param $filePath
     * @return string
     * @returns
     * []
     * @returns
     */
    public function importInvoice($filePath)
    {
        Log::error('开始导入', [], 'importReceipt');
        
        $titleArr = self::$receiptTitleArr;
        
        $excelParams = [
            'filePath'     => $filePath,
            'fieldsMap'    => $titleArr,
            'fieldsRowNum' => 1,
            'ignore_all'   => 1//合并单元格也要
        ];
        $record = ExcelReader::read($excelParams,NULL,NULL,true);
        $data['total'] = count($record[0]);
        $data['successCount'] = 0;
        $data['errorCount'] = 0;
        if(!isset($record[0]) || count($record[0]) < 6){
            throw new \RuntimeException('请上传有效的数据', '20001');
        }
        $excel_data = $record[0];
        //$row = 2汇通天下石油化工（大连）有限公司发票数据
        //$row = 5发票类别：专用发票
        //$row = 7有数据
        $sellet_name_row = 2;
        $data_start_row = 5;
        $receipt_type_row = 5;
        $sellet_name_arr = array_get($excel_data,$sellet_name_row,[]);
        $sellet_name = array_get($sellet_name_arr,0,'');
        if(!$sellet_name || iconv_strlen($sellet_name,"UTF-8") >100){
            throw new \RuntimeException('格式错误！销售方填写有误，请检查～', '20001');
        }
        Log::error("sellet_name:-".$sellet_name,[],'Fuel\Service\InvoiceCheckForSales::importInvoice');
        $receipt_type = array_get($excel_data,$receipt_type_row,'');
        if(!$receipt_type[0] || iconv_strlen($receipt_type[0],"UTF-8") >50){
            throw new \RuntimeException('格式错误！发票类型填写有误，请检查～', '20001');
        }
        $receipt_type = stripos($receipt_type[0], '专用发票') === false ? 20 : 10;//专用发票
        Log::error("receipt_type:-".$receipt_type,[],'Fuel\Service\InvoiceCheckForSales::importInvoice');
        $import_data  = array_slice($excel_data,$data_start_row);
        Log::error("import_data:-".var_export($import_data,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');
        if (!$import_data) {
            throw new \RuntimeException('导入异常，请导入数据,请检查', '20001');
        }
        if (count($import_data)>1000) {
            throw new \RuntimeException('数据数量过大，仅支持1000条数据；', '20001');
        }
        $import_data_val = [];
        $sum_arr = [];//汇总类数据
        foreach ($import_data as $num => &$row) {
            if (stripos($row['tax_rate'], "%") === false && is_numeric($row['tax_rate'])) {
                $row['tax_rate'] = bcmul($row['tax_rate'],100,2).'%';
            }
            if($row['receipt_no'] && isset($import_data_val[$row['receipt_no'].$row['receipt_code']])){

                $doub_row = $import_data_val[$row['receipt_no'].$row['receipt_code']][0];
                $h1 = $doub_row['row_num']+7;
                $h  = $num+7;
                throw new \RuntimeException("数据重复！".$h1."行、".$h."行重复，请检查！", 290001);
            }
            $row['seller_name'] = $sellet_name;
            //发票类型
            $row['receipt_type'] = $receipt_type;
            //总计
            if(empty($row['receipt_code']) && empty($row['receipt_no']) && !empty($row['oil_type'])){
                if($row['oil_type'] != '小计'){
                    $value_map = ['receipt_code','receipt_no','receipt_title','taxpayer_no','bank_account','addr_tel','receipt_time','oil_vs','receipt_order'];
                    foreach ($value_map as $value){
                        $row[$value] = $import_data[$num-1][$value];
                    }
                }elseif($row['oil_type'] == '小计'){
                    $last_row = $import_data[$num-1];
                    $this->checkSum($row,$num,$import_data_val,$last_row);
                    $sum_arr[$import_data[$num-1]['receipt_no'].$import_data[$num-1]['receipt_code']] = ['amount'=>round($row['amount'],2),'tax'=>round($row['tax'],2)];
                    continue;
                }
            }
            //验证主表信息
            $this->checkSaleMain($row,$num+7);
            //商品信息判断
            $this->checkSaleDetail($row,$num+7);
            //税率
            $tax_rate = explode('%',$row['tax_rate']);
            $row['tax_rate'] = $tax_rate[0];
            $import_data_val[$row['receipt_no'].$row['receipt_code']][] = $row;
        }

        Log::error("import_data_val:----".var_export($import_data_val,true).'$sum_arr---'.var_export($sum_arr,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');

        $manage = $this->create($import_data_val,$sum_arr);

        Log::error("manage:----".var_export($manage,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');

        OilReceiptManage::whereIn('id',$manage['receipt_manage_id'])->update([
            'translate_status' => 2,
            'updatetime'=>\helper::now()
        ]);

        (new \Jobs\ImportReceiptTranslateJob(["receipt_manage_id"=>$manage['receipt_manage_id']]))
            ->setTaskName('销项票导入批量翻译')
            ->onQueue('receiptAutoTranslate')
            ->setTries(3)
            ->dispatch();

      //  $this->customTranslateJob($manage['receipt_manage_id']);
        $data['successCount'] = count($import_data);
        $data['total']        = count($import_data);
        return $data;
    }

    /**
     * Desc: 验证小计规则
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 20/7/22 下午6:22
     * @param $row
     * @param $num
     */
    public function checkSum($row,$num,$import_data_val,$last_row){
        //验证小计金额$import_data_val$sum_amount
        $num+=7;

        $sum_amount = 0;
        foreach (array_column($import_data_val[$last_row['receipt_no'].$last_row['receipt_code']],'amount') as $value){
            $sum_amount = bcadd($sum_amount,$value,2);
        }

        $sum_tax = 0;
        foreach (array_column($import_data_val[$last_row['receipt_no'].$last_row['receipt_code']],'tax') as $value){
            $sum_tax = bcadd($sum_tax,$value,2);
        }

        $bccabs_amount = abs(bcsub($sum_amount,$row['amount'],4));
        if(bccomp(0.5,$bccabs_amount,2)<0){
            throw new \RuntimeException("数据错误！号码".$last_row['receipt_no'].",代码".$last_row['receipt_code']."的发票，明细金额之和不等于销项票的合计金额，无法导入，请检查！sum_amount-".$sum_amount.'amount-'.$row['amount'], 290001);
        }

        if(count($import_data_val[$last_row['receipt_no'].$last_row['receipt_code']])>8){
            throw new \RuntimeException("数据错误！号码".$last_row['receipt_no'].",代码".$last_row['receipt_code']."的发票，同一张票的规格不可超过8种，无法导入，请检查！", 290001);

        }

        $bccabs_sum_tax = abs(bcsub($sum_tax,$row['tax'],4));
        if(bccomp(0.05,$bccabs_sum_tax,2)<0){
            throw new \RuntimeException("数据错误！号码".$last_row['receipt_no'].",代码".$last_row['receipt_code']."的发票，明细税额之和不等于销项票的合计税额，无法导入，请检查！sum_tax-".$sum_tax.'tax-'.$row['tax'], 290001);
        }

        //$preg_bankcard='/^(\-)?\d+(\.\d{1,2})?$/';|| !preg_match($preg_bankcard,$row['amount']) || !preg_match($preg_bankcard,$row['tax'])
        //preg_match
        if (!is_numeric($row['amount'])) {
            throw new \RuntimeException("格式错误！第". $num ."行的「小计行金额」填写有误，请检查！", 290001);
        }
        //preg_match
        if(!is_numeric($row['tax'])) {
            throw new \RuntimeException("格式错误！第". $num ."行的「小计行税额列」填写有误，请检查！", 290001);
        }
    }

    /**
     * Desc: 验证主表的数据
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 20/7/22 下午5:14
     * @param $row
     * @param $num
     */

    public function checkNum($receipt_code,$strlen){
       return strlen($receipt_code)>=1 && strlen($receipt_code)<=$strlen && preg_match('/^\d{1,21}$/',$receipt_code) && abs($receipt_code)==$receipt_code ? true : false;
    }

    public function checkSaleMain($row,$num){
        Log::error("receipt_code:---".var_export($row,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');
        //preg_match
        if (!$this->checkNum($row['receipt_code'],32)) {
            throw new \RuntimeException("格式错误！第". $num ."行的「发票代码」填写有误，请检查！", 290001);
        }
        if (!$this->checkNum($row['receipt_no'],32)){
            throw new \RuntimeException("第". $num ."行的「发票号码」填写有误，请检查！", 290001);
        }
        if(!(strlen($row['taxpayer_no'])>=1 && strlen($row['taxpayer_no'])<=20 && preg_match("/^[A-Za-z0-9]+$/" ,$row['taxpayer_no']))){
            throw new \RuntimeException("第". $num ."行的「购方税号」填写有误，请检查！", 290001);
        }
        if (!$row['receipt_title'] || iconv_strlen($row['receipt_title'],"UTF-8")>100) {
            throw new \RuntimeException("第". $num ."行的「购方名称」填写有误，请检查！", 290001);
        }
        if (!$row['bank_account'] || iconv_strlen($row['bank_account'],"UTF-8")>400) {
            throw new \RuntimeException("第". $num ."行的「购方银行账号」填写有误，请检查！", 290001);
        }
        if (!$row['addr_tel'] || iconv_strlen($row['addr_tel'],"UTF-8")>300) {
            throw new \RuntimeException("第". $num ."行的「购方地址」填写有误，请检查！", 290001);
        }
        if (!preg_match('/^[1-9]{1}[0-9]{0,3}\-[0-9]{1,2}\-[0-9]{1,2}$/',$row['receipt_time']) || $row['receipt_time']>date('Y-m-d')){
            throw new \RuntimeException("第". $num ."行的「时间」填写有误，请检查！", 290001);
        }
        //check
        $info = OilReceiptManage::where('receipt_code',$row['receipt_code'])->where('receipt_no',$row['receipt_no'])->first();
        if($info){
            throw new \RuntimeException("数据重复！第". $num ."行【发票代码】、【发票号码】数据库中已存在，不可重复添加，请检查", 290001);
        }
    }


    /**
     * Desc: 验证商品信息的数据
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 20/7/22 下午5:14
     * @param $row
     * @param $num
     */
    public function checkSaleDetail($row,$num){
        //商品
        if (!$row['oil_type'] || iconv_strlen($row['oil_type'],"UTF-8")>100) {
            throw new \RuntimeException("第". $num ."行的「商品名称」填写有误，请检查！", 290001); // 信息不完善
        }
        //规格型号
        if (iconv_strlen($row['sku'],"UTF-8")>40) {
            throw new \RuntimeException("第". $num ."行的「规格型号」填写有误，请检查！".iconv_strlen($row['oil_type'],"UTF-8").'=='.$row['oil_type'], 290001); // 信息不完善
        }
        //单位
        if ( iconv_strlen($row['unit'])>32) {
            throw new \RuntimeException("第". $num ."行的「单位」填写有误，请检查！", 290001); // 信息不完善
        }

        //金额=单价*数量 直接截取
        if($row['num'] && $row['unit_price']){
            //金额=单价*数量 直接截取
            $bcmul_amout  = bcmul($row['num'],$row['unit_price'],2);
            $round_amout  = sprintf("%.2f", bcmul($row['num'],$row['unit_price'],4));
            if(bccomp(0.5,abs(bcsub($round_amout,$row['amount'],4)),4)  < 0){
                throw new \RuntimeException("第". $num ."行的金额不等于单价乘数量，请检查！bcmul_amout--".$bcmul_amout."round_amout-".$round_amout."rowamount--".$row['amount'], 290001);
            }
            //差个小数点
            if (!(strlen($row['num'])>=1 && strlen($row['num'])<=21 && is_numeric($row['num']))) {
                throw new \RuntimeException("格式错误！第". $num ."行的「数量」填写有误，请检查！", 290001);
            }
            if (!(strlen($row['unit_price'])>=1 && strlen($row['unit_price'])<=21 && is_numeric($row['unit_price']))){
                throw new \RuntimeException("第". $num ."行的「单价」填写有误，请检查！", 290001);
            }
        }
        //小数点2位
        if (!(strlen($row['amount'])>=1 && strlen($row['amount'])<=21 && is_numeric($row['amount']) && preg_match('/^(\-)?\d+(\.\d{1,2})?$/',$row['amount']))){
            throw new \RuntimeException("第". $num ."行的「金额」填写有误，请检查！", 290001);
        }
        if (stripos($row['tax_rate'], "%") === false) {
            throw new \RuntimeException("第". $num ."行的「税率」填写有误，请检查！", 290001);
        }
        $tax_rate_arr = explode("%",$row['tax_rate']);
        if ($tax_rate_arr[0]>=100 || !empty($tax_rate_arr[1])) {
            throw new \RuntimeException("第". $num ."行的「税率」填写有误，请检查！", 290001);
        }
        //税额必须=金额*税率（四舍五入两位小数和直接截取前两位小数，都算正确）
       // $bcmul_tax  = bcmul(bcdiv($tax_rate_arr[0],100,2),$row['amount'],2);
        $round_tax = round(round(bcmul(bcdiv($tax_rate_arr[0],100,2),$row['amount'],4),3),2);
        if (bccomp(0.5,abs(bcsub($round_tax,$row['tax'],4)),4) < 0){
            throw new \RuntimeException("第". $num ."行的税额不等于金额乘税率，请检查！round_-".abs(bcsub($round_tax,$row['tax'],4))."rowtax--".$row['tax'].'对比'.bccomp(0.5,abs(bcsub($round_tax,$row['tax'],4)),4), 290001);
        }
        if (!(strlen($row['tax'])>=1 && strlen($row['tax'])<=21 && is_numeric($row['tax']) && preg_match('/^(\-)?\d+(\.\d{1,2})?$/',$row['amount']))){
            throw new \RuntimeException("第". $num ."行的「税额」填写有误，请检查！", 290001);
        }
    }


    /**
     * Desc: 插入数据-入库
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 20/7/22 下午5:43
     * @param $checkOldSalesInfo
     */
    public function create($manage_import_data,$sum_arr){
        global $app;
        $manage_id_list = [];
        $manage_detail_id_list = [];
        foreach ($manage_import_data as $key=>$manage_list){
            $manage_info = $manage_list[0];
            $sum_tax_rate_arr = array_column($manage_list,'tax_rate');
            rsort($sum_tax_rate_arr);
            $sum_tax_rate = count($manage_list)>1 ? $sum_tax_rate_arr[0] : $manage_info['tax_rate'];
            $tax = isset($sum_arr[$manage_info['receipt_no'].$manage_info['receipt_code']]['tax']) ? $sum_arr[$manage_info['receipt_no'].$manage_info['receipt_code']]['tax'] : $manage_info['tax'];
            $no_tax_amount = isset($sum_arr[$manage_info['receipt_no'].$manage_info['receipt_code']]['amount']) ? $sum_arr[$manage_info['receipt_no'].$manage_info['receipt_code']]['amount'] : $manage_info['amount'];

            $addArr = [
                'seller_name'    => $manage_info['seller_name'],
                'receipt_code'   => $manage_info['receipt_code'],
                'receipt_no'     => $manage_info['receipt_no'],
                'receipt_type'   => $manage_info['receipt_type'],
                'receipt_title'  => $manage_info['receipt_title'],
                'taxpayer_no'    => $manage_info['taxpayer_no'],
                'bank_account'   => $manage_info['bank_account'],
                'addr_tel'       => $manage_info['addr_tel'],
                'receipt_time'   => $manage_info['receipt_time'],
                'status'         => 10,//已开票
                'receipt_channel'=> 1,//线下
                'tax_rate'       => $sum_tax_rate,//取最大税率
                'tax_amount'     => $tax,
                'no_tax_amount'  => $no_tax_amount,
                'receipt_amount' => $tax+$no_tax_amount,
                'oil_type'       => $manage_info['oil_type'],
                'drawer'         => '张蒙',
                'payee'          => '洪玉洁',
                'checker'        => '王聪',
                'creator_id'     => $app->myAdmin->id,
                'creator_name'   => $app->myAdmin->true_name,
                'createtime'     => date("Y-m-d H:i:s"),
                'updatetime'     => date("Y-m-d H:i:s"),
                'document_type'  => 10,//这个的？？？
                'color_type'     => $tax >0 && $no_tax_amount >0 ? 20 : 10 //蓝||红票
            ];
            $res = OilReceiptManage::add($addArr);
            $manage_id_list[] = $res['id'];
            foreach ($manage_list as $detailInfo) {
                $createInfo = [
                    'receipt_manage_id' => $res['id'],
                    'tax_rate'          => bcdiv($detailInfo['tax_rate'],100,2),
                    'amount'            => $detailInfo['amount'],
                    'goods_name'        => $detailInfo['oil_type'],
                    'unit'              => $detailInfo['unit'],
                    'sku'               => $detailInfo['sku'],
                    'unit_price'        => $detailInfo['unit_price'],
                    'tax'               => $detailInfo['tax'],
                    'num'               => $detailInfo['num']
                ];
                Log::error("tax_rate:----".$detailInfo['tax_rate'].'tax_rate---'.bcdiv($detailInfo['tax_rate'],100,2),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');
                $detail_ret = OilReceiptDetails::add($createInfo);
                $manage_detail_id_list[] = $detail_ret['id'];
            }
        }
        return ['manage_detail_id_list'=>$manage_detail_id_list,'receipt_manage_id'=>$manage_id_list];
    }

    /**
     * Desc: 油品翻译
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 22/7/22 下午5:30
     * @param array $receipts
     * @param bool  $unitTranslate
     * @return array
     */
    public function customTranslateJob(array $ids){
        Log::error( 'customTranslateJob-接手参数～' . var_export($ids, TRUE), '', 'ImportReceiptTranslateJob_');

        if(empty($ids) || !$ids){
            Log::error('customTranslateJob异常-ids为空～' . var_export($ids, TRUE), '', 'ImportReceiptTranslateJob_');
            throw new \RuntimeException('customTranslateJob异常-ids为空～', 1);
        }
        foreach ($ids as $id){
            $id_info = OilReceiptManage::where('id',$id)->first();
            if(in_array($id_info->translate_status,[1,2])){
                try{

                    $this->customTranslate($id);

                }catch (\RuntimeException $e){
                    OilReceiptManage::where('id',$id)->update([
                        'translate_status' => 4,
                        'updatetime' => \helper::nowTime(),
                    ]);
                    throw new \RuntimeException($e->getMessage(), $e->getCode());
                }
            }else{
                Log::error( 'ImportReceiptTranslateJob翻译状态异常:无需处理～$id---' .$id.'id_info---:'.var_export($id_info, TRUE), '', 'ImportReceiptTranslateJob_');
            }
        }
    }



    /**
     * Desc: 油品翻译
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 22/7/22 下午5:30
     * @param array $receipts
     * @param bool  $unitTranslate
     * @return array
     */
    public function customTranslate($id){
        $insert_translateData=[];
        $receipts = OilReceiptDetails::where('receipt_manage_id',$id)->select('*','goods_name as name')->get()->toArray();
        Log::error("油品翻------receipts:-".var_export($receipts,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');
        $oil_type_arr = [];
        if(!empty($receipts)){
            try {
                //非能源引擎剔除
                $energyEngine= EngineFactory::createEngine('notEnergy_delete');
                $res_energyEngine=$energyEngine->handle($receipts);
                $res_energyEngine_not=$res_energyEngine['translate_not'];

                Log::error("油品翻译后结果------res_energyEngine_not:-".var_export($res_energyEngine,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');
                //油品翻译
                $receiptTranslateEngine = EngineFactory::createEngine('receipt_translate');
                $res_receiptTranslateEngine =$receiptTranslateEngine->handle($res_energyEngine['translated']);
                $res_receiptTranslateEngine_not=$res_receiptTranslateEngine['translate_not'];

                //翻译成功的
                $translate_result = $res_receiptTranslateEngine['translated'];
                Log::error("油品翻译成功------translate_result:-".var_export($translate_result,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');

                //翻译成功
                if(!empty($translate_result)) {
                    foreach ($receipts as $receipt) {
                        foreach ($translate_result as $key => $item) {
                            Log::error("油品翻item---item:------".var_export($item,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');
                            if ($item['id'] == $receipt['id']) {
                                foreach (['in_sku','name','in_second_oil'] as $key_value){
                                    if(!isset($item[$key_value]) || !$item[$key_value]){
                                        OilReceiptDetails::where('id',$receipt['id'])->update(['translate_status'=>4,'updatetime'=>\helper::nowTime()]);
                                        continue;
                                    }
                                }
                                $params = [
                                    'sku' => $item['in_sku'],
                                    'goods_name' =>$item['in_second_oil'],
                                    'updatetime' =>\helper::nowTime(),
                                    'translate_status'=>3,
                                ];
                                OilReceiptDetails::where('id',$receipt['id'])->update($params);
                                $insert_translateData[] = $receipt['id'];
                                $oil_type_arr[] = $item['in_second_oil'];
                            }
                        }
                    }
                    $oil_type_arr = array_unique($oil_type_arr);
                }

                //翻译失败的
                $translate_result_not=array_merge($res_energyEngine_not,$res_receiptTranslateEngine_not);
                Log::error("油品翻译后失败的------translate_result_not:-".var_export($translate_result_not,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');

                //翻译失败更新状态
                if(!empty($translate_result_not)) {
                    foreach ($receipts as $receipt) {
                        foreach ($translate_result as $key => $item) {
                            if ($item['id'] == $receipt['id']) {
                                $params = [
                                    'updatetime' =>\helper::nowTime(),
                                    'translate_status'=>4,
                                ];
                                OilReceiptDetails::where('id',$receipt['id'])->update($params);
                            }
                        }
                    }
                }
            }catch (\RuntimeException $e){
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }
        }
        //所有明细翻译成功==成功 否则==失败
        $status = count($insert_translateData) == count($receipts) ? 3 : 4;
        //二级商品翻译成即为成功
        if($status == 3){
            $status = empty($oil_type_arr) && count($oil_type_arr)>1 ? 4 : 3;
        }
        $update_params = [
            'translate_status' => $status,
            'updatetime' => \helper::nowTime(),
        ];
        //翻译成功 修改商品信息
        if($status == 3){
            $update_params['oil_type'] = $oil_type_arr[0];
        }

        Log::error("修改------id---".$id."update_params===:-".var_export($update_params,true),[],'Fuel\Service\InvoiceCheckForSales::importInvoice');

        OilReceiptManage::where('id',$id)->update($update_params);
        return $insert_translateData;
    }


    /**
     * Desc: 手动出库
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 27/7/22 上午9:32
     */
    public function receiptManageStock($params){
        $manage_query = OilReceiptManage::whereIn('id',$params['ids']);
        $manage_list  = $manage_query->get()->toArray();
        if(empty($manage_list) || !$manage_list){
            throw new \RuntimeException ('此数据不存在～', '1001');
        }
        global $app;
        foreach($manage_list as $value){
            (new \Jobs\InvoiceStockJob(["receipt_manage_id"=>$value['id'],'is_throw'=>true]))
                ->setTaskName('销项票批量出库存')
                ->setUserInfo($app->myAdmin)
                ->onQueue('receiptAutoTranslate')
                ->setTries(3)
                ->dispatch();
        }
        $manage_query->update([
            'out_status' => 2,
            'updatetime' => date("Y-m-d H:i:s"),
        ]);
    }


    /**
     * Desc: 手动删除
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 27/7/22 上午9:32
     */
    public function delReceiptManage($params){
        $manage_id = $params['ids'];
        Capsule::connection()->beginTransaction();
        try {
            // 发票信息
            $receiptInfo = OilReceiptManage::getById([
                'id' => $manage_id
            ]);
            if (!$receiptInfo) {
                throw new \RuntimeException("发票信息不存在：".$manage_id, 2);
            }
            if($receiptInfo->receipt_channel != 1 ){
                 throw new \RuntimeException("仅可删除「开票渠道」为「线下」的销项票数据!", 2);
            }
            if($receiptInfo->translate_status != 4 ){
                throw new \RuntimeException("仅可删除「翻译状态」为「失败」的线下导入的销项票数据，其他翻译状态均不可删除!".$manage_id, 2);
            }
            if($receiptInfo->out_status != 1 ){
                throw new \RuntimeException("仅可删除「出库状态」为：「未出库」线下导入的的销项票数据!", 2);
            }
            OilReceiptDetails::where(['receipt_manage_id'=>$manage_id])->delete();
            OilReceiptManage::where(['id'=>$manage_id])->delete();
            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            Log::error('删除异常：' . __METHOD__, [strval($e)], "invoiceLog_");
            throw new \RuntimeException('执行失败-' . $e->getMessage(), 2);
        }
    }
}