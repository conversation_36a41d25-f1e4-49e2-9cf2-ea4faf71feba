<?php
namespace Fuel\Service;

use Framework\Cache;
use Framework\Log;
use Fuel\Request\DspHyrClient;
use Fuel\Request\GspClient;
use Models\OilCardViceTrades;
use Models\OilStation;
use Illuminate\Database\Capsule\Manager as Capsule;

class FuelStation
{

    /******************************根据经纬度定位油站信息START*************************/

    /**
     * 油站位置信息解析
     * <AUTHOR>
     */
    static public function locateStation()
    {
//            self::updateOilStation(['id'=>124,'station_name'=>'观音堂西加油站','last_trade_id'=>1208014]);

        return true; //临时去掉
        $stationId = Cache::get('FuelStationlocateStationId');
        Log::info('$stationId--'.$stationId,[],'locateStation');

        $info = OilStation::getOilStationByLevel($stationId);
        if($info){
            Cache::put('FuelStationlocateStationId',$info->id,3600);
            self::updateOilStation(['id'=>$info->id,'station_name'=>$info->station_name,'last_trade_id'=>$info->last_trade_id]);
        }else{
            Cache::put('FuelStationlocateStationId',0,3600);
        }
        sleep(5);

        self::locateStation();
    }

    static private function updateOilStation($stationInfo)
    {
        $stationId = $stationInfo['id'];
        //1.获取机构号和车牌号
        $info = OilCardViceTrades::getNewRecordByStation($stationInfo);
        if($info){
            $level = 5;
            foreach ($info as $v){
                if($v['truck_no'] && $v['orgcode']){
                    //2.根据机构号和车牌号获取设备号
                    $data = GspClient::post([
                        'method' => 'gsp.api.getTruckinfoByno',
                        'data'   => [
                            ['orgcode'=>$v['orgcode'],'truckno'=>$v['truck_no'],'id'=>$v['id']]
                        ]
                    ]);
                    if($data){
                        //3.根据设备号获取停留点信息
                        $data = DspHyrClient::get([
                            'method' => 'huoyunren.gpsevent.getstopnew',
                            'data'   => [
                                'gpsno'=>$data[0]->gpsno,
                                'from'=>date('Y-m-d H:i:s',strtotime($v['trade_time'])-10*60),
                                'to'=>date('Y-m-d H:i:s',strtotime($v['trade_time'])+10*60),
                            ]
                        ]);
                        if($data->totalCount > 0){
                            //获取最接近交易时间的停留点
                            if(isset($data->result) && $data->result && isset($info->trade_time) && $info->trade_time){
                                $stopPoint = self::getMinStop($data->result,$info->trade_time);
                                if(isset($stopPoint['lng']) && $stopPoint['lat']){
                                    //经纬度转换
                                    $location = self::g2b($stopPoint['lng'],$stopPoint['lat']);
                                    if(isset($location['x']) && isset($location['y'])){
                                        //4.根据停留点经纬度获取加油站信息
                                        $oilStation = BdMap::getStationByLocation(['lng'=>$location['x'],'lat'=>$location['y']]);
                                        //5.更新油站位置信息
                                        if($oilStation->total > 0){
                                            if($oilStation->total == 1){
                                                $level = 2;
                                            }else if($oilStation->total == 2){
                                                $level = 3;
                                            }else if($oilStation->total > 2){
                                                $level = 4;
                                            }
                                            //匹配最近的加油站
                                            $updateArr = self::getMinDistanceStation($oilStation->results);
                                            $updateArr['locate_time'] = \helper::nowTime();
                                            $updateArr['level'] = $level;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                $updateArr['id'] = $stationId;
                $updateArr['last_trade_id'] = $v['id'];
                OilStation::edit($updateArr);

                if($level == 2){
                    break;
                }
            }
        }
    }

    /**
     * 经纬度转换：谷歌转百度
     * <AUTHOR>
     * @param $x
     * @param $y
     * @return array
     */
    static private function g2b($x,$y){
        $data = @file_get_contents("http://api.map.baidu.com/ag/coord/convert?from=2&to=4&x=".$x."&y=".$y);
        $array = json_decode($data,true);
        $arr = array();
        $arr["x"] = base64_decode($array["x"]);
        $arr["y"] = base64_decode($array["y"]);
        return $arr;
    }

    /**
     * 获取最接近交易时间的停留点
     * <AUTHOR>
     * @param $data
     * @param $tradeTime
     * @return mixed
     */
    static private function getMinStop($data,$tradeTime)
    {
        $timeDiff = [];//时间差
        foreach ($data as $k=>$v){
            $timeDiff[$k]['time_diff'] = abs($v->endTime/1000 - strtotime($tradeTime));
            $timeDiff[$k]['lat'] = $v->lat;
            $timeDiff[$k]['lng'] = $v->lng;
        }
        usort($timeDiff,function($a,$b){
            return $a['time_diff'] > $b['time_diff'] ? 1 : -1;
        });

        return $timeDiff[0];
    }

    /**
     * 获取距离最近的加油站
     * <AUTHOR>
     * @param $data
     * @return mixed
     */
    static private function getMinDistanceStation($data)
    {
        $oilStation = [];
        foreach ($data as $k=>$v){
            $oilStation[$k]['alias'] = $v->name;
            $oilStation[$k]['detail_address'] = $v->address;
            $oilStation[$k]['lat'] = $v->location->lat;
            $oilStation[$k]['lng'] = $v->location->lng;
            $oilStation[$k]['distance'] = $v->detail_info->distance;
        }

        usort($oilStation,function($a,$b){
            return $a['distance'] > $b['distance'] ? 1 : -1;
        });

        return $oilStation[0];
    }

    /******************************根据关键字定位油站信息START*************************/

    static public function locateStationByKeywords(array $data)
    {
        $stationNameArr = [];
        $idArr = [];
        foreach ($data as $v){
            $v = (array)$v;
            $stationNameArr[] = $v['station_name'];
            $idArr[] = $v['id'];
        }

        /********************
         * 开启事务
         *******************/
        Capsule::connection()->getPdo()->beginTransaction();
        try{
            Capsule::connection()->table('oil_station_kw')->whereIn('id',$idArr)->update(['is_used'=>1]);
            $params = ['station_name_in'=>$stationNameArr, 'level'=>5];
            $stationInfo = OilStation::getStationInfo($params);
            $updateArr = [];
            foreach ($data as $v){
                $v = (array)$v;
                if(isset($stationInfo[$v['station_name']])){
                    $keywords = self::getKeywordsByAlias($v['alias']);
                    $result = AMap::getStationByKeywords(['keywords'=>$keywords]);
//                var_dump($keywords,$result);die;
                    $tmp = [];
                    $tmp['where'] = 'id='.$stationInfo[$v['station_name']];
                    $tmp['alias'] = $v['alias'];

                    if($result->status == 1 && $result->pois){
                        $poiInfo = $result->pois[0];

                        $tmp['detail_address'] = $poiInfo->address;

                        $location = explode(',', $poiInfo->location);

                        $tmp['lng'] = $location[0];
                        $tmp['lat'] = $location[1];
                        $tmp['map_type'] = 10;//高德地图
                        $tmp['level'] = 10;//根据关键字查出的信息
                        if($poiInfo->tel){
                            $tmp['phone'] = $poiInfo->tel;
                        }

                        $updateArr[] = $tmp;
                    }
                }
            }
            OilStation::batchEditByPdo($updateArr);

            /********************
             * 提交事务
             *******************/
            Capsule::connection()->getPdo()->commit();
        }catch (\Exception $e){
            /********************
             * 回滚事务
             *******************/
            Capsule::connection()->getPdo()->rollBack();

            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    static private function getKeywordsByAlias($alias)
    {
        $arr = [
            '珠海斗门石油分公司',
            '周口中石化投资经营有限公司漯周界高速',
            '周口淮阳石油分公司',
            '重庆中石化惠通油料有限公司渝湘高速公路',
            '重庆中石化惠通油料有限公司渝湘高速',
            '重庆中石化惠通油料有限公司渝武高速公路',
            '重庆中石化惠通油料有限公司渝邻高速公路',
            '重庆中石化惠通油料有限公司沪渝高速公路',
            '重庆中石化惠通油料有限公司垫恩高速公路',
            '重庆中石化和光石油销售有限公司',
            '重庆中港能源有限公司',
            '重庆市中南石油有限责任公司',
            '中石化四川销售有限公司',
            '中石化森美（福建）石油有限公司',
            '中石化森美(福建)石油有限公司',
            '中石化安徽高速石化',
            '中国石油化工股份有限公司',
            '中国石化股份有限公司',
            '中国石化福建漳州平和石油分公司',
            '中国石化福建漳州南靖石油经营部',
            '中国石化福建漳州龙海石油分公司',
            '中国石化福建漳州华安石油分公司',
            '中国石化福建厦门同安石油分公司',
            '中国石化福建厦门石油分公司',
            '中国石化福建三明石油分公司',
            '江苏省石油集团锡山有限公司',
            '江苏宁沪高速公路股份有限公司',
            '江苏高速石油发展有限公司',
            '江苏高速公路石油发展有限公司',
            '河南中石化中原高速石油有限责任公司',
            '河南中石化高速公路有限责任公司',
            '(鄞西)中石化BP（浙江）石油有限公司',
            '(慈溪)中石化BP（浙江）石油有限公司',
            '(城区)中石化BP（浙江）石油有限公司',
        ];

        foreach ($arr as $v){
            $data = strpos($alias, $v);

            if($data !== false){
                $result = explode('重庆中石化惠通油料有限公司渝湘高速', $alias);

                return $result[1];
            }
        }

        return $alias;
    }
}