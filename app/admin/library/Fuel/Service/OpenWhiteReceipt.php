<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-4-13
 * Time: 下午12:29
 * desc:开票白名单客户，获取开票数据
 */

namespace Fuel\Service;

use Framework\Log;
use Fuel\Defines\OilCom;
use Fuel\Defines\OilType;
use Fuel\Defines\ReceiptApplyStatus;
use Models\OilAccountMoneyCharge;
use Models\OilCardViceTrades;
use Models\OilCreditAccount;
use Models\OilCreditRepay;
use Models\OilOrg;
use Models\OilPayCompany;
use Models\OilReceiptApply;
use Models\OilReceiptTitle;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilOperators;
use Models\OilOrgChangeOperatorLog;

class OpenWhiteReceipt
{
    /**
     * 获取机构可开额度
     */
    static function getOrgQuotaAmount($params = [])
    {
        Log::error('开票白名单客户，获取开票额度', [$params],'white_quota_');
        //校验机构号是否有效
        if (!isset($params['org_id']) || !$params['org_id']) {
            if (!isset($params['orgcode']) || !$params['orgcode']) {
                throw new \RuntimeException('机构缺失', 2);
            } else {
                $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
                if (!$orgInfo) {
                    throw new \RuntimeException('机构号' . $params['orgcode'] . '无效', 2);
                } else {
                    $params['org_id'] = $orgInfo->id;
                }
            }
        } else {
            $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
            $params['org_code'] = $orgInfo->orgcode;
            $params['org_name'] = $orgInfo->org_name;
            $params['orgcode'] = $orgInfo->orgcode;
        }
        \helper::argumentCheck(['receipt_title_id', 'org_id','org_operator_id','oil_type'], $params);

        $info = OilReceiptTitle::getById(['id' => $params['receipt_title_id']]);

        $params['receipt_type'] = $info->receipt_type;
        $params['pay_company_id'] = $info->pay_company_id;

        //油品种类
        $params['oil_type'] = $params['oil_type'] ? $params['oil_type'] : OilType::FUEL_YOU;
        $params['receipt_operator_id'] = $params['org_operator_id'];

        $change = \Fuel\Service\OrgChangeOperatorService::getOrgChangeData(['org_code'=>$orgInfo->orgcode]);
        if( count($change['changeList']) > 0 ){
            $isEndTime = "";
            foreach ($change['changeList'] as $_sub){
                //下游开票额度:1已释放;2已锁定
                if($_sub->down_receipt_status == 2){
                    $isEndTime = $_sub->change_time;
                    break;
                }
            }
            if(isset($params['trade_end_time']) && !empty($params['trade_end_time']) && !empty($isEndTime)){
                if( strtotime($params['trade_end_time']) > strtotime($isEndTime) ){
                    throw new \RuntimeException('消费截止日期不能大于预约换签时间', 2);
                }
            }
            $params['sign_lock_time'] = $isEndTime;
        }

        $conf = (new OrgConfigService())->getOrgRebateUseSetting($orgInfo->orgcode); // 标用一体
        $bigConf = (new OrgConfigService())->getBigCustomerSetting($orgInfo->orgcode); // 先开后结
        Log::error('开票白名单客户，获取开票额度-last:', [$params],'white_quota_');
        if (isset($conf['equal_use']) && $conf['equal_use'] == 1 && isset($bigConf['pre_open_receipt']) && $bigConf['pre_open_receipt'] == 1) {
            $maxAmount = self::getMaxReceiptAmountForYT($params);
        } else {
            $maxAmount = self::getUseChargeByCompany($params);
        }

        return $maxAmount['max_amount'];
        
    }

    /**
     * 获取发票申请金额
     */
    static function getReceiptApplyAmount($params = [])
    {
        $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
        if(!$orgInfo){
            throw new \RuntimeException('机构号'. $params['org_id']. '无效', 2);
        }
        //处理独立开票还是集中开票
        // if ($orgInfo->receipt_mode == 1) {
        //     $orgRoot = substr($orgInfo->orgcode, 0, 6);
        // } else {
        //     $orgRoot = $orgInfo->orgcode;
        // }
        $params['receiptOrgCode'] = $orgInfo->orgcode;
        $params['receipt_operator_id'] = $params['org_operator_id'];
        $params['is_recepit_nowtime'] = $orgInfo->is_recepit_nowtime;
        $title = OilReceiptTitle::getById(['id'=>$params['receipt_title_id']]);
        if(!$title){
            throw new \RuntimeException('发票抬头不存在', 2);
        }
        $params['pay_company_id'] = $title->pay_company_id;
        $params['trade_end_time'] = $params['end_time'];
        $params['trade_start_time'] = $params['begin_time'];

        unset($params['org_id']);
        unset($params['org_operator_id']);
        unset($params['receipt_title_id']);
        unset($params['end_time']);
        unset($params['begin_time']);
        $trades = self::calReceiptTotalTrades($params);
        if($params['white_flag'] == 1){
            return $trades->min_time;
        }
        return bcsub($trades['total_trade_money'] ,$trades['total_fanli'],2);
    }

    /**
     * 机构可开额度列表
     */
    static function getOrgQuotaList($params = [])
    {
        if ($params['receipt_mode'] == 2) { //独立核算
            $sqlObj = OilPayCompany::leftJoin('oil_org', 'oil_org.orgcode', '=', 'oil_pay_company.orgroot');
        } else {
            $sqlObj = OilPayCompany::leftJoin('oil_org', Capsule::Raw("LEFT(oil_org.orgcode,6)"), '=', 'oil_pay_company.orgroot');
        }
        $sqlObj = $sqlObj->where("oil_org.is_del", "=", 0)->where("oil_pay_company.status", "=", 1)
            ->leftJoin('oil_receipt_title', 'oil_pay_company.id', '=', 'oil_receipt_title.pay_company_id')
            ->select('oil_pay_company.charge_total_date', 'oil_org.orgcode as orgroot', 'oil_org.org_name',
                'oil_org.first_apply_receipt', 'oil_org.id as org_id', 'oil_receipt_title.id as receipt_title_id',
                'oil_receipt_title.corp_name', 'oil_receipt_title.receipt_type', 'oil_pay_company.id as pay_company_id',
                'oil_org.exclusive_custom', 'oil_org.is_recepit_nowtime', 'oil_org.is_test', 'oil_org.receipt_mode',
                'oil_pay_company.company_name', 'oil_pay_company.remark','oil_org.operators_id as operators_id',
                'oil_org.can_change_sign')
            ->whereNotNull('oil_org.id')
            ->where('oil_receipt_title.status', '=', 1)
            ->where('oil_receipt_title.is_del', '=', 0)
            ->whereNotNull('oil_pay_company.id');

        if (isset($params['exclusive_custom']) && $params['exclusive_custom']) {
            $sqlObj->where('oil_org.exclusive_custom', $params['exclusive_custom']);
        }

        if (isset($params['is_recepit_nowtime']) && $params['is_recepit_nowtime']) {
            $sqlObj->where('oil_org.is_recepit_nowtime', $params['is_recepit_nowtime']);
        }

        if (isset($params['is_have_corp']) && $params['is_have_corp'] == 1) {
            $sqlObj->whereNull('oil_receipt_title.orgroot');
        } elseif (isset($params['is_have_corp']) && $params['is_have_corp'] == 2) {
            $sqlObj->whereNotNull('oil_receipt_title.orgroot');
        }

        if (isset($params['is_first_receipt_apply']) && $params['is_first_receipt_apply'] == 1) {
            $sqlObj->whereNotNull('oil_org.first_apply_receipt');
        } elseif (isset($params['is_first_receipt_apply']) && $params['is_first_receipt_apply'] == 2) {
            $sqlObj->whereNull('oil_org.first_apply_receipt');
        }

        if (isset($params['pay_company_name']) && $params['pay_company_name']) {
            $sqlObj->where('oil_pay_company.company_name', 'like', '%' . $params['pay_company_name'] . '%');
        }

        if (isset($params['type']) && $params['type'] == 'credit') {
            $sqlObj->leftJoin('oil_credit_account', 'oil_credit_account.org_id', '=', 'oil_org.id')
                   ->whereNotNull('oil_credit_account.id');
        }

        if (isset($params['is_test']) && $params['is_test']) {
            $sqlObj->where('oil_org.is_test', $params['is_test']);
        }

        $sqlObj->groupBy('oil_pay_company.id', 'oil_receipt_title.id');

        if (!$params['org_id'] && !$params['pay_company_name']) {
            throw new \RuntimeException('请选择申请机构或付款公司', 2);
        }

        if ($params['receipt_mode'] == 1) {
            if (isset($params['org_id']) && $params['org_id']) {
                $sqlObj->where('oil_org.id', $params['org_id']);
            }
        } else {
            \helper::argumentCheck(['hiddenOrgCode'], $params);
            $sqlObj->where('oil_org.orgcode', '=', $params['hiddenOrgCode']);
        }

        if (isset($params['pay_company_id']) && $params['pay_company_id']) {
            $sqlObj->where('oil_pay_company.id', $params['pay_company_id']);
        }

        if (isset($params['count']) && $params['count'] == 1) {
            return $sqlObj->count();
        } elseif (isset($params['take']) && isset($params['skip'])) {
            if ($params['receipt_mode'] == 1) {
                if (isset($params['org_id']) && $params['org_id']) {
                    $sqlObj->where('oil_org.id', $params['org_id']);
                }
            } else {
                $sqlObj->where('oil_org.orgcode', '=', $params['hiddenOrgCode']);
            }

            $data = $sqlObj->offset(intval($params['skip']))->limit(intval($params['take']))->get()->toArray();
        }else{
            $data = $sqlObj->paginate($params['limit'], ['*'], 'page', $params['page'])->toArray();
        }

        $data = self::receiptNewFormat($params, $data);

        return $data;
    }

    static public function receiptNewFormat($params, $data)
    {
        $dataList        = [];
        $totalTradesList = [];
        if ($data) {
            if (isset($params['limit'])) {
                $params['page'] = intval($params['page']) > 0 ? intval($params['page']) : 1;
                $index          = 1 + ($params['page'] - 1) * $params['limit'];
            }

            $result      = isset($data['data']) && count($data['data']) ? $data['data'] : $data;

            $operatorMap = OilOperators::getIdMapName("company_name");
            foreach ($result as &$v) {
                if(!isset($v['pay_company_id']) || !$v['pay_company_id']){
                    continue;
                }
                $change = [];
                $params['receipt_operator_id'] = '';
                if( empty($params['operators_id']) ){
                    $params['receipt_operator_id'] = $v['operators_id'];
                }
                $isEndTime = "";
                //receipt_mode 开票方式 1:集中，2:独立
                //todo 机构上存在，能否换签的标签，如果客户不可换签，前端传的运营商不生效，直接只为空，维持原来的统计逻辑
                $changeData = OrgChangeOperatorService::getOrgChangeData( ['org_code'=>$v['orgroot']] );
                if(isset($changeData['changeList']) && count($changeData['changeList']) > 0){
                    foreach ($changeData['changeList'] as $_sub){
                        //下游开票额度:1已释放;2已锁定
                        if( $_sub->status == OilOrgChangeOperatorLog::SIGN_SUCCESS && $_sub->down_receipt_status == 1 ){
                            if( isset($params['operators_id']) && !empty($params['operators_id']) ){
                                $change[$_sub->orgroot][$params['operators_id']] = $params['operators_id'];
                            }else{
                                $change[$_sub->orgroot][$_sub->before_operator_id] = $_sub->before_operator_id;
                                $change[$_sub->orgroot][$_sub->after_operator_id] = $_sub->after_operator_id;
                            }
                        }
                    }
                }

                Log::error("change",[$change],"receipt_white_");

                $nowOrg = substr($v['orgroot'],0,6);

                Log::error("机构：".$nowOrg."，换签数据",[$change],"receipt_white_");

                $conf = (new \Fuel\Service\OrgConfigService())->getOrgRebateUseSetting($v['orgroot']); // 标用一体
                $bigConf = (new \Fuel\Service\OrgConfigService())->getBigCustomerSetting($v['orgroot']); // 先开后结
                if(isset($conf['equal_use']) && $conf['equal_use'] == 1 && isset($bigConf['pre_open_receipt']) && $bigConf['pre_open_receipt'] == 1 ){
                    if(is_array($change) && count($change) > 0 ) {
                        $changeDetail = isset($change[$nowOrg]) ? $change[$nowOrg] : [];
                        foreach (array_keys($changeDetail) as $_v) {
                            $params['receipt_operator_id'] = $_v;
                            if( !empty($isEndTime) ) {
                                $params['_createtimeLe'] =$isEndTime;
                            }
                            $_quota = self::getReceiptQuotaYt($v,$params);
                            foreach ($_quota as $_val) {
                                $_val['operator_name'] = isset($operatorMap[$_v]) ? $operatorMap[$_v] : "";
                                $_val['receipt_operator_id'] = $_v;
                                $dataList[] = $_val;
                            }
                        }
                    } else {
                        if(isset($params['operators_id']) && !empty($params['operators_id']) && $params['operators_id'] != $v['operators_id']){
                            $dataList = [];
                            break;
                        }
                        $_quota = self::getReceiptQuotaYt($v, $params);
                        foreach ($_quota as $_val) {
                            $_val['operator_name'] = isset($operatorMap[$v['operators_id']]) ? $operatorMap[$v['operators_id']] : "";
                            $_val['receipt_operator_id'] = $v['operators_id'];
                            $dataList[] = $_val;
                        }
                    }

                }else{
                    $oneParams = [
                        'org_id'         => $params['org_id'],
                        'receipt_type'   => $v['receipt_type'],
                        'pay_company_id' => $v['pay_company_id'],
                        'receipt_operator_id' => $params['receipt_operator_id'],
                    ];
                    if (isset($params['_createtimeLe']) && $params['_createtimeLe']) {
                        $oneParams['_createtimeLe'] = $params['_createtimeLe'];
                    }

                    if( !empty($isEndTime) ){
                        $oneParams['sign_lock_time'] = $isEndTime;
                    }

                    if(is_array($change) && count($change) >= 1){
                        $changeDetail = isset($change[$nowOrg]) ? $change[$nowOrg] : [];
                        foreach (array_keys($changeDetail) as $_v){
                            $oneParams['receipt_operator_id'] = $_v;

                            $_quotaMap = self::getReceiptQuota($v,$oneParams,$totalTradesList,$index);
                            $_tmpQuota = $_quotaMap['receipt'];
                            $totalTradesList = $_quotaMap['trade'];
                            foreach ($_tmpQuota as $_val){
                                $_val['operator_name'] = isset($operatorMap[$_v]) ? $operatorMap[$_v] : "";
                                $_val['receipt_operator_id'] = $_v;
                                $dataList[] = $_val;
                            }
                        }
                    }else{

                        if(isset($params['operators_id']) && !empty($params['operators_id']) && $params['operators_id'] != $v['operators_id']){
                            $dataList = [];
                            break;
                        }

                        $_quotaMap = self::getReceiptQuota($v,$oneParams,$totalTradesList,$index);
                        $_tmpQuota = $_quotaMap['receipt'];
                        $totalTradesList = $_quotaMap['trade'];
                        foreach ($_tmpQuota as $_val){
                            $_val['operator_name'] = isset($operatorMap[$v['operators_id']]) ? $operatorMap[$v['operators_id']] : "";
                            $_val['receipt_operator_id'] = $v['operators_id'];
                            $dataList[] = $_val;
                        }
                    }

                }

                Log::error('$dataList', [$dataList], 'receipt_white_');
            }

            if (isset($data['data']) && count($data['data'])) {
                $data['data']  = $dataList;
                $data['total'] = count($dataList);
            } else {
                $data = $dataList;
            }
        }
        return $data;
    }


    static public function getReceiptQuota($v = [],$oneParams = [],$totalTradesList = [],$index = 1)
    {
        $amountInfo = self::getUseChargeByCompany($oneParams); //todo

        $v['total_receipt']                                                = $amountInfo['total_receipt'];
        $v['total_receipt_frozen']                                         = $amountInfo['total_receipt_frozen'];
        $v['total_charge_add_repayed_subtraction_receipted_receiptFrozen'] = $amountInfo['total_charge_add_repayed_subtraction_receipted_receiptFrozen'];

        Log::error('amountInfo', [$amountInfo], 'receipt_white_');
        $v['total_charge']                 = $amountInfo['total_charge'];
        $v['total_repaid']                 = $amountInfo['total_repaid'];
        $v['total_charge_addition_repaid'] = $amountInfo['total_charge_addition_repaid'];

        $v['tatal_charge_subtraction_receipted'] = $amountInfo['tatal_charge_subtraction_receipted'];

        $v['total_frozen']                    = $amountInfo['total_frozen'];
        $v['total_trades_subtraction_frozen'] = $amountInfo['total_trades_subtraction_frozen'];
        $v['total_fanli_charge']              = $amountInfo['total_fanli_charge'];
        $v['total_fanli_transfer_into']       = $amountInfo['total_fanli_transfer_into'];
        $v['total_fanli_transfer_out']        = $amountInfo['total_fanli_transfer_out'];
        $v['total_fanli']                     = $amountInfo['total_fanli'];

        $v['total_no_receipt_trade']     = $amountInfo['total_no_receipt_trade'];
        $v['total_no_receipt_fanli']     = $amountInfo['total_no_receipt_fanli'];
        $v['total_no_receipt_use_fanli'] = $amountInfo['total_no_receipt_use_fanli'];
        $v['total_fanli_calculate']      = $amountInfo['total_fanli_calculate'];

        $v['is_have_corp']           = $v['corp_name'] ? '有' : '无';
        $v['_exclusive_custom']      = \Fuel\Defines\ExclusiveCustom::getById($v['exclusive_custom']);
        $v['_is_recepit_nowtime']    = $v['is_recepit_nowtime'] == 2 ? '是' : '否';
        $v['is_first_receipt_apply'] = $v['first_apply_receipt'] ? '否' : '是';
        $v['_is_test']               = $v['is_test'] == 1 ? '否' : '是';
        if (!isset($v['corp_name']) || !$v['corp_name']) {
            $v['corp_name']    = '未维护';
            $v['receipt_type'] = '未维护';
        }
        $amountInfo['params']['tradesList'] = $totalTradesList;
        $list  = self::getUseChargeByCompanyAll($amountInfo, $amountInfo['params']);
        foreach ($list as $oilType => $item) {
            $totalTradesList[$oneParams['receipt_operator_id']][$oilType] = $item['total_trades'];
            if (isset($params['limit'])) {
                $v['id'] = $index;
                $index++;
            }

            $v['max_amount']               = $item['amount'];
            $v['oil_classify']             = $item['name'];
            $v['total_receipt_trades']     = $item['total_receipt_trades'];
            $v['total_trades']             = $item['total_trades'];
            $v['total_use_fanli']          = $item['total_use_fanli'];
            $v['total_org_receipt']        = $item['total_org_receipt'];
            $v['total_org_receipt_frozen'] = $item['total_org_receipt_frozen'];
            if ($oilType != OilType::FUEL_YOU) {
                $v['total_fanli_charge']    = "0.00";
                $v['total_fanli_calculate'] = "0.00";
                $v['total_fanli']           = "0.00";
                $v['total_fanli_transfer_into'] = "0.00";
                $v['total_fanli_transfer_out'] = "0.00";
            }
            $dataList[] = $v;
        }
        return ['receipt'=>$dataList,'trade'=>$totalTradesList];
    }

    static public function getReceiptQuotaYt($v = [],$params = [])
    {
        $oilTypeList = OilType::getReceiptList();
        foreach ($oilTypeList as $oilTypeNo => $oilTypeName) {
            $oneParams = [
                'org_id'         => $params['org_id'],
                'receipt_type'   => $v['receipt_type'],
                'pay_company_id' => $v['pay_company_id'],
                'receipt_operator_id' => isset($params['receipt_operator_id']) && $params['receipt_operator_id'] ? $params['receipt_operator_id'] : '',
                'is_direct_dl'    => $params['is_direct_dl'],
            ];
            if(isset($params['snapshot']) && $params['snapshot'] == 'on'){
                $oneParams['snapshot'] = $params['snapshot'];
            }
            //$oneParams['trade_end_time'] = !empty($params['_createtimeLe']) ? $params['_createtimeLe'] : \helper::nowTime();
            $oneParams['trade_end_time'] = date('Y-m-d 00:00:00');
            $oneParams['oil_type'] = $oilTypeNo;
            $amountInfo = self::getMaxReceiptAmountForYT($oneParams);

            $amountInfo['is_have_corp']           = $v['corp_name'] ? '有' : '无';
            $amountInfo['_exclusive_custom']      = \Fuel\Defines\ExclusiveCustom::getById($v['exclusive_custom']);
            $amountInfo['_is_recepit_nowtime']    = $v['is_recepit_nowtime'] == 2 ? '是' : '否';
            $amountInfo['is_first_receipt_apply'] = $v['first_apply_receipt'] ? '否' : '是';
            $amountInfo['_is_test']               = $v['is_test'] == 1 ? '否' : '是';
            if (!isset($v['corp_name']) || !$v['corp_name']) {
                $amountInfo['corp_name']    = '未维护';
                $amountInfo['receipt_type'] = '未维护';
            }
            $amountInfo['oil_classify']             = $oilTypeName;

            if ($oilTypeNo != OilType::FUEL_YOU) {
                $amountInfo['total_fanli_charge']    = "0.00";
                $amountInfo['total_fanli_calculate'] = "0.00";
                $amountInfo['total_fanli']           = "0.00";
                $amountInfo['total_fanli_transfer_into'] = "0.00";
                $amountInfo['total_fanli_transfer_out'] = "0.00";
            }

            $amountInfo['charge_total_date'] = $v['charge_total_date'];
            $amountInfo['company_name'] = $v['company_name'];
            $amountInfo['corp_name'] = $v['corp_name'];
            $amountInfo['exclusive_custom'] = $v['exclusive_custom'];
            $amountInfo['first_apply_receipt'] = $v['first_apply_receipt'];
            $amountInfo['is_recepit_nowtime'] = $v['is_recepit_nowtime'];
            $amountInfo['org_name'] = $v['org_name'];
            $amountInfo['orgroot'] = $v['orgroot'];
            $amountInfo['receipt_type'] = $v['receipt_type'];
            $amountInfo['pay_company_id'] = $v['pay_company_id'];

            $dataList[] = $amountInfo;
        }
        return $dataList;
    }

    static public function getUseChargeByCompany($params)
    {
        $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
        if (!$orgInfo) {
            throw new \RuntimeException('机构信息不存在', 2);
        }
        //处理独立开票还是集中开票
        // if ($orgInfo->receipt_mode == 1) {
        //     $orgRoot = substr($orgInfo->orgcode, 0, 6);
        // } else {
        //     $orgRoot = $orgInfo->orgcode;
        // }
        $orgRoot = $orgInfo->orgcode;

        $params['receipt_mode'] = $orgInfo->receipt_mode;
        $params['receiptOrgCode'] = $orgRoot;
        $params['is_recepit_nowtime'] = $orgInfo->is_recepit_nowtime;
        $contentArr['params'] = $params;
        //求pay_company_id下指定orgIdList充值之和
        $companyInfo = OilPayCompany::getById(['id' => $params['pay_company_id']]);
        Log::error('付款公司--|' . var_export($companyInfo->company_name, TRUE), [$params], 'receipt_white_');

        $successParams = [
            ReceiptApplyStatus::SUCCESS,
            ReceiptApplyStatus::MAILED,
        ];

        $forzenParams = [
            ReceiptApplyStatus::HANDLING,
            ReceiptApplyStatus::PRE_AUDIT,
            ReceiptApplyStatus::AUDITED,
            ReceiptApplyStatus::OPENING
        ];

        //累计充值
        $params['company_name'] = $companyInfo->company_name;

        //针对G7授信，需要取得还款作为充值
        $creditInfo = OilCreditAccount::getByOrgId(['org_id' => $params['org_id']]);

        $repayCreditSum = 0;
        if (count($creditInfo) > 0) {
            $credit_ids = [];
            foreach ($creditInfo as $detail) {
                if ($detail->CreditProvider->repay_flag == 10) {
                    $credit_ids[] = $detail->id;
                }
            }
            if (count($credit_ids) > 0) {
                $repayCreditSum = self::getReplyCharge($params);
            }
        } else {
            $repayCreditSum = "0.00";
        }

        $chargeAmount = self::getTotalChargeByOrg($params);
        $contentArr['total_charge'] = sprintf("%.2f", $chargeAmount);
        $contentArr['total_repaid'] = self::FormatNum($repayCreditSum);
        $chargeTmp = $contentArr['total_charge'] + $repayCreditSum;
        $contentArr['total_charge_addition_repaid'] = self::FormatNum($chargeTmp);

        //付款公司累计开票
        $receiptSuccess = self::getTotalReceipt($orgRoot, $successParams, $params['pay_company_id'], $params['oil_type'],$params['receipt_operator_id']);
        $contentArr['total_receipt'] = sprintf("%.2f", $receiptSuccess);
        //付款公司累开冻结
        $receiptForzen = self::getTotalReceipt($orgRoot, $forzenParams, $params['pay_company_id'], $params['oil_type'],$params['receipt_operator_id']);
        $contentArr['total_receipt_frozen'] = sprintf("%.2f", $receiptForzen);

        $total_charge_add_repayed_subtraction_receipted_receiptFrozen = $chargeTmp - $receiptSuccess - $receiptForzen;
        $contentArr['total_charge_add_repayed_subtraction_receipted_receiptFrozen'] = sprintf("%.2f", $total_charge_add_repayed_subtraction_receipted_receiptFrozen);

        //累计返利
        $fanliArr = self::getTotalFanli($params);
        $contentArr['total_fanli_charge'] = self::FormatNum($fanliArr['fanli_charge']);
        $contentArr['total_fanli_calculate'] = 0;

        //累计转账返利
        $transterAll['fanli_into'] = $transterAll['fanli_out'] = 0;
        $contentArr['total_fanli_transfer_into'] = self::FormatNum($transterAll['fanli_into']);
        $contentArr['total_fanli_transfer_out'] = self::FormatNum($transterAll['fanli_out']);

        $contentArr['total_fanli'] = self::FormatNum(($contentArr['total_fanli_charge'] + $contentArr['total_fanli_calculate'] + $contentArr['total_fanli_transfer_into'] - $contentArr['total_fanli_transfer_out']));

        $nowReceiptFee = 0;
        if (isset($params['id'])) {//编辑操作
            $applyInfo = OilReceiptApply::getById($params);
            $nowReceiptFee = $applyInfo->receipt_amount;//加上本申请单的额度
            $contentArr['nowReceiptFee'] = $nowReceiptFee;
        }

        $contentArr['dynamic_money'] = $companyInfo->dynamic_money;

        if (isset($params['oil_type']) && !empty($params['oil_type'])) {
            $totalTrades = $max_amount = 0;

            $contentArr['total_org_receipt'] = sprintf("%.2f", $receiptSuccess);

            $contentArr['total_org_receipt_frozen'] = sprintf("%.2f", $receiptForzen);

            $trades_map = self::calReceiptTotalTrades($params);
            $totalTrades = $trades_map['total_trade_money'];

            $contentArr['total_trades'] = $totalTrades;
            $contentArr['total_use_fanli'] = $trades_map['total_fanli'];

            $viceTradesTotal = bcsub($contentArr['total_trades'], $contentArr['total_use_fanli'], 2) - bcadd($contentArr['total_org_receipt'], $contentArr['total_org_receipt_frozen'], 2);

            $contentArr['total_receipt_trades'] = sprintf("%.2f", $viceTradesTotal);

            if (bccomp($total_charge_add_repayed_subtraction_receipted_receiptFrozen, $viceTradesTotal, 2) > 0) {
                $max_amount = $viceTradesTotal;
            } else {
                $max_amount = $total_charge_add_repayed_subtraction_receipted_receiptFrozen;
            }
            $contentArr['max_amount'] = self::FormatNum(($max_amount + $nowReceiptFee + $companyInfo->dynamic_money));
        }
        Log::error("contentArr:" . var_export($contentArr, TRUE), [], "receipt_white_");
        return $contentArr;
    }

    static public function getUseChargeByCompanyAll($contentArr, $params)
    {
        $successParams = [
            ReceiptApplyStatus::SUCCESS,
            ReceiptApplyStatus::MAILED,
        ];
        $forzenParams = [
            ReceiptApplyStatus::HANDLING,
            ReceiptApplyStatus::PRE_AUDIT,
            ReceiptApplyStatus::AUDITED,
            ReceiptApplyStatus::OPENING
        ];
        $result = [];
        $oilTypeList = OilType::getReceiptList();

        //付款公司的累开和累开冻结
        $total_charge_add_repayed_subtraction_receipted_receiptFrozen = $contentArr['total_charge_add_repayed_subtraction_receipted_receiptFrozen'];

        //todo 根据油品类型进行分组取开票额度
        foreach ($oilTypeList as $oilTypeNo => $oilTypeName) {
            $params['oil_type'] = $oilTypeNo;
            $totalTrades = $max_amount = $lastAmount = $orgReceiptAmount = $orgReceiptAmountForzen = 0;
            Log::error("oilTypeNo:" . $oilTypeNo . "==totalTrades:" . $params['tradesList'][$params['receipt_operator_id']][$oilTypeNo], [$params], "receipt_white_");

            $trades_map = self::calReceiptTotalTrades($params);
            $totalTrades = $trades_map['total_trade_money'];

            $total_use_fanli = $trades_map['total_fanli'];

            if (bccomp($totalTrades, 0, 2) == 0) {
                continue;
            }

            //机构累加和累开冻结
            $orgReceiptAmount = self::getTotalReceipt($params['receiptOrgCode'], $successParams, $params['pay_company_id'], $oilTypeNo,$params['receipt_operator_id']);

            //新加机构累开冻结
            $orgReceiptAmountForzen = self::getTotalReceipt($params['receiptOrgCode'], $forzenParams, $params['pay_company_id'], $oilTypeNo,$params['receipt_operator_id']);

            $viceTradesTotal = bcsub($totalTrades,$total_use_fanli, 2) - bcadd($orgReceiptAmount, $orgReceiptAmountForzen, 2);

            if (bccomp($total_charge_add_repayed_subtraction_receipted_receiptFrozen, $viceTradesTotal, 2) > 0) {
                $max_amount = $viceTradesTotal;
            } else {
                $max_amount = $total_charge_add_repayed_subtraction_receipted_receiptFrozen;
            }
            $lastAmount = $max_amount + $contentArr['nowReceiptFee'] + $contentArr['dynamic_money'];

            $result[$oilTypeNo] = [
                "name" => $oilTypeName,
                "amount" => self::FormatNum($lastAmount),
                "total_receipt_trades" => self::FormatNum($viceTradesTotal),
                "total_trades" => self::FormatNum($totalTrades),
                "total_use_fanli" => self::FormatNum($total_use_fanli),
                "total_org_receipt" => self::FormatNum($orgReceiptAmount),
                "total_org_receipt_frozen" => self::FormatNum($orgReceiptAmountForzen),
            ];
        }
        Log::error("result" . var_export($result, TRUE), [], "receipt_white_");
        return $result;
    }

    static public function FormatNum($num)
    {
        return number_format($num, 2, ".", "");
    }

    static public function getMaxReceiptAmountForYT($params)
    {
        Log::error('参数|', [$params], 'receipt_white_');
        if (!$params['oil_type']) {
            throw new \RuntimeException('油品种类不能为空', 2);
        }

        if (!$params['trade_end_time']) {
            //throw new \RuntimeException('消费截止时间为空', 2);
            //G7WALLET-6822
            $params['trade_end_time'] = date('Y-m-d 00:00:00');
        }

        $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
        if (!$orgInfo) {
            throw new \RuntimeException('机构信息不存在', 2);
        }
        //处理独立开票还是集中开票
        // if ($orgInfo->receipt_mode == 1) {
        //     $orgRoot = substr($orgInfo->orgcode, 0, 6);
        // } else {
        //     $orgRoot = $orgInfo->orgcode;
        // }
        $orgRoot = $orgInfo->orgcode;

        $params['receipt_mode'] = $orgInfo->receipt_mode;
        $params['receiptOrgCode'] = $orgRoot;
        $params['is_recepit_nowtime'] = $orgInfo->is_recepit_nowtime;

        //大客户先开后结配置
        $bigConf = (new OrgConfigService())->getBigCustomerSetting($orgInfo->orgcode);
        Log::error('先开后结--|', [$bigConf], 'receipt_white_');
        if (!isset($bigConf['pre_open_receipt']) || $bigConf['pre_open_receipt'] != 1) {
            throw new \RuntimeException('此机构未开启先开后结', 2);
        }

        //增加标用一体和大客户先开后结配置
        $conf = (new OrgConfigService())->getOrgRebateUseSetting($orgInfo->orgcode);
        Log::error('标用一体--|', [$conf], 'receipt_white_');
        if (!isset($conf['equal_use']) || $conf['equal_use'] != 1) {
            throw new \RuntimeException('此机构未开启标用一体', 2);
        }

        $contentArr['params'] = $params;
        //求pay_company_id下指定orgIdList充值之和
        $companyInfo = OilPayCompany::getById(['id' => $params['pay_company_id']]);
        Log::error('付款公司--|', [$companyInfo->company_name], 'receipt_white_');

        //保持和之前开票额度返回数据一致，不参与计算的值均为0
        $contentArr['total_charge'] = 0; //累计充值
        $contentArr['total_repaid'] = 0; //累计还款
        $contentArr['total_charge_addition_repaid'] = 0; //累计充值 + 累计还款
        $contentArr['total_charge_add_repayed_subtraction_receipted_receiptFrozen'] = 0; //累计可用充值 累充+累还-累开-累开冻结
        $contentArr['total_charge_add_repayed_subtraction_receipted_receiptFrozen'] = 0; //累计可用充值 累充+累还-累开-累开冻结
        $contentArr['total_fanli_charge'] = 0; //累计返利充值
        $contentArr['total_fanli_calculate'] = 0; //累计返利计算
        $contentArr['total_fanli_transfer_into'] = 0; //累计返利转入
        $contentArr['total_fanli_transfer_out'] = 0; //累计返利转出
        $contentArr['total_fanli'] = 0; //返利充值+返利计算+返利转入-返利转出
        $contentArr['pay_company_id'] = $params['pay_company_id'];

        //付款公司累计开票
        $successParams = [ReceiptApplyStatus::SUCCESS, ReceiptApplyStatus::MAILED,];
        $receiptSuccess = self::getTotalReceipt($orgRoot, $successParams, $params['pay_company_id'], $params['oil_type'],$params['receipt_operator_id']);
        $contentArr['total_receipt'] = sprintf("%.2f", $receiptSuccess);
        Log::error('累计开票--|', [$receiptSuccess], 'receipt_white_');

        //付款公司累开冻结
        $forzenParams = [ReceiptApplyStatus::HANDLING, ReceiptApplyStatus::PRE_AUDIT, ReceiptApplyStatus::AUDITED, ReceiptApplyStatus::OPENING];
        $receiptForzen = self::getTotalReceipt($orgRoot, $forzenParams, $params['pay_company_id'], $params['oil_type'],$params['receipt_operator_id']); //针对油品冻结
        $contentArr['total_receipt_frozen'] = sprintf("%.2f", $receiptForzen);
        Log::error('累开冻结--|', [$receiptForzen], 'receipt_white_');

        $nowReceiptFee = 0;

        //动态干预值
        $contentArr['dynamic_money'] = $companyInfo->dynamic_money;
        Log::error('动态干预值--|', [$companyInfo->dynamic_money], 'receipt_white_');

        $totalTrades = $max_amount = $orgReceiptAmount = $orgReceiptAmountForzen = 0;

        //机构的累计开票
        $orgReceiptAmount = $receiptSuccess;
        $contentArr['total_org_receipt'] = sprintf("%.2f", $orgReceiptAmount);
        Log::error('机构的累计开票--|', [$orgReceiptAmount], 'receipt_white_');

        //新加机构累开冻结
        $orgReceiptAmountForzen = $receiptForzen;
        $contentArr['total_org_receipt_frozen'] = sprintf("%.2f", $orgReceiptAmountForzen);
        Log::error('机构累开冻结--|', [$orgReceiptAmountForzen], 'receipt_white_');

        $trade_map = self::calReceiptTotalTrades($params);
        $totalTrades = $trade_map['total_trade_money'];
        $contentArr['gas_use_fanli'] = $trade_map['total_fanli'];

        Log::error('累计消费--|', [$totalTrades], 'receipt_white_');

        $contentArr['total_trades'] = $totalTrades;

        //累计消费使用返利
        $contentArr['total_use_fanli'] = $contentArr['gas_use_fanli'];

        //累计可开消费 = 累计消费 - 累计使用返利 - 累计开票 - 累计冻结
        $contentArr['total_receipt_trades'] = 0;

        // 累计消费 - 累计开票 - 累计开票冻结 - 累计使用消费返利
        $max_amount = bcsub(bcsub($totalTrades, $contentArr['gas_use_fanli'], 2), bcadd($orgReceiptAmount, $orgReceiptAmountForzen, 2), 2);
        Log::error("可开额度:累计消费(" . $totalTrades . ") - 累计开票(" . $orgReceiptAmount . ") - 累计开票冻结(" . $orgReceiptAmountForzen . ") - 累计使用消费返利(" . $contentArr['gas_use_fanli'] . ")", [$max_amount], "receipt_white_");

        //加上干预值
        $contentArr['max_amount'] = self::FormatNum(($max_amount + $nowReceiptFee));
        $contentArr['max_amount'] = $contentArr['max_amount'] < 0 ? 0 : $contentArr['max_amount'];
        Log::error("可开额度加上干预值—-|", [$contentArr['max_amount']], "receipt_white_");
        Log::error("结果:", [$contentArr], "receipt_white_");

        return $contentArr;
    }

    static public function getReplyCharge($params)
    {
        //增加授信累计还款（当作充值）逻辑::当前机构如果有累计还款额并且付款公司一致就加否则就不加
        $repaySum = 0;
        $applyTime = date('Y-m-d H:i:s');
        $condition = [
            'org_id' => $params['org_id'],
            'apply_timeLe' => $applyTime,
            'status' => 1,
            "no_way" => 10,
            "is_del" => 0,
            "operators_id" => isset($params['receipt_operator_id']) && $params['receipt_operator_id'] ? $params['receipt_operator_id'] : '',
        ];
        if (isset($params['pay_company_id']) && !empty($params['pay_company_id'])) {
            $condition['pay_company_id'] = $params['pay_company_id'];
        }
        $repayTotal = OilCreditRepay::subRepayMoney($condition);

        Log::error('maxAmountForOrg-getReplyCharge' . var_export($repayTotal, TRUE), [$condition], 'org_receipt_');
        if ($repayTotal) {
            $repaySum = $repayTotal;
        }
        return $repaySum;
    }

    static public function getTotalChargeByOrg($params)
    {
        //求指定orgRoot累计充值
        $chargeParams = [
            'charge_type' => 1,
            'status' => 1,
            'orgroot' => $params['receiptOrgCode'],
            'receipt_operator_id' => isset($params['receipt_operator_id']) && $params['receipt_operator_id'] ? $params['receipt_operator_id'] : '',
        ];

        if (isset($params['_createtimeLe']) && $params['_createtimeLe']) {
            $chargeParams['createtimeLe'] = $params['_createtimeLe'];
        } else {
            $chargeParams['createtimeLne'] = $params['is_recepit_nowtime'] == 2
                ? date('Y-m-d 00:00:00')
                : date('Y-m-01') . ' 00:00:00';
        }

        if (isset($params['pay_company_id']) && !empty($params['pay_company_id'])) {
            $chargeParams['pay_company_id'] = $params['pay_company_id'];
        }

        Log::error('getTotalChargeByOrg->params' . var_export($chargeParams, TRUE), [$params], 'org_receipt_');

        $chargeAmount = OilAccountMoneyCharge::sumTotalChargeByOrgRoot($chargeParams);

        return $chargeAmount;
    }

    static public function getTotalReceipt($orgRoot, $receiptStatus = [], $pay_company_id = 0, $oil_type = 12,$receipt_operator_id = '')
    {
        if (empty($receiptStatus) || count($receiptStatus) == 0) {
            $receiptStatus = [
                ReceiptApplyStatus::AUDITED,
                ReceiptApplyStatus::PRE_AUDIT,
                ReceiptApplyStatus::HANDLING,
                ReceiptApplyStatus::OPENING,
                ReceiptApplyStatus::SUCCESS,
                ReceiptApplyStatus::MAILED,
            ];
        }
        $receiptParams = [
            'orgroot' => $orgRoot,
            'receipt_statusIn' => $receiptStatus,
            'receipt_operator_id' => $receipt_operator_id,
        ];
        if (!empty($oil_type)) {
            switch ($oil_type) {
                case OilType::GAS_YOU:
                    $receiptParams['oil_type_eq'] = OilType::GAS_YOU;
                    break;
                case OilType::UREA_YOU:
                    $receiptParams['oil_type_eq'] = OilType::UREA_YOU;
                    break;
                case OilType::QI_YOU:
                    $receiptParams['oil_type_eq'] = OilType::QI_YOU;
                    break;
                case OilType::CHAI_YOU:
                    $receiptParams['oil_type_eq'] = OilType::CHAI_YOU;
                    break;
                case OilType::FUEL_YOU:
                    $receiptParams['oil_type_list'] = [OilType::QI_YOU, OilType::CHAI_YOU, OilType::FUEL_YOU];
                    break;
                default:
                    $receiptParams['oil_type_list'] = [OilType::QI_YOU, OilType::CHAI_YOU];
            }
        }
        if (!empty($pay_company_id)) {
            $receiptParams['pay_company_id'] = $pay_company_id;
        }

        Log::error('getTotalReceipt->params' . var_export($receiptParams, TRUE), [$orgRoot,$pay_company_id], 'org_receipt_');

        //求指定orgRoot开票之和(含开票成功和开票冻结)
        $receiptAmount = OilReceiptApply::sumReceiptAmountByOrgRoot($receiptParams);
        return $receiptAmount;
    }

    static public function getTotalFanli($params)
    {
        //累计返利充值
        $totalChargeByOrgRootParams = [
            'charge_type' => 2,
            'status' => 1,
            'orgroot' => $params['receiptOrgCode'],
            'receipt_operator_id' => isset($params['receipt_operator_id']) && $params['receipt_operator_id'] ? $params['receipt_operator_id'] : '',
            'sign_lock_time' => isset($params['sign_lock_time']) && $params['sign_lock_time'] ? $params['sign_lock_time'] : '',
        ];

        if (isset($params['_createtimeLe']) && $params['_createtimeLe']) {
            $totalChargeByOrgRootParams['createtimeLe'] = $params['_createtimeLe'];
        } else {
            $totalChargeByOrgRootParams['createtimeLne'] = $params['is_recepit_nowtime'] == 2
                ? date('Y-m-d 00:00:00')
                : date('Y-m-01') . ' 00:00:00';
        }

        if (isset($params['trade_end_time']) && $params['trade_end_time']) {
            $totalChargeByOrgRootParams['createtimeLne'] = $params['trade_end_time'];
        }

        Log::error('getTotalFanli->params' . var_export($totalChargeByOrgRootParams, TRUE), [$params], 'org_receipt_');

        $fanliChargeAmount = OilAccountMoneyCharge::sumTotalChargeByOrgRoot($totalChargeByOrgRootParams);
        Log::info('累计返利充值--' . $fanliChargeAmount, ['search'=>$totalChargeByOrgRootParams,'origin'=>$params], 'getTotalFanli');


        return ["fanli_charge" => $fanliChargeAmount, "fanli_cal" => 0];
    }

    static public function calReceiptTotalTrades($params)
    {
        $searchParams = [
            'orgcode' => $params['receiptOrgCode'],
            'receipt_operator_id' => isset($params['receipt_operator_id']) && $params['receipt_operator_id'] ? $params['receipt_operator_id'] : '',
            'sign_lock_time' => isset($params['sign_lock_time']) && $params['sign_lock_time'] ? $params['sign_lock_time'] : '',
            'pay_company_id' => $params['pay_company_id'],
        ];
        if (isset($params['_createtimeLe']) && $params['_createtimeLe']) {
            $searchParams['createtimeLe'] = $params['_createtimeLe'];

        } else {
            $searchParams['createtimeLne'] = $params['is_recepit_nowtime'] == 2
                ? date('Y-m-d 00:00:00')
                : date('Y-m-01') . ' 00:00:00';
        }

        //圆通可根据传参数来决定消费截止时间
        if (isset($params['trade_end_time']) && $params['trade_end_time']) {
            $searchParams['createtimeLne'] = $params['trade_end_time'];
        }

        if (isset($params['trade_start_time']) && $params['trade_start_time']) {
            $searchParams['createtimeGe'] = $params['trade_start_time'];
        }

        if (isset($params['open_invoice_null']) && $params['open_invoice_null']) {
            $searchParams['open_invoice_null'] = $params['open_invoice_null'];
        }

        if (isset($params['white_flag']) && $params['white_flag']) {
            $searchParams['white_flag_field'] = $params['white_flag'];
        }


        if (isset($params['oil_type'])) {
            switch ($params['oil_type']) {
                case OilType::GAS_YOU:
                    $searchParams['oil_type_eq'] = OilType::GAS_YOU;
                    $searchParams['oil_comIn'] = OilCom::getAllFirstList();
                    break;
                case OilType::UREA_YOU:
                    $searchParams['oil_type_eq'] = OilType::UREA_YOU;
                    $searchParams['oil_comIn'] = OilCom::getAllFirstList();
                    break;
                case OilType::QI_YOU:
                    $searchParams['oil_type_eq'] = OilType::QI_YOU;
                    break;
                case OilType::CHAI_YOU:
                    $searchParams['oil_type_eq'] = OilType::CHAI_YOU;
                    break;
                default:
                    $searchParams['oil_type_list'] = [OilType::QI_YOU, OilType::CHAI_YOU];
            }
            
            $base = self::getOilBaseId($params['oil_type']);
            $searchParams['oil_2_level'] = $base;
            //G7WALLET-6768
            if($params['unset_base_id'] == 1){
                unset($searchParams['oil_2_level']);
            }
        }

        Log::error('calReceiptTotalTrades->params' . var_export($searchParams, TRUE), [$params], 'org_receipt_trades_');
        $total_trades = OilCardViceTrades::sumTradesByOrgIdListForWhite($searchParams);

        return $total_trades;
    }

    static public function getOilBaseId($oil_type = 12)
    {
        //G7WALLET-6768
        $baseId = [
            12 => [11,12,13,14], //燃油
            1 => [11,12,13], //汽油
            2 => [14], //柴油
            5  => [9,10], //天然气
        ];
        return isset($baseId[$oil_type]) ? $baseId[$oil_type] : [];
    }
}
