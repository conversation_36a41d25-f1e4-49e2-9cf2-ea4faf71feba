<?php


namespace Fuel\Service;


use Framework\Helper;
use Fuel\Defines\CooperationType;
use Fuel\Defines\SupplierCompanyTitleConf;
use Fuel\Defines\SupplierConf;
use Fuel\Service\OwingTicket\ReceiptReturnClaimAppService;
use http\Params;
use Models\OilFossPaymentRecords;
use Models\OilSupplierCompanyTitle;
use RuntimeException;
use Illuminate\Database\Capsule\Manager as Capsule;

class SupplierCompanyTitleService
{
    /**
     * 通过回票抬头查询 指定供应商列表
     * @param $suppierCompanyTitle  回票抬头名称
     */
    public static function getSupplierListByTitalName($suppierCompanyTitle)
    {
        /*$where['s_is_del'] = SupplierCompanyTitleConf::IS_DEL_TRUE;
        $where['supplier_company_title'] = $suppierCompanyTitle;

        $list = OilSupplierCompanyTitle::Filter($where)
            ->leftJoin("oil_station_supplier","oil_station_supplier.id","=","oil_supplier_company_title.supplier_id")
            ->select([
                "oil_station_supplier.id as supplier_id",
                "oil_station_supplier.supplier_name",
                "oil_station_supplier.cooperation_type",
                "oil_station_supplier.receipt_claim",
            ])
            ->groupBy("oil_supplier_company_title.supplier_id")
            ->get()
            ->toArray();*/
        //Capsule::connection()->enableQueryLog();
        $obj = OilSupplierCompanyTitle::leftJoin("oil_supplier_company","oil_supplier_company.id","=","oil_supplier_company_title.supplier_company_id")
            ->leftJoin("oil_station_supplier","oil_station_supplier.id","=","oil_supplier_company.supplier_id")
            ->where('oil_supplier_company_title.is_del',SupplierCompanyTitleConf::IS_DEL_TRUE)
            ->whereNotNull('oil_station_supplier.id')
            //->where('oil_supplier_company_title.open_status',SupplierCompanyTitleConf::OPEN_STATUS_REVIEW)
            ->select([
                "oil_station_supplier.id as supplier_id",
                "oil_station_supplier.supplier_name",
                "oil_station_supplier.receipt_claim",
                "oil_supplier_company_title.supplier_company_title"
            ]);
        //G7WALLET-2874
        $titles = explode(",",trim($suppierCompanyTitle));
        $_tmp = array_unique($titles);
        if(count($_tmp) > 1){
                $obj->whereIn("supplier_company_title",$_tmp);
        }else{
            $obj->where('supplier_company_title',$suppierCompanyTitle);
        }
        $list = $obj
            ->get()
            ->toArray();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        //G7WALLET-2818
        //$blackList = (new ReceiptReturnClaimAppService())->getSupplierClaimBlackListData();
        $arr = [];
        $map = [];
        foreach ($list as $key=>$item)
        {
            $item['is_cycle'] = $item['receipt_claim'] = $item['receipt_claim'] ? $item['receipt_claim'] : 0;
            $_key = trim($item['supplier_company_title']);
            if( !in_array($item['supplier_id'],$map[$_key]) ) {
                $map[$_key][] = $item['supplier_id'];
            }
            $arr[$item['supplier_id']] = $item;
        }

        //单个抬头时，直接返回
        if(count($_tmp) == 1){
            return array_values($arr);
        }

        $res = $supplierIds = [];
        foreach (array_values($map) as $_k => $one){
            if($_k == 0){
                $supplierIds = $one;
            }else{
                $supplierIds = array_intersect($supplierIds,$one);
            }
        }

        foreach ($supplierIds as $_id){
            $res[] = $arr[$_id];
        }
        return $res;
    }
    /**
     * 通过 回票抬头名称 搜素列表
     * @param array $params
     * @return mixed
     */
    public static function searchList(array $params)
    {
        $where['is_del'] = SupplierCompanyTitleConf::IS_DEL_TRUE;
        $where['s_supplier_company_title'] = array_get($params,'s_supplier_company_title',"");
        $list = OilSupplierCompanyTitle::getListByFilter($params,[
            'id',
            'supplier_company_title'
        ]);

        return $list;
    }

    /**
     * 获取 分页列表
     * @param array $params
     * @return array
     */
    public static function getList(array $params)
    {
        $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
        $params['page'] = isset($params['page']) ? $params['page'] : 1;

        $params['s_is_del'] = SupplierCompanyTitleConf::IS_DEL_TRUE;
        $sqlObj = OilSupplierCompanyTitle::Filter($params)
            ->leftJoin("oil_station_supplier","oil_station_supplier.id","=","oil_supplier_company_title.supplier_id")
            ->leftJoin("oil_collect_company","oil_collect_company.id","=","oil_supplier_company_title.collect_company_id")
            ->select([
                "oil_supplier_company_title.id",
                "oil_supplier_company_title.supplier_company_id",
                "oil_supplier_company_title.supplier_id",
                "oil_supplier_company_title.collect_company_id",
                "oil_supplier_company_title.supplier_company_title",
                "oil_supplier_company_title.open_status",
                "oil_supplier_company_title.open_operator",
                "oil_supplier_company_title.opentime",
                "oil_supplier_company_title.status",
                "oil_supplier_company_title.creator",
                "oil_supplier_company_title.last_operator",
                "oil_supplier_company_title.createtime",
                "oil_supplier_company_title.updatetime",
                "oil_station_supplier.supplier_name",
                "oil_collect_company.company_name",
            ])
            ->orderBy('oil_supplier_company_title.id', 'desc');
        $resData = $sqlObj->paginate($params['limit'],['*'],'page',$params['page'])->toArray();
        $data = [
            'total'         => $resData['total'],
            'per_page'      => $resData['per_page'],
            'current_page'  => $resData['current_page'],
            'last_page'     => $resData['last_page'],
            'from'          => $resData['from'],
            'to'            => $resData['to'],
            'data'          => self::formatSupplierCompanyTitleData($resData['data'])
        ];

        return $data;

    }

    public static function formatSupplierCompanyTitleData(array $data , $formatDegree = 1)
    {
        array_walk($data,function ($val,$key) use(&$data,$formatDegree){
            $data[$key] = self::formatSupplierCompanyTitleInfo($val,$formatDegree);
        });
        return $data;
    }

    public static function formatSupplierCompanyTitleInfo(array $info , $formatDegree = 1)
    {
        if (empty($info)) return $info;

        $info['open_status_title'] = SupplierCompanyTitleConf::getOpenStatusTitle($info['open_status']);
        $info['status_title'] = SupplierCompanyTitleConf::getStatusTitle($info['status']);

        return $info;
    }

    /**
     * 获取 回票抬头详情
     * @param array $params
     * @return array
     */
    public static function show(array $params)
    {
        $params['is_del'] = SupplierCompanyTitleConf::IS_DEL_TRUE;
        $info = OilSupplierCompanyTitle::getInfoByFilter($params);

        return !empty($info) ? self::formatSupplierCompanyTitleInfo($info->toArray()) : [];
    }

    /**
     * 回票抬头添加
     * @param array $params
     * @return mixed
     */
    public static function create(array $params)
    {
        //校验 回票抬头长度
        if (strlen($params['supplier_company_title']) > 300)
        {
            throw new RuntimeException("回票抬头应为1~100个字", 2);
        }

        if (!in_array($params['status'],[SupplierCompanyTitleConf::STATUS_TRUE,SupplierCompanyTitleConf::STATUS_FALSE]))
        {
            throw new RuntimeException("使用状态参数错误", 2);
        }

        //校验 供应商与收款公司是否存在关联关系
        $supplierCompanyInfo = SupplierCompanyService::getInfoByFilter(['id' => $params['supplier_company_id']],[
            'supplier_id',
            'collect_company_id',
        ]);
        if (empty($supplierCompanyInfo)) throw new RuntimeException("供应商与收款公司不存在关联关系",2);

        $supplierCompanyInfo = $supplierCompanyInfo->toArray();
        
        //检查 回票抬头 是否存在
        $titleWhere['company_title'] = $params['supplier_company_title'];
        $titleWhere['collect_company_id'] = $supplierCompanyInfo['collect_company_id'];
        $titleWhere['supplier_id'] = $supplierCompanyInfo['supplier_id'];
        $titleInfo = OilSupplierCompanyTitle::getInfoByFilter($titleWhere,['id','is_del']);
        if (!empty($titleInfo))
        {
            if ($titleInfo['is_del'] == SupplierCompanyTitleConf::IS_DEL_TRUE)
            {
                throw new RuntimeException("回票抬头在收款公司上已存在",2);
            }
            $update = [
                'id' => $titleInfo['id'],
                'supplier_id' => $supplierCompanyInfo['supplier_id'],
                'collect_company_id' => $supplierCompanyInfo['collect_company_id'],
                'open_status' => SupplierCompanyTitleConf::OPEN_STATUS_TO_REVIEW,
                'open_operator_id' => '',
                'open_operator' => '',
                'opentime' => '',
                'status' => $params['status'],
                'is_del' => SupplierCompanyTitleConf::IS_DEL_TRUE,
                'creator_id' => (new CommonService())->getApp()->myAdmin->id,
                'creator' => (new CommonService())->getApp()->myAdmin->true_name,
            ];

            return OilSupplierCompanyTitle::edit($update);
        }

        //整理添加数据
        $insert = [
            'supplier_company_id' => $params['supplier_company_id'],
            'supplier_id' => $supplierCompanyInfo['supplier_id'],
            'collect_company_id' => $supplierCompanyInfo['collect_company_id'],
            'supplier_company_title' => $params['supplier_company_title'],
            'status' => $params['status'],
            'creator_id' => (new CommonService())->getApp()->myAdmin->id,
            'creator' => (new CommonService())->getApp()->myAdmin->true_name,
        ];

        return OilSupplierCompanyTitle::add($insert);
    }

    /**
     * 回票抬头编辑
     * @param array $params
     * @return mixed
     */
    public static function edit(array $params)
    {
        //校验 供应商与收款公司是否存在关联关系
        $supplierCompanyInfo = SupplierCompanyService::getInfoByFilter(['id' => $params['supplier_company_id']],[
            'supplier_id',
            'collect_company_id',
        ]);
        if (empty($supplierCompanyInfo)) throw new RuntimeException("供应商与收款公司不存在关联关系",2);

        $supplierCompanyInfo = $supplierCompanyInfo->toArray();

        //检查回票抬头是否存在
        $checkWhere['id'] = $params['id'];
        $checkWhere['supplier_id'] = $supplierCompanyInfo['supplier_id'];
        $checkWhere['collect_company_id'] = $supplierCompanyInfo['collect_company_id'];
        $titleWhere['supplier_id'] = $supplierCompanyInfo['supplier_id'];
        $titleInfo = OilSupplierCompanyTitle::getInfoByFilter($checkWhere,['id',"open_status",'status']);
        if (empty($titleInfo)) throw new RuntimeException("回票抬头不存在",2);
        $tryType = false;
        //判断回票抬头状态
        if ($titleInfo['open_status'] == SupplierCompanyTitleConf::OPEN_STATUS_TO_REVIEW)
        {
            throw new RuntimeException("待审核状态不可编辑",2);
        }
        //已审核状态只能修改 使用状态
        elseif ($titleInfo['open_status'] == SupplierCompanyTitleConf::OPEN_STATUS_REVIEW)
        {
            if (!in_array($params['status'],[SupplierCompanyTitleConf::STATUS_TRUE,SupplierCompanyTitleConf::STATUS_FALSE]))
            {
                throw new RuntimeException("使用状态参数错误", 2);
            }
            //判断使用状态 是否相同
            if ($titleInfo['status'] == $params['status'])
            {
                if ($params['status'] == SupplierCompanyTitleConf::STATUS_TRUE)
                {
                    throw new RuntimeException("回票抬头已经是在用状态",2);
                }
                else
                {
                    throw new RuntimeException("回票抬头已经是停用状态",2);
                }
            }
            $update = [
                'id' => $params['id'],
                'status' => $params['status'],
            ];
        }
        elseif ($titleInfo['open_status'] == SupplierCompanyTitleConf::OPEN_STATUS_REJECT)
        {
            //判断回票抬头
            if (empty($params['supplier_company_title'])) throw new RuntimeException("回票抬头不能为空",2);
            if (strlen($params['supplier_company_title']) > 300) throw new RuntimeException("回票抬头应为1~100个字",2);

            //整理需要修改数信息
            $update['id'] = $params['id'];
            $update['supplier_company_title'] = $params['supplier_company_title'];
            $update['open_status'] = SupplierCompanyTitleConf::OPEN_STATUS_TO_REVIEW;
            $update['status'] = $params['status'];

            //校验  回票抬头是否存在
            $checkTitleWhere['check_id'] = $params['id'];
            $checkTitleWhere['collect_company_id'] = $supplierCompanyInfo['collect_company_id'];
            $checkTitleWhere['company_title'] = $params['supplier_company_title'];
            $checkTitleInfo= OilSupplierCompanyTitle::getInfoByFilter($checkTitleWhere,['id','is_del']);

            if (!empty($checkTitleInfo))
            {
                if ($checkTitleInfo['is_del'] == SupplierCompanyTitleConf::IS_DEL_TRUE)
                {
                    throw new RuntimeException("回票抬头在收款公司上已存在",2);
                }
                $tryType = true;
            }

        }
        else
        {
            throw new RuntimeException("回票抬头状态错误",2);
        }

        $update['last_operator_id'] = (new CommonService())->getApp()->myAdmin->id;
        $update['last_operator'] = (new CommonService())->getApp()->myAdmin->true_name;

        if ($tryType)
        {
            try {
                Capsule::connection()->beginTransaction();
                OilSupplierCompanyTitle::remove(['ids' => $checkTitleInfo['id']]);
                $data = OilSupplierCompanyTitle::edit($update);
                Capsule::connection()->commit();
            }catch (RuntimeException $e) {
                Capsule::connection()->rollBack();
                throw new RuntimeException("回票抬头修改失败",2);
            }
        }
        else
        {
            $data = OilSupplierCompanyTitle::edit($update);
        }

        return $data;
    }

    /**
     * 回票抬头删除
     * @param array $params
     * @return mixed
     */
    public static function remove(array $params)
    {
        //校验回票抬头是否存在
        $titleInfo = OilSupplierCompanyTitle::getInfoByFilter($params,[
            'id','supplier_company_title','is_del'
        ]);
        if (empty($titleInfo)) throw new RuntimeException("回票抬头不存在",2);
        if ($titleInfo['is_del'] == SupplierCompanyTitleConf::IS_DEL_FALSE)
        {
            throw new RuntimeException("回票抬头已删除",2);
        }
        //校验回票抬头是否被使用过
        $recordsTotal = OilFossPaymentRecords::getTotal([
            'receipt_title' => $titleInfo['supplier_company_title']
        ]);
        if ($recordsTotal > 0) throw new RuntimeException("该回票抬头已认领过回票，无法删除",2);

        $delete = [
            'id' => $titleInfo['id'],
            'is_del' => SupplierCompanyTitleConf::IS_DEL_FALSE,
        ];

        return OilSupplierCompanyTitle::edit($delete);
    }

    /**
     * 回票抬头审核
     * @param array $params
     * @return mixed
     */
    public static function review(array $params)
    {
        $params['is_del'] = SupplierCompanyTitleConf::IS_DEL_TRUE;
        $titleInfo = OilSupplierCompanyTitle::getInfoByFilter($params,[
            'id','open_status'
        ]);
        if (empty($titleInfo)) throw new RuntimeException("回票抬头不存在",2);
        if ($titleInfo['open_status'] != SupplierCompanyTitleConf::OPEN_STATUS_TO_REVIEW)
        {
            throw new RuntimeException("回票抬头不是待审核状态",2);
        }

        $reviewData = [
            'id' => $titleInfo['id'],
            'open_status' => SupplierCompanyTitleConf::OPEN_STATUS_REVIEW,
            'open_operator_id' => (new CommonService())->getApp()->myAdmin->id,
            'open_operator' => (new CommonService())->getApp()->myAdmin->true_name,
            'opentime' => date("Y-m-d H:i:s",time()),
            'last_operator_id' => (new CommonService())->getApp()->myAdmin->id,
            'last_operator' => (new CommonService())->getApp()->myAdmin->true_name,
        ];

        return OilSupplierCompanyTitle::edit($reviewData);
    }

    /**
     * 回票抬头驳回
     * @param array $params
     * @return mixed
     */
    public static function reject(array $params)
    {
        $params['is_del'] = SupplierCompanyTitleConf::IS_DEL_TRUE;
        $titleInfo = OilSupplierCompanyTitle::getInfoByFilter($params,[
            'id','open_status'
        ]);
        if (empty($titleInfo)) throw new RuntimeException("回票抬头不存在",2);
        if ($titleInfo['open_status'] == SupplierCompanyTitleConf::OPEN_STATUS_REJECT)
        {
            throw new RuntimeException("回票抬头已经驳回",2);
        }

        $reviewData = [
            'id' => $titleInfo['id'],
            'open_status' => SupplierCompanyTitleConf::OPEN_STATUS_REJECT,
            'open_operator_id' => (new CommonService())->getApp()->myAdmin->id,
            'open_operator' => (new CommonService())->getApp()->myAdmin->true_name,
            'opentime' => date("Y-m-d H:i:s",time()),
            'last_operator_id' => (new CommonService())->getApp()->myAdmin->id,
            'last_operator' => (new CommonService())->getApp()->myAdmin->true_name,
        ];

        return OilSupplierCompanyTitle::edit($reviewData);
    }
}