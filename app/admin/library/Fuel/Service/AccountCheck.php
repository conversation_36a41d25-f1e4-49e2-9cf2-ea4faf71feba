<?php
namespace Fuel\Service;

use Framework\Log;
use Models\OilAccountAssign;
use Models\OilAccountAssignDetails;
use Models\OilAccountCheck;
use Models\OilAccountMoney;
use Models\OilAccountMoneyCharge;
use Models\OilAccountMoneyRecords;
use Models\OilAccountMoneyTransfer;
use Models\OilCreditAccount;
use Models\OilFanliCalculate;

class AccountCheck
{
    static public function accountMoney()
    {
        $record = OilAccountMoney::accountCheck();
        if (count($record) > 0) {
            $addArr = [];
            foreach ($record as $v) {
                if (!isset($addArr[ $v['org_id'] ])) {
                    $addArr[ $v['org_id'] ] = $v;
                    $addArr[ $v['org_id'] ]['type'] = 10;
                }
            }

            //添加至三方平账表
            self::addAccountCheck($addArr, 10);
        }
    }

    static public function creditAccountMoney()
    {
        $records = OilCreditAccount::accountCheck();
    }

    /**
     * @title   写入账户流水统计数据
     * <AUTHOR>
     * @param $params
     * @return array
     */
    static public function accountRecord(array $params)
    {
        $addArr = [];

        //分配、返利
        $record = OilAccountMoneyRecords::accountCheckNotTransferAndReCharge($params);
        if (count($record) > 0) {
            foreach ($record as $v) {
                if (!isset($addArr[ $v['org_id'] ])) {
                    $addArr[ $v['org_id'] ] = ['org_id' => $v['org_id'], 'type' => 20, 'orgcode'=>$v['orgcode'], 'orgname'=>$v['org_name']];
                }
                $addArr[ $v['org_id'] ] = array_merge($addArr[ $v['org_id'] ], self::preAccountRecordData($v));
            }
        }

        //充值
        $reCharge = OilAccountMoneyRecords::accountCheckReCharge($params);
        if($reCharge){
            foreach($reCharge as $v){
                $v = (array)$v;

                if($v['charge_type'] == 2){
                    if (isset($addArr[ $v['org_id'] ])) {
                        if(isset($addArr[$v['org_id']]['total_fanli'])){
                            $addArr[ $v['org_id'] ]['total_fanli'] = $addArr[ $v['org_id'] ]['total_fanli'] + $v['total_money'];
                        }else{
                            $addArr[ $v['org_id'] ]['total_fanli'] = $v['total_money'];
                        }
                    }else{
                        $addArr = self::preAccountRecordItem($addArr, $v, 'total_fanli');
                    }
                }elseif($v['charge_type'] == 1){
                    $addArr = self::preAccountRecordItem($addArr, $v, 'total_charge');
                }
            }
        }


        //转入、转出
        $transfer = OilAccountMoneyRecords::accountCheckTransfer($params);
        if(count($transfer) > 0) {
            foreach($transfer as $v){
                $key = \NULL;
                if($v['trade_type'] == 1){
                    $key = 'total_transfer_in';
                }elseif($v['trade_type'] == -1){
                    $key = 'total_transfer_out';
                }
                if(!\is_null($key)){
                    $addArr = self::preAccountRecordItem($addArr, $v, $key);
                }
            }
        }

        //取当前余额
        $balanceRecord = OilAccountMoneyRecords::accountCheckGetBalance($params);
        if($balanceRecord){
            foreach($balanceRecord as $v){
                $v = (array)$v;
                $addArr = self::preAccountRecordItem($addArr, $v, 'balance');
            }
        }

        if($addArr){
            //添加至三方平账表
            self::addAccountCheck($addArr, 20);
        }
    }

    static private function preAccountRecordItem($addArr, $v, $fieldName)
    {
        if (!isset($addArr[ $v['org_id'] ])) {
            $addArr[ $v['org_id'] ] = ['org_id' => $v['org_id'], 'type' => 20, 'orgcode'=>$v['orgcode'], 'orgname'=>$v['org_name']];
        }
        $addArr[ $v['org_id'] ] = array_merge($addArr[ $v['org_id'] ], [$fieldName    =>  $v['total_money']]);

        return $addArr;
    }

    /**
     * @title   添加至三方平账表
     * <AUTHOR>
     * @param $addArr
     * @param $type
     * @return bool
     */
    static private function addAccountCheck($addArr, $type)
    {
        if ($addArr) {
            foreach ($addArr as $v) {
                $result = OilAccountCheck::getByOrgAndType(
                    [
                        'type'   => $type,
                        'org_id' => $v['org_id']
                    ]
                );
                if ($result) {
                    $result->update($v);
                } else {
                    OilAccountCheck::add($v);
                }
            }
        }
    }

    /**
     * @title   预处理账户流水统计数据
     * <AUTHOR>
     * @param $item
     * @return array|null
     */
    static private function preAccountRecordData($item)
    {
        $data    = NULL;
        $noTypes = [
            'FL' => 'total_fanli',
            'CZ' => 'total_charge',
            'FP' => 'total_assign',
        ];

        if (isset($noTypes[ $item['no_type'] ])) {
            $key  = $noTypes[ $item['no_type'] ];

            $data = [$key => $key == 'total_assign' && $item['total_money'] < 0 ? abs($item['total_money']) : $item['total_money']];
        }

        return $data;
    }

    /**
     * @title   写入三方统计－工单维度统计
     * <AUTHOR>
     * @return bool
     */
    static public function workOrder()
    {
        //处理分配工单
        $addArr1 = self::handleAssignWorkOrder();

        //处理充值工单
        $addArr2 = self::handleChargeWorkOrder();

        //处理返利工单
        $addArr3 = self::handleFanliWorkOrder();

        //处理转账工单－转入
        $addArr4 = self::handleTransferInWorkOrder();

        //处理转账工单－转出
        $addArr5 = self::handleTransferOutWorkOrder();

        $addArr = self::arrayMerge($addArr1,$addArr2,$addArr3,$addArr4,$addArr5);

        //添加至三方平账表
        self::addAccountCheck($addArr, 30);

        return true;
    }


    static private function arrayMerge()
    {
        $args = func_get_args();
        $addArr = [];
        foreach ($args as $item){
            foreach ($item as $k=>$v){
                if (!isset($addArr[ $v['org_id'] ])) {
                    $addArr[ $v['org_id'] ] = $v;
                }else{
                    $addArr[$v['org_id']] = array_merge($addArr[$v['org_id']], $v);
                }
            }
        }

        return $addArr;
    }

    /**
     * @title   处理分配工单
     * <AUTHOR>
     * @return array
     */
    static private function handleAssignWorkOrder()
    {
//        $record = OilAccountAssign::accountCheck();
        $record = OilAccountAssignDetails::accountCheck();

        return self::preInsertData($record, 'total_assign', 30);
    }

    /**
     * @title   处理充值工单
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @param int $charge_type
     * @return array
     * @returns
     * array
     * @returns
     */
    static private function handleChargeWorkOrder($charge_type = 1)
    {
        $record = OilAccountMoneyCharge::accountCheck($charge_type);

        return self::preInsertData($record, 'total_charge', 30);
    }

    /**
     * @title   处理返利工单
     * <AUTHOR>
     * @return array
     */
    static private function handleFanliWorkOrder()
    {
        $data = [];
        $record = OilFanliCalculate::accountCheck();
        $_record = self::preInsertData($record, 'total_fanli', 30);

        $fanLiChargeRecord = OilAccountMoneyCharge::accountCheck(2);
        $_fanLiChargeRecord = self::preInsertData($fanLiChargeRecord, 'total_fanli', 30);



        if($_record && $_fanLiChargeRecord){
            foreach($_record as $k=>&$v){
                if(isset($_fanLiChargeRecord[$k])){
                    $v['total_fanli'] = $_fanLiChargeRecord[$k]['total_fanli'] + $v['total_fanli'];
                    unset($_fanLiChargeRecord[$k]);
                }
            }
            $data = \array_merge($_record, $_fanLiChargeRecord);
        }

        return $data;
    }

    /**
     * @title   处理转账工单－转入
     * <AUTHOR>
     * @return array
     */
    static private function handleTransferInWorkOrder()
    {
        $record = OilAccountMoneyTransfer::accountCheckForTransferIn();

        return self::preInsertData($record, 'total_transfer_in', 30);
    }

    /**
     * @title   处理转账工单－转出
     * <AUTHOR>
     * @return array
     */
    static private function handleTransferOutWorkOrder()
    {
        $record = OilAccountMoneyTransfer::accountCheckForTransferOut();

        return self::preInsertData($record, 'total_transfer_out', 30);
    }

    static private function preInsertData($record, $fileName, $type)
    {
        $addArr   = [];
        if (count($record) > 0) {
            foreach ($record as $v) {
                $v = (array)$v;
                if (!isset($addArr[ $v['org_id'] ])){
                    $addArr[ $v['org_id'] ] = ['org_id' => $v['org_id'], 'type' => $type, 'orgcode'=>$v['orgcode'], 'orgname'=>$v['org_name'],$fileName=>$v[$fileName]];
                }elseif(isset($addArr[ $v['org_id'] ][$fileName])){
                    $addArr[ $v['org_id'] ][$fileName] = $addArr[ $v['org_id'] ][$fileName] + $v[$fileName];
                }
            }
        }

        return $addArr;
    }
}