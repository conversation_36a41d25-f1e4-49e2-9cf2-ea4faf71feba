<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2021/10/28
 * Time: 11:35 AM
 */
namespace Fuel\Service;

use Fuel\Defines\OilCom;
use Fuel\Defines\OrgConf;
use Illuminate\Database\Capsule\Manager;
use Models\OilOrgCardRegionDayTrade;
use Fuel\Service\TypeCategoryService;

/**
 * 消费地区统计
 */
class OrgTradeAreaStatisticsService
{
    use SingletonService;

    const OIL_TEMPLATE = '经营数据线上化';
    private $model;

    public function __construct()
    {
        $this->model = new OilOrgCardRegionDayTrade();
    }

    /**
     * 获得油站运营商交易统计
     *
     * @param array $params
     * @return void
     */
    public function getList(array $params)
    {
        \helper::argumentCheck(['date'], $params);

        // 计算总数
        if (intval($params['count']) === 1) {
            $params['month'] = $params['date'];
            unset($params['date']);
            $params['top_org_id_not_in'] = OrgConf::getTestOrgId();

            $sqlBuilder = $this->model->selectRaw('COUNT(DISTINCT province_id) as id_count')
            ->filter($params);

            return $sqlBuilder->first()->id_count ?? 0;
        }

        $params['fields'] = '
            oil_provinces.province, province_id,
            SUM(zsy_money + zsh_money + cylmk_money + qiaopai_money + czk_money + gxk_money + fck_money) AS money,
            SUM(zsy_money + zsh_money + cylmk_money) as en_money,
            SUM(qiaopai_money + czk_money + gxk_money + fck_money) AS e_money,
            SUM(zsy_num + zsh_num + cylmk_num + qiaopai_num + czk_num + gxk_num + fck_num) AS num,
            SUM(zsy_num + zsh_num + cylmk_num) as en_num,
            SUM(qiaopai_num + czk_num + gxk_num + fck_num) AS e_num';

        $params['groupBy'] = ['province_id'];
        $params['orderBy'] = ['money' => 'desc'];
        $params['month'] = $params['date'];
        unset($params['date']);

        $params['withOilProvice'] = true;
        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();

        $list = OilOrgCardRegionDayTrade::getList($params);

        // 当月全部消费地区产生的消费总额
        $allAmount = $this->getAmount($params);

        return $list->map(function($item) use ($allAmount, $params) {
            $item->month = $params['month'] . ' 至 ' . $params['month'];
            $item->province = $item->province ?? '-';
            $item->money_rate = OrgChargeStatisticsService::getInstance()->formatRate($item->money, $allAmount);

            $item->money    = OrgChargeStatisticsService::getInstance()->formatFloatNumber($item->money);
            $item->en_money = OrgChargeStatisticsService::getInstance()->formatFloatNumber($item->en_money);
            $item->e_money  = OrgChargeStatisticsService::getInstance()->formatFloatNumber($item->e_money);
            $item->num      = OrgChargeStatisticsService::getInstance()->formatFloatNumber($item->num);
            $item->en_num   = OrgChargeStatisticsService::getInstance()->formatFloatNumber($item->en_num);
            $item->e_num    = OrgChargeStatisticsService::getInstance()->formatFloatNumber($item->e_num);

            return $item;
        });
    }

    /**
     * 当月全部消费地区产生的消费总额
     *
     * @param array $params
     * @return float
     */
    public function getAmount(array $params) : float
    {
        return $this->model->selectRaw('SUM(zsy_money + zsh_money + cylmk_money + qiaopai_money + czk_money + gxk_money + fck_money) AS all_amount')
        ->filter(collect($params)->only(['month', 'top_org_id_not_in'])->toArray())
        ->first()->all_amount ?? 0;
    }

    /**
     * 油卡类型统计
     *
     * @param array $params
     * @return array
     */
    public function carTypeStatistic(array $params)
    {
        \helper::argumentCheck(['date', 'province_id'], $params);

        $params['fields'] = '
            SUM(zsy_money) AS zsy,
            SUM(zsh_money) AS zsh,
            SUM(cylmk_money) AS cylmk,
            SUM(qiaopai_money) AS qiaopai,
            SUM(czk_money) AS czk,
            SUM(gxk_money) AS gxk,
            SUM(fck_money) AS fck,
            SUM(zsy_num) AS zsy_num,
            SUM(zsh_num) AS zsh_num,
            SUM(cylmk_num) AS cylmk_num,
            SUM(qiaopai_num) AS qiaopai_num,
            SUM(czk_num) AS czk_num,
            SUM(gxk_num) AS gxk_num,
            SUM(fck_num) AS fck_num
        ';

        $params['month'] = $params['date'];
        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();

        unset($params['date']);
        $data = OilOrgCardRegionDayTrade::getList($params)->first()->toArray();

        $oilRelations = $this->getOliComRelation();

        $oilRelationsKey = array_keys($oilRelations);

        // 计算总金额
        $allAmount = 0;
        foreach ($data as $key => $item) {
            if (in_array($key, $oilRelationsKey)) {
                $allAmount += $item;
            }
        }

        $resData = $sort = [];
        foreach ($data as $key => $item) {
            if (in_array($key, $oilRelationsKey)) {
                if ($item == 0) {
                    continue;
                }

                $tmp = [
                    'index' => 0,
                    'card_type'     => OilCom::getName($oilRelations[$key]),
                    'money'         => OrgChargeStatisticsService::getInstance()->formatFloatNumber($item),
                    'money_rate'    => OrgChargeStatisticsService::getInstance()->formatRate($item, $allAmount),
                    'num'           => OrgChargeStatisticsService::getInstance()->formatFloatNumber($data[$key . '_num']),
                ];

                $resData[]  = $tmp;
                $sort[] = $item;
            }
        }

        array_multisort($sort, SORT_DESC, $resData);
        $i = 1;
        foreach ($resData as &$row) {
            $row['index'] = $i++;
        }

        return $resData;
    }

    /**
     * 获得油品的映射
     *
     * @return array
     */
    public function getOliComRelation() : array
    {
        return [
            'zsy' => OilCom::ZSY,
            'zsh' => OilCom::ZSH,
            'cylmk' => OilCom::ZCW_ZSH_CYZYK,
            'qiaopai' => OilCom::SHELL,
            'czk' => OilCom::GAS_FIRST_CHARGE,
            'gxk' => OilCom::GAS_FIRST_TALLY,
            'fck' => OilCom::FORTUNE_CARD,
        ];
    }

    /**
     * 油品类型统计
     *
     * @param array $params
     * @return void
     */
    public function oilTypeStatistic(array $params)
    {
        \helper::argumentCheck(['date', 'province_id'], $params);

        $params['fields'] = '
            oil_base_id,
            SUM(zsy_money + zsh_money + cylmk_money + qiaopai_money + czk_money + gxk_money + fck_money) AS _money,
            SUM(zsy_num + zsh_num + cylmk_num + qiaopai_num + czk_num + gxk_num + fck_num) AS _num
        ';

        $params['groupBy'] = ['oil_base_id'];
        $params['month'] = $params['date'];
        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();

        unset($params['date']);
        $data = OilOrgCardRegionDayTrade::getList($params);

        $oilConfig = TypeCategoryService::getSecondOilConfigData(['template_name' => self::OIL_TEMPLATE]);

        $allAmount = 0;
        $res = [];
        foreach ($data as $row) {

            if (isset($oilConfig[$row->oil_base_id])) {
                if ($oilConfig[$row->oil_base_id]['statistic_node']) {
                    $oilId = $row->oil_base_id;
                    $oilName = $oilConfig[$row->oil_base_id]['name'];
                } else {
                    $top = $oilConfig[$row->oil_base_id]['parent'];

                    $oilId = $top['id'];
                    $oilName = $top['name'];
                }
            } else {
                $oilId = -1;
                $oilName = '其他';
            }

            if (! isset($res[$oilId])) {
                $res[$oilId] = [
                    'oil_id' => $oilId,
                    'oil_name' => $oilName,
                    'money' => 0,
                    'num' => 0,
                ];
            }

            $res[$oilId]['money'] = bcadd($res[$oilId]['money'], $row->_money, 2);
            $res[$oilId]['num'] = bcadd($res[$oilId]['num'], $row->_num, 2);
            $allAmount = bcadd($allAmount, $row->_money, 2);
        }

        $sort = [];
        foreach ($res as $row) {
            $sort[] = $row['money'];
        }
        array_multisort($sort, SORT_DESC, SORT_NUMERIC, $res);

        foreach ($res as $i => &$row) {
            $row['index'] = $i + 1;
            $row['money_rate'] = OrgChargeStatisticsService::getInstance()->formatRate($row['money'], $allAmount);

            $row['money'] = OrgChargeStatisticsService::getInstance()->formatFloatNumber($row['money']);
            $row['num'] = OrgChargeStatisticsService::getInstance()->formatFloatNumber($row['num']);
        }

        return $res;
    }
}
