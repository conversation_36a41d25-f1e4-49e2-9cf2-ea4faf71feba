<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-11-3
 * Time: 下午6:30
 */

namespace Fuel\Service;


use Models\OilCardViceTrades;

class CardViceTradesExport
{

    /**
     * 小于等于条数时同步导出
     * @var int
     */
    protected $syncLimit = 10000;

    /**
     * sql查询每页条数
     * @var int
     */
    protected $pageSize = 5000;

    /**
     * CSV单文件数据行数
     * @var int
     */
    protected $csvPerFile = 200000;

    /**
     * xls/xlsx单文件行数
     * @var int
     */
    protected $xlsPerFile = 10000;

    /**
     * 导出文件类型
     * @var string
     */
    protected $exportFileType = 'csv';


    public function exportList($params)
    {
        $params['_count'] = 1;
        $total = OilCardViceTrades::getTradesList($params);

        if ($total == 0) {
            throw new \RuntimeException('没有可以导出的数据', 2);
        }

        if($total <= $this->syncLimit){

        }else{

        }
    }

    public function syncExport($params, $total)
    {

    }

    private function asyncExport($params, $total)
    {

    }

    private function getDataByPage($params, $total, callable $callBack = \NULL)
    {
        if(!$total){
            throw new \RuntimeException('没有可供导出的数据', 2);
        }

        $pageSize = $this->pageSize;
        $totalPage = ceil($total / $pageSize);
        $_tmpData = [];
        for($i=0;$i<$totalPage;$i++){
            $params['skip'] = $i * $pageSize;
            $params['take'] = $pageSize;

            $record = OilCardViceTrades::getTradesList($params);
            $_tmpData       = array_merge($_tmpData, $record);

            //更新进度
            if($callBack){
                $processRate = (40 / $totalPage) * ($i + 1);
                $callBack($processRate);
            }
            unset($record);
        }

        return $_tmpData;
    }

    private function exportExcel()
    {

    }

    private function exportArchive(array $filesArr)
    {
        if (count($filesArr) > 1 || $this->exportFileType == 'csv') {
            Log::error('archive-begin1:' . var_export($filesArr, TRUE), [], 'jobOk');

            $fileName = date("Ymd_H_i_s");
            $realPath = APP_ROOT . DIRECTORY_SEPARATOR . 'www';
            $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'oil_account_assign' . DIRECTORY_SEPARATOR;
            if (!is_dir($filePath)) {//创建目录
                mkdir($filePath, 0777);
            }
            $zipFile     = $fileName . '.zip';
            $zipFilePath = $filePath . $zipFile;

            Log::error('archive-$zipFile:' . var_export($zipFile, TRUE), [], 'jobOk');
            Log::error('archive-$zipFilePath:' . var_export($zipFilePath, TRUE), [], 'jobOk');

            $zip = new \ZipArchive();
            if ($zip->open($zipFilePath, \ZIPARCHIVE::CREATE) !== TRUE) {
                exit("can't open " . $zipFilePath . " zipFile!");
            }

            for ($i = 0; $i < count($filesArr); $i++) {
                $zip->addFile($filesArr[ $i ], basename($filesArr[ $i ]));
            }
            $zip->close();

            for ($i = 0; $i < count($filesArr); $i++) {
                if (file_exists($filesArr[ $i ])) {
                    @unlink($filesArr[ $i ]);
                }
            }

            Log::error('archive-finished0:' . var_export($zipFilePath, TRUE), [], 'jobOk');
        } else {
            $zipFilePath = $filesArr[0];
        }


        return $zipFilePath;
    }

}