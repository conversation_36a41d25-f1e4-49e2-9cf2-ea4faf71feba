<?php
/**
 * Created by PhpStorm.
 * User: zzg
 * Date: 17-9-14
 * Time: 上午11:44
 */

namespace Fuel\Service;


use Dotenv\Environment\Adapter\ApacheAdapter;
use Framework\Cache;
use Framework\DingTalk\DingTalkAlarm;
use Framework\DingTalk\FeiShuNotify;
use Framework\Log;
use Fuel\Defines\CardFrom;
use Fuel\Defines\CardTradeConf;
use Fuel\Defines\OilCom;
use Fuel\Defines\PcodeConf;
use Fuel\Defines\TradesType;
use Fuel\Response;
use Models\OilAccountMoney;
use Models\OilCardVice;
use Models\OilCardViceTrades;
use Models\OilCardViceTradesException;
use Models\OilCardViceTradesExt;
use Models\OilCardViceTradesZBank;
use Models\OilCrmOrg;
use Models\OilErrorLogs;
use Models\OilFanliCalculate;
use Illuminate\Database\Capsule\Manager as Capsule;
use Framework\Job;
use Models\OilProvinces;
use Models\OilStation;
use SchedulerSDK\Machinery;

class TradesService
{
    /**
     * @title  返利计算
     * @param array $params
     * <AUTHOR>
     */
    public function fanliCalculate(array $params)
    {
        Log::error('入参--' . var_export($params, TRUE), [], 'fanliCal');
        $params['oil_comIn'] = OilCom::getFanLiCalculate();
        $params['tradeTypeList'] = TradesType::getFanLiTradeType();
        $params['card_fromIn'] = CardFrom::getFanliCardFrom();

        //OilCardViceTrades::executeNew('Fl20200205',$params);
        //exit;

        //校验
        $this->checkFanLiCalculate($params);


        global $app;
        if ( $app->config->scheduler->switch == 1 ) {
            (new \Jobs\TradesFanliCalculateJob($params))
                ->setTaskName('下游实体卡返利计算')
                ->onQueue('fanLiCalculate')
                ->setTries(3)
                ->dispatch();
        }else {

            (new Job())
                ->setTaskName('返利计算')
                ->pushTask(function () use ($params) {
                    //生成单号
                    $no = OilFanliCalculate::createOrderNo();
                    Log::error('$no--' . var_export($no, TRUE), [], 'fanliCal');

                    //缓存要使用的返利政策,可以增加定时任务,减少计算时的耗时
                    //OilCardViceTrades::cacheFanliPolicy($params);

                    //$ids = OilCardViceTrades::getTradeIds($params);
                    //OilCardViceTrades::execute($no, $params, $ids);
                    OilCardViceTrades::executeNew($no, $params);
                })
                ->channel('fanLiCalculate')
                ->exec();
        }
    }

    /**
     * @title 获取有消费的机构ID
     * @desc
     * @param $params
     * @return array
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     */
    private function getTradeOrg($params)
    {
        $data = OilCardViceTrades::getSqlObj($params, 'oil_card_vice_trades.org_id')
            ->groupBy('oil_card_vice_trades.org_id')->get();
        $orgIds = [];
        foreach ($data as $v) {
            $orgIds[] = $v->org_id;
        }

        return $orgIds;
    }

    public function checkFanLiCalculate($params = [])
    {
        //校验：是否有关联不出交易地点的数据
        $this->checkTradePlace($params);
        //校验：是否有关联不出油品类型的数据
        $this->checkOilType($params);
    }

    /**
     * 校验油站信息
     * @param $ids
     */
    private function checkTradePlace($params)
    {
        $fields = 'oil_card_vice_trades.vice_no, oil_card_vice_trades.oil_com, oil_card_vice_trades.trade_place, oil_card_vice_trades.trade_time';
        $data = OilCardViceTrades::getSqlObj($params, $fields)
            ->where('oil_card_vice_trades.trade_place', '!=', '')
            ->whereNotIn('oil_card_vice_trades.card_from', [40, 41])
            ->where('oil_card_vice_trades.is_fanli', '=', 0)
            ->where(function ($query) {
                $query->whereNull('oil_station.id')
                    ->orWhereNull('oil_station.regions_id');
            })->groupBy(Capsule::connection()->raw('oil_card_vice_trades.oil_com,oil_card_vice_trades.trade_place'))->get();
        if ($data) {
            $i = 0;
            $alertStr = '请先维护以下油站信息：<br>';
            foreach ($data as $val) {
                $tradeErr = [
                    'module' => '返利计算',
                    'data' => '未关联出交易地点',
                    'details' => '副卡号：' . $val->vice_no . ',交易时间：' . $val->trade_time . ',交易地点：' . $val->trade_place,
                    'createtime' => \helper::nowTime(),
                ];
                //添加错误日志
                OilErrorLogs::add($tradeErr);

                if ($i < 5) {
                    $oilCom = \Fuel\Defines\OilCom::getById($val->oil_com);
                    $alertStr .= $oilCom['oil_com'] . '--' . $val->trade_place . '<br>';
                }
                if ($i == 5) {
                    $alertStr .= '... ...';
                }
                $i++;
            }

            throw new \RuntimeException('加油站未维护' . $alertStr, 2);
        }
    }

    /**
     * 校验油品类型信息
     * @param $ids
     */
    private function checkOilType($params)
    {
        $fields = 'oil_card_vice_trades.vice_no, oil_card_vice_trades.trade_time, oil_card_vice_trades.oil_name';
        $data = OilCardViceTrades::getSqlObj($params, $fields)->where(function ($query) {
            $query->whereNull('oil_type_no.oil_type')->orWhere('oil_type_no.oil_type', '=', '');
        })->groupBy(Capsule::connection()->raw('oil_card_vice_trades.oil_name'))->get();
        if ($data) {
            $i = 0;
            $alertStr = '请先维护以下油品信息：<br>';
            foreach ($data as $val) {
                $typeErr = [
                    'module' => '返利计算',
                    'data' => '未关联出油品类型',
                    'details' => '副卡号：' . $val->vice_no . ',交易时间：' . $val->trade_time . ',油品：' . $val->oil_name,
                    'createtime' => \helper::nowTime(),
                ];
                //添加错误日志
                OilErrorLogs::add($typeErr);

                if ($i < 5) {
                    $alertStr .= $val->oil_name . '<br>';
                }
                if ($i == 5) {
                    $alertStr .= '... ...';
                }
                $i++;
            }

            throw new \RuntimeException('油品未维护' . $alertStr, 2);
        }
    }

    /**
     * @title    消费记录导入
     * @desc
     * @param array $data
     * @return array
     * @returns
     * array
     * @returns
     * @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    static public function importTrades(array $data)
    {
        $insertArr = [];
        $failedArr = [];
        $exceptionArr = [];
        $notExistArr = [];
        $isExistArr = [];

        if ($data) {
            foreach ($data as $v) {
                try {
                    //校验是否重复
                    $tradesExist = OilCardViceTrades::checkExistByOne($v);

                    if (!$tradesExist || count($tradesExist) == 0) {
                        $cardInfo = OilCardVice::getByViceNo(['vice_no' => $v['vice_no']]);
                        if ($cardInfo) {
                            $nowTime = date("Y-m-d H:i:s");
//                            if (in_array($v['trade_type'], ["加油", "IC卡消费", "油品联机消费", "油品脱机消费", "现金加油"])) {
                            if (in_array($v['trade_type'],TradesType::getCashTradeTypeArr())) {
                                $consume_type = '1';
                            } else if (in_array($v['trade_type'], TradesType::getJiFenTradeTypeArr())) {
                                $consume_type = '2';
                            } else if (in_array($v['trade_type'], ["积分圈存", "圈存", "圈提", "圈存(进)", "圈提(出)"])) {
                                $consume_type = '0';
                            }
                            $tradeTime = date("Y-m-d H:i:s", strtotime($v['trade_time']));
                            if (\strpos($tradeTime, '1970') !== \FALSE) {
                                throw new \RuntimeException('交易时间格式不正确', 2);
                            }
                            $v['trade_time'] = date("Y-m-d H:i:s", strtotime($v['trade_time']));

                            $v['unit'] = 1;
                            $v['main_no'] = $cardInfo->CardMain->main_no;
                            $v['oil_com'] = $cardInfo->CardMain->oil_com;
                            $v['truck_no'] = $cardInfo->truck_no;
                            $v['card_from'] = $cardInfo->card_from;
                            $v['org_id'] = $cardInfo->Org->id;
                            $v['org_name'] = $cardInfo->Org->org_name;
                            $v['main_operator_id'] = $cardInfo->CardMain->operators_id;
                            $v['org_operator_id'] = $cardInfo->Org->operators_id;
                            $v['active_region'] = $cardInfo->CardMain->active_region;
                            $v['regions_id'] = $cardInfo->CardMain->fanli_region;
                            //获取省份名称
                            $provinceInfo = OilProvinces::getById(['id' => $v['regions_id']]);
                            if ($provinceInfo) {
                                $v['regions_name'] = $provinceInfo->province;
                            }
                            $v['consume_type'] = $consume_type;
                            $v['fetch_time'] = $v['createtime'] = $v['updatetime'] = $nowTime;
                            $v['api_id'] = md5('zqx_import_' . \var_export($v, TRUE));

                            //油站
                            $stationInfo = OilStation::getByName($v['trade_place']);
                            Log::error('消费导入：' . \var_export($v, true), [], 'importTrades');
                            Log::error('消费导入-$stationInfo：' . \var_export($stationInfo, true), [], 'importTrades');
                            if ($stationInfo) {
                                $v['trade_place_provice_code'] = $stationInfo->province_code;
                                $v['trade_place_provice_name'] = $stationInfo->province_name;
                                $v['station_id'] = $stationInfo->id;
                            }
                            Log::error('消费导入：' . \var_export($v, true), [], 'importTrades');

//                            var_dump($cardInfo->toArray(), $v);exit;
                            if (in_array($v['trade_type'], TradesType::getTradesType())) {
                                if ((abs($v['trade_money']) > 60000 || $v['trade_money'] < 0) || ($v['trade_num'] < 0 || $v['trade_num'] > 60000)) {
                                    //查看trades表中 api_id 是否已经存在
                                    $existRes = OilCardViceTradesException::getByApiId($v['api_id']);
                                    if (!$existRes) {//不存在写入
                                        $exceptionArr[] = OilCardViceTradesException::add($v);
                                    }

                                    continue;
                                }
                            }

                            //释放开票额度
                            $v['receipt_remain'] = $v['trade_money'];
                            //不重复写入数据库
                            $insert = OilCardViceTrades::add($v);
                            if ($insert) {
                                $insertArr[] = $v['vice_no'] . '_' . $v['trade_time'];
                                OilCardVice::updateByViceNo([
                                    'vice_no' => $v['vice_no'],
                                    'trade_time' => $v['trade_time']
                                ]);
                            } else {
                                throw new \RuntimeException('添加失败', 2);
                            }
                        } else {
                            $notExistArr[] = $v['vice_no'] . '_' . $v['trade_time'];
                        }
                    } else {
                        $isExistArr[] = $v['vice_no'] . '_' . $v['trade_time'];
                    }
                } catch (\Exception $e) {
                    $failedArr[] = $v['vice_no'] . '_' . $v['trade_time'];
                }
            }
        } else {
            throw new \RuntimeException('无需要处理的数据', 2);
        }

        return ['success' => $insertArr, 'notExist' => $notExistArr, 'failed' => $failedArr, 'exception' => $exceptionArr, 'isExist' => $isExistArr];
    }

    /**
     * 2020年1月1号后的销售归属脚本
     */
    static public function tradeSaleAction()
    {
        //查询1月1号后的交易无销售信息的数据
        $trades = OilCardViceTrades::leftJoin('oil_card_vice_trades_ext', 'oil_card_vice_trades.id', '=', 'oil_card_vice_trades_ext.trades_id')
            //->whereNull('oil_card_vice_trades_ext.aplowneridname')
            ->where('oil_card_vice_trades.createtime', '>', '2020-01-01')
            ->where('oil_card_vice_trades.createtime', '<', '2020-02-01')
            ->where('oil_card_vice_trades.id', '>', 13226860)
            //->where('oil_card_vice_trades.id','<',13226860)
            ->selectRaw('oil_card_vice_trades_ext.id as ext_id,oil_card_vice_trades.id,oil_card_vice_trades_ext.aplowneridname,oil_card_vice_trades.org_id')
            //->Limit(100)
            ->get();

        if ($trades) {
            $error = $insertArr = $update_sql = [];
            foreach ($trades as $value) {
                //取机构对应的销售信息
                $saleInfo = OilCrmOrg::getByOrgId($value->org_id);
                if ($saleInfo) {
                    $data['trades_id'] = $value->id;
                    $data['aplownerid'] = $saleInfo->aplownerid;
                    $data['aplowneridname'] = $saleInfo->aplowneridname;
                    $data['sale_email'] = $saleInfo->saleEmail;
                    $data['oil_centercode'] = $saleInfo->oil_centercode;
                    $data['oil_centername'] = $saleInfo->oil_centername;
                    if ($value->ext_id) {
                        $data['id'] = $value->ext_id;
                        //$updatArr[] = $data;
                        //var_dump($data);
                        OilCardViceTradesExt::edit($data);
                    } else {
                        //var_dump($data);
                        //$insertArr[] = $data;
                        OilCardViceTradesExt::add($data);
                    }
                } else {
                    $error[] = $value->org_id;
                }
                echo $value->id . ',';
                Log::error($value->id . ',', [], 'tradeSaleAction');
            }

//            if($updatArr){
//                $update_sql = \helper::batchUpdateToSqlStrExt($updatArr,'oil_card_vice_trades_ext','id');
//                echo $update_sql;
//                Capsule::connection()->getPdo()->exec($update_sql);
//            }
//
//            if($insertArr){
//                $insert_sql = \helper::batchInsertToSqlStr($insertArr,'oil_card_vice_trades_ext',true);
//                echo $insert_sql;
//                Capsule::connection()->getPdo()->exec($insert_sql);
//            }

            if ($error) {
                $error_str = implode(',', array_unique($error));
                echo $error_str . '无crm客户档案';
            }
        }

        echo 'finish';

    }


    /**
     * 获取云卡订单详情
     * order_id:对应oil_card_vice_trades里的stream_no字段
     */
    public static function getDetail( array $params )
    {
        //$result = [];
        $stream_no = "";
        if(isset($params['trade_id']) && $params['trade_id']){
            $tradeInfo = OilCardViceTrades::getById(['id'=>$params['trade_id']]);
            if(!$tradeInfo || !$tradeInfo->stream_no){
                throw new \RuntimeException('交易流水不存在', 2);
            }
            if( !in_array($tradeInfo->pcode,PcodeConf::getAllPcode()) ){
                throw new \RuntimeException('运营商不合法', 2);
            }
            $stream_no = $tradeInfo->stream_no;
        }
        if(isset($params['stream_no']) && $params['stream_no']){
            $info = OilCardViceTradesZBank::getByStreamNo($params['stream_no'],false);
            if(!$info){
                throw new \RuntimeException('扣费订单不存在', 2);
            }
            $stream_no = $info->stream_no;
        }

        if(empty($stream_no)){
            throw new \RuntimeException('流水号不能为空', 2);
        }

        $list =  AdapterClient::getYunKaOrderDetail( ["order_id"=> $stream_no ] );
        Log::error("订单详情：".__METHOD__.var_export($params,true),[$list],"yunkaOrder_");

        /*if($list) {
            $details = [];
            $discount = $nums = 0;
            $name = $address = "";
            if($list->orderType == 1){ //维修保养
                $payMoney = $list->order->payMoney;
                $name = $list->order->shopName;
                $address = $list->order->address;
                $nums = 0;
                if(isset($list->servers) && isset($list->servers->items)) {
                    foreach ($list->servers->items as $_subItem) {
                        $details[] = [
                            "goodName" => $_subItem->servesCustomer ? $_subItem->servesCustomer : $_subItem->servesName,
                            "goodNums" => $_subItem->servesTimes,
                            "goodPrice" => number_format($_subItem->servesPrice, 2, ".", ""),
                            "goodMoney" => number_format($_subItem->servesPayMoney, 2, ".", ""),
                        ];
                        $nums += $_subItem->servesTimes;
                        foreach ($_subItem->servesItems as $_thirdItem) {
                            $third = [
                                "goodName" => $_thirdItem->customerSku ? $_thirdItem->customerSku : $_thirdItem->skuName,
                                "goodNums" => $_thirdItem->skuNum,
                                "goodPrice" => number_format($_thirdItem->skuPrice, 2, ".", ""),
                                "goodMoney" => number_format($_thirdItem->skuPayPrice, 2, ".", ""),
                            ];
                            $details[] = $third;
                            $nums += $_thirdItem->skuNum;
                        }
                    }
                }
                if(isset($list->coupon) && count($list->coupon)) {
                    foreach ($list->coupon as $_one) {
                        $discount += $_one->money;
                    }
                }
            }elseif($list->orderType == 2){ //优惠券
                $payMoney = $list->payMoney;
                $nums = $list->num;
                $name = "--";
                $address = "--";
                $discount = $list->reduceMoney;
                $details[] = [
                    "goodName" => $list->couponName ? $list->couponName : "",
                    "goodNums" => $list->num,
                    "goodPrice" => number_format($list->money,2,".",""),
                    "goodMoney" => '--',
                ];
            }

            $result['payMoney'] = number_format($payMoney,2,".","");
            $result['goodNums'] = $nums;
            $result['shopName'] = $name;
            $result['shopAddress'] = $address;
            $result['discount'] = number_format($discount,2,".","");
            $result['details'] = $details;
        }*/
        return $list;
    }



    /**
     * 修改消费返利档位
     */
    public static function updateFanliLevle($excelData = [])
    {
        $errMsg = [];
        $tradeIds = [];
        $updatSql = [];
        foreach ($excelData as $_line => $_item){
            if(empty($_item['fanli_level'])){
                $errMsg[$_line] = "第".$_line."行，返利档位为空";
                continue;
            }
            if( empty($_item['id']) ){
                $errMsg[$_line] = "第".$_line."行，id不能为空";
                continue;
            }
            if ( stripos($_item['fanli_level'],"档") === false ){
                $errMsg[$_line] = "第".$_line."行，档位格式不正确";
                continue;
            }
            $tradeIds[$_item['id']] = ['line'=>$_line,'level'=>$_item['fanli_level']];
        }

        if(count($tradeIds) > 0){
            $tradeList = OilCardViceTrades::getInfoByIds( ['ids'=>array_keys($tradeIds)] );
            if( count($tradeList) > 0){
                $hasTradeIds = [];
                foreach ($tradeList as $_trade){
                    $hasTradeIds[$_trade->id] = $_trade->id;
                    $line = $tradeIds[$_trade->id]['line'];
                    /*if($_trade->is_fanli > 0){
                        $errMsg[$line] = "第".$line."行，该消费已计算返利，不能修改档位";
                        continue;
                    }
                    if($_trade->is_open_invoice > 0){
                        $errMsg[$line] = "第".$line."行，该消费已开票，不能修改档位";
                        continue;
                    }*/
                    if( !in_array($_trade->oil_com,OilCom::getAllFirstList()) ){
                        $errMsg[$line] = "第".$line."行，该消费是实体卡消费，不支持修改返利档位";
                        continue;
                    }
                    $updatSql[] = "update oil_card_vice_trades set fanli_level = '".$tradeIds[$_trade->id]['level']."' where id = ".$_trade->id;
                }
            }
        }

        foreach ($tradeIds as $_id => $line){
            if( !isset($hasTradeIds[$_id]) ){
                $errMsg[$line['line']] = "第".$line['line']."行，库中无此id";
                continue;
            }
            $updatSql[] = "update oil_card_vice_trades set fanli_level = '".$line['level']."' where id = ".$_id;
            //更新计算临时表的数据G7WALLET-3849
            $updatSql[] = "update oil_upstream_rebate_trades_temp set fanli_level = '".$line['level']."' where trade_id = ".$_id;
        }

        if(count($errMsg) > 0){
            ksort($errMsg);
            $errStr = implode(",",$errMsg);
            throw new \RuntimeException($errStr, 2);
        }

        if(count($updatSql) > 0) {
            $batchSqlStr = implode(";", $updatSql);
            $data = Capsule::connection()->getPdo()->exec($batchSqlStr);
        }
        return true;
    }

    //标记剩余的返利扣减余额
    public static function markDiscountFanliRemain($params = [])
    {
        global $app;
        $connection = "online_only_read";
        if(API_ENV == 'dev'){
            $connection = "";
        }
        $condition['org_id'] = isset($params['org_id']) && $params["org_id"] ? $params['org_id'] : "";
        $condition['fanli_discount_remainLt'] = 50;
        $remainList = OilAccountMoney::getListByFilter($condition);
        $minArr = [];
        //标记 1<返利扣减余额<50的账户
        if(count($remainList) > 0){
            $nowMonth = date("Y-m-01")." 00:00:00";
            $lastMonth = date("Y-m-01",strtotime("-1 month",strtotime($nowMonth)))." 00:00:00";
            foreach ($remainList as $_item){
                if( bccomp(0,$_item->fanli_discount_remain,2) >= 0 ) {
                    continue;
                }
                //小于1元的账户，另行处理
                if( bccomp(1,$_item->fanli_discount_remain,2) > 0 ){
                    $minArr[] = $_item;
                    continue;
                }
                $tradeSql = "select oil_card_vice_trades.id,oil_card_vice_trades.use_fanli_money,oil_card_vice_trades.trade_money 
                    from oil_card_vice_trades FORCE INDEX (`idx_org_id_createtime`)
                    left join oil_type_no on oil_card_vice_trades.oil_name = oil_type_no.oil_no 
                    left join oil_card_vice on oil_card_vice.vice_no = oil_card_vice_trades.vice_no 
                    left join oil_card_main on oil_card_main.id = oil_card_vice.card_main_id 
                    where oil_card_vice_trades.org_id = ".$_item->org_id." and oil_card_vice_trades.is_fanli = 0 
                    and oil_card_vice_trades.is_open_invoice is null and oil_card_vice_trades.trade_type in (".TradesType::getCashAndSelfOilTradeTypeArr(true).")
                    and oil_card_vice_trades.oil_com not in (6,7,8) and oil_card_vice_trades.card_from not in (40,41)
                    and oil_card_vice_trades.cancel_sn is null
                    and if (oil_card_main.operators_id = 3, oil_card_vice_trades.createtime < '2020-03-02', if (oil_card_vice_trades.createtime > '2018-03-01' , oil_card_main.is_open_invoice = 10 , 1 = 1) )
                    and oil_card_vice_trades.createtime < '".$nowMonth."' and oil_card_vice_trades.createtime >= '".$lastMonth."'";
                //'加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油','消费','优惠消费'
                if( in_array(substr($_item->orgcode,0,6),$app->config->customer->orgCodeGas) ){
                    $tradeSql .= " and oil_type_no.oil_type = 5 and oil_card_vice_trades.oil_com in (".implode(",",OilCom::getAllFirstList()).")";
                }
                $tradeSql .= " order by oil_card_vice_trades.trade_money desc limit 1";
                $data = Capsule::connection($connection)
                    ->select($tradeSql);
                if(count($data) > 0){
                    $tradeId = $data[0]->id;
                    $use_fanli = $data[0]->use_fanli_money;
                    $trade_money = $data[0]->trade_money;
                    Log::error("money:".$tradeSql,['use_fanli'=>$use_fanli+$_item->fanli_discount_remain,"trade_money"=>$trade_money],"markDiscount_");
                    if( !empty($tradeId) && bccomp($trade_money,($use_fanli + $_item->fanli_discount_remain),2) > 0 ) {
                        $updateTradeSql = "update oil_account_money set fanli_discount_remain = fanli_discount_remain - ".$_item->fanli_discount_remain."  where id = ".$_item->id.";";
                        $updateTradeSql .= "update oil_card_vice_trades set use_fanli_money = use_fanli_money + " . $_item->fanli_discount_remain . ",receipt_remain = trade_money - use_fanli_money where id = " . $tradeId.";";
                        Log::error("updateSql:".$updateTradeSql,['org_id'=>$_item->org_id,"discount_fanli"=>$_item->fanli_discount_remain],"markDiscount_");
                        //echo $updateTradeSql;exit;
                        Capsule::connection()->getPdo()->exec($updateTradeSql);
                    }
                }
            }
        }

        //标记返利扣减余额<1的账户
        if(count($minArr) > 0){
            foreach ($minArr as $_min){
                $tradeFanliSql = "select oil_card_vice_trades.id,oil_card_vice_trades.use_fanli_money,oil_card_vice_trades.trade_money 
                    from oil_card_vice_trades FORCE INDEX (`idx_org_id_createtime`)
                    left join oil_type_no on oil_card_vice_trades.oil_name = oil_type_no.oil_no 
                    left join oil_card_vice on oil_card_vice.vice_no = oil_card_vice_trades.vice_no 
                    left join oil_card_main on oil_card_main.id = oil_card_vice.card_main_id 
                    where oil_card_vice_trades.org_id = ".$_min->org_id." and oil_card_vice_trades.is_fanli = 0 
                    and oil_card_vice_trades.is_open_invoice is null and oil_card_vice_trades.trade_type in (".TradesType::getCashAndSelfOilTradeTypeArr(true).")
                    and oil_card_vice_trades.oil_com not in (6,7,8) and oil_card_vice_trades.card_from not in (40,41)
                    and oil_card_vice_trades.cancel_sn is null
                    and if (oil_card_main.operators_id = 3, oil_card_vice_trades.createtime < '2020-03-02', if (oil_card_vice_trades.createtime > '2018-03-01' , oil_card_main.is_open_invoice = 10 , 1 = 1) )
                    and oil_card_vice_trades.createtime < '".$nowMonth."' and oil_card_vice_trades.createtime >= '".$lastMonth."'";
                //'加油', 'IC卡消费', '油品脱机消费', '油品联机消费', '现金加油', '撬装加油','消费','优惠消费'
                if( in_array(substr($_min->orgcode,0,6),$app->config->customer->orgCodeGas) ){
                    $tradeFanliSql .= " and oil_type_no.oil_type = 5 and oil_card_vice_trades.oil_com in (".implode(",",OilCom::getAllFirstList()).")";
                }
                $tradeFanliSql .= " and oil_card_vice_trades.use_fanli_money > 0 order by oil_card_vice_trades.use_fanli_money desc limit 1";
                 $fanliTrades = Capsule::connection($connection)
                    ->select($tradeFanliSql);
                 if( count($fanliTrades) > 0 ){
                     $tradeId = $fanliTrades[0]->id;
                     $trade_money = $fanliTrades[0]->trade_money;
                     Log::error("secord-money:".$tradeSql,['use_fanli'=>$use_fanli+$_min->fanli_discount_remain,"trade_money"=>$trade_money],"markDiscount_");
                     if(!empty($tradeId)){
                         $updateTradeSql = "update oil_account_money set fanli_discount_remain = 0 where id = ".$_min->id.";";
                         $updateTradeSql .= "update oil_card_vice_trades set use_fanli_money = use_fanli_money + " . $_min->fanli_discount_remain . ",receipt_remain = trade_money - use_fanli_money where id = " . $tradeId.";";
                         Log::error("secord-updateSql:".$updateTradeSql,['org_id'=>$_min->org_id,"discount_fanli"=>$_min->fanli_discount_remain],"markDiscount_");
                         //echo $updateTradeSql;exit;
                         Capsule::connection()->getPdo()->exec($updateTradeSql);
                     }
                 }
            }
        }
        return true;
    }

    public static function fetchPdaTradeFlowDataNeedFix()
    {
        // createtime < 2021-09-02 16:27:10
        $noOwnStation = "'JT5S3K','M2DST4','23ADSB','B2UUDD','KL3VLA','UUSCVV','SCSATJ','KMT3K3','3S44JL','4AUC33','DK3AC4','32LJ2K','BC2BJU','ASTS3V','B4DJJJ','SD5S25','SCK3KS','D5A5U2','JDA5M5','VDKMMU','5BJDM5','3AJCBS','C23D5L','UBLAM3','JDLMC5','KDUL5A','KT2BTM','JB4UAL','42MMKU'";
        $sql = "SELECT id, api_id, cancel_sn, vice_no, trade_money, station_code, createtime FROM oil_card_vice_trades 
                WHERE station_code IS NOT NULL AND ( (cancel_sn IS NULL AND SUBSTR(api_id, 1, 4) != '2021') OR (cancel_sn is NOT NULL and SUBSTR(api_id, 1, 4) != '2021' and SUBSTR(api_id, 1, 2) != 'd_')) 
	            AND createtime >= '2021-07-22 15:26:51' 
                AND createtime < '2021-09-02 16:27:10' AND station_code not in (". $noOwnStation .") order by createtime asc";

        return Capsule::connection()
            ->select($sql, [], false);
    }

    /**
     * 获取撤销消费交易记录
     * @param $cancelSn
     * @param $id
     * @return array
     */
    public static function fetchCancelTradeFlowData($cancelSn, $id)
    {
        $sql = "select id, api_id, cancel_sn, vice_no, trade_money, station_code, createtime FROM oil_card_vice_trades
                where cancel_sn = '". $cancelSn ."' and id != ".$id;

        return Capsule::connection()
            ->select($sql, [], false);
    }

    /**
     * pushTradesToAne
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function pushTradesToAne($orgRoot, $startTime = '', $endTime = '', $idEqual = '')
    {
        $cacheName = __METHOD__ . ":" . $orgRoot;
        $lastId = Cache::get($cacheName);
        if ((!$startTime || !$endTime) && !$lastId) {
            (new DingTalkAlarm())->alarmToGroup('安能数据推送：下发任务失败', "没有任何推送条件", [], true, false);
            throw  new \RuntimeException("安能：没有任何推送条件");
        }

        if ($startTime && $endTime && (strtotime($endTime) - strtotime($startTime)) > 86400 * 3) {
            (new DingTalkAlarm())->alarmToGroup('安能数据推送：下发任务失败', "一次不能超过3天范围", [], true, false);
            throw  new \RuntimeException("安能：一次不能超过3天范围");
        }

        $sqlObj = OilCardViceTrades::leftJoin('oil_org', 'oil_card_vice_trades.org_id', '=', 'oil_org.id')
            ->where("oil_org.orgcode", "like", $orgRoot . "%")
            ->whereIn("oil_card_vice_trades.trade_type", TradesType::getCashAndSelfOilTradeTypeArr())
            ->whereNotIn("oil_card_vice_trades.card_from", [40, 41])
            ->selectRaw('oil_card_vice_trades.truck_no as truckLicenseNumber,
            (case when oil_card_vice_trades.cancel_sn is null then "SUCCESS" else "REFUND_SUCCESS" end) as paymentStatus,
            oil_card_vice_trades.id as orderSn, 
            oil_card_vice_trades.id as tripartiteOrderNumber, 
            oil_card_vice_trades.vice_no as truckerName,
            oil_card_vice_trades.vice_no as cardNumber,
            oil_card_vice_trades.qz_drivertel as truckerMobile,
            oil_card_vice_trades.trade_time as orderTime,
            oil_card_vice_trades.oil_name as skuName,
            oil_card_vice_trades.trade_num as litre,
            oil_card_vice_trades.trade_price as price,
            oil_card_vice_trades.trade_place as stationName,
            oil_card_vice_trades.trade_money as paymentAmount');

        if ($startTime) {
            $sqlObj = $sqlObj->where('oil_card_vice_trades.createtime', '>=', $startTime);
        }
        if ($endTime) {
            $sqlObj = $sqlObj->where('oil_card_vice_trades.createtime', '<=', $endTime);
        }

        if ($lastId && (!$idEqual && !$startTime && !$endTime)) {
            $sqlObj = $sqlObj->where("oil_card_vice_trades.id", ">", $lastId)->limit(1000);
        }
        if ($idEqual) {
            $sqlObj = $sqlObj->where("oil_card_vice_trades.id", "=", $idEqual);
        }

        $data = $sqlObj->get();

        if ($data) {
            $maxId = $lastId;
            $machinerySDK = new Machinery();
            foreach ($data as $v) {
                $v->orderSn = strval($v->orderSn);
                $v->tripartiteOrderNumber = strval($v->tripartiteOrderNumber);
                try {
                    $res = $machinerySDK->sendTask("normal", [
                        'name' => "ane56.pushTrade",
                        'routing_key' => "PUSH_TO_ANE",
                        'args' => [
                            [
                                'Name' => 'pushType',
                                'Type' => 'string',
                                'Value' => "pushTrade"
                            ],
                            [
                                'Name' => 'params',
                                'Type' => 'string',
                                'Value' => json_encode($v)
                            ]
                        ],
                        'retry_count' => 3,
                    ]);

                    $result = \json_decode($res);
                    if (!$result || !isset($result->status) || $result->status != 0 || !isset($result->data) || !$result->data->Signature->UUID) {
                        Log::error(__METHOD__ . ":异常", ['data' => $v, 'err' => $result], 'pushDataToAne');
                        throw new \RuntimeException("下发任务失败", 505);
                    }
                } catch (\Exception $e) {
                    Log::error(__METHOD__ . ":异常", ['data' => $v, 'err' => strval($e)], 'pushDataToAne');
                    (new DingTalkAlarm())->alarmToGroup('安能数据推送：下发任务失败', json_encode($v), [], true, false);
                    throw new \RuntimeException($e);
                }

                if ($v->orderSn > $maxId) {
                    $maxId = $v->orderSn;
                }

                Cache::put($cacheName, $maxId, 86400 * 30);
            }

        }

        return $data;
    }

    static public function getTradeType($trade_type = "")
    {
        $trade_type_arr = explode(',', $trade_type);
        $trade_type_list = [];
        foreach ($trade_type_arr as $value) {
            $value == '电子加油' && $value = '撬装加油';
            if ($value == '交易撤销') {
                $trade_type_list[] = $value;
            } else {
                $trade_type_list = array_merge($trade_type_list, TradesType::gettradesZArr($value));
            }
            if($value == CardTradeConf::RESERVE_TRADE_TYPE){
                array_push($trade_type_list,CardTradeConf::RESERVE_TRADE_TYPE);
                array_push($trade_type_list,CardTradeConf::RESERVE_CANCEL_TRADE_TYPE);
            }
            if($value == CardTradeConf::RESERVE_CANCEL_TRADE_TYPE){
                array_push($trade_type_list,CardTradeConf::RESERVE_CANCEL_TRADE_TYPE);
            }
        }
        Log::error('trades-sql:' . var_export($trade_type_list, TRUE), [], 'sql-trade');
        return array_unique($trade_type_list);
    }

    static public function editUpOperator($params = [])
    {
        $idArr = explode(",",$params['ids']);
        if(count($idArr) == 0){
            throw new \RuntimeException("数据不能为空", 4);
        }

        $ids = implode(",",$idArr);
        $data = Capsule::connection("online_only_read")
            ->select("SELECT MAX(createtime) as max_createtime,MIN(createtime) as min_createtime FROM oil_card_vice_trades
                WHERE id in (".$ids.");");

        print_r($data);
        if(count($data) == 0){
            throw new \RuntimeException("数据不存在", 5);
        }

        $sql[] = "update oil_card_vice_trades_ext set operator_id = ".$params['after_operator_id']." where trades_id in (".$ids.")";
        $sql[] = "update oil_trades set up_operator_id = ".$params['after_operator_id']." where trades_id in (".$ids.")";
        Log::error("modifyUpOperator-params:",[$ids,$sql],"modifyUpOperator_");

        print_r($sql);
        Capsule::connection()->getPdo()->exec(implode(";",$sql));

        $condition["start_time"] = date("Y-m-d",strtotime($data[0]->min_createtime));
        $condition["end_time"] = date("Y-m-d",strtotime("+1 day",strtotime($data[0]->max_createtime)));
        $condition["up_operator_id"] = $params['before_operator_id'];
        Log::error("modifyUpOperator-condition:",[$condition],"modifyUpOperator_");
        $res = (new FeiShuNotify())->Send($condition,"foss-task/oil_card_vice_trades/sendTask");
        $condition["up_operator_id"] = $params['after_operator_id'];
        $res = (new FeiShuNotify())->Send($condition,"foss-task/oil_card_vice_trades/sendTask");

        return $params;
    }
}