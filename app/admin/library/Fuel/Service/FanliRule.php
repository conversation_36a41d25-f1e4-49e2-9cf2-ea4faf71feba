<?php

namespace Fuel\Service;

use Illuminate\Database\Capsule\Manager as Capsule;
use Framework\Log;
use Models\OilFanliRuleCalDetails;

class FanliRule
{
    /**
     * @title   新版返利计算
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @param array $filesArr
     * @param string $excelType
     * @return mixed|string
     * @returns
     * mixed|string
     * @returns
     */
    static public function ruleCalculate(array $tradeInfo)
    {
        //生成单号
        $no = \Models\OilFanliRuleCal::createOrderNo();
        //开启事务
        Capsule::connection()->getPdo()->beginTransaction();
        try {
            $batchUp = $calDetail = array();
            //获取机构通用政策
            $ruleNormal = \Models\OilOrgRuleRelations::getRuleIds(['org_id' => -1]);
            $total_jifen = 0;
            $total_money = 0;
            foreach ($tradeInfo as $trade) {
                $trade_id[] = $trade->id;
                if (!$trade->regions_id && !in_array($trade->oil_com, [20, 21])) {
                    Log::error("消费记录:" . $trade->id . "未取得消费记录的消费地区", [], "fanliRule_");
                    continue;
                }
                $isNormal = true;
                //todo 机构有特殊规则优先使用特殊规则，否则使用通用规则
                $org_id = $trade->org_id;
                $rule = \Models\OilOrgRuleRelations::getRuleIds(['org_id' => $org_id]);
                $ruleID = $ruleNormal->toArray();
                if (count($rule) == 0) {
                    $isNormal = true;
                }else{
                    $ruleSpecial = $rule->toArray();
                    $result = self::calculateRule($no,$trade,$ruleSpecial,true);
                    if($result < 0 && is_numeric($result)) {
                        $isNormal = true;
                    }else {
                        if(is_numeric($result)){
                            $isNormal = true;
                        }else {
                            $isNormal = false;
                            $batchUp[] = $result['fanli'];
                        }
                    }
                }

                if($isNormal) {
                    $result = self::calculateRule($no, $trade, $ruleID);
                    if ($result > 0 && is_numeric($result)) {
                        continue;
                    }else {
                        $batchUp[] = $result['fanli'];
                    }
                }

                if($result['fanli'] > 0){
                    $tmpFanli = $result['fanli'];
                    //返利总额
                    $total_jifen += $tmpFanli['fanli_jifen'];
                    $total_money += $tmpFanli['fanli_money'];

                    //组装返利计算详情数据
                    $calDetail[$trade->oil_com][$trade->org_id][$trade->oil_type_id][$trade->regions_id][$trade->main_id]['fanli_jifen'] += $tmpFanli['fanli_jifen'];
                    $calDetail[$trade->oil_com][$trade->org_id][$trade->oil_type_id][$trade->regions_id][$trade->main_id]['fanli_money'] += $tmpFanli['fanli_money'];
                }
            }

            $calData['no'] = $no;
            $calData['status'] = 1;
            $calData['audit_status'] = 3;
            $calData['fanli_total_money'] = $total_money;
            $calData['fanli_total_jifen'] = $total_jifen;
            $calData['createtime'] = \helper::nowTime();
            $calData['updatetime'] = \helper::nowTime();
            $calInfo = \Models\OilFanliRuleCal::add($calData);

            $logItem['module_name'] = "oil_fanli_rule_cal";
            $logItem['third_id'] = $calInfo->id;
            $logItem['operator_content'] = "返利计算";
            $logItem['complete_status'] = "未审核";
            $logItem['source'] = 1;
            \Models\OilSysOperatorLogs::add($logItem);

            if(count($calDetail) > 0) {
                $last_item = OilFanliRuleCalDetails::packCalDetail($calDetail);
                //todo 返利计算详情
                OilFanliRuleCalDetails::batchAdd($last_item);
            }


            if(count($batchUp) > 0) {
                \Models\OilCardViceTrades::updateBatch('oil_card_vice_trades', $batchUp);
            }

            if(count($trade_id) > 0){
                \Models\OilCardViceTrades::updateIsFanliRule($no, implode(",", $trade_id));
            }
            //事务提交
            Capsule::connection()->getPdo()->commit();
        }catch (\Exception $e) {
            //事务回滚
            Capsule::connection()->getPdo()->rollBack();
            Log::error("异常:".$e->getMessage()."code:".$e->getCode(),[],"fanliRule_");
            throw  new \RuntimeException($e->getMessage(), $e->getCode());
        }

    }

    /**
     * 根据返利规则，进行返利
     * @param $val
     * @param $accountInfo
     * @return array
     */
    static public function calculateRule($no,$trade,$ruleID,$isSpecial=false)
    {
        $keyItem = array(0 => "oil_com", 1 => "oil_vice_provider", 2 => "main_id", 3 => "region_id", 4 => "station_opreater_id", 5 => "station_id",6 => "oil_type");
        $org_id = $trade->org_id;
        $condition['_export'] = 1;
        $condition['rule_object'] = 1;
        $condition['is_del'] = 0;
        $condition['rule_nos'] = $ruleID;
        $condition['expire_time'] = $trade->createtime;
        $ruleList = \Models\OilFanliRule::getList($condition);
        $enableCal = 1;
        if (count($ruleList) == 0) {
            Log::error("机构:" . $org_id . "关联的返利规则不存在", [], "fanliRule_");
            $enableCal = 0;
        }
        $ruleData = $ruleList->toArray();

        foreach ($ruleData as $item) {
            if (floatval($trade->trade_money) >= $item->oil_money_limit && floatval($trade->trade_num) >= $item->oil_amount_limit) {
                $fanliItem['fanli_way'] = $item['fanli_way'];
                $fanliItem['fanli_type'] = $item['fanli_type'];
                $fanliItem['fanli_coe'] = $item['fanli_coe'];
                $fanliItem['fanli_money'] = $item['fanli_money'];
                $fanliItem['rule_id'] = $item['rule_id'];
                $fanliItem['rule_detail_id'] = $item['id'];
                $fanliItem['fanli_min_money'] = $item['fanli_min_money'];
                $fanliItem['fanli_mode'] = $item['fanli_mode'];
                $fanliItem['step_fanli_data'] = $item['step_fanli_data'];
                $fanliItem['coe_unit'] = $item['coe_unit'];
                $fanliItem['add_fanli_edu'] = $item['add_fanli_edu'];
                $fanliItem['start_time'] = $item['start_time'];
                $fanliItem['end_time'] = $item['end_time'];
                $regionValue[$item[$keyItem[0]]][$item[$keyItem[1]]][$item[$keyItem[2]]][$item[$keyItem[3]]][$item[$keyItem[4]]][$item[$keyItem[5]]][$item[$keyItem[6]]] = $fanliItem;
            }
        }

        $allKey = -1;
        //油卡类型
        if (array_key_exists($trade->oil_com, $regionValue)) {
            $supply = $regionValue[$trade->oil_com];
        } else {
            if (array_key_exists($allKey, $regionValue)) {
                $supply = $regionValue[-1];
            } else {
                $enableCal = 0;
                Log::error("消费记录" . $trade->id . ",没有匹配到油卡类型规则", [], "fanliRule_");
            }
        }
        //油卡供应商
        if (array_key_exists($trade->supplyer_id, $supply)) {
            $main_info = $supply[$trade->supplyer_id];
        } else {
            if (array_key_exists($allKey, $supply)) {
                $main_info = $supply[-1];
            } else {
                $enableCal = 0;
                Log::error("消费记录" . $trade->id . ",没有匹配到油卡供应商规则", [], "fanliRule_");
            }
        }

        //主卡id
        if (array_key_exists($trade->main_id, $main_info)) {
            $region_info = $main_info[$trade->main_id];
        } else {
            if (array_key_exists($allKey, $main_info)) {
                $region_info = $main_info[-1];
            } else {
                $enableCal = 0;
                Log::error("消费记录" . $trade->id . ",没有匹配到主卡规则", [], "fanliRule_");
            }
        }
        //消费地区
        if (array_key_exists($trade->regions_id, $region_info)) {
            $operation = $region_info[$trade->regions_id];
        } else {
            if (array_key_exists($allKey, $region_info)) {
                $operation = $region_info[-1];
            } else {
                $enableCal = 0;
                Log::error("消费记录" . $trade->id . ",没有匹配到返利地区规则", [], "fanliRule_");
            }
        }
        //油站运营商
        if (array_key_exists($trade->station_operators, $operation)) {
            $station = $operation[$trade->station_operators];
        } else {
            if (array_key_exists($allKey, $operation)) {
                $station = $operation[-1];
            } else {
                $enableCal = 0;
                Log::error("消费记录" . $trade->id . ",没有匹配到油站运营商规则", [], "fanliRule_");
            }
        }
        //油站id
        if (array_key_exists($trade->station_id, $station)) {
            $oil_type = $station[$trade->station_id];
        } else {
            if (array_key_exists($allKey, $station)) {
                $oil_type = $station[-1];
            } else {
                $enableCal = 0;
                Log::error("消费记录" . $trade->id . ",没有匹配到油站规则", [], "fanliRule_");
            }
        }
        //油品类型
        if (array_key_exists($trade->oil_type_id, $oil_type)) {
            $fanli = $oil_type[$trade->oil_type_id];
        } else {
            if (array_key_exists($allKey, $oil_type)) {
                $fanli = $oil_type[-1];
            } else {
                $enableCal = 0;
                Log::error("消费记录" . $trade->id . ",没有匹配到油品规则", [], "fanliRule_");
            }
        }

        Log::error("消费记录数据:" . json_encode($trade), [], "fanliRule_");

        Log::error("返利规则单号:" . $no, [], "fanliRule_");
        Log::error("消费记录规则:" . json_encode($fanli), [], "fanliRule_");

        $returnResult =array();
        if ($enableCal == 1) {

            //按金额返
            if ($fanli['fanli_type'] == 1) {
                $returnResult = self::fanliTypeByMoney($fanli, $trade);
            }
            //按加油量返
            if ($fanli['fanli_type'] == 2) {
                $returnResult = self::fanliTypeByJifen($fanli, $trade);
            }
            //按阶梯返利返
            //交易的单价，是否大于等于免惠最低价；只有大于等于免惠最低价时，才触发计算返利
            if (in_array(intval($fanli['fanli_type']), [3, 4]) && $trade->trade_price > $fanli['fanli_min_money']) {
                //todo 处理1号卡特殊返利
                if( in_array($fanli['fanli_mode'],[1,2]) ){
                    $returnResult = self::specialStepRule($fanli, $trade);
                }else{
                    $returnResult = self::fanliTypeByStep($fanli, $trade);
                }
            }

            $result = $returnResult['result'];

            $batchUp = [
                'id' => $trade->id,
                'fanli_no' => $no,
                'fanli_money' => $result['fanli_money'] ? $result['fanli_money'] : 0,
                'fanli_jifen' => $result['fanli_jifen'] ? $result['fanli_jifen'] : 0,
                'policy_id' => empty($fanli['rule_id']) ? NULL : $fanli['rule_id'],
                'fanli_way' => empty($fanli['fanli_way']) ? NULL : $fanli['fanli_way'],
                'is_fanli' => 2,//已算(未审核)
                'updatetime' => \helper::nowTime(),
            ];
            return array("fanli"=>$batchUp);
        } else {
            if($isSpecial) {
                return -1;
            }else{
                return 1;
            }
        }
    }

    /**
     * 处理"按金额"类型返利
     * @param $val
     * @param $accountInfo
     * @return array
     */
    static public function fanliTypeByMoney($fanli,$trade)
    {
        $result = [];
        //返现金
        if ($fanli['fanli_way'] == 1) {
            /****************** 处理现金返利循环问题 ************************/
            $result['fanli_money']     = floatval($trade->trade_money) * floatval($fanli['fanli_coe']) * 0.01;
        }
        //返积分
        if ($fanli['fanli_way'] == 2) {
            $result['fanli_jifen'] = floatval($trade->trade_money) * floatval($fanli['fanli_coe']) * 0.01;
        }
        return ['result' => $result];
    }
    /**
     * 处理"按加油量"类型返利
     * @param $val
     * @return array
     */
    static public function fanliTypeByJifen($fanli,$trade)
    {
        $result      = [];
        $checkStatus = 0;//1现金，2积分，3现金+积分
        //返现金
        if ($fanli['fanli_way'] == 1) {
            $result['fanli_money']     = floatval($trade->trade_num) * floatval($fanli['fanli_money']);
        }
        //返积分
        if ($fanli['fanli_way'] == 2) {
            $result['fanli_jifen'] = floatval($trade->trade_num) * floatval($fanli['fanli_money']);
        }
        return ['result' => $result];
    }

    /**
     * 处理1号卡特殊返利规则
     * @param $val
     * @param
     * @return array
     */
    static public function specialStepRule($fanli,$trade)
    {

        $result = [];
        $stepData = json_decode($fanli['step_fanli_data']);

        $fanli_coe = 0;
        //返现金
        if ($fanli['fanli_way'] == 1) {
            /****************** 处理现金返利循环问题 ************************/
            $tradeNum = ($trade->trade_money)/$trade->trade_price;
            if( $tradeNum >= $stepData['fanli_level1_gt'] && $tradeNum < $stepData['fanli_level1_le'] ){
                $fanli_coe = $stepData['fanli_coe_level1'];
            }else{
                //1号卡特殊周期阶梯返利 按照机构累计规则生效期间内的累计加油量
                $field = 'SUM((trade_money-use_fanli_money)/trade_price) tradenumtotal';
                $tradeInfo = \Models\OilCardViceTrades::getSpecialRuleStep($trade->org_id, $fanli['start_time'], $fanli['end_time'], $field);
                if($tradeInfo['tradenumtotal'] >= $stepData['fanli_level1_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level1_le']){
                    $fanli_coe = $stepData['fanli_money_level1'];
                }elseif($tradeInfo['tradenumtotal'] >= $stepData['fanli_level2_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level2_le']){
                    $fanli_coe = $stepData['fanli_money_level2'];
                }elseif($tradeInfo['tradenumtotal'] >= $stepData['fanli_level3_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level3_le']){
                    $fanli_coe = $stepData['fanli_money_level3'];
                }elseif($tradeInfo['tradenumtotal'] >= $stepData['fanli_level4_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level4_le']){
                    $fanli_coe = $stepData['fanli_money_level4'];
                }elseif($tradeInfo['tradenumtotal'] >= $stepData['fanli_level5_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level5_le']){
                    $fanli_coe = $stepData['fanli_money_level5'];
                }
            }
            $result['fanli_money'] = $trade->trade_money * $fanli_coe * 0.01;
        }
        //返积分
        if ($fanli['fanli_way'] == 2) {
            $tradeNum = ($trade->trade_money)/$trade->trade_price;
            if( $tradeNum >= $stepData['fanli_level1_gt'] && $tradeNum < $stepData['fanli_level1_le'] ){
                $fanli_coe = $stepData['fanli_coe_level1'];
            }else{
                //1号卡特殊周期阶梯返利 按照机构累计规则生效期间内的累计加油量
                $field = 'SUM((trade_money-use_fanli_money)/trade_price) tradenumtotal';
                $tradeInfo = \Models\OilCardViceTrades::getSpecialRuleStep($trade->org_id, $fanli['start_time'], $fanli['end_time'], $field);
                if($tradeInfo['tradenumtotal'] >= $stepData['fanli_level1_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level1_le']){
                    $fanli_coe = $stepData['fanli_money_level1'];
                }elseif($tradeInfo['tradenumtotal'] >= $stepData['fanli_level2_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level2_le']){
                    $fanli_coe = $stepData['fanli_money_level2'];
                }elseif($tradeInfo['tradenumtotal'] >= $stepData['fanli_level3_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level3_le']){
                    $fanli_coe = $stepData['fanli_money_level3'];
                }elseif($tradeInfo['tradenumtotal'] >= $stepData['fanli_level4_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level4_le']){
                    $fanli_coe = $stepData['fanli_money_level4'];
                }elseif($tradeInfo['tradenumtotal'] >= $stepData['fanli_level5_gt'] && $tradeInfo['tradenumtotal'] < $stepData['fanli_level5_le']){
                    $fanli_coe = $stepData['fanli_money_level5'];
                }
            }
            $result['fanli_jifen'] = $trade->trade_money * $fanli_coe * 0.01;
        }

        return ['result' => $result];

    }


    /**
     * 处理"按阶梯"类型返利
     * @param $val
     * @param $accountInfo
     * @return array
     */
    static public function fanliTypeByStep($fanli,$trade)
    {
        $result = [];
        //返现金
        if ($fanli['fanli_way'] == 1) {
            /****************** 处理现金返利循环问题 ************************/
            $result['fanli_money']     = self::getStepFanliMoney($fanli,$trade,$trade->trade_money);
        }
        //返积分
        if ($fanli['fanli_way'] == 2) {
            $result['fanli_jifen'] = self::getStepFanliMoney($fanli,$trade,$trade->trade_money);
        }

        return ['result' => $result];
    }

    /**
     * @title  获取阶梯返利金额
     * <AUTHOR>
     * @param $info
     * @param $tradeMoney
     * @return int
     */
    static private function getStepFanliMoney($fanli,$trade,$tradeMoney)
    {
        if($tradeMoney == 0){
            return 0;
        }
        Log::error('开始阶梯返利计算...', [], 'fanliRule_');

        $sessionTime = \Models\OilCardViceTrades::getSessionTime($trade->createtime);

        Log::error('$sessionTime--' . var_export($sessionTime, TRUE), [], 'fanliRule_');
        Log::error('$tradeMoney--' . $tradeMoney, [], 'fanliRule_');
        $stepFanliData = json_decode($fanli['step_fanli_data'], TRUE);

        //统计该条消费记录的累计消费,方案1，按卡累计
        $field = 'SUM(trade_money - use_fanli_money) trademoneytotal,SUM((trade_money-use_fanli_money)/trade_price) tradenumtotal';
        $tradeInfo = \Models\OilCardViceTrades::getRuleStep($trade->vice_no,$sessionTime[0],$trade->createtime,$field);

        Log::error('累计消费信息--' . var_export($tradeInfo->toArray(), TRUE), [], 'fanliRule_');

        $totalTrade = 0;
        if ($fanli['fanli_type'] == 3) {//现金阶梯返利（元）
            $totalTrade = $tradeInfo->trademoneytotal + $tradeMoney;
        } else if ($fanli['fanli_type'] == 4) {//油量阶梯返利（升）
            $tradeMoney = ($tradeMoney - $trade->use_fanli_money) / $trade->trade_price;//转换为加油量
            $totalTrade = $tradeInfo->tradenumtotal + $tradeMoney;
        }

        $fanliMoney = self::getFanliMoneyForStep($fanli,$trade,$tradeMoney,$totalTrade,$stepFanliData);

        Log::error('累计消费--' . var_export($totalTrade, TRUE), [], 'fanliRule_');
        Log::error('本次消费--' . var_export($tradeMoney, TRUE), [], 'fanliRule_');
        Log::error('返利金额--' . var_export($fanliMoney, TRUE), [], 'fanliRule_');

        return $fanliMoney;
    }
    /**
     * @title 获取阶梯返利金额
     * <AUTHOR>
     * @param object $info
     * @param float $tradeMoney 交易金额 OR 加油量
     * @param float $totalTrade
     * @param array $stepFanliData
     * @return int|mixed
     */
    static private function getFanliMoneyForStep($fanli,$trade,$tradeMoney,$totalTrade,$stepFanliData)
    {
        //获取阶梯返利数据
        $levelMoney = \Models\OilCardViceTrades::getStepFanli($tradeMoney, $totalTrade, $stepFanliData);
        Log::error('$levelMoney--' . var_export($levelMoney, TRUE), [], 'fanliRule_');
        $fanliMoney = 0;
        $logStr = '';//记录日志
        if ($fanli['coe_unit'] == 1) {//按现金百分比
            $fanliMoney = ($levelMoney[1] * $stepFanliData['fanli_coe_level1'] + $levelMoney[2] * $stepFanliData['fanli_coe_level2'] + $levelMoney[3] * floatval($stepFanliData['fanli_coe_level3']) + $levelMoney[4] * floatval($stepFanliData['fanli_coe_level4']) + $levelMoney[5]  * floatval($stepFanliData['fanli_coe_level5'])) * 0.01;

            $logStr .= '('.$levelMoney[1].'*'.$stepFanliData['fanli_coe_level1'].'+'.$levelMoney[2].'*'.$stepFanliData['fanli_coe_level2'] . '+' . $levelMoney[3] . '*' . floatval($stepFanliData['fanli_coe_level3']) . '+' . $levelMoney[4] . '*' . floatval($stepFanliData['fanli_coe_level4']) . '+' . $levelMoney[5] . '*' . floatval($stepFanliData['fanli_coe_level5']).')*' . 0.01;
            if($fanli['fanli_type'] == 4){//油量阶梯返利（升）
                $fanliMoney = $fanliMoney * $trade->trade_price;
                $logStr .= '*' . $trade->trade_price;
            }
        } else if ($fanli['coe_unit'] == 2) {//按每升返利金额
            $fanliMoney = $levelMoney[1] * $stepFanliData['fanli_money_level1'] + $levelMoney[2] * $stepFanliData['fanli_money_level2'] + $levelMoney[3] * $stepFanliData['fanli_money_level3'] + $levelMoney[4] * $stepFanliData['fanli_money_level4'] + $levelMoney[5] * $stepFanliData['fanli_money_level5'];

            $logStr .= "($levelMoney[1] * $stepFanliData[fanli_money_level1] + $levelMoney[2] * $stepFanliData[fanli_money_level2] + $levelMoney[3] * $stepFanliData[fanli_money_level3] + $levelMoney[4] * $stepFanliData[fanli_money_level4] + $levelMoney[5] * $stepFanliData[fanli_money_level5])";
            if($fanli['fanli_type'] == 3){//现金阶梯返利（元）
                $fanliMoney = $fanliMoney / $trade->trade_price;
                $logStr .= '/'.$trade->trade_price;
            }
        }

        //若加油量叠加优惠填写了数值，则需要判断这张卡当前消费日期的前一个季度，这张油卡消费总油量是否大于等于2000升；
        //如果大于等于2000升，则需要在原有基础上，多返利一个百分比（或者多返利多少钱）。
        if(isset($fanli['add_fanli_edu']) && $fanli['add_fanli_edu']){
            $sessionTime = \Models\OilCardViceTrades::getSessionTime($trade->createtime,true);//获取上一季度的时间
            $sqlField = 'SUM(trade_num) tradenumtotal';
            $tradeInfo = \Models\OilCardViceTrades::getRuleStep($trade->vice_no,$sessionTime[0],$sessionTime[1],$sqlField,true);
            Log::error('累计加油量------'.$tradeInfo->tradenumtotal, [], 'fanliRule_');
            if($tradeInfo->tradenumtotal >= 2000){
                Log::error('触发返利叠加优惠------', [], 'fanliRule_');
                $levelMoneyTotal = array_sum($levelMoney);
                if ($fanli['coe_unit'] == 1) {//按现金百分比
                    if($fanli['fanli_type'] == 4){//油量阶梯返利（升）
                        $levelMoneyTotal = $levelMoneyTotal * $trade->trade_price;
                    }
                    $fanliMoney += $levelMoneyTotal * $fanli['add_fanli_edu'] * 0.01;
                    $logStr .= " + $levelMoneyTotal * $fanli[add_fanli_edu] * 0.01";
                }else if ($fanli['coe_unit'] == 2) {//按每升返利金额
                    if($fanli['fanli_type'] == 3){//现金阶梯返利（元）
                        $levelMoneyTotal = $levelMoneyTotal / $trade->trade_price;
                    }
                    $fanliMoney += $levelMoneyTotal * $fanli['add_fanli_edu'];
                    $logStr .= " + $levelMoneyTotal * $fanli[add_fanli_edu]";
                }
            }
        }
        Log::error('计算过程--' . $logStr, [], 'fanliRule_');

        return $fanliMoney;
    }


    static public function getStepFanliForImport($v)
    {
        $stepFanli = self::getStepFanliData($v);
        if ($v['coe_unit'] == 1) {//按现金百分比
            $stepFanli['fanli_coe_level1'] = $v['fanli_edu_level1'];
            $stepFanli['fanli_coe_level2'] = $v['fanli_edu_level2'];
            $stepFanli['fanli_coe_level3'] = $v['fanli_edu_level3'];
            $stepFanli['fanli_coe_level4'] = $v['fanli_edu_level4'];
            $stepFanli['fanli_coe_level5'] = $v['fanli_edu_level5'];
        } elseif ($v['coe_unit'] == 2) {//按每升返利金额
            $stepFanli['fanli_money_level1'] = $v['fanli_edu_level1'];
            $stepFanli['fanli_money_level2'] = $v['fanli_edu_level2'];
            $stepFanli['fanli_money_level3'] = $v['fanli_edu_level3'];
            $stepFanli['fanli_money_level4'] = $v['fanli_edu_level4'];
            $stepFanli['fanli_money_level5'] = $v['fanli_edu_level5'];
        }

        return json_encode($stepFanli);
    }

    /**
     * @title 处理阶梯返利数据
     * <AUTHOR>
     * @param $params
     * @return string
     */
    static public function getStepFanliData($params)
    {
        //阶梯返利字段
        $fieldsArr = ['fanli_level1_gt', 'fanli_level1_le', 'fanli_level2_gt', 'fanli_level2_le', 'fanli_level3_gt', 'fanli_level3_le', 'fanli_level4_gt', 'fanli_level4_le', 'fanli_level5_gt', 'fanli_level5_le', 'fanli_coe_level1', 'fanli_coe_level2', 'fanli_coe_level3', 'fanli_coe_level4', 'fanli_coe_level5', 'fanli_money_level1', 'fanli_money_level2', 'fanli_money_level3', 'fanli_money_level4', 'fanli_money_level5'];

        $stepFanliData = \Framework\Helper::getValuesFromArr($params, $fieldsArr);
        //Log::error('$stepFanliData:'.var_export($stepFanliData,TRUE),[],'fanliPolicy');
        if ($params['coe_unit'] == 1) {//按现金百分比
            $stepFanliData['fanli_money_level1'] = '';
            $stepFanliData['fanli_money_level2'] = '';
        } elseif ($params['coe_unit'] == 2) {//按每升返利金额
            $stepFanliData['fanli_coe_level1'] = '';
            $stepFanliData['fanli_coe_level2'] = '';
        }

        return $stepFanliData;
    }

    /**
     * @title 校验阶梯返利数据
     * <AUTHOR>
     * @param $stepFanli
     */
    static public function checkStepFanliInput($stepFanli)
    {
        /*********************校验阶梯区间数据**********************/
        if ($stepFanli['fanli_level1_gt'] === '') {
            throw new \RuntimeException('一级返利区间开始不能为空', 2);
        }
        if (!$stepFanli['fanli_level1_le']) {
            throw new \RuntimeException('一级返利区间结束不能为空', 2);
        }
        if (!$stepFanli['fanli_level2_gt']) {
            throw new \RuntimeException('二级返利区间开始不能为空', 2);
        }
        if (!$stepFanli['fanli_level2_le']) {
            throw new \RuntimeException('二级返利区间结束不能为空', 2);
        }
        $msg = '上级返利区间的结束值必须等于下级返利区间的起始值';
        if (intval($stepFanli['fanli_level1_gt']) >= intval($stepFanli['fanli_level1_le'])) {
            throw new \RuntimeException($msg, 2);
        }
        if (intval($stepFanli['fanli_level2_gt']) != intval($stepFanli['fanli_level1_le'])) {
            throw new \RuntimeException($msg, 2);
        }
        if (intval($stepFanli['fanli_level2_gt']) >= intval($stepFanli['fanli_level2_le'])) {
            throw new \RuntimeException($msg, 2);
        }
        if ($stepFanli['fanli_level3_gt']) {
            if (intval($stepFanli['fanli_level3_gt']) != intval($stepFanli['fanli_level2_le']) || !$stepFanli['fanli_level3_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level3_le']) {
            if (!$stepFanli['fanli_level3_gt'] || intval($stepFanli['fanli_level3_gt']) >= intval($stepFanli['fanli_level3_le'])) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level4_gt']) {
            if ($stepFanli['fanli_level4_gt'] != $stepFanli['fanli_level3_le'] || !$stepFanli['fanli_level4_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level4_le']) {
            if (!$stepFanli['fanli_level4_gt'] || $stepFanli['fanli_level4_gt'] >= $stepFanli['fanli_level4_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level5_gt']) {
            if ($stepFanli['fanli_level5_gt'] != $stepFanli['fanli_level4_le'] || !$stepFanli['fanli_level5_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }

        if ($stepFanli['fanli_level5_le']) {
            if (!$stepFanli['fanli_level5_gt'] || $stepFanli['fanli_level5_gt'] >= $stepFanli['fanli_level5_le']) {
                throw new \RuntimeException($msg, 2);
            }
        }
        /************************校验返利系数/金额数据*********************/
        if (!$stepFanli['fanli_edu_level1']) {
            throw new \RuntimeException('一级返利额度不能为空', 2);
        }
        if (!$stepFanli['fanli_edu_level2']) {
            throw new \RuntimeException('二级返利额度不能为空', 2);
        }
        if ($stepFanli['fanli_level3_gt'] && $stepFanli['fanli_level3_le'] && !$stepFanli['fanli_edu_level3']) {
            throw new \RuntimeException('三级返利额度不能为空', 2);
        }
        if ($stepFanli['fanli_level4_gt'] && $stepFanli['fanli_level4_le'] && !$stepFanli['fanli_edu_level4']) {
            throw new \RuntimeException('四级返利额度不能为空', 2);
        }
        if ($stepFanli['fanli_level5_gt'] && $stepFanli['fanli_level5_le'] && !$stepFanli['fanli_edu_level5']) {
            throw new \RuntimeException('五级返利额度不能为空', 2);
        }
    }

    /**
     * @title   计算返利规则等级
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    static public function calLevel($detail)
    {
        $levelArr = array("oil_com"=>1,"oil_vice_provider"=>2,"main_id"=>3,"region_id"=>4,"station_opreater_id"=>5,"station_id"=>6,"oil_type"=>7);
        $level = 0;
        foreach ($detail as $key => $item){
            $num = $levelArr[$key];
            if($item > 0 && $num ) {
                $num = $num + 1;
            }
            $level += $num;
        }
        return $level;
    }
}
