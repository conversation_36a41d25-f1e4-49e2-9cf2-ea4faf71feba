<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2021/11/19
 * Time: 2:05 PM
 */

namespace Fuel\Service;


use Framework\Log;
use Fuel\Defines\ReceiptApplyDefine;
use Fuel\Defines\ReceiptTranslateDetail;
use Illuminate\Database\Capsule\Manager;
use Models\OilConfigure;
use Models\OilReceiptTranslateDetail;

class ReceiptTranslateDetailService
{
    protected static $instance = null;

    public static function getInstance()
    {
        if (is_null(self::$instance)) {
            self::$instance = new static();
        }

        return self::$instance;
    }

    public function getOilBuyData($receiptApplyId)
    {
        if (empty($receiptApplyId))
            return [];

        $oilConfig = TypeCategoryService::getOilConfigData(ReceiptApplyDefine::OIL_TEMPLATE);
        $unitConfig = OilConfigure::getConfByName([], true);

        $res = [];

        $params = [
            'fields'                                    => 'in_sku, in_second_oil_id, in_unit, sum(in_num) as trade_num_sum, sum(money_tax) as trade_money_sum',
            'transform'                                 => ReceiptTranslateDetail::TRANSFORMING,
            'money_tax_gt'                              => 0,
            'groupBy'                                   => ['in_sku'],
            'receipt_apply_id'                          => $receiptApplyId
        ];

        $buy = OilReceiptTranslateDetail::getListByFilter($params);
        foreach ($buy as $item) {
            $res[$item['in_sku']] = (object) [
                'receipt_apply_id'      => $receiptApplyId,
                'oil_name'              => $item['in_sku'],
                'oil_type'              => $item['in_second_oil_id'],
                'oil_type_name'         => $oilConfig['second'][$item['in_second_oil_id']]['name'],
                'product_name'          => '',
                'unit'                  => $unitConfig[$item['in_unit']],
                'trade_num_sum'         => $item['trade_num_sum'],
                'receipt_money_sum'     => $item['trade_money_sum'],
                'fanli_money_sum'       => 0,
                'use_fanli_money_sum'   => 0,
                'trade_money_sum'       => $item['trade_money_sum'],
                'internal'              => true
            ];
        }

        $params = [
            'fields'                                    => 'in_sku, sum(money_tax) as trade_money_sum',
            'transform'                                 => ReceiptTranslateDetail::TRANSFORMING,
            'money_tax_lt'                              => 0,
            'groupBy'                                   => ['in_sku'],
            'receipt_apply_id'                          => $receiptApplyId
        ];

        $buy = OilReceiptTranslateDetail::getListByFilter($params);

        foreach ($buy as $item) {
            $res[$item['in_sku']]->use_fanli_money_sum = abs($item['trade_money_sum']);
            $res[$item['in_sku']]->receipt_money_sum = $res[$item['in_sku']]->receipt_money_sum - $res[$item['in_sku']]->use_fanli_money_sum;
        }

        Log::debug('翻译明细聚合后结果', $res, 'ReceiptApplyExportError');

        return $res;
    }
}