<?php

namespace Fuel\Service;

use Framework\Cache;
use Framework\Excel\ExcelReader;
use Framework\Excel\ExcelWriter;
use Framework\Helper;
use Framework\Log;
use Fuel\Defines\OilTypeBase;
use Fuel\Defines\ReceiptApplyDefine;
use Fuel\Defines\ReceiptScope;
use Fuel\Defines\Unit;
use Fuel\Response;
use Models\OilReceiptApply;
use Models\OilSupplierReceiptReturnUnit;
use Models\OilTypeNo;
use Fuel\Defines\OilType;

class ReceiptSplit
{
    /**
     * 单票可开最大金额
     * @var float
     */
    protected $perReceiptMoney        = 116900;
    protected $perReceiptMoneyNew     = 112900; //115900;
    protected $perReceiptMoneyGas     = 108000;  //天然气 9%
    protected $perReceiptMoneyGas100w = 1080000;  //天然气 9%
    protected $perReceiptMoney100w    = 1129000; //1159000;
    protected $perReceiptMoney10m    = 1000000000; //10亿 针对数电票;
    protected $manSetPerReceiptMoney  = FALSE;

    protected $minNum = 8;
    protected $minNumForSdp = 10000000;

    public function setPerReceiptMoney($money)
    {
        $this->perReceiptMoney = $money;
        $this->manSetPerReceiptMoney = TRUE;

        return $this;
    }

    private function setPerReceiptMoneyByType($type)
    {
        $money = null;
        switch ($type) {
            case "sdp":
                $money = $this->perReceiptMoney10m;
                break;
            case "new":
                $money = $this->perReceiptMoneyNew;
                break;
            case "gas":
                $money = $this->perReceiptMoneyGas;
                break;
            case "gas-100w":
                $money = $this->perReceiptMoneyGas100w;
                break;
            case "100w":
                $money = $this->perReceiptMoney100w;
                break;
            default:
                $money = $this->perReceiptMoney;
        }

        $this->perReceiptMoney = $money;

        return $money;
    }

    /**
     * @title   发票拆分，得到拆分后的文件，支持按excel文件拆分
     * @desc
     * @param $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function split($params)
    {
        \helper::argumentCheck(['filePath'], $params);
        $excelFilePath = \NULL;

        $type = isset($params['type']) && $params['type'] ? $params['type'] : null;
        if (!$this->manSetPerReceiptMoney) {
            $this->perReceiptMoney = $type ? $this->perReceiptMoneyNew : $this->perReceiptMoney;
        }

        $filePath = $params['filePath'];

        $fields = [
            '申请单ID'     => 'receipt_apply_id',
            '机构运营商发票编号' => 'receipt_no',
            '客户名称'      => 'company_name',
            '税号'        => 'fax_no',
            '公司地址和联系方式' => 'company_address',
            '开户行及账号'    => 'bank_account',
            '开票类型'      => 'receipt_type',
            '开票类目'      => 'receipt_project',
            '油品类型'      => 'oil_type',
            '规格型号'      => 'model',
            '数量'        => 'oil_counts',
            '单价'        => 'oil_price',
            '开票金额（元）'   => 'oil_money',
            '折扣金额（元）'   => 'use_fanli_money',
            '消费期间'      => 'oil_month',
            '联系人'       => 'addr_name',
            '手机号'       => 'addr_mobile',
            '收件地址'      => 'address',
            '备注'        => 'remark',
        ];

        $record = ExcelReader::read(
            [
                'filePath'     => $filePath,
                'fieldsMap'    => $fields,
                'fieldsRowNum' => 1,
                'ignore'       => 1,
            ]
        );

        $excelGasData = [];
        $excelOilData = [];
        foreach ($record[0] as &$splitItemData) {
            if(isset($splitItemData['use_fanli_money']) && $splitItemData['use_fanli_money']){

            }
            //todo 由于燃油和尿素的税率都是13%
            if (in_array($splitItemData['oil_type'], ['天然气', '液化天然气', '天燃气', '液化天燃气'])) {
                $excelGasData[] = $splitItemData;
            } else {
                $excelOilData[] = $splitItemData;
                Log::error(__METHOD__ . "#OIL", [$splitItemData], 'receipt_split_new');
            }
        }

        Log::error(__METHOD__ . "#OIL-isset", [count($excelOilData)], 'receipt_split_new');

        $result = [];

        if ($excelOilData) {
            $type = $type == '100w' ? '100w' : 'new';
            $_oilExcelData = $this->preSplit($excelOilData, $type);
            $result = $result ? array_merge($result, $_oilExcelData) : $_oilExcelData;
        }

        if ($excelGasData) {
            $type = $type == '100w' ? 'gas-100w' : 'gas';
            $_gasExcelData = $this->preSplit($excelGasData, $type);
            $result = $result ? array_merge($result, $_gasExcelData) : $_gasExcelData;

        }

        Log::error(__METHOD__ . "result", [$result], 'receipt_split_new');

        if ($result) {
            $excelFilePath = $this->exportReceiptSplit($result);
        } else {
            throw new \RuntimeException('拆分失败', 2);
        }

        return str_replace(APP_WWW_ROOT, '', $excelFilePath);
    }

    /**
     * @title   发票拆分-返回拆分后数据
     * @desc
     * @param array $record
     * @param string $type
     * @param string $receipt_type
     * @return array
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service
     */
    public function preSplit(array $record, $type = 'new',$receipt_type = '')
    {
        $newResult = [];
        $excelData = [];
        $data = [];
        $moneyGeArr = [];
        $moneyLeArr = [];
        $middleArr = [];

        //G7WALLET-5442
        if($receipt_type && in_array($receipt_type,['数电普票','数电专票','专票（电子）','普票（电子）'])){
            $perReceiptMoney = $this->setPerReceiptMoneyByType('sdp');
            $minNum = $this->minNumForSdp;
        }else{
            $perReceiptMoney = $this->setPerReceiptMoneyByType($type);
            $minNum = $this->minNum;
        }

        if (is_array($record) && $record) {
            foreach ($record as $k => $v) {
                $_data = [];
                $v['oil_price'] = $v['oil_price'] ? $v['oil_price'] : bcdiv(bcadd($v['oil_money'], $v['use_fanli_money'], 9), $v['oil_counts'], 9);

                foreach ($v as $key => $val) {
                    if (in_array($key, ['oil_counts', 'oil_price', 'oil_money'])) {
                        $_data[$key] = floatval($val);
                    } else {
                        $_data[$key] = trim($val);
                    }
                }

                $company_name = isset($v['receipt_apply_id']) ? $v['company_name'] . '_' . $v['receipt_apply_id'] : $v['company_name'];
                $data[$company_name]['receipt_apply_id'] = isset($v['receipt_apply_id']) ? $v['receipt_apply_id'] : null;

                if ($_data['oil_money'] > $perReceiptMoney) {
                    $moneyGeArr[$company_name][] = $_data;
                } elseif ($_data['oil_money'] > 20000 && $_data['oil_money'] <= $perReceiptMoney) {
                    $middleArr[$company_name][] = $_data;
                } else {
                    $moneyLeArr[$company_name][] = $_data;
                }

                $data[$company_name]['total_money'] = isset($data[$company_name]['total_money']) ?
                    $data[$company_name]['total_money'] + $_data['oil_money'] : $_data['oil_money'];
                $data[$company_name]['total_num'] = isset($data[$company_name]['total_num']) ?
                    $data[$company_name]['total_num'] + 1 : 1;

                $moneyNum = ceil($data[$company_name]['total_money'] / $perReceiptMoney);
                $countsNum = ceil($data[$company_name]['total_num'] / $minNum);

                $data[$company_name]['type'] = $moneyNum > $countsNum ? 'money' : 'counts';
                $data[$company_name]['receiptsCounts'] = $moneyNum > $countsNum ? $moneyNum : $countsNum;

                $data[$company_name]['moneyGeArr'] = isset($moneyGeArr[$company_name]) ?
                    $moneyGeArr[$company_name] : [];
                $data[$company_name]['middleArr'] = isset($middleArr[$company_name]) ? $middleArr[$company_name]
                    : [];
                $data[$company_name]['moneyLeArr'] = isset($moneyLeArr[$company_name]) ?
                    $moneyLeArr[$company_name] : [];

                unset($_data);
            }

            $result = [];
            foreach ($data as $k => &$v) {
                if ($v['moneyGeArr']) {
                    $v['moneyGeArr'] = $this->sortByMoney($v['moneyGeArr']);
                }
                if ($v['moneyLeArr']) {
                    $v['moneyLeArr'] = $this->sortByMoney($v['moneyLeArr']);
                }
                if ($v['middleArr']) {
                    $v['middleArr'] = $this->sortByMoney($v['middleArr']);
                }
                $result[$k] = $this->makeReceiptDetail($v,[],$receipt_type,$perReceiptMoney);
            }

            if ($result) {
                $excelData = [];
                foreach ($result as $k => $v) {
                    foreach ($v as $a => $b) {
                        $_company_name = explode('_', $k);
                        $commonData = [
                            'company_name' => $_company_name[0],
                            'totalMoney'   => $b['totalMoney'],
                            'receipts_num' => $b['receipts_num'],
                        ];

                        if ($b['data']) {
                            foreach ($b['data'] as $o => $p) {
                                $commonData['receipt_apply_id'] = $p['receipt_apply_id'];
                                $commonData['receipt_no'] = $p['receipt_no'];
                                $commonData['drawer'] = $p['drawer'];
                                $commonData['addr_name'] = isset($p['addr_name']) ? $p['addr_name'] : '';
                                $commonData['addr_mobile'] = isset($p['addr_mobile']) ? $p['addr_mobile'] : '';
                                $commonData['address'] = isset($p['address']) ? $p['address'] : '';

                                $commonData['receipt_type'] = $p['receipt_type'];
                                $commonData['receipt_project'] = $p['receipt_project'];
                                $commonData['model'] = $p['model'];
//                                $commonData['oil_price'] = sprintf("%.9f", $p['oil_price']);
                                if (in_array($type, ['gas', 'gas-100w'])) {
                                    $commonData['tax_rate'] = 0.09;
                                    $commonData['unit_model'] = 'Kg';
                                } else {
                                    $commonData['unit_model'] = '升';
                                    $commonData['tax_rate'] = $type ? 0.13 : 0.16;
                                }
                                $commonData['second_oil_type_id'] = isset($p['second_oil_type_id']) ? $p['second_oil_type_id'] : 0;

                                if (isset($p['internal']) && $p['internal']) {
                                    $commonData['unit_model'] = isset($p['unit']) ? $p['unit'] : ''; // 单位
                                } else {
                                    // 根据规格型号查询油品信息，查不到抛异常
                                    $checkOilTypeInfo = OilTypeNo::getByOilNo(['oil_no' => $p['model']]);
                                    if (!$checkOilTypeInfo) {
                                        $content = [];
                                        $content[] = '发票申请ID:'.$p['receipt_apply_id'];
                                        $content[] = '规格型号：'.$p['model'].'：未在油品数据维护油品二级品类，请先维护！';
                                        (new ReceiptSplitApply())->alarmExportReceipt($content);

                                        throw new \RuntimeException('规格型号【' . $p['model'] . '】未在油品数据维护里，请先维护！', 2);
                                    }
//                                $commonData['sec_oil_type'] = OilType::$oil_sec_type[$checkOilTypeInfo['oil_sec_type']]['title']; // 二级分类
//                                $commonData['tax_no'] = $checkOilTypeInfo['tax_no']; // 税收编码
                                    $commonData['unit_model'] = $checkOilTypeInfo['unit']; // 单位
                                }

                                $commonData['payee'] = '洪玉洁';
                                $commonData['checker'] = '王聪';
                                $commonData['use_fanli_money'] = $p['use_fanli_money'];

                                if ($b['receipts_num'] > 1) {
                                    $commonData['oil_money'] = round($b['oil_money'], 2);
                                    $commonData['oil_counts'] = sprintf("%.6f", bcdiv($p['oil_counts'], $b['receipts_num'], 20));
                                    //$commonData['use_fanli_money'] = sprintf("%.6f", bcdiv($p['use_fanli_money'], $b['receipts_num'], 20));
                                } else {
                                    $commonData['oil_money'] = round($p['oil_money'], 2);
                                    $commonData['oil_counts'] = sprintf("%.6f", $p['oil_counts']);
                                }

                                $commonData['oil_price'] = bcdiv(bcadd($commonData['oil_money'],$commonData['use_fanli_money'],9), $commonData['oil_counts'],9);
                                $commonData['oil_month'] = $p['oil_month'];
                                $commonData['fax_no'] = $p['fax_no'];
                                $commonData['bank_account'] = $p['bank_account'];
                                $commonData['company_address'] = $p['company_address'];
                                $commonData['origin_remark'] = $p['remark'];
                                $commonData['remark'] = $p['remark'];
                                $commonData['internal'] = isset($p['internal']) ? $p['internal'] : false;

                                if ($o > 0) {
                                    $commonData['totalMoney'] = '';
                                    $commonData['totalMoney'] = '';
                                    $commonData['payee'] = '';
                                    $commonData['checker'] = '';
                                    $commonData['company_name'] = '';
                                    $commonData['fax_no'] = '';
                                    $commonData['receipts_num'] = null;
                                    $commonData['company_address'] = '';
                                    $commonData['bank_account'] = '';
                                }

                                $excelData[$p['receipt_apply_id']][] = $commonData;
                            }
                        }

                    }
                }
            } else {
                throw new \RuntimeException('无处理结果，请检查', 2);
            }
            Log::debug('$excelData', [$excelData], 'ReceiptApplyExportError');

            if ($excelData) {
                $index = 0;
                $group_name = '';
                foreach ($excelData as $_excelData) {
                    foreach ($_excelData as $v) {
                        $v['addr_mobile'] = $v['addr_mobile'] . "\t";
                        if ($v['receipts_num']) {
                            //$remark = $v['remark'];
                            for ($i = 0; $i < $v['receipts_num']; $i++) {
                                $index++;
                                $group_name = Helper::uuid();
                                $v['group_name'] = $group_name;
                                $newResult[] = $v;
                            }
                        } else {
                            $v['group_name'] = $group_name;
                            $newResult[] = $v;
                        }
                    }
                }

            }
        }

        Log::debug('before splitFanli $newResult', $newResult, 'ReceiptApplyExportError');

        $newResult = $this->splitFanli($newResult,$receipt_type);

        Log::debug('$newResult', $newResult, 'ReceiptApplyExportError');

        Log::debug('return $newResult' . count($newResult), [], 'ReceiptApplyExportError');

        return $newResult;
    }

    /*
     * 根据xnum分组
     */
    public function splitFanli($newResult,$receipt_type = '')
    {
        $group = $newArr = $newArrOne = [];
        foreach ($newResult as $key => $val) {
            $group[$val['group_name']][] = $val;
        }

        foreach ($group as $xnum => $value) {
            $line = $have_use_fanli = 0;
            $newXNum = $this->makeReceiptNo($value[0]['receipt_no']);
            foreach ($value as $key => &$info) {
                $info['xnum'] = $newXNum;
                $info['remark'] = implode(';',[$newXNum,$info['remark']]);
                if ($info['use_fanli_money'] > 0) {
                    $have_use_fanli++;
                }
                $line++;
            }
            //var_dump('xnum:'.$xnum,'line:'.$line,'have_use_fanli:'.$have_use_fanli);
            $newArrOne[] = $this->splitFanliOne($value, $line, $have_use_fanli,$receipt_type);
        }

        foreach ($newArrOne as $value) {
            foreach ($value as $item) {
                $newArr[] = $item;
            }
        }

        return $newArr;

    }

    /*
     * 根据使用返利再次拆分
     */
    public function splitFanliOne($newResult, $line, $is_fanli_num,$receipt_type = '')
    {
        $is_split = FALSE;
        if($this->minNum == 8) {
            if ($line == ($this->minNum - 3) && $is_fanli_num > 3) {
                $is_split = TRUE;
            } elseif ($line == ($this->minNum - 2) && $is_fanli_num > 2) {
                $is_split = TRUE;
            } elseif ($line == ($this->minNum - 1) && $is_fanli_num > 1) {
                $is_split = TRUE;
            } elseif ($line >= $this->minNum && $is_fanli_num > 0) {
                $is_split = TRUE;
            }
        }else{
            if ($line == ($this->minNum - 2) && $is_fanli_num > 2) {
                $is_split = TRUE;
            } elseif ($line == ($this->minNum - 1) && $is_fanli_num > 1) {
                $is_split = TRUE;
            } elseif ($line >= $this->minNum && $is_fanli_num > 0) {
                $is_split = TRUE;
            }
        }

        if($receipt_type && in_array($receipt_type,['数电专票','数电普票','专票（电子）','普票（电子）'])){
            $is_split = FALSE;
        }

        if ($is_split) {
            $oneLine = $newResult[0];
            $newXnum = $this->makeReceiptNo($oneLine['receipt_no']);
            $splitArr = [];
            foreach ($newResult as $index => $v) {
                if ($index == 4) {
                    $oneLine['oil_price'] = $v['oil_price'];
                    $oneLine['oil_money'] = $v['oil_money'];
                    $oneLine['oil_counts'] = $v['oil_counts'];
                    $oneLine['use_fanli_money'] = $v['use_fanli_money'];
                    $oneLine['receipt_project'] = $v['receipt_project'];
                    $oneLine['model'] = $v['model'];
                    $oneLine['xnum'] = $newXnum;
                    $oneLine['remark'] = implode(';', [$newXnum, $v['origin_remark']]);
                    if (isset($v['dwawer'])) {
                        $oneLine['drawer'] = $v['drawer'];
                    }
                    $splitArr[] = $oneLine;
                } elseif ($index > 4) {
                    $oneLine['oil_price'] = $v['oil_price'];
                    $oneLine['oil_money'] = $v['oil_money'];
                    $oneLine['oil_counts'] = $v['oil_counts'];
                    $oneLine['use_fanli_money'] = $v['use_fanli_money'];
                    $oneLine['receipt_project'] = $v['receipt_project'];
                    $oneLine['model'] = $v['model'];
                    $oneLine['xnum'] = $newXnum;
                    $oneLine['remark'] = implode(';', [$newXnum, $v['origin_remark']]);
                    $oneLine['totalMoney'] = '';
                    $oneLine['totalMoney'] = '';
                    $oneLine['payee'] = '';
                    $oneLine['checker'] = '';
                    $oneLine['company_name'] = '';
                    $oneLine['fax_no'] = '';
                    $oneLine['receipts_num'] = null;
                    $oneLine['company_address'] = '';
                    $oneLine['bank_account'] = '';
                    if (isset($v['dwawer'])) {
                        $oneLine['drawer'] = $v['drawer'];
                    }

                    $splitArr[] = $oneLine;
                } else {
                    $splitArr[] = $v;
                }
            }
        } else {
            $splitArr = $newResult;
        }

        $temp = [];
        foreach ($splitArr as $value) {
            // 导出增加销售金额、应开金额和折扣金额
            $value['receipt_amount'] = $value['oil_money'];
            if ($value['use_fanli_money'] && $value['use_fanli_money'] > 0) {
                $value['oil_money'] = $value['oil_money'] + $value['use_fanli_money'];
                $temp[] = $value;
            } else {
                $temp[] = $value;
            }
        }

        return $temp;
    }

    /*
     * 根据使用返利再次拆分
     */
    public function splitFanliOne_bak($newResult, $line, $is_fanli_num)
    {
        $is_split = FALSE;
        if ($line == 4 && $is_fanli_num > 1) {
            $is_split = TRUE;
        } elseif ($line >= 5 && $is_fanli_num > 0) {
            $is_split = TRUE;
        }

        if ($is_split) {
            $oneLine = $newResult[0];
            $newXnum = $this->makeReceiptNo($oneLine['receipt_no']);
            $remark = $oneLine['remark'];
            $splitArr = [];
            foreach ($newResult as $index => $v) {
                if ($index == 3) {
                    $oneLine['oil_price'] = $v['oil_price'];
                    $oneLine['oil_money'] = $v['oil_money'];
                    $oneLine['oil_counts'] = $v['oil_counts'];
                    $oneLine['use_fanli_money'] = $v['use_fanli_money'];
                    $oneLine['receipt_project'] = $v['receipt_project'];
                    $oneLine['model'] = $v['model'];
                    $oneLine['xnum'] = $newXnum;
                    $oneLine['remark'] = implode(';', [$newXnum, $remark]);
                    $splitArr[] = $oneLine;
                } elseif ($index > 3) {
                    $oneLine['oil_price'] = $v['oil_price'];
                    $oneLine['oil_money'] = $v['oil_money'];
                    $oneLine['oil_counts'] = $v['oil_counts'];
                    $oneLine['use_fanli_money'] = $v['use_fanli_money'];
                    $oneLine['receipt_project'] = $v['receipt_project'];
                    $oneLine['model'] = $v['model'];
                    $oneLine['xnum'] = $newXnum;
                    $oneLine['remark'] = implode(';', [$newXnum, $remark]);
                    $oneLine['totalMoney'] = '';
                    $oneLine['totalMoney'] = '';
                    $oneLine['payee'] = '';
                    $oneLine['checker'] = '';
                    $oneLine['company_name'] = '';
                    $oneLine['fax_no'] = '';
                    $oneLine['receipts_num'] = null;
                    $oneLine['company_address'] = '';
                    $oneLine['bank_account'] = '';

                    $splitArr[] = $oneLine;
                } else {
                    $splitArr[] = $v;
                }
            }
        } else {
            $splitArr = $newResult;
        }

        $temp = [];
        foreach ($splitArr as $value) {
            if ($value['use_fanli_money'] && $value['use_fanli_money'] > 0) {
                $value['oil_money'] = $value['oil_money'] + $value['use_fanli_money'];
                $value['oil_price'] = bcdiv($value['oil_money'], $value['oil_counts'], 2);
                $temp[] = $value;

                $value['totalMoney'] = '';
                $value['totalMoney'] = '';
                $value['payee'] = '';
                $value['checker'] = '';
                $value['company_name'] = '';
                $value['fax_no'] = '';
                $value['receipts_num'] = null;
                $value['company_address'] = '';
                $value['bank_account'] = '';
                $value['oil_money'] = -$value['use_fanli_money'];
                $value['oil_price'] = '';
                $value['oil_counts'] = '';
                $value['unit_model'] = '';
                $temp[] = $value;
            } else {
                $temp[] = $value;
            }
        }

        return $temp;
    }

    /**
     * @title   生成销售单据号
     * @desc
     * @param $type
     * @return string
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function makeReceiptNo($type)
    {
        $cacheName = 'ReceiptNo_' . $type . date("ymd");
        $lastNoInfo = Cache::get($cacheName);
        if ($lastNoInfo) {
            $nextIndex = $lastNoInfo->no + 1;
            $nextNo = $type . date("Ymd") . str_pad($nextIndex, 4, '0', STR_PAD_LEFT);
        } else {
            $nextIndex = 1;
            $nextNo = $type . date("Ymd") . str_pad($nextIndex, 4, '0', STR_PAD_LEFT);
        }

        Cache::put($cacheName, (object)['no' => $nextIndex], 86400 * 2);

        return $nextNo;
    }

    /**
     * 根据申请单配置，转化开具单位
     * @param $excelData
     * @param array $applyIds
     */
    protected function transformInvoiceUnit(&$excelData, $applyIds=[])
    {
        if (empty($applyIds))
            return;

        $applyIds = array_unique($applyIds);
        if (count($applyIds) != 1)
            return;

        $apply = OilReceiptApply::getById(['id' => $applyIds[0]]);
        if (empty($apply))
            return;

//        if ($apply->unit != Unit::INVOICE_TON) {
//            return;
//        }

        foreach ($excelData as &$v) {
//            if ($apply->is_internal == ReceiptScope::INTERNAL) {
//                $secondOilType  = $v['second_oil_type_id'];
//                $unit           = $v['unit_model'];
//            } else {
//                $checkOilTypeInfo = OilTypeNo::getByOilNo(['oil_no'=>$v['model']]);
//                $secondOilType = OilType::$oil_sec_type[$checkOilTypeInfo['oil_sec_type']]['second_oil_type_id'];
//
//                $unit = $checkOilTypeInfo['unit'];
//            }

            if($apply->is_internal != ReceiptScope::INTERNAL){
                $checkOilTypeInfo = OilTypeNo::getByOilNo(['oil_no'=>$v['model']]);
                $secondOilType = OilType::$oil_sec_type[$checkOilTypeInfo['oil_sec_type']]['second_oil_type_id'];

                $unit = $checkOilTypeInfo['unit'];

                $unit = OilType::transformValueToId($unit);

                $v['unit_model'] = Unit::getTonValue();
                $v['oil_counts'] = Unit::transform($secondOilType, $unit, Unit::getTonId(), $v['oil_counts']);
                $v['oil_price'] = rtrim(sprintf('%0.9f', bcdiv(bcadd($v['oil_money'], $v['use_fanli_money'],2), $v['oil_counts'],9)), '0');
            }

        }
    }

    /**
     * 导出Excel
     * @param $excelData
     * @param $applyIds
     * @return bool|mixed|string
     * @throws \PHPExcel_Exception
     */
    public function exportReceiptSplit($excelData, $applyIds=[])
    {
        $this->transformInvoiceUnit($excelData, $applyIds);

        $baseFilePath = \APP_ROOT . \DIRECTORY_SEPARATOR . 'www';

        $titleArr = [
            'xnum'            => '销售单据编号',
            'payee'           => '收款',
            'checker'         => '复核',
            'company_name'    => '购方名称',
            'fax_no'          => '购方税号',
            'company_address' => '购方地址电话',
            'bank_account'    => '购方银行账号',
            'remark'          => '备注',
            'receipt_project' => '货物名称',
            'model'           => '规格',
            'unit_model'      => '计量单位',
            'oil_price'       => '单价',
            'oil_counts'      => '数量',
            'oil_money'       => '金额',
            'tax_rate'        => '税率',
            'use_fanli_money' => '折扣金额',
            'addr_name'       => '联系人',
            'addr_mobile'     => '手机号',
            'address'         => '收件地址',
            //                    'receipts_num'    => '发票张数',
        ];
        $excelFilePath = ExcelWriter::exportXls([
            'fileName'  => 'receiptSplit' . DIRECTORY_SEPARATOR . 'invoice_split_' . date("YmdHis") . rand(100, 999),
            'sheetName' => '开票流水',
            'fileExt'   => 'xls',
            'download'  => TRUE,
            'title'     => $titleArr,
            'data'      => $excelData,
        ], function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['fax_no', 'xnum', 'remark'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });

        return $excelFilePath;
    }

    private function makeReceiptDetail(&$data, $receiptsArr = [],$receipt_type = '',$perReceiptMoney = '')
    {
        if (empty($receiptsArr)) {
            for ($i = 0; $i < ($data['receiptsCounts'] * 2); $i++) {
                $receiptsArr[] = [];
            }
        }

        if($receipt_type && in_array($receipt_type,['数电专票','数电普票','专票（电子）','普票（电子）'])){
            $minNum = $this->minNumForSdp;
            $perReceiptMoney = $this->perReceiptMoney10m;
        }else{
            $minNum = $this->minNum;
            $perReceiptMoney = $perReceiptMoney ? $perReceiptMoney : $this->perReceiptMoney;
        }

        Log::error("makeReceiptDetail:" . var_export($data, TRUE), [$receiptsArr], "ReceiptApplyExportError");

        if ($data['receiptsCounts'] == 1) {
            $receiptsArr[] = [
                'totalMoney'   => $data['total_money'],
                'mod'          => 0,
                'receipts_num' => 1,
                'data'         => array_merge($data['moneyGeArr'], $data['moneyLeArr'], $data['middleArr']),
            ];
            $data['moneyGeArr'] = $data['moneyLeArr'] = $data['middleArr'] = [];
        } else {
            foreach ($receiptsArr as $o => &$p) {
                $p = $this->sortByMoney($p);
                if ($data['moneyGeArr']) {

                    foreach ($data['moneyGeArr'] as $a => &$b) {
                        //$oil_money = $b['oil_money'];
                        $use_fanli_money = $b['use_fanli_money'];
                        $receipts_num = floor($b['oil_money'] / $perReceiptMoney);
                        $mod = $b['oil_money'] - round($perReceiptMoney * $receipts_num, 2);

                        //var_dump($this->perReceiptMoney.'/'.$oil_money);
                        //var_dump($b['use_fanli_money'].'*('.$this->perReceiptMoney.'/'.$oil_money.')');
                        #$b['use_fanli_money'] = sprintf("%.2f",bcmul($b['use_fanli_money'], bcdiv($this->perReceiptMoney, $b['oil_money'], 6), 6));
//                        $b['use_fanli_money'] = floor($b['use_fanli_money'] / $receipts_num);
//                        $left_use_fanli_money = $use_fanli_money - $b['use_fanli_money'] * $receipts_num;

                        $per_rate = sprintf('%.20f', $perReceiptMoney / $b['oil_money']);
                        $counts = $b['oil_counts'];
                        $_counts = bcmul($per_rate, $counts, 20) * $receipts_num;
                        $b['oil_counts'] = $_counts;
                        $b['use_fanli_money'] = floor(bcmul($per_rate , $b['use_fanli_money'],2));
                        $left_use_fanli_money = (float) bcsub($use_fanli_money, bcmul($b['use_fanli_money'], $receipts_num, 2), 2);
//                        if ($left_use_fanli_money < 0.1) {
//                            $b['use_fanli_money'] = (float) bcsub($use_fanli_money);
//                            $left_use_fanli_money = 0;
//                        }
                        //echo "oil-money".$b['oil_money'].',-mod:'.$mod."-=-receipt_num:".$receipts_num."\r\n";
                        $b['oil_money'] = $b['oil_money'] - $mod;
                        //echo "oil-money".$b['oil_money'].',-mod:'.$mod."-=-receipt_num:".$receipts_num.',mmm:'.$b['oil_money']."\r\n";
                        $p = [
                            'totalMoney'   => $b['oil_money'],
                            'mod'          => $mod,
                            'receipts_num' => $receipts_num,
                            'oil_money'    => $perReceiptMoney,
                            'data'         => [$b],
                        ];

                        if ($mod > 20000 && $mod < $perReceiptMoney) {
                            $c = $b;
                            $c['oil_money'] = $mod;
//                            $c['oil_counts'] = $counts - $_counts;
                            $c['oil_counts'] = bcsub($counts, $_counts,20);
                            $c['use_fanli_money'] = $left_use_fanli_money;
                            $data['middleArr'][] = $c;
                        } else {
                            $c = $b;
                            $c['oil_money'] = $mod;
//                            $c['oil_counts'] = $counts - $_counts;
                            $c['oil_counts'] = bcsub($counts, $_counts,20);
                            //var_dump($mod.'/'.$b['oil_money']);
                            //var_dump($use_fanli_money.'*('.$mod.'/'.$oil_money.')');
                            $c['use_fanli_money'] = $left_use_fanli_money;
                            $data['moneyLeArr'][] = $c;
                        }

                        unset($data['moneyGeArr'][$a]);
                        break;
                    }
                }

                if ($data['middleArr']) {
                    rsort($data['middleArr']);
                    foreach ($data['middleArr'] as $a => &$b) {
                        if (!$p) {
                            $p = [
                                'totalMoney'   => $b['oil_money'],
                                'mod'          => 0,
                                'receipts_num' => 1,
                                'data'         => [$b],
                            ];
                            unset($data['middleArr'][$a]);
                        } elseif ($p['totalMoney'] < $perReceiptMoney && ($p['totalMoney'] + $b['oil_money']) <=
                            $perReceiptMoney
                        ) {
                            $p['data'][] = $b;
                            $p['totalMoney'] = $p['totalMoney'] + $b['oil_money'];
                            unset($data['middleArr'][$a]);
                        }

                        if (count($p['data']) >= $minNum) {
                            break;
                        } elseif (($p['totalMoney'] + $b['oil_money']) > $perReceiptMoney) {
                            continue;
                        } elseif (($p['totalMoney'] >= ($perReceiptMoney - 2000) && $p['totalMoney'] <=
                            $perReceiptMoney)
                        ) {
                            break;
                        }
                    }

                }

                if ($data['moneyLeArr']) {
                    rsort($data['moneyLeArr']);
                    foreach ($data['moneyLeArr'] as $a => &$b) {
                        if (!$p) {
                            $p = [
                                'totalMoney'   => $b['oil_money'],
                                'mod'          => 0,
                                'receipts_num' => 1,
                                'data'         => [$b],
                            ];
                            unset($data['moneyLeArr'][$a]);
                        } elseif (($p['totalMoney'] + $b['oil_money']) <=
                            $perReceiptMoney && count($p['data']) < $minNum
                        ) {
                            $p['data'][] = $b;
                            $p['totalMoney'] = $p['totalMoney'] + $b['oil_money'];
                            unset($data['moneyLeArr'][$a]);
                        }

                        if (count($p['data']) >= $minNum) {
                            break;
                        } elseif (($p['totalMoney'] + $b['oil_money']) > $perReceiptMoney) {
                            continue;
                        } elseif (($p['totalMoney'] >= ($perReceiptMoney - 10) && $p['totalMoney'] <=
                            $perReceiptMoney)
                        ) {
                            break;
                        }
                    }
                }
            }

        }

        if (empty($data['moneyGeArr']) && empty($data['middleArr']) && empty($data['moneyLeArr'])) {
            if ($receiptsArr) {
                foreach ($receiptsArr as $k => &$v) {
                    if (empty($v)) {
                        unset($receiptsArr[$k]);
                    }
                }
            }

            return array_values($receiptsArr);
        } else {
            $this->makeReceiptDetail($data, $receiptsArr);
        }
    }

    public function sortByMoney(array $data)
    {
        if ($data) {
            $moneyArr = [];
            foreach ($data as $key => $val) {
                $moneyArr[$key] = $val['oil_money'];
            }
            array_multisort($moneyArr, SORT_DESC, $data);
        }

        return $data;
    }

    public function makeXml(array $data, $type)
    {
        $totalNum = 0;
        foreach ($data as $v) {
            foreach ($v as $b) {
                if ($b['receipts_num']) {
                    $totalNum = $totalNum + $b['receipts_num'];
                }
            }
        }

        $xmlContent = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n";
        $xmlContent .= "<Kp>\n";
        $xmlContent .= "<Version>2.0</Version>\n";
        $xmlContent .= "<Fpxx>\n";
        $xmlContent .= "<Zsl>" . $totalNum . "</Zsl>\n";
        $xmlContent .= "<Fpsj>\n";

        foreach ($data as $k => $v) {
            $index = $k;
            foreach ($v as $a => $b) {
                $indexSec = $a + 1;
                if (intval($b['receipts_num']) > 1 && count($b['data']) == 1) {
                    for ($i = 0; $i < $b['receipts_num']; $i++) {
                        $itemOne = $b['data'][0];
                        $xmlContent .= "<Fp>\n";
                        $xmlContent .= "<Djh>" . $index . '_' . $indexSec . '_' . ($i + 1) . "</Djh>\n";
                        $xmlContent .= "<Gfmc>" . $itemOne['company_name'] . "</Gfmc>\n";
                        $xmlContent .= "<Gfsh>" . trim($itemOne['fax_no']) . "</Gfsh>\n";
                        $xmlContent .= "<Gfyhzh>" . $itemOne['bank_account'] . "</Gfyhzh>\n";
                        $xmlContent .= "<Gfdzdh>" . $itemOne['company_address'] . "</Gfdzdh>\n";
                        $xmlContent .= "<Bz/>\n";
                        $xmlContent .= "<Fhr>王聪</Fhr>\n";
                        $xmlContent .= "<Skr>洪玉洁</Skr>\n";
                        $xmlContent .= "<Spbmbbh>13.0</Spbmbbh>\n";
                        $xmlContent .= "<Hsbz>0</Hsbz>\n";
                        $xmlContent .= "<Spxx>\n";

                        $itemOne['receipts_num'] = $b['receipts_num'];
                        $xmlContent .= $this->childXml($itemOne, 1, $xmlContent, $type);

                        $xmlContent .= "</Spxx>\n";
                        $xmlContent .= "</Fp>\n";
                    }
                } else {
                    $itemOne = $b['data'][0];
                    $xmlContent .= "<Fp>\n";
                    $xmlContent .= "<Djh>" . $index . '_' . $indexSec . "</Djh>\n";
                    $xmlContent .= "<Gfmc>" . $itemOne['company_name'] . "</Gfmc>\n";
                    $xmlContent .= "<Gfsh>" . trim($itemOne['fax_no']) . "</Gfsh>\n";
                    $xmlContent .= "<Gfyhzh>" . $itemOne['bank_account'] . "</Gfyhzh>\n";
                    $xmlContent .= "<Gfdzdh>" . $itemOne['company_address'] . "</Gfdzdh>\n";
                    $xmlContent .= "<Bz/>\n";
                    $xmlContent .= "<Fhr>王聪</Fhr>\n";
                    $xmlContent .= "<Skr>洪玉洁</Skr>\n";
                    $xmlContent .= "<Spbmbbh>13.0</Spbmbbh>\n";
                    $xmlContent .= "<Hsbz>0</Hsbz>\n";
                    $xmlContent .= "<Spxx>\n";

                    $xnum = 0;
                    foreach ($b['data'] as $o) {
                        $xnum = $xnum + 1;
                        $o['receipts_num'] = $b['receipts_num'];
                        $xmlContent .= $this->childXml($o, $xnum, $xmlContent, $type);
                    }
                    $xmlContent .= "</Spxx>\n";
                    $xmlContent .= "</Fp>\n";
                }
            }
        }
        $xmlContent .= "</Fpsj>\n";
        $xmlContent .= "</Fpxx>\n";
        $xmlContent .= "</Kp>\n";

        $xmlFilePath = \APP_ROOT . \DIRECTORY_SEPARATOR . 'www' . \DIRECTORY_SEPARATOR . 'download' . \DIRECTORY_SEPARATOR . 'receiptSplit'
            . \DIRECTORY_SEPARATOR;
        $xmlFileName = Helper::uuid() . '.xml';
        \file_put_contents($xmlFilePath . $xmlFileName, $xmlContent);

        return $xmlFilePath . $xmlFileName;
    }

    private function childXml($itemOne, $xnum, $xmlContent, $type)
    {
        if ($type && $type == 'new') {
            if ($itemOne['oil_type'] == '柴油') {
                $itemOne['oil_type'] = '1070101030100000000';
            } elseif ($itemOne['oil_type'] == '汽油') {
                $itemOne['oil_type'] = '1070101010100000000';
            }
        } else {
            if ($itemOne['oil_type'] == '柴油') {
                $itemOne['oil_type'] = '1070101030000000000';
            } elseif ($itemOne['oil_type'] == '汽油') {
                $itemOne['oil_type'] = '1070101990000000000';
            }
        }
        $xmlContent = "<Sph>\n";
        $xmlContent .= "<Xh>" . $xnum . "</Xh>\n";
        $xmlContent .= "<Spmc>" . $itemOne['receipt_project'] . "</Spmc>\n";
        $xmlContent .= "<Ggxh>" . $itemOne['model'] . "</Ggxh>\n";
        $xmlContent .= "<Jldw>升</Jldw>\n";
        $xmlContent .= "<Spbm>" . $itemOne['oil_type'] . "</Spbm>\n";
        $xmlContent .= "<Qyspbm/>\n";
        $xmlContent .= "<Syyhzcbz>0</Syyhzcbz>\n";
        $xmlContent .= "<Lslbz/>\n";
        $xmlContent .= "<Yhzcsm/>\n";
        $xmlContent .= "<Dj>" . $itemOne['oil_price'] . "</Dj>\n";
        $xmlContent .= "<Sl>" . bcdiv($itemOne['oil_counts'], $itemOne['receipts_num'], 15) . "</Sl>\n";
        $xmlContent .= "<Je>" . bcdiv($itemOne['oil_money'], $itemOne['receipts_num'], 15) . "</Je>\n";
        $xmlContent .= "<Slv>0.13</Slv>\n";
        $xmlContent .= "<Kce/>\n";
        $xmlContent .= "</Sph>\n";

        return $xmlContent;
    }

    public function testSplit()
    {
        $dataStr = "{
	\"江苏天辰石油化工有限责任公司_28023\": {
		\"receipt_apply_id\": 28023,
		\"total_money\": 3393649.02,
		\"total_num\": 4,
		\"type\": \"money\",
		\"receiptsCounts\": 31,
		\"moneyGeArr\": [{
			\"receipt_apply_id\": \"28023\",
			\"receipt_no\": \"HT01\",
			\"drawer\": \"张蒙\",
			\"company_name\": \"江苏天辰石油化工有限责任公司\",
			\"fax_no\": \"91320791MA24N41L95\",
			\"company_address\": \"中国(江苏）自由贸易试验区连云港片区经济技术开发区新光路22号A05栋113号-90；***********\",
			\"bank_account\": \"江苏银行连云港墟沟支行；*****************\",
			\"receipt_type\": \"专票\",
			\"oil_type\": \"柴油\",
			\"receipt_project\": \"柴油\",
			\"model\": \"0号 车用柴油(Ⅵ)\",
			\"oil_counts\": 195010.76,
			\"oil_price\": 5.*************,
			\"oil_money\": 1075929.34,
			\"use_fanli_money\": \"12220.31\",
			\"oil_month\": \"\",
			\"addr_name\": \"庄先生\",
			\"addr_mobile\": \"***********\",
			\"address\": \"山东省日照市东港区山东路与烟台路交汇处安泰国际大厦1号楼1103室\",
			\"remark\": \"*****************;\"
		}, {
			\"receipt_apply_id\": \"28023\",
			\"receipt_no\": \"HT01\",
			\"drawer\": \"张蒙\",
			\"company_name\": \"江苏天辰石油化工有限责任公司\",
			\"fax_no\": \"91320791MA24N41L95\",
			\"company_address\": \"中国(江苏）自由贸易试验区连云港片区经济技术开发区新光路22号A05栋113号-90；***********\",
			\"bank_account\": \"江苏银行连云港墟沟支行；*****************\",
			\"receipt_type\": \"专票\",
			\"oil_type\": \"柴油\",
			\"receipt_project\": \"柴油\",
			\"model\": \"0#柴油国六\",
			\"oil_counts\": 344502.42,
			\"oil_price\": 6.*************,
			\"oil_money\": 2039606.79,
			\"use_fanli_money\": \"29293.67\",
			\"oil_month\": \"\",
			\"addr_name\": \"庄先生\",
			\"addr_mobile\": \"***********\",
			\"address\": \"山东省日照市东港区山东路与烟台路交汇处安泰国际大厦1号楼1103室\",
			\"remark\": \"*****************;\"
		}, {
			\"receipt_apply_id\": \"28023\",
			\"receipt_no\": \"HT01\",
			\"drawer\": \"张蒙\",
			\"company_name\": \"江苏天辰石油化工有限责任公司\",
			\"fax_no\": \"91320791MA24N41L95\",
			\"company_address\": \"中国(江苏）自由贸易试验区连云港片区经济技术开发区新光路22号A05栋113号-90；***********\",
			\"bank_account\": \"江苏银行连云港墟沟支行；*****************\",
			\"receipt_type\": \"专票\",
			\"oil_type\": \"柴油\",
			\"receipt_project\": \"柴油\",
			\"model\": \"0#柴油\",
			\"oil_counts\": 46527.01,
			\"oil_price\": 5.*************,
			\"oil_money\": 271324.7,
			\"use_fanli_money\": \"3607.55\",
			\"oil_month\": \"\",
			\"addr_name\": \"庄先生\",
			\"addr_mobile\": \"***********\",
			\"address\": \"山东省日照市东港区山东路与烟台路交汇处安泰国际大厦1号楼1103室\",
			\"remark\": \"*****************;\"
		}],
		\"middleArr\": [],
		\"moneyLeArr\": [{
			\"receipt_apply_id\": \"28023\",
			\"receipt_no\": \"HT01\",
			\"drawer\": \"张蒙\",
			\"company_name\": \"江苏天辰石油化工有限责任公司\",
			\"fax_no\": \"91320791MA24N41L95\",
			\"company_address\": \"中国(江苏）自由贸易试验区连云港片区经济技术开发区新光路22号A05栋113号-90；***********\",
			\"bank_account\": \"江苏银行连云港墟沟支行；*****************\",
			\"receipt_type\": \"专票\",
			\"oil_type\": \"柴油\",
			\"receipt_project\": \"柴油\",
			\"model\": \"0#柴油国六（低价)\",
			\"oil_counts\": 1135.16,
			\"oil_price\": 5.***********,
			\"oil_money\": 6788.19,
			\"use_fanli_money\": \"0.00\",
			\"oil_month\": \"\",
			\"addr_name\": \"庄先生\",
			\"addr_mobile\": \"***********\",
			\"address\": \"山东省日照市东港区山东路与烟台路交汇处安泰国际大厦1号楼1103室\",
			\"remark\": \"*****************;\"
		}]
	}
}";
        $data = \GuzzleHttp\json_decode($dataStr,true);
        $result = [];
        foreach ($data as $k => &$v) {
            if ($v['moneyGeArr']) {
                $v['moneyGeArr'] = $this->sortByMoney($v['moneyGeArr']);
            }
            if ($v['moneyLeArr']) {
                $v['moneyLeArr'] = $this->sortByMoney($v['moneyLeArr']);
            }
            if ($v['middleArr']) {
                $v['middleArr'] = $this->sortByMoney($v['middleArr']);
            }
            $result[$k] = $this->makeReceiptDetail($v);
        }

        return $result;
    }

    public function testPreSplit(array $record, $type = 'new')
    {
        $newResult = [];
        $excelData = [];
        $data = [];
        $moneyGeArr = [];
        $moneyLeArr = [];
        $middleArr = [];
        $perReceiptMoney = $this->setPerReceiptMoneyByType($type);
        if (is_array($record) && $record) {
            foreach ($record as $k => $v) {
                $_data = [];
                $v['oil_price'] = $v['oil_price'] ? $v['oil_price'] : bcdiv(bcadd($v['oil_money'], $v['use_fanli_money'], 2), $v['oil_counts'], 20);

                foreach ($v as $key => $val) {
                    if (in_array($key, ['oil_counts', 'oil_price', 'oil_money'])) {
                        $_data[$key] = floatval($val);
                    } else {
                        $_data[$key] = trim($val);
                    }
                }

                $company_name = isset($v['receipt_apply_id']) ? $v['company_name'] . '_' . $v['receipt_apply_id'] : $v['company_name'];
                $data[$company_name]['receipt_apply_id'] = isset($v['receipt_apply_id']) ? $v['receipt_apply_id'] : null;

                if ($_data['oil_money'] > $perReceiptMoney) {
                    $moneyGeArr[$company_name][] = $_data;
                } elseif ($_data['oil_money'] > 20000 && $_data['oil_money'] <= $perReceiptMoney) {
                    $middleArr[$company_name][] = $_data;
                } else {
                    $moneyLeArr[$company_name][] = $_data;
                }

                $data[$company_name]['total_money'] = isset($data[$company_name]['total_money']) ?
                    $data[$company_name]['total_money'] + $_data['oil_money'] : $_data['oil_money'];
                $data[$company_name]['total_num'] = isset($data[$company_name]['total_num']) ?
                    $data[$company_name]['total_num'] + 1 : 1;

                $moneyNum = ceil($data[$company_name]['total_money'] / $perReceiptMoney);
                $countsNum = ceil($data[$company_name]['total_num'] / $this->minNum);

                $data[$company_name]['type'] = $moneyNum > $countsNum ? 'money' : 'counts';
                $data[$company_name]['receiptsCounts'] = $moneyNum > $countsNum ? $moneyNum : $countsNum;

                $data[$company_name]['moneyGeArr'] = isset($moneyGeArr[$company_name]) ?
                    $moneyGeArr[$company_name] : [];
                $data[$company_name]['middleArr'] = isset($middleArr[$company_name]) ? $middleArr[$company_name]
                    : [];
                $data[$company_name]['moneyLeArr'] = isset($moneyLeArr[$company_name]) ?
                    $moneyLeArr[$company_name] : [];

                unset($_data);
            }

            $result = [];
            foreach ($data as $k => &$v) {
                if ($v['moneyGeArr']) {
                    $v['moneyGeArr'] = $this->sortByMoney($v['moneyGeArr']);
                }
                if ($v['moneyLeArr']) {
                    $v['moneyLeArr'] = $this->sortByMoney($v['moneyLeArr']);
                }
                if ($v['middleArr']) {
                    $v['middleArr'] = $this->sortByMoney($v['middleArr']);
                }
                $result[$k] = $this->makeReceiptDetail($v);
            }

            if ($result) {
                $excelData = [];
                foreach ($result as $k => $v) {
                    foreach ($v as $a => $b) {
                        $_company_name = explode('_', $k);
                        $commonData = [
                            'company_name' => $_company_name[0],
                            'totalMoney'   => $b['totalMoney'],
                            'receipts_num' => $b['receipts_num'],
                        ];

                        if ($b['data']) {
                            foreach ($b['data'] as $o => $p) {
                                $commonData['receipt_apply_id'] = $p['receipt_apply_id'];
                                $commonData['receipt_no'] = $p['receipt_no'];
                                $commonData['drawer'] = $p['drawer'];
                                $commonData['addr_name'] = isset($p['addr_name']) ? $p['addr_name'] : '';
                                $commonData['addr_mobile'] = isset($p['addr_mobile']) ? $p['addr_mobile'] : '';
                                $commonData['address'] = isset($p['address']) ? $p['address'] : '';

                                $commonData['receipt_type'] = $p['receipt_type'];
                                $commonData['receipt_project'] = $p['receipt_project'];
                                $commonData['model'] = $p['model'];
//                                $commonData['oil_price'] = sprintf("%.9f", $p['oil_price']);
                                if (in_array($type, ['gas', 'gas-100w'])) {
                                    $commonData['tax_rate'] = 0.09;
                                    $commonData['unit_model'] = 'Kg';
                                } else {
                                    $commonData['unit_model'] = '升';
                                    $commonData['tax_rate'] = $type ? 0.13 : 0.16;
                                }

                                // 根据规格型号查询油品信息，查不到抛异常
                                $checkOilTypeInfo = OilTypeNo::getByOilNo(['oil_no' => $p['model']]);
                                if (!$checkOilTypeInfo) {
                                    throw new \RuntimeException('规格型号【' . $p['model'] . '】未在油品数据维护里，请先维护！', 2);
                                }
//                                $commonData['sec_oil_type'] = OilType::$oil_sec_type[$checkOilTypeInfo['oil_sec_type']]['title']; // 二级分类
//                                $commonData['tax_no'] = $checkOilTypeInfo['tax_no']; // 税收编码
                                $commonData['unit_model'] = $checkOilTypeInfo['unit']; // 单位

                                $commonData['payee'] = '洪玉洁';
                                $commonData['checker'] = '王聪';
                                $commonData['use_fanli_money'] = $p['use_fanli_money'];

                                if ($b['receipts_num'] > 1) {
                                    $commonData['oil_money'] = round($b['oil_money'], 2);
                                    $commonData['oil_counts'] = sprintf("%.6f", bcdiv($p['oil_counts'], $b['receipts_num'], 20));
                                    //$commonData['use_fanli_money'] = sprintf("%.6f", bcdiv($p['use_fanli_money'], $b['receipts_num'], 20));
                                } else {
                                    $commonData['oil_money'] = round($p['oil_money'], 2);
                                    $commonData['oil_counts'] = sprintf("%.6f", $p['oil_counts']);
                                }
                                $commonData['oil_price'] = bcdiv(bcadd($commonData['oil_money'],$commonData['use_fanli_money'],2), $commonData['oil_counts'],9);
                                $commonData['oil_month'] = $p['oil_month'];
                                $commonData['fax_no'] = $p['fax_no'];
                                $commonData['bank_account'] = $p['bank_account'];
                                $commonData['company_address'] = $p['company_address'];
                                $commonData['origin_remark'] = $p['remark'];
                                $commonData['remark'] = $p['remark'];

                                if ($o > 0) {
                                    $commonData['totalMoney'] = '';
                                    $commonData['totalMoney'] = '';
                                    $commonData['payee'] = '';
                                    $commonData['checker'] = '';
                                    $commonData['company_name'] = '';
                                    $commonData['fax_no'] = '';
                                    $commonData['receipts_num'] = null;
                                    $commonData['company_address'] = '';
                                    $commonData['bank_account'] = '';
                                }

                                $excelData[$p['receipt_apply_id']][] = $commonData;
                            }
                        }

                    }
                }
            } else {
                throw new \RuntimeException('无处理结果，请检查', 2);
            }
            Log::debug('$excelData', [$excelData], 'testPreSplit');

            if ($excelData) {
                $index = 0;
                $group_name = '';
                foreach ($excelData as $_excelData) {
                    foreach ($_excelData as $v) {
                        $v['addr_mobile'] = $v['addr_mobile'] . "\t";
                        if ($v['receipts_num']) {
                            $remark = $v['remark'];
                            for ($i = 0; $i < $v['receipts_num']; $i++) {
                                $index++;
                                $group_name = Helper::uuid();
                                $v['group_name'] = $group_name;
                                $newResult[] = $v;
                            }
                        } else {
                            $v['group_name'] = $group_name;
                            $newResult[] = $v;
                        }
                    }
                }

            }
        }

        $newResult = $this->splitFanli($newResult);

        Log::debug('return $newResult' . count($newResult), [], 'testPreSplits');

        return $newResult;
    }

    /*
     * 多主体发票拆分校验
     */
    public function checkCompany($params)
    {
        $from_company_info = \Models\OilOperators::getById(['id'=>$params['from_company']]);
        if(!$from_company_info){
            throw new \RuntimeException("转出公司不存在！",2);
        }

        if($from_company_info->status != 1){
            throw new \RuntimeException("转出公司状态已停用！",2);
        }

        if(!in_array($from_company_info->company_type,["A","B"]))
        {
            throw new \RuntimeException("转出公司类型不正确！",2);
        }

        $into_company_info = \Models\OilOperators::getById(['id'=>$params['into_company']]);
        if(!$into_company_info){
            throw new \RuntimeException("转入公司不存在！",2);
        }

        if($into_company_info->status != 1){
            throw new \RuntimeException("转入公司状态已停用！",2);
        }

        if(!in_array($into_company_info->company_type,['A','C']))
        {
            throw new \RuntimeException("转入公司类型不正确！",2);
        }

        return ['from_company_info'=>$from_company_info,'into_company_info'=>$into_company_info];
    }

    /*
     * 多主体拆分核心
     */
    public function splitCoreAction($record,$companys,$pre_amount = 112900)
    {
        //Log::error('splitCoreAction-record:',[$record],'splitCoreAction');
        $receiptList = [];
        foreach ($record as $key => $item){
            //处理
            $x = bcdiv(bcsub($item['receipt_amount'],$item['discount_amount'],4),$pre_amount,12);
            //Log::error('splitCoreAction-X:'.$x,[],'splitCoreAction');
            //张数
            $num = intval(ceil($x));
            //Log::error('splitCoreAction-张数:'.$num,[],'splitCoreAction');
            for ($i = 1;$i <= $num;$i++)
            {
                //Log::error('receipt_one_amount-'.$i.':round(('.$item['receipt_amount'].'/'.$x.'),2)',[],'splitCoreAction');
                $receipt_one_amount = round(bcdiv($item['receipt_amount'],$x,4),2);
                //Log::error('$receipt_one_discount_amount-'.$i.':round(('.$receipt_one_amount.'-'.$pre_amount.'),2)',[],'splitCoreAction');
                $receipt_one_discount_amount = round(bcsub($receipt_one_amount,$pre_amount,4),2);
                //Log::error('$receipt_one_num-'.$i.':round(('.$item['receipt_num'].'/'.$x.'),2)',[],'splitCoreAction');
                $receipt_one_num = bcdiv($item['receipt_num'],$x,5);

                if ($i == $num){
                    //Log::error('最后一张',[],'splitCoreAction');
                    //Log::error('receipt_one_amount-'.$i.':round(('.$item['receipt_amount'].'-（'.$receipt_one_amount.'*'.($num - 1).'）),2)',[],'splitCoreAction');
                    //Log::error('receipt_one_discount_amount-'.$i.':round(('.$item['discount_amount'].'-（'.$receipt_one_discount_amount.'*'.($num - 1).'）),2)',[],'splitCoreAction');
                    //Log::error('receipt_one_num-'.$i.':round(('.$item['receipt_num'].'-（'.$receipt_one_num.'*'.($num - 1).'）),2)',[],'splitCoreAction');
                    $tempNextAmount = bcmul($receipt_one_amount,($num - 1),2);
                    $tempNextDiscountAmount = bcmul($receipt_one_discount_amount,($num - 1),2);
                    $tempNextNum = bcmul($receipt_one_num,($num - 1),5);
                    $receipt_one_amount = round(bcsub($item['receipt_amount'],$tempNextAmount,4),2);
                    $receipt_one_discount_amount = round(bcsub($item['discount_amount'],$tempNextDiscountAmount,4),2);
                    $receipt_one_num = bcsub($item['receipt_num'],$tempNextNum,5);
                }

                $order_no = $this->makeReceiptNo('FOSS_RT');
                $receiptList[] = [
                    'from_company_code' => $companys['from_company_info']->company_code,
                    'order_no' => $order_no,
                    'receipt_type' => 0,
                    'into_company_name' => $companys['into_company_info']->company_name,
                    'into_taxpayer_no' => $companys['into_company_info']->taxpayer_no,
                    'into_address_phone' => $companys['into_company_info']->company_address.$companys['into_company_info']->company_tel,
                    'into_bank_card_no' => $companys['into_company_info']->receipt_bank.$companys['into_company_info']->receipt_bank_account_no,
                    'receipt_category' => $item['receipt_category'],
                    'oil_type' => $item['oil_type'],
                    'receipt_unit' => $item['receipt_unit'],
                    'receipt_num' => $receipt_one_num,
                    'tax_price' => bcdiv($receipt_one_amount,$receipt_one_num,9),
                    'receipt_amount' => $receipt_one_amount,
                    'tax_rate' => $item['tax_rate'],
                    'discount_amount' => $receipt_one_discount_amount,
                    'tax_category_code' => '',
                    'order_type' => '',
                    'remark' => $order_no,
                    'email' => '',
                ];

            }
        }
        //Log::error('splitCoreAction-end:',[$receiptList],'splitCoreAction');
        return $receiptList;
    }

    /*
     * 生成多主体文件
     */
    public function exportReceiptSplitNew($excelData)
    {
        ini_set('memory_limit','1024M');
        $realPath = \APP_ROOT . \DIRECTORY_SEPARATOR . 'www';
        //$realPath = realpath(dirname(dirname(__DIR__))) . '/www';

        $titleArr = [
            'from_company_code' => '公司代码',
            'order_no' => '订单号',
            'receipt_type' => '发票类型',
            'into_company_name' => '购方名称',
            'into_taxpayer_no' => '税号',
            'into_address_phone' => '地址电话',
            'into_bank_card_no' => '开户行及账号',
            'receipt_category' => '商品或服务名称',
            'oil_type' => '规格型号',
            'receipt_unit' => '计量单位',
            'receipt_num' => '数量',
            'tax_price' => '含税单价',
            'receipt_amount' => '含税金额',
            'tax_rate' => '税率',
            'discount_amount' => '折扣额',
            'tax_category_code' => '税收分类编码',
            'order_type' => '订单类型',
            'remark' => '备注',
            'email' => '邮箱',
        ];
        $excelFilePath = ExcelWriter::exportXls([
            'fileName'  => 'receiptSplit' . DIRECTORY_SEPARATOR . 'invoice_split_' . date("YmdHis") . rand(100, 999),
            'sheetName' => '开票拆分文件',
            'fileExt'   => 'xls',
            'download'  => TRUE,
            'title'     => $titleArr,
            'data'      => $excelData,
        ], function ($phpExcelObj, $data, $lineCell) {
            if (in_array($data['name'], ['fax_no', 'xnum', 'remark'])) {
                $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
            } else {
                $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
            }
        });

        return str_replace($realPath,'',$excelFilePath);
    }

    public function internalReceiptSplit($data,$pre_amount = 112900)
    {
        $_data = \helper::objectToArray($data);
        $supplier_ids = array_column($_data,'supplier_id');

        $supplierUnitMap = OilSupplierReceiptReturnUnit::whereIn('supplier_id',$supplier_ids)->get()->toArray();

        $_supplierUnitMap = [];
        if($supplierUnitMap){
            foreach ($supplierUnitMap as $item)
            {
                $oil_name = $item['oil_name'];
                if($oil_name == '天然气'){
                    $oil_name = "天然气类";
                }
                $_supplierUnitMap[$item['supplier_id']][$oil_name] = $item['unit'];
            }
        }else{
            throw new \RuntimeException('供应商回票单位未维护'.implode(',',$supplier_ids),2);
        }

        //校验并拼装 unit oil_sec_type_name
        $result = [];
        foreach ($_data as &$value)
        {
            if(!in_array($value['supplier_id'],array_keys($_supplierUnitMap))){
                throw new \RuntimeException('供应商回票单位未维护'.$value['supplier_id'],2);
            }

            if(!isset($_supplierUnitMap[$value['supplier_id']][$value['oil_sec_type_name']])){
                throw new \RuntimeException('供应商'.$value['supplier_id'].'的'.$value['oil_sec_type_name'].'未维护单位',2);
            }

            $value['supplier_return_unit'] = $_supplierUnitMap[$value['supplier_id']][$value['oil_sec_type_name']];
            $value['supplier_return_unit'] = empty($value['supplier_return_unit']) ? '空' : $value['supplier_return_unit'];

            if($value['unit'] != $value['supplier_return_unit']){
                $value['trade_num_sum'] = $this->transferUint($value['supplier_return_unit'],$value['trade_num_sum'],$value['oil_sec_type_name']);
                $value['origin_trade_num'] = $this->transferUint($value['supplier_return_unit'],$value['origin_trade_num'],$value['oil_sec_type_name']);
            }

            $trade_num[$value['oil_sec_type_name'].$value['supplier_return_unit']][] = $value['trade_num_sum'];
            $receipt_money[$value['oil_sec_type_name'].$value['supplier_return_unit']][] = $value['receipt_money_sum'];
            $discount_money[$value['oil_sec_type_name'].$value['supplier_return_unit']][] = $value['use_fanli_money_sum'];

            $result[$value['oil_sec_type_name'].$value['supplier_return_unit']]['supplier_id'] = $value['supplier_id'];
            $result[$value['oil_sec_type_name'].$value['supplier_return_unit']]['receipt_apply_id'] = $value['receipt_apply_id'];
            $result[$value['oil_sec_type_name'].$value['supplier_return_unit']]['oil_sec_type_name'] = $value['oil_sec_type_name'];
            $result[$value['oil_sec_type_name'].$value['supplier_return_unit']]['unit'] = $value['supplier_return_unit'];
            $result[$value['oil_sec_type_name'].$value['supplier_return_unit']]['trade_num'] = strval(round(array_sum($trade_num[$value['oil_sec_type_name'].$value['supplier_return_unit']]),2));
            $result[$value['oil_sec_type_name'].$value['supplier_return_unit']]['receipt_money'] = strval(round(array_sum($receipt_money[$value['oil_sec_type_name'].$value['supplier_return_unit']]),2));
            $result[$value['oil_sec_type_name'].$value['supplier_return_unit']]['discount_money'] = strval(round(array_sum($discount_money[$value['oil_sec_type_name'].$value['supplier_return_unit']]),2));
            $result[$value['oil_sec_type_name'].$value['supplier_return_unit']]['split_num'] = $this->getSplitNum(array_sum($receipt_money[$value['oil_sec_type_name'].$value['supplier_return_unit']]),array_sum($discount_money[$value['oil_sec_type_name'].$value['supplier_return_unit']]),$pre_amount);

        }

        return $result;
    }

    public function internalReceiptData($data)
    {
        $_data = \helper::objectToArray($data);
        $supplier_ids = array_column($_data,'supplier_id');

        $supplierUnitMap = OilSupplierReceiptReturnUnit::whereIn('supplier_id',$supplier_ids)->get()->toArray();

        $_supplierUnitMap = [];
        if($supplierUnitMap){
            foreach ($supplierUnitMap as $item)
            {
                $oil_name = $item['oil_name'];
                if($oil_name == '天然气'){
                    $oil_name = "天然气类";
                }
                $_supplierUnitMap[$item['supplier_id']][$oil_name] = $item['unit'];
            }
        }else{
            throw new \RuntimeException('供应商回票单位未维护'.implode(',',$supplier_ids),2);
        }

        //校验并拼装 unit oil_sec_type_name
        $result = [];
        foreach ($_data as &$value)
        {
            if(!in_array($value['supplier_id'],array_keys($_supplierUnitMap))){
                throw new \RuntimeException('供应商回票单位未维护'.$value['supplier_id'],2);
            }

            if(!isset($_supplierUnitMap[$value['supplier_id']][$value['oil_sec_type_name']])){
                throw new \RuntimeException('供应商'.$value['supplier_id'].'的'.$value['oil_sec_type_name'].'未维护单位',2);
            }

            $value['supplier_return_unit'] = $_supplierUnitMap[$value['supplier_id']][$value['oil_sec_type_name']];
            $value['supplier_return_unit'] = empty($value['supplier_return_unit']) ? '空' : $value['supplier_return_unit'];

            if($value['unit'] != $value['supplier_return_unit']){
                $value['trade_num_sum'] = $this->transferUint($value['supplier_return_unit'],$value['trade_num_sum'],$value['oil_sec_type_name']);
                $value['origin_trade_num'] = $this->transferUint($value['supplier_return_unit'],$value['origin_trade_num'],$value['oil_sec_type_name']);
            }

            $trade_num[$value['oil_name'].$value['supplier_return_unit']][] = $value['trade_num_sum'];
            $receipt_money[$value['oil_name'].$value['supplier_return_unit']][] = $value['receipt_money_sum'];
            $fanli_money[$value['oil_name'].$value['supplier_return_unit']][] = $value['fanli_money_sum'];
            $discount_money[$value['oil_name'].$value['supplier_return_unit']][] = $value['use_fanli_money_sum'];
            $trade_money[$value['oil_name'].$value['supplier_return_unit']][] = $value['trade_money_sum'];

            $temp = $value;
            $temp['trade_num_sum'] = strval(round(array_sum($trade_num[$value['oil_name'].$value['supplier_return_unit']]),2));
            $temp['receipt_money_sum'] = strval(round(array_sum($receipt_money[$value['oil_name'].$value['supplier_return_unit']]),2));
            $temp['fanli_money_sum'] = strval(round(array_sum($fanli_money[$value['oil_name'].$value['supplier_return_unit']]),2));
            $temp['use_fanli_money_sum'] = strval(round(array_sum($discount_money[$value['oil_name'].$value['supplier_return_unit']]),2));
            $temp['trade_money_sum'] = strval(round(array_sum($trade_money[$value['oil_name'].$value['supplier_return_unit']]),2));
            $temp['oil_sec_type_name'] = $value['oil_sec_type_name'];
            $temp['supplier_return_unit'] = $value['supplier_return_unit'];
            $temp['internal'] = true;

            $result[$value['oil_name'].$value['supplier_return_unit']] = $temp;
        }

        return $result;
    }

    public function checkInternalReceiptSplit($data)
    {
        $_data = \helper::objectToArray($data);
        $supplier_ids = array_column($_data,'supplier_id');

        $supplierUnitMap = OilSupplierReceiptReturnUnit::leftJoin('oil_station_supplier','oil_station_supplier.id','=','oil_supplier_receipt_return_unit.supplier_id')
            ->selectRaw('oil_supplier_receipt_return_unit.*,oil_station_supplier.supplier_name')
            ->whereIn('supplier_id',$supplier_ids)->get()->toArray();

        $_supplierUnitMap = $_supplierUnitMapName = [];
        if($supplierUnitMap){
            foreach ($supplierUnitMap as $item)
            {
                $oil_name = $item['oil_name'];
                if($oil_name == '天然气'){
                    $oil_name = "天然气类";
                }
                $_supplierUnitMap[$item['supplier_id']][$oil_name] = $item['unit'];
                $_supplierUnitMapName[$item['supplier_id']] = $item['supplier_name'];
            }
        }else{
            throw new \RuntimeException('供应商回票单位未维护'.implode(',',$supplier_ids),2);
        }

        //校验并拼装 unit oil_sec_type_name
        $result = [];
        foreach ($_data as &$value)
        {
            if(!in_array($value['supplier_id'],array_keys($_supplierUnitMap))){
                throw new \RuntimeException('供应商回票单位未维护'.$value['supplier_id'],2);
            }

            if(!isset($_supplierUnitMap[$value['supplier_id']][$value['oil_sec_type_name']])){
                throw new \RuntimeException('供应商'.$value['supplier_id'].'的'.$value['oil_sec_type_name'].'未维护单位',2);
            }

            $value['supplier_return_unit'] = $_supplierUnitMap[$value['supplier_id']][$value['oil_sec_type_name']];
            $value['supplier_return_unit'] = empty($value['supplier_return_unit']) ? '空' : $value['supplier_return_unit'];

            if($value['supplier_return_unit'] == '空'){
                $result[$value['supplier_id'].$value['supplier_return_unit']]['supplier_id'] = $value['supplier_id'];
                $result[$value['supplier_id'].$value['supplier_return_unit']]['supplier_name'] = $_supplierUnitMapName[$value['supplier_id']];
                $result[$value['supplier_id'].$value['supplier_return_unit']]['oil_sec_type_name'] = $value['oil_sec_type_name'];
                $result[$value['supplier_id'].$value['supplier_return_unit']]['unit'] = $value['supplier_return_unit'];
            }
        }

        return $result;
    }

    public function getSplitNum($receipt_amount,$discount_amount,$pre_amount)
    {
        $x = bcdiv(bcsub($receipt_amount,$discount_amount,4),$pre_amount,12);
        //张数
        $num = intval(ceil($x));

        return $num;
    }

    public function transferUint($new,$trade_num,$oil_name)
    {
        $new_trade_num = $trade_num;
        if($new == '吨' && $oil_name == '柴油'){
            $new_trade_num = $trade_num / 1176;
        }
        if($new == '吨' &&  in_array($oil_name,['汽油','甲醇汽油','乙醇汽油'])){
            $new_trade_num = $trade_num / 1388;
        }

        if($new == '吨' &&  in_array($oil_name,['天然气'])){
            $new_trade_num = $trade_num / 1000;
        }

        return $new_trade_num;
    }
}
