<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/9/29/029
 * Time: 11:50
 */

namespace Fuel\Service;

use \Framework\Job;
use \Framework\Config;
use Models\OilAccountChargeWechat;

require_once APP_ROOT.DIRECTORY_SEPARATOR.'library'.DIRECTORY_SEPARATOR.'Framework'.DIRECTORY_SEPARATOR.'SDK'.DIRECTORY_SEPARATOR.'weChatPay'.DIRECTORY_SEPARATOR.'WxPay.Data.php';
require_once APP_ROOT.DIRECTORY_SEPARATOR.'library'.DIRECTORY_SEPARATOR.'Framework'.DIRECTORY_SEPARATOR.'SDK'.DIRECTORY_SEPARATOR.'weChatPay'.DIRECTORY_SEPARATOR.'WxPay.Api.php';
require_once APP_ROOT.DIRECTORY_SEPARATOR.'library'.DIRECTORY_SEPARATOR.'Framework'.DIRECTORY_SEPARATOR.'SDK'
    .DIRECTORY_SEPARATOR.'weChatPay'.DIRECTORY_SEPARATOR.'WxPay.JsApiPay.php';

class weChatPay
{
    static $deviceInfo = 'WEB';

    static $tradeType = 'JSAPI';

    static $mchName = '汇通天下石油化工（大连）有限公司 - 油卡充值';

    /**
     * 微信支付统一下单
     * out_trade_no、body、total_fee、trade_type必填
     * @param array $params
     * @return null
     */
    static public function createOrder(array $params)
    {

        \helper::argumentCheck(['id', 'out_trade_no', 'total_fee', 'openid'], $params);

        $result = NULL;

        $payObject = new \WxPayUnifiedOrder();
        $payObject->SetOut_trade_no($params['out_trade_no']);

        $totalFee = $params['total_fee'] * 100;
        $payObject->SetTotal_fee($totalFee);
        $payObject->SetBody(self::$mchName);
        $payObject->SetTrade_type(self::$tradeType);
        $payObject->SetOpenid($params['openid']);

        $attach = [
            'id'        => $params['id'],
            'no'        => $params['out_trade_no'],
            'total_fee' => $params['total_fee']
        ];
        $payObject->SetAttach(
            json_encode($attach)
        );

        try {
            $result = \WxPayApi::unifiedOrder($payObject);

            if ($result['return_code'] != 'SUCCESS' || $result['result_code'] != 'SUCCESS') {
                \Framework\Log::dataLog('wxError:' . var_export($payObject,true), 'wxError');

            }

        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $result;
    }

    /**
     * 支付回调处理
     * @param array $params
     * @return mixed
     */
    static public function notifyResult(array $params)
    {
        $data = NULL;
        $checkExist = OilAccountChargeWechat::getByTransactionId($params['transaction_id']);
        if(!$checkExist || !isset($checkExist->id)){
            $data = OilAccountChargeWechat::add($params);
        }
        return $data;
    }

    /**
     * 查询微信订单
     * @param array $params
     * @param string out_trade_no
     * @return null|\成功时返回
     */
    static public function orderDetail(array $params)
    {
        \helper::argumentCheck(['out_trade_no'], $params);
        $result = NULL;
        $payObject = new \WxPayOrderQuery();
        $payObject->SetOut_trade_no($params['out_trade_no']);

        try {
            $result = \WxPayApi::orderQuery($payObject);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $result;
    }

    /**
     * 关闭微信订单
     * @param array $params
     * @param string out_trade_no
     * @return null|\成功时返回
     */
    static public function closeOrder(array $params)
    {
        \helper::argumentCheck(['out_trade_no'], $params);

        $result = NULL;
        $payObject = new \WxPayCloseOrder();
        $payObject->SetOut_trade_no($params['out_trade_no']);

        try {
            $result = \WxPayApi::closeOrder($payObject);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $result;
    }

    /**
     * 下载对账单
     * bill_date 为必填参数
     * @param array $params
     * @return null|\成功时返回
     */
    static public function downloadBill(array $params)
    {
        \helper::argumentCheck(['bill_date'], $params);
        $result = NULL;
        $payObject = new \WxPayDownloadBill();
        $payObject->SetBill_date($params['bill_date']);

        try {
            $result = \WxPayApi::downloadBill($payObject);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $result;
    }

    /**
     * 获取jsapi支付的参数
     * @param array $UnifiedOrderResult 统一支付接口返回的数据
     * @throws WxPayException
     *
     * @return json数据，可直接填入js函数作为参数
     */
    public function GetJsApiParameters($UnifiedOrderResult)
    {
        if(!array_key_exists("appid", $UnifiedOrderResult)
            || !array_key_exists("prepay_id", $UnifiedOrderResult)
            || $UnifiedOrderResult['prepay_id'] == "")
        {
            throw new WxPayException("参数错误");
        }
        $jsapi = new WxPayJsApiPay();
        $jsapi->SetAppid($UnifiedOrderResult["appid"]);
        $timeStamp = time();
        $jsapi->SetTimeStamp("$timeStamp");
        $jsapi->SetNonceStr(WxPayApi::getNonceStr());
        $jsapi->SetPackage("prepay_id=" . $UnifiedOrderResult['prepay_id']);
        $jsapi->SetSignType("MD5");
        $jsapi->SetPaySign($jsapi->MakeSign());
        $parameters = json_encode($jsapi->GetValues());
        return $parameters;
    }

    /**
     * 获取OpenId
     * @return \用户的openid
     */
    static public function GetOpenid()
    {
        $jsApi = new \JsApiPay();

        return $jsApi->GetOpenid();
    }
}