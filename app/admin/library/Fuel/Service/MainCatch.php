<?php
/**
 * Created by PhpStorm.
 * User: leiqing
 * Date: 20/1/6
 * Time: 下午2:47
 */

namespace Fuel\Service;


use Framework\Log;
use Fuel\Request\dspClient;
use Fuel\Request\OilAgentClient;

class MainCatch
{
    public function configtradeduration(array $params)
    {
        $res = dspClient::post([
            'method' => 'huoyunren.gascard.configtradeduration',
            'data' => [
                'data' => json_encode([
                    'cardNo' => $params['main_no'],
                    'duration' => $params['catch_day'],
                ]),
                'format' => 'json'
            ]
        ]);

        return $res;
    }

    /**
     * 向代理系统添加主卡
     */
    public function addMain2Dsp($params = [])
    {
        //添加主卡
        $apiParams = [
            'method' => 'crawl-provider.accountservice.addGspCard',
            'data'   => $params,
        ];

        $result = OilAgentClient::post(
            $apiParams
        );

        return $result;

    }

    public function crawlTaskCreate($params,$mainInfo)
    {
        $_params = [
            'beginDate' => $params['start_time'],
            'endDate' => $params['end_time'],
            'password' => $mainInfo->account_password,
            'parentcard' => $params['main_no'], //主卡
        ];
        if( !empty($params['vice_no']) ){
            $_params['cardList'] = explode("|",$params['vice_no']);
        }
        $apiParams = [
            'method' => 'crawl-provider.fuelcardservice.addTask',
            'data'   => [
                'account'=> $params['oil_com'] == 1 ? $mainInfo->account_name : $params['main_no'],
                'cardtype'=> $params['oil_com'] == 1 ? 'zsh' : 'zsy',
                #'tasktype'=> $params['oil_com'] == 1 ? 'zshGASCrawler' : 'zsyGASCrawler',
                'tasktype'=> $params['task_type'],
                'level' => 0,
                'params' => \GuzzleHttp\json_encode($_params)
            ],
        ];

        Log::error('请求补爬参数:',$apiParams,'crawlTaskCreate');
        $result = OilAgentClient::post(
            $apiParams
        );
        Log::error('请求补爬结果:',$result,'crawlTaskCreate');

        return $result;
    }

}