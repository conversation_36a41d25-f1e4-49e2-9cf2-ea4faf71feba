<?php
/**
 * 开通机构
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-4-26
 * Time: 上午11:12
 */

namespace Fuel\Service;

use Framework\Config;
use Framework\Log;
use Fuel\Request\EfficacyClient;

class Efficacy
{
    /**
     * 获取销项发票
     * @param array $params
     * @return array
     */
    static public function getReceiptManage(array $params)
    {
        $tax_nos = implode(',',\Fuel\Defines\ReceiptReturn::$buyer_tax_no_map);
        $params = array_merge($params,['salerTaxNum'=>$tax_nos]); //339901999999199,91130405MAC02J407Y
        $args = [
            'payload' => \GuzzleHttp\json_encode($params,256),
            'pathVariableMap' => [
                'organizationId' => 0,
            ]
        ];
//        '/v1/rest/invoke?namespace=HZERO&serverCode=HSCS-COLLECT&interfaceCode=hscs-collect.ope-mplatform.queryInvoiceHistoty'
        $_data = (new EfficacyClient())->request('ope-mplatform/queryInvoiceHistoty',
            'post', $args,[],120);

        //解析参数
        $data = $_data['parseResponseData']['payload'];
        if($data['total'] > 0 && $data['result']){
            return $data['result'];
        }else{
            return [];
        }
    }

    /**
     * 获取进项发票
     * @param $params
     * @return array
     *
     */
    static public function getReceiptReturn($params)
    {
        $tax_nos = implode(',',\Fuel\Defines\ReceiptReturn::$buyer_tax_no_map);
        $params = array_merge($params,['taxno'=>$tax_nos]); //339901999999199,91130405MAC02J407Y
        $args = [
            'payload' => \GuzzleHttp\json_encode($params,256),
            'pathVariableMap' => [
                'organizationId' => 0,
            ]
        ];
        ///v1/rest/invoke?namespace=HZERO&serverCode=HSCS-COLLECT&interfaceCode=hscs-collect.invoice.receivable
        $_data = (new EfficacyClient())->request('invoice/receivable',
            'post', $args,[],120);

        //解析参数
        $data = $_data['parseResponseData']['payload'];
        if($data['result']){
            return $data['result'];
        }else{
            return [];
        }
    }
    
    static public function handReceiptReturn($params,$no)
    {
        $params = array_merge($params,['taxno'=>$no]);
        $args = [
            'payload' => \GuzzleHttp\json_encode($params,256),
            'pathVariableMap' => [
                'organizationId' => 0,
            ]
        ];
        ///v1/rest/invoke?namespace=HZERO&serverCode=HSCS-COLLECT&interfaceCode=hscs-collect.invoice.receivable
        $_data = (new EfficacyClient())->request('invoice/receivable',
            'post', $args,[],120);
    
        //解析参数
        $data = $_data['parseResponseData']['payload'];
        if($data['result']){
            return $data['result'];
        }else{
            return [];
        }
    }

    /*
     * 发票签收
     */
    static public function doSign($params)
    {
        $args = [
            'payload' => \GuzzleHttp\json_encode($params,256),
            'pathVariableMap' => [
                'organizationId' => 0,
            ]
        ];
        ///v1/rest/invoke?namespace=HZERO&serverCode=HSCS-COLLECT&interfaceCode=hscs-collect.invoice.receiptPool
        $_data = (new EfficacyClient())->request('invoice/receiptPool',
            'post', $args,[],120);

        //解析参数
        $data = $_data['parseResponseData']['payload'];

        return $data;
    }
}