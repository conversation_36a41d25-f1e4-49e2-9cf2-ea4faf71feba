<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 19-9-26
 * Time: 下午12:02
 */

namespace Fuel\Service;


use Framework\DingTalk\DingTalkAlarm;
use Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCompany;
use Models\OilCrmOrg;
use Fuel\Service\AccountCenter\BaseService;
use Models\OilOrg;

class Crm extends BaseService
{
    static public function getCrmOrgList($params)
    {
        \helper::argumentCheck(['startTime','endTime'],$params);
        $params['productline'] = 30; //指定油品

        $data = \Fuel\Request\CrmClient::post(
            [
                'method' => 'QryAccountOrganization',
                'data'   => $params
            ]
        );

        return $data;

    }

    static public function cronCrmOrg($params)
    {
        $_maxUpdate = OilCrmOrg::max('lastupdatetime');

        $maxUpdate = $_maxUpdate ? $_maxUpdate : '2015-01-01';

        if(isset($params['maxUpdate']) && $params['maxUpdate']){
            $maxUpdate = $params['maxUpdate'];
        }

        $errorMsg = [];

        $number = 0;
        for($page=1; $page < 10000; $page++) {
            $sendArgs = ['startTime'=>$maxUpdate,'endTime'=>date('Y-m-d'),'pageNum'=>$page,'pageSize'=>100];

            var_dump($sendArgs);
            $crmData = self::getCrmOrgList($sendArgs);
            $number = $number + count($crmData);
            var_dump(count($crmData));
            if($crmData){
                foreach ($crmData as $val)
                {
                    $val = (array)$val;
                    $val['new_whetherproxy'] = $val['new_whetherproxy'] ? 1 : 2;
                    if($val['cdc_id']){
                        //$company = OilCompany::where('crm_id',$val['cdc_id'])->first();
                        $orgInfo = OilOrg::where('crm_id',$val['cdc_id'])->first();
                        if($orgInfo){
                            if(substr($orgInfo->orgcode,0,6) != '201XW3'){
                                $val['orgcode'] = substr($orgInfo->orgcode,0,6);
                            }else{
                                $val['orgcode'] = $orgInfo->orgcode;
                            }


                            if($val['accountnumber']){
                                $info = OilCrmOrg::where('accountnumber',$val['accountnumber'])->first();
                                if($info){
                                    $info->update($val);
                                }else{
                                    $val['sign_aplowneridname'] = $val['aplowneridname'];
                                    OilCrmOrg::add($val);
                                }
                                //OilCrmOrg::updateOrCreate([['accountnumber',$val['accountnumber']]],$val);
                            }
                        }else{
                            $errorMsg[] = ['name'=>$val['name'],'cdc_id'=>$val['cdc_id']];
                        }
                    }

                }
            }else{
                echo 'No data break';
                break;
            }
        }

        if($errorMsg){
            $alarmMessage[] = "* 描述：同步客户档案有未知机构\n";
            $alarmMessage[] = "* 环境：" . (API_ENV . "\n" ? ucfirst(API_ENV) . "\n" : "未知\n");
            $alarmMessage[] = "* 内容：" . json_encode($errorMsg, JSON_UNESCAPED_UNICODE) . "\n";
            $contentStr     = implode("", $alarmMessage);
            try {
                (new DingTalkAlarm())->alarmToGroup('同步客户档案有未知机构', $contentStr, [], TRUE, TRUE);
            } catch (\Exception $e) {
                Log::error('DingTalk' . $e->getMessage(), [strval($e)], 'cronCrmOrg');
            }
        }

        echo '成功插入或更新'.$number.'条';
    }

    static public function updateChildCrmIDAll($params)
    {
        //1 查找子集机构并且crmID为空的。
        $childOrg = OilOrg::whereRaw("(crm_id = '' or crm_id is NULl) and LENGTH(orgcode) > 6")
            //->whereIn('orgcode',['200NYC','200000'])
            ->get();

        //echo $childOrg->toSql();exit;
        $crm_org_map = OilOrg::getCrmOrgMap();

        if($childOrg)
        {
            $updateArr = [];
            foreach ($childOrg as $value){
                echo $value->orgcode;
                $org_len = strlen($value->orgcode);
                $each_num = ($org_len - 6) / 2;

                for($i=1;$i <= $each_num;$i++)
                {
                    $orgcode_new = substr($value->orgcode,0,$org_len - ($i * 2));
                    echo $orgcode_new.'|';
                    if(isset($crm_org_map[$orgcode_new])){
                        $updateArr[] = ['orgcode'=>$value->orgcode,'crm_id'=>$crm_org_map[$orgcode_new]];
                        break;
                    }
                }

            }

            if($updateArr){
                $sql = \helper::batchUpdateToSqlStrExt($updateArr,'oil_org','orgcode');

                echo $sql;

                Capsule::connection()->getPdo()->exec($sql);
            }

            echo "finish";exit;

        }

    }

}