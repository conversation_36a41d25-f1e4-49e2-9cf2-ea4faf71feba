<?php
/**
 * Functions description
 * Created by PhpStorm.
 * User: youki
 * Date: 2018/3/23
 * Time: 下午9:16
 */

namespace Fuel\Service;

use Models\OilCardApplyDetails;
use Models\OilCardApply;
use Models\OilOrg;
use \Framework\Log;

class ApplyDetailService
{
    public function setStep($args)
    {
        switch ($args['step']) {
            case 1 :
                $apply_info = $this->genApplyInfo($args);
                $data = [
                    'status'   => TRUE,
                    'apply_id' => $apply_info->id,
                ];
                break;
            case 2 :
                $data = OilCardApply::edit([
                    'id'       => $args['apply_id'],
                    'app_step' => 2,
                ]);
                // 删除未识别的信息
                OilCardApplyDetails::delNoRegImages($args['apply_id']);
                self::updateOilNum($args['apply_id']);
                break;
            case 3 :
                // 上传身份证信息
                $data = OilCardApply::edit([
                    'id'         => $args['apply_id'],
                    'attach_url' => $args['apply_id'],
                    'app_step'   => 3,
                ]);
                break;
            case 4 :
                // 填写卡信息
                $data = $this->perfectApplyInfo($args);
                break;
        }

        return $data;
    }

    /**
     * @title   判断工单是否存在
     * @desc
     * @version
     * @level   1
     * <AUTHOR> <<EMAIL>>
     * @since   2018/3/27
     * @package Fuel\Service
     * @params  type filedName required?
     * @param $args
     * @return static
     * @returns
     * []
     * @returns
     */
    private function genApplyInfo($args)
    {
        $org_info = OilOrg::getByOrgcode($args['org_code']);

        $params = [
            'status'       => -20,
            'app_status'   => -20,
            'app_step'     => 1,
            'org_id'       => $org_info->id,
            'data_from'    => 3,
            'apply_time'   => date('Y-m-d H:i:s', time()),
            'creator_name' => $args['last_operator']
        ];
        $status = -20;
        $params = array_merge($args, $params);

//        if ($params['oil_com'] == '2') {
//            // 中石油默认重庆
//            $params['fanli_region'] = 23;
//            $params['fanli_region_name'] = '重庆';
//        }

        if ($params['oil_com'] == '9') {
            // 壳牌卡默认陕西
            $params['fanli_region'] = 28;
            $params['fanli_region_name'] = '陕西';
        }

        if (isset($args['apply_id']) && $args['apply_id'] != '') {
            // 已存在
            $cardApply = OilCardApply::find($args['apply_id']);
            $params = $this->insertFixArr($params);
            $cardApply->update($params);
        } else {
            if ($args['oil_com'] != '52') {
                $ext_arr = [
                    'status'     => 10,
                    'app_status' => 10,
                ];
                $status = 10;
                $params = array_merge($params, $ext_arr);
            }
            $params = $this->insertFixArr($params);
            // 新建草稿
            $cardApply = OilCardApply::add($params);
        }

        CardViceApp::addCardAplayStauts([
            'app_id'           => $cardApply->id,
            'status'           => $status,
            'last_operator'    => $params['last_operator'],
            'last_operator_id' => $params['last_operator_id'],
        ]);

        return $cardApply;
    }

    /**
     * @title   查看是否有未完成的工单
     * @desc
     * @version
     * @level   1
     * <AUTHOR> <<EMAIL>>
     * @since   2018/3/27
     * @package Fuel\Service
     * @params  type filedName required?
     * @param $orgcode
     * @returns
     * []
     * @returns
     */
    public function findNoFinish($orgcode)
    {
        return OilCardApply::select('id')
            ->where('orgcode', $orgcode)
            ->where('oil_com', '52')
            ->whereIn('status', ['-20', '-10'])
            ->first();
    }

    // 完善工单信息
    private function perfectApplyInfo($params)
    {
        $data = [
            'id'         => $params['apply_id'],
            'app_step'   => 4,
            'status'     => 10,
            'app_status' => 10,
            'apply_time' => date('Y-m-d H:i:s', time())
        ];

        $fixed = array_merge($params, $data);
        $data = $this->insertFixArr($fixed);

        CardViceApp::addCardAplayStauts([
            'app_id'        => $params['apply_id'],
            'status'        => '10',
            'last_operator' => $params['last_operator']
        ]);

        // 计算开卡数量
        self::updateOilNum($params['apply_id']);

        return OilCardApply::edit($data);
    }

    // 获取单个信息
    public function getApplyById($id)
    {
        return OilCardApply::where('id', $id)->first();
    }

    // 补充对应关系
    private function insertFixArr($args)
    {
        $k_v = [
            'province_name'  => 'province',
            'city_name'      => 'city',
            'district_name'  => 'area',
            'address_id'     => 'org_addr_id',
            'mobile'         => 'org_addr_mobile',
            'org_code'       => 'orgcode',
            'name'           => 'org_addr_name',
            'contact_mobile' => 'mobile',
            'district_id'    => 'area_id'
        ];
        foreach ($k_v as $key => $value) {
            if (isset($args[$key]) && !empty($args[$key])) {
                $args[$value] = $args[$key];
                unset($args[$key]);
            }
        }

        Log::error('拼接后参数', $args, 'yzh_testtestetstetst');
        return $args;
    }

    /**
     * @title 获取上次填写过的信息
     * @desc
     * <AUTHOR> <<EMAIL>>
     * @package Fuel\Service
     * @since 2018/4/3
     * @params  type filedName required?
     * @param $orgcode
     * @return mixed
     */
    public function getLastAddress($orgcode)
    {
        // 查询有无已完成的单子
        $lastInfo = OilCardApply::select([
            'org_addr_id',
            'org_addr_name',
            'org_addr_mobile',
            'address',
            'contact_name',
            'mobile',
            'province',
            'city',
            'area'
        ])
            ->where('orgcode', $orgcode)
            ->where('status', '30')
            ->latest('createtime')
            ->first();

        return $lastInfo;
    }

    /**
     * @title 验证车牌号
     * @desc
     * <AUTHOR> <<EMAIL>>
     * @package Fuel\Service
     * @since 2018/4/3
     * @params  type filedName required?
     * @param $license
     * @return bool
     */
    public static function isCarLicense($license)
    {
        if (empty($license)) {
            return FALSE;
        }
        #匹配民用车牌和使馆车牌
        # 判断标准
        # 1，第一位为汉字省份缩写
        # 2，第二位为大写字母城市编码
        # 3，后面是5位仅含字母和数字的组合
        {
            $regular = "/[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新使]{1}[A-Z]{1}[0-9a-zA-Z]{5}$/u";
            preg_match($regular, $license, $match);
            if (isset($match[0])) {
                return TRUE;
            }
        }

        #匹配特种车牌(挂,警,学,领,港,澳)
        #参考 https://wenku.baidu.com/view/4573909a964bcf84b9d57bc5.html
        {
            $regular = '/[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[A-Z]{1}[0-9a-zA-Z]{4}[挂警学领港澳]{1}$/u';
            preg_match($regular, $license, $match);
            if (isset($match[0])) {
                return TRUE;
            }
        }

        #匹配武警车牌
        #参考 https://wenku.baidu.com/view/7fe0b333aaea998fcc220e48.html
        {
            $regular = '/^WJ[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]?[0-9a-zA-Z]{5}$/ui';
            preg_match($regular, $license, $match);
            if (isset($match[0])) {
                return TRUE;
            }
        }

        #匹配军牌
        #参考 http://auto.sina.com.cn/service/2013-05-03/18111149551.shtml
        {
            $regular = "/[A-Z]{2}[0-9]{5}$/";
            preg_match($regular, $license, $match);
            if (isset($match[0])) {
                return TRUE;
            }
        }
        #匹配新能源车辆6位车牌
        #参考 https://baike.baidu.com/item/%E6%96%B0%E8%83%BD%E6%BA%90%E6%B1%BD%E8%BD%A6%E4%B8%93%E7%94%A8%E5%8F%B7%E7%89%8C
        {
            #小型新能源车
            $regular = "/[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[A-Z]{1}[DF]{1}[0-9a-zA-Z]{5}$/u";
            preg_match($regular, $license, $match);
            if (isset($match[0])) {
                return TRUE;
            }
            #大型新能源车
            $regular = "/[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[A-Z]{1}[0-9a-zA-Z]{5}[DF]{1}$/u";
            preg_match($regular, $license, $match);
            if (isset($match[0])) {
                return TRUE;
            }
        }

        return FALSE;
    }

    /**
     * @title 更办卡数量
     * @desc
     * <AUTHOR> <<EMAIL>>
     * @package Fuel\Service
     * @since 2018/4/3
     * @params  type filedName required?
     * @param $apply_id
     * @return mixed
     */
    public static function updateOilNum($apply_id)
    {
        $count = OilCardApplyDetails::where('card_apply_id', $apply_id)
            ->where('truck_status', 1)
            ->count();

        return OilCardApply::edit([
            'num' => $count,
            'id'  => $apply_id
        ]);
    }
}