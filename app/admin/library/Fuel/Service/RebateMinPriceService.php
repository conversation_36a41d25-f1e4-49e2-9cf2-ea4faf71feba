<?php
/**
* 返利免惠最低价 RebateMinPriceService.php
* $Author: 刘培俊 (liu<PERSON><PERSON><PERSON>@g7.com.cn) $
* $Date: 2021/4/14 15:08 $
* CreateBy Phpstorm
*/

namespace Fuel\Service;

use Framework\Helper;
use Framework\Log;
use Fuel\Defines\RebateFormula;
use Fuel\Defines\RebatePolicy;
use Models\OilProvinces;
use Models\OilRebateFormula;
use Models\OilRebateMinPrice;
use Models\OilRebatePolicy;
use Models\OilRebateTpl;

class RebateMinPriceService
{
    /**
     * 创建
     * @param array $params
     * @return bool
     */
    public function create(array $params)
    {
        \helper::argumentCheck(['name', 'content', 'start_time', 'end_time'], $params);
        // 名称去空格
        $params['name'] = trim($params['name']);
        
        // 公共校验
        $params = $this->commonValidate($params);
        
        // 唯一性校验
        $checkUniqueNameId = OilRebateMinPrice::getResField(['name'=>$params['name']], 'id');
        if ($checkUniqueNameId) {
            throw new \RuntimeException('已存在名为【'.$params['name'].'】的免惠最低价信息', 2);
        }
        
        // 创建
        $createInfo = [
            'name'          => $params['name'],
            'start_time'    => $params['start_time'],
            'end_time'      => $params['end_time'],
            'content'       => $params['content'],
            'creator'       => $this->getApp()->myAdmin->true_name,
            'last_operator' => $this->getApp()->myAdmin->true_name,
        ];
        $data = OilRebateMinPrice::add($createInfo);
        
        return ['id'=>$data['id']];
    }
    
    /**
     * 编辑
     * @param array $params
     * @return bool
     */
    public function update(array $params)
    {
        \helper::argumentCheck(['id','name', 'content', 'start_time', 'end_time'], $params);
    
        // 名称去空格
        $params['name'] = trim($params['name']);
    
        // 公共校验
        $params = $this->commonValidate($params);
        
        // 判断信息是否存在
        $checkInfo = OilRebateMinPrice::getById(['id'=>$params['id']]);
        if (!$checkInfo) {
            throw new \RuntimeException('编辑的信息不存在或已删除', 2);
        }
    
        // 名称唯一性校验
        $checkUniqueNameId = OilRebateMinPrice::getResField(['name'=>$params['name']], 'id');
        if ($checkUniqueNameId && $checkUniqueNameId != $params['id']) {
            throw new \RuntimeException('已存在名为【'.$params['name'].'】的免惠最低价信息', 2);
        }
        
        // 判断是否已匹配规则
        $resBind = $this->checkIsMinPriceBindPolicy(['min_price_id'=>$params['id']]);
        if ($resBind == 1) {
            throw new \RuntimeException('【'.$params['name'].'】已匹配规则，不允许修改！', 2);
        }
    
        // 更新
        $updateInfo = [
            'id'            => $params['id'],
            'name'          => $params['name'],
            'start_time'    => $params['start_time'],
            'end_time'      => $params['end_time'],
            'content'       => $params['content'],
            'last_operator' => $this->getApp()->myAdmin->true_name,
        ];
        
        OilRebateMinPrice::edit($updateInfo);
        
        return ['id'=>$checkInfo['id']];
    }
    
    /**
     * 删除返利公式
     * @param array $params
     * @return bool
     */
    public function delete(array $params)
    {
        \helper::argumentCheck(['ids'], $params);
        
        $Ids = explode(',', $params['ids']);
        if (empty($Ids)) {
            throw new \RuntimeException('ids传参格式不对', 2);
        }
        $checkList = OilRebateMinPrice::getFilterList(['idIn'=>$Ids]);
        if (count($checkList) < 1) {
            throw new \RuntimeException('要删除的免惠最低价不存在', 2);
        }
        
        foreach ($checkList as $info) {
            // 判断是否已匹配规则
            $resBind = $this->checkIsMinPriceBindPolicy(['min_price_id'=>$info['id']]);
            if ($resBind == 1) {
                throw new \RuntimeException('【'.$info['name'].'】已匹配规则，不允许删除！', 2);
            }
        }
        
        OilRebateMinPrice::remove(['ids'=>$Ids]);
        
        return [];
    }
    
    /**
     * 公用校验
     * @param array $params
     * @return bool
     */
    public function commonValidate(array $params)
    {
        \helper::argumentCheck(['start_time', 'end_time'], $params);
        
        // 时间判断
        if ($params['start_time'] >= $params['end_time']) {
            throw new \RuntimeException('结束时间必须大于开始时间', 2);
        }
    
        // 时间不能跨月
        $firstday = date('Y-m-01', strtotime($params['start_time']));
        $lastday  = date('Y-m-d', strtotime("$firstday +1 month -1 day"));
        if (date('Y-m-d', strtotime($params['end_time'])) > $lastday) {
            throw new \RuntimeException('不能跨月设置免惠最低价', 2);
        }
    
        // 免惠最低价校验
        $params['content'] = $this->validateMinPrice($params);
    
        return $params;
    }
    
    /**
     * 免惠最低价校验
     * @param array $params
     * @return bool
     */
    public function validateMinPrice(array $params)
    {
        \helper::argumentCheck(['content'], $params);
    
        $filter = [
            'start_time_lte' => $params['end_time'],
            'end_time_gte' => $params['start_time']
        ];
        if (!empty($params['id'])) {
            $filter['idNeq'] = $params['id'];
        }
        $repeatNum = OilRebateMinPrice::getTotal($filter);
        if ($repeatNum > 0) {
            throw new \RuntimeException('该时间段内有重复免惠最低价！', 2);
        }
        
        if (is_array($params['content'])) {
            $minPriceArr = $params['content'];
        } else {
            if (!Helper::isJson($params['content'])) {
                throw new \RuntimeException('content必须为json格式', 2);
            }
            $minPriceArr = \GuzzleHttp\json_decode($params['content'],true);
        }
        
        $provinceData = OilProvinces::getProviceMap();
        foreach ($provinceData as &$val) {
            if (!$val['code'] || !$val['name']) {
                unset($val);
            }
        }
        
        // 省份排重
        $minPriceData = array_column($minPriceArr,null,'code');
        if (count($minPriceData) != count($minPriceArr)) {
            throw new \RuntimeException('免惠最低价省份有重复！', 2);
        }

        // 数量比较
        if (count($minPriceData) < count($provinceData)) {
            throw new \RuntimeException('免惠最低价省份不全！', 2);
        }
        
        foreach ($minPriceArr as $value) {
            if (!isset($value['code']) || !isset($value['name']) || !isset($value['price'])) {
                throw new \RuntimeException('content数据结构错误！', 2);
            }
            // 免惠最低价必须大于0
            if ($value['price'] <= 0) {
                throw new \RuntimeException('【'.$value['name'].'】免惠最低价必须大于0！', 2);
            }
            if (Helper::getFloatLength($val['discount']) > 2) {
                throw new \RuntimeException('免惠最低价最多只能保留两位小数', 2);
            }
        }
        
        return json_encode($minPriceArr, JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * 获取环境变量app
     * @return object|void
     */
    private function getApp()
    {
        global $app;
        if(!$app->myAdmin || !$app->myAdmin->id){
            $app->myAdmin = new \stdClass();
            $app->myAdmin->id = 8888;
            $app->myAdmin->true_name = '系统自动';
        }
        
        return $app;
    }
    
    /**
     * 检测免惠最低价是否绑定政策
     * @param array $params
     * @return mixed
     */
    public function checkIsMinPriceBindPolicy(array $params)
    {
        \helper::argumentCheck(['min_price_id'], $params);
    
        $num = OilRebatePolicy::getMinPriceBindNum($params);
        
        return $num ? 1 : 2;
    }
    
    /**
     * 检测免惠最低价是否计算
     * @param array $params
     * @return mixed
     */
    public function checkIsMinPriceCalculate(array $params)
    {
        \helper::argumentCheck(['min_price_id'], $params);
        
        $num = OilRebatePolicy::getMinPriceCalNum($params);
        
        return $num ? 1 : 2;
    }
    
    /**
     * 获取绑定和未绑定政策的id集合
     * @return array
     */
    public function getBindOrUnBindIds()
    {
        $ret = [
            'bind_ids'   => [],
            'unbind_ids' => [],
        ];
        
        $allIds = OilRebateMinPrice::getPluckFields(['status'=>1], 'id');
        if (!empty($allIds)) {
            $tmpBindPolicyIds = OilRebatePolicy::getPluckFields(['min_price_idId'=>$allIds], 'min_price_id');
            if (!empty($tmpBindPolicyIds)) {
                $ret['bind_ids'] = array_unique($tmpBindPolicyIds);
            }
            
            $ret['unbind_ids'] = array_diff($allIds, $ret['bind_ids']);
        }
        
        return $ret;
    }
}