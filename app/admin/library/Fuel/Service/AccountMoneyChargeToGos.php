<?php
/**
 * Created by PhpStorm.
 * User: love
 * Date: 17-5-27
 * Time: 上午11:11
 */

namespace Fuel\Service;

use Framework\GosTaskInterface;
use Framework\Helper;
use Fuel\Defines\AccountAssignStatus;
use GosSDK\Gos;
use Models\OilAccountMoneyCharge;
use Models\OilOrg;
use GosSDK\Defines\Methods\AccountMoneyCharge as AccountMoneyChargeMethod;

class AccountMoneyChargeToGos implements GosTaskInterface
{

    /**
     * @title   添加gos
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @param string $id
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     */
    static public function addToGos($id = ''){
        return self::sendBatchCreateTask([$id]);
    }

    /**
     * @title 预处理数据
     * <AUTHOR>
     * @param $data
     * @return mixed
     */
    static public function handleData(&$data)
    {
        foreach ($data as &$v) {
            $v = self::handleOneData($v);
        }

        return $data;
    }

    static public function handleOneData($v)
    {
        $orgInfo = OilOrg::getByOrgIdArrMap([$v['org_id']]);
        $v['org_id'] = $orgInfo[$v['org_id']];
        $v['status'] = AccountAssignStatus::getAssignStatusForGos($v['status']);

        if($v['creator_id']){
            $v['creator_name'] = '客服';
        }else{
            $v['creator_name'] = $v['other_creator'];
        }
        $v['updater_name'] = $v['last_operator'];

        return $v;
    }

    /**
     * @title
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function init()
    {
        $data = [];
        $pageSize = 200;
        for($page=1; $page < 10000; $page++){
            $data = OilAccountMoneyCharge::orderBy('createtime', 'asc')
                ->orderBy('id', 'asc')
                ->offset(($page- 1) * $pageSize)
                ->limit($pageSize)
                ->get()->toArray();

            if (!$data) {
                echo 'no data';
                break;
            }
            $data = self::formatData($data);

            try{
                $data[] = (new Gos())
                    ->setMethod(AccountMoneyChargeMethod::CREATE_BATCH)
                    ->setParams($data)
                    ->async();
            }catch(\Exception $e){
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }
        }

        return $data;
    }

    /**
     * @title   数据格式化
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @param array $data
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function formatData(array $data)
    {
        foreach ($data as &$v) {
            $v = self::handleOneData($v);
        }

        return $data;
    }

    /**
     * @title   单条数据格式化
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @param $v
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    static public function formatUniqueData($v)
    {
        $orgInfo = OilOrg::getByOrgIdArrMap([$v['org_id']]);
        $v['org_id'] = $orgInfo[$v['org_id']];
        $v['status'] = AccountAssignStatus::getAssignStatusForGos($v['status']);

        if($v['creator_id']){
            $v['creator_name'] = '客服';
        }else{
            $v['creator_name'] = $v['other_creator'];
        }
        $v['updater_name'] = $v['last_operator'];

        return $v;
    }

    /**
     * @title   通过ID获取数据
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @param array $ids
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function getData(array $ids)
    {
        $record = OilAccountMoneyCharge::getByIdList($ids);

        $data = [];
        if($record){
            $data = self::formatData($record->toArray());
        }

        return $data;
    }

    static public function sendBatchCreateTask(array $ids, $type = 'async')
    {
        $data   = self::getData($ids);
        $result = (new GosTask())
            ->setTaskName('现金充值单新增')
            ->setIds($ids)
            ->setMethod(AccountMoneyChargeMethod::CREATE_BATCH)
            ->setData($data)
            ->exec($type);

        return $result;
    }

    static public function sendBatchUpdateTask(array $ids, $type = 'async')
    {
        $data   = self::getData($ids);
        $result = (new GosTask())
            ->setTaskName('现金充值单更新')
            ->setIds($ids)
            ->setMethod(AccountMoneyChargeMethod::UPDATE_BATCH)
            ->setData($data)
            ->exec($type);

        return $result;
    }

    static public function sendBatchDeleteTask(array $ids, $type = 'async')
    {
        $result = (new GosTask())
            ->setTaskName('现金充值单删除')
            ->setIds($ids)
            ->setMethod(AccountMoneyChargeMethod::DELETE)
            ->setData(['id'=>$ids])
            ->exec($type);

        return $result;
    }
}