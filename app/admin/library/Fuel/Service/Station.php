<?php

namespace Fuel\Service;

use Exception;
use Framework\Helper;
use Framework\Log;
use Fuel\Defines\CardViceConf;
use Fuel\Defines\StationArea;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilProvinces;
use Models\OilStation;
use Models\OilOwnStation;
use Framework\DingTalk\FeiShuNotify;
use Models\OilCardVice;
use Models\OilCardViceTrades;
use Models\OilStationCard;
use Models\OilStationCardBindDetail;
use Models\OilStationSupplier;
use Models\OilSupplierRelation;


class Station
{
    /**
     * @title   获取油站列表
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package StationSDK\Core
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    static public function getList(array $params)
    {
        $params['isPc'] = 1;
        $params['path'] = "/api/station/v1/station/list";
        $data = (new \StationSDK\Core\Station())->sdkRequest($params);
        return self::formatData($data);
    }

    static public function getStationPriceList(array $params)
    {
        \helper::argumentCheck(['station_code','org_code','start_time','end_time'],$params);
        $params['isPc'] = 1;
        $params['path'] = "/api/station/v1/station/getStationPriceList";
        $data = (new \StationSDK\Core\Station())->sdkRequest($params);
        return self::formatData($data);
    }

    static public function formatData($data)
    {
        $arr = isset($data->data) ? $data->data : $data;
        foreach ($arr as &$v) {
            $address = "";
            if (!empty($v->provice_name)) {
                $address = $v->provice_name;
            } else {
                $v->provice_name = "";
            }
            if (!empty($v->city_name)) {
                $address .= $v->city_name;
            } else {
                $v->city_name = "";
            }
            if (!empty($v->address)) {
                $address .= $v->address;
            } else {
                $v->address = "";
            }
            $v->location_txt = $address;
            if ($v->card_classify == 2 && $v->isdel == 0 && $v->isstop == 0) {
                $v->status_txt = "已上线";
            } else {
                $v->status_txt = "已下线";
            }
            switch ($v->station_type) {
                case 1:
                    $v->station_type_txt = "撬装站";
                    break;
                case 2:
                    $v->station_type_txt = "加油站";
                    break;
                case 3:
                    $v->station_type_txt = "油罐车";
                    break;
                case 4:
                    $v->station_type_txt = "加气站";
                    break;
                case 5:
                    $v->station_type_txt = "加注站";
                    break;
                default:
                    $v->station_type_txt = "";
            }
            if (isset($v->station_ext)) {
                $v->dockor_name = $v->station_ext->dockor_name;
                if (isset($v->station_ext->station_ownner)) {
                    $v->ownner_name = $v->station_ext->station_ownner->name;
                    $v->ownner_email = $v->station_ext->station_ownner->email;
                    $v->ownner_phone = $v->station_ext->station_ownner->phone;
                }
            }
        }
        return $data;
    }

    static public function getStationCodeNameList(array $params)
    {
        $params['path'] = "/api/station/v1/station/list";
        $data = (new \StationSDK\Core\Station())->sdkRequest($params);
        $arr = isset($data->data) ? $data->data : $data;

        $res = [];
        foreach ($arr as $v) {
            $tmp = [
                'station_name'      => $v->station_name,
                'station_code'      => $v->station_code,
                'pcode_name'        => $v->pcode_name,
                'is_highway'        => $v->is_highway,
                'is_highway_txt'    => $v->is_highway_txt
            ];
            $res[] = $tmp;
        }

        return $res;
    }

    /**
     * @title   处理油站负责人
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package StationSDK\Core
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    static public function handleOwnner(array $params)
    {
        $params['path'] = "/api/station/v1/ownner/handle";
        return (new \StationSDK\Core\Station())->sdkRequest($params);
    }

    /**
     * @title   获取省市列表
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package StationSDK\Core
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    static public function getCityList(array $params)
    {
        $params['path'] = "/api/city/v1/city/list";
        return (new \StationSDK\Core\Station())->sdkRequest($params);
    }

    /**
     * @title  获取政策列表
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package StationSDK\Core
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    static public function getPolicyList(array $params)
    {
        $params['path'] = "/api/policy/v1/policy/list";
        return (new \StationSDK\Core\Station())->sdkRequest($params);
    }

    /**
     * @title  获取筛选条件
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package StationSDK\Core
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    static public function getClassify(array $params)
    {
        $params['path'] = "/api/policy/v1/policy/classify";
        return (new \StationSDK\Core\Station())->sdkRequest($params);
    }

    /**
     * @title  添加返利政策
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package StationSDK\Core
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    static public function addPolicy(array $params)
    {
        $params['path'] = "/api/policy/v1/policy/addData";
        return (new \StationSDK\Core\Station())->sdkRequest($params);
    }

    /**
     * @title  查询油站信息
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package StationSDK\Core
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    static public function stationInfo(array $params)
    {
        $params['path'] = "/api/station/v1/station/detail";
        return (new \StationSDK\Core\Station())->sdkRequest($params);
    }

    /**
     * @title  获取返利类别
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package StationSDK\Core
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    static public function getMobileClassify(array $params)
    {
        $params['path'] = "/api/policy/v1/policy/allPolicyClassify";
        return (new \StationSDK\Core\Station())->sdkRequest($params);
    }

    /**
     * 根据时间获取消费记录
     *
     */
    static public function getHistoryList(array $params)
    {
        $params['path'] = "/api/station/v1/history/list";
        return (new \StationSDK\Core\Station())->sdkRequest($params);
    }

    /**
     * @param $params
     * @return array
     */
    public function syncGosStationForLatest($params)
    {
        $result = [];
        $data = (new \GosSDK\Gos())
            ->setMethod('v1/station/getStationByUpdateTime')
            ->setParams((array)$params)
            ->sync();

        if ($data) {
            $stationCodes = [];
            foreach ($data as $k => $v) {
                if (!$v->station_name || !$v->station_code) {
                    continue;
                }
                $stationCodes[] = $v->station_code;
            }

            $_stationMap = OilStation::getByStationCodes($stationCodes);
            foreach ($data as $k => $v) {
                $v->detail_address = $v->address;
                $v->province_code = $v->provice_code;
                $v->province_name = $v->provice_name;
                $v->alias = $v->remark_name;
                $v->oil_com = 20;

                $provinceInfo = OilProvinces::getByCode($v->provice_code);
                if ($provinceInfo) {
                    $v->regions_id = $provinceInfo->id;
                }

                unset($v->id);

                try {
                    if (!isset($_stationMap[$v->station_code])) {
                        $checkExist = OilStation::getByName($v->station_name);
                        if (!$checkExist) {
                            OilStation::add((array)$v);
                            $result[] = $v->station_code;
                        } else {
                            if (in_array($checkExist->oil_com, [20, 21])) {
                                $v->id = $checkExist->id;
                                $this->editStationById($v);
                                $result[] = $v->station_code;
                            }
                        }
                    } else {
                        $this->editStationByName($v);
                        $result[] = $v->station_code;
                    }
                } catch (Exception $e) {
                    Log::error(__METHOD__, ['exception' => strval($e), 'params' => $v]);
                }

            }
        }

        return $result;
    }

    private function editStationByName($v)
    {
        OilStation::where("station_code", $v->station_code)->whereIn('oil_com', [20, 21])->update(
            [
                'detail_address' => $v->address,
                'station_name' => $v->station_name,
                'alias' => $v->remark_name,
                'oil_com' => $v->oil_com,
                'pcode' => $v->pcode,
                'province_code' => $v->provice_code,
                'province_name' => $v->provice_name,
            ]
        );
    }

    private function editStationById($v)
    {
        OilStation::where("id", $v->id)->update(
            [
                'detail_address' => $v->address,
                'station_name' => $v->station_name,
                'alias' => $v->remark_name,
                'oil_com' => $v->oil_com,
                'pcode' => $v->pcode,
                'province_code' => $v->provice_code,
                'province_name' => $v->provice_name,
            ]
        );
    }

    /**
     * @throws Exception
     */
    public static function createOrUpdateBindServiceArea(array $params)
    {
        Capsule::connection()->beginTransaction();
        $existsData = OilOwnStation::getByLock([
            'station_code' => $params['station_code'],
        ]);
        if ($existsData and in_array($existsData->status, [
            OilOwnStation::WAIT_AUDIT,
            OilOwnStation::AUDIT_PASS
            ])) {
            if (!empty($params['update_card_assign_method'])) {
                $existsData->card_assign_method = $params['card_assign_method'];
                $existsData->save();
                Capsule::connection()->commit();
                return;
            }
            throw new Exception("该站点属于待审核状态,无法编辑", 5000100);
        }
        try {

            if (!$existsData) {

                OilOwnStation::add([
                    'station_code' => $params['station_code'],
                    'station_name' => $params['station_name'],
                    'pcode' => $params['pcode'],
                    'pname' => $params['pname'],
                    'area_id' => $params['area_id'],
                    'status' => 1,
                    'contact' => $params['contact'],
                    'contact_phone' => $params['contact_phone'],
                    'createtime' => date('Y-m-d H:i:s'),
                    'creator' => $params['modifier'],
                    'operator_id' => $params['operator_id'],
                    'card_assign_method' => $params['card_assign_method'],
                ]);

            } else {

                if ($existsData->status == OilOwnStation::AUDIT_REFUSE) {

                    $existsData->station_name = $params['station_name'];
                    $existsData->pcode = $params['pcode'];
                    $existsData->pname = $params['pname'];
                    $existsData->area_id = $params['area_id'];
                    $existsData->status = 1;
                    $existsData->contact = $params['contact'];
                    $existsData->contact_phone = $params['contact_phone'];
                    $existsData->updatetime = date("Y-m-d H:i:s");
                    $existsData->admin_remark = "";
                    $existsData->audit_time = null;
                    $existsData->auditor = "";
                    $existsData->modifier = $params['modifier'];
                    $existsData->operator_id = $params['operator_id'];
                    $existsData->card_assign_method = $params['card_assign_method'];
                    $existsData->save();
                }
            }
            Capsule::connection()->commit();
        } catch (Exception $exception) {

            Capsule::connection()->rollBack();
            throw $exception;
        }

        // 飞书提醒
        self::StationVerifyAlarm($params['station_code']);

    }

    /**
     * @param array $params
     * @return object|null
     * <AUTHOR> <<EMAIL>>
     * @since 2021/6/18 11:13 上午
     */
    public static function getBindServiceAreaDataByStationCodes(array $params)
    {
        return OilOwnStation::getByStationCodes($params);
    }

    /**
     * @throws Exception
     */
    public static function auditStationBindServiceArea(array $params)
    {
        Capsule::connection()->beginTransaction();
        $existsObj = OilOwnStation::getByLock([
            'id' => $params['id'],
            'status' => [1],
        ], false);
        try{

            if ($existsObj) {

                if ($existsObj->count() != count($params['id'])) {

                    throw new Exception("绑定关系审核状态异常,请核实后重试", 5001002);
                }
                $existsData = $existsObj->toArray();
                OilOwnStation::edit([
                    'id' => array_column($existsData, 'id')
                ], [
                    'status' => $params['status'],
                    'audit_time' => date("Y-m-d H:i:s"),
                    'auditor' => $params['auditor'],
                ]);

                //已审核的站点才需要记录绑卡记录
                if( $params['status'] == OilOwnStation::AUDIT_PASS ) {
                    foreach ($existsData as $_item) {
                        $changeLog = [];
                        $changeLog['classify'] = StationArea::STATION_CLASSIFY;
                        $changeLog['res_type'] = StationArea::STATION_BIND;
                        $changeLog['res_code'] = $_item['station_code'];
                        $changeLog['res_to_id'] = $_item['area_id'];
                        EntityChange::addRecord($changeLog);
                    }
                }
            }
            Capsule::connection()->commit();
        } catch (Exception $exception) {

            Capsule::connection()->rollBack();
            throw $exception;
        }
    }

    public static function getOilOwnStationList(array $params)
    {
        if (!empty($params['createtimeGe']) and is_string($params['createtimeGe'])) {
            $params['createtimeGe'] .= " 00:00:00";
        }
        if (!empty($params['createtimeLe']) and is_string($params['createtimeLe'])) {
            $params['createtimeLe'] .= " 23:59:59";
        }
        if (!empty($params['updatetimeGe']) and is_string($params['updatetimeGe'])) {
            $params['updatetimeGe'] .= " 00:00:00";
        }
        if (!empty($params['updatetimeLe']) and is_string($params['updatetimeLe'])) {
            $params['updatetimeLe'] .= " 23:59:59";
        }
        return OilOwnStation::getList($params);
    }

    /**
     * @param array $params
     * @throws Exception
     * <AUTHOR> <<EMAIL>>
     * @since 2021/6/29 3:07 下午
     */
    public static function syncOwnStationInfo(array $params)
    {
        $params['path'] = "/api/station/v1/station/getStationBySelfOperatedSupplier";
        $data = (new \StationSDK\Core\Station())->sdkRequest($params);
        foreach ($data as &$v) {
            $v = get_object_vars($v);
            $v['pname'] = $v['supplier_name'];
            $v['where'] = "station_code = '{$v['station_code']}'";
            unset($v['id'], $v['station_code'], $v['supplier_code'], $v['supplier_name']);
        }
        $updateSql = Helper::getBatchEditSql($data, 'oil_own_station');
        Capsule::connection()->getPdo()->exec($updateSql);
    }


    /**
     * 待审核站点通知运营审核
     * @param $stationCode
     * @return bool
     */
    public static function StationVerifyAlarm($stationCode)
    {
        if (!$stationCode) {
            return false;
        }
        $info = OilOwnStation::getStationBindArea(['station_code'=>$stationCode]);
        if (!$info || $info['status'] != 1) {
            return false;
        }

        global $app;
        $chatId = "oc_d00eef070c94a17113c10c22f76ff252";
        $atArr = [
            // "刘培俊"   => "<EMAIL>",
            // "付明乐"   => "<EMAIL>",
        ];
        // 线上
        if ($app->config->api_env == "pro") {
            $chatId = 'oc_e9e744d0ca305df2ef54075df6af8dc2';
            $atArr  = [
//                "王丽华" => "<EMAIL>",
//                "王欢"   => "<EMAIL>",
//                "刘娜"   => "<EMAIL>",
//                "李明霞" => "<EMAIL>",
//                "娄燕"   => "<EMAIL>",
//                "裴慧欣" => "<EMAIL>",
//                "师聪慧" => "<EMAIL>",
                "吕婷"   => "<EMAIL>",
            ];
        }

        $content = [];
        $content[] = "油站名称：" . $info['station_name'] . "\n";
        $content[] = "油站联系人：" . $info['contact'] . "\n";
        $content[] = "油站联系电话：" . $info['contact_phone'] . "\n";
        $content[] = "归属供应商：" . $info['supplier_name'] . "\n";
        $content[] = "归属服务区：" . $info['area_name'] . "\n";
        $content[] = "创建人：" . $info['creator'] . "\n";
        $content[] = "备注：请尽快审核";
        // 飞书通知运营审核
        $apiParams = [
            'title'    => '***油站审核通知***',
            'chat_id'  => $chatId,
            'emails'   => [],
            'msg_type' => 'card',
            'content'  => implode("\n", $content),
            'at'       => $atArr
        ];
        (new FeiShuNotify())->Send($apiParams);

        return true;
    }

    /**
     * 根据station_code获取own_station的审核状态以及绑卡数量
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection|null
     */
    public static function getOwnStation(array $params)
    {
        \helper::argumentCheck(['station_code'], $params);

        $data = OilOwnStation::getByStationCodes($params);

        $stationCardNum = OilStationCard::whereIn('code',$params['station_code'])
            ->selectRaw('code,count(*) as total')
            ->groupBy('code')
            ->get()
            ->toArray();

        $stationCardNumMap = array_column($stationCardNum,'total','code');

        if($data){
            foreach ($data as &$v) {
                if($v['statistics_type'] == 10){
                    $v['card_num'] = $stationCardNumMap[$v['station_code']] ?? 0;
                }else{
                    $v['card_num'] = 1; //统计方式为 记账则 默认认为有card_num为1
                }

            }
        }

        return $data;
    }

    public static function editStationInfo(array $params)
    {
        $info = OilOwnStation::getById(['id'=>$params['id']]);
        if(!$info){
            throw new Exception("油站不合法,请核实后重试", 5001002);
        }
        if( !in_array($params['is_proxy'],[1,2]) ){
            throw new Exception("数据不合法,请核实后重试", 5001005);
        }

        if($params['is_proxy'] == 1) {
            $cardList = OilStationCard::getFilterList(['code' => $info->station_code]);
            if (count($cardList) == 0) {
                throw new Exception("油站下没有主站卡，无法设置为代销站", 5001003);
            }
            foreach ($cardList as $_one) {
                if ($_one['property'] == CardViceConf::CARD_PROPERTY_STATION_CUSTODY) {
                    throw new Exception("油站绑定站点托管卡，无法设置为代销站", 5001004);
                }
            }
        } else {
            $condition['code'] = $info->station_code;
            $condition['_export'] = 1;
            $bindList = OilStationCard::getList($condition);
            $origin = [];
            $tgNum = 0;
            foreach ($bindList as $_item){
                if( $_item->property == CardViceConf::CARD_PROPERTY_STATION_CUSTODY ){
                    $tgNum++;
                }
                $origin[$_item->origin_id] = $_item->origin_id;
            }
            $_tmp = self::getSupplierByCode($info->station_code);
            $nowSupplierId = $_tmp['sid'];
            if( (count($origin) > 1) || ( count($origin) == 1 && !isset($origin[$nowSupplierId]) ) || $tgNum > 0 ) {
                throw new Exception("该油站存在油卡混合使用情况，无法更改站点代销状态", 5001006);
            }
        }

        OilOwnStation::edit(['id'=>$info->id],["is_proxy"=>$params['is_proxy'],"updatetime"=>\helper::nowTime()]);
        return $params;
    }

    /**
     * @param int $code
     * @return array|string[]
     * 根据油站编码，获取供应商和服务区
     */
    public static function getSupplierByCode($code = 0)
    {
        $ownStationInfo = OilOwnStation::getInfoByFilter(['station_code'=>$code], ['id','area_id','status']);
        if ( !empty($ownStationInfo) ) {
            $relationInfo = OilSupplierRelation::getInfoByFilter([
                'area_id' => $ownStationInfo['area_id'],
                'is_area' => 1]);
            if (!empty($relationInfo)) {
                $supplierName = OilStationSupplier::getResField(['id' => $relationInfo['supplier_id']], 'supplier_name');
            }
            $area = \Models\OilStationArea::getResField(['id'=>$ownStationInfo['area_id']],"name");
            return ["sid"=>$relationInfo['supplier_id'],"sname"=>$supplierName,"aid"=>$ownStationInfo['area_id'],"aname"=>$area];
        }
        return ["sid"=>"","sname"=>"","aid"=>"","aname"=>""];
    }


    /**
     * Desc:
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 20/4/23 下午1:44
     * @param array $params
     * @return mixed
     */
    static public function sendSystemOrg(array $params)
    {
        $params['path'] = "/api/station/v1/station/pushListByOrgcode";
        return  (new \StationSDK\Core\Station())->sdkRequest($params);
    }

    /** 
     * Desc:根据station_code获取分配的vice_no
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 20/4/23 下午1:44
     * @param array $params
     * @return mixed
     */ 

    static public function getAssignCardByStationCode(array $params)
    {
        \helper::argumentCheck(['station_code','gms_order_id'], $params);
        
        // $own_station_info = OilOwnStation::getInfoByFilter(['station_code'=>$params['station_code']],['card_assign_method']);
        
        $own_station_info = OilOwnStation::leftJoin('oil_station_area', 'oil_own_station.area_id', '=', 'oil_station_area.id')
        ->leftJoin('oil_supplier_relation', 'oil_supplier_relation.area_id', '=', 'oil_own_station.area_id')
        ->leftJoin('oil_station_supplier', 'oil_supplier_relation.supplier_id', '=', 'oil_station_supplier.id')
        ->where('oil_own_station.station_code',$params['station_code'])
        ->selectRaw("oil_own_station.card_assign_method,oil_station_supplier.supplier_name,oil_station_area.name as area_name,oil_station_area.code as area_code,oil_supplier_relation.supplier_id,oil_supplier_relation.is_area")
        ->first();
        Log::error('getAssignCardByStationCode', ['params' => $params,'own_station_info' => $own_station_info],'getAssignCardByStationCode');
        if(empty($own_station_info)){
            return [];
        }
        
        if($own_station_info->card_assign_method != 4){
            return [];
        }

        //查询绑定的卡片
        $bindCard  = OilStationCard::getFilterList(['code'=>$params['station_code']]);
        $cardNum = count($bindCard);
        Log::error('getAssignCardByStationCode', ['cardNum' => $cardNum],'getAssignCardByStationCode');
        if($cardNum > 1 || $cardNum == 0){
            Log::error('站点关联的油卡数量', ['cardNum' => $cardNum],'getAssignCardByStationCode');
            self::sendCardAlerm('站点关联的油卡数量'.$cardNum,$params['station_code'],$params['gms_order_id'],'找不到唯一油卡');
            return [];
        }

        $vice_info = OilCardVice::getInfoByFilter(['vice_no'=>$bindCard[0]['vice_no']]);
        Log::error('vice_info', ['vice_info' => $vice_info],'getAssignCardByStationCode');
        if(empty($vice_info)){
            Log::error('系统未找到卡片信息', ['cardNum' => $cardNum],'getAssignCardByStationCode');
            self::sendCardAlerm('系统未找到卡片信息'.$vice_info,$params['station_code'],$params['gms_order_id'],'找不到唯一油卡');
            return [];
        }
        Log::error('res', ['vice_no'=>$bindCard[0]['vice_no'],'supplier_name'=>$own_station_info->supplier_name,'supplier_id'=>$own_station_info->supplier_id,'area_code'=>$own_station_info->area_code,'area_name'=>$own_station_info->area_name],'getAssignCardByStationCode');
        return ['vice_no'=>$bindCard[0]['vice_no'],'supplier_name'=>$own_station_info->supplier_name,'supplier_id'=>$own_station_info->supplier_id,'area_code'=>$own_station_info->area_code,'area_name'=>$own_station_info->area_name];

    }

    static public function sendCardAlerm($errstr,$station_code,$gms_order_id,$title)
    {
        if(API_ENV == 'pro'){
            $chat_id  = "oc_37229db5ec049cdaeb4ba4189e1b163e";
            $at = [
                '冼世文' => '<EMAIL>',
                '李明霞' => '<EMAIL>'
            ];
        }else{
            $chat_id  = "oc_37229db5ec049cdaeb4ba4189e1b163e";
            $at = [
                '雷庆' => '<EMAIL>',
            ];
        }
        $content = [
            '订单编号：'.$gms_order_id,
            '站点编码：'.$station_code,
            '异常备注：'.$errstr,
        ];
        $evn = '[环境：'.API_ENV."]";
        $apiParams = [
            'title'    => '代管卡分配异常-'.$title.$evn,
            'chat_id'  => $chat_id,
            'msg_type' => 'card',
            'content'  => implode("\n",$content),
            'at'       => $at
        ];
        
        (new FeiShuNotify())->Send($apiParams);
    }

    static public function sendAssignAlerm($errstr,$gms_order_id)
    {
        if(API_ENV == 'pro'){
            $chat_id  = "oc_37229db5ec049cdaeb4ba4189e1b163e";
            $at = [
                '冼世文' => '<EMAIL>',
                '李明霞' => '<EMAIL>'
            ];
        }else{
            $chat_id  = "oc_37229db5ec049cdaeb4ba4189e1b163e";
            $at = [
                '雷庆' => '<EMAIL>',
            ];
        }
        $trade_info = OilCardViceTrades::where('api_id',$gms_order_id)->first();

        $content = [
            '订单编号：'.$gms_order_id,
            '站点名称：'.$trade_info->trade_place ?? '',
            '站点编码：'.$trade_info->station_code ?? '',
            '异常备注：'.$errstr,
        ];
        $evn = '[环境：'.API_ENV."]";
        $apiParams = [
            'title'    => '代管卡分配异常-分配异常'.$evn,
            'chat_id'  => $chat_id,
            'msg_type' => 'card',
            'content'  => implode("\n",$content),
            'at'       => $at
        ];
        
        (new FeiShuNotify())->Send($apiParams);
    }

}