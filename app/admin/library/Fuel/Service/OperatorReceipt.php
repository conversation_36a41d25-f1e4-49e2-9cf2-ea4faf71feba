<?php

namespace Fuel\Service;


//内部公司发票相关
use Framework\Excel\ExcelReader;
use Framework\Log;
use Fuel\Defines\ReceiptApplyDefine;
use Fuel\Defines\ReceiptApplyStatus;
use Fuel\Defines\TradesType;
use Fuel\Service\Import\ReceiptInternalApply;
use Illuminate\Database\Capsule\Manager as Capsule;
use Jobs\ExportReportDataJob;
use Models\OilAccountAssign;
use Models\OilOperatorDayTrades;
use Models\OilOperatorReceiptInit;
use Models\OilOperators;
use Models\OilOrgOperatorDayTrades;
use Models\OilReceiptApply;
use Models\OilTrades;
use Models\OilTypeBase;

class OperatorReceipt
{

    const OilBaseProd = [
        14 => [14], //柴油
        11 => [11], //汽油
        12 => [12], //甲醇汽油
        13 => [13], //乙醇汽油
        21 => [9, 10, 20], //天然气类
    ];

    const OilBaseTest = [
        14 => [14],
        11 => [11],
        12 => [12],
        13 => [13],
        21 => [9, 10, 20],
    ];

    const OilBaseNameMap = [
        '柴油' => 14,
        '汽油' => 11,
        '甲醇汽油' => 12,
        '乙醇汽油' => 13,
        '天然气类' => 21,
    ];

    //G7WALLET-6097
    const InnerInitTime = "2023-07-01";

    static public function getBaseType($level = "")
    {
        $typeId = self::OilBaseTest;
        if (in_array(API_ENV, ['prod', 'pro'])) {
            $typeId = self::OilBaseProd;
        }
        if (!empty($level)) {
            return [$level => isset($typeId[$level]) && $typeId[$level] ? $typeId[$level] : []];
        }
        return $typeId;
    }

    static public function getOilType($isMap = false)
    {
        //todo 优化成通过油品模版
        $typeId = array_keys(self::getBaseType());
        $data = OilTypeBase::getList(['idIn' => $typeId, '_export' => 1]);
        $list = $data->toArray();
        if ($isMap) {
            $map = [];
            foreach ($list as $_val) {
                $map[$_val['id']] = $_val['name'];
            }
            return $map;
        } else {
            return $list;
        }
    }

    static public function getOperatorMap($isExt = false)
    {
        $list = OilOperators::getList(["_export" => 1]);
        $map = [];
        if ($isExt) {
            foreach ($list as $_val) {
                $map[$_val['id']] = [
                    'id' => $_val['id'],
                    "name" => $_val['name'],
                    'company_name'=>$_val['company_name'],
                    "company_code" => $_val['company_code'],
                    "createtime"=>$_val['createtime'],
                    "opento_id"=>$_val['opento_id'],
                    "company_type"=>$_val['company_type']
                ];
            }
        } else {
            foreach ($list as $_val) {
                $map[$_val['id']] = $_val['company_name'];
            }
        }
        return $map;
    }

    /**
     * @param array $params
     * down_operator_id
     */
    static public function getInitTime($params = [])
    {
        \helper::argumentCheck(['down_operator_id'], $params);
        $params['_export'] = 1;
        //$params['is_use'] = 1;
        $list = OilOperatorReceiptInit::getList($params);
        $arr = $list->toArray();

        //是否区分时间 1：不区分，2：区分
        $flag = 1;
        if (count($arr) == 0) {
            $info = OilOperators::getById(['id' => $params['down_operator_id']]);
            if (!$info) {
                throw new \Exception('该销售类运营商未期初：【' . $params['down_operator_id'] . "】,运营商不存在", 40001);
            }
            return ['init' => ['init_time' => $info->createtime, 'init_assign_fee' => 0], 'flag' => $flag];
        }
        $init_map = [];
        foreach ($arr as $_val) {
            if ($_val['flag'] == 1) {
                $init_map = [
                    'init_time' => $_val['init_time'],
                    'init_assign_fee' => bcsub($_val['card_balance'], $_val['init_total_fee'], 2),
                ];
                break;
            } else {
                $flag = 2;

                if($_val['is_use'] == 2){
                    $_val['day_trade_money'] = $_val['day_operator_num'] = $_val['day_discount_money'] = 0;
                }
                $last_left_money = $last_left_num = $last_left_discount = 0;
                if($_val['classify'] == 2){
                    $lastInit = OilOperatorReceiptInit::oneByFilter([
                        "flag" => 2,
                        "up_operator_id" => $_val['up_operator_id'],
                        "down_operator_id" => $_val['down_operator_id'],
                        "oil_super_id" => $_val['oil_super_id'],
                        "id_elt" => $_val['id'],
                    ]);
                    $last_left_discount = 0;
                    $trade_info = OilTrades::where('trades_id',$lastInit->trade_id)->first();
                    $last_left_money = $trade_info->trade_money - $lastInit->operator_money;
                    $last_left_num = $trade_info->trade_num - $lastInit->operator_num;
                    if($lastInit->is_use == 1){
                        $last_left_money += $lastInit->day_trade_money;
                        $last_left_num += $lastInit->day_operator_num;
                        $last_left_discount += $lastInit->day_discount_money;
                    }
                    if($_val['is_use'] == 2){
                        $last_left_money = $last_left_num = $last_left_discount = 0;
                    }
                }
                $init_map[$_val['up_operator_id']][$_val['oil_super_id']] =
                    [
                        'init_assign_fee' => bcadd($_val['init_total_fee'], $_val['card_balance'], 2),
                        'init_time' => $_val['init_time'],
                        'trade_id' => $_val['trade_id'],

                        'day_trade_money' => $_val['day_trade_money'] + $last_left_money,
                        //'operator_num' => bcadd($_val['operator_num'] ,$_val['day_operator_num'],2),
                        'day_operator_num' => $_val['day_operator_num'] + $last_left_num,
                        'day_discount_money' => $_val['day_discount_money'] + $last_left_discount,

                        'day_left_trade_money' => $_val['day_left_trade_money'],
                        'day_left_discount_money' => $_val['day_left_discount_money'],
                        'day_left_operator_num' => $_val['day_left_operator_num'],
                        'classify' => $_val['classify'],
                    ];
            }
        }
        return ['init' => $init_map, 'flag' => $flag];
    }

    /**
     * @param array $params
     * 获取可开票时段
     * up_operator_id
     * down_operator_id
     * oil_supplier_id
     */
    static public function getCanReceiptTime($params = [], $isArr = false)
    {
        \helper::argumentCheck(['up_operator_id', 'down_operator_id', 'oil_supplier_id'], $params);
        $oil_super_id = $params['oil_supplier_id'];
        $typeId = self::getBaseType($params['oil_supplier_id']);
        $operator = self::getOperatorMap(true);
        $params['oil_base_idIn'] = isset($typeId[$params['oil_supplier_id']]) && $typeId[$params['oil_supplier_id']] ? $typeId[$params['oil_supplier_id']] : 0;
        unset($params['oil_supplier_id']);
        $params['is_open_invoiceNeq'] = 1;
        //todo 获取期初时间
        $init_time = $origin_time = "";
        $initArr = self::getInitTime(['down_operator_id' => $params['down_operator_id']]);
        if ($initArr['flag'] == 1) {
            $origin_time = $init_time = $initArr['init']['init_time'];
        } else {
            $_tmpInit = $initArr['init'];
            if (isset($_tmpInit[$params['up_operator_id']])) {
                if (isset($_tmpInit[$params['up_operator_id']][$oil_super_id])) {
                    $_tmp = $_tmpInit[$params['up_operator_id']][$oil_super_id];
                    $origin_time = $init_time = $_tmp['init_time'];
                }else{
                    $init_time = isset($operator[$params['up_operator_id']]) && $operator[$params['up_operator_id']] ? $operator[$params['up_operator_id']]['createtime'] : "2023-01-01 00:00:00";
                }
            }else{
                $init_time = isset($operator[$params['up_operator_id']]) && $operator[$params['up_operator_id']] ? $operator[$params['up_operator_id']]['createtime'] : "2023-01-01 00:00:00";
            }
        }
        if (empty($init_time)) {
            throw new \Exception('该销售类运营商未期初：【' . $params['down_operator_id'] . "】", 40011);
        }
        $params['batch_no_ge'] = $init_time;
        $nowTime = date("Y-m-d", strtotime("-1day")) . " 23:59:59";
        //得到已开票时段
        $list = OilOperatorDayTrades::getCanInvoce($params);
        $listArr = $list->toArray();
        if (count($list) == 0) {
            if ($isArr) {
                return [$init_time . "~" . $nowTime];
            }
            return $init_time . "~" . $nowTime;
        }
        $minArr = $maxArr = [];
        foreach ($listArr as &$_one) {
            //内部票更新
            $init_time_batch = OilOperatorReceiptInit::where('flag',2)
                ->where('up_operator_id',$params['up_operator_id'])
                ->where('down_operator_id',$params['down_operator_id'])
                ->where('oil_super_id',$oil_super_id)
                ->where('batch_no',$_one['batch_no'])
                ->first();

            if($init_time_batch && isset($init_time_batch->init_time) && $init_time_batch->init_time < $_one['min_time']){
                $_one['min_time'] = $init_time_batch->init_time;
            }
            $minArr[$_one['min_time']] = $_one['batch_no'];

            //内部票更新
            $left_init_time_batch = OilOperatorReceiptInit::where('flag',2)
                ->where('up_operator_id',$params['up_operator_id'])
                ->where('down_operator_id',$params['down_operator_id'])
                ->where('oil_super_id',$oil_super_id)
                ->where('left_batch_no',$_one['batch_no'])
                ->first();

            if($left_init_time_batch && isset($left_init_time_batch->init_time) && $left_init_time_batch->init_time > $_one['max_time']){
                $_one['max_time'] = $left_init_time_batch->init_time;
            }
            $maxArr[$_one['batch_no']] = $_one['max_time'];
        }
        $use_min_step = array_column($listArr, "min_time");
        $use_max_step = array_column($listArr, "max_time");
        sort($use_min_step);
        rsort($use_max_step);
        $can_use_time_step = [];
        foreach ($use_min_step as $_k => $_min) {
            if (strtotime($_min) < strtotime($init_time)) {
                continue;
            }
            if (strtotime($_min) - strtotime($init_time) >= 0) {
                $batch = $minArr[$_min];
                $max = $maxArr[$batch];

                if (strtotime($_min) - strtotime($init_time) == 0) {
                    $init_time = $max;
                    continue;
                }
                $_time = " 00:00:00";
                if($initArr['flag'] == 2){
                    $_time = substr($init_time,10) == '' ? " 00:00:00" : substr($init_time,10);//时分秒
                }
                if(substr($init_time,10) !=''){
                    $batch_time = strtotime("-1day", strtotime($_min));
                }else{
                    $batch_time = strtotime($_min);
                }
                if (strtotime("+1day", strtotime($init_time)) >  $batch_time|| $origin_time == $init_time) {
                    $_strDay = "+0day";
                } else {
                    $_strDay = "+1day";
                }
                //期初的数据不对
                if(substr($_min,10) == ''){
                    $can_use_time_step[] = date("Y-m-d", strtotime($_strDay, strtotime($init_time))) . $_time . "~" . date("Y-m-d", strtotime("-1day", strtotime($_min))) . " 23:59:59";
                }else{
                    $can_use_time_step[] = date("Y-m-d", strtotime($_strDay, strtotime($init_time))) . $_time . "~" . $_min;
                }

                $init_time = $max;
            }
        }

        //过滤无效可开票时段
        if (count($can_use_time_step) > 0) {
            foreach ($can_use_time_step as $_k => $_time) {
                $_tmpTime = explode("~", $_time);

                $hasNum = OilOperatorDayTrades::count([
                    'up_operator_id' => $params['up_operator_id'],
                    'down_operator_id' => $params['down_operator_id'],
                    'oil_base_idIn' => $params['oil_base_idIn'],
                    'trade_create_Ge' => substr($_tmpTime[0], 0, 10),
                    'trade_create_Lt' => $_tmpTime[1],
                    'is_open_invoice' => 1
                ]);
                if ($hasNum <= 0) {
                    unset($can_use_time_step[$_k]);
                    continue;
                }
            }
        }

        $use_max_step_time = '';
        if(substr($use_max_step[0],10) == ''){
            $use_max_step_time = date("Y-m-d", strtotime("+1day", strtotime($use_max_step[0]))) . " 00:00:00";
        }else{
            $use_max_step_time = $use_max_step[0];
        }
        if ($use_max_step_time > strtotime($nowTime)) {
            $can_use_time_step[] = $use_max_step_time . "~" . "2099-12-31 23:59:59";
        } else {
            $can_use_time_step[] = $use_max_step_time . "~" . $nowTime;
        }

        if ($isArr) {
            return $can_use_time_step;
        }
        return implode(";", $can_use_time_step);
    }

    /**
     * @param array $params
     * @return array
     * up_operator_id
     * up_operator_id
     * oil_super_id
     * trade_create_Ge
     * trade_create_Lt
     * desc:内部公司欠票统计
     */
    static public function operatorReceiptQuota($params = [],$addInit = 1)
    {
        $start = microtime(true);
        Log::error('begin-inner:'.round(microtime(true) - $start,3), [$params], 'Inner_receipt_');
        Log::error("请求参数",[$params],"operator_quota");
        if (!isset($params['trade_create_Lt']) || empty($params['trade_create_Lt'])) {
            $params['trade_create_Lt'] = date("Y-m-d");
        }
        $oil_super_id = "";
        if (isset($params['oil_super_id']) && !empty($params['oil_super_id'])) {
            $oil_super_id = $params['oil_super_id'];
        }
        //$operator = OilOperators::getIdMapName();
        $operator = self::getOperatorMap(true);

        $company_type = isset($operator[$params['up_operator_id']]) && $operator[$params['up_operator_id']] ? $operator[$params['up_operator_id']]['company_type'] : '';
        $operatorToA = isset($operator[$params['up_operator_id']]) && $operator[$params['up_operator_id']] ? $operator[$params['up_operator_id']]['opento_id'] : '';

        $d_company_type = isset($operator[$params['down_operator_id']]) && $operator[$params['down_operator_id']] ? $operator[$params['down_operator_id']]['company_type'] : '';
        if($company_type == 'A' && $d_company_type == 'A'){
            if(!empty($operatorToA)) {
                $_ids = explode(",", $operatorToA);
                if (!in_array($params['down_operator_id'], $_ids)) {
                    return [];
                }
            }else{
                return [];
            }
        }

        $oilType = self::getOilType(true);
        $dataItem = [];
        $typeId = self::getBaseType($oil_super_id);
        if (!empty($oil_super_id) && isset($typeId[$oil_super_id])) {
            $params['oil_base_idIn'] = $typeId[$oil_super_id];
        }
        //todo 获取期初时间
        $init_time = "";
        $init_assign = $init_num = $init_money = $init_discount = 0;//期初时的可用分配金额
        $initArr = self::getInitTime(['down_operator_id' => $params['down_operator_id']]);
        if ($initArr['flag'] == 1) {
            $init_time = $initArr['init']['init_time'];
            $init_assign = $initArr['init']['init_assign_fee'];
        } else {
            //todo 完善大连运营商逻辑
            if (isset($initArr['init'][$params['up_operator_id']])) {
                $_tmp = $initArr['init'][$params['up_operator_id']];
                //没有油品时，取最早期初时间
                $initTimeArr = array_column($initArr['init'][$params['up_operator_id']],"init_time");
                $init_time = min($initTimeArr);
            }else{
                $init_time = isset($operator[$params['up_operator_id']]) && $operator[$params['up_operator_id']] ? $operator[$params['up_operator_id']]['createtime'] : "2023-01-01 00:00:00";
            }
        }
        if (empty($init_time)) {
            throw new \Exception('该销售类运营商未期初：【' . $params['down_operator_id'] . "】", 40011);
        }
        //期初时间或上次开票的截止时间
        if (!isset($params['trade_create_Ge']) || empty($params['trade_create_Ge'])) {
            $params['trade_create_Ge'] = $init_time;
        }

        //如果起始时间小于期初时间，使用期初时间
        if( strtotime($params['trade_create_Ge']) <= strtotime($init_time) ){
            $params['trade_create_Ge'] = $init_time;
        }

        $profit_val = 0;
        $isOk = false;
        $time = ReceiptApplyDefine::receiptProfitRate($params['up_operator_id']);
        //G7WALLET-6232
        if( isset($params['_lock_export']) && $params['_lock_export'] == 1 ) {
            $isOk = true;
            $last = array_pop($time);
            $profit_val = $last['rate'];
        } else {
            foreach ($time as $_val) {
                if (strtotime($params['trade_create_Ge']) >= strtotime($_val['start']) && strtotime($params['trade_create_Ge']) <= strtotime($_val['end']) &&
                    strtotime($params['trade_create_Lt']) >= strtotime($_val['start']) && strtotime($params['trade_create_Lt']) <= strtotime($_val['end'])
                ) {
                    $isOk = true;
                    $profit_val = $_val['rate'];
                }
            }
        }

        if (!$isOk) {
            throw new \Exception('不允许跨时间段', 40011);
        }
        if (empty($profit_val)) {
            throw new \Exception('无法获取分润比例', 40012);
        }

        Log::error('second-inner:'.round(microtime(true) - $start,3), [$params], 'Inner_receipt_');

        $data = OilOperatorDayTrades::receiptQutoa($params);

        Log::error('receiptQutoa:'.round(microtime(true) - $start,3), [$params], 'Inner_receipt_');

        Log::error("查询day_trades",[count($data)],"operator_quota");

        $orgDayReceipt = OilOrgOperatorDayTrades::rootReceiptQuota($params);

        Log::error('OilOrgOperatorDayTrades:'.round(microtime(true) - $start,3), [$params], 'Inner_receipt_');

        Log::error("查询lock_trades",[count($orgDayReceipt)],"operator_quota");

        if(isset($params['_lock_export']) && $params['_lock_export'] == 1){
            $assignMap = [
                "use_assign" => 0,
                "assign" => 0
            ];
        } else {
            //累计分配和可用分配
            $assignMap = self::getSumData([
                'ge' => $init_time,
                "down_operator_id" => $params['down_operator_id'],
            ]);
        }
        Log::error('assign:'.round(microtime(true) - $start,3), [$params], 'Inner_receipt_');
        Log::error("查询分配",[count($assignMap)],"operator_quota");

        foreach ($data as $_key => $_val) {
            $receiptMap = [];
            $_val->up_operator_name = isset($operator[$_val->up_operator_id]) && $operator[$_val->up_operator_id] ? $operator[$_val->up_operator_id]['company_name'] : '';
            $_val->down_operator_name = isset($operator[$_val->down_operator_id]) && $operator[$_val->down_operator_id] ? $operator[$_val->down_operator_id]['company_name'] : '';
            $_val->oil_base_name = isset($oilType[$_val->oil_base_id]) && $oilType[$_val->oil_base_id] ? $oilType[$_val->oil_base_id] : '';

            $company_code = isset($operator[$_val->up_operator_id]) && $operator[$_val->up_operator_id] ? $operator[$_val->up_operator_id]['company_code'] : '';

            //期初数据
            if ($initArr['flag'] == 2) {
                $_initArr = isset($_tmp[$_val->oil_base_id]) && $_tmp[$_val->oil_base_id] ? $_tmp[$_val->oil_base_id] : [
                    'init_assign_fee' => 0,
                    'init_time' => 0,
                    'trade_id' => 0,
                    'trade_money' => 0,
                    'operator_num' => 0,
                    'discount_money' => 0,
                ];
                if ($addInit == 1) {
                    $init_assign = $_initArr['init_assign_fee'];
                    $init_num = $_initArr['day_operator_num'];
                    $init_money = $_initArr['day_trade_money'];
                    $init_discount = $_initArr['day_discount_money'];
                }
            }

            $_val->trade_num = number_format(($_val->trade_num + $init_num), 2, ".", "");
            $_val->trade_money = bcadd($_val->trade_money, $init_money,2);

            //下游返利
            $_val->down_fanli_fee = number_format($_val->down_fanli_fee, 2, ".", "");
            $_val->profit = number_format($_val->profit, 2, ".", "");
            //下游让利
            //$profit_val = ReceiptApplyInternal::getProfit($_val->up_operator_id,$params['trade_create_Lt']);
            $_val->down_profit = number_format(($_val->profit * $profit_val), 2, ".", "");

            $receiptMap = self::getReceiptData([
                'ge' => $init_time,
                'oil_base_id' => $_val->oil_base_id,
                'up_operator_id' => $_val->up_operator_id,
                "down_operator_id" => $_val->down_operator_id,
                "company_code" => $company_code,
            ]);
            $_val->use_assign = number_format(($assignMap['use_assign'] + $init_assign), 2, ".", "");//可用分配
            $_val->assign_all = number_format(($assignMap['assign'] + $init_assign), 2, ".", "");//累计分配
            $_val->apply_time = $receiptMap['last_apply_time'];//上次开票截止时间
            $_val->receipt_fee = number_format($receiptMap['receipt_ok'], 2, ".", "");//已开
            $_val->receipt_froze = number_format($receiptMap['receipt_froze'], 2, ".", "");//冻结
            $_val->discount_money = number_format($_val->discount_money, 2, ".", "");

            $_val->step_time = "";
            if( $_val->trade_num > 0 && $_val->oil_base_id > 0) {
                $step = self::getCanReceiptTime([
                    'up_operator_id' => $_val->up_operator_id,
                    'down_operator_id' => $_val->down_operator_id,
                    'oil_supplier_id' => $_val->oil_base_id
                ], true);
                $_val->step_time = implode(";<br>", $step);
            }

            //防止防止小数保留位数问题
            //周期折扣
            $_val->discount_money = number_format( ($_val->discount_money+$init_discount), 2, ".", "");
            //可用消费 = 开票金额 =  周期消费（下游结算总额）- 周期折扣
            $_val->unsign_operator_money = number_format( ( $_val->trade_money - $_val->discount_money), 2, ".", "");
            $_val->unsign_operator_num = $_val->trade_num;

            $lock_money = $lock_num = $unlock_money = $unlock_num = "0.00";
            $use_month = $lock_month = [];
            if(count($orgDayReceipt) > 0){
                $_dayKey = $_val->up_operator_id.'#'.$_val->down_operator_id.'#'.$_val->oil_base_id;
                $dayTotal = isset($orgDayReceipt['total']) && isset($orgDayReceipt['total'][$_dayKey]) ? $orgDayReceipt['total'][$_dayKey] : [];
                $dayLock = isset($orgDayReceipt['lock']) && isset($orgDayReceipt['lock'][$_dayKey]) ? $orgDayReceipt['lock'][$_dayKey] : [];
                $dayUnlock = isset($orgDayReceipt['unlock']) && isset($orgDayReceipt['unlock'][$_dayKey]) ? $orgDayReceipt['unlock'][$_dayKey] : [];

                if(count($dayTotal) > 0){
                    $root_money = number_format($dayTotal['money'],2,".","");
                    $root_discount = number_format($dayTotal['discount'],2,".","");
                    $root_num = number_format($dayTotal['trade_num'],2,".","");

                    $_val->trade_money = number_format(($_val->trade_money+$root_money),2,".","");
                    $_val->discount_money = number_format(($_val->discount_money+$root_discount),2,".","");
                    $_val->trade_num = number_format(($_val->trade_num+$root_num),2,".","");
                }

                if(count($dayLock) > 0){
                    $lock_money = number_format($dayLock['use_money'],2,".","");
                    $lock_num = number_format($dayLock['trade_num'],2,".","");
                    $lock_month = array_unique($dayLock['month']);
                }
                if(count($dayUnlock) > 0){
                    $unlock_money = number_format($dayUnlock['use_money'],2,".","");
                    $unlock_num = number_format($dayUnlock['trade_num'],2,".","");
                    $use_month = array_unique($dayUnlock['month']);
                }
            }

            //防止防止小数保留位数问题
            //周期折扣
            $_val->discount_money = number_format( ($_val->discount_money+$init_discount), 2, ".", "");
            //可用消费 = 开票金额 =  周期消费（下游结算总额）- 周期折扣
            $_val->use_trade_money = number_format( ( $_val->trade_money - $_val->discount_money), 2, ".", "");

            $_val->lock_operator_money = $lock_money;
            $_val->lock_operator_num = $lock_num;

            $_val->unlock_operator_money = $unlock_money;
            $_val->unlock_operator_num = $unlock_num;

            $last_fee = number_format(($_val->use_trade_money - $_val->lock_operator_money),2,".","");
            //可开票金额
            if(isset($params['_lock_export']) && $params['_lock_export'] == 1) {
                $_val->receipt_remain = $last_fee;
            }else{
                $_val->receipt_remain = min($last_fee, $_val->use_assign);
            }
            if ($last_fee == 0) {
                $_val->receipt_num = "0.00";
            } else {
                if ($_val->receipt_remain != $last_fee) {
                    $_val->receipt_num = number_format(($_val->receipt_remain * (($_val->trade_num - $_val->lock_operator_num) / $last_fee)), 2, ".", "");
                } else {
                    $_val->receipt_num = number_format(($_val->trade_num - $_val->lock_operator_num),2,".","");
                }
            }

            if(count($use_month) > 0){
                $_val->receipt_month = implode(";<br/>",$use_month);
            }else{
                $_val->receipt_month = '';
            }
            if(count($lock_month) > 0){
                $_val->lock_receipt_month = implode(";<br/>",$lock_month);
            }else{
                $_val->lock_receipt_month = '';
            }
        }

        Log::error('result:'.round(microtime(true) - $start,3), [$params], 'Inner_receipt_');
        Log::error("查询结果",[count($data)],"operator_quota");

        return $data;
    }

    //获取累计数据
    static public function getSumData($params = [])
    {
        $nowDay = date("Y-m-d") . " 00:00:00";
        $start = microtime(true);
        $assign = OilAccountAssign::sumAssignData([
            "complete_time_Ge" => $params['ge'], //期初时间
            'complete_time_Lt' => $nowDay,//当前时间0点
            "org_operators_id" => $params['down_operator_id'], //是销售
            'status' => 1,
        ]);
        Log::error('sumAssignData:'.round(microtime(true) - $start,3), ['param'=>$params,"assign"=>$assign], 'Inner_receipt_');

        //共享卡消费 G7WALLET-6097
        $init_trade = 0;
        $init_time = $params['ge'];
        if( in_array($params['down_operator_id'],[2,18]) ){
            $init_trade_info = OilOperatorReceiptInit::oneByFilter(['down_operator_id'=>$params['down_operator_id'],"init_time"=>$init_time]);
            if( $init_trade_info && isset($init_trade_info->share_trade) ) {
                $init_trade += $init_trade_info->share_trade;
                $init_time = self::InnerInitTime;
            }
        }
        $share_trade = OilTrades::getSumMoney([
            "oil_com" => 21,
            'down_operator_id' => $params['down_operator_id'], //销售公司
            'trade_createtime_ge' => $init_time,//期初时间
            'trade_createtime_lt' => $nowDay, //当前时间0点,
        ]);
        $share_trade += $init_trade;
        Log::error('getSumShareTrade:'.round(microtime(true) - $start,3), ['param'=>$params,"share"=>$share_trade,"init_time"=>$init_time,"init_trade"=>$init_trade], 'Inner_receipt_');

        $receiptSum = self::getReceipt([
            'receipt_statusIn' => [ReceiptApplyStatus::AUDITED, ReceiptApplyStatus::PRE_AUDIT, ReceiptApplyStatus::HANDLING, ReceiptApplyStatus::OPENING, ReceiptApplyStatus::SUCCESS, ReceiptApplyStatus::MAILED],
            'org_id' => $params['down_operator_id'], //todo 使用operator_id
            'is_internal' => 1,
            'apply_time_ge' => $params['ge'],
        ], false);

        $assign += $share_trade;
        $use_assign = $assign - $receiptSum;
        Log::error('assign-result:'.round(microtime(true) - $start,3), [$params], 'Inner_receipt_');
        return ['assign' => $assign, "use_assign" => $use_assign];
    }

    //获取每种油品累计数据
    static public function getReceiptData($params = [])
    {
        $nowDay = date("Y-m-d") . " 00:00:00";
        //todo 确定是销售运营商
        //return ['assign'=>0,'receipt_ok'=>0,"receipt_froze"=>0,"use_assign"=>11110];

        //发票申请燃油与甲醇汽油 有冲突，固转换一次油品
        $oil_type_id = $params['oil_base_id'] == 12 ? 15 : $params['oil_base_id'];
        $receiptOk = self::getReceipt([
            'oil_type_eq' => $oil_type_id,
            'receipt_statusIn' => [ReceiptApplyStatus::SUCCESS, ReceiptApplyStatus::MAILED],
            'org_id' => $params['down_operator_id'], //todo 使用operator_id
            'is_internal' => 1,
            'apply_time_ge' => $params['ge'],
        ]);
        $receiptFroze = self::getReceipt([
            'oil_type_eq' => $oil_type_id,
            'receipt_statusIn' => [ReceiptApplyStatus::AUDITED, ReceiptApplyStatus::PRE_AUDIT, ReceiptApplyStatus::HANDLING, ReceiptApplyStatus::OPENING],
            'org_id' => $params['down_operator_id'], //todo 使用operator_id
            'is_internal' => 1,
            'apply_time_ge' => $params['ge'],
        ]);
        $last_apply_time = self::getReceipt([
            'oil_type_eq' => $oil_type_id,
            'receipt_statusIn' => [ReceiptApplyStatus::AUDITED, ReceiptApplyStatus::PRE_AUDIT, ReceiptApplyStatus::HANDLING, ReceiptApplyStatus::OPENING, ReceiptApplyStatus::SUCCESS, ReceiptApplyStatus::MAILED],
            'org_id' => $params['down_operator_id'], //todo 使用operator_id
            'is_internal' => 1,
            'apply_time_ge' => $params['ge'],
            'seller_company_code' => $params['company_code'],
        ], true);
        return ['receipt_ok' => $receiptOk, "receipt_froze" => $receiptFroze, "last_apply_time" => $last_apply_time];
    }

    static public function getReceipt($params = [], $isTime = false)
    {
        /*$receiptStatus = [
            ReceiptApplyStatus::AUDITED,
            ReceiptApplyStatus::PRE_AUDIT,
            ReceiptApplyStatus::HANDLING,
            ReceiptApplyStatus::OPENING,
            ReceiptApplyStatus::SUCCESS,
            ReceiptApplyStatus::MAILED,
        ];*/
        return OilReceiptApply::sumReceiptAmountForOperator($params, $isTime);
    }

    //发票撤销,触发重新生成毛利

    /**
     * @param array $params
     * @throws \GuzzleHttp\Exception\GuzzleException
     * start_time
     * end_time+1天
     * base_id
     */
    static public function updateOperatorDayTrades($params = [], $path = "/oil_card_vice_trades/sendTask")
    {
        //请求foss-task
        $url = \Framework\Config::get('fossTask.apiUrl');
        $url = $url . $path;

        //请求ldap
        $client = new \GuzzleHttp\Client();
        try {
            $req = $client->request('POST', $url, ['form_params' => $params]);
            $resultData = $req->getBody()->getContents();
            $result = json_decode($resultData, true);
            if ($result['code'] == 0) {
                $data = true;
            } else {
                throw new \Exception($result['message'], $result['code']);
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage(), $e->getCode());
        }

        return $result;
    }

    /**
     * @param array $params
     * 天津汇通期初方案
     */
    static public function initOperatorData($params = [])
    {
        $batch = [];
        //天津汇通最早分配时间
        $begin_time = "2022-10-10 11:46:28";
        \helper::argumentCheck(['init_time'], $params);
        $assign = OilAccountAssign::sumAssignData([
            "complete_time_Ge" => $begin_time,
            'complete_time_Lt' => $params['init_time'],
            "org_operators_id" => $params['down_operator_id'],
            'status' => 1,
        ]);
        $trades = OilTrades::getSumMoney([
            'trade_createtime_ge' => $begin_time,
            'trade_createtime_lt' => $params['init_time'],
            'down_operator_id' => $params['down_operator_id'],
        ]);

        $insertData['flag'] = 1;
        $insertData['down_operator_id'] = $params['down_operator_id'];
        $insertData['init_total_fee'] = $trades; //累计消费
        $insertData['card_balance'] = $assign; //累计分配
        $insertData['init_time'] = $params['init_time'];
        $insertData['createtime'] = \helper::nowTime();
        $insertData['updatetime'] = \helper::nowTime();
        $batch[] = $insertData;

        if (count($batch) > 0) {
            OilOperatorReceiptInit::batchAdd($batch);
        }
        return true;
    }


    /**
     * @param array $params
     * @throws \GuzzleHttp\Exception\GuzzleException
     * 汇通大连期初方案
     */
    static public function initOperatorDataDaLian($params = [])
    {
        //oil_trades 最早时间[2022-09-01 15:49:39]，如果早于该时间，需要把oil_card_vice_trades的数据写入oil_trades表中
        $end_time = "2023-02-01 00:00:00";
        $use_trade = [];

        $params['flag'] = 2;
        $params['init_stock_gt'] = 1;
        $params['_export'] = 1;
        $params['down_operator_id'] = 2;//写死 大连
        $params['classify'] = 2; //期初类别 1：期初，2：调差
        $params['trade_idNull'] = 1;
        $initData = OilOperatorReceiptInit::getList($params);
        if (count($initData) == 0) {
            throw new \RuntimeException("数据为空,库存数需先写入期初表", 2);
        }
        $initArr = $initData->toArray();
        $limit = 20000000;

        foreach ($initArr as $_val) {
            $updateData = [];
            $up_id = $_val['up_operator_id'];
            $down_id = $_val['down_operator_id'];
            $base_id = $_val['oil_super_id'];
            $typeId = self::getBaseType($base_id);
            $baseIdIn = isset($typeId[$base_id]) && $typeId[$base_id] ? $typeId[$base_id] : [];
            if (count($baseIdIn) == 0) {
                throw new \Exception("油品类型不存在", 50003);
            }
            if (empty($up_id) || empty($down_id)) {
                throw new \Exception("供给/销售类运营商不存在", 50004);
            }

            $isFirst = 1;
            if ($_val['classify'] == 2) {
                $isFirst = 2;
                //获取上一次数据
                $lastInit = OilOperatorReceiptInit::oneByFilter([
                    "flag" => 2,
                    "up_operator_id" => $up_id,
                    "down_operator_id" => $down_id,
                    "oil_super_id" => $base_id,
                    "id_elt" => $_val['id'],
                ]);
                $end_time = $lastInit->init_time;
                $use_trade = [
                    "end_trade" => $lastInit->trade_id,
                    "operator_money" => $lastInit->operator_money,
                    "discount_money" => $lastInit->discount_money,
                    "operator_num" => $lastInit->operator_num,
                ];
                $trade_num = OilTrades::where('trades_id',$lastInit->trade_id)->first();

                $_val['init_stock'] = $_val['init_stock'] - ($trade_num->trade_num-$lastInit->operator_num);

                print_r($_val['init_stock']);
                print_r($trade_num->toArray());
                print_r($lastInit->operator_num);

            }

            $init_stock = $_val['init_stock'];
            $_trade = -1;
            $page = 1;
            $begin_time = date("Y-m-d", strtotime("-400day", strtotime($end_time))) . " 00:00:00";
            while (true) {
                $start = ($page - 1) * $limit;

                Log::error("getInitTrades==while",[
                    "up_operator_id" => $up_id,
                    "down_operator_id" => $down_id,
                    "oil_super_idIn" => $baseIdIn,
                    "stock" => $init_stock,
                    "begin_time" => $begin_time,
                    'start' => $start,
                    'limit' => $limit,
                    'end_time'=>$end_time,
                    'use_trade'=>$use_trade,
                    'isFirst'=>$isFirst
                ], "dalian_init_");

                $_trade = self::getFirstTrades([
                    "up_operator_id" => $up_id,
                    "down_operator_id" => $down_id,
                    "oil_super_idIn" => $baseIdIn,
                    "stock" => $init_stock,
                    "begin_time" => $begin_time,
                    'start' => $start,
                    'limit' => $limit
                ], $end_time, $use_trade, $isFirst);

                print_r($_trade);

                //根据条件分页后没有查询到消费
                //todo 1.把开始时间向前调整 2.oil_trades的数据不够
                if ($_trade == -10) {
                    $oil_trade_begin_time = self::getInitTrades([
                        "up_operator_id" => $up_id,
                        "down_operator_id" => $down_id,
                        "oil_super_idIn" => $baseIdIn,
                        "end_time" => $begin_time
                    ]);//获取本次交易的最早的时间
                    if(!$oil_trade_begin_time){
                        Log::error("getInitTrades==消费记录不存在", [$params], "dalian_init_");
                        break;
                    }
                    Log::error("getInitTrades==消费记录不存在", ['oil_trade_begin_time'=>$oil_trade_begin_time,'begin_time'=>$begin_time], "dalian_init_");
                    if($oil_trade_begin_time < $begin_time){
                        $end_time = $begin_time;
                        $begin_time = $oil_trade_begin_time;
                        $_trade = self::getFirstTrades([
                            "up_operator_id" => $up_id,
                            "down_operator_id" => $down_id,
                            "oil_super_idIn" => $baseIdIn,
                            "stock" => $init_stock,
                            "begin_time" => $begin_time,
                            'start' => $start,
                            'limit' => $limit
                        ], $end_time, $use_trade, $isFirst);
                    }else{
                        $start_time = date("Y-m-d", strtotime("-60day", strtotime($begin_time))) . " 00:00:00";
                        Log::error("getInitTrades==oilTrades记录不存在", ['start_time'=>$start_time,'begin_time'=>$begin_time], "dalian_init_");
                        //推送俩个月
                        /*$start_time = date("Y-m-d", strtotime("-60day", strtotime($begin_time))) . " 00:00:00";
                        //还得扩展数据
                        $job_params = [
                            'createtime_stime'=>$start_time,
                            'createtime_etime'=>$begin_time,
                            "up_operator_id" => $up_id,
                            "down_operator_id" => $down_id,
                            "oil_super_idIn" => $baseIdIn,
                        ];
                        //拉取数据
                        (new \Jobs\UpstreamOilTradeWriteJob($job_params))
                            ->setTaskName('期初OilCardViceTrades同步oil_trades数据')
                            ->onQueue('export')
                            ->dispatch();*/
                        break;
                    }
                }
                if (is_array($_trade) && isset($_trade['trades_id'])) {
                    break;
                } else {
                    $init_stock -= $_trade['sum_stock'];
                }
                $page++;
            }

            $profit_val = ReceiptApplyInternal::getProfit($up_id);

            if (is_array($_trade)) {

                $aggregateWhere = [];
                $nextTime = date("Y-m-d", strtotime("+1day", strtotime($_trade['init_time'])));
                $aggregateWhere['up_operator_id'] = $up_id;
                $aggregateWhere['down_operator_id'] = $down_id;
                $aggregateWhere['start_time'] = $nextTime;
                $aggregateWhere['end_time'] = $end_time;
                $aggregateWhere['base_id'] = $base_id;

                $aggregateWhere['init_id'] = $_val['id'];
                $aggregateWhere['init_stock'] = $_val['init_stock'];
                $aggregateWhere['init_time_ge'] = date("Y-m-d", strtotime($_trade['init_time'])) . " 00:00:00";

                $updateData['id'] = $_val['id'];
                $updateData['init_time'] = $_trade['init_time'];
                $updateData['trade_id'] = $_trade['trades_id'];
                $updateData['trade_money'] = $_trade['trade_money'];
                $updateData['discount_money'] = $_trade['discount_money'];
                $updateData['operator_num'] = $_trade['trade_num'];
                if($_val['classify'] == 2){
                    //大连的首次卡余额，写到日志【cardBalance_】
                    $updateData['card_balance'] = 0;
                }
                $updateData['remark'] = \helper::nowTime() . "，单笔交易拆分";
                $updateData['updatetime'] = \helper::nowTime();

                //计算剩余数据
                $day_trades = OilOperatorDayTrades::receiptDayQutoa(
                    [
                        'trade_create_Ge' => $_trade['init_time'],
                        'trade_create_Lt' => $nextTime,
                        'up_operator_id' => $up_id,
                        'down_operator_id' => $down_id,
                        'oil_base_idIn' => $baseIdIn,
                    ]
                );
                print_r($baseIdIn);
                print_r($day_trades);
                if(isset($day_trades[0]) && $day_trades[0]){
                    $updateData['day_trade_money'] = $day_trades[0]->trade_money;
                    $updateData['day_operator_num'] = $day_trades[0]->trade_num;
                    // $updateData['day_discount_money'] = $day_trades[0]->day_discount_money;
                    ////实际盈利 = 下游成本总额 - 上游成本总额
                    $profit = ($day_trades[0]->trade_money - $day_trades[0]->down_fanli) - ($day_trades[0]->mac_amount - $day_trades[0]->up_fanli);
                    $tmpFee = $profit * $profit_val;
                    $day_discount_money = round(bcadd($day_trades[0]->down_fanli, $tmpFee, 6),2);
                    $updateData['day_discount_money'] = $day_discount_money;
                }else{
                    $updateData['day_trade_money'] = 0;
                    $updateData['day_operator_num'] = 0;
                    $updateData['day_discount_money'] = 0;
                }

                //当天开票金额
                $updateData['day_trade_money'] = $updateData['day_trade_money']+$_trade['trade_money'];
                $updateData['day_operator_num'] = $updateData['day_operator_num']+$updateData['operator_num'];
                $updateData['day_discount_money'] = $updateData['day_discount_money']+$updateData['discount_money'];

                $aggregateWhere['use_discount'] = $updateData['day_discount_money'];
                $aggregateWhere['use_money'] = $updateData['day_trade_money'];
                print_r($aggregateWhere);
                Log::error("下发生成日汇总任务", [$aggregateWhere], "dalian_");
                //todo 下发生成日汇总数据任务
                $aggregateWhere['profit_val'] = $profit_val;
                print_r($aggregateWhere);exit;
                self::updateOperatorDayTrades($aggregateWhere);

                OilOperatorReceiptInit::edit($updateData);

            }

            exit;
        }
    }

    /**
     * @param array $params
     * @param string $end_time
     * 根据库存向前推到首笔交易
     */
    static public function getFirstTrades($params = [], $end_time = "", $use_trade = [], $isFirst = 1)
    {
        Capsule::connection()->enableQueryLog();
        $sql = "SELECT
    oil_trades.trades_id,
	oil_trades.trade_money,
	oil_trades.mac_amount,
	oil_trades.trade_createtime,
	oil_trades.trade_num,
	oil_trades.trade_price,
	rebate.final_straight_down_rebate,
	rebate.final_after_rebate,
	rebate.mark_rebate,
	rebate.down_cal_rebate
FROM
	`oil_trades` 
	LEFT JOIN `oil_card_vice_trade_rebate` AS `rebate` ON `rebate`.`trade_id` = `oil_trades`.`trades_id`
	LEFT JOIN `oil_type_no` ON `oil_trades`.`oil_name` = `oil_type_no`.`oil_no` 
WHERE
	1 ";
        if ($isFirst == 1) {
            $sql .= " and `oil_trades`.`trade_createtime` < '" . $end_time . "'";
        } else {
            $sql .= " and `oil_trades`.`trade_createtime` < '" . $end_time . "' and `oil_trades`.`trades_id` < " . $use_trade['end_trade'] . "";
        }

        $exceipt = (new ExportReportDataJob())->exceiptOrg;
        $sql .= " and `oil_trades`.`trade_createtime` >= '" . $params['begin_time'] . "'";
        //$sql .= " and `rebate`.`trade_create_time` >= '" . $params['begin_time'] . "' and `rebate`.`trade_create_time` < '" . $end_time . "'";
        $sql .= " and `oil_trades`.top_orgcode not in ('" . implode("','", $exceipt) . "')";

        $sql .= " 
	AND `up_operator_id` = " . $params['up_operator_id'] . " 
	AND `down_operator_id` = " . $params['down_operator_id'] . " 
	AND `oil_type_no`.`oil_base_id` IN ( " . implode(",", $params['oil_super_idIn']) . " ) 
	and	oil_trades.trade_type IN ( ".TradesType::getCashAndSelfOilTradeTypeArr(true,true)." ) 
ORDER BY
	oil_trades.trade_createtime DESC limit " . $params['start'] . "," . $params['limit'];

        $data = Capsule::connection()->select($sql);

        $sql = Capsule::connection()->getQueryLog();
        print_r($sql);

        if (count($data) == 0) {
            Log::error("该油品对应的消费记录不存在", [$params], "dalian_");
            print_r($params);
            return -10;
            /*throw new \Exception('该油品对应的消费记录不存在', 50003);*/
        }
        $sum_stock = 0;
        $firstTrade = [];
        foreach ($data as $_one) {
            $sum_stock = bcadd($sum_stock, $_one->trade_num, 2);
            if (bccomp($sum_stock, $params['stock'], 2) >= 0) {
                $firstTrade = $_one;
                break;
            }
        }
        if (count($firstTrade) == 0) {
            Log::error("该油品对应的消费记录不存在", [$params], "dalian_");
            return ['sum_stock' => $sum_stock];
            //print_r($params);
            //throw new \Exception('消费升数小于库存数', 50003);
        }
        //$params['up_operator_id']
        $profit_val = ReceiptApplyInternal::getProfit($params['up_operator_id']);
        $profit = ($firstTrade->trade_money - $firstTrade->down_cal_rebate) - ($firstTrade->mac_amount - ($firstTrade->final_straight_down_rebate + $firstTrade->final_after_rebate + $firstTrade->mark_rebate));
        $tmpFee = $profit * $profit_val;
        $discount_money = round(bcadd($_one->down_cal_rebate, $tmpFee, 6),2);
        if (bccomp($sum_stock, $params['stock'], 2) == 0) {
            return [
                'init_time' => $firstTrade->trade_createtime,
                'trades_id' => $firstTrade->trades_id,
                "trade_money" => $firstTrade->trade_money,
                "trade_num" => $firstTrade->trade_num,
                'discount_money' => $discount_money,
            ];
        } else {
            $total_ok = bcsub($sum_stock, $firstTrade->trade_num, 2);
            $use_num = bcsub($params['stock'], $total_ok, 2);
            $tax = $use_num / $firstTrade->trade_num;
            $use_money = bcmul($tax, $firstTrade->trade_money, 2);
            //todo 确定折扣金额 得到开票金额
            $discount = bcmul($tax, $discount_money, 2);
            return [
                'init_time' => $firstTrade->trade_createtime,
                'trades_id' => $firstTrade->trades_id,
                "trade_money" => $use_money,
                "trade_num" => $use_num,
                'discount_money' => $discount,
            ];
        }
    }


    /**
     * @param array $params
     * @param string $end_time
     * 根据库存向前推到首笔交易
     */
    static public function getInitTrades($params = [])
    {
        Capsule::connection()->enableQueryLog();
        $sql = "SELECT
    oil_trades.trades_id,
	oil_trades.trade_createtime
FROM
	`oil_trades` 
	LEFT JOIN `oil_type_no` ON `oil_trades`.`oil_name` = `oil_type_no`.`oil_no` 
WHERE
	1 ";
        $sql .= " and `oil_trades`.`trade_createtime` < '" .  $params['end_time'] . "'";

        $sql .= " 
	AND `up_operator_id` = " . $params['up_operator_id'] . " 
	AND `down_operator_id` = " . $params['down_operator_id'] . " 
	AND `oil_type_no`.`oil_base_id` IN ( " . implode(",", $params['oil_super_idIn']) . " ) 
ORDER BY
	oil_trades.trade_createtime asc limit 1";

        $data = Capsule::connection()->select($sql);

        $sql = Capsule::connection()->getQueryLog();
        print_r($sql);

        if (count($data) == 0) {
            Log::error("getInitTrades==消费记录不存在", [$params], "dalian_");
            print_r($params);
            return false;
        }else{
            return $data[0]->trade_createtime;
        }
    }

    static public function lockOperatorReceiptQuota($params,$addInit = 1)
    {
        Log::error("请求参数",[$params],"operator_quota");
        if (!isset($params['trade_create_Lt']) || empty($params['trade_create_Lt'])) {
            $params['trade_create_Lt'] = date("Y-m-d");
        }
        $operator = self::getOperatorMap(true);
        $oilType = self::getOilType(true);

        $init_num = $init_money = $init_discount = 0;//期初时的可用分配金额
        $initArr = self::getInitTime(['down_operator_id' => $params['down_operator_id']]);
        if ($initArr['flag'] == 1) {
            $init_time = $initArr['init']['init_time'];
        } else {
            if (isset($initArr['init'][$params['up_operator_id']])) {
                $_tmp = $initArr['init'][$params['up_operator_id']];
                //没有油品时，取最早期初时间
                $initTimeArr = array_column($initArr['init'][$params['up_operator_id']],"init_time");
                $init_time = min($initTimeArr);
            }else{
                $init_time = isset($operator[$params['up_operator_id']]) && $operator[$params['up_operator_id']] ? $operator[$params['up_operator_id']]['createtime'] : "2023-01-01 00:00:00";
            }
        }
        if (empty($init_time)) {
            throw new \Exception('该销售类运营商未期初：【' . $params['down_operator_id'] . "】", 40011);
        }
        if (!isset($params['trade_create_Ge']) || empty($params['trade_create_Ge'])) {
            $params['trade_create_Ge'] = $init_time;
        }

        if( strtotime($params['trade_create_Ge']) <= strtotime($init_time) ){
            $params['trade_create_Ge'] = $init_time;
        }

        $data = OilOperatorDayTrades::receiptQutoa($params);

        Log::error("查询day_trades",[count($data)],"operator_quota");

        $orgDayReceipt = OilOrgOperatorDayTrades::rootReceiptQuota($params);

        Log::error("查询lock_trades",[count($orgDayReceipt)],"operator_quota");

        foreach ($data as $_val) {
            $_val->up_operator_name = isset($operator[$_val->up_operator_id]) && $operator[$_val->up_operator_id] ? $operator[$_val->up_operator_id]['company_name'] : '';
            $_val->down_operator_name = isset($operator[$_val->down_operator_id]) && $operator[$_val->down_operator_id] ? $operator[$_val->down_operator_id]['company_name'] : '';
            $_val->oil_base_name = isset($oilType[$_val->oil_base_id]) && $oilType[$_val->oil_base_id] ? $oilType[$_val->oil_base_id] : '';

            //期初数据
            if ($initArr['flag'] == 2) {
                $_initArr = isset($_tmp[$_val->oil_base_id]) && $_tmp[$_val->oil_base_id] ? $_tmp[$_val->oil_base_id] : [
                    'init_assign_fee' => 0,
                    'init_time' => 0,
                    'trade_id' => 0,
                    'trade_money' => 0,
                    'operator_num' => 0,
                    'discount_money' => 0,
                ];
                if ($addInit == 1) {
                    $init_num = $_initArr['day_operator_num'];
                    $init_money = $_initArr['day_trade_money'];
                    $init_discount = $_initArr['day_discount_money'];
                }
            }

            $_val->trade_num = number_format(($_val->trade_num + $init_num), 2, ".", "");
            $_val->trade_money = bcadd($_val->trade_money, $init_money,2);

            //下游返利
            $_val->down_fanli_fee = number_format($_val->down_fanli_fee, 2, ".", "");
            $_val->profit = number_format($_val->profit, 2, ".", "");
            //下游让利
            $profit_val = ReceiptApplyInternal::getProfit($_val->up_operator_id,$params['trade_create_Lt']);
            $_val->down_profit = number_format(($_val->profit * $profit_val), 2, ".", "");

            $_val->discount_money = number_format($_val->discount_money, 2, ".", "");

            $_val->step_time = "";
            if( $_val->trade_num > 0 && $_val->oil_base_id > 0) {
                $step = self::getCanReceiptTime([
                    'up_operator_id' => $_val->up_operator_id,
                    'down_operator_id' => $_val->down_operator_id,
                    'oil_supplier_id' => $_val->oil_base_id
                ], true);
                $_val->step_time = implode(";", $step);
            }

            //周期折扣
            $_val->discount_money = number_format( ($_val->discount_money+$init_discount), 2, ".", "");
            //可用消费 = 开票金额 =  周期消费（下游结算总额）- 周期折扣
            $_val->use_trade_money = number_format( ( $_val->trade_money - $_val->discount_money), 2, ".", "");

            $lock_money = $lock_num = "0.00";
            $use_month = $lock_month = [];
            if(count($orgDayReceipt) > 0){
                $_dayKey = $_val->up_operator_id.'#'.$_val->down_operator_id.'#'.$_val->oil_base_id;
                if(isset($orgDayReceipt[$_dayKey])){
                    $lock_money = number_format($orgDayReceipt[$_dayKey]['money'],2,".","");
                    $lock_num = number_format($orgDayReceipt[$_dayKey]['trade_num'],2,".","");
                    $lock_month = array_unique($orgDayReceipt[$_dayKey]['lock_month']);
                    $use_month = array_unique($orgDayReceipt[$_dayKey]['receipt_month']);
                }
            }
            $_val->lock_operator_money = $lock_money;
            $_val->lock_operator_num = $lock_num;
            $last_fee = number_format(($_val->use_trade_money - $_val->lock_operator_money),2,".","");

            //可开票金额
            $_val->receipt_remain = min($last_fee, $_val->use_assign);
            if ($_val->use_trade_money == 0) {
                $_val->receipt_num = "0.00";
            } else {
                if ($_val->receipt_remain != $last_fee) {
                    $_val->receipt_num = number_format(($_val->receipt_remain * (($_val->trade_num- $_val->lock_operator_num) / $last_fee)), 2, ".", "");
                } else {
                    $_val->receipt_num = number_format(($_val->trade_num - $_val->lock_operator_num),2,".","");
                }
            }

            if(count($use_month) > 0){
                $_val->receipt_month = implode(";",$use_month);
            }else{
                $_val->receipt_month = '';
            }
            if(count($lock_month) > 0){
                $_val->lock_receipt_month = implode(";",$lock_month);
            }else{
                $_val->lock_receipt_month = '';
            }
        }

        return $data;
    }

    static public function getStepMonth($params = [])
    {
        //return ['month'=>["2023-04","2023-05"],'data'=>['2023-04'=>["receipt_amount"=>10,"receipt_num"=>4],'2023-05'=>['receipt_amount'=>11,'receipt_num'=>3]]];
        $result = [];
        $data = OilOrgOperatorDayTrades::getDataByGroup($params);
        if(count($data) > 0){
            $month = $map = [];
            foreach ($data as $_val){
                $month[] = $_val->month;
                $map[$_val->month] = ['receipt_amount'=>$_val->operator_money,'receipt_num'=>$_val->operator_num];
            }
            return ['month'=>$month,'data'=>$map];
        }
        return $result;
    }
}