<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2018/3/18
 * Time: 下午5:29
 */

namespace Fuel\Service;


use Framework\Cache;
use Framework\Config;
use Framework\DingTalk\DingTalkAlarm;
use Framework\Log;
use Framework\Mailer\MailSender;
use Fuel\Defines\CardFrom;
use Fuel\Defines\CardMain;
use Fuel\Defines\CardViceConf;
use Fuel\Defines\OilCom;
use Fuel\Request\dspClient;
use Fuel\Request\OilAgentClient;
use GosSDK\Lib\Encrypter;
use http\Exception\RuntimeException;
use Models\OilCardMain;
use Models\OilCardVice;
use Models\OilCardViceTrades;
use Models\OilCardViceTradesError;
use Models\OilCustomerCard;
use Models\OilOrg;
use Models\OilProvinces;
use Models\OilWxQrcode;
use Illuminate\Database\Capsule\Manager as Capsule;
use \Models\OilOrgWx;

class CustomerCard
{
    public function __construct()
    {

    }

    /**
     * @title   探测帐号秘密是否可用
     * @desc
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    public function probeAccount(array $params)
    {
        \helper::argumentCheck(['orgcode', 'account_no', 'password', 'oil_com', 'other_creator'], $params);

        //校验
        $info = $this->checkAccount($params);

        if (!$info['accountInfo']) {
            //不存在
            //todo 调用油品代理 请求代理根据帐号密码查询接口
            $_params['account']   = $params['account_no'];
            $_params['passwd']    = $params['password'];
            $_params['cardType']  = $params['oil_com'] == 1 ? 'zsh' : 'zsy';
            $_params['actionTag'] = 'TRUST'; //普通托管

            if (in_array(substr($params['orgcode'], 0, 6), \Fuel\Defines\CustomerCard::getNewProxyOrgCode())) {
                $_params['actionTag'] = 'TDEPPON'; //德邦托管
            }else{
                if (isset($params['channel']) && $params['channel'] == 'gas') {
                    $_params['actionTag'] = 'TDEPPON'; //新托管
                }else{
                    throw new \RuntimeException('旧托管已停用，请联系使用新托管服务',2);
                }
            }

            // 第一次绑定新账号不执行删除
            $check_account_no_info = OilCustomerCard::getByAllAccountNo($params['account_no']);
            Log::error('OilCustomerCard::getByAllAccountNo:'.var_export($check_account_no_info,true),[$_params],'customerCard');

            //说明绑定过，代理有可能删除失败，强制删除一次
            if ($info && isset($info->id) && $info->id) {
                //删除代理
                $res = OilAgentClient::post([
                    'method' => 'crawl-provider.manage.canceltrust',
                    'data'   => ['account'=>$_params['account'],'cardType'=>$_params['cardType'],'actionTag'=>$_params['actionTag']],
                ]);

                Log::error('probeAccount-canceltrust-res:'.var_export($res,true),[$_params],'customerCard');
            }

            $parentCardList = $this->probeProxy($_params);

            //Log::error('parentCardList'.var_export($parentCardList,TRUE),[],'customer');

            //正常返回主卡信息列表
            $data['account_no']   = $params['account_no'];
            $data['password']     = $params['password'];
            $data['oil_com']      = $params['oil_com'];
            $data['mainCardList'] = $parentCardList;
        }

        return $data;
    }

    /*
     * 校验
     */
    public function checkAccount($params, $openOrg = FALSE)
    {
        //orgcode校验
        $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            //G7WALLET-469 由于机构必须crm_id，因此不能自动添加机构
            throw new \RuntimeException('机构在foss中不存在或未使用！', 120);
            /*$params['data_from']  = 1;
            $params['creator_id'] = isset($params['creator_id']) && $params['creator_id'] ? $params['creator_id'] : 1;
            $org                  = new Org($params);
            $org->save();
            $orgInfo = OilOrg::getByOrgcode($params['orgcode']);*/
        }
        if($orgInfo->status != 1){
            throw new \RuntimeException('机构在foss中不存在或未使用！', 120);
        }

        //zqx端校验
        $info = OilCustomerCard::getByAccountNo($params['account_no']);

        //Log::error('$info:'.var_export($info,TRUE),[],'checkAccount');

        if ($info && isset($info->id) && $info->id) {
            //存在
            if (substr($info->orgcode, 0, 6) != substr($params['orgcode'], 0, 6)) {
                throw new \RuntimeException('该账号已被托管，无法再次托管！', 101);
            }

            if ($info->orgcode == $params['orgcode']) {
                throw new \RuntimeException('该账号已被托管，直接查看托管列表！', 105);
            }

            if (strlen($info->orgcode) < strlen($params['orgcode'])) {
                throw new \RuntimeException('此账号已被上级[' . $info->Org->org_name . ']机构托管，您可以联系上级机构分配使用权限！', 102);
            } elseif (strlen($info->orgcode) == strlen($params['orgcode'])) {
                throw new \RuntimeException('该账号已被托管，无法再次托管！', 101);
            } else {
                throw new \RuntimeException('此账号已被子级机构托管，您可直接绑卡或更改托管卡归属！', 103);
            }
        }

        //校验错误次数
        //$this->checkErrorTryNum($params['account_no']);

        return ['orgInfo' => $orgInfo, 'accountInfo' => $info];
    }

    /**
     * @title   确认托管
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function confirmTrusteeship(array $params)
    {
        \helper::argumentCheck(['orgcode', 'account_no', 'password', 'oil_com', 'other_creator', 'mainCardList'], $params);

        Log::error('params:' . var_export($params, true), [], 'confirmTrusteeship');
        //校验
        $info = $this->checkAccount($params);

        $params['org_id'] = $info['orgInfo']->id;

        $is_encrypt = 1; //密码是否加密

        //请求三方 确认托管接口
        $_params['account']   = trim($params['account_no']);
        $_params['passwd']    = trim($params['password']);
        $_params['cardType']  = $params['oil_com'] == 1 ? 'zsh' : 'zsy';
        $_params['actionTag'] = 'TRUST'; //普通托管
        //判断是否新德邦模式
        if (in_array(substr($params['orgcode'], 0, 6), \Fuel\Defines\CustomerCard::getNewProxyOrgCode())) {
            $_params['version']   = 'v2';
            $_params['actionTag'] = 'TDEPPON'; //德邦托管
            $is_encrypt           = 2;         //密码加密
        }else{
            if (isset($params['channel']) && $params['channel'] == 'gas') {
                $_params['version']   = 'v2';
                $_params['actionTag'] = 'TDEPPON'; //新托管
                $is_encrypt           = 2;
            }else{
                throw new \RuntimeException('旧托管已停用，请联系使用新托管服务',2);
            }
        }

        $mainCardList = json_decode($params['mainCardList'], TRUE); //todo 对里面的参数进行校验

        //存本地库
        $params['main_card_num'] = count($mainCardList);

        if ($is_encrypt == 2) {
            $params['password'] = $this->passwordEncrypt($params['password']);
        }
        $params['is_encrypt'] = $is_encrypt;

        $card_from = CardFrom::CUSTOMER_CARD_SIMPLE;

        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            //checkExist
            $customerCardInfo = OilCustomerCard::getByAccountNoLockForUpdate($params['account_no']);
            if ($customerCardInfo && isset($customerCardInfo->id) && $customerCardInfo->id) {
                throw new \RuntimeException($params['account_no'] . '已被成功托管过，无需重复托管', 2);
            }

            OilCustomerCard::add($params);

            if ($mainCardList) {
                foreach ($mainCardList as $value) {
                    //checkExist
                    $cardMainInfo = OilCardMain::getByMainNo($value['main_no']);
                    if ($cardMainInfo && isset($cardMainInfo->id) && $cardMainInfo->id) {
                        if ($cardMainInfo->account_name != trim($params['account_no'])) {
                            $mainUpdateArr = [
                                'id'               => $cardMainInfo->id,
                                'account_name'     => trim($params['account_no']),
                                'account_password' => trim($params['password']),
                                'is_encrypt'       => trim($params['is_encrypt']),
                                'account_remain'   => $value['account_remain'],
                                'refresh_status'   => -10,
                                'customer_name'    => isset($value['customer_name']) && $value['customer_name'] ? $value['customer_name'] : '',
                                'updatetime'       => \helper::nowTime()
                            ];
                            if ($cardMainInfo->org_id != $info['orgInfo']->id) {
                                $mainUpdateArr['org_id'] = $info['orgInfo']->id;

                                // 变更副卡所属机构
                                OilCardVice::where(['card_main_id' => $cardMainInfo->id])
                                           ->whereIn('card_from', [40, 41])
                                           ->update(['org_id' => $info['orgInfo']->id]);
                            }
                            OilCardMain::edit($mainUpdateArr);
                        } else {
                            throw new \RuntimeException(trim($value['main_no']) . '已托管过，无需重复托管', 2);
                        }
                    } else {
                        $province_id = 1;
                        //开卡省份
                        if (isset($value['active_region']) && $value['active_region']) {
                            $region_name   = \helper::substr_chiness($value['active_region'], 0, 2);
                            $province_info = OilProvinces::getByProvinceNameLk($region_name);
                            if ($province_info) {
                                $province_id = $province_info->id;
                            }
                        }

                        $cardData = [
                            'main_no'          => trim($value['main_no']),
                            'card_from'        => $card_from ? $card_from : 41,
                            'org_id'           => $info['orgInfo']->id,
                            'oil_com'          => $params['oil_com'],
                            'active_region'    => $province_id,
                            'fanli_region'     => $province_id,
                            'card_owner'       => $value['card_owner'],
                            'active_time'      => isset($value['active_time']) && $value['active_time'] ? $value['active_time'] : \helper::nowTime(),
                            'account_remain'   => $value['account_remain'],
                            'point_remain'     => 0,
                            'refresh_status'   => -10,
                            'account_name'     => trim($params['account_no']),
                            'account_password' => trim($params['password']),
                            'is_encrypt'       => trim($params['is_encrypt']),
                            'remark'           => '数据托管卡',
                            'creator_id'       => 8888,
                            'cretetime'        => \helper::nowTime(),
                            'customer_name'    => isset($value['customer_name']) && $value['customer_name'] ? $value['customer_name'] : '',
                        ];

                        OilCardMain::add($cardData);
                    }


                }
            }

            //将获得到的副卡存入副卡表（未知，不知是否在这一步）

            //获取机构订阅二维码
            $qr_img_url = $this->getQrByOrgcode($params['orgcode']);

            // addTrust 托管到代理系统
            $this->confirmProbeProxy($_params);
            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Log::error(__METHOD__, ['params' => $params, 'exception' => strval($e)], 'confirmTrusteeship');
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode() > 0 ? $e->getCode() : 2);
        }

        return ['qr_img_url' => $qr_img_url];
    }

    /**
     * 托管zsh账号发送短信验证码
     *
     * @param array $params
     * @return mixed
     */
    public function sendSms(array $params)
    {
        \helper::argumentCheck(['tax', 'idno', 'mobile','other_creator'], $params);

        $params['actionTag'] = 'TDEPPON';
        //持卡人姓名
        if($params['card_holder_name']) {
            $params['cardHolder'] = trim($params['card_holder_name']);
        }

        //请求代理
        Log::error('sendSms:',[$params],'sendSms');
        $res = OilAgentClient::post([
            'method' => 'crawl-provider.trustaccount.sendsms',
            'data'   => $params,
        ]);
        Log::error('sendSms-res:',[$res],'sendSms');

        return $res;
    }

    /**
     * 托管zsh账号登录并获取数据
     *
     * @param array $params
     * @return mixed
     */
    public function loginZsh(array $params)
    {
        \helper::argumentCheck(['orgcode','oil_com','tax', 'idno', 'mobile','smsCode','startTime','endTime','other_creator','card_holder_name'], $params);

        Log::error('loginZsh:',[$params],'loginZsh');
        //orgcode校验
        $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
        if (!$orgInfo) {
            throw new \RuntimeException('机构在foss中不存在或未使用！', 120);
        }
        if($orgInfo->status != 1){
            throw new \RuntimeException('机构在foss中不存在或未使用！', 120);
        }

        //判断日期天数小于等于60天
        $diff_day = (strtotime($params['endTime']) - strtotime($params['startTime'])) / 86400;
        if($diff_day > 60){
            throw new \RuntimeException('交易时间范围不能大于60天！', 120);
        }
        if(strtotime($params['endTime']) > strtotime(date('Y-m-d').' 23:59:59')){
            throw new \RuntimeException('交易截止日期不能大于今天',121);
        }

        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            //判断customer_card是否已存在
            $customerCardInfo = OilCustomerCard::getByTaxNoAndIdNoAndMobileLockForUpdate(
                $params['idno'],$params['tax'],$params['mobile']
            );

            if($customerCardInfo){

                //兼容修改持卡人
                if($params['card_holder_name'] && $customerCardInfo->card_holder_name != $params['card_holder_name']) {
                    OilCustomerCard::edit(
                        [
                            'id'=>$customerCardInfo->id,
                            'card_holder_name'=>$params['card_holder_name']
                        ]);
                }

                if($customerCardInfo->org_id != $orgInfo->id){
                    throw new \RuntimeException('此账号已被托管，请先解除原托管账号，否则无法完成托管',2);
                }
            }else{
                OilCustomerCard::add([
                    'account_no' => $params['tax'].'_'.$params['idno'].'_'.$params['mobile'],
                    'org_id' => $orgInfo->id,
                    'orgcode' => $orgInfo->orgcode,
//                    'is_encrypt' => $params['email'],
                    'oil_com' => 1,
                    'sync_status' => '',
                    'sync_time' => \helper::nowTime(),
                    'card_num' => 0, //如果为0，油卡数量会显示正在同步
                    'status' => 1,//1正常 2异常
                    'is_first_finish' => 0, //0 默认未爬取完1此帐号已经爬取完2已发通知
                    'other_creator_id' => $params['other_creator_id'] ?? '', //0 默认未爬取完1此帐号已经爬取完2已发通知
                    'other_creator' => $params['other_creator'], //g7s端创建人姓名
                    'card_owner_ID' => $params['idno'], //身份证号
                    'company_tax' => $params['tax'], //企业税号
                    'reserve_phone' => $params['mobile'], //主站注册手机
                    'is_new' => 2, //新中石化托管卡，1就托管卡
                    'email' => $params['email'], //邮箱
                    'card_holder_name' => trim($params['card_holder_name'])//持卡人姓名
                ]);
            }

            //todo 获取数据记录日志表

            //请求代理
            $sendData = [
                'actionTag' => 'TDEPPON',
                'tax' => $params['tax'],
                'idno' => $params['idno'],
                'mobile' => $params['mobile'],
                'smsCode' => $params['smsCode'],
                'startTime' => $params['startTime'],
                'endTime' => $params['endTime'],
                'cardHolder' => trim($params['card_holder_name'])
            ];
            Log::error('requestDsp:',[$sendData],'loginZsh');
            $res = OilAgentClient::post([
                'method' => 'crawl-provider.trustaccount.login',
                'data'   => $sendData,
            ]);
            Log::error('requestDsp-res:',[$res],'loginZsh');

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Log::error(__METHOD__, ['params' => $params, 'exception' => strval($e)], 'loginZsh');
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode() > 0 ? $e->getCode() : 2);
        }

        return $res;
    }

    /*
     * 取消托管zsh账号接口
     */
    public function zshDeleteAccount(array $params)
    {
        \helper::argumentCheck(['orgcode','id','other_creator'], $params);



        Capsule::connection()->beginTransaction();
        try{
            $params['account_no'] = $params['tax'].'_'.$params['idno'].'_'.$params['mobile'];
            $accountInfo = OilCustomerCard::getById($params);
            if(!$accountInfo){
                throw new \RuntimeException('此账户数据不存在！', 120);
            }

            if($accountInfo->is_new != 2){
                throw new \RuntimeException('此账户非新中石化！', 120);
            }

            //请求代理
            Log::error('zshDeleteAccount:',[$params],'zshDeleteAccount');
            $res = OilAgentClient::post([
                'method' => 'crawl-provider.trustaccount.cancel',
                'data'   => [
                    'actionTag' => 'TDEPPON',
                    'tax' => $accountInfo->company_tax,
                    'idno' => $accountInfo->card_owner_ID,
                    'mobile' => $accountInfo->reserve_phone,
                ],
            ]);
            Log::error('zshDeleteAccount-res:',[$res],'zshDeleteAccount');

            //删除本地
            OilCustomerCard::hardDelete($accountInfo->id);

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), 2);
        }

        return true;
    }

    /**
     * 密码加密
     *
     * @param $password
     * @return string
     */
    public function passwordEncrypt($password)
    {
        $encrypt = new Encrypter(\Fuel\Defines\CustomerCard::getEncriptKey());

        $encryptStr = $encrypt->encrypt($password);

        return $encryptStr;
    }

    /**
     * 密码解密
     *
     * @param $password
     * @return string
     */
    public function passwordDeCrypt($password)
    {
        $encrypt = new Encrypter(\Fuel\Defines\CustomerCard::getEncriptKey());

        return $encrypt->decrypt($password);
    }

    /**
     * @title   根据机构获取二维码图片地址
     * @desc
     * @param $orgcode
     * @return string
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    public function getQrByOrgcode($orgcode)
    {
        $qrCode     = OilWxQrcode::getQrCodeByOrgCode(['orgcode' => $orgcode, 'wxQrCodePreFix' => '301']);
        $qr_img_url = $qrCode ? $qrCode->img_url : '';

        return $qr_img_url;
    }

    /**
     * @title   请求代理根据帐号密码获得主卡相关信息
     * @desc
     * @param $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    public function probeProxy($params)
    {
        \helper::argumentCheck(['account', 'passwd', 'cardType', 'actionTag'], $params);
        $data = NULL;
        try {
            //Log::error('probeProxy-params:'.var_export($params,true),[],'customerCard');
            $data = OilAgentClient::post([
                'method' => 'crawl-provider.manage.checktrust',
                'data'   => $params,
            ]);
            Log::error('probeProxy-res:'.var_export($data,true),[$params],'customerCard');
        } catch (\Exception $e) {
            $errorNum = Cache::get($params['account'] . date('Y-m-d'));
            $errorNum = $errorNum ? $errorNum + 1 : 1;
            Cache::put($params['account'] . date('Y-m-d'), $errorNum, 86400);

            $mayTryNum = 3 - $errorNum;
//            throw new \RuntimeException($e->getMessage().',您今天还有'.$mayTryNum.'次密码出错的机会',$mayTryNum < 0 ? 104 : 2);
            throw new \RuntimeException($e->getMessage(), $mayTryNum < 0 ? 104 : 2);
        }

        $parentCardList = [];
        if ($data) {
            foreach ($data->parentCardList as $v) {
                $parentCardList[] = [
                    'main_no'        => $v->cardNo,
                    'card_owner'     => $v->cardHolder, //该主卡在网厅的注册持卡人
                    'customer_name'  => $v->compName, //该主卡在网厅的客户名称
                    'account_remain' => $v->balance,
                    'active_region'  => $v->cardProvince,
                    'active_time'    => $v->startDate,
                    'point_remain'   => $v->integral,
                ];
            }
        }

        return $parentCardList;
    }

    /**
     * 同步代理托管账户的状态
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function syncAccountStatus($params)
    {
        $update_data = $errorMsg = [];
        //发起代理查询
        $params = [
            'method' => 'huoyunren.gascard.syncAccount',
            'data'   => [
                'data'   => json_encode([
                    'accounts' => ''
                ]),
                'format' => 'json',
            ],
        ];
        $res = dspClient::post($params);
        $dsp_map = array_column($res,'wrongPass','account');

        $local_map = [];
        $local_data = OilCustomerCard::getAll();
        foreach ($local_data as $key=>$value){
            $local_map[$value['account_no']] = $value['deleted_at'] ? 3 : $value['status'];
        }

        //代理账户数量大于本地账户数量
        if(count($dsp_map) > count($local_map)){
            $errorMsg[] = implode(',',array_keys(array_diff_key($dsp_map,$local_map))).'代理有foss系统无此账号';
        }
        //本地账户数量代理账户数量
        if(count($dsp_map) < count($local_map)){
            $errorMsg[] = implode(',',array_keys(array_diff_key($local_map,$dsp_map))).'foss有代理系统无此账号';
        }

        $status_map = [0=>1,444=>2,9999=>3];
        foreach ($local_map as $account_no => $status){
            //本地和代理的状态不一致
            if(isset($dsp_map[$account_no]) && $status_map[$dsp_map[$account_no]] && $status_map[$dsp_map[$account_no]] != $status){
                $update_data[] = ['status'=>$status_map[$dsp_map[$account_no]],'account_no'=>$account_no];
                $errorMsg[] = ['local_status'=>$status,'dsp_status'=>$dsp_map[$account_no],'account_no'=>$account_no];
            }
        }

        if($update_data){
            $sql = \helper::batchUpdateToSqlStrExt($update_data,'oil_customer_card','account_no');
            Log::error($sql,[],'syncAccountStatus');
            Capsule::connection()->getPdo()->exec($sql);

            //飞书提醒
            $alarmMessage[] = "* 描述：托管账号状态发现不一致\n";
            $alarmMessage[] = "* 环境：" . (API_ENV . "\n" ? ucfirst(API_ENV) . "\n" : "未知\n");
            $alarmMessage[] = "* 内容：" . json_encode($errorMsg, JSON_UNESCAPED_UNICODE) . "\n";
            $contentStr     = implode("", $alarmMessage);

            try {
                (new DingTalkAlarm())->alarmToGroup('托管账号状态发现不一致', $contentStr, [], TRUE, TRUE);
            } catch (\Exception $e) {
                Log::error('DingTalk' . $e->getMessage(), [strval($e)], 'cronCrmOrg');
            }
        }

        return true;
    }

    /**
     * @title   确认托管
     * @desc
     * @param $params
     * @return mixed
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    public function confirmProbeProxy($params)
    {
        \helper::argumentCheck(['account', 'passwd', 'cardType', 'actionTag'], $params);

        try {
            //Log::error('confirmProbeProxy-params:'.var_export($params,true),[],'customerCard');

            $data = OilAgentClient::post([
                'method' => 'crawl-provider.manage.addtrust',
                'data'   => $params,
            ]);
            Log::error(__METHOD__, ['params' => $params, 'data' => $data], 'customerCard');

            //Log::error('confirmProbeProxy-res:'.var_export($data,true),[],'customerCard');
        } catch (\Exception $e) {
            $errorNum = Cache::get($params['account'] . date('Y-m-d'));
            $errorNum = $errorNum ? $errorNum + 1 : 1;
            Cache::put($params['account'] . date('Y-m-d'), $errorNum, 86400);

            $mayTryNum = 3 - $errorNum;
            Log::error(__METHOD__, ['params' => $params, 'exception' => strval($e)], 'confirmTrusteeship');

//            throw new \RuntimeException($e->getMessage().',您今天还有'.$mayTryNum.'次密码出错的机会',$mayTryNum < 0 ? 104 : 2);
            throw new \RuntimeException($e->getMessage(), $mayTryNum < 0 ? 104 : 2);
        }

        return TRUE;
    }

    public function checkErrorTryNum($account_no)
    {
        $errorNum = Cache::get($account_no . date('Y-m-d'));

        if ($errorNum && $errorNum >= 3) {
            throw new \RuntimeException('今日密码出错次数已达上限，请明日再来', 104);
        }
    }

    /**
     * @title   请求代理根据帐号密码查询接口
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getAccountInfoFromProxy(array $params)
    {
        //todo
    }

    /**
     * @title   编辑帐号只能修改密码或者所属机构 （？如果修改机构，卡的所属机构也更改）
     * @desc
     * @param array $params
     * @returns
     * []
     * @returns
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     */
    public function editAccount(array $params)
    {
        \helper::argumentCheck(['orgcode', 'current_orgcode'], $params);

        //判断是否新德邦模式
        if (in_array(substr($params['orgcode'], 0, 6), \Fuel\Defines\CustomerCard::getNewProxyOrgCode())
        || (isset($params['channel']) && $params['channel'] == 'gas')) {
            \helper::argumentCheck(['account_no'], $params);
            $_params['version']   = 'v2';
            $_params['actionTag'] = 'TDEPPON';
            $is_encrypt           = 2; //密码加密
            $accountInfo          = OilCustomerCard::getByAccountNoAndOrg(['account_no' => $params['account_no'], 'orgcode' => $params['current_orgcode']]);
        } else {
            $_params['actionTag'] = 'TRUST';
            $is_encrypt           = 1;
            \helper::argumentCheck(['id'], $params);
            $accountInfo = OilCustomerCard::getByIdAndOrg(['id' => $params['id'], 'orgcode' => $params['current_orgcode']]);
        }

        if (!$accountInfo) {
            throw new \RuntimeException('账户信息不存在', 2);
        }


        try {
            //todo 调用油品代理 请求代理根据帐号密码查询接口
            $_params['account'] = $accountInfo->account_no;
            if (isset($params['password']) && $params['password']) {
                $_params['passwd'] = $params['password'];
            } else {
                if ($accountInfo->is_encrypt == 2) {
                    $passwd = $this->passwordDeCrypt($accountInfo->password);
                } else {
                    $passwd = $accountInfo->password;
                }
                $_params['passwd'] = $passwd;
            }
            $_params['cardType']  = $accountInfo->oil_com == 1 ? 'zsh' : 'zsy';

            $res = OilAgentClient::post([
                'method' => 'crawl-provider.manage.checktrust',
                'data'   => $_params,
            ]);

            Log::error('editAccount-checktrust-res:'.var_export($res,true),[$_params],'customerCard');

        } catch (\Exception $e) {
            //中国邮政
            if(stripos($e->getMessage(),"已经托管") === false) {
                throw new \RuntimeException($e->getMessage(), 2);
            }
        }

        //Log::error('$params'.var_export($params,TRUE),[],'editAccount');
        Capsule::connection()->beginTransaction();
        try {
            //修改email
            if (isset($params['email']) && $params['email'] && $params['email'] != $accountInfo->email) {
                $accountInfo->update(['email' => $params['email']]);
            }

            //修改密码
            if (isset($params['password']) && $params['password'] != '') {
                $this->checkErrorTryNum($accountInfo->account_no);
                //根据新的密码再次发起代理探测查询并修改
                $_params['account']  = $accountInfo->account_no;
                $_params['cardType'] = $accountInfo->oil_com == 1 ? 'zsh' : 'zsy';
                $_params['passwd']   = $params['password'];
                $_params['opType']   = 'UPDATE';
                $this->confirmProbeProxy($_params);

                if ($is_encrypt == 2) {
                    $params['password'] = $this->passwordEncrypt($params['password']);
                }
                $params['is_encrypt'] = $is_encrypt;

                //修改本地
                $accountInfo->update(['password' => $params['password'], 'is_encrypt' => $is_encrypt, 'status' => 1, 'sync_status' => NULL]);

                //修改主卡
                $mainInfo = OilCardMain::getByAccountInfo($accountInfo->account_no);
                if ($mainInfo) {
                    if ($mainInfo->account_status != 10) {
                        $mainInfo->update([
                            'account_password'    => $params['password'],
                            'is_encrypt'          => $is_encrypt,
                            'account_status'      => 10,
                            'account_status_desc' => '正常',
                            'account_status_time' => \helper::nowTime()
                        ]);
                    }
                }
            }

            //修改机构 注释不能修改机构
            /*if(isset($params['orgcode']) && $params['orgcode'] && $params['orgcode'] != $accountInfo->orgcode){
                $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
                $accountInfo->update(['org_id'=>$orgInfo->id,'orgcode'=>$params['orgcode']]);

                //修改帐号下所有主卡的所属机构和所有副卡的所属机构
                OilCardMain::updateOrgWithMainCardAndCardVice($accountInfo->account_no,$orgInfo->id);
            }*/
            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            Log::error('exception' . strval($e), [], 'editAccount');
            throw new \RuntimeException($e->getMessage(), 2);
        }

        return TRUE;
    }

    /**
     * @title   解除授权（是否是直接删除，或者假删）
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function removeAccount(array $params)
    {
        \helper::argumentCheck(['orgcode'], $params);
        Log::error('removeAccount-params:' . var_export($params, true), [], 'customerCard');

        //判断是否新德邦模式
        if (in_array(substr($params['orgcode'], 0, 6), \Fuel\Defines\CustomerCard::getNewProxyOrgCode()) || (isset($params['channel']) && $params['channel'] == 'gas') ) {
            \helper::argumentCheck(['account_no'], $params);
            $accountInfo = OilCustomerCard::getByAccountNoAndOrg($params);
            $actionTag   = 'TDEPPON';
        } else {
            \helper::argumentCheck(['id'], $params);
            $accountInfo = OilCustomerCard::getByIdAndOrg($params);
            $actionTag   = 'TRUST';
        }

        if (!$accountInfo) {
            throw new \RuntimeException('帐号信息不存在！', 2);
        }

        Capsule::connection()->beginTransaction();
        try {
            $sendParams = [
                'account'   => $accountInfo->account_no,
                'cardType'  => $accountInfo->oil_com == 1 ? 'zsh' : 'zsy',
                'actionTag' => $actionTag,
            ];

            //删除代理
            $res = OilAgentClient::post([
                'method' => 'crawl-provider.manage.canceltrust',
                'data'   => $sendParams,
            ]);

            Log::error('removeAccount-res:'.var_export($res,true),[$sendParams],'customerCard');

            //删除本地
            OilCustomerCard::remove(['ids' => $accountInfo->id]);

            //删除主卡
            $mainList = OilCardMain::getByAccount($accountInfo->account_no);
            $mainIds  = [];
            if ($mainList) {
                foreach ($mainList as $v) {
                    $mainIds[] = $v->id;
                }
            }

            OilCardMain::remove(['ids' => $mainIds]);

            //得到要删除的卡id
            $cardIds = OilCardVice::whereIn('card_main_id', $mainIds)
                                  ->where('card_from', CardFrom::CUSTOMER_CARD_SIMPLE)
                                  ->pluck('id')->toArray();

            //删除副卡
            OilCardVice::removeByMainIds(['mainIds' => $mainIds]);

            if ($cardIds) {
                //删除卡到gos
                CardViceToGos::sendBatchDeleteTask($cardIds);
            }

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), 2);
        }


        return TRUE;
    }

    /**
     * @title   根据帐号查主卡信息
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getMainCardByAccountNo(array $params)
    {
        \helper::argumentCheck(['account_no'], $params);

        $mainList = OilCardMain::getByAccount($params['account_no']);

        return $mainList;
    }

    /**
     * @title   根据帐号查主卡信息
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getMainCardList(array $params)
    {
        \helper::argumentCheck(['orgcode'], $params);

        $params['limit'] = isset($params['page_size']) ? $params['page_size'] : 50;
        $params['page']  = isset($params['page_no']) ? $params['page_no'] : 1;

        $data = ['page_no' => 1, 'page_size' => 10, 'result' => [], 'totalCount' => 0];

        $account_arr = OilCustomerCard::getByOrgCode($params);

        $params['account_name_in'] = $account_arr;
        $params['card_from']       = 41;
        //Log::error('$params'.var_export($params,true),[],'tim423');
        if ($account_arr) {
            $data = OilCardMain::getList($params);

            //临时取消
            /*if($data && isset($params['curent_orgcode']) && $params['curent_orgcode']){
                foreach ($data as $v){
                    //机构名称显示机构层级关系处理
                    $v->org_name = \Models\OilOrg::getOrgTreePart($params['curent_orgcode'],$v->orgcode);
                }
            }*/
        }

        Log::error('data', [$data], 'tim0603');

        return $data;
    }

    /**
     * @title   根据帐号查主卡信息
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function getCardList(array $params)
    {
        \helper::argumentCheck(['orgcode'], $params);

        $params['limit'] = isset($params['page_size']) ? $params['page_size'] : 50;
        $params['page']  = isset($params['page_no']) ? $params['page_no'] : 1;

        $data = ['page_no' => 1, 'page_size' => 10, 'result' => [], 'totalCount' => 0];

        $account_arr = OilCustomerCard::getByOrgCode($params);

        $params['account_name_in'] = $account_arr;

        $params['card_from'] = 41;
        $params['org_flag']  = 1;

        if (isset($params['main_no_lk']) && $params['main_no_lk']) {
            $mainIds = OilCardMain::whereIn('account_name', $account_arr)->where('main_no', 'like', '%' . $params['main_no_lk'] . '%')->pluck('id')->toArray();
        } else {
            $mainIds = OilCardMain::whereIn('account_name', $account_arr)->pluck('id')->toArray();
        }

        $params['card_main_id_in'] = $mainIds;

        $orgIds              = OilOrg::where('orgcode', 'like', $params['orgcode'] . '%')->pluck('id')->toArray();
        $params['org_id_in'] = $orgIds;

        $params['statusNotIn'] = "废弃";
        if ($mainIds && $orgIds) {
            if (isset($params['_export']) && $params['_export'] == 1) {
                $data = CardViceExport::instance()->exportCardForG7s($params);
            } else {
                $data = OilCardVice::getList($params);
            }
        }

        return $data;
    }

    /**
     * @title   发起根据帐号同步副卡信息以及主卡数量，在完成执行判断首次执行发送微信通知、
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function syncAccountInfo(array $params)
    {
        //获取所有托管卡账户
        $accountListObj = OilCustomerCard::whereIn('status', [1, 2]);
        //->where('account_no','****************')
        if (isset($params['version']) && $params['version'] == 'v2') {
            $accountListObj->where('is_encrypt', 2);
        }
        if (isset($params['account_no']) && $params['account_no']) {
            $accountListObj->where('account_no', $params['account_no']);
        }
        $accountList = $accountListObj->orderBy('sync_time', 'asc')
                                      ->get();

        if ($accountList) {
            foreach ($accountList as $accountInfo) {

                $mainList = OilCardMain::getByAccount($accountInfo->account_no);
                $mainIds  = [];
                if ($mainList) {
                    foreach ($mainList as $v) {
                        $mainIds[] = $v->id;
                    }
                }

                $max_id = OilCardVice::whereIn('card_main_id', $mainIds)->max('third_id'); //todo add field to mysql table

                $max_id = $max_id ? $max_id : 0;

                //发起代理查询
                $params = [
                    'method' => 'huoyunren.gascard.syncsubcard',
                    'data'   => [
                        'data'   => json_encode([
                            'account'  => $accountInfo->account_no,
                            'cardType' => $accountInfo->oil_com == 1 ? 'zsh' : 'zsy',
                            'lastId'   => $max_id,
                        ]),
                        'format' => 'json',
                    ],
                ];
                Log::error('$params--' . var_export($params, TRUE), [], 'syncAccountInfo');
                $res = dspClient::post($params);
                Log::error('res--' . var_export($res, TRUE), [], 'syncAccountInfo');

                if ($res) {
                    foreach ($res as $cardInfo) {
                        //得到主卡信息
                        $main_id = $this->getMainId($cardInfo, $accountInfo);

                        //添加副卡
                        try {
                            $this->insertCard($cardInfo, $accountInfo, $main_id);
                        } catch (\Exception $e) {
                            //insert into card error
                        }
                    }
                }

                //一个账户爬取完毕判断是否需要微信通知
                if ($accountInfo->is_first_finish == 1 && $accountInfo->is_encrypt != 2) {
                    $this->sendWeixin($accountInfo);
                }

                //变更主卡数量和副卡数量
                $this->updateMainNumAndCardNum($accountInfo, $accountInfo->is_first_finish);

            }

        }

        return true;

    }

    /*
     * 首次爬去完通知客户
     */
    public function firstFinishSendMail($email, $accountInfo)
    {
        //if ($email) {
        Log::error('进入首次爬取发送邮件', [$email], 'firstFinishSendMail');
        $oil_com_name = $accountInfo->oil_com == 1 ? '中石化' : '中石油';
        $emailContent = '<h3>油卡托管客户，你好：</h3></br>';
        $emailContent .= '你托管的 ' . $oil_com_name . ' <font color="red">' . $accountInfo->account_no . '</font> 账号已经完成了首次数据同步，请登陆系统查询。';

        $this->sendNoticeEmail($emailContent, $email);
        //}
    }

    /*
     * 最终修改主卡数和副卡数以及同步时间
     *
     */
    public function updateMainNumAndCardNum($accountInfo, $is_first_finish = 2)
    {
        $mainList = OilCardMain::getByAccount($accountInfo->account_no); //重新查主卡的信息
        $mainIds  = [];
        if ($mainList) {
            foreach ($mainList as $v) {
                $mainIds[] = $v->id;
            }
        }

        $mainNum = count($mainIds);
        $cardNum = OilCardVice::whereIn('card_main_id', $mainIds)->count();

        OilCustomerCard::edit([
            'id'            => $accountInfo->id,
            'main_card_num' => $mainNum,
            'card_num'      => $cardNum,
            'sync_time'     => \helper::nowTime(),
        ]);

        if ($is_first_finish != 2 && $cardNum > 0) {
            Log::error('发送首次通知邮件' . $accountInfo->email, [], 'syncAccountInfo');
            $this->firstFinishSendMail($accountInfo->email, $accountInfo);

            OilCustomerCard::edit(['id' => $accountInfo->id, 'is_first_finish' => 2]);
        }
    }

    /*
     * 插入副卡操作
     */
    public function insertCard($cardInfo, $accountInfo, $main_id)
    {
//        $status = '使用';
//        if($cardInfo->cardStatus){
//            $status = $cardInfo->cardStatus;
//            if($cardInfo->cardStatus == '正常'){
//                $status = '使用'; //兼容中石油卡状态是正常不是使用，统一使用
//            }
//        }
        $inData = [
            'third_id'       => $cardInfo->id,
            'vice_no'        => $cardInfo->cardNo,
            'card_from'      => CardFrom::CUSTOMER_CARD_SIMPLE,
            'oil_com'        => $accountInfo->oil_com,
            'card_main_id'   => $main_id,
            'vice_tmp_id'    => 1,
            'org_id'         => $accountInfo->org_id,
            'org_id_fanli'   => $accountInfo->org_id,
            'active_time'    => \helper::nowTime(),
            'creator_id'     => 8888,
            'refresh_status' => 0,
            'createtime'     => \helper::nowTime(),
            'status'         => $cardInfo->cardStatus ? $cardInfo->cardStatus : '--',
        ];

        //$cardInfoLocal = OilCardVice::getByThirdId($cardInfo->id);
        $cardInfoLocal = OilCardVice::getByViceNo(['vice_no' => $cardInfo->cardNo]);

        if ($cardInfoLocal) {
            $cardInfoLocal->update($inData); //是否要编辑还需要确定

            //修改卡到到gos
            CardViceToGos::batchUpdateToGosByViceIds([$cardInfoLocal->id]);
        } else {
            $insertRes = OilCardVice::add($inData); //因为是增量没有多少数据所以单条循环插入
            //添加卡到gos
            CardViceToGos::batchAddByIdsToGos([$insertRes->id]);
        }
    }

    /*
     * 首次发送微信通知
     */
    public function sendWeixin($accountInfo)
    {
        //发送微信通知
        try {
            OilOrgWx::sendFinishNotify([
                'org_id'     => $accountInfo->org_id,
                'account_no' => $accountInfo->account_no,
                'oil_com'    => $accountInfo->oil_com == 1 ? '中石化' : '中石油',
            ]);
        } catch (\Exception $e) {
            Log::error('sendNotify--' . strval($e), [], 'wxDebug');
        }

        OilCustomerCard::edit(['id' => $accountInfo->id, 'is_first_finish' => 2]);
    }

    /*
     * 获取主卡id
     */
    public function getMainId($cardInfo, $accountInfo)
    {
        $mainCard = OilCardMain::getByMainNo($cardInfo->parentCardNo);

        if (!$mainCard) {
            //主卡不存在要创建
            $cardData['main_no']          = $cardInfo->parentCardNo;
            $cardData['card_from']        = CardFrom::CUSTOMER_CARD_SIMPLE;  //41
            $cardData['org_id']           = $accountInfo->org_id;
            $cardData['oil_com']          = $accountInfo->oil_com;
            $cardData['active_region']    = 1; //todo 需要转换
            $cardData['fanli_region']     = 1; //todo 需要转换
            $cardData['card_owner']       = '';
            $cardData['active_time']      = '';
            $cardData['account_remain']   = 0;
            $cardData['point_remain']     = 0;
            $cardData['refresh_status']   = -10;
            $cardData['account_name']     = $accountInfo->account_no;
            $cardData['account_password'] = $accountInfo->password;
            $cardData['is_encrypt']       = $accountInfo->is_encrypt;
            $cardData['remark']           = '数据托管卡';
            $cardData['creator_id']       = 8888;
            $cardData['cretetime']        = \helper::nowTime();

            $mainCard = OilCardMain::add($cardData);
        }

        return $mainCard->id;
    }

    /**
     * @title   代理首次爬取完毕会回调此方法 并存入表中
     * @desc
     * @version
     * @level   1
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @returns
     * []
     * @returns
     */
    public function notifyCardSnatchFinish(array $params)
    {
        \helper::argumentCheck(['account_no'], $params);

        Log::error('notifyCardSnatchFinish', [$params], 'notifyCardSnatchFinish');

        $accountInfo = OilCustomerCard::getByAccountNo($params['account_no']); //todo $third_id

        if (!$accountInfo) {
            throw new \RuntimeException('此帐号已不存在', 2);
        }

//        OilCustomerCard::edit([
//            'id'=>$accountInfo->id,
//            'is_first_finish' => 1
//        ]);
        if ($accountInfo->is_first_finish == 0) {
            $accountInfo->update(['is_first_finish' => 1]);
        }

        //新增触发查询
        $this->syncAccountInfo(['account_no' => $params['account_no'], 'version' => 'v2']);
        Log::error('end', ['end'], 'notifyCardSnatchFinish');

        return TRUE;
    }

    /**
     * @title   在爬取时出现模拟登录异常时回调接口
     * @desc
     * @param array $params
     * @returns
     * []
     * @return
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    public function notifyLoginException(array $params)
    {
        \helper::argumentCheck(['account_no', 'message'], $params);
        Log::error('notifyLoginException' . var_export($params, true), [], 'notifyLoginException');
        $email = NULL;
        //如果有错误就更改帐号的状态
        $accountInfo = OilCustomerCard::getByAccountNo($params['account_no']);

        if (!$accountInfo) {
            throw new \RuntimeException('此帐号已不存在', 2);
        }

        $email = $accountInfo->email;
        Log::error('email-', [$email], 'notifyLoginException');
//        OilCustomerCard::edit([
//            'id' => $accountInfo->id,
//            'status' => 2,
//            'sync_status' => $params['message'],
//        ]);
        if ($accountInfo->status == 2) {
            $udate = [
                'status'      => 2,
                'sync_status' => strpos($params['message'], '密码') !== false ? '密码错误' : '登录异常',
            ];
        } else {
            $udate = [
                'status'      => 2,
                'sync_status' => strpos($params['message'], '密码') !== false ? '密码错误' : '登录异常',
                'sync_time'   => \helper::nowTime()
            ];
        }
        $accountInfo->update($udate);

        //同时处理主卡的状态
        $mainInfo = OilCardMain::getByAccount($params['account_no']);

        if ($mainInfo) {
            OilCardMain::where('card_from', CardFrom::CUSTOMER_CARD_SIMPLE)
                       ->where('account_name', $params['account_no'])
                       ->update([
                           'account_status'      => 20,
                           'account_status_desc' => $udate['sync_status'],
                           'account_status_time' => \helper::nowTime()
                       ]);
        }

        //发送通知邮件
        if ($email) {
            Log::error('进入发送邮件', [$email], 'notifyLoginException');
            $cache_value = Cache::get('sendNoticeEmail');
            $cache_value = $cache_value ? $cache_value : [];
            Log::error('cache_value', [$cache_value], 'notifyLoginException');
            $oil_com_name = $accountInfo->oil_com == 1 ? '中石化' : '中石油';
            $emailContent = '<h3>油卡托管客户，你好：</h3></br>';
            $emailContent .= '你托管的 ' . $oil_com_name . ' <font color="red">' . $params['account_no'] . '</font> 账号出现 <font color="red"> 登陆密码错误</font> 的问题，请及时登陆主站，验证是否存在问题，如果你修改了网厅的账号和密码请同步更新托管账号和密码，以防出现数据抓取延时的问题。';
            if (!in_array($params['account_no'], $cache_value)) {
                Log::error('发送邮件', [$email], 'notifyLoginException');
                $this->sendNoticeEmail($emailContent, $email);

                Cache::put('sendNoticeEmail', array_merge($cache_value, [$params['account_no']]), 86400 - (time() + 8 * 3600) % 86400);
            }

        }

        return TRUE;

    }

    public function sendNoticeEmail($content, $email)
    {
        $params['title']   = date("Y-m-d", time()) . "油卡托管提醒";
        $params['content'] = $content;
        \helper::argumentCheck(['title', 'content'], $params);

        $alarmEmailList = array(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        );
        if ($email) {
            $alarmEmailList[] = $email;
        }

        try {
            $emailRes = \Framework\Mailer\MailSender::sendNow($params['title'], $params['content'], $alarmEmailList, true, false, [], false);
            \Framework\Log::error("对账发送邮件结果" . json_encode($emailRes), [], "sendNoticeEmail");
        } catch (\Exception $e) {
            \Framework\Log::error("对账发送邮件有误" . json_encode($e), [], "sendNoticeEmail");
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

    }

    /**
     * 自有主卡队列消费
     */
    public function subscribe_gsp_main_card($queue_name)
    {
        global $app;
        \helper::import($app->getAppRoot() . "lib/redis.queue.class.php");
        $config = Config::get('redis');
        $queue  = new \redisQueue($config);
        $queue->setQueue($queue_name)->consume(function ($msg) {
            try {
                $record = json_decode($msg);

                \Framework\Log::error(__METHOD__ . "#自有主卡队列消费", ['msg' => $record], 'subscribe_gsp_main_card');

                if ($record) {
                    if (isset($record->cardNo) && $record->cardNo && isset($record->balance) && $record->balance) {
                        OilCardMain::updateByMainNo([
                            'main_no'         => $record->cardNo,
                            'remain_syn_time' => date("Y-m-d H:i:s", $record->lastUpdated / 1000),
                            'account_remain'  => $record->balance,
                            'point_remain'    => $record->integral,
                        ]);
                    }


                }
            } catch (\Exception $e) {
                $content = ["标题" => '自有主卡队列消费异常', '详情' => $msg, 'exception' => strval($e)];
                \Framework\Log::error(__METHOD__ . "#自有主卡队列消费异常", ['eception' => strval($e), 'msg' => $msg], 'subscribe_gsp_main_card');

                (new \Framework\DingTalk\DingTalkAlarm())->alarmToGroup("自有主卡队列消费异常", implode("\n", $content));
                echo strval($e);
            }
        });
    }

    /**
     * 主卡队列消费
     */
    public function subscribe_main_card($queue_name)
    {
        global $app;
        \helper::import($app->getAppRoot() . "lib/redis.queue.class.php");
        $config = Config::get('redis');
        $queue  = new \redisQueue($config);
        $queue->setQueue($queue_name)->consume(function ($msg) {
            try {
                $record = json_decode($msg);
                $res    = [];
                if ($record) {
//                    \Framework\Log::error(__METHOD__ . "#主卡队列报文:", [$record], 'subscribe_main_card');

                    $res = ['cardNo' => $record->cardNo];

                    $accountInfo = OilCustomerCard::getByAccountNo($record->cardAccount);
                    if ($accountInfo) {
                        $province_id = 1;
                        //开卡省份
                        if (isset($record->cardProvince) && $record->cardProvince) {
                            $region_name   = \helper::substr_chiness($record->cardProvince, 0, 2);
                            $province_info = OilProvinces::getByProvinceNameLk($region_name);
                            if ($province_info) {
                                $province_id = $province_info->id;
                            }
                        }

                        $arr = [
                            'main_no'             => $record->cardNo,
                            'account_remain'      => $record->balance,
                            'card_from'           => CardFrom::CUSTOMER_CARD_SIMPLE,
                            'org_id'              => $accountInfo->org_id,
                            'oil_com'             => $accountInfo->oil_com,
                            'active_region'       => $province_id,
                            'fanli_region'        => $province_id,
                            'card_owner'          => $record->cardHolder,
                            'customer_name'       => isset($record->compName) ? $record->compName : '',
                            'active_time'         => '',
                            'point_remain'        => $record->integral,
                            'account_status'      => isset($record->cardStatus) && $record->cardStatus == '使用' ? 10 : 20,
                            'account_status_desc' => isset($record->cardStatus) && $record->cardStatus ? $record->cardStatus : '',
                            'refresh_status'      => -10,
                            'account_name'        => $accountInfo->account_no,
                            'account_password'    => $accountInfo->password,
                            'is_encrypt'          => $accountInfo->is_encrypt,
                            'remark'              => '数据托管卡',
                            'creator_id'          => '8888',
                            'property'            => 3,//托管业务卡(半托管)
                            'province_receipt_out'=> "",
                            'fanli_desc'          => "",
                            'card_owner_ID'       => "",
                            'company_tax'         => "",
                            'reserve_phone'       => "",
                            'query_code'          => "",
                            'goods_limit'         => "",
                        ];

                        $mainInfo = \Models\OilCardMain::getByMainNo($record->cardNo);

                        if (!$mainInfo || !isset($mainInfo->id) || !$mainInfo->id) {
                            $arr['createtime'] = date("Y-m-d H:i:s");
                            $res['result']     = \Models\OilCardMain::add($arr);
                        } else {
                            $arr['id']         = $mainInfo->id;
                            $arr['updatetime'] = date("Y-m-d H:i:s");
                            $res['result']     = \Models\OilCardMain::edit($arr);
                        }

//                        \Framework\Log::error(__METHOD__ . "#主卡队列消费", ['result' => $res, 'msg' => $msg], 'subscribe_main_card');
                    } else {
                        \Framework\Log::error(__METHOD__ . "#主卡队列消费异常", ['msg' => $msg], 'subscribe_main_card');
                    }
                }
            } catch (\Exception $e) {
                $content = ["标题" => '主卡队列消费异常', '详情' => $msg, 'exception' => strval($e)];
                \Framework\Log::error(__METHOD__ . "#主卡队列消费异常", ['eception' => strval($e), 'params' => $msg], 'subscribe_main_card');

                (new \Framework\DingTalk\DingTalkAlarm())->alarmToGroup("主卡队列消费异常", implode("\n", $content));
                echo strval($e);
            }

//            \Framework\Log::error(__METHOD__ . "#主卡队列消费结果", ['result' => 'ok'], 'subscribe_main_card');
        });
    }

    /**
     * 副卡队列消费
     */
    public function subscribe_vice_card($queue_name)
    {
        global $app;
        \helper::import($app->getAppRoot() . "lib/redis.queue.class.php");
        $config = Config::get('redis');
        $queue  = new \redisQueue($config);
        $queue->setQueue($queue_name)->consume(function ($msg) {
            $res = [];
            try {
                $record = json_decode($msg);
                if ($record) {
//                    \Framework\Log::error(__METHOD__ . "#主卡队列消费报文", ['record' => $record], 'subscribe_vice_card');

                    $res = ['cardNo' => $record->cardNo];

                    $mainInfo = OilCardMain::getByMainNo($record->parentCardNo);
                    //G7WALLET-2405
                    if( stripos($mainInfo->account_status_desc,"挂失") !== false ){
                        return;
                    }
                    if ($mainInfo) {
                        $inData = [
                            'third_id'       => $record->id,
                            'vice_no'        => $record->cardNo,
                            'card_from'      => CardFrom::CUSTOMER_CARD_SIMPLE,
                            'oil_com'        => $mainInfo->oil_com,
                            'card_main_id'   => $mainInfo->id,
                            'total_charge'   => $record->totalAssign,
                            'card_remain'    => $record->cardBalance,
                            'point_remain'   => $record->cardIntegral,
                            'vice_tmp_id'    => 1,
                            'org_id'         => $mainInfo->org_id,
                            'org_id_fanli'   => $mainInfo->org_id,
                            'active_time'    => \helper::nowTime(),
                            'creator_id'     => 8888,
                            'refresh_status' => 0,
                            'property'       =>3,//数据托管业务(半托管)
                        ];

                        if (in_array($inData['card_from'], [40, 41])) {
                            $inData['status'] = $record->cardStatus ? $record->cardStatus : '使用';
                        }

//                        $cardInfoLocal = OilCardVice::getByThirdId($record->id);
                        $cardInfoLocal = OilCardVice::getOneByViceNo($record->cardNo);

                        if ($cardInfoLocal) {
                            $inData['id']         = $cardInfoLocal->id;
                            $inData['updatetime'] = \helper::nowTime();
                            $cardInfoLocal->update($inData); //是否要编辑还需要确定

                            //修改卡到到gos
                            if (!in_array($inData['card_from'], [40, 41])) {
                                CardViceToGos::batchUpdateToGosByViceIds([$cardInfoLocal->id]);
                            }
                        } else {
                            $inData['createtime'] = \helper::nowTime();
                            $insertRes            = OilCardVice::add($inData); //因为是增量没有多少数据所以单条循环插入
                            //添加卡到gos
                            if (!in_array($inData['card_from'], [40, 41])) {
                                CardViceToGos::batchAddByIdsToGos([$insertRes->id]);
                            }
                        }

//                        \Framework\Log::error(__METHOD__ . "#副卡队列消费", ['result' => $res, 'msg' => $msg], 'subscribe_vice_card');

                        unset($inData, $mainInfo, $insertRes, $cardInfoLocal, $record);

                    } else {
                        \Framework\Log::error(__METHOD__ . "#副卡队列异常", ['msg' => '主卡信息不存在', 'msg' => $msg], 'subscribe_vice_card');
                    }


                }
            } catch (\Exception $e) {
                $content = ["标题" => '副卡队列消费异常', '详情' => $msg, 'exception' => strval($e)];
                \Framework\Log::error(__METHOD__ . "#副卡队列消费异常", ['exception' => strval($e), 'msg' => $msg], 'subscribe_vice_card');

                (new \Framework\DingTalk\DingTalkAlarm())->alarmToGroup("副卡队列消费异常", implode("\n", $content));
                echo strval($e);
            }

//            \Framework\Log::error(__METHOD__ . "#副卡队列消费结果", ['result' => 'ok'], 'subscribe_vice_card');
        });
    }


    public function syncCard()
    {
        $account_map = [
        ];

        foreach ($account_map as $v) {
            print_r($v);

            $accountInfo = OilCustomerCard::where('account_no', $v['account_no'])->first();
            $this->syncOne($accountInfo);
        }
        echo "finish";
    }

    public function syncOne($accountInfo, $max_id = 0)
    {
        //发起代理查询
        $params = [
            'method' => 'huoyunren.gascard.syncsubcard',
            'data'   => [
                'data'   => json_encode([
                    'account'  => $accountInfo->account_no,
                    'cardType' => $accountInfo->oil_com == 1 ? 'zsh' : 'zsy',
                    'lastId'   => $max_id,
                ]),
                'format' => 'json',
            ],
        ];
        Log::error('$params--' . var_export($params, TRUE), [], 'syncAccountInfo');
        $res = dspClient::post($params);
        Log::error('total--' . count($res), [], 'syncAccountInfo');
        Log::error('res--' . var_export($res, TRUE), [], 'syncAccountInfo');

        if ($res) {
            foreach ($res as $cardInfo) {
                $max_id = $cardInfo->id;
                //得到主卡信息
                $main_id = $this->getMainId($cardInfo, $accountInfo->account_no);

                //添加副卡
                try {
                    $this->insertCard($cardInfo, $accountInfo, $main_id);
                } catch (\Exception $e) {
                    //insert into card error
                }
            }

            $this->syncOne($accountInfo, $max_id);
        }
    }

    public function getIdsByTime($params)
    {
        $start_time = isset($params['start_time']) && $params['start_time'] ? strtotime($params['start_time']) : strtotime("-1 day");
        $end_time   = isset($params['end_time']) && $params['end_time'] ? strtotime($params['end_time']) : strtotime(date("Y-m-d") . " 00:00:00");
        //发起代理查询
        $params = [
            'method' => 'huoyunren.gascard.syncNewGasRecord.ID',
            'data'   => [
                'data'   => \json_encode([
                    'beginTransTime' => $start_time,
                    'endTransTime'   => $end_time,
                ]),
                'format' => 'json',
            ],
        ];

        $res = dspClient::post($params);
//        Log::error(__METHOD__, ['代理条数' => count($res)], 'compareTrades');

        if ($res) {
            $diff       = [];
            $_res       = array_chunk($res, 800);
            $existTotal = 0;
            foreach ($_res as $v) {
                $_existRecord = Capsule::connection('online_only_read')
                                       ->table('oil_card_vice_trades')
                                       ->whereIn('api_id', $v)
                                       ->where('fetch_time', '>=', date("Y-m-d H:i:s", $start_time - 60 * 60 * 1))
                                       ->where('fetch_time', '<=', date("Y-m-d H:i:s", $end_time + 60 * 60 * 1))
                                       ->pluck('api_id');

                $_diff = array_diff($v, $_existRecord);
                if ($_diff) {
                    $diff = $diff ? array_merge($diff, $_diff) : $_diff;
                }

                $existTotal += count($_existRecord);

            }
//            Log::error(__METHOD__, ['数据库存在条数' => $existTotal, '差异ID数' => count($diff)], 'compareTrades');

            if ($diff) {
                $result = $this->getTradesByIds($diff);
//                Log::error(__METHOD__, ['type' => '入库结果', 'result' => $result, 'diff' => $diff], 'compareTrades');
            }
        }

        return true;
    }

    public function getTradesByIds($ids)
    {
        $result = [];
        //发起代理查询
        $params = [
            'method' => 'huoyunren.gascard.syncNewGasRecord.list',
            'data'   => [
                'data'   => \json_encode([
                    'IDs' => implode(',', $ids),
                ]),
                'format' => 'json',
            ],
        ];

        $res = dspClient::post($params);

//        Log::error(__METHOD__, ['type' => '按ID同步的消费条数', 'result' => count($res)], 'compareTrades');


        if ($res) {

            $cardMainMap = [];
            $cardViceMap = [];
            foreach ($res as $v) {
                $cardMainMap[$v->parentCard] = $v->parentCard;
                $cardViceMap[$v->cardNo]     = $v->cardNo;
            }
            // 主卡信息
            $mainCardInfo = Capsule::connection('online_only_read')->table('oil_card_main')->whereIn('main_no', array_values($cardMainMap))->pluck('main_no');
            // 副卡信息
            $viceCardInfo = Capsule::connection('online_only_read')->table('oil_card_vice')->whereIn('vice_no', array_values($cardViceMap))->pluck('vice_no');

//            Log::error(__METHOD__, ['cardMainMap' => $cardMainMap, 'cardViceMap' => $cardViceMap], 'compareTrades');
//            Log::error(__METHOD__, ['main' => $mainCardInfo, 'vice' => $viceCardInfo], 'compareTrades');


            $_res = [];
            foreach ($res as &$v) {
                // 判断主副卡是否不存在的异常交易
//                $cardMain = OilCardMain::getByMainNo($v->parentCard);
                if (in_array($v->parentCard, $mainCardInfo)) {
//                    $viceInfo = OilCardVice::getByViceNo(['vice_no' => $v->cardNo]);
                    if (!in_array($v->cardNo, $viceCardInfo)) {
                        $v->reason = '子卡不存在';
//                        Log::error(__METHOD__, ['type' => $v->cardNo . '-子卡不存在', 'id' => $v->id], 'compareTrades');

                        OilCardViceTradesError::add((array)$v);
                    } else {
                        $v->createAt = $v->dateCreated / 1000;
                        $v->fuelDate = $v->fueldate;
                        $v->exType   = $v->extype;
                        unset($v->extype, $v->fueldate);

                        $_res[] = $v;
                    }
                } else {
                    $v->reason = '主卡不存在';
//                    Log::error(__METHOD__, ['type' => $v->parentCard . '-主卡不存在', 'result' => $v->id], 'compareTrades');
                    OilCardViceTradesError::add((array)$v);
                }

            }

//            Log::error(__METHOD__, ['type' => '可补入库条数', 'total' => count($_res)], 'compareTrades');
//            Log::error(__METHOD__, ['type' => '可补入库条数', 'total' => $_res], 'compareTrades');

            $result = SyncGasRecord::syncNewGasRecord($_res, true);
        }

        return $result;
    }

    /**
     * @param $params
     * @return array
     */
    public function sendAccountAlarmToCustomer($params)
    {
        \Framework\Log::error(__METHOD__, [$params], 'sendAccountAlarmToCustomer');

        $email_content_list = [];
        $type_text          = [
            'add'    => '主卡新增',
            'remove' => '主卡移除',
            'empty'  => '没有主卡'
        ];
        if (isset($params->list) && $params->list) {
            $account_list = [];
            foreach ($params->list as $v) {
                $account_list[] = $v->account_no;
            }
            $accountInfo = OilCustomerCard::getByAccountNoList($account_list);
            \Framework\Log::error(__METHOD__ . 'accountInfo', [$email_content_list], 'sendAccountAlarmToCustomer');

            $account_map = [];
            if ($accountInfo) {
                foreach ($accountInfo as $v) {
                    $account_map[$v->account_no] = $v;
                }

                foreach ($params->list as $v) {
                    if (!isset($account_map[$v->account_no]) || !$account_map[$v->account_no]) {
                        continue;
                    }

                    $account = $account_map[$v->account_no];
                    OilCustomerCard::edit([
                        'id'          => $account->id,
                        'status'      => 2,
                        'sync_status' => '账户异常',

                    ]);

                    $oil_com_info = OilCom::getById($account->oil_com);
                    $oil_com_text = $oil_com_info['name'];

                    $email_content_list[$account->email] = [];
                    if ($v->result) {
                        $content   = [];
                        $content[] = "油卡托管客户，你好：<br /><br />";
                        foreach ($v->result as $item) {
                            $info  = "油卡托管客户，你好：<br /><br />";
                            $info  .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;你托管的 ' . $oil_com_text . ' ';
                            $title = $v->account_no . '账户' . $type_text[$item->type];
                            $info  .= " <span style=\"color:red;\">" . $v->account_no . '</span> 账号，出现<span style="color:red;">' . $type_text[$item->type] . "</span>";
                            $info  .= "问题，";
                            if ($item->cards) {
                                $info  .= $type_text[$item->type] . ' ' . count($item->cards) . ' 张卡片 ';
                                $title .= $type_text[$item->type] . count($item->cards) . '张卡片 ';

                                $info .= "(" . implode(", ", $item->cards) . ")，\n";
                            }
                            $info      .= "请及时登录主站，验证是否存在问题，如果你修改了网厅的账号和密码请同步更新托管账号和密码，以防出现数据抓取延迟的问题。";
                            $content[] = $info;

                            $email_content_list[$account->email][] = implode("", $content);
                            $email_content_list[$account->email]   = [
                                'title'   => $title,
                                'email'   => $account_map[$v->account_no]->email,
                                'content' => implode("\n", $content)
                            ];
                            $this->sendNoticeEmail($info, $account_map[$v->account_no]->email);
                            try {
                                (new DingTalkAlarm())->alarmToGroup('德邦托管账户探测', $info);
                            } catch (\Exception $e) {

                            }
//                            MailSender::sendNow($title, $info, [$account_map[$v->account_no]->email], true, false, [], false);
                        }
                    }
                }
                \Framework\Log::error(__METHOD__, [$email_content_list], 'sendAccountAlarmToCustomer');

//                if ($email_content_list) {
//                    foreach ($email_content_list as $k => $v) {
//                        MailSender::sendNow('油卡账户异动提醒', implode("<br />", $v), [$k], true, false, [], false);
//                    }
//                }
            }
        }

        return $email_content_list;
    }

    public function compareChargeByTime($params)
    {
        $start_time = isset($params['start_time']) && $params['start_time'] ? strtotime($params['start_time']) : strtotime("-1 day");
        $end_time   = isset($params['end_time']) && $params['end_time'] ? strtotime($params['end_time']) : strtotime(date("Y-m-d") . " 00:00:00");
        //发起代理查询
        $params = [
            'method' => 'huoyunren.gascard.syncchargedetail.ID',
            'data'   => [
                'data'   => \json_encode([
                    'beginTransTime' => $start_time,
                    'endTransTime'   => $end_time,
                ]),
                'format' => 'json',
            ],
        ];

        $res = dspClient::post($params);
        Log::error(__METHOD__, ['代理条数' => count($res)], 'compareRecharge');

        if ($res) {
            $diff       = [];
            $_res       = array_chunk($res, 800);
            $existTotal = 0;
            foreach ($_res as $v) {
                $_existRecord = Capsule::connection('online_only_read')
                                       ->table('oil_card_main_recharge')
                                       ->whereIn('api_id', $v)
                                       ->where('fetch_time', '>=', date("Y-m-d H:i:s", $start_time - 60 * 60 * 1))
                                       ->where('fetch_time', '<=', date("Y-m-d H:i:s", $end_time + 60 * 60 * 1))
                                       ->pluck('api_id');

                $_diff = array_diff($v, $_existRecord);
                if ($_diff) {
                    $diff = $diff ? array_merge($diff, $_diff) : $_diff;
                }

                $existTotal += count($_existRecord);

            }
            Log::error(__METHOD__, ['数据库存在条数' => $existTotal, '差异ID数' => count($diff)], 'compareRecharge');

            if ($diff) {
//                $result = $this->getTradesByIds($diff);
                (new DingTalkAlarm())->alarmToGroup($start_time . '托管卡充值同步出现差异', '差异条数：' . count($diff));
//                Log::error(__METHOD__, ['type' => '入库结果', 'result' => $result, 'diff' => $diff], 'compareRecharge');
                Log::error(__METHOD__, ['type' => '充值差异', 'result' => $diff, 'count' => count($diff)], 'compareRecharge');
            }
        }

        return true;
    }

    public function compareAssignByTime($params)
    {
        $start_time = isset($params['start_time']) && $params['start_time'] ? strtotime($params['start_time']) : strtotime("-1 day");
        $end_time   = isset($params['end_time']) && $params['end_time'] ? strtotime($params['end_time']) : strtotime(date("Y-m-d") . " 00:00:00");
        //发起代理查询
        $params = [
            'method' => 'huoyunren.gascard.syncassigndata.ID',
            'data'   => [
                'data'   => \json_encode([
                    'beginTransTime' => $start_time,
                    'endTransTime'   => $end_time,
                ]),
                'format' => 'json',
            ],
        ];

        $res = dspClient::post($params);

        Log::error(__METHOD__, ['代理条数' => count($res)], 'compareAssign');

        if ($res) {
            $diff       = [];
            $_res       = array_chunk($res, 800);
            $existTotal = 0;
            foreach ($_res as $v) {
                $_existRecord = Capsule::connection('online_only_read')
                                       ->table('oil_vice_assign')
                                       ->whereIn('api_id', $v)
                                       ->where('fetch_time', '>=', date("Y-m-d H:i:s", $start_time - 60 * 60 * 1))
                                       ->where('fetch_time', '<=', date("Y-m-d H:i:s", $end_time + 60 * 60 * 1))
                                       ->pluck('api_id');

                $_diff = array_diff($v, $_existRecord);
                if ($_diff) {
                    $diff = $diff ? array_merge($diff, $_diff) : $_diff;
                }

                $existTotal += count($_existRecord);

            }
            Log::error(__METHOD__, ['数据库存在条数' => $existTotal, '差异ID数' => count($diff)], 'compareAssign');

            if ($diff) {
//                $result = $this->getTradesByIds($diff);
//                Log::error(__METHOD__, ['type' => '入库结果', 'result' => $result, 'diff' => $diff], 'compareRecharge');
                Log::error(__METHOD__, ['type' => '分配差异', 'result' => $diff, 'count' => count($diff)], 'compareAssign');
                (new DingTalkAlarm())->alarmToGroup($start_time . '托管卡分配同步出现差异', '差异条数：' . count($diff));

            }
        }

        return true;
    }

}