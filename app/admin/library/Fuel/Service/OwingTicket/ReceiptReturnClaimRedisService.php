<?php


namespace Fuel\Service\OwingTicket;


use Framework\RedisInstance;

class ReceiptReturnClaimRedisService
{
    private $redis;

    private $claimKey = "receipt_return_claim_list:claim";
    private $rollKey = "receipt_return_claim_list:roll";

    public function __construct()
    {
        $this->redis = RedisInstance::getInstance();
    }

    /**
     * 批量认领缓存
     * @param $receiptIds
     * @return mixed
     */
    public function addReceiptClaimIds($receiptIds)
    {
        foreach ($receiptIds as $item)
        {
            $this->redis->sAdd($this->claimKey,$item);
        }

    }
    public function selectReceiptClaimIds()
    {
        return $this->redis->sMembers($this->claimKey);
    }

    public function deleteReceiptClaimIds($receiptIds)
    {
        foreach ($receiptIds as $item) {
            $this->redis->sRem($this->claimKey, $item);
        }
    }

    /**
     * 批量撤销缓存
     */
    public function addReceiptRollIds($batchNo)
    {
        return $this->redis->sAdd($this->rollKey,$batchNo);
    }

    public function selectReceiptRollIds()
    {
        return $this->redis->sMembers($this->rollKey);
    }

    public function deleteReceiptRollIds($batchNo)
    {
        return $this->redis->sRem($this->rollKey,$batchNo);
    }
}