<?php
/**
 * Created by PhpStorm.
 * User: love
 * Date: 17-5-26
 * Time: 下午3:48
 */


namespace Fuel\Service;

use Framework\GosTaskInterface;
use Framework\Log;
use GosSDK\Gos;
use Models\OilOrg;
use \GosSDK\Defines\Methods\Org as OrgMethod;

class OrgToGos implements GosTaskInterface
{

    /**修改gos
     * @title 添加至Gos
     * <AUTHOR>
     * @param string $id
     */
    static public function addToGos($id){
        return true;
        GosJobFactory::instance()
            ->setTaskName('机构添加')
            ->pushTask(function () use ($id) {
                Log::info('$id--' . var_export($id, TRUE), [], 'addToGos');
                $data   = OilOrg::where('id', $id)->first()->toArray();

                Log::info('$data--' . var_export($data, TRUE), [], 'addToGos');
                $result = (new Gos())
                    ->setMethod(OrgMethod::CREATE)
                    ->setParams($data)
                    ->async();

                Log::info('pushQueue-update', [$result], 'addToGos');
            })
            ->channel('addToGos')
            ->exec();
    }

    /**
     * @title 修改gos
     * <AUTHOR>
     * @param $id
     */
    static public function updateToGos($id)
    {
        return true;
        GosJobFactory::instance()
            ->setTaskName('机构修改')
            ->pushTask(function () use ($id) {
                $data   = OilOrg::where('id', $id)->first()->toArray();

                Log::info('$data--' . var_export($data, TRUE), [], 'pushQueue');
                $result = (new Gos())
                    ->setMethod(OrgMethod::UPDATE)
                    ->setParams($data)
                    ->async();

                Log::info('pushQueue-update', [$result], 'pushQueue');
            })
            ->channel('updateToGos')
            ->exec();
    }

    /**
     * @title   删除gos
     * @desc
     * @version
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     * @param $params
     */
    static public function deleteToGos(array $params)
    {
        return true;
        GosJobFactory::instance()
            ->setTaskName('机构删除')
            ->pushTask(function () use ($params) {
                Log::info('机构删除--' . var_export($params, TRUE), [], 'pushQueue');

                $orgInfo = OilOrg::getById($params);
                if(!$orgInfo){
                    throw new \RuntimeException('机构不存在', 2);
                }
                $orgInfo = $orgInfo->toArray();

                $result = (new Gos())
                    ->setMethod(OrgMethod::DELETE)
                    ->setParams($orgInfo)
                    ->async();

                Log::info('pushQueue-update', [$result], 'pushQueue');
            })
            ->channel('deleteToGos')
            ->exec();
    }

    /**
     * @title   批量添加gos
     * @desc
     * @version
     * <AUTHOR>
     * @since
     * @params  type filedName required?
     */
    static public function batchAddToGos()
    {
        $pageSize = 400;
        for($page=1; $page < 100000; $page++){
            $data = OilOrg::orderBy('createtime', 'asc')
                ->orderBy('id', 'asc')
                ->where('orgcode','200I1A0M')
                ->where('is_del' ,'=','0')
                ->offset(($page- 1) * $pageSize)
                ->limit($pageSize)
                ->get()->toArray();

            if (!$data) {
                echo 'no data';
                break;
            }
            (new Gos())
                ->setMethod(OrgMethod::CREATE_BATCH)
                ->setParams($data)
                ->sync();
        }
    }

    ////
    static public function init()
    {
        $pageSize = 400;
        for($page=1; $page < 100000; $page++){
            $data = OilOrg::orderBy('createtime', 'asc')
                ->orderBy('id', 'asc')
                //->where('orgcode','200I1A0M')
                ->where('is_del' ,'=','0')
                ->offset(($page- 1) * $pageSize)
                ->limit($pageSize)
                ->get()
                ->toArray();

            if (!$data) {
                echo 'no data';
                break;
            }
            (new Gos())
                ->setMethod(OrgMethod::UPDATE_BATCH)
                ->setParams($data)
                ->sync();
        }
    }

    static public function getData(array $ids)
    {
        $result = [];
        $data = OilOrg::getByIds($ids);
        if($data){
            $result = $data->toArray();
        }

        return $result;
    }

    static public function formatData(array $data)
    {
        // TODO: Implement formatData() method.
    }

    static public function formatUniqueData($data)
    {
        // TODO: Implement formatUniqueData() method.
    }

    static public function sendBatchCreateTask(array $ids, $type = 'async')
    {
        $data   = self::getData($ids);
        $result = (new GosTask())
            ->setTaskName('油品机构新增')
            ->setIds($ids)
            ->setMethod(OrgMethod::CREATE_BATCH)
            ->setData($data)
            ->exec($type);

        return $result;
    }

    static public function sendBatchUpdateTask(array $ids, $type = 'async')
    {
        $data   = self::getData($ids);

        //Log::error('sendBatchUpdateTask:'.\var_export($data, true),[],'orgToGos');
        $result = (new GosTask())
            ->setTaskName('油品机构更新')
            ->setIds($ids)
            ->setMethod(OrgMethod::UPDATE_BATCH)
            ->setData($data)
            ->exec($type);

        return $result;
    }

    static public function sendBatchDeleteTask(array $ids, $type = 'async')
    {
        $result = (new GosTask())
            ->setTaskName('油品机构删除')
            ->setIds($ids)
            ->setMethod(OrgMethod::DELETE)
            ->setData(['id'=>$ids])
            ->exec($type);

        return $result;
    }
}