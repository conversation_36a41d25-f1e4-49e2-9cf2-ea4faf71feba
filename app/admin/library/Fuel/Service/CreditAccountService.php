<?php
/**
* 授信账户相关 CreditAccountService.php
* $Author: 刘培俊 (liu<PERSON><PERSON><PERSON>@g7.com.cn) $
* $Date: 2021/8/21 09:36 $
* CreateBy Phpstorm
*/

namespace Fuel\Service;

use Framework\Log;
use Models\OilAccountAssignDetails;
use Models\OilAccountMoneyCharge;
use Models\OilCreditAccount;
use Models\OilCreditBill;
use Models\OilOrg;
use Models\OilSupplierAccount;
use Models\OilSupplierAccountSnapshot;
use Models\OilSupplierAccountStatement;
use Illuminate\Database\Capsule\Manager as Capsule;

class CreditAccountService
{
    /**
     * 查询授信账户可用余额
     * @param $accountNo
     * @param array $accountInfo
     * @return float
     */
    public static function getAccountUsableBalance($accountNo, $accountInfo=[])
    {
        if (empty($accountInfo)) {
            $accountInfo = OilCreditAccount::getByAccountNo($accountNo);
        }
        //查询账户金额
        $time9 = _getTime();
        Log::error("开始查询分配冻结金额：", [], 'getOrgAccountBalance');
        $assign_money = OilAccountAssignDetails::getCreditUsedAssign(['org_id' => $accountInfo['org_id'], 'account_no' => $accountInfo['account_no']]);
        Log::error("完成查询分配冻结金额：", [_getTime() - $time9], 'getOrgAccountBalance');

        //获取账户可用余额
        $usable_money = bcsub(bcsub($accountInfo['credit_total'], $accountInfo['used_total'], 2), $assign_money, 2);
        Log::error('$usable_money:' . $usable_money, [], 'getOrgAccountBalance');
        Log::error('$strif:' . $accountInfo['credit_total'] . '- (' . $accountInfo['used_total'] . ' + '.$assign_money.')', [], 'getOrgAccountBalance');

        return ['usable_money'=>round($usable_money, 2), 'frozen_money'=>$assign_money];
    }

    /*
     * 快照
     */
    public static function cronSnapshotAction($params)
    {
        $insertData = [];
//        $params['_export'] = 1;
//        $params['account_no'] = 1019005;
        $list = OilSupplierAccount::getOrginData($params);
        $data_list = $list->toArray();
        $account_ids = array_column($data_list,'id');
        if($list){
            $balance_map = OilSupplierAccountStatement::getLastByDay($account_ids,$params);
            foreach ($data_list as $value){
                //拼装数据
                $value['snapshot_date'] = isset($params['datetime']) && $params['datetime'] ? $params['datetime'] : date('Y-m-d',strtotime('-1 day'));
                $value['createtime'] = \helper::nowTime();
                $value['account_id'] = $value['id'];
                //取明细中的最后一条余额
                $statement_balance = isset($balance_map[$value['id']]) ? $balance_map[$value['id']] : null;
                $statement_balance ? $value['balance'] = $statement_balance : '';
                //todo 查重复
                $insertData[] = $value;
            }
            $_data = array_chunk($insertData, 500);
            foreach ($_data as $k => $v) {
                $batchInsertSql = \helper::batchInsertToSqlStr($v,'oil_supplier_account_snapshot');
                if($batchInsertSql){
                    Capsule::connection()->getPdo()->exec($batchInsertSql);
                }
            }
            unset($_data,$insertData);

            return true;

        }
    }

    /**
     * 根据机构ID获取授信账户ID
     * @param $orgId
     * @return array
     */
    public function getCreditIdsByOrgId($orgId)
    {
        //根据机构找授信账户 兼容一个机构有多个授信账户
        $creditIds = OilCreditAccount::leftJoin('oil_credit_provider', 'oil_credit_provider.id', '=', 'oil_credit_account.credit_provider_id')
            ->where('oil_credit_account.org_id', $orgId)
            ->where('oil_credit_provider.is_own', 1)
            //->where('oil_credit_provider.product_code',CreditProvider::CREDIT_SELF)
            //->where('oil_credit_provider.bill_way',CreditProvider::BILL_WAY_TRADE) //消费出账
            ->where('oil_credit_account.status', 10)//正常
            ->select('oil_credit_account.*')
            ->pluck('id');

        return !$creditIds ? [] : $creditIds->toArray();
    }

    /**
     * 获取授信账户待结账单数据
     * @param $creditIds
     * @param null $bankName
     * @return array
     */
    public function getUnPayCreditBill($creditIds,$bankName = null,$operator_id = 0)
    {
        $where = "a.account_id in (".implode(',',$creditIds).") AND a.`status` IN ( 10, 20 )";

        //G7WALLET-2972【油卡】【运营】圆通保理清账系统支持方案
        if($bankName && $bankName == '上海圆真商业保理有限公司'){
            $where .= " AND a.bill_time >= '2022-06-21 00:00:00'";
        }

        if( !empty($operator_id) ){
            $where .= " and a.org_operator_id = ".$operator_id;
        }

        // 取出待结账单
        $sql = "SELECT
	*
FROM
	(
	SELECT
	    a.id,
	    a.bill_no,
		a.debt_money,
		a.not_repaid_principal,
		a.repaid_total,
		a.bill_time,
		a.debt_money - sum( IF ( b.`status` IN ( 10, 20 ), IFNULL( b.money, 0 ), 0 ) ) true_not_repaid_principal
	FROM
		oil_credit_bill a
		LEFT JOIN oil_repay_bill b ON a.id = b.bill_id
	WHERE
		".$where."
	GROUP BY
		a.id
	) a
WHERE
	true_not_repaid_principal > 0
order by
	bill_time
ASC";

        return Capsule::connection()->select($sql);
    }

    public function creditRepayCheck($pay_no,$arrival_money,$orgInfo)
    {
        //校验pay_no
        if(!$pay_no){
            throw new \RuntimeException('此充值类型关联单号不能为空!', 2);
        }
        $chargeInfo = OilAccountMoneyCharge::getByNo($pay_no);
        if(!$chargeInfo){
            throw new \RuntimeException('关联申请单不存在!', 2);
        }
        //金额比较
        if($arrival_money != abs($chargeInfo->arrival_money)){
            throw new \RuntimeException('充值还款额和关联单号额度不符', 2);
        }
        //机构数校验
        if(substr($orgInfo->orgcode, 0, 6) != substr($chargeInfo->from_orgcode,0,6)){
            throw new \RuntimeException('和关联单号机构不符', 2);
        }
        //账单还款金额比较 //todo

        $creditIds = $this->getCreditIdsByOrgId($orgInfo->id);
        if(!$creditIds){
            throw new \RuntimeException('无授信账户信息！', 2);
        }

        $creditBill = $this->getUnPayCreditBill($creditIds);
        $billIds = array_column($creditBill,'id');

        //加锁账单
        $creditLockSearch = OilCreditBill::getByIdsLock(['ids'=>$billIds]);

        if(!$creditBill){
            throw new \RuntimeException('无授信账单！',2);
        }
        $not_repay_total = array_sum(array_column($creditBill,'true_not_repaid_principal'));
        if($not_repay_total < $arrival_money){
            throw new \RuntimeException('授信账单总金额小于充值还款额！',2);
        }

    }
}