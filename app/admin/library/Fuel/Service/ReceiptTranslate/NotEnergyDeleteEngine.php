<?php
namespace Fuel\Service\ReceiptTranslate;

use Framework\Log;
use Models\OilConfigure;
use Models\OilReceiptTranslateDetail;
use Models\OilTypeCategory;
use Fuel\Defines\ReceiptTranslateDetail;
use Fuel\Service\ReceiptTranslate\Until;
/**
 * 非能源回票剔除引擎
 * Class NotEnergyDeleteEngine
 * @package Fuel\Service\ReceiptTranslate
 */
class NotEnergyDeleteEngine extends Engine
{
    /**
     * 获取哪些顶级分类下的二级油品
     * @var string[]
     */
    protected  $topOil1=['石油制品','矿产品'];

    /**
     * 获取哪些顶级分类做特殊判断
     * @var string[]
     */
    protected  $topOil2=['燃气','有机化学原料','液化气'];
    /**
     * @param array $receipts
     * @return array
     */
    public function handle(array $receipts)
    {
        $translate_not=[];
        if(!empty($receipts)){
            //获取油品分类表中  tag【石油制品】和【矿产品】的二级油品分类
            $top1=OilConfigure::getConfByName(['sys_key'=>$this->topOil1]);
            $top_ids=array_keys($top1);
            //默认模板获取二级油品name
            $secondLevel=OilTypeCategory::getAllSecondLevelOilNameByTemplate(['tag'=>$top_ids]);
            //获取【油品顶级分类】为【燃气】和【有机化学原料】 topOil2
            $arr=array_merge($secondLevel,$this->topOil2);

            Log::error('$arr-oil_info----',[$arr],'Engine----');

            foreach ($receipts as $k=>$item){

                Log::error('$item----',[$item],'Engine----');

                if(isset($item['name'])){
                    $name=Until::regexReplace($item['name']);

                    Log::error('$name----',[$name],'Engine----');

                    $name1=Until::getName1($name);

                    Log::error('$name1----',[$name1],'Engine----');

                    if($name1 == ''){
                        //无*号或者只有一个*号 需退票 退票原因为【货物或应税劳务、服务名称有误】
                        $params['id']=$item['id'];
                        $params['is_return']=ReceiptTranslateDetail::IS_RETURN_NEED;
                        $params['in_status']=ReceiptTranslateDetail::IN_STATUS_NOT;
                        $params['select_status']=ReceiptTranslateDetail::SECOND_OIL_SELECT_NO_NEED;
                        $params['translate_status']=ReceiptTranslateDetail::TRANSLATE_STATUS_NOT;
                        $params['return_reason']='货物或应税劳务、服务名称有误';
                        $params['progress']=ReceiptTranslateDetail::PROGRESS_ENERGY;
                        $params['creator']='系统自动';
                        $params['last_operator']='系统自动';
                        unset($receipts[$k]);
                        $translate_not[$item['id']]=$params;
                    }else{
                        if(!in_array($name1,$arr)){
                            //不属于2种情况 进入【无需翻译】状态
                            $params['id']=$item['id'];
                            $params['is_return']=ReceiptTranslateDetail::IS_RETURN_NO_NEED;
                            $params['in_status']=ReceiptTranslateDetail::IN_STATUS_NOT;
                            $params['select_status']=ReceiptTranslateDetail::SECOND_OIL_SELECT_NO_NEED;
                            $params['translate_status']=ReceiptTranslateDetail::TRANSLATE_STATUS_NO_NEED;
                            $params['progress']=ReceiptTranslateDetail::PROGRESS_ENERGY;
                            $params['creator']='系统自动';
                            $params['last_operator']='系统自动';
                            unset($receipts[$k]);
                            $translate_not[$item['id']]=$params;
                        }
                    }
                }
            }
            return ['translate_not'=>$translate_not,'translated'=>$receipts];
        }else{
            return ['translate_not'=>$translate_not,'translated'=>[]];
        }
    }
}