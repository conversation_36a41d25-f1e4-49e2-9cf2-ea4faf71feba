<?php
namespace Fuel\Service\ReceiptTranslate;

use Fuel\Defines\MessageMaintenance;
use Models\OilConfigure;
use Models\OilMessageMaintenance;
use Models\OilTypeBase;
use Models\OilTypeCategory;
use Fuel\Defines\ReceiptTranslateDetail;
use Models\OilReceiptTranslateDetail;
class SkuTranslateEngine extends  Engine
{
    /**
     * 自动翻译规则型号处理
     * @param array $data
     * @return array
     */
    public function handle(array $data)
    {
        $translate_not=[];
        if(!empty($data))
        {
            foreach ($data as $k=>$item)
            {
                $in_second_oil=$item['in_second_oil']??'';
                $in_sku=$item['in_sku']??'';
                $top_oil=$item['top_oil']??'';
                $res=$this->handleSkuAndSecondOil($in_second_oil,$in_sku,$top_oil);
                if($res['code'] != 1){
                    $params['id']=$item['id'];
                    $params['in_status']=ReceiptTranslateDetail::IN_STATUS_NOT;
                    $params['progress']=ReceiptTranslateDetail::PROGRESS_SKU;
                    $params['creator']='系统自动';
                    $params['last_operator']='系统自动';
                    if($res['in_sku'] != ''){
                        $params['translate_status']=ReceiptTranslateDetail::TRANSLATE_STATUS_PART;
                    }else{
                        $params['translate_status']=ReceiptTranslateDetail::TRANSLATE_STATUS_NOT;
                    }
                    if($res['code'] == 0)
                    {
                        $params['in_sku']=$res['in_sku'];
                        $params['is_return']=ReceiptTranslateDetail::IS_RETURN_NEED;
                        $params['return_reason']=isset($res['return_reason'])?$res['return_reason']:'';
                        $params['select_status']=ReceiptTranslateDetail::SECOND_OIL_SELECT_NO_NEED;
                    }elseif($res['code'] == 2)
                    {
                        //部分翻译
                        $params['in_sku']=$res['in_sku'];
                        $params['select_status']=ReceiptTranslateDetail::SECOND_OIL_SELECT_NOT;
                        $params['is_return']=ReceiptTranslateDetail::IS_RETURN_NO_NEED;
                    }elseif($res['code'] == 3)
                    {
                        //部分翻译
                        $params['in_sku']=$res['in_sku'];
                        $params['select_status']=ReceiptTranslateDetail::SECOND_OIL_UNKNOWN;
                        $params['is_return']=ReceiptTranslateDetail::IS_RETURN_NO_NEED;
                    }
                    unset($data[$k]);
                    $translate_not[$item['id']]=$params;
                }else{
                    $data[$k]['in_sku']=$res['in_sku'];
                    $data[$k]['in_second_oil']=$res['in_second_oil'];
                    $data[$k]['in_second_oil_id']=$res['in_second_oil_id'];
                }
            }
            return ['translate_not'=>$translate_not,'translated'=>$data];
        }
        return ['translate_not'=>$translate_not,'translated'=>[]];
    }
    /**
     * 规格型号翻译
     * @param $second_oil：二级油品
     * @param $sku 规则文字
     * @param $top_oil
     * @return array
     * code 0 翻译失败  1 翻译完成  2 未选择需要人工选择
     */
    public function handleSkuAndSecondOil($second_oil,$sku,$top_oil)
    {
        if($second_oil != ''){
            //二级油品已知
            $levelTwo=OilTypeCategory::getInfoByName($second_oil);
            $params=[
                'in_level1_id'=>$levelTwo->pid,
                'in_level2_id'=>$levelTwo->oil_id,
                'source'=>MessageMaintenance::SOURCE_RECEIPT_DETAIL,//回票明细
            ];
            if($sku != ''){
                //规则型号已知 进入油品信息维护表
                $res=OilMessageMaintenance::getInfoByFilter(['in_sku'=>$sku,'in_level2_id'=>$levelTwo->oil_id]);
                if(!$res){
                    $params['in_sku']=$sku;
                    $params['creator']='系统自动';
                    $params['last_operator']='系统自动';
                    $params['review_status']=MessageMaintenance::REVIEW_STATUS_PASS;
                    OilMessageMaintenance::add($params);
                }
                return ['in_second_oil'=>$second_oil,'in_second_oil_id'=>$levelTwo->oil_id,'in_sku'=>$sku,'code'=>1];
            }else{
                //则认为规格型号=二级油品。对应回票明细条目标记【已入库】，翻译完成
                return ['in_second_oil'=>$second_oil,'in_second_oil_id'=>$levelTwo->oil_id,'in_sku'=>$second_oil,'code'=>1];
            }
        }else{
            if($sku != ''){
                if($top_oil != ''){
                    //燃气顶级油品特殊处理
                    $top=OilConfigure::getSysInfo(['sys_key'=>$top_oil]);
                    if($top){
                        //获取燃气下对应的二级油品
                        $second_oil=OilTypeCategory::getLevel2OilByFilter($top->id);
                        $num=count($second_oil);
                        if($num > 1){
                            //标记为未选择 进行人工选择
                            return ['in_second_oil'=>'','in_second_oil_id'=>'','in_sku'=>$sku,'code'=>2];
                        }elseif($num == 1){
                            return ['in_second_oil'=>$second_oil[0]['name'],'in_second_oil_id'=>$second_oil[0]['id'],'in_sku'=>$sku,'code'=>1];
                        }else{
                            return ['in_second_oil'=>'','in_second_oil_id'=>'','in_sku'=>$sku,'return_reason'=>'二级油品未翻译完成','code'=>0];
                        }
                    }else{
                        return ['in_second_oil'=>'','in_second_oil_id'=>'','in_sku'=>$sku,'return_reason'=>'二级油品未翻译完成','code'=>0];
                    }
                }else{
                    //根据规格型号获取油品名称是否已经配置二级油品
                    $message=OilMessageMaintenance::getAllBySameSku($sku);
                    if(!count($message)){
                        //油品信息维护表中没有配置对应的sku的二级油品
                        $notReview=OilMessageMaintenance::getFirstNotReviewBySameSku($sku);
                        if(!$notReview){
                            //不存在  未审核的  则新增
                            $params=[
                                'in_sku'=>$sku,
                                'creator'=>'系统自动',
                                'last_operator'=>'系统自动',
                                'source'=>MessageMaintenance::SOURCE_RECEIPT_DETAIL,//回票明细
                            ];
                            OilMessageMaintenance::add($params);
                        }
                        return ['in_second_oil'=>'','in_second_oil_id'=>'','in_sku'=>$sku,'return_reason'=>'','code'=>3];
                    }else{
                        //【油品名称】：【2级分类】比例关系
                        $second_oil=array_filter(array_column($message,'in_level2_id'));
                        $second_oil_num=count($second_oil);
                        if($second_oil_num > 1){
                            //标记为未选择 进行人工选择
                            return ['in_second_oil'=>'','in_second_oil_id'=>'','in_sku'=>$sku,'code'=>2];
                        }elseif($second_oil_num == 1){
                            //【油品名称】：【2级分类】=1：1
                            $oil=OilTypeBase::getById(['id'=>array_shift($second_oil)]);
                            return ['in_second_oil'=>$oil->name,'in_second_oil_id'=>$oil->id,'in_sku'=>$sku,'code'=>1];
                        }else{
                            return ['in_second_oil'=>'','in_second_oil_id'=>'','in_sku'=>$sku,'return_reason'=>'','code'=>3];
                        }
                    }
                }
            }else{
                //翻译失败，是否需要退票为【需退票】，退票原因为【规格型号和2级油品无法翻译】。线下退票
                return ['in_second_oil'=>'','in_second_oil_id'=>'','in_sku'=>'','return_reason'=>'规格型号和2级油品无法翻译','code'=>0];
            }
        }
    }

    /**
     * 手动翻译-进项明细再次翻译
     * @param $item
     * @return array
     */
    public function handlerEdit($item){
        if(isset($item['in_second_oil_id']) && $item['in_second_oil_id'] != ''){
            $secondOil=OilTypeBase::getById(['id'=>$item['in_second_oil_id']]);
            if($secondOil){
                $secondOilName=$secondOil->name;
            }else{
                $secondOilName='';
            }
        }else{
            $secondOilName='';
        }
        $res=$this->handleSkuAndSecondOil($secondOilName,$item['in_sku'],'');
        $info['last_operator']='系统自动';
        $info['id']=$item['id'];
        if($res['code'] != 1){
            $info['in_status']=ReceiptTranslateDetail::IN_STATUS_NOT;
            if($res['code'] == 0)
            {
                $info['is_return']=ReceiptTranslateDetail::IS_RETURN_NEED;
                $reason=isset($res['return_reason'])?$res['return_reason']:'';
                $info['return_reason']=$item['return_reason'] != ''?$item['return_reason'].';'.$reason:$reason;
                $info['select_status']=ReceiptTranslateDetail::SECOND_OIL_SELECT_NO_NEED;
                $info['translate_status']=ReceiptTranslateDetail::TRANSLATE_STATUS_NOT;
                $info['progress']=ReceiptTranslateDetail::PROGRESS_SKU;
            }elseif($res['code'] == 2)
            {
                //部分翻译
                $info['in_sku']=$res['in_sku'];
                $info['select_status']=ReceiptTranslateDetail::SECOND_OIL_SELECT_NOT;
                $info['translate_status']=ReceiptTranslateDetail::TRANSLATE_STATUS_PART;
                $info['progress']=ReceiptTranslateDetail::PROGRESS_SKU;
            }elseif($res['code'] == 3)
            {
                //部分翻译
                $info['in_sku']=$res['in_sku'];
                $info['select_status']=ReceiptTranslateDetail::SECOND_OIL_UNKNOWN;
                $info['is_return']=ReceiptTranslateDetail::IS_RETURN_NO_NEED;
                $info['progress']=ReceiptTranslateDetail::PROGRESS_SKU;
            }
        }else{
            $info['in_sku']=$res['in_sku'];
            $info['in_second_oil_id']=$res['in_second_oil_id'];
            $info['select_status']=ReceiptTranslateDetail::SECOND_OIL_SELECT_NO_NEED;
            $info['progress']=ReceiptTranslateDetail::PROGRESS_SKU;
        }
        OilReceiptTranslateDetail::edit($info);
        return ['code'=>$res['code'],'info'=>$info];

    }
}