<?php
namespace Fuel\Service\ReceiptTranslate;

use Fuel\Defines\ReceiptTranslateDetail;
use Fuel\Service\InvoiceStockService;
use Models\OilReceiptReturnDetail;
use Models\OilReceiptTranslateDetail;
use Framework\Log;
use Illuminate\Database\Capsule\Manager as Capsule;
class ReceiptTranslateService
{
    /**
     * 回票翻译
     * @param array $receipts
     * @return array
     */
    public function autoTranslate(){
        //获取已审核的回票提取字段name和sku //['ids'=>[32357]]  ['ids'=>[32348]] ['ids'=>[32361]]
        $receipts=OilReceiptReturnDetail::getReviewedOilReceipt(['ids'=>[100]]);
        //折扣行翻译
        $discountReceipts=[];
        //非折扣行翻译
        $noDiscountReceipts=[];
        //回票翻译数据过滤
        if(!empty($receipts)){
            $receiptTranslateIds=OilReceiptTranslateDetail::getFilterByPluckFiled(['in_status'=>ReceiptTranslateDetail::IN_STATUS_OVER],'receipt_return_detail_id');
            foreach ($receipts as $k=>$item){
                if(in_array($item['return_detail_id'],$receiptTranslateIds)){
                    //已入库的不能再进行翻译
                    unset($receipts[$k]);
                    continue;
                }
                //折扣行翻译 跳过单位翻译
                if($item['money'] < 0 && $item['unit'] == false){
                    $discountReceipts[]=$item;
                }else{
                    $noDiscountReceipts[]=$item;
                }
            }
        }
        if(!empty($receipts)){
            $receipts=  $this->customTranslate($noDiscountReceipts);
        }
        if(!empty($discountReceipts)){
            $discountReceipts= $this->customTranslate($discountReceipts,false);
        }
        return array_merge($receipts,$discountReceipts);
    }



    public function customTranslate(array $receipts,$unitTranslate=true){
        $insert_translateData=[];
        if(!empty($receipts)){
            try {
                //非能源引擎剔除
                $energyEngine= EngineFactory::createEngine('notEnergy_delete');
                $res_energyEngine=$energyEngine->handle($receipts);
                $res_energyEngine_not=$res_energyEngine['translate_not'];
                //回票翻译
                $receiptTranslateEngine= EngineFactory::createEngine('receipt_translate');
                $res_receiptTranslateEngine=$receiptTranslateEngine->handle($res_energyEngine['translated']);
                $res_receiptTranslateEngine_not=$res_receiptTranslateEngine['translate_not'];
                //sku翻译
                $skuTranslateEngine= EngineFactory::createEngine('sku_translate');
                $res_skuTranslateEngine=$skuTranslateEngine->handle($res_receiptTranslateEngine['translated']);
                $res_skuTranslateEngine_not=$res_skuTranslateEngine['translate_not'];
                if($unitTranslate){
                    //单位翻译
                    $unitTranslateEngine= EngineFactory::createEngine('unit_translate');
                    $res_unitTranslateEngine=$unitTranslateEngine->handle($res_skuTranslateEngine['translated']);
                    $res_unitTranslateEngine_not=$res_unitTranslateEngine['translate_not'];
                }else{
                    $res_unitTranslateEngine_not=[];
                }
                //tax翻译
                $taxTranslateData=isset($res_unitTranslateEngine)?$res_unitTranslateEngine['translated']:$res_skuTranslateEngine['translated'];
                $taxTranslateEngine= EngineFactory::createEngine('tax_translate');
                $res_taxTranslateEngine=$taxTranslateEngine->handle($taxTranslateData);
                $res_taxTranslateEngine_not=$res_taxTranslateEngine['translate_not'];
                //翻译完成存入翻译明细
                $translate_result=array_merge($res_energyEngine_not,$res_receiptTranslateEngine_not,$res_skuTranslateEngine_not,$res_unitTranslateEngine_not,$res_taxTranslateEngine_not,$res_taxTranslateEngine['translated']);
                if(!empty($translate_result)) {
                    foreach ($receipts as $receipt) {
                        foreach ($translate_result as $key => $item) {
                            if ($item['id'] == $receipt['id']) {
                                $params = [
                                    'receipt_return_detail_id' => $receipt['return_detail_id'],
                                    'receipt_return_id' => $receipt['return_id'],
                                    'receipt_no' => $receipt['receipt_no'],
                                    'receipt_code' => $receipt['receipt_code'],
                                    'in_second_oil_id' => $item['in_second_oil_id'] ?? null,
                                    'in_sku' => $item['in_sku'] ?? null,
                                    'in_unit' => $item['in_unit'] ?? null,
                                    'in_num' => $item['in_num'] ?? null,
                                    'select_status' => $item['select_status'] ?? ReceiptTranslateDetail::SECOND_OIL_SELECT_NO_NEED,
                                    'translate_status' => $item['translate_status'] ?? 0,
                                    'is_return' => $item['is_return'] ?? 0,
                                    'in_status' => $item['in_status'] ?? ReceiptTranslateDetail::IN_STATUS_NOT,
                                    'in_mode' => $item['in_mode'] ?? 0,
                                    'return_reason' => $item['return_reason'] ?? '',
                                    'progress' => $item['return_reason'] ?? ReceiptTranslateDetail::PROGRESS_ENERGY,
                                    'creator' => $item['creator']??'系统自动',
                                    'last_operator' => $item['last_operator']??'系统自动',
                                    'createtime' => \helper::nowTime(),
                                    'updatetime' => \helper::nowTime(),
                                    'money'        => $receipt['money'],
                                    'tax_money'    => $receipt['tax'],
                                    'money_tax'    => bcadd($receipt['money'],$receipt['tax'],2),
                                    'origin_name'  => $receipt['name'],//原来的货物名称
                                ];
                                $insert_translateData[$key] = $params;
                            }
                        }
                    }
                    if (!empty($insert_translateData)) {
                        OilReceiptTranslateDetail::batchAdd($insert_translateData);
                    }
                }
            }catch (\Exception $e){
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }
        }
        return $insert_translateData;
    }

    /**
     * 获取进项票明细列表
     * @param array $params
     * @return array
     */
    public function getList(array  $params){
        $data=OilReceiptTranslateDetail::getList($params);

        return $this->handleData($data);
     }

     public function handleData($data){
         foreach ($data as $k=>$item){
             $item->translate_status_val=ReceiptTranslateDetail::getTranslateStatusById($item->translate_status);
             $item->is_return_val=ReceiptTranslateDetail::getIsReturnStatusById($item->is_return);
             $item->in_status_val=ReceiptTranslateDetail::getInStatusById($item->in_status);
             $item->in_mode_val=ReceiptTranslateDetail::getInModeStatusById($item->in_mode);
             $item->select_status_val=ReceiptTranslateDetail::getSecondOilSelectStatusById($item->select_status);
             $item->num=$item->num?sprintf("%.6f", $item->num):null;
             $item->in_num=$item->in_num?sprintf("%.6f", $item->in_num):null;
             $item->tax=$item->tax?$item->tax.'%':null;
             $item->tax_money=$item->tax_money?$item->tax_money:null;
             $item->transform_val=ReceiptTranslateDetail::getTransformById($item->transform);
         }
         return $data;
     }
    /**
     * 手动翻译-规格型号已知、二级油品未知
     * @param array $params
     * @return array
     */
     public function manualTranslate(array  $params){
         //规格型号已知 油品未知的进入规格翻译
         //同步测试
//         (new \Jobs\ReceiptManualTranslate($receipts))->handle();
         if(!empty($params['ids'])){
             $task = (new \Jobs\ReceiptManualTranslate($params['ids']))
                 ->setTaskName('进项票明细手动翻译')
                 ->onQueue('receiptAutoTranslate')
                 ->setTries(3)
                 ->dispatch();
             Log::error(__METHOD__, [$task], "receiptAutoTranslate");
         }
     }

    /**
     * 选择同类油品
     * @return mixed
     */
     public function getSameSkuList(){
         return OilReceiptTranslateDetail::getListGroupBySku();
     }

    /**
     * 手动入库
     * @param array $data
     */
     public function manualInStock(array $data){
         //校验入库 【入库规格型号】、【入库2级油品】、【入库数量】、【入库数量】均有值，且入库状态是【未入库】
         Capsule::connection()->beginTransaction();
         try {
             foreach ($data as $id){
                 $detail=OilReceiptTranslateDetail::getById(['id'=>$id]);
                 if(!$detail->in_sku || !$detail->in_second_oil_id || !$detail->in_unit || !$detail->in_num || $detail->in_status == ReceiptTranslateDetail::IN_STATUS_NOT ){
                     throw new \RuntimeException('明细不能入库', 2);
                 }
                 (new  InvoiceStockService)->insertStock(false,[
                     'oil_type_id'  => $detail->in_second_oil_id,
                     'classify'     => 10,
                     'res_id'       => $detail->receipt_return_detail_id,
                     'res_type'     => 10,
                 ]);
                 OilReceiptTranslateDetail::edit([
                     'id'=>$detail->id,
                     'in_status'=>ReceiptTranslateDetail::IN_STATUS_OVER
                 ]);
             }
             Capsule::connection()->commit();
         }catch (\Exception $e){
             Capsule::connection()->rollBack();
             throw new \RuntimeException($e->getMessage(), $e->getCode());
         }
     }

    /**
     * 批量修改二级油品
     * @param array $params
     */
     public function edit(array $params){
         if (is_array($params['id']) && !empty($params['id'])){
             //是否存在多个规格型号
             $sku=OilReceiptTranslateDetail::getFilterByPluckFiled([
                 'id'=>$params['id']
             ],'in_sku');
             if(count(array_unique($sku)) > 1){
                 throw new \RuntimeException('不能选择多个规格型号', 2);
             }
             $in_status=OilReceiptTranslateDetail::getFilterByPluckFiled([
                 'id'=>$params['id']
             ],'in_status');
             if(in_array(ReceiptTranslateDetail::IN_STATUS_OVER,$in_status)){
                 throw new \RuntimeException('已入库的不能选择二级油品', 2);
             }
             $is_return=OilReceiptTranslateDetail::getFilterByPluckFiled([
                 'id'=>$params['id']
             ],'is_return');
             if(in_array(ReceiptTranslateDetail::IS_RETURN_NEED,$is_return)){
                 throw new \RuntimeException('需退票的不能选择二级油品', 2);
             }
             OilReceiptTranslateDetail::batchEdit($params['id'],[
                 'in_second_oil_id'=>$params['in_second_oil_id']??'',
                 'select_status'=>ReceiptTranslateDetail::SECOND_OIL_SELECTED
             ]);
         }
     }

    /**
     * 进项票撤销
     * @param array $data
     */
     public function cancelInstock(array $data){
         Capsule::connection()->beginTransaction();
         try {
             foreach ($data as $id){
                 $detail=OilReceiptTranslateDetail::getById(['id'=>$id]);
                 (new  InvoiceStockService)->insertStock(false,[
                     'oil_type_id'  => $detail->in_second_oil_id,
                     'classify'     => 10,
                     'res_id'       => $detail->receipt_return_detail_id,
                     'res_type'     => 20,
                 ]);
                 OilReceiptTranslateDetail::edit([
                     'id'=>$detail->id,
                     'in_status'=>ReceiptTranslateDetail::IN_STATUS_REVOKE
                 ]);
             }
             Capsule::connection()->commit();
         }catch (\Exception $e){
             Capsule::connection()->rollBack();
             throw new \RuntimeException($e->getMessage(), $e->getCode());
         }
     }

    /**
     * 油品类别翻译，得到油品种类id
     */
     public function transterOilType($params = [])
     {
         $energyEngine = EngineFactory::createEngine('notEnergy_delete');
         $res_energyEngine = $energyEngine->handle($params);
         //回票翻译
         $receiptTranslateEngine = EngineFactory::createEngine('receipt_translate');
         $res_receiptTranslateEngine = $receiptTranslateEngine->handle($res_energyEngine['translated']);
         //sku翻译
         $skuTranslateEngine = EngineFactory::createEngine('sku_translate');
         $res_skuTranslateEngine = $skuTranslateEngine->handle($res_receiptTranslateEngine['translated']);
         return isset($res_skuTranslateEngine['translated']) ? $res_skuTranslateEngine['translated'] : [];
     }
}