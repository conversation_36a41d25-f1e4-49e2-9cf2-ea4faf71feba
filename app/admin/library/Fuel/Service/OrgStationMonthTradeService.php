<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2021/10/28
 * Time: 11:35 AM
 */

namespace Fuel\Service;

use Exception;
use Carbon\Carbon;
use Models\OilOrg;
use Fuel\Defines\OrgConf;
use Models\OilOrgStationDayTrade;
use Models\OilOrgStationMonthTrade;
use Illuminate\Database\Eloquent\Collection;
use Models\OilStation;

class OrgStationMonthTradeService
{
    use SingletonService;

    const OIL_TEMPLATE = '经营数据线上化';
    const STATISTIC_DATA_CREATED = "org:station:day:trade:created:%s";

    /**
     * 站点机构维度月交易数据 转换 为月交易数据
     *
     * @param string $date 转换时间 格式 2021-01-01
     * @param string $action 是否初始化 init
     * @return void
     * @throws
     */
    public function writeStatisticData(string $date, string $action)
    {
        try {
            // 每次获取数据条数
            $pageSize = 6000;

            // action 必须为init
            if (!empty($action) && $action !== 'init') {
                throw new Exception('action 值不正确');
            }

            // 初始化有数据则不处理
            if (!empty($action) && $action === 'init') {
                if (!is_null(OilOrgStationMonthTrade::first())) {
                    throw new Exception('表中已有数据无需初始化');
                }
            }

            // 获取日交易数据
            $tradeBuilder = OilOrgStationDayTrade::select(
                'date', 'top_org_id', 'top_org_code', 'station_pcode', 'station_code', 'station_name', 'oil_base_id',
                'zsy_money', 'zsy_num', 'zsy_count', 'zsh_money', 'zsh_num', 'zsh_count', 'cylmk_money', 'cylmk_num',
                'cylmk_count', 'qiaopai_money', 'qiaopai_num', 'qiaopai_count', 'czk_money', 'czk_num', 'czk_count',
                'gxk_money', 'gxk_num', 'gxk_count', 'fck_money', 'fck_num', 'fck_count');

            // 无初始动作, 则按开匹配
            if (empty($action)) {
                $tradeBuilder = $tradeBuilder->where('date', $date);
            }

            // 总页数
            $tradePage = ceil($tradeBuilder->count() / $pageSize) ;
            // 分批读取数据
            for ($i = 0; $i < $tradePage; $i++) {
                $tradePageData = $tradeBuilder->limit($pageSize)->offset($i * $pageSize)->get();

                foreach ($tradePageData as $tradePageDataItem) {
                    $tradeItemDate = Carbon::parse($tradePageDataItem->date)->format('Y-m');
                    $dateNow = Carbon::now()->toDateTimeString();

                    $tradeData = OilOrgStationMonthTrade::where([
                        'date' => $tradeItemDate,
                        'top_org_id' => $tradePageDataItem->top_org_id,
                        'station_pcode' => $tradePageDataItem->station_pcode,
                        'station_code' => $tradePageDataItem->station_code,
                        'station_name' => $tradePageDataItem->station_name,
                        'oil_base_id' => $tradePageDataItem->oil_base_id,
                    ])->first();

                    // 创建
                    if (is_null($tradeData)) {
                        OilOrgStationMonthTrade::create([
                            'date' => $tradeItemDate,
                            'top_org_code' => $tradePageDataItem->top_org_code,
                            'top_org_id' => $tradePageDataItem->top_org_id,
                            'station_pcode' => $tradePageDataItem->station_pcode,
                            'station_code' => $tradePageDataItem->station_code,
                            'station_name' => $tradePageDataItem->station_name,
                            'oil_base_id' => $tradePageDataItem->oil_base_id,
                            'zsy_money' => $tradePageDataItem->zsy_money,
                            'zsy_num' => $tradePageDataItem->zsy_num,
                            'zsy_count' => $tradePageDataItem->zsy_count,
                            'zsh_money' => $tradePageDataItem->zsh_money,
                            'zsh_num' => $tradePageDataItem->zsh_num,
                            'zsh_count' => $tradePageDataItem->zsh_count,
                            'cylmk_money' => $tradePageDataItem->cylmk_money,
                            'cylmk_num' => $tradePageDataItem->cylmk_num,
                            'cylmk_count' => $tradePageDataItem->cylmk_count,
                            'qiaopai_money' => $tradePageDataItem->qiaopai_money,
                            'qiaopai_num' => $tradePageDataItem->qiaopai_num,
                            'qiaopai_count' => $tradePageDataItem->qiaopai_count,
                            'czk_money' => $tradePageDataItem->czk_money,
                            'czk_num' => $tradePageDataItem->czk_num,
                            'czk_count' => $tradePageDataItem->czk_count,
                            'gxk_money' => $tradePageDataItem->gxk_money,
                            'gxk_num' => $tradePageDataItem->gxk_num,
                            'gxk_count' => $tradePageDataItem->gxk_count,
                            'fck_money' => $tradePageDataItem->fck_money,
                            'fck_num' => $tradePageDataItem->fck_num,
                            'fck_count' => $tradePageDataItem->fck_count,
                            'createtime' => $dateNow,
                            'updatetime' => $dateNow
                        ]);
                    } else {
                        // 更新
                        $tradeData->zsy_money += $tradePageDataItem->zsy_money;
                        $tradeData->zsy_num += $tradePageDataItem->zsy_num;
                        $tradeData->zsy_count += $tradePageDataItem->zsy_count;
                        $tradeData->zsh_money += $tradePageDataItem->zsh_money;
                        $tradeData->zsh_num += $tradePageDataItem->zsh_num;
                        $tradeData->zsh_count += $tradePageDataItem->zsh_count;
                        $tradeData->cylmk_money += $tradePageDataItem->cylmk_money;
                        $tradeData->cylmk_num += $tradePageDataItem->cylmk_num;
                        $tradeData->cylmk_count += $tradePageDataItem->cylmk_count;
                        $tradeData->qiaopai_money += $tradePageDataItem->qiaopai_money;
                        $tradeData->qiaopai_num += $tradePageDataItem->qiaopai_num;
                        $tradeData->qiaopai_count += $tradePageDataItem->qiaopai_count;
                        $tradeData->czk_money += $tradePageDataItem->czk_money;
                        $tradeData->czk_num += $tradePageDataItem->czk_num;
                        $tradeData->czk_count += $tradePageDataItem->czk_count;
                        $tradeData->gxk_money += $tradePageDataItem->gxk_money;
                        $tradeData->gxk_num += $tradePageDataItem->gxk_num;
                        $tradeData->gxk_count += $tradePageDataItem->gxk_count;
                        $tradeData->fck_money += $tradePageDataItem->fck_money;
                        $tradeData->fck_num += $tradePageDataItem->fck_num;
                        $tradeData->fck_count += $tradePageDataItem->fck_count;
                        $tradeData->updatetime = $dateNow;
                        $tradeData->save();
                    }
                };
            } // for
        } catch (\Throwable $th) {
            throw new Exception($th);
        }
    } // writeStatisticData

    /**
     * 站点交易统计列表
     *
     * @param array $params
     * @return array
     */
    public function getStationConsumeList(array $params)
    {
        \helper::argumentCheck(['date'], $params);

        $params['station_code_neq_empty'] = 1;
        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();

        $params['fields'] = 'oil_org_station_month_trade.station_code, oil_org_station_month_trade.station_pcode,
            SUM(zsy_money+zsh_money+cylmk_money+qiaopai_money+czk_money+gxk_money+fck_money) AS _money,
            SUM(zsy_num+zsh_num+cylmk_num+qiaopai_num+czk_num+gxk_num+fck_num) AS _num,
            SUM(zsy_count+zsh_count+cylmk_count+qiaopai_count+czk_count+gxk_count+fck_count) AS _cnt,
            count(distinct top_org_id) as _org_cnt';

        $params['groupBy'] = ['oil_org_station_month_trade.station_code'];
        $params['orderBy'] = ['_money' => 'desc'];

        $data = OilOrgStationMonthTrade::getList($params);

        return $this->formatStationConsumeData($data, $params['date']);
    }

    /**
     * 格式化站点消费数据
     * @param $data array
     * @param $date string 格式如 2021-10-09,2021-10-21
     * @return array
     */
    protected function formatStationConsumeData($data, $date)
    {
        if (empty($data) || empty($date))
            return [];
        $infos = $this->batchFetchStationData($data);
        $moneyCount = $this->getTradeMoneyOrgData($date);
        $res = [];
        foreach ($data as $row) {
            $tmp = [
                'month'             => $date . ' 至 ' . $date,
                'station_name'      => '',
                'station_code'      => $row->station_code,
                'station_pcode'     => $row->station_pcode,
                'pcode_name'        => '',
                'province_code'     => '',
                'province_name'     => '',
                'is_highway'        => '',
                'is_highway_txt'    => '',
                'money'             => OrgChargeStatisticsService::getInstance()->formatFloatNumber($row->_money),
                'money_rate'        => OrgChargeStatisticsService::getInstance()->formatRate($row->_money, $moneyCount->_money_cnt),
                'num'               => OrgChargeStatisticsService::getInstance()->formatFloatNumber($row->_num),
                'count'             => $row->_cnt,
                'org_cnt'           => $row->_org_cnt,
                'org_cnt_rate'      => OrgChargeStatisticsService::getInstance()->formatRate($row->_org_cnt, $moneyCount->_org_cnt),
                'first_trade_time'  => '',
                'latest_trade_time' => '',
            ];
            if (isset($infos[$row->station_code])) {
                $station = $infos[$row->station_code];

                if (isset($station['station_name'])) {
                    $tmp['station_name']    = $station['station_name'];
                    $tmp['pcode_name']      = $station['pcode_name'];
                    $tmp['is_highway']      = $station['is_highway'];
                    $tmp['is_highway_txt']  = $station['is_highway_txt'];
                }

                if (isset($station['province_code'])) {
                    $tmp['province_code']       = $station['province_code'];
                    $tmp['province_name']       = $station['province_name'];
                    $tmp['first_trade_time']    = $station['first_trade_time'];
                    $tmp['latest_trade_time']   = $station['latest_trade_time'];
                }
            }
            $res[] = $tmp;
        }
        return $res;
    }

    /**
     * 格式化机构数据
     *
     * @param Collection $data
     * @return array
     */
    protected function formatOrgConsumeData(Collection $data)
    {
        if (empty($data))
            return [];

        $ids = [];
        $total = 0;
        foreach ($data as $row) {
            $total += $row->_money;
            $ids[] = $row->top_org_id;
        }

        $org = [];
        $list = OilOrg::getByIds($ids);
        foreach ($list as $row) {
            $org[$row->id] = $row;
        }

        $i = 0;
        $res = [];
        foreach ($data as $row) {
            $tmp = [
                'id'            => ++$i,
                'top_org_id'    => $row->top_org_id,
                'top_org_name'  => '',
                'top_org_code'  => '',
                'money'         => OrgChargeStatisticsService::getInstance()->formatFloatNumber($row->_money),
                'money_rate'    => OrgChargeStatisticsService::getInstance()->formatRate($row->_money, $total),
                'num'           => OrgChargeStatisticsService::getInstance()->formatFloatNumber($row->_num),
                'count'         => $row->_count,
            ];

            if (isset($org[$row->top_org_id])) {
                $tmp['top_org_code'] = $org[$row->top_org_id]->orgcode;
                $tmp['top_org_name'] = $org[$row->top_org_id]->org_name;
            }

            $res[] = $tmp;
        }

        return $res;
    }

    /**
     * 机构消费统计机构总数
     *
     * @param array $params
     * @return integer
     */
    public function getStationConsumeTotal(array $params)
    {
        $total = 0;

        $params['station_code_neq_empty'] = 1;
        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();

        $params['fields'] = 'COUNT(DISTINCT oil_org_station_month_trade.station_code) AS _cnt';
        $params['_export'] = 1;

        $data = OilOrgStationMonthTrade::getList($params);
        foreach ($data as $row) {
            $total = $row->_cnt;
        }

        return $total;
    }

    /**
     * 站点油品类型消费统计
     *
     * @param array $params
     * @return array
     */
    public function getOilTypeConsumeListByStation(array $params)
    {
        \helper::argumentCheck(['date'], $params);

        $params['fields'] = 'oil_base_id, SUM(zsy_money+zsh_money+cylmk_money+qiaopai_money+czk_money+gxk_money+fck_money) AS _money,
            SUM(zsy_num+zsh_num+cylmk_num+qiaopai_num+czk_num+gxk_num+fck_num) AS _num,
            SUM(zsy_count+zsh_count+cylmk_count+qiaopai_count+czk_count+gxk_count+fck_count) AS _count';

        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();
        $params['groupBy'] = ['oil_org_station_month_trade.oil_base_id'];
        $params['orderBy'] = ['_money' => 'desc'];

        $data = OilOrgStationMonthTrade::getList($params);

        $oilConfig = TypeCategoryService::getSecondOilConfigData(['template_name' => self::OIL_TEMPLATE]);

        $res = [];
        $total = 0;
        foreach ($data as $row) {
            $total = bcadd($total, $row->_money, 2);

            if (isset($oilConfig[$row->oil_base_id])) {
                if ($oilConfig[$row->oil_base_id]['statistic_node']) {
                    $oilId      = $row->oil_base_id;
                    $oilName    = $oilConfig[$row->oil_base_id]['name'];
                } else {
                    $top = $oilConfig[$row->oil_base_id]['parent'];

                    $oilId      = $top['id'];
                    $oilName    = $top['name'];
                }
            } else {
                $oilId = -1;
                $oilName = '其他';
            }

            if (! isset($res[$oilId])) {
                $res[$oilId] = [
                    'oil_id'        => $oilId,
                    'oil_name'      => $oilName,
                    'money'         => 0,
                    'money_rate'    => '',
                    'num'           => 0,
                    'count'         => 0
                ];
            }

            $res[$oilId]['money'] = bcadd($res[$oilId]['money'], $row->_money, 2);
            $res[$oilId]['num'] = bcadd($res[$oilId]['num'], $row->_num, 2);
            $res[$oilId]['count'] += $row->_count;
        }

        $sort = [];
        foreach ($res as $row) {
            $sort[] = $row['money'];
        }

        array_multisort($sort, SORT_DESC, SORT_NUMERIC, $res);
        foreach ($res as $i => &$row) {
            $row['id'] = $i + 1;
            $row['money_rate'] = OrgChargeStatisticsService::getInstance()->formatRate($row['money'], $total);

            $row['money'] = OrgChargeStatisticsService::getInstance()->formatFloatNumber($row['money']);
            $row['num']   = OrgChargeStatisticsService::getInstance()->formatFloatNumber($row['num']);
        }

        return $res;
    }

    /**
     * 站点活跃客户统计
     *
     * @param array $params
     * @return array
     */
    public function getOrgConsumeListByStation(array $params)
    {
        \helper::argumentCheck(['date'], $params);

        $params['fields'] = 'top_org_id, SUM(zsy_money+zsh_money+cylmk_money+qiaopai_money+czk_money+gxk_money+fck_money) AS _money,
            SUM(zsy_num+zsh_num+cylmk_num+qiaopai_num+czk_num+gxk_num+fck_num) AS _num,
            SUM(zsy_count+zsh_count+cylmk_count+qiaopai_count+czk_count+gxk_count+fck_count) AS _count';

        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();
        $params['groupBy'] = ['oil_org_station_month_trade.top_org_id'];
        $params['orderBy'] = ['_money' => 'desc'];

        $data = OilOrgStationMonthTrade::getList($params);

        return $this->formatOrgConsumeData($data);
    }

     /**
     * 获取站点数据
     * @param $data Collection
     * @return array
     */
    public function batchFetchStationData($data)
    {
        if ($data->isEmpty())
            return [];

        $codes = [];
        foreach ($data as $item) {
            $codes[] = $item->station_code;
        }
        $station = OilStation::getList(['_export' => 1, 'station_code' => $codes]);

        // 从gms获取站信息
        $params = [
            'per_page' => count($codes),
            'start' => 0,
            'station_code' => $codes
        ];
        $data = Station::getStationCodeNameList($params);

        $res = [];
        foreach ($data as $item) {
            $res[$item['station_code']] = $item;
        }

        foreach ($station as $item) {
            $res[$item->station_code]['province_code'] = $item->province_code;
            $res[$item->station_code]['province_name'] = $item->province_name;
            $res[$item->station_code]['first_trade_time'] = $item->first_trade_time;
            $res[$item->station_code]['latest_trade_time'] = $item->latest_trade_time;
        }

        return $res;
    }

     /**
     * 获取指定时间内交易总额、活跃客户数
     * @param $date  string 格式如 2021-10-09,2021-10-21
     * @return array
     */
    public function getTradeMoneyOrgData($date)
    {
        if (empty($date))
            return [];

        $res = [];

        $params['date'] = $date;
        $params['top_org_id_not_in'] = OrgConf::getTestOrgId();
        $params['fields'] = 'COUNT(DISTINCT top_org_id) AS _org_cnt, SUM(zsy_money+zsh_money+cylmk_money+qiaopai_money+czk_money+gxk_money+fck_money) AS _money_cnt';
        $params['_export'] = 1;

        $data = OilOrgStationMonthTrade::getList($params);
        foreach ($data as $row) {
            $res = $row;
        }

        return $res;
    }
}