<?php
/**
* 上游返利公式
*/
namespace Fuel\Service\UpstreamRebate;

use Framework\Helper;
use Framework\Log;
use Fuel\Service\RebatePolicyService;
use Models\OilRebatePolicy;
use Models\OilRebatePolicyHistory;
use Models\OilSupplierAccountStatementTradeExt;
use RuntimeException;
use Fuel\Defines\ErrorCode;
use Fuel\Defines\UpstreamRebate;
use Fuel\Service\SingletonService;
use Illuminate\Support\Collection;
use Models\UpstreamRebate\Formula;
use stdClass;

class FormulaService
{
    use SingletonService;

    private $model;
    private $app;
    private $userEnum;

    public function __construct()
    {
        // 初始化公式模型
        $this->model = new Formula();

        $this->userEnum = [
            'has_rebate' => 'hasRebate',
            'ca_method' => 'caMethod',
            'ca_obj' => 'caObj',
            'rebate_type' => 'rebateType',
            'lowest_recharge_limit'=> 'lowestRechargeLimit',
            'recharge_limit_type' => 'rechargeLimitType',
            'rebate_unit' => 'rebateUnit',
            'status' => 'formulaStatus'
        ];
    }

    public function getResField($params, $pluckField)
    {
        return $this->model->filter($params)->pluck(...$pluckField);
    }

    /**
     * 获得元数据
     *
     * @param array $params
     * @return Collection
     */
    public function getMeta($params)
    {
        // 必填检测
        \helper::argumentCheck(['type'], $params);
        // 支持类型检测
        $typeArr = collect(explode(',', $params['type']))->unique();

        // 支持元数据
        // 0. hasRebate 是否有返利
        // 1. caMethod 计算模式
        // 2. caObj 计算对象
        // 3. rebateType 返利形式
        // 4. lowestRechargeLimit 最低充值限额
        // 5. rechargeLimitType 充值限额类型
        // 6. rebateUnit 返利数值单位
        // 7. formulaStatus 公式状态
        // 8. caRechargeObj 消费数据对应的计算模式)
        // 9. policyType 合作类型
        // 10. reviewStatus 审核状态
        // 11. maincardOil 主卡支持的油品
        // 12. oilLimit 油品限制类型
        $diffType = $typeArr->diff([
            'hasRebate',
            'caMethod',
            'caObj',
            'rebateType',
            'lowestRechargeLimit',
            'rechargeLimitType',
            'rebateUnit',
            'formulaStatus',
            'caRechargeObj',
            'policyType',
            'reviewStatus',
            'maincardOil',
            'oilLimit'
        ]);
        // 输入和支持做差集, 不为表示其中有key不支持
        if (!$diffType->isEmpty()) {
            throw new RuntimeException($diffType->implode(',') . ' 不支持此类型', ErrorCode::ERROR_PARAM);
        }

        // 组装数据
        return collect($typeArr)
            ->map(function($value) {
                return [
                    $value => collect(UpstreamRebate::${$value})
                        ->map(function($value, $key) {
                            return [
                                'key' => $key,
                                'value' => $value
                            ];
                        })->values()
                ];
            })
            ->collapse();
    }

    /**
     * 创建返利公式
     * [{"start_value":0,"end_value":100,"discount":2,"degree":1},{"start_value":100,"end_value":200,"discount":"3","degree":2}]
     * @param array $params
     * @return bool
     */
    public function createFormula($params)
    {
        $baseField = ['name', 'status', 'has_rebate'];
        // 校验枚举参数的正确性
        $this->checkEnum($this->userEnum, $params);
        // 校验业务参数的正确性
        $this->validateParam($params);

        // 初始化插入数据
        $data = collect($params);
        // 无返利
        if (intval($params['has_rebate']) === UpstreamRebate::HAS_REBATE_NOT) {
            // 只写入基础数据。过滤其它字段, 覆盖data值
            $data = collect($params)->filter(function($value, $key) use ($baseField) {
                return in_array($key, $baseField);
            });

            // 无返利的情况下其它字段都设置为默认值
            $data['ca_method'] = 0;
            $data['ca_obj'] = 0;
            $data['rebate_type'] = 0;
            $data['rebate_unit'] = 0;
            $data['rebate_value'] = 0;
            $data['lowest_recharge_limit'] = 0;
            $data['recharge_limit_type'] = 0;
            $data['ext'] = '';
        }

        // 写入创建者和最后操作者
        $data->put('creator', $this->app->myAdmin->true_name);
        $data->put('last_operator', $this->app->myAdmin->true_name);
        $this->model->create($data->toArray());

        return ;
    }

    /**
     * 编辑返利公式
     *
     * @param array $params
     * @return void
     */
    public function updateFormula($params) {
        // 必填检测
        \helper::argumentCheck(['id'], $params);

        // 校验枚举参数的正确性
        $this->checkEnum($this->userEnum, $params);

        // 查询需要修改的数据
        $updateData = $this->model->find($params['id']);
        if (\is_null($updateData)) {
            throw new \RuntimeException('返利公式不存在', 2);
        }
        // 校验业务参数的正确性, 当编辑的名字不是以前的名字时，需要校验唯一性
        $this->validateParam($params, $updateData->name !== $params['name']);

        // 检测公式已绑定政策, 只能更新名字
        if ($this->isUsed($params['id'])) {
            // 注入最后操作者
            $updateData->last_operator = $this->app->myAdmin->true_name;
            $updateData->name = $params['name'];
            $updateData->save();
            return;
        }

        // 无返利的情况下其它字段都设置为默认值
        if (intval($params['has_rebate']) === UpstreamRebate::HAS_REBATE_NOT) {
            $params['ca_method'] = 0;
            $params['ca_obj'] = 0;
            $params['rebate_type'] = 0;
            $params['rebate_unit'] = 0;
            $params['rebate_value'] = 0;
            $params['lowest_recharge_limit'] = 0;
            $params['recharge_limit_type'] = 0;
            $params['ext'] = '';
        }

        // 注入最后操作者
        $params['last_operator'] = $this->app->myAdmin->true_name;

        $res = OilRebatePolicy::getUpstreamPolicyIdsByFormulaId($params['id'],'id');
        $relPolicyIds = !empty($res) ? array_column($res, 'id') : [];
        if (!empty($relPolicyIds)) {
            $comparePolicyData = (new RebatePolicyService())->packCompareData($relPolicyIds);
        }

        $compareInfo = $this->packCompareData($params['id']);
        $this->model->where('id', $params['id'])->update(collect($params)->only($this->model->getFillAble())->all());

        // 公式对比
        $isChange = $this->compareUpstreamFormula($params['id'], $compareInfo);
        if ($isChange) {
            foreach ($comparePolicyData as $info) {
                // 写入变更记录表
                $nowMaxSerialNum = OilRebatePolicyHistory::getMaxSerialNum($info['policy_id']) + 1;
                OilRebatePolicyHistory::add(
                    [
                        'policy_id' => $info['policy_id'],
                        'serial_num' => $nowMaxSerialNum,
                        'content' => \GuzzleHttp\json_encode($info),
                    ]
                );
                // 批量更新历史上游消费流水扩展表数据
                OilSupplierAccountStatementTradeExt::updateByFilter(['policy_id'=>$info['policy_id'], 'serial_num'=>0], ['serial_num'=>$nowMaxSerialNum]);
            }
        }

    } // updateFormula

    /**
     * 公式变动校验
     * @param $formulaId
     * @param array $compareInfo
     * @return bool
     */
    public function compareUpstreamFormula($formulaId, array $compareInfo = [])
    {
        $nowPolicyData = $this->packCompareData($formulaId);
        if (md5(\GuzzleHttp\json_encode($nowPolicyData)) != md5(\GuzzleHttp\json_encode($compareInfo))) {
            return true;
        }
        return false;
    }

    /**
     * 比较数据
     * @param $formulaId
     * @return array
     */
    public function packCompareData($formulaId)
    {
        $info = $this->model->where('id', $formulaId)->first();
        $info = !$info ? [] : $info->toArray();
        $ret = [];
        if (!empty($info)) {
            $ret = [
                'ca_method'   => $info['ca_method'],
                'ca_obj'      => $info['ca_obj'],
                'rebate_type' => $info['rebate_type'],
                'rebate_value' => $info['rebate_value'],
                'rebate_unit' => $info['rebate_unit'],
                'ext'          => $info['ext'],
            ];
        }
        return $ret;
    }

    /**
     * 返利列表
     *
     * @param array $params
     * @return void
     */
    public function getList($params)
    {
        $pageSize = isset($params['limit']) ? $params['limit'] : 50;
        $page = isset($params['page']) ? $params['page'] : 1;
        $params['idIn'] = empty($params['ids']) ? '' : explode(',', $params['ids']);

        //查看后返/直降标识
        if(isset($params['is_after'])){
            $params['ca_method_in'] = $params['is_after'] == 1 ? UpstreamRebate::$afterCaMethod : UpstreamRebate::$realTimeCaObj;
        }
        if(isset($params['policy_type']) && $params['policy_type'] == 40){
            $params['ca_method_in'] = UpstreamRebate::$chargeCaMethod;
            $params['ca_obj'] = 20; //充返类型
        }
        Log::error('getList-params',[$params],'tim321312');
        $listData = $this->model->filter($params);
        //公式引用时获取返利公式同时返回无返利公式
        if(isset($params['has_rebate']) && $params['has_rebate'] == 1){
            $listData = $this->model->filter($params)->orWhere('has_rebate',UpstreamRebate::HAS_REBATE_NOT);
        }
        // 导出数据
        if(isset($params['_export']) && intval($params['_export']) === 1) {
            return $listData->get();
        }

        // 列表数据
        $listData = $listData->orderBy('id', 'desc')->paginate($pageSize, ['*'], 'page', $page);

        // 公式引用查询
        $listData->map(function($value) {
            // 无返利时, 返利显示为--
            if (intval($value->has_rebate) === UpstreamRebate::HAS_REBATE_NOT) {
                $value->rebate_value = '--';
            }elseif(!in_array($value->ca_method,UpstreamRebate::$caCommonMethod)){
                //有返利且返利模式为阶梯时
                $extArr = json_decode($value->ext, true);
                $rebate_value = '';
                foreach($extArr as $extItem) {
                    $rebate_value .= $extItem['start_value'] . '~' . $extItem['end_value'] . ':' . $extItem['discount'] . ($value->rebate_unit == UpstreamRebate::REBATE_UNIT_PERSENT ? '%' : '') . ";";
                }
                $value->rebate_value=rtrim($rebate_value,';');
            }
            // 转换枚举值
            $value->rebate_type = UpstreamRebate::$rebateType[$value->rebate_type] ?? '--';
            $value->rebate_unit = UpstreamRebate::$rebateUnit[$value->rebate_unit] ?? '--';
            $value->ca_method = UpstreamRebate::$caMethod[$value->ca_method] ?? '--';
            $value->status = UpstreamRebate::$formulaStatus[$value->status] ?? '--';

             // G7WALLET-1679 计算对象是消费数据，最低充值限额、充值限额类型应该展示成-
             if (intval($value->ca_obj) === UpstreamRebate::CA_OBJ_TRADE) {
                $value->recharge_limit_type_val = '--';
                $value->recharge_limit_type = '--';
                $value->lowest_recharge_limit = '--';
            } else {
                $value->recharge_limit_val = $value->recharge_limit_type;
                $value->recharge_limit_type = UpstreamRebate::$rechargeLimitType[$value->recharge_limit_type] ?? '--';
                $value->lowest_recharge_limit = UpstreamRebate::$lowestRechargeLimit[$value->lowest_recharge_limit] ?? '--';
            }

            $value->ca_obj = UpstreamRebate::$caObj[$value->ca_obj] ?? '--';
            $value->has_rebate = UpstreamRebate::$hasRebate[$value->has_rebate] ?? '--';
            // 引用次数， 可做异步优化
            $value->invoke = $this->invokeCount($value->id);
        });

        $listData = $listData->toArray();
        // 解决导出对count的依赖
        $listData['count'] = $listData['total'];

        // 普通分页查询
        return $listData;
    }

    /**
     * 引用次数
     *
     * @param int $formulaID
     * @return int
     */
    public function invokeCount($formulaID)
    {
        $invokeData = $this->model->from('oil_upstream_rebate_policy_limit_item_formula as a')
            ->selectRaw('COUNT(DISTINCT `d`.`policy_id`) as invokeCount')
            ->leftJoin('oil_upstream_rebate_policy_limit_item as b', 'a.limit_item_id', '=', 'b.id')
            ->leftJoin('oil_upstream_rebate_policy_limit as c', 'c.id', '=', 'b.limit_id')
            ->leftJoin('oil_upstream_rebate_policy_relate as d', 'c.id', '=', 'd.policy_limit_id')
            ->leftJoin('oil_upstream_rebate_policy as e', 'e.id', '=', 'd.policy_id')
            ->where('a.formula_id', $formulaID)
            ->where('e.review_status', UpstreamRebate::REVIEW_STATUS_PASS)
            ->first();

        return $invokeData->invokeCount;
    }

    /**
     * 查询公式详情
     *
     * @param  mixed $params
     * @return Formula
     */
    public function detail($params)
    {
        // 必填检测
        \helper::argumentCheck(['id'], $params);
        $detail = $this->model->find($params['id']);

        if (is_null($detail)) {
            return new stdClass();
        }

        // 特殊字段默认值处理
        if ($detail['lowest_recharge_limit'] === UpstreamRebate::LOWEST_RECHARGE_LIMIT_NOT_HAVE) {
            $detail['recharge_limit_type'] = '';
        }

        // 处理阶梯数值
        if (!in_array($detail['ca_method'], [
            UpstreamRebate::CA_METHOD_SINGLE_DOWN,
            UpstreamRebate::CA_METHOD_ALL_AMOUNT]) &&
            !empty($detail['ext'])
        ) {
            $extArr = json_decode($detail['ext'], true);

            $detail['rebate_value'] = '';
            foreach($extArr as $extItem) {
                $detail['rebate_value'].= $extItem['start_value'] . '~' . $extItem['end_value'] . ':' . $extItem['discount'] . ($detail['rebate_unit'] == UpstreamRebate::REBATE_UNIT_PERSENT ? '%' : '') . '<br />';
            }
        }

        $detail = $detail->toArray();
        $detail['ca_obj_value'] = UpstreamRebate::$caObj[$detail['ca_obj']] ?? '';
        $detail['ca_method_value'] = UpstreamRebate::$caMethod[$detail['ca_method']] ?? '';
        $detail['rebate_type_value'] = UpstreamRebate::$rebateType[$detail['rebate_type']] ?? '';
        $detail['rebate_unit_value'] = UpstreamRebate::$rebateUnit[$detail['rebate_unit']] ?? '';
        $detail['has_rebate_value'] = UpstreamRebate::$hasRebate[$detail['has_rebate']] ?? '';
        $detail['lowest_recharge_limit_value'] = UpstreamRebate::$lowestRechargeLimit[$detail['lowest_recharge_limit']] ?? '';
        $detail['recharge_limit_type_value'] = UpstreamRebate::$rechargeLimitType[$detail['recharge_limit_type']] ?? '';
        $detail['recharge_limit_type_val'] = $detail['recharge_limit_type_val'];

        if(isset($params['is_after']) && $params['is_after']){
            $_detail = [];
            foreach ($detail as $key => $value){
                $_detail[$params['is_after'].'_'.$key] = $value;
            }
            $detail = $_detail;
        }

        return $detail;
    }

    /**
     * 公式是否被使用
     *
     * @param int $id
     * @return bool
     */
    public function isUsed($id)
    {
//        return $id === 0;
        $info = OilRebatePolicy::where('formula_id',$id)->whereOr('direct_drop_formula_id',$id)->first();
        if($info){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 检测枚举是否正确
     *
     * @param array $params
     * @return bool
     */
    public function checkEnum($needCheck, $params)
    {
        collect($needCheck)->map(function($value, $key) use ($params) {
            // 参入参数有枚举值
            if (array_key_exists($key, $params) && !in_array($params[$key], array_keys(UpstreamRebate::${$value}))) {
                throw new \RuntimeException($key . ' 值不正确', ErrorCode::ERROR_PARAM);
            }
        });
    } // checkEnum

    /**
     * 校验业务参数的正确性
     *
     * @param array $params
     * @param bool $checkNameUnique 是否测试名字的唯一性
     * @return void
     */
    public function validateParam($params, $checkNameUnique = true)
    {
        // 当计算对象为充值返时, 只有部份计算模式支持。 消费返全部支持
        if (
            intval($params['ca_obj']) === UpstreamRebate::CA_OBJ_RECHARGE &&
            !in_array($params['ca_method'], UpstreamRebate::$caRechargeObj)
        ) {
            throw new \RuntimeException('计算对象为【' . UpstreamRebate::$caObj[$params['ca_obj']] . '】时, 不支持【' . UpstreamRebate::$caMethod[$params['ca_method']] . '】计算模式。', ErrorCode::ERROR_PARAM);
        }

        // 创建时才检测名称
        if ($checkNameUnique) {
            // 公式名称不能重复
            $formula = $this->getResField([
                'name' => $params['name']
            ], ['id']);
            if (!$formula->isEmpty()) {
                throw new \RuntimeException('已存在名为【'.$params['name'].'】的返利公式', ErrorCode::ERROR_PARAM);
            }
        }

        // 无返利则不校验其它参数
        if (intval($params['has_rebate']) === UpstreamRebate::HAS_REBATE_NOT) {
            $baseField = ['name', 'status', 'has_rebate'];
            // 必填检测
            \helper::argumentCheck($baseField, $params);
            return;
        } else {
            $baseField = ['name', 'status', 'ca_method', 'ca_obj', 'rebate_type', 'rebate_unit', 'has_rebate'];
            // 必填检测
            \helper::argumentCheck($baseField, $params);
            if($params['ca_obj'] == UpstreamRebate::CA_OBJ_RECHARGE && $params['lowest_recharge_limit'] == UpstreamRebate::LOWEST_RECHARGE_LIMIT_HAVE){
                \helper::argumentCheck(['recharge_limit_val'], $params);
            }
            // 直降类型的公式，返利类型不能选择积分
            if (in_array($params['ca_method'], UpstreamRebate::$realTimeCaObj) && $params['rebate_type'] == UpstreamRebate::REBATE_TYPE_JIFEN) {
                throw new \RuntimeException('直降类型的公式，返利类型不能选择积分!', ErrorCode::ERROR_PARAM);
            }
        }

        // 普通计算模式 返利数值百分比 0<=x<=100 返利数值元/每单位 0<=x<=100
        if (in_array($params['ca_method'], UpstreamRebate::$caCommonMethod)) {
            $this->validateCommonMethod($params);
            // 普通计算不校验其它参数
            return;
        }
        // 梯度参数校验
        $this->validateStepMethod($params);
    } // validateParam

    /**
     * 检验普通计算模式的参数
     *
     * @param  array $params
     * @return void
     */
    public function validateCommonMethod($params)
    {
        if (!isset($params['rebate_value'])) {
            throw new \RuntimeException('缺少返利值[rebate_value]', ErrorCode::ERROR_PARAM);
        }

        if (
            UpstreamRebate::REBATE_UNIT_PERSENT === intval($params['rebate_unit']) &&
            (!is_numeric($params['rebate_value']) ||
            $params['rebate_value'] < 0 ||
            $params['rebate_value'] > 100
        )) {
            throw new \RuntimeException('返利单位是百分比时，返利值必须是数值类型且大于等于0且小于等于100', ErrorCode::ERROR_PARAM);
        }

        if (
            UpstreamRebate::REBATE_UNIT_PER === intval($params['rebate_unit']) &&
            (!is_numeric($params['rebate_value']) ||
            $params['rebate_value'] < 0 ||
            $params['rebate_value'] > 10)
        ) {
            throw new \RuntimeException('返利单位是元/每单位时，返利值必须是数值类型且大于等于0且小于等于10', ErrorCode::ERROR_PARAM);
        }


        // 返利值最多保留4位小数
        if (Helper::getFloatLength($params['rebate_value']) > 4) {
            throw new \RuntimeException('返利值最多保留4位小数', ErrorCode::ERROR_PARAM);
        }

        // 计算模式为累计返利 计算对象为充值数据时 最低充值限额和充值限额类型必须存在
        if (
            intval($params['ca_method']) === UpstreamRebate::CA_METHOD_ALL_AMOUNT &&
            intval($params['ca_obj']) === UpstreamRebate::CA_OBJ_RECHARGE
        ) {
            if (!isset($params['lowest_recharge_limit'])) {
                throw new \RuntimeException('缺少最低充值限额[lowest_recharge_limit]', ErrorCode::ERROR_PARAM);
            }

            // 最低充值限制为有时才校验类型
            if (
                UpstreamRebate::LOWEST_RECHARGE_LIMIT_HAVE === intval($params['lowest_recharge_limit']) &&
                !isset($params['recharge_limit_type'])
            ) {
                throw new \RuntimeException('缺少充值限额类型[recharge_limit_type]', ErrorCode::ERROR_PARAM);
            }
        }
    } // end validateCommonMethod

    /**
     * 检验阶梯计算模式的参数
     *
     * @param array $params
     * @return void
     */
    public function validateStepMethod($params)
    {
        if (!isset($params['ext'])) {
            throw new \RuntimeException('缺少阶梯字段[ext]', ErrorCode::ERROR_PARAM);
        }

        $extData = json_decode($params['ext'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \RuntimeException('阶梯字段[ext]解析错误', ErrorCode::ERROR_PARAM);
        }

        // 按degree排序
        $extData = collect($extData)->sortBy('degree');

        // 开始校栓degree
        $degree = $extData->pluck('degree');
        if ($degree->min() !== 1) {
            throw new \RuntimeException('degree最小值只能为1, 或有节点缺少degree', ErrorCode::ERROR_PARAM);
        }

        if ($degree->count() !== $degree->unique()->count()) {
            throw new \RuntimeException('degree值中有重复', ErrorCode::ERROR_PARAM);
        }

        if ($degree->max() !== $degree->count()) {
            throw new \RuntimeException('degree值不连续', ErrorCode::ERROR_PARAM);
        }

        $curEndValue = '';
        $extData->map(function($value) use (&$curEndValue, $params) {
            // json中的字段
            $jsonField = collect(['start_value', 'end_value', 'discount', 'degree']);
            // 校验节点中的字段完整性
            if (!$jsonField->diff(array_keys($value))->isEmpty()) {
                throw new \RuntimeException('节点:' . $value['degree'] . '中字段不全', ErrorCode::ERROR_PARAM);
            }

            // 所有字段都为数字
            $jsonField->map(function($subValue) use ($value) {
                if (!\is_numeric($value[$subValue])) {
                    // throw new \RuntimeException('节点:' . $value['degree'] . '中' . $subValue . '值不是数值', ErrorCode::ERROR_PARAM);
                    throw new \RuntimeException('返利数值/阶梯值为空或不是数字 ', ErrorCode::ERROR_PARAM);
                }
            });

            // 结束值不能小于起始值
            if ($value['end_value'] <= $value['start_value']) {
                throw new \RuntimeException('节点:' . $value['degree'] . '中阶梯结束值必须大于起始值！', ErrorCode::ERROR_PARAM);
            }

            // 第一个的起始值必须为0
            if ($value['degree'] == 1 && $value['start_value'] != 0) {
                throw new \RuntimeException('阶梯返利第一级start_value值必须是0！', ErrorCode::ERROR_PARAM);
            }

            // 返利值的小数最多4位
            if (Helper::getFloatLength($value['discount']) > 4) {
                throw new \RuntimeException('节点:' . $value['degree'] . '中阶梯返利值最多保留4位小数', ErrorCode::ERROR_PARAM);
            }

            // 返利单位是百分比时，返利值不能大于100
            if (
                UpstreamRebate::REBATE_UNIT_PERSENT === intval($params['rebate_unit']) &&
                ($value['discount'] < 0 || $value['discount'] > 100)
            ) {
                throw new \RuntimeException('返利单位是百分比时，返利值必须为0到100之间数值', ErrorCode::ERROR_PARAM);
            }

            if (
                UpstreamRebate::REBATE_UNIT_PER === intval($params['rebate_unit']) &&
                ($value['discount'] < 0 || $value['discount'] > 10)
            ) {
                throw new \RuntimeException('返利单位是元/每单位时，返利值必须为0到10之间数值', ErrorCode::ERROR_PARAM);
            }

            // start_value和end_value必须闭合
            if (
                !empty($curEndValue) &&
                intval($value['start_value']) !== $curEndValue
            ) {
                // throw new \RuntimeException('节点:' . $value['degree'] . '中[start_value]和上一级[end_value]不一致', ErrorCode::ERROR_PARAM);
                throw new \RuntimeException('每一层阶梯的开始数值必须等于上一层阶梯的结束数值!', ErrorCode::ERROR_PARAM);
            }

            $curEndValue = intval($value['end_value']);
        });
    } // validateStepMethod


    /**
     * 匹配阶梯数值
     * @param array $stepData
     * @param $checkValue
     * @return int
     */
    public function matchStepValue(array $stepData, $checkValue)
    {
        $matchValue = 0;
        foreach ($stepData as $stepInfo) {
            if ($stepInfo['end_value'] > $checkValue) {
                $matchValue = $stepInfo['discount'];
                break;
            }
            continue;
        }
        return $matchValue;
    }
}