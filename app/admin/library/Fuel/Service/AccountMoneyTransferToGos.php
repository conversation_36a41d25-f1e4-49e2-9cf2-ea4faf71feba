<?php

namespace Fuel\Service;

use Framework\GosTaskInterface;
use Framework\Log;
use Fuel\Defines\AccountMoneyTransferStatus;
use GosSDK\Defines\Methods\AccountMoneyTransfer as AccountMoneyTransferMethod;
use GosSDK\Gos;
use Models\GspSysUsers;
use Models\OilAccountMoneyTransfer;
use Models\OilOrg;

class AccountMoneyTransferToGos implements GosTaskInterface
{
    ///
    static public function init()
    {
        $pageSize = 200;
        for ($page = 1; $page < 10000; $page++) {
            $data = OilAccountMoneyTransfer::orderBy('id', 'asc')
//                ->where(function($query){
//                    $query->where('createtime','>','2017-09-01 00:00:00')->orWhere('updatetime','>','2017-08-20 22:30:00');
//                })
//                ->where('createtime','<','2017-10-31 23:59:59')
                ->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get()->toArray();

            if (!$data) {
                echo 'no data';
                break;
            }
            //预处理
            $data = self::formatData($data);

            try{
                $result = (new Gos())
                    ->setMethod(AccountMoneyTransferMethod::UPDATE_BATCH)
                    ->setParams($data)
                    ->sync();

                var_dump($result);
            }catch(\Exception $e){
                echo strval($e);
            }
        }
    }

    static public function getData(array $ids)
    {
        $data = OilAccountMoneyTransfer::getByIdList($ids);

        return AccountMoneyTransferToGos::formatData($data);
    }

    static public function formatData(array $data)
    {
        $orgIds = [];
        foreach ($data as $v){
            $orgIds[] = $v['org_id'];
            $orgIds[] = $v['into_org_id'];
        }
        $orgInfo = OilOrg::getByOrgIdArrMap($orgIds);

        foreach ($data as $k=>&$v) {
            if(!isset($orgInfo[$v['org_id']]) || !isset($orgInfo[$v['into_org_id']])){
                unset($data[$k]);
                continue;
            }
            $v['org_id'] = $orgInfo[$v['org_id']];
            $v['into_org_id'] = $orgInfo[$v['into_org_id']];

            if($v['creator_id']){
                $v['creator_name'] = '客服';
            }else{
                $v['creator_name'] = $v['other_creator'];
            }
            $v['updater_name'] = $v['last_operator'];
        }

        return $data;
    }

    static public function formatUniqueData($data)
    {
        // TODO: Implement formatUniqueData() method.
    }

    static public function sendBatchCreateTask(array $ids, $type = 'async')
    {
        $data   = self::getData($ids);
        $result = (new GosTask())
            ->setTaskName('转账单新增')
            ->setIds($ids)
            ->setMethod(AccountMoneyTransferMethod::UPDATE_BATCH)
            ->setData($data)
            ->exec($type);

        return $result;
    }

    static public function sendBatchUpdateTask(array $ids, $type = 'async')
    {
        $data   = self::getData($ids);
        $result = (new GosTask())
            ->setTaskName('转账单更新')
            ->setIds($ids)
            ->setMethod(AccountMoneyTransferMethod::UPDATE_BATCH)
            ->setData($data)
            ->exec($type);

        return $result;
    }

    static public function sendBatchDeleteTask(array $ids, $type = 'async')
    {
        $result = (new GosTask())
            ->setTaskName('转账单删除')
            ->setIds($ids)
            ->setMethod(AccountMoneyTransferMethod::DELETE)
            ->setData(['id'=>$ids])
            ->exec($type);

        return $result;
    }
}