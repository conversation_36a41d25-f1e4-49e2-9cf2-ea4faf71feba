<?php

namespace Fuel\Service;

use Framework\Cache;
use Framework\Log;
use Fuel\Defines\BindStatus;
use Fuel\Defines\CardFrom;
use Fuel\Defines\OilCom;
use Fuel\Defines\ViceCardStatus;
use GosSDK\Defines\Methods\CardVice as CardViceMethod;
use GosSDK\Gos;
use Models\GspSysUsers;
use Models\OilCardAccount;
use Models\OilCardMain;
use Models\OilCardVice;
use Models\OilOrg;
use Models\OilViceTags;

class CardViceToGos
{
    /**
     * @title    根据卡id批量同步到gos
     * @desc
     * @param array  $ids
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function batchAddByIdsToGos(array $ids, $type = 'async')
    {
        return self::sendBatchCreateTask($ids, $type);
    }

    static public function batchAddToGosByViceNos(array $viceNos, $type = 'async')
    {
        $data = OilCardVice::getByViceNos($viceNos)->toArray();
        //预处理
        $data = self::formatData($data);

        return self::sendBatchCreateTaskByData($data, $type);
    }

    /**
     * @title   获取vice_id=>tag_name
     * @desc
     * @param $viceIds
     * @return array
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    static private function getTagNameByViceIds($viceIds)
    {
        $tagInfo     = OilViceTags::getTagInfoByViceIds($viceIds);
        $viceTagInfo = [];
        foreach ($tagInfo as $v) {
            $viceTagInfo[$v['vice_id']] = isset($viceTagInfo[$v['vice_id']]) ? $viceTagInfo[$v['vice_id']] . ';' . $v['tag_name'] : $v['tag_name'];
        }

        return $viceTagInfo;
    }

    static public function getCardAccountList($viceIds)
    {
        $cardAccountList = OilCardAccount::leftJoin('oil_credit_account', 'oil_credit_account.account_no', '=', 'oil_card_account.common_account_no')
                                         ->leftJoin('oil_credit_provider', 'oil_credit_provider.id', '=', 'oil_credit_account.credit_provider_id')
                                         ->whereIn('oil_card_account.vice_id', $viceIds)->where('oil_card_account.subAccountType', 'CREDIT')
                                         ->selectRaw('oil_card_account.*,oil_credit_provider.name')
                                         ->get();
        $cardAccountMap  = [];
        if ($cardAccountList) {
            foreach ($cardAccountList as $value) {
                $cardAccountMap[$value->vice_id][] = [
                    'product_name' => $value->name,
                    'account_name' => '卡授信账号',
                    'account_no'   => $value->cardSubAccountID,
                    'amount'       => $value->amount,
                ];
            }
        }

        return $cardAccountMap;
    }

    /**
     * @title   获取主卡返利地区
     * @desc
     * @param $cardMainIds
     * @return array
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    static private function getCardMainInfo($cardMainIds)
    {
        $cardMainInfo = OilCardMain::getByIdArr($cardMainIds);

        $data = [];
        foreach ($cardMainInfo as $v) {
            $data[$v->id]['code']     = isset($v->FanLiRegion) ? $v->FanLiRegion->code : '';
            $data[$v->id]['province'] = isset($v->FanLiRegion) ? $v->FanLiRegion->province : '';
        }

        return $data;
    }

    /**
     * @title    push批量修改至Gos
     * @desc
     * @param array  $viceNos
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function batchUpdateToGos(array $viceNos, $type = 'async')
    {
        $data = OilCardVice::getByViceNos($viceNos)->toArray();
        //预处理
        $data = CardViceToGos::formatData($data);

        return self::sendBatchUpdateTaskByData($data, $type);
    }

    /**
     * @title    push批量修改至Gos
     * @desc
     * @param array  $viceIds
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function batchUpdateToGosByViceIds(array $viceIds, $type = 'async')
    {
        return self::sendBatchUpdateTask($viceIds, $type);
    }


    /**
     * @title    push批量修改至Gos
     * @desc
     * @param array  $viceIds
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function batchUpdateSyncToGosByViceIds(array $viceIds, $type = 'async')
    {
        return self::sendBatchUpdateTask($viceIds, $type);
    }

    /**
     * @title    push批量修改至Gos
     * @desc
     * @param array  $viceNos
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function batchUpdateToGosByViceNos(array $viceNos, $type = 'async')
    {
        $data = OilCardVice::getByViceNos($viceNos)->toArray();
        //预处理
        $data = CardViceToGos::formatData($data);

        return self::sendBatchUpdateTaskByData($data, $type);
    }

    ////////////////////////////////////////////////

    /**
     * @title    数据初始化
     * @desc
     * @return array
     * @returns
     * array
     * @returns
     * <AUTHOR> @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     */
    static public function init()
    {
        $data     = [];
        $pageSize = 100;
        for ($page = 1; $page < 10000; $page++) {
            $ids = OilCardVice::whereIn('oil_com', [1, 2, 3, 50, 52])
                              ->orderBy('id', 'asc')
                              ->offset(($page - 1) * $pageSize)
                              ->limit($pageSize)
                              ->pluck('id')
                              ->toArray();

            if (!$ids) {
                echo 'no ids';
                break;
            }
            $data[] = self::sendBatchUpdateTask($ids, 'sync');
        }

        return $data;
    }

    /**
     * @title    根据ids获取数据并格式化
     * @desc
     * @param array $ids
     * @return array|mixed
     * @returns
     * array|mixed
     * @returns
     * @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    static public function getData(array $ids)
    {
        $data   = \NULL;
        $record = OilCardVice::getByIds($ids);
        if (!$record || count($record) == 0) {
            return $data;
        }

        return self::formatData($record->toArray());
    }

    static public function formatUniqueData($data)
    {
    }

    /**
     * @title    数据格式化
     * @desc
     * @param array $data
     * @return array
     * @returns
     * array
     * @returns
     * @package  Fuel\Service
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR>
    static public function formatData(array $data)
    {
        $orgIdArr      = [];
        $userIdArr     = [];
        $cardMainIdArr = [];
        $cardViceIdArr = [];
        foreach ($data as $v) {
            $orgIdArr[]      = $v['org_id'];
            $orgIdArr[]      = $v['org_id_fanli'];
            $orgIdArr[]      = $v['gas_money_id'];
            $cardMainIdArr[] = $v['card_main_id'];
            $cardViceIdArr[] = $v['id'];
            $userIdArr[]     = $v['creator_id'];
        }
        //获取机构信息
        $orgInfo = OilOrg::getByOrgIdArrMap(array_unique($orgIdArr));
        //获取主卡信息
        $cardMainInfo = self::getCardMainInfo(array_unique($cardMainIdArr));
        //获取标签信息
        $tagInfo = self::getTagNameByViceIds($cardViceIdArr);
        //获取创建人信息
        $userInfo = GspSysUsers::getByIdMap(array_unique($userIdArr));
        //获取卡账户信息
        $cardAccountList = self::getCardAccountList($cardViceIdArr);

//        Log::error('$cardAccountList' . var_export($cardAccountList, TRUE), [], 'cardViceToGos');
        foreach ($data as $k => &$v) {
            if (!isset($orgInfo[$v['org_id']])) {
                unset($data[$k]);
                continue;
            }

            //08-20,6,7,8的卡跳过
            if (in_array($v['oil_com'], [6, 7, 8])) {
                unset($data[$k]);
                continue;
            }

            $v['org_id']            = $orgInfo[$v['org_id']];
            $v['org_id_fanli']      = isset($orgInfo[$v['org_id_fanli']]) ? $orgInfo[$v['org_id_fanli']] : NULL;
            $v['gas_money_id']      = $v['gas_money_id'] && isset($orgInfo[$v['gas_money_id']]) ? $orgInfo[$v['gas_money_id']] : NULL;
            $v['fanli_region']      = isset($cardMainInfo[$v['card_main_id']]) && isset($cardMainInfo[$v['card_main_id']]['code']) ? $cardMainInfo[$v['card_main_id']]['code'] : '';
            $v['fanli_region_name'] = isset($cardMainInfo[$v['card_main_id']]) && isset($cardMainInfo[$v['card_main_id']]['province']) ? $cardMainInfo[$v['card_main_id']]['province'] : '';

            $v['tag_name'] = isset($tagInfo[$v['id']]) ? $tagInfo[$v['id']] : '';

            $v['today_balance'] = $v['card_remain'];
            $v['creator_name']  = isset($userInfo[$v['creator_id']]) ? $userInfo[$v['creator_id']] : '';
            $v['updater_name']  = isset($v['last_operator']) ? $v['last_operator'] : '';

            if (!empty($v['status'])) {
                $v['status'] = ViceCardStatus::getStatusForGos($v['status']);
            }

            //取卡的授信余额展示给前端
            $v['credit_card_remain'] = isset($cardAccountList[$v['id']]) ? $cardAccountList[$v['id']] : [];

            unset($v['third_id']);
        }

        return $data;
    }

    /**
     * @title    批量创建
     * @desc
     * @param array  $ids
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function sendBatchCreateTask(array $ids, $type = 'async')
    {
        $data = self::getData($ids);

        if(!$data){
            return true;
        }
        /*return (new GosTask())
            ->setTaskName('卡片批量新增')
            ->setIds($ids)
            ->setMethod(CardViceMethod::CREATE_BATCH)
            ->setData($data)
            ->exec($type);*/
        return self::sendBatchUpdateTaskByData($data,' async', '卡片批量新增');
    }

    /**
     * @title    根据卡片data传输给gos
     * @desc
     * @param array  $data
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function sendBatchCreateTaskByData(array $data, $type = 'async')
    {
        Log::info('添加参数--' . var_export($data, true), [], 'cardViceToGos');
        $result = \NULL;
        $ids    = GosTask::getValByKey($data, 'id');
        if ($data && $ids) {
//            $result = (new GosTask())
//                ->setTaskName('卡片批量新增')
//                ->setIds($ids)
//                ->setMethod(CardViceMethod::CREATE_BATCH)
//                ->setData($data)
//                ->exec($type);

            $result = self::sendBatchUpdateTaskByData($data,' async', '卡片批量新增');
        }

        return $result;
    }

    /**
     * 根据数据批量更新
     * @param array $data
     * @param string $type
     * @param string $title
     * @return mixed|null
     */
    static public function sendBatchUpdateTaskByData(array $data, $type = 'async', $title='卡片批量更新')
    {
        if(empty($data)){
            return true;
        }
        Log::info(__METHOD__, [$data], 'cardViceToGos');
        $result = \NULL;
        $ids    = GosTask::getValByKey($data, 'id');

        if ($data && $ids) {
//            $result = (new GosTask())
//                ->setTaskName('卡片批量更新')
//                ->setIds($ids)
//                ->setMethod(CardViceMethod::UPDATE_BATCH)
//                ->setData($data)
//                ->exec($type);

            $result = (new GosTask())
                ->setTaskName($title)
                ->setIds($ids)
                ->setMethod(CardViceMethod::UPDATE_BATCH)
                ->setData($data)
                ->push2List('list:card_vice');
        }

        return $result;
    }

    /**
     * 疑似废弃
     * @title    1号卡迁移，更改gos系统的third_id及sys_id
     * @desc
     * @param array  $data
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function sendBatchUpdateTaskById(array $data, $type = 'sync')
    {
        Log::info('编辑参数--' . var_export($data, true), [], 'cardViceToGos');
        $result = \NULL;
        if ($data) {
            $result = (new GosTask())
                ->setTaskName('批量更改G7能源账户')
                ->setIds($data)
                ->setMethod(CardViceMethod::UPDATE_BATCH_BYVICENO)
                ->setData($data)
                ->exec($type);
        }

        return $result;
    }

    /**
     * 疑似废弃
     * 迁移机构账户余额的接口
     * @param array $viceNos
     * @param string $type
     * @return mixed|null
     */
    static public function sendBatchUpdateSysIdByViceNos(array $viceNos, $type = 'sync')
    {
        $data = OilCardVice::getByViceNosForMove1Card($viceNos)->toArray();
        Log::info('编辑参数--' . var_export($data, true), [], 'cardViceToGos');
        $result = \NULL;
        if ($data) {
            $result = (new GosTask())
                ->setTaskName('批量更改G7能源账户')
                ->setIds($data)
                ->setMethod(CardViceMethod::UPDATE_BATCH_BYVICENO)
                ->setData($data)
                ->exec($type);
        }

        return $result;
    }

    /**
     * @title    批量异步更新
     * @desc
     * @param array  $ids
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function sendBatchUpdateTask(array $ids, $type = 'async')
    {
        $data   = self::getData($ids);
        if(!$data){
            return true;
        }
        return self::sendBatchUpdateTaskByData($data);
    }

    /**
     * @title    批量删除
     * @desc
     * @param array  $ids
     * @param string $type
     * @return mixed|null
     * @returns
     * mixed|null
     * @returns
     * @since
     * @params   type filedName required?
     * @version  1.0.0
     * <AUTHOR> @package  Fuel\Service
     */
    static public function sendBatchDeleteTask(array $ids, $type = 'async')
    {
        $result = (new GosTask())
            ->setTaskName('卡片批量删除')
            ->setIds($ids)
            ->setMethod(CardViceMethod::DELETE)
            ->setData(['id' => $ids])
            ->exec($type);

        return $result;
    }

    /**
     * @title   油站限定是否可用
     * @desc
     * @param array $params
     * @return mixed|null
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    static public function checkStationByOrgCodesAndStations(array $params)
    {
        $result = $data = (new Gos())
            ->setRequestType('POST')
            ->setMethod('v1/stationLimit/checkStationByOrgCodesAndStations')
            ->setParams($params)
            ->sync();

        return $result;
    }
}