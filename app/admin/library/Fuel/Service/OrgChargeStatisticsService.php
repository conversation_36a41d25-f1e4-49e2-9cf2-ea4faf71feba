<?php
/**
 * Created by PhpStorm.
 * User: liying
 * Date: 2021/10/28
 * Time: 11:35 AM
 */

namespace Fuel\Service;

use Fuel\Defines\OrgConf;
use Illuminate\Database\Capsule\Manager;
use Models\OilOrgDayChargesNew;
use Illuminate\Support\Collection;
use Models\OilPayCompany;

/**
 * 机构充值消费统计
 */
class OrgChargeStatisticsService
{
    use SingletonService;

    private $model;

    public function __construct()
    {
        $this->model = new OilOrgDayChargesNew();
    }

    /**
     * 获得油站运营商交易统计
     *
     * @param array $params
     * @return array
     */
    public function getList(array $params)
    {
        \helper::argumentCheck(['date'], $params);

        $params['orgroot_not_in'] = OrgConf::getTestOrgCode();

        // 计算总数
        if (intval($params['count']) === 1) {
            $sqlBuilder = $this->model->selectRaw('COUNT(DISTINCT orgroot) as id_count')->from('oil_org_day_charges_new')
            ->filter($params);

            return $sqlBuilder->first()->id_count ?? 0;
        }

        $limit = isset($params['limit']) ? $params['limit'] : 50;
        $page = isset($params['page']) ? $params['page'] : 1;
        $skip = ($page - 1) * $limit;

        $list = $this->model->selectRaw('oil_org.org_name,
            oil_org_day_charges_new.orgroot,
            sum( IF(charge_type = 2, 0, oil_org_day_charges_new.total_money) ) AS all_money,
            sum( IF(charge_type = 2, 0, oil_org_day_charges_new.total_num) ) AS all_num,
            sum( CASE WHEN oil_org_day_charges_new.charge_type = 1 THEN oil_org_day_charges_new.total_money ELSE 0 END ) AS nomal_money,
            sum( CASE WHEN oil_org_day_charges_new.charge_type = 2 THEN oil_org_day_charges_new.total_money ELSE 0 END ) AS rebate_money,
            sum( CASE WHEN oil_org_day_charges_new.charge_type = 3 THEN oil_org_day_charges_new.total_money ELSE 0 END ) AS credit_charge_money,
            sum( CASE WHEN oil_org_day_charges_new.charge_type = 4 THEN oil_org_day_charges_new.total_money ELSE 0 END ) AS credit_loan_money,
            sum( CASE WHEN oil_org_day_charges_new.charge_type = 5 THEN oil_org_day_charges_new.total_money ELSE 0 END ) AS substitute_money,
            sum( CASE WHEN oil_org_day_charges_new.charge_type = 8 THEN oil_org_day_charges_new.total_money ELSE 0 END ) AS self_credit_money
        ')
        ->filter($params)
            ->leftJoin('oil_org', function ($join) {
                $join->on('oil_org.orgcode', '=', 'oil_org_day_charges_new.orgroot')
                ->where('is_del', '=', 0);
            })
        ->groupBy('oil_org_day_charges_new.orgroot')
        ->orderBy('all_money', 'desc')
        ->skip($skip)
        ->take($limit)
        ->get();

        // 付款公司数量
        $payCompanyCounts = $this->getPayCompanyCount($list->pluck('orgroot'), $params);
        // 机构总交易量
        $allAmount = $this->getOrgAmount($params);

        $time = $this->getOrgCreateTime($list);

        return $list->map(function($item) use ($payCompanyCounts, $allAmount, $time, $params) {
            $item->date = $params['date'] . ' 至 ' . $params['date'];
            $item->org_name = $item->org_name ?? '-';
            $item->orgroot = $item->orgroot ?? '-';
            $item->company_count = isset($payCompanyCounts[$item->orgroot]) ? $payCompanyCounts[$item->orgroot] : 0;
            $item->money_rate = $this->formatRate($item->all_money, $allAmount);
            $item->createtime = isset($time[$item->orgroot]) ? $time[$item->orgroot]['min_createtime'] : '';

            $item->all_money            = $this->formatFloatNumber($item->all_money);
            $item->nomal_money          = $this->formatFloatNumber($item->nomal_money);
            $item->rebate_money         = $this->formatFloatNumber($item->rebate_money);
            $item->credit_charge_money  = $this->formatFloatNumber($item->credit_charge_money);
            $item->credit_loan_money    = $this->formatFloatNumber($item->credit_loan_money);
            $item->substitute_money     = $this->formatFloatNumber($item->substitute_money);
            $item->self_credit_money    = $this->formatFloatNumber($item->self_credit_money);

            return $item;
        });
    }

    /**
     * 获取机构创建时间
     * @param $data \Illuminate\Database\Eloquent\Collection
     * @return array
     */
    protected function getOrgCreateTime($data)
    {
        if ($data->isEmpty()) {
            return [];
        }

        $codes = [];
        foreach ($data as $item) {
            $len = strlen($item->orgroot);
            if (! isset($codes[$len])) {
                $codes[$len] = [];
            }

            $codes[$len][] = $item->orgroot;
        }

        $time = [];
        // 取客户创建时间、首次消费时间
        foreach ($codes as $len => $list) {
            $time += Org::getOrgEarliestCreateTimeAndFirstTradeTime($list, $len);
        }

        return $time;
    }

    /**
     * 获得机构的付款公司数量
     *
     * @param Collection $orgRoot
     * @return Collection
     */
    public function getPayCompanyCount(Collection $orgRoots, $params)
    {
        $time = date('Y-m-d H:i:s', strtotime( '+1 month', strtotime($params['date'])));

        $codes = [];
        foreach ($orgRoots as $item) {
            $len = strlen($item);
            if (! isset($codes[$len])) {
                $codes[$len] = [];
            }

            $codes[$len][] = $item;
        }

        $res = [];
        foreach ($codes as $len => $list) {
            $data = OilPayCompany::selectRaw('COUNT(*) as company_count, LEFT(orgroot, '. $len .') AS org_code')
                ->whereRaw("LEFT(orgroot, $len) IN ('". implode("','", $list) ."')")
                ->where('createtime', '<', $time)
                ->groupBy('org_code')
                ->get();

            foreach ($data as $v) {
                $res[$v->org_code] = $v->company_count;
            }
        }

        return $res;
    }

    /**
     * 获得所有机构的交易金额
     *
     * @param array $params
     * @return float
     */
    public function getOrgAmount(array $params) : float
    {
        return $this->model->selectRaw('sum( IF(charge_type = 2, 0, total_money) ) as all_amount')
        ->filter($params)
        ->first()->all_amount ?? 0;
    }

    /**
     * 付款公司统计
     *
     * @param array $params
     * @return array
     */
    public function payCompanyStatistic(array $params)
    {

        \helper::argumentCheck(['date', 'orgroot'], $params);

        $params['charge_type_neq'] = 2;

        $listData = $this->model->selectRaw('
                pay_company_id,
                pay_company_name,
                SUM(total_money) AS all_money
            ')
            ->filter($params)
            ->groupBy('pay_company_id')
            ->orderBy('all_money', 'desc')
            ->get();

        $time = date('Y-m-d H:i:s', strtotime( '+1 month', strtotime($params['date'])));
        $condition = [
            'createtimeLe' => $time,
            'orgroot_pre_like' => $params['orgroot'],
            '_export' => 1
        ];
        $company = OilPayCompany::getFilterList($condition);

        $allAmount = $listData->sum('all_money');

        $res = [];
        $i = 1;
        foreach ($listData as $item) {
            $tmp = [
                'index'             => $i++,
                'pay_company_id'    => $item->pay_company_id,
                'pay_company_name'  => $item->pay_company_name,
                'all_money'         => $this->formatFloatNumber($item->all_money),
                'money_rate'        => $this->formatRate($item->all_money, $allAmount)
            ];

            $res[$item->pay_company_id] = $tmp;
        }

        foreach ($company as $v) {
            if (isset($res[$v['id']]))
                continue;

            $tmp = [
                'index'             => $i++,
                'pay_company_id'    => $v['id'],
                'pay_company_name'  => $v['company_name'],
                'all_money'         => 0,
                'money_rate'        => '0%'
            ];
            $res[$v['id']] = $tmp;
        }

        return array_values($res);
    }

    /**
     * 比例格式
     *
     * @param float $dividend 被除数
     * @param float $divisor 除数
     * @return string
     */
    public function formatRate(float $dividend, float $divisor) : string
    {
        if (bccomp($divisor, 0, 2) == 0 || bccomp($dividend, 0, 2) == 0) {
            return '0%';
        }

        return sprintf('%.2f', round(bcmul(bcdiv($dividend, $divisor, 10), 100, 10), 2)) . '%';
    }

    public function formatFloatNumber($number, $digit=2)
    {
        return sprintf('%.'. $digit .'f', $number);
    }
}
