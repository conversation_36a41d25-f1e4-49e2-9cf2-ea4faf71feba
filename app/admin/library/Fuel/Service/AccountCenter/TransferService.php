<?php
/**
 * 转账工单相关
 * Created by PhpStorm.
 * User: kevin
 * Date: 2017/12/5
 * Time: 下午2:18
 */

namespace Fuel\Service\AccountCenter;


use Framework\Log;
use G7Pay\Core\AccountTransfer;
use Models\OilAccountMoney;
use Models\OilAccountMoneyTransfer;
use Models\OilOrg;

class TransferService extends BaseService implements InterfaceAccount
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @title   获取数据
     * @desc
     * @param array $ids
     * @return array
     * @returns
     * []
     * @returns
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function getData(array $ids)
    {
        $data = [];

        $records = OilAccountMoneyTransfer::getByIdList($ids, 'object');

        if ($records) {
            $data = $this->formatData($records);
        }

        return $data;
    }

    public function init(array $params)
    {
        $pageSize = 100;
        $totalPage = ********;
        for ($i = 0; $i < $totalPage; $i++) {
            $offset = $i * $pageSize;
            $records = OilAccountMoneyTransfer::Filter([
//                'no' => 'ZZ17121800001',
'status' => '1',
            ])->take($pageSize)->skip($offset)->get();
            if ($records && count($records) > 0) {
                $apiParams = $this->formatData($records);
                if ($apiParams) {
                    foreach ($apiParams as $v) {
                        //创建预转账单
                        $info = $this->transferReserve($v);
                        //预转账确认成功
                        $this->transferReserveSuccess([
                            'billID'      => $info->billID,
                            'totalAmount' => intval($v['totalAmount']),
                        ]);
                    }
                }
            } else {
                break;
            }
        }

        return '初始化完毕';
    }

    /**
     * @title   格式化参数
     * @desc
     * @param array $data
     * @return array
     * @returns
     * []
     * @returns
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    private function formatData($data)
    {
        $apiParams = $transferDetail = [];

        if ($data) {
            $account = [];
            $orgIdArr = [];
            foreach ($data as $v) {
                $orgIdArr[] = $v->org_id;
                $orgIdArr[] = $v->into_org_id;
            }
            $accountInfo = OilAccountMoney::getByOrgIdList($orgIdArr);
            foreach ($accountInfo as $v) {
                $account[$v->org_id] = ['cash' => $v->subAccountID, 'cashBack' => $v->backSubAccountID];
            }

            foreach ($data as $v) {
                $cashAmount = intval(bcmul($v->money - $v->use_fanli, 100));

                $baseInfo = [
                    'id'          => $v->id,
                    'billID'      => $v->billID,
                    'totalAmount' => abs(intval(bcmul($v->money, 100))),
                    'comment'     => $v->remark,
                    'extID'       => $v->sn,
                ];

                if ($cashAmount > 0) {
                    $transferDetail[] = [
                        'amount'          => $cashAmount,
                        'inSubAccountID'  => $account[$v->into_org_id]['cash'],
                        'outSubAccountID' => $account[$v->org_id]['cash'],
                    ];
                }

                //如果此账户初始化时发现返利余额有值就进行返利账户的充值
                if ($v->use_fanli > 0) {
                    $transferDetail[] = [
                        'amount'          => intval(bcmul($v->use_fanli, 100)),
                        'inSubAccountID'  => $account[$v->into_org_id]['cashBack'],
                        'outSubAccountID' => $account[$v->org_id]['cashBack'],
                    ];
                }

                $baseInfo['transferDetail'] = $transferDetail;

                $apiParams[] = $baseInfo;
            }
        }

        return $apiParams;
    }

    /**
     * 推送单条记录至G7Pay
     * @param $id
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function singleSend($id)
    {
        $data = [];

        $ids = is_array($id) ? $id : explode(",", $id);
        $apiParams = $this->getData($ids);

        try {
            //预处理
            if ($apiParams) {
                foreach ($apiParams as $v) {
                    //创建预转账单
                    $info = $this->transferReserve($v);
                    //预转账确认成功
                    $data[] = $this->transferReserveSuccess([
                        'billID'      => $info->billID,
                        'totalAmount' => intval($v['totalAmount']),
                    ]);
                }
            } else {
                throw new \RuntimeException(implode(",", $ids) . '转账单ID未找到工单信息', 2);
            }
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }

    /**
     * 按照转账单ID创建预转账单
     * @param $id
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function transferReserveById($id)
    {
        $data = [];

        $ids = is_array($id) ? $id : explode(",", $id);
        $apiParams = $this->getData($ids);

        try {
            //预处理
            if ($apiParams) {
                foreach ($apiParams as $v) {
                    //创建预转账单
                    $data[] = $this->transferReserve($v);
                }
            } else {
                throw new \RuntimeException(implode(",", $ids) . '转账单ID未找到工单信息', 2);
            }
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }

    /**
     * 创建转账单(涉及冻结流程)
     * @param array $params
     * @return mixed|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function transferReserve(array $params)
    {
        $data = NULL;
        try {
            $data = (new AccountTransfer())->transferReserve([
                'totalAmount'    => $params['totalAmount'],//转账金额
                'transferDetail' => $params['transferDetail'],
                'extID'          => $params['extID'],//外部单据号
            ]);
            if ($data && $data->billID) {
                OilAccountMoneyTransfer::edit(['id' => $params['id'], 'billID' => $data->billID]);
            }
        } catch (\Exception $e) {
            Log::error('创建预转账单:' . $e->getCode() . '--' . $e->getMessage(), [], 'accountException');
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        if ($data && isset($data->billID)) {
            try {
                (new AccountCheckService())->checkTransfer($params['billID'], '转账冻结');
            } catch (\Exception $e) {
                Log::error(strval($e), [], 'AccountCheck');
            }
        }

        return $data;
    }

    /**
     * 按照转账单ID-审核
     * @param $id
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function transferReserveSuccessById($id)
    {
        $data = [];

        $ids = is_array($id) ? $id : explode(",", $id);
        $apiParams = $this->getData($ids);

        try {
            //预处理
            if ($apiParams) {
                foreach ($apiParams as $v) {
                    //确认扣款
                    $data[] = $this->transferReserveSuccess($v);
                }
            } else {
                throw new \RuntimeException(implode(',', $ids) . '转账单ID未找到工单信息', 2);
            }
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }

    /**
     * 预转账确认成功
     * @param array $params
     * @return mixed|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function transferReserveSuccess(array $params)
    {
        $data = NULL;
        Log::error('转账确认成功参数：' . var_export($params, TRUE), [], 'transferService');

        if (!isset($params['billID']) || !$params['billID']) {
            return $data;
        }

        try {
            $data = (new AccountTransfer())->transferReserveSuccess(
                [
                    'billID'      => $params['billID'],//支付平台分配单据
                    'totalAmount' => $params['totalAmount'],//转账金额
                    'comment'     => isset($params['remark_work']) ? $params['remark_work'] : '',
                ]
            );
        } catch (\Exception $e) {
            Log::error('预转账单确认成功:' . $e->getCode() . '--' . $e->getMessage(), [], 'accountException');
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        try {
            (new AccountCheckService())->checkTransfer($params['billID'], '转账扣款');
        } catch (\Exception $e) {
            Log::error(strval($e), [], 'AccountCheck');
        }

        return $data;
    }

    /**
     * @title   按照转账单ID-驳回/删除转账
     * @desc
     * @param $id
     * @return array
     * @returns
     * []
     * @returns
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function transferReserveFailById($id)
    {
        $data = [];

        $ids = is_array($id) ? $id : explode(",", $id);
        $apiParams = $this->getData($ids);

        try {
            //预处理
            if ($apiParams) {
                foreach ($apiParams as $v) {
                    //确认扣款
                    $data[] = $this->transferReserveFail($v);
                }
            } else {
                throw new \RuntimeException(implode(',', $ids) . '转账单ID未找到工单信息', 2);
            }
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }

    /**
     * @title 预转账确认失败
     * @desc
     * @param array $params
     * @return mixed
     * @throws AccountException
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     */
    public function transferReserveFail(array $params)
    {
        $data = NULL;
        try {
            $data = (new AccountTransfer())->transferReserveFail(
                [
                    'billID'      => $params['billID'],//支付平台分配单据
                    'totalAmount' => $params['totalAmount'],//转账金额
                    'comment'     => isset($params['remark_work']) ? $params['remark_work'] : '',
                ]
            );

        } catch (\Exception $e) {
            Log::error('预转账单确认失败:' . $e->getCode() . '--' . $e->getMessage(), [], 'accountException');
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }

    /**
     * @title 查询转账单
     * @desc
     * @param array $params
     * @return mixed
     * @throws AccountException
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     */
    public function getList(array $params)
    {
        try {
            $data = (new AccountTransfer())->getList($params);
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }

    /**
     * @title 众邦分配，直接转账接口
     * @desc
     * @param array $params
     * @return mixed
     * @throws AccountException
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     */
    public function transferDirectZbank(array $params)
    {
        try {
            $data = (new AccountTransfer())->transferDirectFee($params);
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }

    /**
     * @title 众邦分配，转账单撤销
     * @desc
     * @param array $params
     * @return mixed
     * @throws AccountException
     * @returns
     * []
     * @returns
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     */
    public function transferRevokeZbank(array $params)
    {
        try {
            $data = (new AccountTransfer())->transferRevoke($params);
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }


    /**
     * @title   按转账单ID销审
     * @desc
     * @param $id
     * @return array
     * @returns
     * []
     * @returns
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function revokeById($id)
    {
        $data = [];

        $ids = is_array($id) ? $id : explode(",", $id);
        $apiParams = $this->getData($ids);

        try {
            //预处理
            if ($apiParams) {
                foreach ($apiParams as $v) {
                    //确认扣款
                    $data[] = $this->revoke($v);
                }
            } else {
                throw new \RuntimeException(implode(',', $ids) . '转账单ID未找到工单信息', 2);
            }
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }

    /**
     * @title   销审
     * @desc
     * @param $params
     * @return mixed|null
     * @returns
     * []
     * @returns
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function revoke($params)
    {
        $data = NULL;
        try {
            $data = (new AccountTransfer())->revoke(
                [
                    'billID'      => $params['billID'],//支付平台分配单据
                    'totalAmount' => abs($params['totalAmount']),//转账金额
                    'comment'     => isset($params['remark_work']) ? $params['remark_work'] : '',
                ]
            );

        } catch (\Exception $e) {
            Log::error('预转账单销审失败:' . $e->getCode() . '--' . $e->getMessage(), [], 'accountException');
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }


    /*
     * 创建转账单+审核
     */
    public function createTransfer($from_org_id,$into_org_id,$money)
    {
        global $app;
        if(!$app->myAdmin || !$app->myAdmin->id){
            $app->myAdmin = new \stdClass();
            $app->myAdmin->id = 8888;
            $app->myAdmin->true_name = '系统自动';
        }

        $from_org_info = OilOrg::getById(['id'=>$from_org_id]);
        $into_org_info = OilOrg::getById(['id'=>$into_org_id]);
        $from_account_info = OilAccountMoney::getByOrgIdForLock(['org_id'=>$from_org_id]);
        $into_account_info = OilAccountMoney::getByOrgIdForLock(['org_id'=>$into_org_id]);

        $resultsRe = [
            'no'               => OilAccountMoneyTransfer::createNo('ZZ'),
            'sn'               => \GosSDK\Lib\Helper::uuid(),
            'no_type'          => 'ZZ',
            'org_id'           => $from_org_id,
            'into_org_id'      => $into_org_id,
            'from_orgname'     => $from_org_info->org_name,
            'into_orgname'     => $into_org_info->org_name,
            'from_orgcode'     => $from_org_info->orgcode,
            'into_orgcode'     => $into_org_info->orgcode,
            'from_account_no'  => $from_account_info->account_no ? $from_account_info->account_no : null,
            'into_account_no'  => $into_account_info->account_no ? $into_account_info->account_no : null,
            'status'           => 0,
            'app_time'         => \helper::nowTime(),
            'data_from'        => 1,
            'use_fanli'        => 0,
            'money'            => $money,
            'remark'           => $from_org_info->org_name.'来款转账'.$into_org_info->org_name,
            'remark_work'      => $from_org_info->org_name.'来款转账'.$into_org_info->org_name,
            'creator_id'       => $app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'other_creator_id' => '',
            'other_creator'    => '',
            'last_operator_id' => $app->myAdmin->id ,
            'last_operator'    => $app->myAdmin->true_name,
            'updatetime'       => \helper::nowTime(),
        ];

        $result = OilAccountMoneyTransfer::add($resultsRe);
        if (!$result) {
            throw new \RuntimeException('创建转账申请单失败', 7);
        }

        //添加工单日志
        \Models\OilCardViceAppLog::add([
            'type'             => 5,
            'app_id'           => $result->id,
            'status'           => 0,
            'status_name'      => '待审核',
            'last_operator'    => $app->myAdmin->true_name,
            'last_operator_id' => $app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'updatetime'       => \helper::nowTime(),
        ]);

        //审核，账户相关操作
        $res = \Fuel\Service\AccountTransfer::transferMoney($result);

        //修改工单
        $result_edit = OilAccountMoneyTransfer::edit([
            'id' => $result->id,
            'status' => 1,
            'audit_time'    =>  date("Y-m-d H:i:s"),
            'use_fanli' => $res['use_fanli'],
            'last_operator_id' => $app->myAdmin->id,
            'last_operator'    => $app->myAdmin->true_name,
            'updatetime'       => \helper::nowTime()]);
        if (!$result_edit) {
            throw new \RuntimeException('更新转账申请单失败', 7);
        }

        //添加工单日志
        \Models\OilCardViceAppLog::add([
            'type'             => 5,
            'app_id'           => $result->id,
            'status'           => 1,
            'status_name'      => '已审核',
            'last_operator'    => $app->myAdmin->true_name,
            'last_operator_id' => $app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'updatetime'       => \helper::nowTime(),
        ]);

        return $result;
    }
}