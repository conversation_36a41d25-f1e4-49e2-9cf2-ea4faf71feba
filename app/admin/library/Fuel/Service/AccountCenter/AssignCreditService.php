<?php
/**
 * 转账工单相关
 * Created by PhpStorm.
 * User: kevin
 * Date: 2017/12/5
 * Time: 下午2:18
 */

namespace Fuel\Service\AccountCenter;


use Framework\Log;
use G7Pay\Core\AccountCredit;
use Models\OilAccountAssign;
use Models\OilCreditAccount;
use Models\OilCreditRepay;

class AssignCreditService extends BaseService implements InterfaceAccount
{
    private $exception = 1;

    public function __construct()
    {
        parent::__construct();
        return;
    }

    public function init(array $params)
    {

    }

    /**
     * @title 机构授信
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @throws AccountException
     * @returns
     * []
     * @returns
     */
    public function create(array $data)
    {
        //return;
        $res = NULL;
        Log::error('createAssign:' . var_export($data, TRUE), [], 'G7PayAbstract');

        try {
            //预分配申请
            $res = (new AccountCredit())->createForCredit($data);

            Log::error('initSend:create:' . var_export($res, TRUE), [], 'payCenter');
            if (!$res && !isset($res->billID) && !$res->billID) {
                AccountException::handler('创建预分配单返回值不符合预期', 2, var_export($res, TRUE),$this->exception);
            }

            //回写billId到分配单
            OilAccountAssign::edit([
                'id'     => $data['id'],
                'billID' => $res->billID,
            ]);
        } catch (\Exception $e) {
            Log::error('授信-预分配:' . $e->getCode() . '--' . $e->getMessage(), [], 'accountException');
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e),$this->exception);
        }

        if ($res) {
            //账户检测
            try {
                (new AccountCheckService())->checkAssign($res->billID, '授信冻结');
            } catch (\Exception $e) {
                Log::error(strval($e), [], 'AccountCheck');
            }
        }

        return $res;
    }

    public function create0(array $data)
    {
        //return;
        $res = NULL;
        Log::error('createAssign:' . var_export($data, TRUE), [], 'G7PayAbstract');

        try {
            //预分配申请
            $res = (new AccountCredit())->createForCredit($data);

            Log::error('initSend:create:' . var_export($res, TRUE), [], 'payCenter');
            if (!$res && !isset($res->billID) && !$res->billID) {
                AccountException::handler('创建预分配单返回值不符合预期', 2, var_export($res, TRUE),$this->exception);
            }

            //回写billId到分配单
            OilAccountAssign::edit([
                'id'     => $data['id'],
                'billID' => $res->billID,
            ]);
        } catch (\Exception $e) {
            Log::error('授信-预分配:' . $e->getCode() . '--' . $e->getMessage(), [], 'accountException');
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e),$this->exception);
        }

        if ($res) {
            //账户检测
            try {
                (new AccountCheckService())->checkAssign($res->billID, '授信冻结');
            } catch (\Exception $e) {
                Log::error(strval($e), [], 'AccountCheck');
            }
        }

        return $res;
    }

    /**
     * @title   授信账户确认扣款
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed|null
     * @returns
     * []
     * @returns
     */
    public function doSuccess(array $params)
    {
        //return;
        $res = NULL;
        try {
            $res = (new AccountCredit())->successForCredit([
                'totalAmount' => abs($params['totalAmount']),
                'billID'      => $params['billID'],
            ]);
            Log::error('initSend:doSuccess:' . var_export($res, TRUE), [], 'payCenter');
        } catch (\Exception $e) {
            Log::error('授信-预分配:' . $e->getCode() . '--' . $e->getMessage(), [], 'accountException');
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e),$this->exception);
        }

        //账户检测
        try {
            (new AccountCheckService())->checkAssign($params['billID'], '授信账户确认扣款');
        } catch (\Exception $e) {
            Log::error(strval($e), [], 'AccountCheck', '授信账户确认扣款');
        }

        return $res;
    }

    public function doSuccess0(array $params)
    {
        //return;
        $res = NULL;
        try {
            $res = (new AccountCredit())->successForCredit([
                'totalAmount' => abs($params['totalAmount']),
                'billID'      => $params['billID'],
            ]);
            Log::error('initSend:doSuccess:' . var_export($res, TRUE), [], 'payCenter');
        } catch (\Exception $e) {
            Log::error('授信-预分配:' . $e->getCode() . '--' . $e->getMessage(), [], 'accountException');
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e),$this->exception);
        }

        //账户检测
        try {
            (new AccountCheckService())->checkAssign($params['billID'], '授信账户确认扣款');
        } catch (\Exception $e) {
            Log::error(strval($e), [], 'AccountCheck', '授信账户确认扣款');
        }

        return $res;
    }

}