<?php
/**
 * 运营商相关
 * Created by PhpStorm.
 * User: kevin
 * Date: 2017/12/5
 * Time: 下午2:20
 */

namespace Fuel\Service\AccountCenter;


use G7Pay\Core\Merchant;
use Models\OilOperators;

class MerchantService extends BaseService implements InterfaceAccount
{
    private $fieldsMap = [
        'id'    =>  'id',
        'name'         => 'name',
        'company_name' => 'company',
        'sub_name'     => 'address',
        'code'         => 'email',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @title
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $params
     * @returns
     * []
     * @returns
     */
    private function formatUnique(array $params)
    {
        $data = [];
        foreach ($params as $k => $v) {
            if (isset($this->fieldsMap[$k])) {
                if ($k == 'sub_name'){
                    $data['address'] = md5($params['company_name']);
                }elseif($k == 'code'){
                    $data['email'] = md5($params['company_name'].$params['bank']).'@fuel.huoyunren.com';
                }else{
                    $data[$this->fieldsMap[$k]] = $v;
                }
            }
        }

        return $data;
    }

    /**
     * @title   初始化
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $params
     * @returns
     * []
     * @returns
     */
    public function init(array $params)
    {
        $records = OilOperators::getList(['_export' => 1,'merchantIDNull'=>1]);

        if ($records && count($records) > 0) {
            $records = $records->toArray();
            foreach ($records as $v) {
                $data[] = $this->create($v);
            }
        }
    }

    /**
     * @title   根据运营商ID创建运营商到账户中心
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $id
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function createMerchantById($id)
    {
        $operator = OilOperators::getById(['id' => $id])->toArray();

        return $this->create($operator);
    }

    /**
     * @title   创建运营商
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function create(array $params)
    {
        try {
            $apiParams = $this->formatUnique($params);
            $id = $apiParams['id'];
            unset($apiParams['id']);

            $data = (new Merchant())->create($apiParams);
            if(isset($data->merchantID) && $data->merchantID){
                OilOperators::edit(
                    [
                        'id'    =>  $id,
                        'merchantID'    =>  $data->merchantID
                    ]
                );
            }else{
                AccountException::handler('创建商户返回值不符合预期', 2,var_export($data,true));
            }
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(),$e->getCode(),strval($e));
        }

        return $data;
    }

    /**
     * @title
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $merchantId
     * @returns
     * []
     * @returns
     */
    public function getByMerchantId($merchantId)
    {
        try {
            $data = (new Merchant())->getById(['merchantID' => $merchantId]);
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(),$e->getCode(),strval($e));
        }

        return $data;
    }

    /**
     * @title   根据email获取运营商
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $email
     * @return mixed
     * @throws AccountException
     * @returns
     * []
     * @returns
     */
    public function getByEmail($email)
    {
        $data = FALSE;
        try {
            $data = (new Merchant())->getByEmail(['email' => $email]);
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(),$e->getCode(),strval($e));
        }

        return $data;
    }
}