<?php
/**
 * 油卡分配相关
 * Created by PhpStorm.
 * User: kevin
 * Date: 2017/12/5
 * Time: 下午2:18
 */

namespace Fuel\Service\AccountCenter;


use Framework\Log;
use Fuel\Defines\AccountType;
use G7Pay\Core\AccountCredit;
use G7Pay\Core\AssignOrder;
use Models\OilAccountAssign;
use Models\OilAccountMoney;
use Models\OilCardAccount;
use Models\OilCreditAccount;
use Models\OilOperators;
use Models\OilOrg;

class AssignService extends BaseService implements InterfaceAccount
{
    public function __construct()
    {
        parent::__construct();
    }

    public function getById($id)
    {
        return OilAccountAssign::where('id', $id)
            //->whereIn('account_type',[AccountType::CASH])
            ->with(
            [
                'AssignDetail' => function ($query) {
                    return $query->select('id', 'assign_id', 'vice_id', 'org_name', 'assign_money as amount', 'use_fanli_remain', 'truck_no');
                },
            ])
            ->select('id', 'billID', 'money_total as totalAmount', 'org_id', 'org_name', 'sn as extID', 'jifen_total',
                'account_type', 'account_no', 'apply_time', 'use_cash_fanli', 'remark_work as comment')
            ->get()
            ->toArray();
    }

    /**
     * @title 每天初始化昨天的分配记录到支付中心
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed|null
     * @throws AccountException
     * @returns
     * []
     * @returns
     */
    public function init(array $params)
    {
        //获取昨天分配成功的分配单数据
        $pageSize = 100;
        for ($page = 1; $page < 10000; $page++) {
            $data = OilAccountAssign::orderBy('id', 'asc')
                //->where('createtime','>=',date('Y-m-d',strtotime("-1 day")).' 00:00:00')
                //->where('createtime','<=',date('Y-m-d',strtotime("-1 day")).' 23:59:00')
                ->where('status', 1)
                ->with(
                    [
                        'AssignDetail' => function ($query) {
                            return $query->select('assign_id', 'vice_id', 'assign_money as amount', 'use_fanli_remain');
                        },
                    ])
                ->whereIn('no', ['FP171219298342'])
                ->select('id', 'money_total as totalAmount', 'org_id', 'sn as extID', 'jifen_total', 'account_type', 'account_no')
                ->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get()
                ->toArray();

            if (!$data) {
                echo 'no data';
                break;
            }

            try {

                //预处理
                $data = self::formatData($data);
                $this->initSend($data);
            } catch (\Exception $e) {
                AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
            }
        }


        return TRUE;
    }

    /**
     * @title
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $id
     * @throws AccountException
     * @returns
     * []
     * @returns
     */
    public function singleSend($id)
    {
        Log::info('$id--' . $id, [], 'assignService');
        $data = $this->getById($id);
        Log::info('$data--' . var_export($data, TRUE), [], 'assignService');
        try {
            //预处理
            $data = self::formatData($data);
            $this->initSendForNoFrozen($data);
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return TRUE;
    }

    /**
     * @title 格式化数据格式
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $data
     * @return array
     * @returns
     * []
     * @returns
     */
    public function formatData($data)
    {
        $vice_ids = $org_ids = $subCreditAccountMap = $subCashAccountMap = $subCashBackAccountMap = $credit_account_nos = $useFanliOrg = [];

        $accountNoArr = [];
        if ($data) {
            foreach ($data as $key => $v) {
                $accountNoArr[$v['account_type']][] = $v['account_no'];

                switch ($v['account_type']) {
                    case AccountType::CREDIT:
                        $creditAccountNoArr[] = $v['account_no'];
                        break;
                    case AccountType::CASH:
                        //返现账户
                        if ($v['use_cash_fanli'] > 0) {
                            $fanLiAccountNoArr[] = $v['org_id'];
                        }
                        $cash_org_ids[] = $v['org_id'];

                        break;
                    case AccountType::SCORE:
                        $pointsAccountNoArr[] = $v['account_no'];
                        break;
                    default:

                }

                if ($v['assign_detail']) {
                    foreach ($v['assign_detail'] as $value) {
                        $vice_ids[] = $value['vice_id'];
                    }
                }
            }

            //查找现金机构信息
            if ($cash_org_ids) {
                $subCashAccountMap = OilAccountMoney::getSubAccountMap(array_unique($cash_org_ids));
            }
            //查找返现机构信息
            if ($fanLiAccountNoArr) {
                $subCashBackAccountMap = OilAccountMoney::getBackSubAccountMap(array_unique($fanLiAccountNoArr));
            }
            //查找授信机构信息
            if ($creditAccountNoArr) {
                $subCreditAccountMap = OilCreditAccount::getSubAccountMap(array_unique($creditAccountNoArr));
            }
            //查找积分机构信息 todo
            if ($pointsAccountNoArr) {
//                $subPointAccountMap = OilAccountMoney::getSubAccountMap(array_unique($pointsAccountNoArr));
            }

            //查找卡账号信息
            $cardAccountMap = OilCardAccount::getCardAccountMap(array_unique($vice_ids));

            //拼接支付平台数据
            foreach ($data as $k => &$v) {
                switch ($v['account_type']) {
                    case AccountType::CREDIT:
                        $v = $this->formatForCreditAssignDetails($v, $subCreditAccountMap[$v['account_no']], $cardAccountMap);
                        break;
                    case AccountType::CASH://现金、返利分配
                        if (isset($subCashAccountMap[$v['org_id']]) && $subCashAccountMap[$v['org_id']]) {
                            $v = $this->formatCashAssignDetails($v, $subCashAccountMap[$v['org_id']], $cardAccountMap, $subCashBackAccountMap);
                        } else {
                            AccountException::handler($v['org_id'] . '此机构id不存在subAccountId', 2);
                        }
                        break;
                    case AccountType::SCORE://积分分配
//                        $pointsAccountNoArr[] = $v['account_no'];
                        continue;

                        break;
                    default:

                }
            }
        }
        
        return $data;
    }

    private function formatForCreditAssignDetails($item, $creditAccountId, $cardAccountMap)
    {
        $item['subAccountID'] = $creditAccountId;
        $item['totalAmount'] = 0;
        $item['capitalDetail'] = [];

        if ($item['assign_detail']) {
            //查询机构运营商
            $orgInfo = OilOrg::getById(['id' => $item['org_id']]);


            if (!$orgInfo) {
                AccountException::handler($item['org_id'] . '未找到', 2);
            }

            //g7Pay credit account
            //@todo 本地已无授信账户ID，待完善
            if (!$item['subAccountID']) {
                $accountRecord = (new AccountService())->getBalanceByOrgCode(['orgCode' => $orgInfo->orgcode]);
                if ($accountRecord && isset($accountRecord->data)) {
                    foreach ($accountRecord->data as $v) {
                        if ($v->subAccountType == AccountType::ACCOUNT_CENTER_CREDIT_GLP) {
                            $item['subAccountID'] = $v->subAccountID;
                            OilCreditAccount::updateByAccountNo(['account_no' => $item['account_no'], 'subAccountID' => $v->subAccountID]);
                            break;
                        }
                    }
                }
            }

            if (!$item['subAccountID']) {
                AccountException::handler($item['account_no'] . '授信账户未找到', 2);
            }

            if (!$orgInfo->operators_id) {
                AccountException::handler($item['org_id'] . '未找到机构运营商', 2);
            }
            $operatorInfo = OilOperators::getById(['id' => $orgInfo->operators_id]);

            foreach ($item['assign_detail'] as &$value) {
                //是否存在卡账户
                if (!isset($cardAccountMap['CASH']) || !$cardAccountMap['CASH'][$value['vice_id']]) {
                    AccountException::handler($value['vice_id'] . '此卡id不存在cardSubAccountID', 2,'',1);
                }

                //重新计算totalAmount 绝对值之和
                $item['totalAmount'] += abs(intval(bcmul($value['amount'], 100)));

                $cardSubAccountID = $cardAccountMap['CASH'][$value['vice_id']];
                $item['capitalDetail'][] = [
                    'amount'           => abs(intval(bcmul($value['amount'], 100))),//分配额度
                    'cardSubAccountID' => $cardSubAccountID,//支付平台卡子账户ID
                    'applyTime'        => date('c', strtotime($item['apply_time'])),//申请时间(yyyy-MM-dd HH:mm:ss)
                    'orgBelong'        => $value['org_name'],//所属机构
                    'loaner'           => $operatorInfo->company_name,//债权人
                    'orgPay'           => $item['org_name'],//付款机构
                    'platNumber'       => $item['truck_no'] ? $item['truck_no'] : '',//车牌号
                ];
            }
            unset($item['assign_detail']);
        }

        return $item;
    }

    /**
     * @title   现金、返利账户分配明细
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $v
     * @param $cashAccountId
     * @param $cardAccountMap
     * @param $subCashBackAccountMap
     * @return mixed
     * @returns
     * []
     * @returns
     */
    private function formatCashAssignDetails($v, $cashAccountId, $cardAccountMap, $subCashBackAccountMap)
    {
        $v['totalAmount'] = 0;
        if ($v['assign_detail']) {

            $assignType = $v['jifen_total'] > 0 ? 'POINT' : 'CASH';
            foreach ($v['assign_detail'] as &$value) {
                //是否存在卡账户
                if (!isset($cardAccountMap[$assignType][$value['vice_id']]) || !$cardAccountMap[$assignType][$value['vice_id']]) {
                    AccountException::handler($value['vice_id'] . '此卡id不存在cardSubAccountID', 2,'',1);
                }

                //重新计算totalAmount 绝对值之和
                $v['totalAmount'] += abs(intval(bcmul($value['amount'], 100)));

                $cardSubAccountID = $cardAccountMap[$assignType][$value['vice_id']];
                $use_fanli_remain = intval(bcmul($value['use_fanli_remain'], 100));
                //使用返利
                if ($use_fanli_remain != 0) {
                    if (!isset($subCashBackAccountMap[$v['org_id']])) {
                        AccountException::handler($v['org_id'] . '此机构不存在返现账户', 2);
                    }

                    $v['allocDetail'][] = [
                        'cardSubAccountID' => $cardSubAccountID,
                        'subAccountID'     => $subCashBackAccountMap[$v['org_id']],
                        'transferType'     => $use_fanli_remain > 0 ? 'USER2CARD' : 'CARD2USER',
                        'amount'           => abs($use_fanli_remain),
                        'originalAmount'   => $use_fanli_remain,
                    ];
                }
                //如果 现金 != 返利
                $amountDiff = intval(bcmul($value['amount'], 100)) - $use_fanli_remain;
                if ($amountDiff != 0) {
                    $v['allocDetail'][] = [
                        'cardSubAccountID' => $cardSubAccountID,
                        'subAccountID'     => $cashAccountId,
                        'transferType'     => $amountDiff > 0 ? 'USER2CARD' : 'CARD2USER',
                        'amount'           => abs($amountDiff),
                        'originalAmount'   => $amountDiff,
                    ];
                }

                unset($value['assign_id'], $value['vice_id']);
            }

            if ($v['allocDetail']) {
                $v['allocDetail'] = $this->sortByFiled($v['allocDetail'], 'originalAmount');
            }
        }

        return $v;
    }

    public function sortByFiled($data, $sortField, $sortType = SORT_ASC)
    {
        if ($data) {
            $temp_array = [];
            foreach ($data as $key => $value) {
                $temp_array[$key] = $value[$sortField];
            }

            array_multisort($temp_array, $sortType, $data);
        }

        return $data;
    }

    private function formatPointsAssignDetails($item, $accountMap)
    {
        $totalAmount = 0;
        foreach ($item['details'] as $v) {

        }
    }

    /**
     * @title 初始化昨天数据发送
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $data
     * @return mixed|null
     * @throws AccountException
     * @returns
     * []
     * @returns
     */
    public function initSend(array $data)
    {
        Log::info('$data--' . var_export($data, TRUE), [], 'assignService');
        try {
            //请求账户中心
            if ($data) {
                foreach ($data as $v) {
                    $res = $this->create($v);
                    $this->doSuccess(['totalAmount' => $v['totalAmount'], 'billID' => $res->billID, 'account_type' => $v['account_type']]);
                }
            }
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $res;
    }

    /**
     * @title 初始化昨天数据通过直接分配（非冻结）发送
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $data
     * @return mixed|null
     * @throws AccountException
     * @returns
     * []
     * @returns
     */
    public function initSendForNoFrozen(array $data)
    {
        Log::info('$data--' . var_export($data, TRUE), [], 'assignService');
        try {
            //请求账户中心
            if ($data) {
                foreach ($data as $v) {
                    $res = $this->createForNoFrozen($v);
                }
            }
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $res;
    }

    /**
     * @title   按分配单id创建预分配单
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $assignId
     * @return array|null
     * @returns
     * []
     * @returns
     */
    public function createById($assignId)
    {
        $result = NULL;
        Log::error('createById:' . var_export($assignId, TRUE), [], 'G7PayAbstractERROR');

        $data = $this->getById($assignId);

        Log::error('createById:$data' . var_export($data, TRUE), [], 'G7PayAbstractERROR');

        if ($data) {
            try {
                //预处理
                $data = self::formatData($data);

                Log::error('createById:$data2' . var_export($data, TRUE), [], 'G7PayAbstractERROR');

                if ($data) {
                    foreach ($data as $v) {
                        if (in_array($v['account_type'], [AccountType::CASH])) { //, AccountType::SCORE
                            $result[] = $this->create($v);
                        } elseif (in_array($v['account_type'], [AccountType::CREDIT])) {
                            $result[] = (new AssignCreditService())->create($v);
                        }
                    }
                }
            } catch (\Exception $e) {
                AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
            }
        }

        Log::error('createById:$result' . var_export($result, TRUE), [], 'G7PayAbstractERROR');

        return $result;
    }

    public function createById0($assignId)
    {
        $result = NULL;
        Log::error('createById:' . var_export($assignId, TRUE), [], 'G7PayAbstractERROR');

        $data = $this->getById($assignId);

        Log::error('createById:$data' . var_export($data, TRUE), [], 'G7PayAbstractERROR');

        if ($data) {
            try {
                //预处理
                $data = self::formatData($data);

                Log::error('createById:$data2' . var_export($data, TRUE), [], 'G7PayAbstractERROR');

                if ($data) {
                    foreach ($data as $v) {
                        if (in_array($v['account_type'], [AccountType::CASH])) { //, AccountType::SCORE
                            $result[] = $this->create($v);
                        } elseif (in_array($v['account_type'], [AccountType::CREDIT])) {
                            $result[] = (new AssignCreditService())->create0($v);
                        }
                    }
                }
            } catch (\Exception $e) {
                AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
            }
        }

        Log::error('createById:$result' . var_export($result, TRUE), [], 'G7PayAbstractERROR');

        return $result;
    }

    /**
     * @title 预分配申请
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $data
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function create(array $data)
    {
        $res = NULL;
        Log::error('createAssign:' . var_export($data, TRUE), [], 'G7PayAbstract');

        try {
            if (in_array($data['account_type'], [AccountType::CASH])) { //, AccountType::SCORE
                //预分配申请
                $res = (new AssignOrder())->cardAllocReserve(
                    [
                        'extID'       => $data['extID'],
                        'totalAmount' => $data['totalAmount'],
                        'allocDetail' => $data['allocDetail'],
                    ]
                );

                Log::error('initSend:create:' . var_export($res, TRUE), [], 'payCenter');
                if (!$res && !isset($res->billID) && !$res->billID) {
                    AccountException::handler('创建预分配单返回值不符合预期', 2, var_export($res, TRUE));
                }

                //回写billId到分配单
                OilAccountAssign::edit([
                    'id'     => $data['id'],
                    'billID' => $res->billID,
                ]);

                if ($res) {
                    //账户检测
                    try {
                        (new AccountCheckService())->checkAssign($res->billID, '冻结');
                    } catch (\Exception $e) {
                        Log::error(strval($e), [], 'AccountCheck');
                    }
                }

            } elseif (in_array($data['account_type'], [AccountType::CREDIT])) {
                $res = (new AssignCreditService())->create($data);
            }

        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $res;
    }

    /**
     * @title 直接分配申请(非冻结)
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $data
     * @return mixed|null
     * @throws AccountException
     * @returns
     * []
     * @returns
     */
    public function createForNoFrozen(array $data)
    {
        $res = NULL;
        Log::error('createAssign:' . var_export($data, TRUE), [], 'G7PayAbstract');

        try {
            if (in_array($data['account_type'], [AccountType::CASH])) { //, AccountType::SCORE
                //直接分配申请（非冻结）
                $res = (new AssignOrder())->cardAlloc(
                    [
                        'extID'       => $data['extID'],
                        'totalAmount' => $data['totalAmount'],
                        'allocDetail' => $data['allocDetail'],
                    ]
                );

                Log::error('initSend:create:' . var_export($res, TRUE), [], 'payCenter');
                if (!$res && !isset($res->billID) && !$res->billID) {
                    AccountException::handler('创建预分配单返回值不符合预期', 2, var_export($res, TRUE));
                }

                //回写billId到分配单
                OilAccountAssign::edit([
                    'id'     => $data['id'],
                    'billID' => $res->billID,
                ]);

                if ($res) {
                    //账户检测
                    try {
                        (new AccountCheckService())->checkAssign($res->billID, '冻结');
                    } catch (\Exception $e) {
                        Log::error(strval($e), [], 'AccountCheck');
                    }
                }

            } elseif (in_array($data['account_type'], [AccountType::CREDIT])) {
                $res = (new AssignCreditService())->create($data);
            }

        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $res;
    }

    /**
     * @title 预分配单成功
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    private function doSuccess(array $params)
    {
        $res = NULL;
        try {
            if (in_array($params['account_type'], [AccountType::CASH])) { //, AccountType::SCORE
                $res = (new AssignOrder())->success([
                    'totalAmount' => abs($params['totalAmount']),
                    'billID'      => $params['billID'],
                ]);

                //账户检测
                try {
                    (new AccountCheckService())->checkAssign($params['billID'], '确认扣款');
                } catch (\Exception $e) {
                    Log::error(strval($e), [], 'AccountCheck', '确认扣款');
                }
            } elseif (in_array($params['account_type'], [AccountType::CREDIT])) {
                $res = (new AssignCreditService())->doSuccess($params);
            }
            Log::error('initSend:doSuccess:' . var_export($res, TRUE), [], 'payCenter');
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $res;
    }

    /**
     * @title   预分配单确认成功-等价于审核
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $assignId
     * @return array|null
     * @returns
     * []
     * @returns
     */
    public function doSuccessById($assignId)
    {
        $result = NULL;

        $data = $this->getById($assignId);
        if ($data) {
            try {
                //预处理
                $data = self::formatData($data);
                if ($data) {
                    foreach ($data as $v) {
                        if (in_array($v['account_type'], [AccountType::CASH])) { //, AccountType::SCORE
                            $result[] = $this->doSuccess($v);
                        } elseif (in_array($v['account_type'], [AccountType::CREDIT])) {
                            $result[] = (new AssignCreditService())->doSuccess($v);
                        }
                    }
                }
            } catch (\Exception $e) {
                AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
            }
        }

        return $result;
    }

    public function doSuccessById0($assignId)
    {
        $result = NULL;

        $data = $this->getById($assignId);
        if ($data) {
            try {
                //预处理
                $data = self::formatData($data);
                if ($data) {
                    foreach ($data as $v) {
                        if (in_array($v['account_type'], [AccountType::CASH])) { //, AccountType::SCORE
                            $result[] = $this->doSuccess($v);
                        } elseif (in_array($v['account_type'], [AccountType::CREDIT])) {
                            $result[] = (new AssignCreditService())->doSuccess0($v);
                        }
                    }
                }
            } catch (\Exception $e) {
                AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
            }
        }

        return $result;
    }

    /**
     * @title   分配单按ID确认失败-等价于删单
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $assignId
     * @return array|null
     * @returns
     * []
     * @returns
     */
    public function doFailById($assignId)
    {
        $result = NULL;

        $data = $this->getById($assignId);
        if ($data) {
            try {
                //预处理
                $data = self::formatData($data);
                if ($data) {
                    foreach ($data as $v) {
                        $result[] = $this->doFail($v);
                    }
                }
            } catch (\Exception $e) {
                AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
            }
        }

        return $result;
    }

    /**
     * @title 分配撤销
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    private function revoke(array $params)
    {
        Log::error('revoke:' . var_export($params, TRUE), [], 'G7PayAbstractERROR');
        $res = NULL;
        try {
            $res = (new AssignOrder())->revoke([
                'totalAmount' => $params['totalAmount'],
                'billID'      => $params['billID'],
                'comment'     => $params['comment'],
            ]);

            $this->createById($params['id']);

        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        //账户检测
        try {
            (new AccountCheckService())->checkAssign($params['billID'], '销审退款');
        } catch (\Exception $e) {
            Log::error(strval($e), [], 'AccountCheck');
        }

        return $res;
    }

    /**
     * @title   按分配单id销审
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param $assignId
     * @return array|null
     * @returns
     * []
     * @returns
     */
    public function revokeById($assignId)
    {
        $result = NULL;

        $data = $this->getById($assignId);
        if ($data) {
            try {
                //预处理
                $data = self::formatData($data);
                if ($data) {
                    foreach ($data as $v) {
                        $result[] = $this->revoke($v);
                    }
                }
            } catch (\Exception $e) {
                AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
            }
        }

        return $result;
    }

    /**
     * @title 预分配单失败
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function doFail(array $params)
    {
        $res = NULL;
        try {
            $res = (new AssignOrder())->fail([
                'totalAmount' => $params['totalAmount'],
                'billID'      => $params['billID'],
            ]);
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        //账户检测
        try {
            (new AccountCheckService())->checkAssign($params['billID'], '释放冻结');
        } catch (\Exception $e) {
            Log::error(strval($e), [], 'AccountCheck');
        }

        return $res;
    }

    /**
     * @title 查询
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service\AccountCenter
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @throws AccountException
     * @returns
     * []
     * @returns
     */
    public function getList(array $params)
    {
        $data = NULL;
        try {
            $data = (new AssignOrder())->getList($params);
        } catch (\Exception $e) {
            AccountException::handler($e->getMessage(), $e->getCode(), strval($e));
        }

        return $data;
    }

}