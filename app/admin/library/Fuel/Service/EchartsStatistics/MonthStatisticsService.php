<?php
namespace Fuel\Service\EchartsStatistics;

use Fuel\Service\OrgCardRegionDayTradeService;
use Fuel\Service\OrgCardTypeStatisticsService;
use Fuel\Service\OrgChargeStatisticsService;
use Fuel\Service\OrgMainCardOperatorStatisticsService;
use Fuel\Service\OrgOilOperatorTradeStatisticsService;
use Fuel\Service\OrgOilTypeStatisticsService;
use Fuel\Service\OrgStationMonthTradeService;
use Fuel\Service\OrgTradeAreaStatisticsService;

class MonthStatisticsService
{
    /**
     * 机构消费统计 数据图表 整理
     * @param $params
     */
    public function getOrgConsume($params)
    {
        $params['page'] = 1;
        $params['limit'] = 10;
        $data = OrgCardRegionDayTradeService::getOrgConsumeList($params);

        return $this->formatEchartsColumnarData($data,"top_org_name","money",$params['month']);
    }

    /**
     * 机构充值统计
     * @param $params
     */
    public function getOrgRecharge($params)
    {
        $params['page'] = 1;
        $params['limit'] = 10;
        $data = (new OrgChargeStatisticsService())->getList($params);

        return $this->formatEchartsColumnarData($data,"org_name","all_money",$params['month']);
    }

    /**
     * 站点交易统计
     * @param $params
     * @return array
     */
    public function getOrgStationConsume($params)
    {
        $params['page'] = 1;
        $params['limit'] = 10;

        $data = (new OrgStationMonthTradeService())->getStationConsumeList($params);

        return $this->formatEchartsColumnarData($data,"station_name","money",$params['month']);
    }

    /**
     * 格式化柱状图 数据结构
     * @param array $data   数据源
     * @param $companyName  机构名称 的下标
     * @param $money        机构金额 的下标
     * @param $month        月份
     * @return array
     */
    private function formatEchartsColumnarData($data,$orgName,$money,$month)
    {
        $orgNames = [];
        $coegSeries = [];
        foreach ($data as $key=>$item)
        {
            array_unshift($orgNames,$item[$orgName]);
            array_unshift($coegSeries,$item[$money]);
        }

        return [
            'data' => $orgNames,
            'series' => [
                [
                    'name' => $month,
                    'type' => 'bar',
                    'data' => $coegSeries
                ]
            ]
        ];
    }

    /**
     * 油卡类型 统计
     * @param $params
     * @return array
     */
    public function getOrgCardTypeData($params)
    {
        $params['_export'] = 1;

        $data =  (new OrgCardTypeStatisticsService())->getList($params);

        return $this->formatEchartsCakeData($data,"card_type");
    }

    /**
     * 油品类型统计
     * @param $params
     * @return array
     */
    public function getOrgOilTypeData($params)
    {
        $params['_export'] = 1;

        $data =  (new OrgOilTypeStatisticsService())->getList($params);

        return $this->formatEchartsCakeData($data,"oil_name");
    }

    /**
     * 消费地区统计
     * @param $params
     * @return array
     */
    public function getOrgAreaData($params)
    {
        $params['_export'] = 1;

        $data =  (new OrgTradeAreaStatisticsService())->getList($params);

        return $this->formatEchartsCakeData($data,"province");
    }

    /**
     * 油站运营商统计
     * @param $params
     * @return array
     */
    public function getOperatorData($params)
    {
        $params['_export'] = 1;

        $data =  (new OrgOilOperatorTradeStatisticsService())->getList($params);

        return $this->formatEchartsCakeData($data,"station_pname");
    }

    /**
     * 主卡运营商
     * @param $params
     * @return array
     */
    public function getMainCardOperatorData($params)
    {
        $params['_export'] = 1;

        $data =  (new OrgMainCardOperatorStatisticsService())->getList($params);

        return $this->formatEchartsCakeData($data,"name");
    }

    /**
     * 饼状图 数据整理
     * @param $data     数据源
     * @param $orgName  名称字段
     * @param $money    金额字段
     * @return array
     */
    private function formatEchartsCakeData($data,$name)
    {
        $returnData = [];
        foreach ($data as $key=>$item)
        {
            $returnData[$key]['value'] = $item["money"];
            $returnData[$key]['percen'] = $item["money_rate"];
            $returnData[$key]['name'] = $item[$name];
        }

        return $returnData;
    }

}