<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 19-8-6
 * Time: 上午11:14
 */

namespace Fuel\Service;

use Framework\Cache;
use Framework\DingTalk\DingTalkAlarm;
use Framework\Job;
use Framework\Log;
use Framework\Sms\NewSms;
use Fuel\Defines\CardFrom;
use Fuel\Defines\CardViceConf;
use Fuel\Defines\ConsumeType;
use Fuel\Defines\ErrorCode;
use Fuel\Defines\OilCom;
use Fuel\Defines\ViceCardStatus;
use Fuel\Request\GasClient;
use Fuel\Request\OrderClient;
use Fuel\Service\AccountCenter\AccountService;
use Fuel\Service\AccountCenter\CardService;
use G7Pay\Core\Card;
use helper;
use Kafka\Exception;
use Models\OilAccountAssignTaskDetail;
use Models\OilAccountMoney;
use Models\OilCardAccount;
use Models\OilCardVice;
use Models\OilCardViceApp;
use Models\OilCardViceTrades;
use Models\OilCreditAccount;
use Models\OilCreditProvider;
use Models\OilDict;
use Models\OilOrg;
use Illuminate\Database\Capsule\Manager as Capsule;

use Framework\Excel\ExcelWriter;
use Models\OilTruckCardGet;

class CardVice
{
    /*
     * array(    'card_no' => '****************',    'open_id' => 'odrxi0U1V1Y6BPAynUiWmjB_jP5k',    'driverphone' => '***********',    'gascode' => '2029PK01', )
     */
    static public function getCardByOpenid(array $params)
    {
        helper::argumentCheck(['openid'], $params);

        $res = $phone = NULL;

        try {

            $data = GasClient::post([
                'method' => 'gas.api.getCardListByOpenId',
                'data' => ['open_id' => $params['openid']],
            ]);

            Log::error('data' . var_export($data, TRUE), [$params['openid']], 'getCardByOpenid');

            if ($data) {
                $resData = (array)$data;
                if ($resData) {
                    foreach ($resData as $val) {
                        if ($val->open_id) {
                            $res = $val;
                        }
                    }
                }

                Log::error('res' . var_export($res, TRUE), [], 'getCardByOpenid');
            }
        } catch (\Exception $e) {
            //Log
            Log::error('Exception:' . strval($e), [], 'getCardByOpenid');
        }

        return $res;
    }

    /*
     * 扫码加油显示卡列表
     */
    static public function getDriverAllCardAccount(array $params)
    {
        helper::argumentCheck(['openId', 'station_id'], $params);

        Log::error("paras" . \GuzzleHttp\json_encode($params), [], "accountList");
        //车辆卡有卡号，不用请求根据openid获取卡号
        if (isset($params['truck_card_no']) && !empty($params['truck_card_no'])) {
            $vice_no = trim($params['truck_card_no']);
        } else {
            //根据openid获取当前使用卡片及手机号
            $cardInfo = self::getCardByOpenid(['openid' => $params['openId']]);
            if (!$cardInfo) {
                throw new \RuntimeException($params['openId'] . '未找到子账户信息', 5009001);
            }
            if (!$cardInfo->driverphone) {
                throw new \RuntimeException('openid没有获取到子账户信息和手机号', 2);
            }
            $vice_no = trim($cardInfo->card_no);
        }

        $localCardInfo = OilCardVice::getByViceNo(['vice_no' => $vice_no]);
        if (!$localCardInfo) {
            throw new \RuntimeException($params['truck_card_no'] . '未找到子账户信息', 5009002);
        }

        $condition['phone'] = $localCardInfo->driver_tel;
        $condition['vice_no'] = $localCardInfo->vice_no;
        $condition['station_id'] = $params['station_id'];
        $condition['card_level'] = $localCardInfo->card_level;
        if ($condition['card_level'] == 1) {
            unset($condition['vice_no']);
        }
        $condition['selectViceNo'] = $localCardInfo->vice_no; //卡包中当前使用的卡
        //$data = self::getCardList($localCardInfo->driver_tel, $localCardInfo->vice_no, $params['station_id'], $localCardInfo->card_level);
        $data = self::getCardList($condition);

        return $data;
    }

    /*
     * 根据手机号获取所有卡片
     */
    static public function getCardList($params)
    {
        //Capsule::connection()->enableQueryLog();
        $sqlObj = OilCardVice::leftJoin('oil_org', 'oil_card_vice.org_id', '=', 'oil_org.id')
            ->whereIn('oil_card_vice.oil_com', OilCom::getFirstList())
            ->whereIn('oil_card_vice.status', [ViceCardStatus::USING, ViceCardStatus::LOCKING, ViceCardStatus::FREEZE]);
        /*if ($card_level == 2) {
            $sqlObj->where('oil_card_vice.vice_no', $vice_no);
        } else {
            $sqlObj->where('card_level', 1);
            if (is_array($driver_tel)) {
                $sqlObj->whereIn('oil_card_vice.driver_tel', $driver_tel);
            } else {
                $sqlObj->where('oil_card_vice.driver_tel', $driver_tel);
            }

            //获取正在使用中的卡
            if ($is_use == 1) {
                $sqlObj->where('oil_card_vice.vice_no', $vice_no);
            }
        }
        if ($vice_no_search) {
            $sqlObj->where('oil_card_vice.vice_no', $vice_no_search);
        }*/
        if (isset($params['vice_no']) && !empty($params['vice_no'])) {
            $sqlObj->where('oil_card_vice.vice_no', $params['vice_no']);
        }
        if (isset($params['card_level']) && !empty($params['card_level'])) {
            $sqlObj->where('oil_card_vice.card_level', $params['card_level']);
        }
        if (isset($params['orgRoot']) && !empty($params['orgRoot'])) {
            $sqlObj->where('oil_org.orgcode', 'like', $params['orgRoot'] . "%");
        }
        if (isset($params['phone']) && is_array($params['phone'])) {
            $sqlObj->whereIn('oil_card_vice.driver_tel', $params['phone']);
        } else {
            $sqlObj->where('oil_card_vice.driver_tel', '=', $params['phone']);
        }

        $station_id = array_get($params, "station_id", "");
        $select_vice_no = array_get($params, "selectViceNo", ""); //选中的油卡

        $sqlObj->orderBy("oil_card_vice.status", "asc");
        $sqlObj->orderBy("oil_card_vice.trade_time", "desc");

        $card_list = $sqlObj->selectRaw('oil_card_vice.id,oil_card_vice.vice_no,oil_card_vice.paylimit,oil_card_vice.oil_top,oil_card_vice.day_top,oil_card_vice.month_top,
            oil_card_vice.ischeck,oil_card_vice.check_truckno,oil_card_vice.oil_com,oil_card_vice.card_remain,oil_org.orgcode,oil_org.org_name,
            oil_card_vice.truck_no,oil_card_vice.able_transfer,oil_card_vice.deduction_account_no,oil_card_vice.deduction_account_name,oil_org.is_del,
            oil_card_vice.card_level,oil_card_vice.status,oil_card_vice.org_id, oil_card_vice.driver_tel,oil_card_vice.driver_name')
            ->get();
        //$sql = Capsule::connection()->getQueryLog();
        //print_r($sql);exit;
        if (!$card_list) {
            throw new \RuntimeException('没有找到相关的子账户', 2);
        }

        $vice_ids = $stationArgs = $oneShareCards = [];
        foreach ($card_list as $value) {

            if (empty($value->deduction_account_no)) {
                $orgIds[] = $value->org_id;
            }

            //共享卡扣款账户是授信账户
            if ($value->oil_com == OilCom::GAS_FIRST_TALLY) {
                if ($value->deduction_account_no && substr($value->deduction_account_no, 0, 3) == '208') { //授信账户
                    $oneShareCards[] = $value->deduction_account_no;
                } else {
                    $orgIds[] = $value->org_id;
                }
            }
            //记录卡的现金机构对应的状态
            $orgDel = $value->is_del;
            $vice_ids[$value->id] = ['id' => $value->id, "card_remain" => $value->card_remain, "is_del" => $orgDel, "status" => $value->status];


            $orgcode = $value->orgcode;
            //锁定的状态优先,油站限定,非使用的卡,不用在请求油站限定
            if ($value->status == '使用' && !empty($orgcode)) {
                //拼接油站限定参数
                $stationArgs['data'][] = [
                    'org_code' => $orgcode,
                    'station_id' => $station_id,
                    'vice_no' => $value->vice_no,
                ];
            }
        }

        if ($station_id && count($stationArgs['data']) > 0) {
            $stationMap = self::batchCardLimit($stationArgs);
        }

        //获取共享卡现金账户
        $cashMap = self::cashAccountList($orgIds);

        //获取共享卡授信账户数据
        $shareMap = self::shareAccoutList($oneShareCards);

        //得到充值卡账户数据
        $chargeMap = self::chargeAccountList($vice_ids);

        // 获取不限加油距离机构配置
        $orgCodeMap = [];
        $ignoreDistanceOrg = OilDict::getByType(5);
        if ($ignoreDistanceOrg) {
            foreach ($ignoreDistanceOrg as $v) {
                $orgCodeMap[] = $v->val;
            }
        }

        // 获取距离限制
        $distanceLimit = OilDict::getByType(6);
        $distanceLimitVal = 0;
        if ($distanceLimit) {
            $distanceLimitVal = isset($distanceLimit[0]) && isset($distanceLimit[0]) ? $distanceLimit[0]->val : 0;
        }
        foreach ($card_list as $key => &$value) {
            $time = self::getTruckCardLeftSeconds($value);
            $value->left_seconds = $time['left_seconds'];
            $value->car_card_end_time = $time['car_card_end_time'];
            $value->truck_version = $time['truck_version'];

            $value->account_status = "";                                                //账户状态
            $card_selected = $value->vice_no == $select_vice_no ? TRUE : FALSE; //默认选中
            //$value->status                 = $value->status == '使用' ? 10 : 20;
            switch ($value->status) {
                case ViceCardStatus::USING:
                    $value->status = 10;
                    break;
                case ViceCardStatus::FREEZE:
                    $value->status = 30;
                    break;
                default:
                    $value->status = 20;
            }
            $value->is_selected = $card_selected;
            $value->station_available = isset($stationMap[$value->vice_no]) ? $stationMap[$value->vice_no]->status : TRUE;
            $value->station_available_desc = isset($stationMap[$value->vice_no]) ? $stationMap[$value->vice_no]->desc : "";
            $dayTop = OilCardVice::getSumTrade(['vice_no' => $value->vice_no, "limitType" => 1]);
            $monthTop = OilCardVice::getSumTrade(['vice_no' => $value->vice_no, "limitType" => 2]);

            $value->has_remain = 0; //是否有余额

            $value->limit_distance_val = $distanceLimitVal;
            /*if (in_array(substr($value->orgcode, 0, 6), $orgCodeMap)) {
                $value->limit_distance = 1;
            } else {
                $value->limit_distance = 0;
            }*/
            $value->limit_distance = self::getLimitDistance($value->orgcode, $orgCodeMap);
            if ($value->oil_com == OilCom::GAS_FIRST_TALLY) {
                //共享卡 账户特殊处理
                $value->account_list = [];
                $value->service_rate = 0;
                if (isset($shareMap[$value->deduction_account_no]) && !empty($value->deduction_account_no) && substr($value->deduction_account_no, 0, 3) == '208') {
                    $value->service_rate = $shareMap[$value->deduction_account_no]['service_rate'] ? $shareMap[$value->deduction_account_no]['service_rate'] : 0;
                    if ($shareMap[$value->deduction_account_no]['account_status'] == 10) {
                        $value->account_status = 10;
                    } else {
                        $value->account_status = 20;
                    }
                    if (bccomp($shareMap[$value->deduction_account_no]['balance'], 0, 2) > 0) {
                        $value->has_remain = 1;
                    }
                } else { //取现金账户
                    if (isset($cashMap[$value->org_id])) {
                        if (isset($cashMap[$value->org_id]['money']) && bccomp($cashMap[$value->org_id]['money'], 0, 2) > 0) {
                            $value->has_remain = 1;
                        }
                    }
                    $value->account_status = isset($vice_ids[$value->id]) && $vice_ids[$value->id]['is_del'] == 1 ? 20 : 10;
                }

                //限额处理
                if (substr($value->deduction_account_no, 0, 3) == '208') {
                    $creditLimit = ConsumeType::getCreditLimit($value->orgcode);
                    $value->oil_top = $value->oil_top > $creditLimit['oil_top'] ? sprintf('%.2f', $creditLimit['oil_top']) : sprintf('%.2f', $value->oil_top);
                    $value->day_top = $value->day_top > $creditLimit['day_top'] ? sprintf('%.2f', $creditLimit['day_top']) : sprintf('%.2f', $value->day_top);
                    $value->month_top = $value->month_top > $creditLimit['month_top'] ? sprintf('%.2f', $creditLimit['month_top']) : sprintf('%.2f', $value->month_top);
                }
            } else {
                $cashList = isset($chargeMap[$value->id]) && isset($chargeMap[$value->id]['cash']) ? $chargeMap[$value->id]['cash'] : [];
                if (empty($cashList)) { //卡无现金子账户
                    try {
                        (new DingTalkAlarm())->alarmToGroup('小程序卡列表', '卡号：' . $value->vice_no . PHP_EOL . '无现金卡账户', [], TRUE, TRUE);
                    } catch (\Exception $e) {

                    }
                    continue;
                }

                if (count($cashList) > 0) {
                    if (isset($chargeMap[$value->id]) && isset($chargeMap[$value->id]['credit'])) {
                        $creditList = array_merge($chargeMap[$value->id]['credit'], $chargeMap[$value->id]['cash']);
                    } else {
                        $creditList = $chargeMap[$value->id]['cash'];
                    }
                    foreach ($creditList as &$item) {
                        //排除发财卡的授信账户
                        if ($item['account_type'] == 20 && $value->oil_com == OilCom::FORTUNE_CARD) {
                            continue;
                        }
                        if (empty($value->deduction_account_no) && $item['account_type'] == 10) {
                            $value->deduction_account_no = $item['account_no'];
                            $value->deduction_account_name = $value->oil_com == OilCom::GAS_FIRST_TALLY ? ConsumeType::FIRST_TALLY_ACCOUNT_NAME : ConsumeType::FIRST_CHARGE_ACCOUNT_NAME;
                        }
                        $item['status'] = $item['account_status'];
                        $item['selected'] = false;
                        $item['is_selected'] = false;
                        if (($item['account_no'] == $value->deduction_account_no)) {
                            $item['selected'] = true;
                            $item['is_selected'] = true;
                        }
                        $item['has_remain'] = 0;
                        if (bccomp($item['balance'], 2, 0) > 0) {
                            $item['has_remain'] = 1;
                        }
                        if ($item['account_type'] == 20 && $item['selected']) {
                            $creditLimit = ConsumeType::getCreditLimit($value->orgcode);
                            $value->oil_top = $value->oil_top > $creditLimit['oil_top'] ? sprintf('%.2f', $creditLimit['oil_top']) : sprintf('%.2f', $value->oil_top);
                            $value->day_top = $value->day_top > $creditLimit['day_top'] ? sprintf('%.2f', $creditLimit['day_top']) : sprintf('%.2f', $value->day_top);
                            $value->month_top = $value->month_top > $creditLimit['month_top'] ? sprintf('%.2f', $creditLimit['month_top']) : sprintf('%.2f', $value->month_top);
                        }
                    }
                    $account_type = array_column($creditList, 'account_type');
                    $creatime = array_column($creditList, 'createtime');
                    array_multisort($account_type, SORT_ASC, $creatime, SORT_DESC, $creditList);
                    $value->account_list = $creditList;
                }
            }
            $tmpDay = $value->day_top - $dayTop;
            if ($tmpDay <= 0) {
                $value->day_top = 0.00;
            } else {
                $value->day_top = number_format($tmpDay, 2, ".", "");
            }
            $tmpMonth = $value->month_top - $monthTop;
            if ($tmpDay <= 0) {
                $value->month_top = 0.00;
            } else {
                $value->month_top = number_format($tmpMonth, 2, ".", "");
            }

            if (bccomp($value->day_top, $value->month_top, 2) > 0) {
                $value->day_top = $value->month_top;
            }

            if (bccomp($value->oil_top, $value->day_top, 2) > 0) {
                $value->oil_top = $value->day_top;
            }
        }

        Log::error('return:', [$card_list], 'getCardList');

        return $card_list;
    }

    //获取机构加油距离限定
    static public function getLimitDistance($orgcode = "", $orgMap = [])
    {
        $is_limit = 0;
        if (empty($orgcode) || count($orgMap) == 0) {
            return $is_limit;
        }
        $orgList = self::splitOrgCode($orgcode);
        foreach ($orgList as $_orgcode) {
            if (in_array($_orgcode, $orgMap)) {
                $is_limit = 1;
                break;
            }
        }
        return $is_limit;
    }

    //切割机构
    static public function splitOrgCode($orgcode = "")
    {
        $org_len = strlen($orgcode);
        if ($org_len > 6) {
            $each_num = ($org_len - 6) / 2;

            $orgList[] = $orgcode;

            for ($i = 1; $i <= $each_num; $i++) {
                $orgList[] = substr($orgcode, 0, $org_len - ($i * 2));
            }
        } else {
            $orgList = [$orgcode];
        }
        return $orgList;
    }

    /**
     * @title   获取卡相应的卡账户状态
     * @desc
     * @param array $params
     * @param array $cardInfo
     * @return int|null
     * @returns
     * []
     * @returns
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @version
     * @level   1
     * <AUTHOR>
     */
    static public function getCardAccountStatus(array $params, $cardInfo = [])
    {
        $account_status = 20;
        if (empty($cardInfo)) {
            $cardInfo = OilCardVice::where('vice_no', $params['vice_no'])->first();
        }

        if (!$cardInfo) {
            $account_status = 20;
        } else {
            if (isset($cardInfo->deduction_account_no) && $cardInfo->deduction_account_no) {
                if (substr($cardInfo->deduction_account_no, 0, 3) == '208') {
                    $accountInfo = OilCreditAccount::getByAccountNo($cardInfo->deduction_account_no);
                    $account_status = $accountInfo->status;
                    if ($accountInfo->subAccountID && $accountInfo->is_own != 1 && $account_status == 10) {
                        $account_status = 20;
                        //取成都卡账户状态
                        $creditInfo = (new \Fuel\Service\AccountCenter\AccountService())->getZBankAccountInfo(['subAccountID' => $accountInfo->subAccountID, 'one' => 1]);
                        if ($creditInfo) {
                            //bccomp($creditInfo->restCreditAmount, 0,2) > 0
                            if ($creditInfo->status == 'NORMAL') {
                                $account_status = 10;
                            }
                        }
                    }
                } elseif ($cardInfo->oil_com == 20) {
                    //取卡账户
                    $cardAccountInfo = OilCardAccount::where('cardSubAccountID', $cardInfo->deduction_account_no)->first();
                    if ($cardAccountInfo) {
                        if ($cardAccountInfo->subAccountType == 'CREDIT') {
                            $accountInfo = OilCreditAccount::getByAccountNo($cardAccountInfo->common_account_no);
                            $account_status = $accountInfo->status;
                            if ($accountInfo->subAccountID && $account_status == 10) {
                                $cardCreditInfo = (new \Fuel\Service\AccountCenter\AccountService())->getZBankAccountInfo(['subAccountID' => $accountInfo->subAccountID, 'one' => 1]);
                                if ($cardCreditInfo) {
                                    if ($cardCreditInfo->status != 'NORMAL') {
                                        $account_status = 20;
                                    }
                                    /*if (bccomp($cardCreditInfo->restCreditAmount, 0) < 0) {
                                        $account_status = 20;
                                    }*/
                                } else {
                                    $account_status = 20;
                                }
                            }
                        } elseif ($cardAccountInfo->subAccountType == 'CASH') {
                            //取机构的状态
                            $orgInfo = OilOrg::where('id', $cardInfo->org_id)->first();
                            $account_status = $orgInfo->is_del == 1 ? 20 : 10;
                        }
                    } else {
                        $account_status = 20;
                    }
                } else {
                    //取机构的状态
                    $orgInfo = OilOrg::where('id', $cardInfo->org_id)->first();
                    $account_status = $orgInfo->is_del == 1 ? 20 : 10;
                }
            } else {
                //取机构的状态
                $orgInfo = OilOrg::where('id', $cardInfo->org_id)->first();
                $account_status = $orgInfo->is_del == 1 ? 20 : 10;
            }
        }

        return $account_status;
    }

    /*
     * 发送消费短信提醒
     */
    static public function sendDriverConsumeTip(array $params, $mode = "sync")
    {
        helper::argumentCheck(['orgcode', 'driver_tel', 'content'], $params);
        Log::error('params' . var_export($params, TRUE), [], 'sendDriverConsumeTip');
        Log::error('orgcode' . var_export(substr($params['orgcode'], 0, 6), TRUE), [], 'sendDriverConsumeTip');
        if (in_array(substr($params['orgcode'], 0, 6), ['2008KB', '200NWQ', '200NW5', '200NZ2', '2026BV'])) {
            $sendData['content'] = $params['content'];
            $sendData['mobiles'] = $params['driver_tel'];
            $sendData['path'] = '/message/general';

            if ($mode == "sync") {
                $res = \Framework\Sms\NewSms::send($sendData);
            } else {
                $res = (new Job())
                    ->setTaskName('发送短信')
                    ->pushTask(function () use ($sendData) {
                        \Framework\Sms\NewSms::send($sendData);
                    })
                    ->channel('default')
                    ->exec();
            }

            Log::error('result' . var_export($res, TRUE), [], 'sendDriverConsumeTip');
        }

        return TRUE;
    }

    /*
     * 发送消费短信提醒
     */
    static public function sendManagerConsumeTip(array $params, $mode = "sync")
    {
        helper::argumentCheck(['mobiles', 'content'], $params);
        Log::error('params' . var_export($params, TRUE), [], 'sendManagerConsumeTip');
        $sendData['content'] = $params['content'];
        $sendData['mobiles'] = $params['mobiles'];
        $sendData['path'] = '/message/general';

        if ($mode == "sync") {
            $res = \Framework\Sms\NewSms::send($sendData);
        } else {
            $res = (new Job())
                ->setTaskName('发送短信')
                ->pushTask(function () use ($sendData) {
                    \Framework\Sms\NewSms::send($sendData);
                })
                ->channel('default')
                ->exec();
        }

        Log::error('result' . var_export($res, TRUE), [], 'sendManagerConsumeTip');

        return TRUE;
    }

    /*
     * 根据openid获取消费记录
     */
    static public function getTradesByOpenId(array $params)
    {
        $phones = self::getPhonesByOpenId($params['openId']);

        $phones = $phones ? $phones : ['12345678901'];

        //最近6个月的消费
        $time = date('Y-m-d H:i:s', strtotime('-6 month', time()));

        $params['phones'] = $phones;
        $params['createtimeGe'] = $time;
        if (isset($params['page_size']) && $params['page_size']) {
            $params['limit'] = isset($params['page_size']) ? $params['page_size'] : 10;
            $params['page'] = isset($params['page_no']) ? $params['page_no'] : 1;
        } else {
            $params['limit'] = isset($params['limit']) ? $params['limit'] : 50;
            $params['page'] = isset($params['page']) ? $params['page'] : 1;
        }

        $params['ext'] = 1;
        //Log::error('params'.var_export($params,true),[],'tim1218');

        $data = OilCardViceTrades::getList($params);

        return $data;
    }

    /*
     * 切换卡账户
     */
    static public function switchCardAccount($params)
    {
        $uData = [
            'is_use' => 2,
        ];
        if ($params['oil_com'] == OilCom::GAS_FIRST_CHARGE) {
            $uData['deduction_account_no'] = $params['account_no'];
            $uData['deduction_account_name'] = $params['account_name'];
        }

        //todo 处理切换卡账户名称
        $res = OilCardVice::updateCardAccount($params['vice_no'], $params['phone'], $uData);

        if ($res) {
            //其他卡更改is_use
            OilCardVice::updateOtherCardAccount($params['vice_no'], $params['phone']);
        } else {
            throw new \RuntimeException('你无权切换此子账户', 401);
        }

        return $res;
    }

    //统一充值卡切换账户名称
    //from 1:只切换卡账户,2:切换卡账户且更改is_use
    static public function changeCardAccount($params)
    {
        Log::error(var_export($params, true), [], 'changeCardAccountNo');

        $cardInfo = OilCardVice::where('vice_no', $params['vice_no'])->whereIn("oil_com", OilCom::getAllFirstList())->first();

        if (!$cardInfo) {
            throw new \RuntimeException("子账户信息不存在", 2);
        }

        if ($params['from'] == 1) {
            if (in_array($cardInfo->oil_com, [OilCom::GAS_FIRST_TALLY, OilCom::FORTUNE_CARD])) {
                throw new \RuntimeException("该卡类型不允许切换账户", 2);
            }
            $upArr = self::checkCardAccount($cardInfo, $params['account_no']);

        } elseif ($params['from'] == 2) {
            if ($cardInfo->oil_com == OilCom::GAS_FIRST_CHARGE) {
                if (empty($params['account_no']) || !isset($params['account_no'])) {
                    throw new \RuntimeException("请选择扣款账户", 2);
                }
                $upArr = self::checkCardAccount($cardInfo, $params['account_no']);
            }
            $upArr['is_use'] = 2;
        } else {
            throw new \RuntimeException("操作不合法", 2);
        }

        Log::error(var_export($upArr, true), [], 'changeCardAccountNo');

        $data = $cardInfo->update($upArr);

        if ($params['from'] == 2 && $data) {
            //其他卡更改is_use
            OilCardVice::updateOtherCardAccount($params['vice_no'], $params['phone']);
        }

        return $data;
    }

    //校验充值卡的扣款账户
    static public function checkCardAccount($cardInfo, $account_no)
    {
        $cardAccount = \Models\OilCardAccount::where('vice_id', $cardInfo->id)->where('cardSubAccountID', $account_no)->first();

        if (!$cardAccount) {
            throw new \RuntimeException("子账户的扣款账户不存在", 2);
        }
        $product_name = $cardInfo->oil_com == OilCom::GAS_FIRST_TALLY ? ConsumeType::FIRST_TALLY_ACCOUNT_NAME : ConsumeType::FIRST_CHARGE_ACCOUNT_NAME; //需求号：ENINET-3012
        //$product_name = isset($params['product_name']) && $params['product_name'] ? $params['product_name'] : $params['deduction_account_name'];
        if (isset($cardAccount->common_account_no) && !empty($cardAccount->common_account_no) && substr($cardAccount->common_account_no, 0, 3) == '208') {
            $creditInfo = \Models\OilCreditAccount::getByAccountNoWithProvider($cardAccount->common_account_no);
            if ($creditInfo && $creditInfo->CreditProvider) {
                $product_name = $creditInfo->CreditProvider->name;
            }
        }
        return ['deduction_account_no' => $account_no, 'deduction_account_name' => $product_name];
    }

    /*
     * 用微信的openid获取使用过的手机号列表
     */
    static public function getPhonesByOpenId($openId)
    {
        $data = NULL;

        try {
            $data = GasClient::post([
                'method' => 'gas.api.getPhonesByOpenId',
                'data' => ['open_id' => $openId],
            ]);

            $data = array_filter($data);
        } catch (\Exception $e) {
            Log::error('获取手机号异常' . strval($e), [$openId], 'getPhonesByOpenId');
        }

        return $data;
    }

    /*
     * 独立修改卡密码方法
     */
    static public function editPassword(array $params)
    {
        helper::argumentCheck(['vice_no', 'new_password'], $params);

        $viceInfo = OilCardVice::getByViceNo(['vice_no' => $params['vice_no']]);
        if (!$viceInfo) {
            throw new \RuntimeException('子账户信息不存在', 2);
        }

        if ($viceInfo->ischeck == 1) {
            if (!isset($params['old_password']) || !$params['old_password']) {
                throw new \RuntimeException('旧密码不能为空', 2);
            }

            if ($params['old_password'] != $viceInfo->vice_password) {
                throw new \RuntimeException('旧密码不正确', 2);
            }

            if ($params['old_password'] == $params['new_password']) {
                throw new \RuntimeException('旧密码与新密码不能相同', 2);
            }
        }

        //请求gas
        $gasParams = [
            'gascode' => $viceInfo->Org->orgcode,
            'card_no' => [$viceInfo->vice_no],
            'password' => $params['new_password'],
            'is_check' => 1,
            'ischeck' => 1,
            'is_one_card' => 1
        ];

        Log::error('CardSet--参数：' . json_encode($gasParams), [], 'editPassword');
        $gasRes = GasClient::post([
            'method' => 'gas.api.cardSet',
            'data' => $gasParams
        ]);
        Log::error('CardSet--返回参数：' . json_encode($gasRes), [], 'editPassword');

        //修改本地卡密码
        $viceInfo->update(['vice_password' => $params['new_password'], 'ischeck' => 1]);

        //togos
        CardViceToGos::batchUpdateToGos([$params['vice_no']]);

    }

    /*
     * 获取划拨短信验证码
     */
    static public function getTransferSmsCode(array $params)
    {
        helper::argumentCheck(['mobile', 'vice_no'], $params);

        if (!preg_match("/^1\d{10}$/", $params['mobile'])) {
            throw new \RuntimeException('手机号格式不正确', 2);
        }

        //校验手机号是否一致
        $vice_info = OilCardVice::getByViceNo(['vice_no' => $params['vice_no']]);
        if (!$vice_info) {
            throw new \RuntimeException('子账户已不存在', 2);
        }

        if (trim($vice_info->status) != ViceCardStatus::USING) {
            throw new \RuntimeException('子账户状态异常', 2);
        }

        if ($vice_info->driver_tel == $params['mobile']) {
            throw new \RuntimeException('接收人手机号不能与划拨人手机号相同', 2);
        }

        $smsCode = helper::generateRandomCode(4);

        $_cache_name = 'transfer_' . $params['mobile'];

        if (Cache::get($_cache_name)) {
            throw new \RuntimeException('短信码已发送，请误重复获取', 2);
        }

        //短信缓存
        Cache::put($_cache_name, $smsCode, 60 * 5);

        $args['content'] = '尊敬的用户：您的验证码为(' . $smsCode . ')，五分钟内有效';
        $args['mobiles'] = $params['mobile'];
        $args['path'] = '/message/general';

        try {
            NewSms::send($args);
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return true;

    }

    /*
     * 获取划拨短信验证码
     */
    static public function checkTransferSmsCode(array $params)
    {
        helper::argumentCheck(['mobile', 'captcha_code'], $params);

        if (!preg_match("/^1\d{10}$/", $params['mobile'])) {
            throw new \RuntimeException('手机号格式不正确', 2);
        }

        $_cache_name = 'transfer_' . $params['mobile'];

        $cache_sms_code = Cache::get($_cache_name);
        //if ($params['captcha_code'] != '0000') {
        if (!$cache_sms_code) {
            throw new \RuntimeException('验证码已失效,请重新获取验证码', 2);
        }

        //校验手机号验证码
        if ($params['captcha_code'] != $cache_sms_code) {
            throw new \RuntimeException('验证码不正确', 2);
        }
        //}

        return true;

    }

    /*
     * 卡划拨功能
     */
    static public function cardTransfer(array $params)
    {
        helper::argumentCheck(['out_vice_no', 'mobile', 'captcha_code', 'receive_name', 'transfer_amount', 'card_password'], $params);

        Log::error('params-' . var_export($params, true), [], 'cardTransfer');
        //校验
        $viceInfo = self::checkParams($params);
        $params['other_creator'] = $params['other_creator'] ? $params['other_creator'] : $viceInfo->driver_name;
        if (!$params['other_creator']) {
            //throw new \RuntimeException('司机姓名不能为空',2);
            $params['other_creator'] = '-';
        }
        Capsule::connection()->beginTransaction();
        try {
            //检测接收者是否存在发财卡
            $fortune_card_info = OilCardVice::where('driver_tel', $params['mobile'])
                ->where('org_id', $viceInfo->org_id)
                ->where('oil_com', OilCom::FORTUNE_CARD)
                ->whereIn('status', ['使用', '冻结'])
                ->lockForUpdate()
                ->first();
            if (!$fortune_card_info) {
                //不存在则静默创建此卡
                $fortune_card_info = self::createFortuneCard([
                    'mobile' => $params['mobile'],
                    'receive_name' => $params['receive_name'],
                    'out_vice_Info' => $viceInfo,
                    'creat' => $viceInfo,
                    'other_creator_id' => $params['other_creator_id'],
                    'other_creator' => $params['other_creator'],
                    'remark_work' => isset($params['remark_work']) && $params['remark_work'] ? $params['remark_work'] : NULL,
                ]);
                Log::error('创建卡完毕', [], 'cardTransfer');
            }
            Capsule::connection()->commit();
        } catch (\Exception $exception) {
            Capsule::connection()->rollBack();
            Log::error('创建发财卡失败' . $exception->getMessage(), [], 'cardTransfer');
            throw new \RuntimeException($exception->getMessage(), $exception->getCode());
        }

        //Capsule::connection()->beginTransaction();
        try {
            //锁卡
            //$out_vice_info = OilCardVice::getByViceNoForLock(['vice_no'=>$viceInfo->vice_no]);
            //$in_vice_info = OilCardVice::getByViceNoForLock(['vice_no'=>$fortune_card_info->vice_no]);

            Log::error('创建划拨工单开始', [], 'cardTransfer');
            //创建划拨工单
            $transfer_res = AccountTransfer::createFortuneTransfer([
                'out_vice_info' => $viceInfo,
                'sn' => isset($params['sn']) && $params['sn'] ? $params['sn'] : \GosSDK\Lib\Helper::uuid(),
                'in_vice_info' => $fortune_card_info,
                'amount' => $params['transfer_amount'],
                'other_creator' => $params['other_creator'],
                'remark_work' => isset($params['remark_work']) && $params['remark_work'] ? $params['remark_work'] : NULL,
                'data_from' => isset($params['data_from']) && $params['data_from'] ? $params['data_from'] : 4,
            ]);

            Log::error('审核工单', [$transfer_res], 'cardTransfer');

            if ($transfer_res) {
                //审核工单
                AccountTransfer::auditFortune(['id' => $transfer_res->id]);
            }

            Log::error('完毕', [], 'cardTransfer');

        } catch (Exception $e) {
            Log::error('Exception' . strval($e), [], 'cardTransfer');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return true;
    }

    /*
     * 卡划拨功能
     */
    static public function cardTransferForFoss(array $params)
    {
        helper::argumentCheck(['out_vice_no', 'mobile', 'receive_name', 'transfer_amount', 'sn'], $params);

        Log::error('params-' . var_export($params, true), [], 'cardTransfer');
        //校验
        $viceInfo = self::checkParamsForFoss($params);
        $params['other_creator'] = $params['other_creator'] ? $params['other_creator'] : $viceInfo->driver_name;

        Capsule::connection()->beginTransaction();
        try {
            //检测接收者是否存在发财卡
            $fortune_card_info = OilCardVice::where('driver_tel', $params['mobile'])
                ->where('org_id', $viceInfo->org_id)
                ->where('oil_com', OilCom::FORTUNE_CARD)
                ->whereIn('status', ['使用', '冻结'])
                ->lockForUpdate()
                ->first();
            if (!$fortune_card_info) {
                //不存在则静默创建此卡
                $fortune_card_info = self::createFortuneCard([
                    'mobile' => $params['mobile'],
                    'receive_name' => $params['receive_name'],
                    'out_vice_Info' => $viceInfo,
                    'creat' => $viceInfo,
                    'other_creator_id' => $params['other_creator_id'],
                    'other_creator' => $params['other_creator'],
                ]);
                Log::error('创建卡完毕', [], 'cardTransfer');
            }
            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            Log::error('创建发财卡失败ForFoss' . $e->getMessage(), [], 'cardTransfer');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        //Capsule::connection()->beginTransaction();
        try {
            Log::error('创建划拨工单开始', [], 'cardTransfer');
            //创建划拨工单
            $transfer_res = AccountTransfer::createFortuneTransfer([
                'out_vice_info' => $viceInfo,
                'in_vice_info' => $fortune_card_info,
                'amount' => $params['transfer_amount'],
                'app_time' => $params['app_time'],
                'other_creator' => $params['other_creator'],
                'remark_work' => isset($params['remark_work']) && $params['remark_work'] ? $params['remark_work'] : NULL,
                'remark' => isset($params['remark']) && $params['remark'] ? $params['remark'] : NULL,
                'sn' => $params['sn'],
            ]);

            Log::error('完毕', [], 'cardTransfer');

        } catch (Exception $e) {
            Log::error('Exception' . strval($e), [], 'cardTransfer');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return true;
    }

    /*
     * 创建发财卡
     */
    static public function createFortuneCard(array $params)
    {
        helper::argumentCheck(['mobile', 'receive_name', 'out_vice_Info'], $params);

        $out_vice_info = $params['out_vice_Info'];

        $insertData = $localCardResult = [];

        //请求gas发卡
        $cardResult = self::sendGasCreateCard([
            'card_owner' => $params['receive_name'],
            'driver_tel' => $params['mobile'],
            'orgcode' => $out_vice_info->org->orgcode,
            "org_id_big" => $out_vice_info->org->id,
            "driver_name" => $params['receive_name'],
            "org_id" => $out_vice_info->org->id,
            "create_orgcode" => $out_vice_info->org->orgcode,
            "create_orgcode_name" => $out_vice_info->org->org_name,
            'create_orgcode' => $out_vice_info->org->orgcode,
            'other_creator_id' => $params['other_creator_id'],
            'other_creator' => $params['other_creator'],
            'work_no' => OilCardViceApp::createOrderNo('KK'),
            'create_orgcode_name' => $out_vice_info->org->org_name,
            'fanli_orgcode' => $out_vice_info->FanLiOrg->orgcode,
        ]);

        //插入本地卡库
        if ($cardResult && isset($cardResult->details) && $cardResult->details) {
            foreach ($cardResult->details as $cardItem) {
                //[{\"driverphone\":\"13103186705\",\"drivername\":\"\",\"card_no\":\"6211040738969308\",\"password\":\"\",\"truck_no\":\"\",\"detail_id\":\"66569\",\"ischeck\":4,\"pcode\":\"20003JCP\",\"orgcode\":\"200133\",\"gascode\":\"200133\",\"oil_top\":5000,\"day_top\":10000,\"month_top\":200000,\"paylimit\":0,\"assign_amount\":\"NaN\",\"card_level\":\"1\",\"isreceive\":1}
                $cardItem = (array)$cardItem;
                $localCardResult = self::createLocalCard([
                    'org_id' => $out_vice_info->org_id,
                    'org_id_fanli' => $out_vice_info->org_id_fanli,
                    'gsporg_id' => $out_vice_info->gsporg_id,
                    'card_owner' => $params['receive_name'],
                    'driver_name' => $params['receive_name'] != '' ? trim($params['receive_name']) : NULL,
                    'driver_tel' => trim($params['mobile']),
                    'vice_no' => $cardItem['card_no'],
                    'card_main_id' => $out_vice_info->card_main_id,
                    'gas_money_id' => $out_vice_info->gas_money_id,
                    'creator_id' => $params['creator_id'] ? $params['creator_id'] : '-',
                ]);

                $insertData[] = $localCardResult->id;
            }
        }

        Log::error('创建账户中心卡账户-', [], 'cardTransfer');

        //创建账户中心卡账户
        (new CardService())->createByViceId($insertData);

        Log::error('添加卡到gos-', [$insertData], 'cardTransfer');
        //添加卡到gos
        CardViceToGos::batchAddByIdsToGos($insertData);

        return $localCardResult;
    }

    /*
     * 创建本地卡
     */
    static public function createLocalCard(array $params)
    {
        $viceInfo = [
            'vice_tmp_id' => 1,
            'org_id' => $params['org_id'],
            'org_id_fanli' => $params['org_id_fanli'],
            'active_time' => helper::nowTime(),
            'gsporg_id' => $params['gsporg_id'],
            'card_from' => 30,
            'card_owner' => $params['card_owner'],
            'driver_name' => $params['card_owner'] != '' ? trim($params['card_owner']) : NULL,
            'driver_tel' => trim($params['driver_tel']),
            'vice_no' => $params['vice_no'],
            'paylimit' => 0,
            'day_top' => 20000,
            'month_top' => 200000,
            'oil_top' => 10000,
            'card_main_id' => $params['card_main_id'],
            'oil_com' => OilCom::FORTUNE_CARD,
            'gas_money_id' => $params['gas_money_id'],//gas账户id
            'creator_id' => $params['creator_id'],
            'createtime' => helper::nowTime(),
            'isreceive' => 1, //领卡状态
            'card_level' => 1, //车辆卡标识
            'check_truckno' => 2, //是否车牌一致
            'limit_type' => 1, //车辆卡限制类型
            'ischeck' => 4, //验证方式 4不验证 1验证
            'is_use' => CardViceApp::getCardIsUse($params['driver_tel'], 1), //默认使用
            'able_transfer' => 1, //发财卡默认启动划油属性
        ];
        Log::error('localCardInfo-' . var_export($viceInfo, true), [], 'cardTransfer');
        $insertInfo = OilCardVice::updateOrAdd(['vice_no' => $params['vice_no']], $viceInfo);
        Log::error('localCardInfo-result-' . var_export($insertInfo->toArray(), true), [], 'cardTransfer');

        return $insertInfo;
    }

    static public function sendGasCreateCard(array $params)
    {
        //{"remote":true,"gasParams":{"card_level":"1","oil_com":"20","details":[{"driver_tel":"13103186705","driver_name":"雷庆","truck_no":"","assign_amount":"NaN","remark":"","bind_status":0,"orgcode":"200133","isSimple":1,"vice_password":"123456","set_passwd":1,"paylimit":1,"day_top":10000,"oil_top":5000,"month_top":200000,"card_owner":"雷庆","org_id":"650","org_id_big":"650","vice_app_id":"10848","createtime":"2020-05-13 11:54:37","create_orgcode":"200133","create_orgcode_name":"油品管理责任有限公司","detail_id":"66568","ischeck":4}],"distributionList":[],"fanli_orgcode":"200133","qrcodeCardRsults":[],"token":"34783E23A3A38B11DA66922478DC188F","platform":"chrome","cardOpenid":"ohEpVwqBuq4MAWG6G8Ax_iH03Luo","card_from":"30","data_from":"3","Arraykey":"firstGas","card_type":"2","num":1,"create_orgcode":"200133","other_creator_id":"320BD74D1FBC135BC7ACAC7F125D5F2C","other_creator":"傅明樂","work_no":"KK200513377300801","create_orgcode_name":"油品管理责任有限公司"}}
        $_gasParams = [
            'num' => 1,
            'card_level' => 1,
            'oil_com' => 20,
            'card_type' => 2,
            'card_from' => 30,
            'details' => [
                [
                    'truck_no' => "",
                    'card_owner' => $params['card_owner'],
                    'driver_tel' => $params['driver_tel'],
                    'orgcode' => $params['orgcode'],
                    "org_id_big" => $params['org_id_big'],
                    "driver_name" => $params['driver_name'],
                    'bind_status' => 0,
                    "isSimple" => 1,
                    "paylimit" => 0, //不开启限额
                    "set_passwd" => 0,
                    "vice_password" => NULL,
                    "org_id" => $params['org_id'],
                    "create_orgcode" => $params['orgcode'],
                    "create_orgcode_name" => $params['create_orgcode_name'],
                    "day_top" => 20000,
                    "month_top" => 200000,
                    "oil_top" => 10000,
                    "ischeck" => 4
                ]
            ],
            'paylimit' => 0,
            'set_passwd' => 0,
            'fanli_orgcode' => $params['fanli_orgcode'],
            'create_orgcode' => $params['orgcode'],
            'other_creator_id' => $params['other_creator_id'],
            'other_creator' => $params['other_creator'],
            'work_no' => $params['work_no'],
            'create_orgcode_name' => $params['create_orgcode_name']
        ];

        Log::error(__METHOD__ . '请求Gas参数', [$_gasParams], "cardTransfer");
        $_results = GasClient::post([
            'method' => 'gas.api.genWorkElectCard',
            'data' => $_gasParams
        ]);
        Log::error(__METHOD__ . '请求Gas结果：' . var_export($_results, true), [], "cardTransfer");

        return $_results;
    }

    static public function checkParams($params)
    {
        //校验卡信息
        $viceInfo = OilCardVice::getByViceNo(['vice_no' => $params['out_vice_no']]);

        Log::error('viceInfo' . var_export($viceInfo->toArray(), true), [], 'checkParams');
        if (!$viceInfo) {
            throw new \RuntimeException('子账户信息不存在', 2);
        }

        if (!in_array($viceInfo->oil_com, OilCom::getAllowTransfer())) {
            throw new \RuntimeException('此子账户类型不允许划拨', 2);
        }

        Log::error('viceInfo' . var_export($viceInfo->ischeck, true), [], 'checkParams');
        if ($viceInfo->ischeck != 1) {
            throw new \RuntimeException('此子账户未开启密码校验', 2);
        }

        if (trim($viceInfo->status) != ViceCardStatus::USING) {
            throw new \RuntimeException('子账户状态异常，请联系车队长', 2);
        }

        if ($viceInfo->card_level == CardViceConf::CARD_LEVEL_CAR) {
            throw new \RuntimeException('车辆账户不允许划拨', 2);
        }

        if ($viceInfo->able_transfer != 1) {
            throw new \RuntimeException('此子账户未开启油费划拨功能', 2);
        }

        //校验金额
        if ($params['transfer_amount'] <= 0) {
            throw new \RuntimeException('油费划拨金额有误', 2);
        }

        if ($params['transfer_amount'] > $viceInfo->card_remain) {
            throw new \RuntimeException('划拨金额不能超出本余额', 2002);
        }

        //验证密码是否正确
        if ($params['card_password'] != $viceInfo->vice_password) {
            $errorNum = Cache::get('lock_vice_num' . $viceInfo->no);
            $errorNum = $errorNum ? $errorNum : 0;
            $errorCode = $errorNum >= 5 ? 2003 : 2004;

            if ($errorCode == 2003) {
                //锁卡操作
                OilCardVice::edit(['id' => $viceInfo->id, 'status' => '锁定']);
                CardViceToGos::batchUpdateToGos([$viceInfo->vice_no], 'async');
                Cache::forget('lock_vice_num' . $viceInfo->no);
            } else {
                Cache::put('lock_vice_num' . $viceInfo->no, $errorNum + 1, 86400);
            }

            throw new \RuntimeException('密码错误', $errorCode);
        }
        Cache::forget('lock_vice_num' . $viceInfo->no);//成功后清楚密码错误次数

        //校验手机号验证码
        $captcha_code = Cache::get('transfer_' . $params['mobile']);
        if ($params['captcha_code'] != '0000') {
            if ($params['captcha_code'] != $captcha_code) {
                throw new \RuntimeException('验证码不正确', 2);
            }
        }
        //验证码消费
        Cache::forget('transfer_' . $params['mobile']);

        return $viceInfo;
    }

    static public function checkParamsForFoss($params)
    {
        //校验卡信息
        $viceInfo = OilCardVice::getByViceNo(['vice_no' => $params['out_vice_no']]);

        Log::error('viceInfo' . var_export($viceInfo->toArray(), true), [], 'checkParams');
        if (!$viceInfo) {
            throw new \RuntimeException('子账户信息不存在', 2);
        }

        if (!in_array($viceInfo->oil_com, OilCom::getAllowTransfer())) {
            throw new \RuntimeException('此子账户类型不允许划拨', 2);
        }

        if (trim($viceInfo->status) != ViceCardStatus::USING) {
            throw new \RuntimeException('子账户状态异常，请联系车队长', 2);
        }

        if ($viceInfo->able_transfer != 1) {
            throw new \RuntimeException('此子账户未开启油费划拨功能', 2);
        }

        //校验金额
        if ($params['transfer_amount'] <= 0) {
            throw new \RuntimeException('油费划拨金额有误', 2);
        }

        if ($params['transfer_amount'] > $viceInfo->card_remain) {
            throw new \RuntimeException('划拨金额不能超出本余额', 2002);
        }

        return $viceInfo;
    }

    public static function getTruckCardLeftSeconds($vice)
    {
        $data = [
            'left_seconds' => 0,
            'car_card_end_time' => '',
            'truck_version' => '0'
        ];
        if (empty($vice) || $vice->card_level != CardViceConf::CARD_LEVEL_CAR || empty($vice->driver_tel)) {
            return $data;
        }

        $record = OilTruckCardGet::Filter([
            'mobile' => $vice->driver_tel,
            'vice_no' => $vice->vice_no
        ])->orderBy('start_time', 'desc')->first();

        if (!empty($record)) {
            $data['car_card_end_time'] = $record->end_time;

            $tmp = strtotime($record->end_time);
            $data['left_seconds'] = time() > $tmp ? 0 : $tmp - time() + 1;
            $data['truck_version'] = CardViceConf::TRUCK_VERSION;
        }

        return $data;
    }

    //小程序四期,卡列表及账户平铺
    public static function cardListWithAccount(array $params)
    {
        $has_tally_balance = isset($params['has_tally_balance']) ? $params['has_tally_balance'] : 0;
        if(isset($params['plate_truck_vice_no']) && count($params['plate_truck_vice_no']) > 0){
            unset($params['phone']);
            $params['vice_noIn'] = $params['plate_truck_vice_no'];
        }
        if (isset($params['phone']) && !empty($params['phone'])) {
            if (is_array($params['phone'])) {
                $params['driver_telIn'] = $params['phone'];
            } else {
                $params['driver_telEq'] = $params['phone'];
            }
            unset($params['phone']);
        }
        if (isset($params['orgRoot']) && !empty($params['orgRoot'])) {
            $orgIds = OilOrg::getByOrgcodeLike($params['orgRoot']);
            if (count($orgIds) > 0) {
                $params['org_id_in'] = $orgIds;
            }
            unset($params['orgRoot']);
        }
        if (empty($params['vice_noOnly'])) {
            $params['statusIn'] = [ViceCardStatus::USING, ViceCardStatus::LOCKING, ViceCardStatus::FREEZE];
            $params['oil_comIn'] = OilCom::getFirstList();
        }
        $params['_export'] = 1;
        $field = 'id,vice_no,oil_com,card_remain,truck_no,deduction_account_no,deduction_account_name,status,driver_name,driver_tel,org_id,is_use,createtime,ischeck,card_level';
        if (!empty($params['extra_fields']) && is_array($params['extra_fields'])) {
            $fieldArr = array_unique(array_merge(explode(',', $field), $params['extra_fields']));
            $field = implode(',', $fieldArr);
        }
        $card_list = OilCardVice::getCardList($params, $field);
        if (!$card_list) {
            throw new \RuntimeException('没有找到相关的子账户信息', 2);
        }
        $station_id = isset($params['station_id']) && $params['station_id'] ? $params['station_id'] : '';
        $vice_ids = $limitMap = $oneShareCards = $orgIds = $stationArgs['data'] = [];
        foreach ($card_list as $value) {
            if (empty($value->deduction_account_no)) {
                $orgIds[] = $value->org_id;
            }

            //共享卡扣款账户是授信账户
            if ($value->oil_com == OilCom::GAS_FIRST_TALLY) {
                if ($value->deduction_account_no && substr($value->deduction_account_no, 0, 3) == '208') { //授信账户
                    $oneShareCards[] = $value->deduction_account_no;
                } else {
                    $orgIds[] = $value->org_id;
                }
            }

            $orgDel = isset($value->Org) ? $value->Org->is_del : 0;
            $vice_ids[$value->id] = ['id' => $value->id, "card_remain" => $value->card_remain, "is_del" => $orgDel, "status" => $value->status];

            $orgcode = isset($value->Org) && $value->Org->orgcode ? $value->Org->orgcode : '';
            //锁定的状态优先,油站限定,非使用的卡,不用在请求油站限定
            if ($value->status == '使用' && !empty($orgcode)) {
                //拼接油站限定参数
                $stationArgs['data'][] = [
                    'org_code' => $orgcode,
                    'station_id' => $station_id,
                    'vice_no' => $value->vice_no,
                    'oil_name' => isset($params['oil_name']) && $params['oil_name'] ? trim($params['oil_name']) : '',
                ];
            }
        }

        if ($station_id && count($stationArgs['data']) > 0) {
            $limitMap = self::batchCardLimit($stationArgs);
        }

        //获取共享卡现金账户
        $cashMap = self::cashAccountList($orgIds);

        //获取共享卡授信账户数据
        $shareMap = self::shareAccoutList($oneShareCards);

        //得到充值卡账户数据
        $chargeMap = self::chargeAccountList($vice_ids);

        $cardAccount = $normal = $exceptionList = $useing = [];

        foreach ($card_list as $key => $value) {
            $viceInfo['vice_no'] = $value->vice_no;
            $viceInfo['oil_com'] = $value->oil_com;
            $viceInfo['truck_no'] = empty($value->truck_no) ? '' : $value->truck_no;
            $viceInfo['driver_name'] = empty($value->driver_name) ? '' : $value->driver_name;
            $viceInfo['driver_tel'] = empty($value->driver_tel) ? '' : $value->driver_tel;
            $viceInfo['org_id'] = $value->org_id;
            $viceInfo['orgcode'] = isset($value->Org) && $value->Org->orgcode ? $value->Org->orgcode : '';
            $viceInfo['org_name'] = isset($value->Org) && $value->Org->org_name ? $value->Org->org_name : '';
            $viceInfo['is_selected'] = $value->is_use == 2 ? true : false; //正在使用的卡
            $viceInfo['selected'] = false;                              //正在使用的卡账户
            $viceInfo['balance'] = number_format($value->card_remain, 2, ".", "");
            $viceInfo['account_type'] = '';
            $viceInfo['product_name'] = "";
            $viceInfo['account_name'] = '';
            $viceInfo['account_no'] = '';
            $viceInfo['common_account_no'] = '';
            $viceInfo['consume_type'] = 2; // 1:长久消费,2:非长久消费
            $viceInfo['service_rate'] = 0;
            $viceInfo['station_available'] = isset($limitMap[$value->vice_no]) ? $limitMap[$value->vice_no]->status : TRUE;
            $viceInfo['station_available_desc'] = isset($limitMap[$value->vice_no]) ? $limitMap[$value->vice_no]->desc : "";
            $viceInfo['createtime'] = $value->createtime->format('Y-m-d H:i:s');
            $viceInfo['create_timestamp'] = $value->createtime->timestamp;
            $viceInfo['account_status'] = 10;
            $viceInfo['card_level'] = $value->card_level;

            $time = self::getTruckCardLeftSeconds($value);
            $viceInfo['left_seconds'] = $time['left_seconds'];
            $viceInfo['car_card_end_time'] = $time['car_card_end_time'];
            $viceInfo['truck_version'] = $time['truck_version'];

//            $viceInfo['card_status']            = $value->status == "使用" ? 10 : 20;
            if ($value->status == ViceCardStatus::USING) {
                $viceInfo['card_status'] = 10;
            } elseif ($value->status == ViceCardStatus::LOCKING) {
                $viceInfo['card_status'] = 20;
            } elseif ($value->status == ViceCardStatus::FREEZE) {
                $viceInfo['card_status'] = 30;
            } else {
                $viceInfo['card_status'] = -10;
            }
            $viceInfo['card_status_txt'] = $value->status;
            if ($viceInfo['card_status'] == 10 && !$viceInfo['station_available']) {
                $viceInfo['card_status_txt'] = $viceInfo['station_available_desc'];
            }
            $viceInfo['has_remain'] = 0;
            $viceInfo['ischeck'] = $value->ischeck;
            if (!empty($params['extra_fields']) && is_array($params['extra_fields'])) {
                foreach ($params['extra_fields'] as $tmpField) {
                    $viceInfo[$tmpField] = $value->$tmpField;
                }
            }

            if ($value->oil_com == OilCom::GAS_FIRST_TALLY) {
                $viceInfo['balance'] = '0.00'; //共享卡余额设置成0
                $value->account_list = [];
                if (isset($shareMap[$value->deduction_account_no]) && !empty($value->deduction_account_no) && substr($value->deduction_account_no, 0, 3) == '208') {
                    $viceInfo['selected'] = true;
                    $viceInfo['account_type'] = 20;
                    $viceInfo['balance'] = !$has_tally_balance ? "0.00" : $shareMap[$value->deduction_account_no]['balance']; //共享卡余额设置成0

                    $viceMap = array_merge($viceInfo, $shareMap[$value->deduction_account_no]);
                    if (bccomp(0, $shareMap[$value->deduction_account_no]['balance'], 2) >= 0) {
                        $viceMap['card_status_txt'] = "余额不足";
                    } else {
                        $viceMap['has_remain'] = 1;
                    }

                    if ($viceMap['selected'] && $viceMap['is_selected']) {
                        array_push($useing, $viceMap);
                    } else {
                        if ($viceMap['account_status'] == 10 && $viceMap['card_status'] == 10 && bccomp($shareMap[$value->deduction_account_no]['balance'], 0, 2) > 0 && $viceMap['station_available']) {
                            array_push($normal, $viceMap);
                        } else {
                            array_push($exceptionList, $viceMap);
                        }
                    }
                } else { //取现金账户
                    if (isset($cashMap[$value->org_id])) {
                        $viceInfo['common_account_no'] = $value->deduction_account_no;
                        $viceInfo['product_name'] = $value->deduction_account_name;
                        $viceInfo['selected'] = true;
                        $viceInfo['account_type'] = 10;
                        $viceInfo['account_name'] = ConsumeType::FIRST_TALLY_ACCOUNT_NAME;
                        $viceInfo['account_status'] = isset($vice_ids[$value->id]) && $vice_ids[$value->id]['is_del'] == 1 ? 20 : 10;
                        $viceInfo['product_name'] = $viceInfo['account_name'];
                        $viceInfo['account_no'] = isset($cashMap[$value->org_id]['account_no']) ? $cashMap[$value->org_id]['account_no'] : "";
                        $viceInfo['common_account_no'] = $viceInfo['account_no'];
                        $viceInfo['balance'] = !$has_tally_balance ? "0.00" : $cashMap[$value->org_id]['money']; //余额设置成0

                        if (isset($cashMap[$value->org_id]['money']) && bccomp(0, $cashMap[$value->org_id]['money'], 2) >= 0) {
                            $viceInfo['card_status_txt'] = "余额不足";
                        } else {
                            $viceInfo['has_remain'] = 1;
                        }
                        if ($viceInfo['selected'] && $viceInfo['is_selected']) {
                            array_push($useing, $viceInfo);
                        } else {
                            if ($viceInfo['account_status'] == 10 && $viceInfo['card_status'] == 10 &&
                                isset($cashMap[$value->org_id]['money']) && bccomp($cashMap[$value->org_id]['money'], 0, 2) > 0 && $viceInfo['station_available']) {
                                array_push($normal, $viceInfo);
                            } else {
                                array_push($exceptionList, $viceInfo);
                            }
                        }
                    }
                }
            } else {
                $cashList = isset($chargeMap[$value->id]) && isset($chargeMap[$value->id]['cash']) ? $chargeMap[$value->id]['cash'] : [];
                if (empty($cashList)) { //卡无现金子账户
                    try {
                        (new DingTalkAlarm())->alarmToGroup('小程序切换账户', '卡号：' . $value->vice_no . PHP_EOL . '无现金卡账户', [], TRUE, TRUE);
                    } catch (\Exception $e) {

                    }
                    continue;
                }

                if (count($cashList) > 0) {
                    if (isset($chargeMap[$value->id]) && isset($chargeMap[$value->id]['credit'])) {
                        $creditList = array_merge($chargeMap[$value->id]['credit'], $chargeMap[$value->id]['cash']);
                    } else {
                        $creditList = $chargeMap[$value->id]['cash'];
                    }
                    foreach ($creditList as $item) {
                        //排除发财卡的授信账户
                        if ($item['account_type'] == 20 && $value->oil_com == OilCom::FORTUNE_CARD) {
                            continue;
                        }
                        $viceInfo['selected'] = false;
                        if (($item['account_no'] == $value->deduction_account_no) || (empty($value->deduction_account_no) && $item['account_type'] == 10)) {
                            $viceInfo['selected'] = true;
                        }
                        $viceItem = array_merge($viceInfo, $item);
                        if ($viceItem['account_type'] == 10 && empty($viceItem['common_account_no'])) {
                            $viceItem['common_account_no'] = isset($cashMap[$value->org_id]['account_no']) ? $cashMap[$value->org_id]['account_no'] : "";
                        }
                        Log::error("item" . var_export($item, true), [], "account_");
                        if (bccomp(0, $viceItem['balance'], 2) >= 0) {
                            $viceItem['card_status_txt'] = "余额不足";
                        } else {
                            $viceItem['has_remain'] = 1;
                        }
                        if ($viceItem['selected'] && $viceItem['is_selected']) {
                            array_push($useing, $viceItem);
                        } else {
                            if ($viceItem['account_status'] == 10 && $viceItem['card_status'] == 10 && $viceItem['station_available'] && bccomp($viceItem['balance'], 0, 2) > 0) {
                                array_push($normal, $viceItem);
                            } else {
                                array_push($exceptionList, $viceItem);
                            }
                        }
                    }
                }
            }
            unset($value->Org);
        }
        //按照余额降序排序,余额一致时按照创建时间降序
        $balance = array_column($normal, 'balance');
        $createtime = array_column($normal, 'create_timestamp');
        array_multisort($balance, SORT_DESC, $createtime, SORT_DESC, $normal);
        $cardAccount = array_merge($normal, $exceptionList);
        if (!empty($useing)) {
            $cardAccount = array_merge($useing, $cardAccount);
        }
        return $cardAccount;
    }

    public static function batchCardLimit($stationArgs)
    {
        //请求gos油站限定
        $stationMap = [];
        try {
            $stationLimit = CardViceToGos::checkStationByOrgCodesAndStations($stationArgs);
            Log::error('$stationLimit', [$stationLimit], 'cardNewList_');

            foreach ($stationLimit as $value) {
                $stationMap[$value->vice_no] = $value;
            }
        } catch (\Exception $e) {
            Log::error('$stationLimitError', [], 'cardNewList_');
        }
        return $stationMap;
    }

    //充值卡数据
    public static function chargeAccountList($vice_ids)
    {
        if (count($vice_ids) == 0) {
            return [];
        }
        $maplist = OilCardAccount::leftJoin('oil_credit_account', 'oil_credit_account.account_no', '=', 'oil_card_account.common_account_no')
            ->leftJoin('oil_credit_provider', 'oil_credit_provider.id', '=', 'oil_credit_account.credit_provider_id')
            ->whereIn('oil_card_account.vice_id', array_keys($vice_ids))
            ->whereIn('oil_card_account.subAccountType', ['CREDIT', 'CASH'])
            ->selectRaw("oil_card_account.amount,oil_credit_provider.name,oil_credit_account.status,oil_credit_account.consume_type,oil_card_account.subAccountType,
                oil_card_account.cardSubAccountID,oil_credit_provider.status as p_status,oil_credit_provider.service_fee,
                oil_card_account.common_account_no,oil_card_account.vice_id,oil_card_account.createtime")
            ->get();

        if ($maplist) {
            $cardCreditListMap = [];
            foreach ($maplist as $item) {
                if ($item->subAccountType == 'CREDIT') {
                    if (empty($item->name)) {
                        continue;
                    }
                    $creditCode = $item->name;
                    if (stripos($creditCode, "(") !== false) {
                        $creditCode = substr($creditCode, stripos($creditCode, "(") + 1, -1);
                    }
                    $cardCreditListMap[$item->vice_id]['credit'][] = [
                        'product_name' => $creditCode,
                        'account_no' => $item->cardSubAccountID,
                        'account_type' => 20,
                        'account_name' => $item->name,
                        'balance' => $item->amount ? number_format($item->amount, 2, ".", "") : "0.00",
                        'account_status' => $item->status == 10 && $item->p_status == 10 ? 10 : 20,
                        'status' => $item->status == 10 && $item->p_status == 10 ? 10 : 20,
                        'service_rate' => $item->service_fee,
                        'common_account_no' => $item->common_account_no,
                        'consume_type' => 2,
                        'createtime' => $item->createtime->format('Y-m-d H:i:s'),
                    ];
                } elseif ($item->subAccountType == 'CASH') {
                    if (!array_key_exists('cash', $cardCreditListMap[$item->vice_id])) { //处理有的卡有多张卡现金账户
                        $cardCreditListMap[$item->vice_id]['cash'][] = [
                            'product_name' => ConsumeType::FIRST_CHARGE_ACCOUNT_NAME,
                            'account_no' => $item->cardSubAccountID,
                            'account_type' => 10,
                            'account_name' => ConsumeType::FIRST_CHARGE_ACCOUNT_NAME,
                            'balance' => isset($vice_ids[$item->vice_id]) && $vice_ids[$item->vice_id]['card_remain'] ? number_format($vice_ids[$item->vice_id]['card_remain'], 2, ".", "") : "0.00",
                            //'card_status' => isset($vice_ids[$item->vice_id]) && $vice_ids[$item->vice_id]['is_del'] == 1 ? 30 : 10,
                            'account_status' => isset($vice_ids[$item->vice_id]) && $vice_ids[$item->vice_id]['is_del'] == 1 ? 20 : 10,
                            'status' => isset($vice_ids[$item->vice_id]) && $vice_ids[$item->vice_id]['is_del'] == 1 ? 20 : 10,
                            'service_rate' => 0,
                            'common_account_no' => $item->common_account_no,
                            'consume_type' => 2,
                            'createtime' => $item->createtime->format('Y-m-d H:i:s'),
                        ];
                    }
                }
            }
            return $cardCreditListMap;
        } else {
            return [];
        }
    }

    //根据共享卡扣款账号,得到账户状态及服务费
    public static function shareAccoutList($oneShareCards)
    {
        if (count($oneShareCards) == 0) {
            return [];
        }
        //共享卡扣款账户是授信账户map
        $creditList = OilCreditAccount::leftJoin('oil_credit_provider', 'oil_credit_provider.id', '=', 'oil_credit_account.credit_provider_id')
            ->whereIn('account_no', $oneShareCards)
            ->selectRaw("oil_credit_provider.service_fee,oil_credit_account.account_no,oil_credit_provider.status as p_status,oil_credit_provider.name,
                oil_credit_account.status,oil_credit_account.subAccountID,oil_credit_provider.product_code,oil_credit_account.is_own,oil_credit_account.credit_total,oil_credit_account.used_total,oil_credit_account.consume_type")
            ->get();

        $oneshareMap = $_shareList = [];
        $card_account_list = [];

        if (count($creditList) > 0) {
            foreach ($creditList as $_item) {
                $creditCode = $_item->name;
                if (stripos($creditCode, "(") !== false) {
                    $creditCode = substr($creditCode, stripos($creditCode, "(") + 1, -1);
                }
                //得到授信账户余额
                $balance = number_format(($_item->credit_total - $_item->used_total), 2, ".", "");
                $status = $_item->status;
                if ($_item->p_status == 10 && $_item->status == 10) {
                    if ($_item->is_own == 0) {
                        $balance = "0.00";
                        $_shareList[] = strval($_item->subAccountID);
                        $card_account_list[$_item->subAccountID] = ['account_no' => $_item->account_no];
                    }
                }
                $oneshareMap[$_item->account_no] = [
                    'status' => $status,
                    "service_rate" => $_item->service_fee,
                    'balance' => $balance,
                    'account_no' => $_item->account_no,
                    'product_name' => $creditCode,
                    'account_type' => 20,
                    'account_name' => $_item->name,
                    'account_status' => $status,
                    'common_account_no' => $_item->account_no,
                    'consume_type' => $_item->consume_type
                ];
            }

            if (count($_shareList) > 0) {
                //批量远程请求获取状态
                $cardCreditList = (new \Fuel\Service\AccountCenter\AccountService())->getAccountList(['subAccountIDs' => $_shareList]);
                if (count($cardCreditList) > 0) {
                    foreach ($cardCreditList as $value) {
                        if (!isset($card_account_list[$value->subAccountID]) || empty($card_account_list[$value->subAccountID])) {
                            continue;
                        }
                        $_creditItem = $card_account_list[$value->subAccountID];
                        $balance = $value->restCreditAmount / 100;
                        if ($value->status == 'NORMAL') {
                            $oneshareMap[$_creditItem['account_no']]['account_status'] = 10;
                            $oneshareMap[$_creditItem['account_no']]['status'] = 10;
                            $oneshareMap[$_creditItem['account_no']]['balance'] = $balance;
                        } else {
                            $oneshareMap[$_creditItem['account_no']]['account_status'] = 20;
                            $oneshareMap[$_creditItem['account_no']]['status'] = 20;
                            $oneshareMap[$_creditItem['account_no']]['balance'] = "0.00";
                        }
                    }
                }
            }
        }
        return $oneshareMap;
    }

    //共享卡现金扣款账户
    public static function cashAccountList($orgIds = [])
    {
        $cashAccount = [];
        if (count($orgIds) == 0) {
            return $cashAccount;
        }
        $list = OilAccountMoney::getByOrgIdList(array_unique($orgIds));
        foreach ($list as $_item) {
            $cashAccount[$_item->org_id] = ['org_id' => $_item->org_id, "account_no" => $_item->account_no, "money" => $_item->money];
        }
        return $cashAccount;
    }

    /**
     * 取卡信息及当前使用账户
     *
     * @param array $params
     * @return array|mixed
     */
    public static function getCardInfoWithCurrentAccount(array $params,$trace_id = '')
    {
        helper::argumentCheck(['vice_noOnly'], $params);
        $params['extra_fields'] = ['ischeck', 'paylimit', 'oil_top', 'day_top', 'month_top', 'card_level', 'limit_type'];
        $cardAccountList = self::cardListWithAccount($params);

        Log::error("1params:" . var_export($params, true), [$trace_id], "getCardInfoWithCurrentAccount_");

        $currentCardAccountInfo = [];
        foreach ($cardAccountList as $value) {
            if ($value['selected']) {
                $currentCardAccountInfo = $value;
                //解决福佑下单超时问题，理论上对接客户的卡都没有限额
                if( $currentCardAccountInfo['paylimit'] == 1 ) {
                    $dayTop = OilCardVice::getSumTrade(['vice_no' => $value['vice_no'], 'limitType' => 1]);
                    Log::error("dayTop:" . var_export($params, true), [$trace_id], "getCardInfoWithCurrentAccount_");
                    $monthTop = OilCardVice::getSumTrade(['vice_no' => $value['vice_no'], 'limitType' => 2]);
                    Log::error("monthTop:" . var_export($params, true), [$trace_id], "getCardInfoWithCurrentAccount_");
                }else{
                    $monthTop = $dayTop = 0;
                }
                $caculatePayLimit = false;
                // 日次月限额
                if ($value->oil_com == OilCom::GAS_FIRST_TALLY) {
                    if (substr($value['deduction_account_no'], 0, 3) == '208') {
                        $caculatePayLimit = true;
                    }
                } else {
                    if ($value['account_type'] == 20) {
                        $caculatePayLimit = true;
                    }
                }

                if (!$caculatePayLimit) {
                    $creditLimit = ConsumeType::getCreditLimit($value['orgcode']);
                    $currentCardAccountInfo['oil_top'] = $value['oil_top'] > $creditLimit['oil_top'] ? sprintf('%.2f', $creditLimit['oil_top']) : sprintf('%.2f', $value['oil_top']);
                    $currentCardAccountInfo['day_top'] = $value['day_top'] > $creditLimit['day_top'] ? sprintf('%.2f', $creditLimit['day_top']) : sprintf('%.2f', $value['day_top']);
                    $currentCardAccountInfo['month_top'] = $value['month_top'] > $creditLimit['month_top'] ? sprintf('%.2f', $creditLimit['month_top']) : sprintf('%.2f', $value['month_top']);
                }

                // 日限额
                $tmpDay = bcsub($currentCardAccountInfo['day_top'], $dayTop, 2);
                $currentCardAccountInfo['day_top'] = ($tmpDay <= 0) ? '0.00' : $tmpDay;
                // 月限额
                $tmpMonth = bcsub($currentCardAccountInfo['month_top'], $monthTop, 2);
                if ($tmpDay <= 0 || $tmpMonth <= 0) {
                    $currentCardAccountInfo['month_top'] = '0.00';
                } else {
                    $currentCardAccountInfo['month_top'] = $tmpMonth;
                }
                // 日限额大于月限额时取月限额
                if (bccomp($currentCardAccountInfo['day_top'], $currentCardAccountInfo['month_top'], 2) > 0) {
                    $currentCardAccountInfo['day_top'] = $currentCardAccountInfo['month_top'];
                }
                // 次限额大于日限额时取日限额
                if (bccomp($value['oil_top'], $currentCardAccountInfo['day_top'], 2) > 0) {
                    $currentCardAccountInfo['oil_top'] = $currentCardAccountInfo['day_top'];
                }
            }
        }

        Log::error("2params:" . var_export($params, true), [$trace_id], "getCardInfoWithCurrentAccount_");

        // 根据机构配置取扣款账户及余额
        if (!empty($currentCardAccountInfo)) {
            $orgDeductSetting = (new OrgConfigService())->getOrgDeductSetting($currentCardAccountInfo['orgcode']);
            if (isset($orgDeductSetting['deduct_method']) && $orgDeductSetting['deduct_method'] == 20) {
                $deductAccountNo = OilCardVice::getResField(['vice_no' => $currentCardAccountInfo['vice_no']], 'deduction_account_no');
                // 取扣款账户机构 仅针对现金
                if (!empty($deductAccountNo) && substr($deductAccountNo, 0, 3) == '108') {
                    $deductAccountInfo = OilAccountMoney::getByAccountNo(['account_no' => $deductAccountNo]);
                    $deductAccountInfo = !$deductAccountInfo ? [] : $deductAccountInfo->toArray();
                    if (!empty($deductAccountInfo['org']['orgcode']) && $deductAccountInfo['org']['orgcode'] != $currentCardAccountInfo['orgcode']) {
                        $currentCardAccountInfo['account_no'] = $deductAccountInfo['account_no'];
                        $currentCardAccountInfo['balance'] = $deductAccountInfo['money'];
                    }
                }
            }

        }

        Log::error("3params:" . var_export($params, true), [$trace_id], "getCardInfoWithCurrentAccount_");

        return $currentCardAccountInfo;
    }

    /**
     * @param $params
     * @return int|mixed
     */
    public function deleteTgCardByIds($params)
    {
        $result = 0;
        $card = OilCardVice::getViceByOrgCodeAndIds($params);

        if (count($card) > 0) {
            $idMap = [];
            foreach ($card as $v) {
                $idMap[] = $v->id;
            }

            $result = OilCardVice::updateByIds($idMap, ['status' => '废弃']);
        }
        return $result;
    }

    /**
     * @param $params
     * @return array
     * <AUTHOR> <<EMAIL>>
     * @since 2021/7/14 6:45 下午
     */
    public static function getQueryBalanceTaskResultByTaskId($params)
    {
        helper::argumentCheck(['task_id'], $params);
        $data = Cache::get("query_card_balance_task_{$params['task_id']}");
        if ($data) {

            $taskArg = Cache::get("query_card_balance_task_args_{$params['task_id']}");
            foreach ($data as $v) {

                if ($v['cardNo'] == $taskArg) {

                    return [
                        "code" => 0,
                        "msg" => "查询成功",
                        "data" => $v,
                    ];
                }
            }
        }
        return [
            "code" => 5001009,
            "msg" => "查询卡片余额任务尚未完成,请稍后重试",
            "data" => [],
        ];
    }

    /**
     * 校验卡数量
     */
    static public function checkCardNum($org_code, $oil_com, $num)
    {
        //增加是否限制开卡校验
        $limitRule = (new OrgConfigService())->getOrgOpenCardLimit($org_code);
        Log::error('checkCardNum', [$limitRule], 'checkCardNum');
        if ($limitRule && in_array($oil_com, OilCom::getFirstList())) {
            if ($limitRule['is_limit'] == 1 && $limitRule['open_charge_card_num'] == 0 && $limitRule['open_share_card_num'] == 0) {
                throw new \RuntimeException('该机构限制开卡，请联系客服', 2);
            }

            //限制开卡数量校验
            if ($limitRule['is_limit'] == 1 && $limitRule['open_charge_card_num'] >= 0 && $oil_com == OilCom::GAS_FIRST_CHARGE) {
                //查询已开卡数量
                $openedCardNum = OilCardVice::getCardNumByOrgCode(['orgcode' => $org_code, 'oil_com' => $oil_com]);
                Log::error('openedCardNum', [$openedCardNum], 'checkCardNum');
                if (($openedCardNum + $num) > $limitRule['open_charge_card_num']) {
                    throw new \RuntimeException('该机构限制开卡数量，请联系客服', 2);
                }
            }

            //限制开卡数量校验
            if ($limitRule['is_limit'] == 1 && $limitRule['open_share_card_num'] >= 0 && $oil_com == OilCom::GAS_FIRST_TALLY) {
                //查询已开卡数量
                $openedCardNum = OilCardVice::getCardNumByOrgCode(['orgcode' => $org_code, 'oil_com' => $oil_com]);
                Log::error('openedShareCardNum', [$openedCardNum], 'checkCardNum');
                if (($openedCardNum + $num) > $limitRule['open_share_card_num']) {
                    throw new \RuntimeException('该机构限制开卡数量，请联系客服', 2);
                }
            }
        }

        return true;

    }

    public static function exportReportLossCard()
    {
        $limit = 1000;
        $page = 1;
        $format = "
SELECT vice_no, oil_com, remain_get_time 
FROM oil_card_vice WHERE `status` = '挂失' AND oil_com IN (1, 2,26, 52) 
limit {$limit} offset %s";

        $zsh = $zsy = $chai = $zsy_dz = [];
        while (true) {
            $offset = ($page - 1) * $limit;

            $data = Capsule::connection('slave')->select(sprintf($format, $offset));
            if (empty($data))
                break;

            foreach ($data as $item) {
                $tmp = [
                    'vice_no' => $item->vice_no,
                    'remain_get_time' => $item->remain_get_time
                ];
                switch ($item->oil_com) {
                    case OilCom::ZSH:
                        $tmp['oil_com'] = '中石化';
                        $zsh[] = $tmp;
                        break;
                    case OilCom::ZSY:
                        $tmp['oil_com'] = '中石油';
                        $zsy[] = $tmp;
                        break;
                    case OilCom::ZCW_ZSH_CYZYK:
                        $tmp['oil_com'] = '柴油联名卡';
                        $chai[] = $tmp;
                        break;
                    case OilCom::ZSY_DZ:
                        $tmp['oil_com'] = OilCom::ZSY_DZ_NAME;
                        $zsy_dz[] = $tmp;
                        break;
                }
            }
            $page++;
            usleep(100);
        }

        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data';
        $fileExt = 'xlsx';

        $title = [
            'vice_no' => '卡号',
            'oil_com' => '油卡类型',
            'remain_get_time' => '代理余额同步时间',
        ];

        $exportData = [
            'filePath' => $filePath,
            'download' => 1,
            'fileName' => '挂失卡_' . date("YmdHis"),
            'sheetName' => '中石化',
            'fileExt' => $fileExt,
            'title' => $title,
            'multiSheet' => [
                [
                    'sheetName' => '中石油',
                    'title' => $title,
                    'data' => $zsy
                ],
                [
                    'sheetName' => '柴油联名卡',
                    'title' => $title,
                    'data' => $chai
                ],
                [
                    'sheetName' => '电子卡-中石油',
                    'title' => $title,
                    'data' => $zsy_dz
                ]
            ],
            'data' => $zsh,
        ];

        try {
            $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
                if (in_array($data['name'], ['vice_no'])) {
                    $phpExcelObj->setCellValueExplicit($lineCell['cellNum'] . $lineCell['lineNum'], $data['value'], \PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
                }
            });
            Log::error(__METHOD__ . "#导出文件：" . $fileUrl, [], 'report_loss_card');
        } catch (\Exception $exception) {
            Log::error(__METHOD__ . "#导出异常：", [strval($exception)], 'report_loss_card');
        }
        return $fileUrl;
    }

    protected static function checkTruckCardGetParam($viceNo, $mobile)
    {
        helper::argumentCheck(['vice_no', 'mobile'], ['vice_no' => $viceNo, 'mobile' => $mobile]);

        $vice = OilCardVice::getByViceNo(['vice_no' => $viceNo]);
        if (empty($vice)) {
            throw new \RuntimeException('卡不存在', ErrorCode::ERROR_VICE_NOT_EXIST);
        }

        if ($vice->card_level != CardViceConf::CARD_LEVEL_CAR) {
            throw new \RuntimeException('此卡不是车辆卡', ErrorCode::ERROR_VICE_NOT_CAR);
        }

        if (!in_array($vice->status, [CardViceConf::STATUS_USE])) {
            throw new \RuntimeException('卡账户状态异常', ErrorCode::ERROR_VICE_STATUS_EXCEPTION);
        }

        try {
            $white = GasClient::post([
                'method' => 'gas.api.checkTruckCardTake',
                'data' => [
                    'driver_phone' => $mobile,
                    'card_no' => $viceNo,
                ],
            ]);

            Log::error('请求 gas.api.checkTruckCardTake', [$viceNo, $mobile, $white], 'getTruckCard');

            if (empty($white)) {
                // 无权限使用
                throw new \RuntimeException('无权限领取', ErrorCode::ERROR_VICE_NO_AUTHORITY_GET);
            }

            $record = OilTruckCardGet::where('mobile', $mobile)->orderBy('start_time', 'desc')->first();
            if ($record) {
                if (time() <= strtotime($record->end_time)) {
                    throw new \RuntimeException('您已领取车辆卡', ErrorCode::ERROR_VICE_GET);
                }

                // 查询待支付订单
                $order = (new FossOrderService())->getOrderPaginate([
                    'card_no' => $record->vice_no,
                    'driver_phone' => $record->mobile,
                    'order_status' => 1
                ]);

                if (!isset($order['count'])) {
                    throw new \RuntimeException('foss-order服务异常', ErrorCode::ERROR_FOSS_ORDER_SERVICE_EXCEPTION);
                }

                if ($order['count'] > 0) {
                    throw new \RuntimeException('您已领取车辆卡', ErrorCode::ERROR_VICE_GET);
                }
            }

            $record = OilTruckCardGet::where('vice_no', $viceNo)->orderBy('start_time', 'desc')->first();
            if ($record) {
                if (time() <= strtotime($record->end_time)) {
                    throw new \RuntimeException('车辆账户正在被占用', ErrorCode::ERROR_VICE_ACCOUNT_OCCUPY);
                } else {
                    // 查询待支付订单
                    $order = (new FossOrderService())->getOrderPaginate([
                        'card_no' => $viceNo,
                        'driver_phone' => $record->mobile,
                        'order_status' => 1
                    ]);

                    if (!isset($order['count'])) {
                        throw new \RuntimeException('foss-order服务异常', ErrorCode::ERROR_FOSS_ORDER_SERVICE_EXCEPTION);
                    }

                    if ($order['count'] > 0) {
                        throw new \RuntimeException('车辆账户正在被占用', ErrorCode::ERROR_VICE_ACCOUNT_OCCUPY);
                    }
                }
            }
        } catch (\Exception $e) {
            //Log
            Log::error('获取车辆卡 参数验证 异常', [$viceNo, $mobile, strval($e)], 'getTruckCard');

            throw $e;
        }

        return [$vice, $white];
    }

    /**
     * 车辆卡领取
     * @param $viceNo
     * @param $mobile
     * @throws \Exception
     */
    public static function getTruckCard($viceNo, $mobile,array $params)
    {
        helper::argumentCheck(['vice_no', 'mobile'], ['vice_no' => $viceNo, 'mobile' => $mobile]);

        list($vice, $white) = self::checkTruckCardGetParam($viceNo, $mobile);

        Capsule::connection()->beginTransaction();
        try {
            $now = date('Y-m-d H:i:s');
            $data = [
                'vice_card_id' => $vice->id,
                'mobile' => $mobile,
                'driver_name' => $white->drivername,
                'vice_no' => $viceNo,
                'truck_no' => $vice->truck_no,
                'get_type' => isset($params['get_type']) && !empty($params['get_type']) ? $params['get_type'] : 10,
                'start_time' => $now,
                'end_time' => date('Y-m-d H:i:s', strtotime($now) + 10 * 60),
                'createtime' => $now,
                'updatetime' => $now
            ];
            OilTruckCardGet::add($data);

            OilCardVice::edit([
                'id' => $vice->id,
                'driver_tel' => $mobile,
                'driver_name' => $white->drivername,
                'is_use' => CardViceConf::USING_NO
            ]);

            Capsule::connection()->commit();
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();

            Log::error('获取车辆卡 异常', [$viceNo, $mobile, strval($e)], 'getTruckCard');

            throw $e;
        }

        // 更新gas 卡表
        try {
            $gasParams = [
                'card_no' => [$viceNo],
                'driverphone' => $mobile,
                'drivername' => $white->drivername,
                'gascode' => strtoupper($vice->Org->orgcode)
            ];
            GasClient::post([
                'method' => 'gas.api.cardSet',
                'data' => $gasParams
            ]);

            CardViceToGos::batchUpdateToGos($gasParams['card_no'], "sync");
        } catch (\Exception $e) {
            Log::error('同步gas gos 异常', [$viceNo, $mobile, strval($e)], 'getTruckCard');

            self::pushTruckCardErrorToFeiShu('车辆卡领取', '副卡信息同步 gas gos 异常', $viceNo);
        }
    }

    protected static function canReturn($vice, $record, $active = true)
    {
        $ret = [
            'return' => false,
            'update_end_time' => false
        ];

        if (empty($vice) || empty($record)) {
            $logContent = [];
            if (! empty($vice)) {
                $logContent['vice'] = $vice->toArray();
            }
            if (! empty($record)) {
                $logContent['record'] = $record->toArray();
            }

            Log::error('车辆卡领取记录不存在', $logContent, CardConsumeService::LOG_CHANNEL);
            return $ret;
        }

        try {
            // 查询待支付订单
            $order = (new FossOrderService())->getOrderPaginate([
                'card_no' => $record->vice_no,
                'driver_phone' => $record->mobile,
                'order_status' => 1
            ]);

            if (!isset($order['count']) || $order['count'] > 0) {
                return $ret;
            }

            $expire = time() > strtotime($record->end_time) ? true : false;

            // 无待支付订单
            if (!$expire && $active) {
                $ret['return'] = $ret['update_end_time'] = true;

                return $ret;
            }

            if ($expire) {
                $ret['return'] = true;

                return $ret;
            }
        } catch (\Exception $e) {
            return $ret;
        }

        return $ret;
    }

    public static function returnTruckCard($params)
    {
        foreach ($params as $item) {
            if (!is_array($item) || empty($item['mobile']) || empty($item['vice_no']))
                continue;

            $vice = OilCardVice::getByViceNo(['vice_no' => $item['vice_no']]);
            if (empty($vice)) {
                Log::error('副卡不存在', ['params' => $item], CardConsumeService::LOG_CHANNEL);
                continue;
            }
            if ($vice->card_level != CardViceConf::CARD_LEVEL_CAR) {
                Log::error('副卡不是车辆卡，无需归还', ['params' => $item], CardConsumeService::LOG_CHANNEL);
                continue;
            }
            if (empty($vice->driver_tel)) {
                Log::error('车辆卡已归还', ['params' => $item], CardConsumeService::LOG_CHANNEL);
                continue;
            }

            if ($vice->driver_tel != $item['mobile']) {
                Log::error('车辆卡绑定手机号异常，无法归还', ['params' => $item, 'vice' => $vice->toArray()], CardConsumeService::LOG_CHANNEL);

//                self::pushTruckCardErrorToFeiShu('车辆卡归还', '车辆卡绑定手机号异常', $item['vice_no']);

                continue;
            }

            $record = OilTruckCardGet::Filter([
                'mobile' => $item['mobile'],
                'vice_no' => $item['vice_no']
            ])->orderBy('start_time', 'desc')->first();

            $res = self::canReturn($vice, $record, isset($item['active']) && $item['active']);

            if (!$res['return']) {
                Log::error('不可以归还', ['params' => $item], CardConsumeService::LOG_CHANNEL);
                continue;
            }

            Capsule::connection()->beginTransaction();
            try {
                $now = date('Y-m-d H:i:s');
                if ($res['update_end_time']) {
                    OilTruckCardGet::edit([
                        'id' => $record->id,
                        'end_time' => $now,
                        'updatetime' => $now
                    ]);
                }

                OilCardVice::edit([
                    'id' => $vice->id,
                    'driver_tel' => '',
                    'driver_name' => '',
                    'is_use' => CardViceConf::USING_NO
                ]);

                Capsule::connection()->commit();
            } catch (\Exception $e) {
                Capsule::connection()->rollBack();

                Log::error('归还车辆卡异常', ['params' => $item, strval($e)], CardConsumeService::LOG_CHANNEL);

                self::pushTruckCardErrorToFeiShu('车辆卡归还', $e->getMessage(), $record->vice_no);

                continue;
            }

            // 更新gas 卡表
            try {
                $gasParams = [
                    'card_no' => [$record->vice_no],
                    'driverphone' => '',
                    'drivername' => '',
                    'gascode' => strtoupper($vice->Org->orgcode)
                ];
                GasClient::post([
                    'method' => 'gas.api.cardSet',
                    'data' => $gasParams
                ]);

                CardViceToGos::batchUpdateToGos($gasParams['card_no'], "sync");
            } catch (\Exception $e) {
                Log::error('归还车辆卡异常 同步gas gos 异常', ['params' => $item, strval($e)], CardConsumeService::LOG_CHANNEL);

                self::pushTruckCardErrorToFeiShu('车辆卡归还', '同步gas gos 异常', $record->vice_no);
            }

            Log::error('归还车辆卡成功', $item, CardConsumeService::LOG_CHANNEL);
        }
    }

    public static function pushTruckCardErrorToFeiShu($subject, $errMsg, $viceNo = '')
    {
        $content = [
            "* 项目：{$subject}",
            "* 错误信息：{$errMsg}",
        ];

        if ($viceNo) {
            $content[] = "* 副卡号：" . $viceNo;
        }

        (new DingTalkAlarm())->alarmToGroup($subject, implode("\n\n", $content), [], true, false);
    }

    public static function returnTruckCardCron()
    {
        $page = 1;
        $limit = 300;

        $redis = Cache::getInstance(CardConsumeService::REDIS_CONFIG);

        while (true) {
            $offset = ($page - 1) * $limit;

            $data = OilCardVice::Filter([
                'card_from' => CardFrom::GAS_CARD,
                'card_level' => CardViceConf::CARD_LEVEL_CAR,
                'driver_tel_neq_blank' => true
            ])->offset($offset)
                ->limit($limit)
                ->pluck('id')->toArray();

            if (empty($data)) {
                break;
            }

            $records = OilTruckCardGet::whereIn('id', function ($query) use ($data) {
                $query->selectRaw('max(id)')
                    ->from('oil_truck_card_get')
                    ->whereIn('vice_card_id', $data)
                    ->groupBy('vice_card_id');
            })->get();

            $ret = [];
            foreach ($records as $record) {
                if (time() <= strtotime($records->end_time))
                    continue;

                $tmp = [
                    'mobile' => $record->mobile,
                    'vice_no' => $record->vice_no,
                    'active' => false
                ];
                $ret[] = $tmp;
            }

            if (!empty($ret)) {
                $msg = [
                    'trace_id' => uniqid(),
                    'module' => 'truck_card',
                    'action' => 'return',
                    'data' => $ret
                ];
                $redis->rpush(CardConsumeService::QUEUE, json_encode($msg));
                Log::error('车辆卡归还异步任务分发 ', $msg, 'returnTruckCardCron');
            }

            $page++;
        }
    }

    /**
     * @param array $params
     * 设置卡的扣款账户
     */
    public static function setCardAccount($params = [])
    {
        global $app;
        if( !in_array($app->myAdmin->id,$app->config->customer->transferUserIds) ){
            throw new \RuntimeException('无权限操作，请联系【产品】进行操作', 2);
        }
        $info = OilCardVice::getInfoByFilter(['vice_no' => $params['vice_no']]);
        if (!$info) {
            throw new \RuntimeException('卡号不正确', 2);
        }

        if (!in_array($info->oil_com, OilCom::getAllFirstList())) {
            throw new \RuntimeException('仅允许设置电子卡', 2);
        }

        $orgInfo = OilOrg::getById(['id' => $info->org_id]);
        if (!$orgInfo) {
            throw new \RuntimeException('卡所属机构不存在', 2);
        }

        $flag = substr($params['account_no'], 0, 3);
        if (!in_array($flag, ['108', '208'])) {
            throw new \RuntimeException('账户类型不合法', 2);
        }
        $orgId = "";
        $name = "现金账户";
        if ($flag == '208') {
            $credit = OilCreditAccount::getNolockByNo($params['account_no']);
            if (!$credit) {
                throw new \RuntimeException('账户不存在', 2);
            }
            $name = "授信账户";
            if(!empty($credit->credit_provider_id)) {
                $provider = OilCreditProvider::getById(['id' => $credit->credit_provider_id]);
                if($provider && !empty($provider->name)) {
                    $name = $provider->name;
                }
            }
            $orgId = $credit->org_id;
        } else {
            $cash = OilAccountMoney::getByAccountNos([$params['account_no']]);
            if (count($cash) <= 0) {
                throw new \RuntimeException('账户不存在', 2);
            }
            $orgId = $cash[0]->org_id;
        }

        $org = OilOrg::getById(['id' => $orgId]);
        if (!$org) {
            throw new \RuntimeException('账户归属机构不存在', 2);
        }

        if (substr($orgInfo->orgcode, 0, 6) != substr($org->orgcode, 0, 6)) {
            throw new \RuntimeException('账户机构与卡机构不属于同一机构树', 2);
        }

        return OilCardVice::edit(['id' => $info->id, "deduction_account_no" => $params['account_no'], "deduction_account_name" => $name]);
    }

    public static function batchUpdate($params = [])
    {
        $viceNos = array_column($params['details'],"vice_no");
        if(count($viceNos) == 0){
            throw new \RuntimeException('卡号不能为空', 2);
        }
        $list = OilCardVice::getCardList(['vice_noIn'=>$viceNos,"_export"=>1]);
        if(count($list) == 0){
            throw new \RuntimeException('卡信息不存在', 2);
        }
        $map = [];
        foreach ($list as $_item){
            if( substr($params['orgcode'],0,6) != substr($_item->Org->orgcode,0,6) ){
                throw new \RuntimeException('不能跨顶级修改卡信息：'.$_item->vice_no, 2);
            }
            $map[$_item->vice_no] = ['id'=>$_item->id];
        }
        foreach ($params['details'] as $_val){
            if( empty($_val['vice_no']) ){
                throw new \RuntimeException('参数不合法，卡号不能为空', 2);
            }
            if( !in_array($_val['vice_no'],array_keys($map)) ){
                throw new \RuntimeException('参数不合法，卡号不存在：'.$_val['vice_no'], 2);
            }
            $updata = [];
            $updata['id'] = $map[$_val['vice_no']]['id'];
            if( isset($_val['driver_name']) && !empty($_val['driver_name']) ) {
                $updata['driver_name'] = $_val['driver_name'];
            }
            if( isset($_val['truck_no']) && !empty($_val['truck_no']) ) {
                $updata['truck_no'] = $_val['truck_no'];
            }
            if( isset($_val['driver_tel']) && !empty($_val['driver_tel']) ) {
                $updata['driver_tel'] = $_val['driver_tel'];
            }
            $updata['updatetime'] = helper::nowTime();
            OilCardVice::edit($updata);
        }
        CardViceToGos::batchUpdateToGos($viceNos);
        return $params['details'];
    }
}