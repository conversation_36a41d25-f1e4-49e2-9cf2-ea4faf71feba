<?php
/**
 * 与g7pay交互
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/5/23/023
 * Time: 11:27
 */

namespace Fuel\Service;

use Framework\Config;
use Framework\Log;

class BdMap
{
    /**
     * 根据经纬度获取油站信息
     * @param array $params
     * @return mixed
     */
    static public function getStationByLocation(array $params)
    {
        $lat = $params['lat']; //纬度值
        $lng = $params['lng']; //经度值

        $args = [];
        $args['query'] = isset($params['query']) && $params['query'] ? $params['query'] : Config::get('map.query');
        $args['location'] = $lat.','.$lng;
        $args['page_size'] = 20;
        $args['page_num'] = 0;
        $args['scope'] = isset($params['scope']) && $params['scope'] ? $params['scope'] : Config::get('map.scope');
        $args['radius'] = isset($params['radius']) && $params['radius'] ? $params['radius'] : Config::get('map.radius');
        $args['output'] = isset($params['output']) && $params['output'] ? $params['output'] : Config::get('map.output');
        $args['ak'] = Config::get('map.ak');

        unset($params);

        $strArgs = http_build_query($args);

        $url = Config::get('map.apiUrl');

        try{
            $output = file_get_contents($url.'?'.$strArgs);
            $result = json_decode($output);
            if(json_last_error() !== JSON_ERROR_NONE) {
                Log::error('参数格式错误:'.$output,[],'BdError');
                throw new \RuntimeException(json_last_error_msg(),2);
            }
        }catch (\Exception $e){
            throw new \RuntimeException($e->getMessage(),$e->getCode());
        }

        return $result;
    }

}