<?php

namespace Fuel\Service;

use Framework\DingTalk\FeiShuNotify;
use Framework\Helper;
use Framework\Log;
use Fuel\Defines\DongLiBaoRepay;
use Fuel\Defines\FanliAudit;
use helper as GlobalHelper;
use Models\OilAccountMoneyCharge;
use Models\OilCardViceTrades;
use Models\OilCreditBill;
use Models\OilCronMonitor;
use Models\OilOperators;
use Models\OilOrg;
use Models\OilOrgChangeOperatorDetails;
use Models\OilOrgChangeOperatorLog;
use RuntimeException;

class OrgChangeOperatorService
{

    //获取机构运营商
    static public function getOrgOperator($params = [])
    {
        $result = [];
        $change = self::getOrgChangeData(['org_code'=>$params['orgcode']]);
        $operatorMap = OilOperators::getIdMapName('company_name');

        $orgInfo = OilOrg::getByOrgcode($params['orgcode']);
        $operator_name = isset($operatorMap[$orgInfo->operators_id]) ? $operatorMap[$orgInfo->operators_id] : "";
        $firstOperator = ['id'=>$orgInfo->operators_id,"name"=>$operator_name,"is_select"=>1];

        if(count($change['changeList']) == 0){
            $result[] = $firstOperator;
        }else{
            foreach ($change['changeList'] as $_val){
                $isSelect = 2;//未选中
                if($_val->status == OilOrgChangeOperatorLog::SIGN_SUCCESS){
                    $after = $_val->after_operator_id;
                    $before = $_val->before_operator_id;

                    $operator_name = isset($operatorMap[$after]) ? $operatorMap[$after] : "";
                    $result[$after] = ['id'=>$after,"name"=>$operator_name,"is_select"=>$isSelect];

                    $operator_name = isset($operatorMap[$before]) ? $operatorMap[$before] : "";
                    $result[$before] = ['id'=>$before,"name"=>$operator_name,"is_select"=>$isSelect];
                    if($after == $change['operator_id']){
                        $result[$after]['is_select'] = 1;
                    }
                    if( $before == $change['operator_id'] ){
                        $result[$before]['is_select'] = 1;
                    }
                }else{ //G7WALLET-6695 换签标记中，让客户提交换签之前的发票:204PII,2056H0，如有问题先不要注释
                  $_tmpId = $_val->before_operator_id;
                  $operator_name = isset($operatorMap[$_tmpId]) ? $operatorMap[$_tmpId] : "";
                  $result[$_tmpId] = ['id'=>$_tmpId,"name"=>$operator_name,"is_select"=>1];
                }
            }
            if(!$result){
                //$result[] = $firstOperator;
            }

        }
        return array_values($result);
    }

    //获取机构换签记录
    static public function getOrgChangeData($params = [])
    {
        $orgInfo = new \stdClass();
        if (isset($params['org_id']) && !empty($params['org_id'])) {
            $orgInfo = OilOrg::getById(['id' => $params['org_id']]);
        }
        if (isset($params['org_code']) && !empty($params['org_code'])) {
            $orgInfo = OilOrg::getByOrgcode($params['org_code']);
        }
        if (!$orgInfo) {
            throw new RuntimeException('机构信息不存在', 2);
        }
        $org_code = substr($orgInfo->orgcode, 0, 6);
        if (empty($org_code)) {
            throw new RuntimeException('机构编码不合法', 2);
        }
        $changeList = OilOrgChangeOperatorDetails::getChangeDataByFilter(['orgroot' => $org_code]);
        if ($orgInfo->receipt_mode == 2) {
            $org_code = $orgInfo->orgcode;
        }
        return ['org_code' => $org_code, "changeList" => $changeList,"operator_id"=>$orgInfo->operators_id];
    }

    static public function getChangeLog($tradeInfo)
    {
        Log::error('消费撤销检测换签,getChangeLog：',[$tradeInfo->org_id],"cancelConsume");
        $changeData = self::getOrgChangeData(['org_id' => $tradeInfo->org_id]);
        $change = $changeData['changeList'];
        Log::error('消费撤销检测换签记录,getChangeLog：',[$change],"cancelConsume");
        if (count($change) == 0) {
            return 2;
        }
        foreach ($change as $_item){
            if( in_array($_item->sub_status,[ OilOrgChangeOperatorLog::SIGN_SUCCESS ]) && $tradeInfo->org_operator_id == $_item->before_operator_id ){
                return 1;
            }
        }
        return 2;
    }

    static public function setTradeCancel($tradeInfo)
    {
        try {
            Log::error('消费撤销检测换签：',[$tradeInfo->org_id],"cancelConsume");
            $changeData = self::getOrgChangeData(['org_id' => $tradeInfo->org_id]);
            $change = $changeData['changeList'];
            Log::error('消费撤销检测换签记录：',[$change],"cancelConsume");
            if (count($change) == 0) {
                return true;
            }

            $updateId = 0;
            $updateSubId = 0;
            foreach ($change as $_val) {
                if ($_val->before_operator_id == $tradeInfo->org_operator_id) {
                    if ($_val->sub_status == OilOrgChangeOperatorLog::SIGN_CHECK) {
                        $updateId = $_val->id;
                        $updateSubId = $_val->sub_id;
                    }
                    /*if ($_val->sub_status == OilOrgChangeOperatorLog::SIGN_SUCCESS) {
                        throw new RuntimeException('机构换签成功，转出运营商消费不允许撤销', 20);
                    }*/
                    if ($_val->sub_status == OilOrgChangeOperatorLog::SIGN_END_START) {
                        throw new RuntimeException('机构换签终止中，转出运营商消费不允许撤销', 21);
                    }
                }
            }

            if (!empty($updateId) && !empty($updateSubId)) {
                OilOrgChangeOperatorLog::edit(["id" => $updateId, "status" => 10, "updatetime" => \helper::nowTime()]);
                OilOrgChangeOperatorDetails::edit(["id" => $updateSubId, "status" => 10, "updatetime" => \helper::nowTime()]);
            }
            return true;
        } catch (\Exception $e) {
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    //系统标记任务
    //is_cron 1:cron 2:非cron
    static public function checkSignChange($params = [])
    {
        try {
            if (!isset($params['is_cron'])) {
                $params['is_cron'] = 1;
            }

            $params['status'] = OilOrgChangeOperatorLog::SIGN_SYSTEM;
            $change = OilOrgChangeOperatorDetails::getChangeDataByFilter($params);
            Log::error("系统标记,长度：".count($change),[$params],"sign_mark_");
            if (count($change) == 0) {
                throw new RuntimeException('没有系统标记中的换签记录', 201);
            }
            $end_time = date("Y-m-d") . " 00:00:00";
            if ($params['is_cron'] != 1) {
                $end_time = date("Y-m-d H:i", strtotime("-1 min", time())) . ":00";
            }
            foreach ($change as $_item) {

                $orginfo = OilOrg::getByOrgcode($_item->orgcode);

                $updateDetail = $condition = $task = [];
                $condition['orgcode'] = $_item->orgcode;
                $condition['org_operator_id'] = $_item->before_operator_id;
                $condition['group_len'] = strlen($condition['orgcode']);
                $income = self::getSumCharge($condition);
                if ($income == -110) {
                    self::noticeForSign(['orgcode' => "机构编码：".$condition['orgcode'],'name'=>"机构名称：".$orginfo->org_name, "msg" => "描述：现金充值单存在付款公司为空的数据！！！"]);
                    continue;
                }
                $condition['end_time'] = $_item->change_time;
                $trade = self::getSumTrades($condition);
                $diff_fee = $income - $trade['money'];
                Log::error("换签差额,机构编码：".$condition['orgcode'], [['income' => $income, "trade" => $trade['money'], "diff" => $diff_fee]], "sign_mark_");
                if (bccomp($diff_fee, 0, 2) < 0) {
                    self::noticeForSign([
                        'orgcode' => "机构编码：".$condition['orgcode'],
                        'name'=>"机构名称：".$orginfo->org_name,
                        "msg" => "描述：交易数据大于充值数据不合规！！差额：".number_format(abs($diff_fee),2,".","")
                    ]);
                    //G7WALLET-6760
                    //continue;
                }
                

                $refund_id = $refund_time = "";
                $id_str = explode(",", $trade['trades_id']);
                if (count($id_str) > 0) {
                    $_tmp_str = explode("#", $id_str[0]);
                    if (count($_tmp_str) > 0) {
                        $refund_id = $_tmp_str[0];
                        $refund_time = $_tmp_str[1];
                    }
                }

                if ( bccomp($diff_fee, 0, 2) == 0 || bccomp(0,$_item->original_money,2) > 0 ) {
                    $updateDetail['id'] = $_item->sub_id;
                    $updateDetail['status'] = OilOrgChangeOperatorLog::SIGN_CHECK;
                    $updateDetail['updatetime'] = \helper::nowTime();

                    $updateDetail['trades_id'] = $refund_id;
                    $updateDetail['sign_end_time'] = $refund_time;
                    $updateDetail['original_money'] = $diff_fee;
                    $updateDetail['actual_money'] = $diff_fee;
                    $updateDetail['remark'] = $params['remark'] ? $params['remark'] : '';
                    //G7WALLET-6760
                    if(bccomp(0,$_item->original_money,2) > 0){
                        unset($updateDetail['original_money']);
                        unset($updateDetail['actual_money']);
                    }

                    $new['status'] = OilOrgChangeOperatorLog::SIGN_SYSTEM;
                    $new['orgroot'] = substr($_item->orgcode,0,6);
                    $new['orgcodeNeq'] = $_item->orgcode;
                    $changeData = OilOrgChangeOperatorDetails::getChangeDataByFilter($new);
                    $_status = OilOrgChangeOperatorLog::SIGN_CHECK;
                    if( count($changeData) > 0 ){
                        $_status = OilOrgChangeOperatorLog::SIGN_SYSTEM;
                    }
                    OilOrgChangeOperatorLog::edit(["id" => $_item->id, "status" => $_status, "updatetime" => \helper::nowTime()]);
                    OilOrgChangeOperatorDetails::edit($updateDetail);

                    self::noticeForSign(['orgcode' => "机构编码：".$condition['orgcode'],'name'=>"机构名称：".$orginfo->org_name, "msg" => "描述：系统标记完成，进入【人工核对中】"]);
                    continue;
                }
                if( strlen($condition['orgcode']) < 6 ){
                    self::noticeForSign(['orgcode' => "机构编码：".$condition['orgcode'],'name'=>"机构名称：".$orginfo->org_name, "msg" => "描述：机构编码有误"]);
                    continue;
                }

                OilOrgChangeOperatorDetails::edit(['id'=>$_item->sub_id,"original_money"=>$diff_fee,"updatetime"=>\helper::nowTime()]);
                //todo 开始标记
                $task['task_classify'] = 10;
                $task['left_money'] = $diff_fee;
                $task['start_time'] = $_item->change_time;
                $task['end_time'] = $end_time; //小于
                $task['org_code'] = $condition['orgcode'];
                $task['org_name'] = $orginfo && isset($orginfo->org_name) ? $orginfo->org_name : "";
                $task['org_operator_id'] = $condition['org_operator_id'];
                $task['up_operator_id'] = $_item->id;
                $task['down_operator_id'] = $_item->sub_id;
                $task['is_cron'] = $params['is_cron'] == 1 ? 1 : 2;
                $task['last_trade_id'] = $refund_id;
                $task['last_trade_time'] = $refund_time;

                if($params['is_cron'] == 1) {
                    sleep(20);
                }
                try{
                    Log::error("消费标记任务参数", [$task], "sign_mark_");
                    $_task = OperatorReceipt::updateOperatorDayTrades($task);
                }catch (\Exception $e){
                    Log::error("消费标记任务结果:" . json_encode($_task, true), [$task], "sign_mark_");
                }
            }
        } catch (\Exception $e) {
            Log::error("系统标记任务异常", [$e->getMessage()], "sign_mark_");
            throw new RuntimeException($e->getMessage(), $e->getCode());
        }

        return true;
    }

    /**
     * @param array $params
     * orgcode :核算层编码
     * org_operator_id：转出运营商
     * group_len:核算层机构长度
     * @return int|mixed
     */
    static public function getSumCharge($params = [])
    {
        //累计现金充值+累计返利充值
        //累计账单
        //累计动力宝
        //累计返利审核
        $fee = 0;
        Log::error("换签进账参数：", [$params], "sign_mark_");
        $chargeData = OilAccountMoneyCharge::getChargeData($params, true);
        $charge = isset($chargeData[$params['orgcode']]) ? $chargeData[$params['orgcode']] : [];
        if (count($charge) > 0) {
            Log::error("换签充值数据", [$charge], "sign_mark_");
            if (isset($charge[1])) {
                if (stripos("空", $charge[1]['company_id']) !== false) {
                    return -110;
                }
                $fee += $charge[1]['money'];
            }
            if (isset($charge[2])) {
                $fee += $charge[2]['money'];
            }
        }

        if ($params['org_operator_id'] == 2) {
            $fanliAudit = FanliAudit::getFanliMap($params['group_len'], $params['orgcode']);
            if (isset($fanliAudit[$params['orgcode']]) && isset($fanliAudit[$params['orgcode']]['fee'])) {
                Log::error("换签返利审核数据", [$fanliAudit[$params['orgcode']]], "sign_mark_");
                $fee += $fanliAudit[$params['orgcode']]['fee'];
            }

            $creditDlb = DongLiBaoRepay::getCreditMap($params['group_len'], $params['orgcode']);
            if (isset($creditDlb[$params['orgcode']]) && isset($creditDlb[$params['orgcode']]['fee'])) {
                Log::error("换签动力宝数据", [$creditDlb[$params['orgcode']]], "sign_mark_");
                $fee += $creditDlb[$params['orgcode']]['fee'];
            }
        }

        $creditData = OilCreditBill::getBillDataForSign($params);
        $credit = isset($creditData[$params['orgcode']]) ? $creditData[$params['orgcode']] : [];
        if (count($credit) > 0) {
            Log::error("换签授信账单数据", [$credit], "sign_mark_");
            $fee += $credit['money'];
        }
        return $fee;
    }

    /**
     * @param array $params
     * orgcode :核算层编码
     * org_operator_id：转出运营商
     * group_len:核算层机构长度
     * end_time :预约换签时间
     * @return int|mixed
     */
    static public function getSumTrades($params = [])
    {
        $result = ['money' => 0, "trades_id" => '', 'operator_id' => '', 'trade_num' => 0, 'trade_count' => 0];
        Log::error("换签交易参数：", [$params], "sign_mark_");
        $tradeData = OilCardViceTrades::getTradesData($params);
        $trade = isset($tradeData[$params['orgcode']]) ? $tradeData[$params['orgcode']] : [];
        if (count($trade) > 0) {
            Log::error("换签交易数据", [$trade], "sign_mark_");
            $result = $trade;
        }
        return $result;
    }

    /**
     * @param array $params
     * 获取可用充值
     * pay_company_id：付款公司
     * org_id：机构id
     * operator_id:运营商id
     * createtimeLe:时间小于等于
     */
    static public function getSumUseChange($params = [])
    {
        \helper::argumentCheck(['pay_company_id','org_id','operator_id'], $params);

        $change = self::getOrgChangeData( ['org_id'=>$params['org_id']] );
        if(count($change) <= 0){
            $params['operator_id'] = "";
        }

        $oneParams = [
            'org_id'         => $params['org_id'],
            'pay_company_id' => $params['pay_company_id'],
            'receipt_operator_id' => isset($params['operator_id']) && !empty($params['operator_id']) ? $params['operator_id'] : '',
            '_createtimeLe' => isset($params['createtimeLe']) ? $params['createtimeLe'] : "",
        ];
        $oneParams['is_direct_dl'] = $oneParams['receipt_operator_id'] == 2 ? 1 : 2;
        Log::error("换签可用充值参数：", [$oneParams], "sign_mark_");
        $amountInfo = OpenReceipt::getUseChargeByCompany($oneParams);
        Log::error("换签可用充值结果：", [$amountInfo], "sign_mark_");
        return $amountInfo['total_charge_add_repayed_subtraction_receipted_receiptFrozen'];
    }

    static public function noticeForSign($emailContent = [],$title='')
    {
        $isTest = API_ENV == 'dev' || API_ENV == 'test';
        //报警
        $title = $title ? $title : '下游客户换签';
        $title = ($isTest ? 'TEST-' : '') . $title;
        $content = implode("\n", $emailContent);

        $chat_id = "oc_e25ad23499b09a83c8c687396a7f1cb5";
        if( in_array(API_ENV,['pro','prod']) ){
            $chat_id = "oc_9ed813a22fb9c5193ae357f3854192ae";
        }

        $apiParams = [
            'title'    => $title,
            'chat_id'  => $chat_id,
            'msg_type' => 'card',
            'content'  => $content."\n",
            'at'       => []
        ];
        if (!$isTest) {
            /*$apiParams['at']['张博学'] = "<EMAIL>";
            $apiParams['at']['李松林'] = "<EMAIL>";
            $apiParams['at']['雷庆'] = "<EMAIL>";
            $apiParams['at']['李永华'] = "<EMAIL>";*/
        }
        // Log::error("换签通知参数 noticeForSign ：", [$apiParams], "sign_mark_");
        $res = (new FeiShuNotify())->Send($apiParams);
        //Log::error("换签通知结果 noticeForSign：", [$res], "sign_mark_");
    }

    static public function forceUpdateStatus($params = [])
    {
        $condition['status'] = OilOrgChangeOperatorLog::SIGN_SYSTEM;
        $condition['id'] = $params['detail_id'];
        $change = OilOrgChangeOperatorDetails::getChangeDataByFilter($condition);
        if(count($change) == 0 || !isset($change[0]->sub_id)){
            throw new RuntimeException('只有【系统标记中】的换签记录，可以提前人工核对', 208);
        }

        $sub_id = $change[0]->id;
        $updateDetail['id'] = $sub_id;
        $updateDetail['status'] = OilOrgChangeOperatorLog::SIGN_CHECK;
        $updateDetail['updatetime'] = \helper::nowTime();
        $updateDetail['sign_end_time'] = \helper::nowTime();
        $updateDetail['refund_money'] = 0;
        $updateDetail['remark'] = $params['remark'];

        $new['status'] = OilOrgChangeOperatorLog::SIGN_SYSTEM;
        $new['orgroot'] = substr($change[0],0,6);
        $new['orgcodeNeq'] = $change[0]->orgcode;
        $changeData = OilOrgChangeOperatorDetails::getChangeDataByFilter($new);
        $_status = OilOrgChangeOperatorLog::SIGN_CHECK;
        if( count($changeData) > 0 ){
            $_status = OilOrgChangeOperatorLog::SIGN_SYSTEM;
        }
        OilOrgChangeOperatorLog::edit(["id" => $sub_id, "status" => $_status, "updatetime" => \helper::nowTime()]);
        OilOrgChangeOperatorDetails::edit($updateDetail);

        self::noticeForSign(['orgcode' => "机构编码：".$change[0]->orgcode,'name'=>"机构名称：".$change[0]->org_name, "msg" => "描述：系统标记完成，进入【人工核对中】"]);
        return true;
    }
}