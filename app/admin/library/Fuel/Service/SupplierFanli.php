<?php
/**
* 站端相关 StationBalance.php
* $Author: 刘培俊 (liu<PERSON><EMAIL>) $
* $Date: 2020/8/6 14:44 $
* CreateBy Phpstorm
*/

namespace Fuel\Service;

use Framework\Log;
use Fuel\Defines\CooperationType;
use Fuel\Defines\ErrorCode;
use Fuel\Defines\PayCenter;
use Fuel\Defines\RebateFormula;
use Fuel\Defines\SupplierAccountConf;
use Fuel\Defines\SupplierFanliAccountConf;
use Jobs\SupplierAccountIntegralJob;
use Models\OilCardMain;
use Models\OilFossPaymentRecords;
use Models\OilStationSupplier;
use Models\OilStationArea;
use Models\OilSupplierAccount;
use Models\OilSupplierFanli;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilSupplierFanliAccount;
use Models\OilSupplierFanliRechargeExt;
use Models\OilSupplierRelation;


class SupplierFanli
{
    public static function getList($params)
    {
        $data = OilSupplierFanli::getList($params);

        $export = empty($params['_export']) ? false : true;

        self::formatData($data, $export);

        return $data;
    }

    protected static function formatData(&$data, $export)
    {
        $ids = [];
        foreach ($data as $row) {
            if ($row->rebate_form != RebateFormula::CA_OBJ_RECHARGE)
                continue;

            $ids[] = $row->id;
        }
        $recharge = [];

        if (!empty($ids)) {
            // 获取充值单号
            $res = OilSupplierFanliRechargeExt::getList(['fanli_id' => $ids, '_export' => 1]);
            foreach ($res as $row) {
                if (! isset($recharge[$row->fanli_id])) {
                    $recharge[$row->fanli_id] = [];
                }
                $recharge[$row->fanli_id][] = $row->payment_no;
            }
        }

        foreach ($data as $row) {
            $row->recharge_no = '';
            $row->recharge_no_txt = '--';

            switch ($row->rebate_form) {
                case RebateFormula::CA_OBJ_RECHARGE:
                    $row->rebate_form_txt   = '充值返利';

                    if (isset($recharge[$row->id])) {
                        $row->recharge_no = implode(",", $recharge[$row->id]);
                        if (! $export) {
                            $row->recharge_no_txt = implode("<br />", $recharge[$row->id]);
                        } else {
                            $row->recharge_no_txt = implode(",", $recharge[$row->id]);
                        }
                    }

                    break;
                case RebateFormula::CA_OBJ_TRADE:
                    $row->rebate_form_txt = '消费返利';
                    break;
                case RebateFormula::CA_OBJ_STRAIGHT:
                    $row->rebate_form_txt = '直降返利';
                    break;
            }

            $row->trade_starttime_txt = empty($row->trade_starttime) ? '--' : $row->trade_starttime;
            $row->trade_endtime_txt = empty($row->trade_endtime) ? '--' : $row->trade_endtime;
        }
    }

    protected static function getRechargeInfo($nos, $supplierId)
    {
        if (empty($nos))
            return [];

        $list = explode(',', $nos);

        $tmp = [];
        foreach ($list as $v) {
            $tmp[] = trim($v);
        }

        $list = array_count_values($tmp);
        foreach ($list as $k => $c) {
            if ($c > 1) {
                throw new \RuntimeException("充值单{$k}存在重复",2);
            }
        }

        if (count($list) > 50) {
            throw new \RuntimeException("充值单号最多输入50个",2);
        }

        $res = [];
        $payment = OilFossPaymentRecords::getList(['no' => implode(',', $tmp), '_export' => 1]);
        foreach ($payment as $record) {
            if ($record->is_del == 1) {
                throw new \RuntimeException("充值单{$record->no}已删除",2);
            }
            if ($record->status != PayCenter::STATUS_PAY_SUCCESS) {
                throw new \RuntimeException("充值单{$record->no}未支付成功",2);
            }
            if ($record->supplier_id != $supplierId) {
                throw new \RuntimeException("充值单{$record->no}充值供应商与当前供应商不匹配",2);
            }
            $res[$record->no] = [
                'id' => $record->id,
                'no' => $record->no
            ];
        }

        $diff = array_diff($tmp, array_keys($res));
        if (! empty($diff)) {
            throw new \RuntimeException("充值单". array_pop($diff) ."不存在",2);
        }

        return $res;
    }

    public static function inertSupplierFanli(array $params)
    {
        $editId = "";
        if(isset($params['id']) && !empty($params['id'])){
            $info = OilSupplierFanli::getById(['id'=>$params['id'],"is_del"=>2]);
            if(!$info){
                throw new \RuntimeException("数据不存在",2);
            }
            if($info->status == 20){
                throw new \RuntimeException("请勿编辑审核通过的工单",2);
            }
            $editId = $info->id;
        }
        global $app;
        $supplierInfo = OilStationSupplier::getById(['id'=>$params['supplier_id']]);
        if(!$supplierInfo){
            throw new \RuntimeException("油站供应商不合法",2);
        }
        /*if( !in_array($supplierInfo->cooperation_type,[CooperationType::COOPERATION_TYPE_ZD,CooperationType::COOPERATION_TYPE_ZK]) ){
            throw new \RuntimeException("仅支持合作类型是站点和主卡",2);
        }*/
        //$insert['supplier_code'] = "";
        $insert['no'] = OilSupplierFanli::createNo();
        $insert['supplier_name'] = $supplierInfo->supplier_name;
        $insert['supplier_id'] = $supplierInfo->id;
        $insert['cooperation_type'] = $supplierInfo->cooperation_type;
        $insert['settle_obj'] = $supplierInfo->settle_obj;
        $insert['real_arrival_time'] = $params['real_arrival_time'];
        $insert['operator_id'] = $params['operator_id'];
        $insert['collect_company_id'] = $params['collect_company_id'];
        $mainInfo = null;
        if ($supplierInfo->cooperation_type == CooperationType::COOPERATION_TYPE_ZK) {
            $mainInfo = OilCardMain::getById(['id'=>$params['area_id']]);
            $insert['settle_object_id'] = $mainInfo->id;
            $insert['settle_object_name'] = $mainInfo->main_no;
        }
        if ($supplierInfo->cooperation_type == CooperationType::COOPERATION_TYPE_PT) {
            $insert['settle_object_id'] = $supplierInfo->id;
            $insert['settle_object_name'] = $supplierInfo->supplier_name;
        }
        if ($supplierInfo->cooperation_type == CooperationType::COOPERATION_TYPE_ZD) {
            if ($supplierInfo->settle_obj == SupplierAccountConf::SETTLE_OBJ_SUPPLIER) {
                $insert['settle_object_id'] = $supplierInfo->id;
                $insert['settle_object_name'] = $supplierInfo->supplier_name;
            }
            if ($supplierInfo->settle_obj == SupplierAccountConf::SETTLE_OBJ_AREA) {
                $areaInfo = OilStationArea::getById(['id' => $params['area_id']]);
                $insert['settle_object_id'] = $params['area_id'];
                $insert['settle_object_name'] = $areaInfo->name;
            }
        }
        if($supplierInfo->settle_obj == 20){
            $_param = [];
            $_param['supplier_id'] = $supplierInfo->id;
            if(empty($params['area_id'])){
                throw new \RuntimeException("请选择服务区/主卡",2);
            }
            if($supplierInfo->cooperation_type == CooperationType::COOPERATION_TYPE_ZK){
                if ( !$mainInfo ) {
                    throw new \RuntimeException("主卡不合法", 2);
                }
                $insert['area_id'] = $mainInfo->id;
                $insert['area_code'] = $mainInfo->main_no;
                $insert['area_name'] = $mainInfo->main_no;
                $_param['cooperation_type'] = 30;
                $_param['card_main_id'] = $mainInfo->id;
            } else {
                $areaInfo = OilStationArea::getById(['id' => $params['area_id'], 'is_del' => 0]);
                if (!$areaInfo) {
                    throw new \RuntimeException("服务区不合法", 2);
                }
                $insert['area_id'] = $areaInfo->id;
                $insert['area_code'] = $areaInfo->code;
                $insert['area_name'] = $areaInfo->name;
                $_param['cooperation_type'] = 20;
                $_param['is_area'] = 1;
                $_param['area_id'] = $areaInfo->id;
            }

            $_param['count'] = 1;
            $hasCount = OilSupplierRelation::getList($_param);
            if( $hasCount <= 0 ){
                throw new \RuntimeException("供应商与绑定服务区/主卡不一致", 2);
            }
        }

        $recharge = [];
        if ($params['rebate_form'] == RebateFormula::CA_OBJ_TRADE) {
            if(strtotime($params['trade_starttime']) > strtotime($params['trade_endtime'])){
                throw new \RuntimeException("消费时间的起始值不能大于结束值", 2);
            }
        } elseif ($params['rebate_form'] == RebateFormula::CA_OBJ_RECHARGE && !empty($params['recharge_no'])) {
            // 校验充值单号
            $recharge = self::getRechargeInfo($params['recharge_no'], $params['supplier_id']);
        }

        $insert['fanli_fee'] = $params['fanli_fee'];
        $insert['service_fee'] = $params['service_fee'];
        $insert['rebate_form'] = $params['rebate_form'];

        if ($params['rebate_form'] == RebateFormula::CA_OBJ_TRADE) {
            $insert['trade_starttime'] = $params['trade_starttime'];
            $insert['trade_endtime'] = $params['trade_endtime'];
        } elseif ($editId && $params['rebate_form'] == RebateFormula::CA_OBJ_RECHARGE) {
            $insert['trade_starttime'] = null;
            $insert['trade_endtime'] = null;
        }

        $insert['fanli_type'] = $params['fanli_type'];
        $insert['arrive_type'] = $params['arrive_type'];
        $insert['last_operator'] = $app->myAdmin->true_name;
        $insert['updatetime'] = \helper::nowTime();
        $insert['creator'] = $app->myAdmin->true_name;
        $insert['createtime'] = \helper::nowTime();
        $insert['remark'] = $params['remark'];

        try {
            Capsule::connection()->beginTransaction();

            if(empty($editId)){
                $res = OilSupplierFanli::add($insert);
                $id = $res->id;
            }else{
                unset($insert['creator']);
                unset($insert['no']);
                unset($insert['createtime']);
                $insert['id'] = $editId;
                $insert['status'] = 10;
                $res = OilSupplierFanli::edit($insert);
                $id = $editId;

                OilSupplierFanliRechargeExt::removeByFanLiIds([$id]);
            }

            if (! empty($recharge)) {
                $tmp = [];
                foreach ($recharge as $row) {
                    $tmp[] = [
                        'fanli_id' => $id,
                        'payment_id' => $row['id'],
                        'payment_no' => $row['no']
                    ];
                }
                OilSupplierFanliRechargeExt::insert($tmp);
            }

            Capsule::connection()->commit();
            return $res;
        } catch (\Exception $e) {
            Capsule::connection()->rollBack();
            Log::Error('supplier_fanli_add', ['err_msg' => $e->getMessage().$e->getTraceAsString()]);
            throw new \RuntimeException ('系统异常，请稍后重试', ErrorCode::ERROR_SYSTEM);
        }
    }

    public static function batchUpdate($params = [])
    {
        global $app;
        $updateIds = explode(",",$params['ids']);
        $list = OilSupplierFanli::getList(["is_del"=>2,"_export"=>1,'idIn' => $updateIds]);
        if(count($list) == 0){
            throw new \RuntimeException("请选择操作数据",2);
        }

        Capsule::connection()->beginTransaction();
        try {
            foreach ($list as $_item) {
                $updateItem['updatetime'] = \helper::nowTime();
                $updateItem['last_operator'] = $app->myAdmin->true_name;
                $updateItem['id'] = $_item->id;
                if (!empty($params['auditRemark'])) {
                    if (empty($_item['audit_remark'])) {
                        $updateItem['audit_remark'] = $params['auditRemark'];
                    } else {
                        $updateItem['audit_remark'] = $_item['audit_remark'] . "," . $params['auditRemark'];
                    }
                }
                if ($params['flag'] == 1) {//删除
                    if ($_item->status == 20) {
                        throw new \RuntimeException("请勿删除审核通过的工单", 2);
                    }
                    $updateItem['is_del'] = 1;
                    OilSupplierFanli::edit($updateItem);
                } elseif ($params['flag'] == 2) {//审核
                    if($_item->status != 10){
                        throw new \RuntimeException("请选择待审核的工单", 2);
                    }
                    if($_item->status == 20){
                        throw new \RuntimeException("请勿重复审核", 2);
                    }
                    $updateItem['audit_time'] = \helper::nowTime();
                    $updateItem['audit_operator'] = $app->myAdmin->true_name;
                    $updateItem['status'] = 20;
                    OilSupplierFanli::edit($updateItem);
                    if(in_array($_item->fanli_type,[10,20]) && $_item->arrive_type == 10) {
                        if($_item->fanli_type == 10 && $_item->arrive_type == 10){
                            //todo 临时去掉
                            (new SupplierAccount())->handleSupplierAccountBusiness(['res_type'=>20,'res_id'=>$_item->id],false);
                        }
                        //充值到账户
                        //上游返利账户余额增加
                        (new OilSupplierFanliAccountService())->handleSupplierFanliAccountBusiness([
                            'amount'=>$_item['fanli_fee'],
                            'supplier_id'=>$_item['supplier_id'],
                            'res_type'=>SupplierFanliAccountConf::STATEMENT_RES_TYPE_FANLI_INPUT,
                            'business_no'=>$_item['no'],
                            'log_list'=>[[
                                'amount'=>$_item['fanli_fee'],
                                'res_id'=>$_item['id'],
                            ]],
                        ],false);
                    }
                } elseif ($params['flag'] == 3) {//驳回
                    if($_item->status != 10){
                        throw new \RuntimeException("请选择待审核的工单", 2);
                    }
                    if ($_item->status == 20) {
                        throw new \RuntimeException("请勿驳回通过的工单", 2);
                    }
                    if($_item->status == 30){
                        throw new \RuntimeException("请勿重复驳回", 2);
                    }
                    $updateItem['audit_time'] = \helper::nowTime();
                    $updateItem['audit_operator'] = $app->myAdmin->true_name;
                    $updateItem['status'] = 30;
                    OilSupplierFanli::edit($updateItem);
                }
            }
            /*if (count($updateIds) > 0) {
                OilSupplierFanli::batchUpdate($updateIds, $updateItem);
            }*/
            Capsule::connection()->commit();
        }catch (\Exception $e){
            Log::error("返利录入：".$e->getMessage(),[$params],"supplierFanli_");
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        if ($params['flag'] == 2) {
            (new SupplierAccountIntegralJob($params))
                ->setTaskName('上游返利录入，异步修改积分账户信息')
                ->setUserInfo($app->myAdmin)
                ->onQueue('addSupplierAccountStatementJob')
                ->dispatch();
        }

        return $updateIds;
    }
    public static function batchImport($result)
    {
        global $app;
        try {
            $dataInsert = $ext = [];
            foreach ($result[0] as $line => $v) {
                \helper::argumentCheck(["supplier_id","supplier_name","settle",'fanli_type',"arrive_type",'fanli_fee',
                    "service_fee", "rebate_form"], $v);

                $item = $recharge = [];
                $supperInfo = OilStationSupplier::getById(['id'=>$v['supplier_id']]);
                if(!$supperInfo){
                    throw new \RuntimeException("第：".$line."行，油站供应商不存在", 2);
                }
                if($supperInfo->supplier_name != $v['supplier_name']){
                    throw new \RuntimeException("第：".$line."行，油站供应商id与油站供应商名称不一致", 2);
                }

                /*if(!in_array($supperInfo->cooperation_type,[CooperationType::COOPERATION_TYPE_ZK,CooperationType::COOPERATION_TYPE_ZD])){
                    throw new \RuntimeException("仅支持合作类型是站点和主卡",2);
                }*/

                if(!in_array($v['settle'],['1级-油站供应商','2级-服务区','2级-主卡'])){
                    throw new \RuntimeException("第：".$line."行，核算主体不合法", 2);
                }
                $item['settle_obj'] = ($v['settle'] == "2级-服务区" || $v['settle'] == "2级-主卡") ? 20 : 10;
                if($supperInfo->settle_obj != $item['settle_obj']){
                    throw new \RuntimeException("第：".$line."行，核算主体不一致", 2);
                }
                //$item['supplier_code'] = "";
                $item['no'] = OilSupplierFanli::createNo();
                $item['supplier_name'] = $supperInfo->supplier_name;
                $item['supplier_id'] = $supperInfo->id;
                $item['cooperation_type'] = $supperInfo->cooperation_type;
                $item['settle_obj'] = $supperInfo->settle_obj;
                $item['area_id'] = 0;
                $item['area_code'] = "";
                $item['area_name'] = "";
                $item['trade_starttime'] = null;
                $item['trade_endtime'] = null;

                if($supperInfo->settle_obj == 20){
                    $_param = [];
                    $_param['supplier_id'] = $supperInfo->id;
                    if(empty($v['area_code']) || empty($v['area_name'])){
                        throw new \RuntimeException("第：".$line."行，请填写服务区/主卡", 2);
                    }
                    if($supperInfo->cooperation_type == CooperationType::COOPERATION_TYPE_ZK){
                        $mainInfo = OilCardMain::getByMainNo($v['area_code']);
                        if ( !$mainInfo ) {
                            throw new \RuntimeException("主卡不合法", 2);
                        }
                        $item['area_id'] = $mainInfo->id;
                        $item['area_code'] = $mainInfo->main_no;
                        $item['area_name'] = $mainInfo->main_no;
                        $_param['cooperation_type'] = 30;
                        $_param['card_main_id'] = $mainInfo->id;
                    }else {
                        $areaInfo = OilStationArea::getInfoByFilter(['code' => $v['area_code'], "is_del" => 0]);
                        if (!$areaInfo) {
                            throw new \RuntimeException("第：" . $line . "行，服务区不存在", 2);
                        }
                        if ($areaInfo->name != $v['area_name']) {
                            throw new \RuntimeException("第：" . $line . "行，服务区编码与服务区名称不一致", 2);
                        }
                        $item['area_id'] = $areaInfo->id;
                        $item['area_code'] = $areaInfo->code;
                        $item['area_name'] = $areaInfo->name;
                        $_param['cooperation_type'] = 20;
                        $_param['is_area'] = 1;
                        $_param['area_id'] = $areaInfo->id;
                    }

                    $_param['count'] = 1;
                    $hasCount = OilSupplierRelation::getList($_param);
                    if( $hasCount <= 0 ){
                        throw new \RuntimeException("第：".$line."行，供应商与绑定服务区/主卡不一致", 2);
                    }
                }
                if (! in_array($v['rebate_form'], ['充值返利', '消费返利'])) {
                    throw new \RuntimeException("第：".$line."行，返利类型不合法", 2);
                }
                $item['rebate_form'] = $v['rebate_form'] == '消费返利' ? RebateFormula::CA_OBJ_TRADE : RebateFormula::CA_OBJ_RECHARGE;

                if(!in_array($v['fanli_type'],['现金','积分'])){
                    throw new \RuntimeException("第：".$line."行，返利形式不合法", 2);
                }
                if(!in_array($v['arrive_type'],['充到账户余额','不充到账户余额'])){
                    throw new \RuntimeException("第：".$line."行，返利到账类型不合法", 2);
                }

                if(trim($v['fanli_type']) == '积分' && trim($v['arrive_type']) == '充到账户余额'){
                    throw new \RuntimeException("第：".$line."行，如果返利形式是积分，则返利到账类型不能是“充到账户余额”", 2);
                }

                if ($item['rebate_form'] == RebateFormula::CA_OBJ_TRADE) {
                    \helper::argumentCheck(["trade_start","trade_end"], $v);

                    if(!preg_match("/^\d{4}[\-](0?[1-9]|1[012])[\-](0?[1-9]|[12][0-9]|3[01])(\s+(0?[0-9]|1[0-9]|2[0-3])\:(0?[0-9]|[1-5][0-9])\:(0?[0-9]|[1-5][0-9]))?$/",$v['trade_start'])){
                        throw new \RuntimeException("第：".$line."行，消费开始时间不合法", 2);
                    }

                    if(!preg_match("/^\d{4}[\-](0?[1-9]|1[012])[\-](0?[1-9]|[12][0-9]|3[01])(\s+(0?[0-9]|1[0-9]|2[0-3])\:(0?[0-9]|[1-5][0-9])\:(0?[0-9]|[1-5][0-9]))?$/",$v['trade_end'])){
                        throw new \RuntimeException("第：".$line."行，消费结束时间不合法", 2);
                    }

                    if(strtotime($v['trade_start']) > strtotime($v['trade_end'])){
                        throw new \RuntimeException("第：".$line."行，消费时间不合法", 2);
                    }

                    $item['trade_starttime'] = $v['trade_start'];
                    $item['trade_endtime'] = $v['trade_end'];
                } else {
                    if (! empty($v['recharge_no'])) {
                        try {
                            $recharge = self::getRechargeInfo($v['recharge_no'], $item['supplier_id']);
                        } catch (\RuntimeException $e) {
                            throw new \RuntimeException("第：".$line."行，" . $e->getMessage(), $e->getCode());
                        }
                    }
                }

                $item['fanli_type'] = $v['fanli_type'] == '积分' ? 20 : 10;
                $item['arrive_type'] = $v['arrive_type'] == '不充到账户余额' ? 20 : 10;
                $item['fanli_fee'] = $v['fanli_fee'];
                $item['service_fee'] = $v['service_fee'];
                $item['last_operator'] = $app->myAdmin->true_name;
                $item['updatetime'] = \helper::nowTime();
                $item['creator'] = $app->myAdmin->true_name;
                $item['createtime'] = \helper::nowTime();
                $item['remark'] = $v['remark'];

                if ($item['rebate_form'] == RebateFormula::CA_OBJ_RECHARGE && !empty($recharge)) {
                    $ext[] = ['item' => $item, 'ext' => $recharge];
                } else {
                    $dataInsert[] = $item;
                }
            }

            try {
                Capsule::connection()->beginTransaction();

                if(count($dataInsert) > 0){
                    OilSupplierFanli::batchAdd($dataInsert);
                }

                if (! empty($ext)) {
                    foreach ($ext as $v) {
                        $res = OilSupplierFanli::add($v['item']);

                        $tmp = [];
                        foreach ($v['ext'] as $row) {
                            $tmp[] = [
                                'fanli_id' => $res->id,
                                'payment_id' => $row['id'],
                                'payment_no' => $row['no']
                            ];
                        }
                        OilSupplierFanliRechargeExt::insert($tmp);
                    }
                }

                Capsule::connection()->commit();
            } catch (\Exception $e) {
                Capsule::connection()->rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            \Framework\Log::error('导入失败--' . $e->getMessage(), [], 'supplierStationBatchImport_');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    //校验及获取账户流水数据
    public static function validateSupplierAccountStatement($res_id = 0)
    {
        if(empty($res_id)){
            throw new \RuntimeException("资源id不能为空",2);
        }
        $info = OilSupplierFanli::getOneByFilter(['id'=>$res_id,"status"=>20,"is_del"=>2]);
        if(!$info){
            throw new \RuntimeException("资源数据不存在或状态未审核",3);
        }
        if(empty($info->no)){
            throw new \RuntimeException("返利单号不能为空",3);
        }
        if($info->arrive_type == 10 && $info->fanli_type == 10) {

            $summary = $info->supplier_name;
            if ($info->settle_obj == 20) {
                $res_type = 2;
                if($info->cooperation_type == CooperationType::COOPERATION_TYPE_ZK){
                    $res_type = 5;
                }
                //针对非一级核算主体，必须加上供应商ID
                $accountInfo = OilSupplierAccount::getInfoByFilter(['supplier_id'=>$info->supplier_id,'res_id' => $info->area_id, "res_type" => $res_type, "status" => 1]);
                $summary = $info->area_name;
            } else {
                $accountInfo = OilSupplierAccount::getInfoByFilter(['res_id' => $info->supplier_id, "res_type" => 1, "status" => 1]);
            }
            if (!$accountInfo) {
                throw new \RuntimeException("账户不存在或已停用", 5);
            }
            return [
                "account_id" => $accountInfo->id,
                "business_no" => $info->no,
                "money" => bcadd($info->fanli_fee, $info->service_fee, 2),
//                 "summary" => "返利录入-" . $summary,
                "summary" => $summary.'获得返利',
            ];
        }else{
            throw new \RuntimeException("无需生成流水:".$res_id,4);
        }
    }

    private function getApp()
    {
        global $app;
        if(!$app->myAdmin || !$app->myAdmin->id){
            $app->myAdmin = new \stdClass();
            $app->myAdmin->id = 8888;
            $app->myAdmin->true_name = '系统自动';
        }

        return $app;
    }

}