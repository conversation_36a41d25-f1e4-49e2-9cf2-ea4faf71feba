<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-4-13
 * Time: 下午12:29
 */

namespace Fuel\Service;

use Framework\Log;
use Fuel\Defines\AccountMoneyTransferStatus;
use Fuel\Defines\OrgStatus;
use Fuel\Request\GasHjyClient;
use GosSDK\Defines\Methods\AccountMoneyTransfer;
use Illuminate\Database\Capsule\Manager as Capsule;
use Kafka\Exception;
use Models\OilAccountMoney;
use Models\OilAccountMoneyFanliRecords;
use Models\OilAccountMoneyTransfer;
use Models\OilOrg;
use Models\OilPayCompany;

class AccountTransfer
{

    /**
     * @title   转账单审核校验
     * @desc
     * @version
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     */
    static public function auditByValidate($transferInfo)
    {
        if ($transferInfo->status == 1) {
            throw new \RuntimeException('该单号已审核', 2);
        }

        //检查机构是否合法,检查资金账户是否存在，不存在则创建资金账户
        $checkAccount = \Models\OilAccountMoney::checkAccount($transferInfo);
        if (!$checkAccount) {
            throw new \RuntimeException('机构错误', 2);
        }

        //可用余额
        $used_money = FrozenMoney::accountBalance($transferInfo->org_id);
        if ( round( $used_money,2) < 0 ) {
            throw new \RuntimeException('现金账户余额不足，或存在冻结金额导致现金账户可用余额不足', 2);
        }
    }

    /**
     * @title   转账单销审校验
     * @desc
     * @version
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     */
    static public function unAuditByValidate($transferInfo)
    {
        if ($transferInfo->status == 0) {
            throw new \RuntimeException('该单号已销审或未审核', 2);
        }
        //检查机构是否合法,检查资金账户是否存在，不存在则创建资金账户
        $checkAccount = \Models\OilAccountMoney::checkAccount($transferInfo);
        if (!$checkAccount) {
            throw new \RuntimeException('机构错误', 2);
        }
    }

    static public function transferMoney($transferInfo)
    {
        global $app;
        $userInfo = $app->myAdmin;
        //账户Service
        $accountMoneyService = (new \Fuel\Service\AccountMoney())->setOrg($transferInfo->org_id);
        //现金账户信息
        $from_account_info    = $accountMoneyService->getAccountInfo();

        $from_after_money = $from_account_info->money - $transferInfo->money;
        if($from_after_money < 0){
            throw new \RuntimeException('账户余额扣减后不能小于0', 2);
        }

        $from_record = [
            'org_id'      => $from_account_info->org_id,
            'money_id'    => $from_account_info->id,
            'money'       => -$transferInfo->money,
            'after_money' => $from_account_info->money - $transferInfo->money,
            'no'          => $transferInfo->no,
            'sn'          => $transferInfo->sn,
            'no_type'     => 'ZZ',
            'trade_type'  => -1,
            'remark'      => $transferInfo->from_orgname . '转入 ' . $transferInfo->into_orgname . $transferInfo->money . '元',
            'remark_work' => $transferInfo->remark_work,
            'createtime'  => \helper::nowTime(),
            'operator_id' => $userInfo->id,
            'operator_name' => $userInfo->true_name,
        ];

        //转出方
        $accountMoneyService = $accountMoneyService
            ->setMoneyRecord($from_record)
            ->setMoney($from_after_money)
            ->setTotalTransferOut($from_account_info->total_transfer_out + $transferInfo->money);

        //判断返利金额
        if($transferInfo->use_fanli > 0){
            //G7WALLET-6056
            $end_fanli_total = $from_account_info->cash_fanli_remain - $transferInfo->use_fanli;
            if($end_fanli_total < 0){
                throw new \RuntimeException('返利余额不能小于0', 2);
            }

            /*$from_record['money'] = $transferInfo->use_fanli;
            $from_record['after_money'] = $end_fanli_total;
            $from_record['no_type'] = 'UF';
            $from_record['remark'] = '使用返利';

            $accountMoneyService = $accountMoneyService->setFanliMoneyRecord($from_record)->setCashFanLiRemain($end_fanli_total);*/

            OilAccountMoneyFanliRecords::updateFanliById($from_account_info->id, $transferInfo->use_fanli*-1, 2, [
                "org_id" => $from_account_info->org_id,
                "after_money" => $end_fanli_total,
                "total_fee" => $transferInfo->money,
                "org_name" => $transferInfo->from_orgname,
                "no" => $transferInfo->no,
                "sn" => $transferInfo->no,
                "rate" => $transferInfo->use_fanli/$transferInfo->money,
                "account" => "cash_fanli_remain",
            ], true, false);
        }

        $accountMoneyService->change();

        //接收方
        $accountMoneyService = (new \Fuel\Service\AccountMoney())->setOrg($transferInfo->into_org_id);
        $into_account_info = $accountMoneyService->getAccountInfo();

        $into_after_money = $into_account_info->money + $transferInfo->money;
        if($into_after_money < 0){
            throw new \RuntimeException('账户余额扣减后不能小于0', 2);
        }
        $into_record = [
            'org_id'      => $into_account_info->org_id,
            'money_id'    => $into_account_info->id,
            'money'       => $transferInfo->money,
            'after_money' => $into_account_info->money + $transferInfo->money,
            'no'          => $transferInfo->no,
            'sn'          => $transferInfo->sn,
            'no_type'     => 'ZZ',
            'trade_type'  => 1,
            'remark'      => $transferInfo->from_orgname . '转入 ' . $transferInfo->into_orgname . $transferInfo->money . '元',
            'remark_work' => $transferInfo->remark_work,
            'createtime'  => \helper::nowTime(),
            'operator_id' => $userInfo->id,
            'operator_name' => $userInfo->true_name,
        ];

        $accountMoneyService->setMoneyRecord($into_record)
            ->setMoney($into_account_info->money + $transferInfo->money)
            ->setTotalTransferIn($into_account_info->total_transfer_in + $transferInfo->money);

        //判断返利金额
        if($transferInfo->use_fanli > 0){

            //G7WALLET-6056
            $end_fanli_total = $into_account_info['cash_fanli_remain'] + $transferInfo->use_fanli;

            if($end_fanli_total < 0){
                throw new \RuntimeException('返利余额不能小于0', 2);
            }

            /*$into_record['money'] = $transferInfo->use_fanli;
            $into_record['after_money'] = $end_fanli_total;
            $into_record['no_type'] = 'UF';
            $into_record['remark'] = '使用返利来的';

            $accountMoneyService = $accountMoneyService->setFanliMoneyRecord($into_record)->setCashFanLiRemain($end_fanli_total);*/

            OilAccountMoneyFanliRecords::updateFanliById($into_account_info->id, $transferInfo->use_fanli, 2, [
                "org_id" => $into_account_info->org_id,
                "after_money" => $end_fanli_total,
                "total_fee" => $transferInfo->money,
                "org_name" => $transferInfo->from_orgname,
                "no" => $transferInfo->no,
                "sn" => $transferInfo->no,
                "rate" => $transferInfo->use_fanli/$transferInfo->money,
                "account" => "cash_fanli_remain",
            ], true, false);
        }

        $accountMoneyService->change();

        return ['use_fanli'=>$transferInfo->use_fanli,'from_record'=>$from_record,'into_record'=>$into_record];
    }

    static public function unAuditTransferMoney($transferInfo)
    {
        global $app;
        $userInfo = $app->myAdmin;
        //账户Service
        //接收方
        $accountMoneyService = (new \Fuel\Service\AccountMoney())->setOrg($transferInfo->into_org_id);
        $into_account_info = $accountMoneyService->getAccountInfo();

        if ($into_account_info->money < $transferInfo->money) {
            throw new \RuntimeException('账户资金余额不足', 2);
        }

        //当intoOrg的返利余额不够fromOrg当时分配的转账的返利use_fanli提示错误
        if($transferInfo->use_fanli > 0 && $into_account_info->cash_fanli_remain < $transferInfo->use_fanli){
            throw new \RuntimeException('账户余额不足或账户中的返利金额不足，无法销审', 2);
        }

        $into_after_money = $into_account_info->money - $transferInfo->money;
        if($into_after_money < 0){
            throw new \RuntimeException('账户余额扣减后不能小于0', 2);
        }

        $into_record = [
            'org_id'      => $into_account_info->org_id,
            'money_id'    => $into_account_info->id,
            'money'       => -$transferInfo->money,
            'after_money' => $into_account_info->money - $transferInfo->money,
            'no'          => $transferInfo->no,
            'sn'          => $transferInfo->sn,
            'no_type'     => 'ZZ',
            'trade_type'  => 1,
            'remark'      => $transferInfo->from_orgname . '转入 ' . $transferInfo->into_orgname . $transferInfo->money . '元,已销审',
            'remark_work' => $transferInfo->remark_work,
            'createtime'  => \helper::nowTime(),
            'operator_id' => $userInfo->id,
            'operator_name' => $userInfo->true_name,
        ];

        //判断返利金额
        //$end_fanli_total = $transferInfo->money > $into_account_info->cash_fanli_remain ? 0 : $into_account_info->cash_fanli_remain - $transferInfo->money;
        //$use_fanli = $transferInfo->money > $into_account_info->cash_fanli_remain ? $into_account_info->cash_fanli_remain : $transferInfo->money;

        $accountMoneyService->setMoneyRecord($into_record)
            ->setMoney($into_account_info->money - $transferInfo->money)
            ->setTotalTransferIn($into_account_info->total_transfer_in - $transferInfo->money);

        if($transferInfo->use_fanli > 0){
            $end_fanli_total = $into_account_info->cash_fanli_remain - $transferInfo->use_fanli;
            if($end_fanli_total < 0){
                throw new \RuntimeException('账户余额扣减后不能小于0', 2);
            }

            $into_record['money'] =  $transferInfo->use_fanli;
            $into_record['after_money'] = $end_fanli_total;
            $into_record['no_type'] = 'UF';
            $into_record['remark'] = '使用返利(销审)';

            $accountMoneyService->setFanliMoneyRecord($into_record)->setCashFanLiRemain($end_fanli_total);
        }

        $accountMoneyService->change();

        //转出机构
        $accountMoneyService = (new \Fuel\Service\AccountMoney())->setOrg($transferInfo->org_id);
        //现金账户信息
        $from_account_info    = $accountMoneyService->getAccountInfo();

        $from_after_money = $from_account_info->money + $transferInfo->money;
        if($from_after_money < 0){
            throw new \RuntimeException('账户余额扣减后不能小于0', 2);
        }

        $from_record = [
            'org_id'      => $from_account_info->org_id,
            'money_id'    => $from_account_info->id,
            'money'       => $transferInfo->money,
            'after_money' => $from_account_info->money + $transferInfo->money,
            'no'          => $transferInfo->no,
            'sn'          => $transferInfo->sn,
            'no_type'     => 'ZZ',
            'trade_type'  => -1,
            'remark'      => $transferInfo->from_orgname . '转入 ' . $transferInfo->into_orgname . $transferInfo->money . '元,已销审',
            'remark_work' => $transferInfo->remark_work,
            'createtime'  => \helper::nowTime(),
            'operator_id' => $userInfo->id,
            'operator_name' => $userInfo->true_name,
        ];

        //转出方
        $accountMoneyService->setMoneyRecord($from_record)
            ->setMoney($from_account_info->money + $transferInfo->money)
            ->setTotalTransferOut($from_account_info->total_transfer_out - $transferInfo->money);

        if($transferInfo->use_fanli > 0){
            $end_fanli_total = $from_account_info->cash_fanli_remain + $transferInfo->use_fanli;
            if($end_fanli_total < 0){
                throw new \RuntimeException('账户余额扣减后不能小于0', 2);
            }

            $from_record['money'] =  $transferInfo->use_fanli;
            $from_record['after_money'] = $end_fanli_total;
            $from_record['no_type'] = 'UF';
            $from_record['remark'] = '使用返利(销审)';

            $accountMoneyService->setFanliMoneyRecord($from_record)->setCashFanLiRemain($end_fanli_total);
        }

        $accountMoneyService->change();

        return ['use_fanli'=>$transferInfo->use_fanli];
    }

    /**
     * @title 计算转账单使用的返利，并修改之
     * @desc
     * @version
     * @level 1
     * <AUTHOR>
     * @package Fuel\Service
     * @since
     * @params  type filedName required?
     * @param $id
     * @returns
     * []
     * @returns
     */
    static public function calculateFanli($id)
    {
        //获取转账单信息
        $transferInfo = OilAccountMoneyTransfer::getById(['id'=>$id]);
        $org_info = OilOrg::getById(['id'=>$transferInfo->org_id]);
        if(OrgStatus::isUseNewRebateMark($org_info->orgcode)){
            //G7WALLET-6056
            $accountInfo = OilAccountMoney::getByOrgIdForLock(['org_id'=>$transferInfo->org_id]);
            $rate = $accountInfo->cash_fanli_remain/$accountInfo->money;
            $trans_fanli = 0;
            if($rate <= 0){
                $trans_fanli = 0;
            }elseif($rate >= 1){
                $trans_fanli = $transferInfo->money;
            }elseif ($rate <= 0.1){
                $trans_fanli = number_format($transferInfo->money * 0.1,2,".","");
            }elseif($rate > 0.1 && $rate < 1){
                if(fmod($rate,0.1) == 0){
                    $new_rate = $rate;
                }else {
                    $new_rate = substr($rate, 0, 3) + 0.1;
                }
                $trans_fanli = number_format($transferInfo->money * $new_rate,2,".","");
            }

            $up_fanli = 0;
            if($trans_fanli > 0){
                if(bccomp($trans_fanli,$accountInfo->cash_fanli_remain,2) >= 0){
                    $up_fanli = $accountInfo->cash_fanli_remain;
                }elseif (bccomp($trans_fanli,0,2) > 0 && bccomp($accountInfo->cash_fanli_remain,$trans_fanli,2) > 0){
                    $up_fanli = $trans_fanli;
                }
            }

            Log::error("trans-fanli",["rate"=>$rate,"no"=>$transferInfo->no,"t_fanli"=>$trans_fanli,"up_fanli"=>$up_fanli],"accountMoneyTransfer");

            if($up_fanli > 0){
                OilAccountMoneyTransfer::edit(['id'=>$id,'use_fanli'=>$up_fanli]);
            }
        }else{
            //获取返利可用金额
            $fanliRemain = Assign::getFanliRemainForUse($transferInfo->org_id);
            if($fanliRemain > 0){
                if($fanliRemain > $transferInfo->money){
                    $useFanli = $transferInfo->money;
                }else{
                    $useFanli = $fanliRemain;
                }
                OilAccountMoneyTransfer::edit(['id'=>$id,'use_fanli'=>$useFanli]);
            }
        }

    }

    /*
     * 创建发财卡转账单
     */
    static public function createFortuneTransfer(array $params)
    {
        \helper::argumentCheck(['out_vice_info','in_vice_info','amount','other_creator'],$params);

        if($params['out_vice_info']->driver_tel == $params['in_vice_info']->driver_tel){
            throw new \RuntimeException('接收人手机号不能与划拨人手机号相同',2);
        }

        if($params['out_vice_info']->vice_no == $params['in_vice_info']->vice_no){
            throw new \RuntimeException('接收者的卡号和划油卡号不能一致',2);
        }

        $orgInfo = \Models\OilOrg::getById(['id'=>$params['out_vice_info']->org_id]);
        $insertArr = [
            'no' => OilAccountMoneyTransfer::createNo('HB'),
            'sn' => $params['sn'],
            'no_type' => 'HB',
            'org_id' => $params['out_vice_info']->org_id,
            'from_phone' => $params['out_vice_info']->driver_tel,
            'from_account_no' => $params['out_vice_info']->vice_no,
            'from_orgcode'   => $params['out_vice_info']->org->orgcode,
            'from_orgname' => $params['out_vice_info']->driver_name,
            'into_phone' => $params['in_vice_info']->driver_tel,
            'into_orgcode' => $orgInfo->orgcode,
            'into_org_id' => $params['out_vice_info']->org_id,
            'into_account_no' => $params['in_vice_info']->vice_no,
            'into_orgname' => $params['in_vice_info']->driver_name,
            'app_time' => isset($params['app_time']) ? $params['app_time'] : \helper::nowTime(),
            'money' => $params['amount'],
            'createtime' => \helper::nowTime(),
            'other_creator' => $params['other_creator'],
            'data_from' => isset($params['data_from']) ? $params['data_from'] : 1,
            'remark_work' => isset($params['remark_work']) ? $params['remark_work'] : null,
            'remark' => isset($params['remark']) ? $params['remark'] : null,
        ];

        Capsule::connection()->beginTransaction();
        try {
            $info = OilAccountMoneyTransfer::getBySnWithOrgCodeForUpdate(['from_orgcode' => $insertArr['from_orgcode'], 'sn' => $insertArr['sn']]);
            if ($info) {
                throw new \RuntimeException('流水号重复，请重新进入油费划拨', 2);
            }
            $res = OilAccountMoneyTransfer::add($insertArr);

            Capsule::connection()->commit();
            //推送gos
            AccountMoneyTransferToGos::sendBatchCreateTask([$res->id]);

            return $res;
        }catch (\Exception $e){
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), 2);
        }
    }

    /*
     * 划拨工单审核
     */
    static public function auditFortune(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        $transferInfo = OilAccountMoneyTransfer::getById(['id'=>$params['id']]);

        try{
            //请求卡油费划拨
            (new CardViceBill())->cardFcTrade(['transfer_id'=>$params['id']]);

            Log::error('333',[],'tim0528');
            $res = $transferInfo->update([
                'status' => 1,
                'audit_time' => \helper::nowTime()
            ]);
            Log::error('res',[$res],'tim0528');
        }catch (\Exception $e){
            throw new \RuntimeException($e->getMessage(),$e->getCode());
        }

        //推送gos
        AccountMoneyTransferToGos::sendBatchUpdateTask([$params['id']]);

        return $res;

    }

    /*
     * 划拨工单销审
     */
    static public function unAuditFortune(array $params)
    {
        \helper::argumentCheck(['id'],$params);

        $transferInfo = OilAccountMoneyTransfer::getById(['id'=>$params['id']]);

        //请求卡油费划拨
        //todo 请求培俊

        $res = $transferInfo->update([
            'status' => 0,
            'updatetime' => \helper::nowTime()
        ]);

        //推送gos
        AccountMoneyTransferToGos::sendBatchUpdateTask([$res->id]);

        return $res;

    }

    /**
     * 请求慧加油（根据机构获取账户余额）
     * @param $params
     * @return mixed
     */
    static public function getAccountByGas($params)
    {
        \helper::argumentCheck(['org_code'], $params);

        /*******************调用慧加油平台审核接口*********************/
        $data = GasHjyClient::post([
            'method' => 'smartplus_api/gateway/account/getAccountByG7s',
            'data' => ['org_code'=>$params['org_code']]
        ]);

        if(!$data){
            throw new \RuntimeException('您尚未开通该业务',2);
        }

        return $data;
    }

    /*
     * 创建转账工单，并请求gas把转账结果修改转账工单
     */
    static public function transferForGas($params)
    {
        \helper::argumentCheck(['sn','roll_in_org_code','roll_out_org_code','amount'],$params);
        $params['from_orgcode'] = $params['roll_out_org_code'];
        $params['into_orgcode'] = $params['roll_in_org_code'];
        $params['money'] = $params['amount'];
        // 校验
        $gasAccount = self::getAccountByGas(['org_code'=>$params['into_orgcode']]);
        $balance = $gasAccount->cash->balance;
        if($balance < 0 || $params['amount'] > $balance){
            throw new \RuntimeException('机构余额不足',2);
        }

        //校验机构号是否有效
        $org_info = OilAccountMoneyTransfer::getOrgInfo($params);
        if (!$org_info) {
            throw new \RuntimeException('机构不存在',2);
        }

        if($org_info['into_org_info']->id == $org_info['from_org_info']->id) {
            throw new \RuntimeException('同机构不允许转账',2);
        }

        if($params['money'] == 0){
            throw new \RuntimeException('转账不能为0', 2);
        }

        //可用余额
        $used_money = FrozenMoney::accountBalance($org_info['from_org_info']->id);
        Log::debug('addTransfer---', [$used_money, round(($used_money - $params['money']), 2)], 'addTransfer');
        if (round(($used_money - $params['money']), 2) < 0) {
            throw new \RuntimeException('现金账户余额不足，或存在冻结金额导致现金账户可用余额不足', 2);
        }

        Capsule::connection()->beginTransaction();
        try{
            //检查sn是否存在
            $transInfo = OilAccountMoneyTransfer::getBySnWithOrgCodeForUpdate(['sn'=>$params['sn'],'from_orgcode'=>$org_info['from_org_info']->orgcode]);
            if($transInfo){
                throw new \RuntimeException('该流水号已存在', 2);
            }

            $other_creator_id = $params['other_creator_id'] ?? '';
            $other_creator = $params['other_creator'] ?? '';
            $remark_work = $params['remark_work'] ?? '';

            // 请求gas
            self::transferCreateForGas([
                'roll_in_org_code' => $params['roll_in_org_code'],
                'roll_out_org_code' => $params['roll_in_org_code'], //特殊情况
                'sn' => $params['sn'],
                'amount' => $params['amount'] * 100,
                'data_source' => 'g7s',
                'creator_name' => $other_creator
            ]);

            // 创建工单+审核
            $result = self::createTransferApp($org_info['from_org_info']->id,$org_info['into_org_info']->id,$params['money'],$other_creator_id,$other_creator,$remark_work);
            Capsule::connection()->commit();
        }catch (Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        if($result){
            //push至Gos系统
            AccountMoneyTransferToGos::sendBatchCreateTask([$result->id],'sync');
        }

        return true;
    }

    /*
     * 创建转账单+审核
     */
    static public function createTransferApp($from_org_id,$into_org_id,$money,$other_creator_id,$other_creator,$remark_work)
    {
        global $app;
        if(!$app->myAdmin || !$app->myAdmin->id){
            $app->myAdmin = new \stdClass();
            $app->myAdmin->id = 8888;
            $app->myAdmin->true_name = '系统自动';
        }

        $from_org_info = OilOrg::getById(['id'=>$from_org_id]);
        $into_org_info = OilOrg::getById(['id'=>$into_org_id]);
        $from_account_info = OilAccountMoney::getByOrgIdForLock(['org_id'=>$from_org_id]);
        $into_account_info = OilAccountMoney::getByOrgIdForLock(['org_id'=>$into_org_id]);

        $resultsRe = [
            'no'               => OilAccountMoneyTransfer::createNo('ZZ'),
            'sn'               => \GosSDK\Lib\Helper::uuid(),
            'no_type'          => 'ZZ',
            'org_id'           => $from_org_id,
            'into_org_id'      => $into_org_id,
            'from_orgname'     => $from_org_info->org_name,
            'into_orgname'     => $into_org_info->org_name,
            'from_orgcode'     => $from_org_info->orgcode,
            'into_orgcode'     => $into_org_info->orgcode,
            'from_account_no'  => $from_account_info->account_no ? $from_account_info->account_no : null,
            'into_account_no'  => $into_account_info->account_no ? $into_account_info->account_no : null,
            'status'           => 0,
            'app_time'         => \helper::nowTime(),
            'data_from'        => 2,
            'use_fanli'        => 0,
            'money'            => $money,
            'remark'           => '安得:'.$from_org_info->org_name.'来款转账'.$into_org_info->org_name,
            'remark_work'      => $remark_work ?? '',
            'creator_id'       => $app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'other_creator_id' => $other_creator_id ?? '',
            'other_creator'    => $other_creator ?? '',
            'last_operator_id' => $app->myAdmin->id ,
            'last_operator'    => $app->myAdmin->true_name,
            'updatetime'       => \helper::nowTime(),
        ];

        $result = OilAccountMoneyTransfer::add($resultsRe);
        if (!$result) {
            throw new \RuntimeException('创建转账申请单失败', 7);
        }

        //添加工单日志
        \Models\OilCardViceAppLog::add([
            'type'             => 5,
            'app_id'           => $result->id,
            'status'           => 0,
            'status_name'      => '待审核',
            'last_operator'    => $app->myAdmin->true_name,
            'last_operator_id' => $app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'updatetime'       => \helper::nowTime(),
        ]);

        //审核，账户相关操作
        $res = \Fuel\Service\AccountTransfer::transferMoney($result);

        //修改工单
        $result_edit = OilAccountMoneyTransfer::edit([
            'id' => $result->id,
            'status' => 1,
            'audit_time'    =>  date("Y-m-d H:i:s"),
            'use_fanli' => $res['use_fanli'],
            'last_operator_id' => $app->myAdmin->id,
            'last_operator'    => $app->myAdmin->true_name,
            'updatetime'       => \helper::nowTime()]);
        if (!$result_edit) {
            throw new \RuntimeException('更新转账申请单失败', 7);
        }

        //添加工单日志
        \Models\OilCardViceAppLog::add([
            'type'             => 5,
            'app_id'           => $result->id,
            'status'           => 1,
            'status_name'      => '已审核',
            'last_operator'    => $app->myAdmin->true_name,
            'last_operator_id' => $app->myAdmin->id,
            'createtime'       => \helper::nowTime(),
            'updatetime'       => \helper::nowTime(),
        ]);

        return $result;
    }

    /*
     * 获取机构上级
     */
    static public function getOrgListForGas($params)
    {
        \helper::argumentCheck(['org_code'],$params);

        $orgCodeIn = [];
        $org_code = $params['org_code'];
        if(strlen($org_code) > 6){
            $top = substr($org_code,0,6);

            $child = str_split(substr($org_code,6),2);

            $topstr = $top;

            foreach ($child as $key => $value) {
                $topstr = $topstr.$value;
                $orgCodeIn[] = $topstr;
            }
            array_unshift($orgCodeIn, $top);

        }else{
            $orgCodeIn = [$org_code];
        }

        $sql_obj = OilOrg::whereIn('orgcode',$orgCodeIn)
            ->where('is_del',0)
            ->where('orgcode','!=',$params['org_code']);
        if($params['keyword']){
            $sql_obj = $sql_obj->where('org_name','like','%'.$params['keyword'].'%');
        }
        $data = $sql_obj->select('id','orgcode','org_name','sub_org_name')
        ->get();

        return $data;
    }

    /**
     * 请求慧加油（转账申请）
     * @param $params
     * @return mixed
     */
    static public function transferCreateForGas($params)
    {
        \helper::argumentCheck(['roll_in_org_code','roll_out_org_code','sn','data_source','amount'], $params);

        /*******************调用慧加油平台审核接口*********************/
        $data = GasHjyClient::post([
            'method' => 'smartplus_api/gateway/account/transferCreate',
            'data' => $params
        ]);

        return $data;
    }
    //【紧急工单】支持网货宿州基地的预购油业务
    static public function transferForCarrier($params)
    {
        Log::error('params',[$params],'transferForCarrier');
        \helper::argumentCheck(['orgcode', 'sn', 'money', 'company_id'], $params);

        if (bccomp($params['money'], 0, 2) <= 0) {
            Log::error("金额不合法：", [], "transferForCarrier");
            throw new \RuntimeException('转账金额不合法', 101);
        }

        $companyInfo = OilPayCompany::getById(['id' => $params['company_id']]);
        if (!$companyInfo) {
            Log::error("付款公司不存在：", [], "transferForCarrier");
            throw new \RuntimeException('付款公司不存在', 102);
        }

        $companyMap = AccountMoneyTransferStatus::getCompanyOrgMap();
        if(count($companyMap) == 0){
            Log::error("未配置付款公司：", [], "transferForCarrier");
            throw new \RuntimeException('未配置付款公司', 103);
        }
        $from_org_code = isset($companyMap[$params['company_id']])? $companyMap[$params['company_id']] : '';
        if(!$from_org_code){
            Log::error("未配置付款公司：", [], "transferForCarrier");
            throw new \RuntimeException('未配置付款公司', 104);
        }
        $transferMap = AccountMoneyTransferStatus::getTransferOrgMap();
        if(count($transferMap) == 0){
            Log::error("未配置转账关系：", [], "transferForCarrier");
            throw new \RuntimeException('未配置转账关系', 105);
        }
        $to_map = isset($transferMap[$from_org_code])? $transferMap[$from_org_code] : '';
        if(count($to_map) == 0){
            Log::error("未配置转账关系：", [], "transferForCarrier");
            throw new \RuntimeException('未配置转账关系', 106);
        }
        $to_org_code = isset($to_map[$params['orgcode']])? $to_map[$params['orgcode']] : '';
        if(!$to_org_code){
            Log::error("未配置转账关系：", [], "transferForCarrier");
            throw new \RuntimeException('未配置转账关系', 107);
        }

        $fromOrgInfo = OilOrg::getByOrgcode($from_org_code);
        if (!$fromOrgInfo) {
            Log::error("转出机构不存在", [], "transferForCarrier");
            throw new \RuntimeException('转出机构不存在', 108);
        }
        $toOrgInfo = OilOrg::getByOrgcode($to_org_code);
        if (!$toOrgInfo) {
            Log::error("转入机构不存在", [], "transferForCarrier");
            throw new \RuntimeException('转入机构不存在', 109);
        }
        $from_org_id = $fromOrgInfo->id;
        $to_org_id = $toOrgInfo->id;

        $from_account_Info = \Models\OilAccountMoney::getByOrgId(['org_id'=>$from_org_id]);
        $into_account_Info = \Models\OilAccountMoney::getByOrgId(['org_id'=>$to_org_id]);

        Capsule::connection()->beginTransaction();
        try {
            $used_money = FrozenMoney::accountBalance($from_org_id);
            Log::error('addTransfer---', [$used_money, round(($used_money - $params['money']), 2)], 'transferForCarrier');
            if (round(($used_money - $params['money']), 2) < 0) {
                throw new \RuntimeException('现金账户余额不足，或存在冻结金额导致现金账户可用余额不足', 201);
            }

            $hasInfo = OilAccountMoneyTransfer::getBySnLock(['sn' => $params['sn']]);
            if (count($hasInfo) > 0) {
                Log::error("转账单已提交，禁止重复提交：" . json_encode($params), [], "transferForCarrier");
                throw new \RuntimeException('转账单已提交，禁止重复提交', 202);
            }

            $transferData = [
                'no'               => OilAccountMoneyTransfer::createNo('ZZ'),
                'sn'               => $params['sn'],
                'no_type'          => 'ZZ',
                'org_id'           => $from_org_id,
                'into_org_id'      => $to_org_id,
                'from_orgname'     => $fromOrgInfo->org_name,
                'into_orgname'     => $toOrgInfo->org_name,
                'from_orgcode'     => $fromOrgInfo->orgcode,
                'into_orgcode'     => $toOrgInfo->orgcode,
                'from_account_no'  => $from_account_Info->account_no ? $from_account_Info->account_no : null,
                'into_account_no'  => $into_account_Info->account_no ? $into_account_Info->account_no : null,
                'status'           => 0,
                'app_time'         => \helper::nowTime(),
                'data_from'        => 1,
                'money'            => $params['money'],
                'remark'           => isset($params['remark']) && $params['remark'] ? $params['remark'] : '',
                'remark_work'      => isset($params['remark_work']) && $params['remark_work'] ? $params['remark_work'] : '',
                'creator_id'       => '8888',
                'createtime'       => \helper::nowTime(),
                'other_creator_id' => '8888',
                'other_creator'    => '网货系统',
                'last_operator_id' => '8888',
                'last_operator'    => '网货系统',
                'updatetime'       => \helper::nowTime(),
            ];
            $res = OilAccountMoneyTransfer::add($transferData);
            Capsule::connection()->commit();
            Log::error("no:" . json_encode($params), [$res->no], "transferForCarrier");
            AccountMoneyTransferToGos::sendBatchCreateTask([$res->id],'sync');

        }catch (Exception $e) {
            Capsule::connection()->rollBack();
            Log::error("新增异常" . json_encode($params), [$e->getMessage(),$e->getCode()], "transferForCarrier");
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        Log::error("begin-audit:" . json_encode($params), [$res->no], "transferForCarrier");
        
        try {
            self::auditTransferForCarrier(['ids'=>$res->id]);
        }catch (Exception $e) {
            Log::error("审核异常" . json_encode($params), [$e->getMessage(),$e->getCode()], "transferForCarrier");
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
        Log::error("end-audit:" . json_encode($params), [$res->no], "transferForCarrier");
        return $res;
    }

    public static function auditTransferForCarrier($params)
    {
        \helper::argumentCheck(['ids'], $params);
        //开启事务
        Capsule::connection()->beginTransaction();
        try {
            $transferInfo = OilAccountMoneyTransfer::getByIdForUpdate(['id' => $params['ids']]);
            //校验
            self::auditByValidate($transferInfo);

            //账户相关操作
            self::transferMoney($transferInfo);

            //修改工单
            $result = $transferInfo->update([
                'status' => 1,
                'audit_time'    =>  date("Y-m-d H:i:s"),
                'last_operator_id' => '8888',
                'last_operator'    => '网货系统',
                'updatetime'       => \helper::nowTime()
            ]);
            if (!$result) {
                throw new \RuntimeException('更新转账申请单失败', 501);
            }
            Capsule::connection()->commit();
            AccountMoneyTransferToGos::sendBatchUpdateTask([$params['ids']],'sync');
        } catch (Exception $e) {
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
        return true;
    }
}