<?php

namespace Fuel\Service;


use Framework\Job;
use Models\OilDownload;
use Models\OilJobs;
use Models\OilJobsFailed;
use SuperClosure\Serializer;

class JobService
{
    /**
     * 任务重试
     * @param $id
     * @return bool|void
     * @throws \ReflectionException
     */
    public function reTryJob($id)
    {
        $result = false;
        $downloadInfo = OilDownload::getById(['id' => $id]);
        if ($downloadInfo) {
            $job = OilJobs::getByJobId(['job_id' => $downloadInfo->jobs_id]);

            if (!$job) {
                $job = OilJobsFailed::getById(['id' => $downloadInfo->jobs_id]);
            }
            if ($job) {
                try{
                    global $app;
                    $app->myAdmin = $job->userInfo ? \json_decode($job->userInfo) : new \stdClass();
                    if ($job->type == 20) {
                        if(!$job->service_name && !$job->service){
                            throw new \RuntimeException('缺少执行的service', 2);
                        }
                        $service = $job->service_name ? $job->service_name : $job->service;
                        $serviceObj = new \ReflectionClass($service);
                        $job->data = isset($job->data) ? \unserialize($job->data) : \unserialize($job->params);
                        $instance = $serviceObj->newInstance($job->data);
                        $job->jobId = $downloadInfo->jobs_id;
                        $result = $instance
                            ->setJobs($job)
                            ->handle();
                    } else {
                        $serializer = new Serializer();
                        $taskInfo = \json_decode($job->data);
                        $closure = $serializer->unserialize($taskInfo);
                        $result = Job::fire($closure, $job);
                    }
                }catch (\Exception $e){
                    throw new \RuntimeException($e->getMessage(),2);
                }
            } else {
                throw new \RuntimeException('未找到ID：' . $id . '的任务', 2);
            }
        } else {
            throw new \RuntimeException('未找到ID：' . $id . '的下载任务', 2);
        }

        OilJobsFailed::remove(['ids'=>$downloadInfo->jobs_id]);
        OilJobs::where('job_id',$downloadInfo->jobs_id)->update(['status'=>2]);

        return $result;
    }
}