<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/1/11/011
 * Time: 17:12
 */

namespace Framework\Excel;

use Framework\Log;
use PHPExcel;
use PHPExcel_IOFactory;
use PHPExcel_Reader_Excel2007;

class ExcelReader
{
    /**
     * $excelParams = [
     * 'filePath'    =>            APP_ROOT.DIRECTORY_SEPARATOR.'www'.DIRECTORY_SEPARATOR.'download'.DIRECTORY_SEPARATOR.'FP1512040009_1000113700014194903.xls',
     * 'fieldsMap'    =>  ['抬头'=>'字段名'],
     * 'fieldsRowNum' =>  1
     * ];
     * $result = ExcelReader::read($excelParams);
     * print_r($result);
     * exit;
     */
    /**
     * @title
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  Framework\Excel
     * @since
     * @params   type filedName required?
     * @param array $params
     * @param callable|NULL $callBack
     * @param callable|NULL $callBack2
     * @return array
     * @returns
     * array
     * @returns
     */
    static public function read(array $params, callable $callBack = NULL, callable $callBack2 = NULL,$modifyTime = false)
    {
        Log::error('$params==='.var_export($params,true), [], 'importReceipt');
        $filerNameArr = [];
        if (isset($params['filterSheet'])) {
            $filerNameArr = is_array($params['filterSheet']) ? $params['filterSheet'] : [$params['filterSheet']];
        }

        $inputFileName = $params['filePath'];
        $inputFileType = PHPExcel_IOFactory::identify($inputFileName);
        //G7WALLET-4518
        if(in_array($inputFileType,['CSV','HTML','SYLK'])){
            throw new \RuntimeException('导入模板无法识别，请重新下载模板！！！', 2);
        }

        $objReader = PHPExcel_IOFactory::createReader($inputFileType);

        $objReader->setReadDataOnly(TRUE);

        $objReader->setLoadAllSheets();
        $objPHPExcel = $objReader->load($inputFileName);

        $sheetNames = $objReader->listWorksheetNames($inputFileName);

        $data = [];

        $fieldsMap = [];
        if(isset($params['fieldsMap']) && $params['fieldsMap']){
            $fieldsMap = [$params['fieldsMap']];
        }elseif(isset($params['multiFieldsMap']) && $params['multiFieldsMap']){
            $fieldsMap = $params['multiFieldsMap'];
        }
        if (count($sheetNames) > 0) {
            foreach ($sheetNames as $k => $v) {
                Log::error('$sheetNames-$k=='.$k.'$sheetNames-v：' . var_export($v,true), [], 'importReceipt');
                if($k >= count($fieldsMap)){
                    break;
                }
                if (in_array($v, $filerNameArr)) {
                    continue;
                }
                $title = [];

                $data[$k] = [];
                $currentSheet = $objPHPExcel->getSheet($k);
                Log::error('getSheet-v：' . var_export($currentSheet,true), [], 'importReceipt');
                $allColumn = $currentSheet->getHighestColumn();
                $allRow = $currentSheet->getHighestRow();
                $params['fieldsRowNum'] = isset($params['fieldsRowNum']) ? $params['fieldsRowNum'] : 1;

                Log::error('$fieldsMap==='.var_export($fieldsMap,true), [], 'importReceipt');

                $highestColumnNum = \PHPExcel_Cell::columnIndexFromString($allColumn);

                $columnArr = self::makeAllColumns($highestColumnNum);
                Log::error('$allRow--'.$allRow.'===$columnArr-v：' . var_export($columnArr,true), [], 'importReceipt');
                for ($currentRow = 1; $currentRow <= $allRow; $currentRow++) {

                    foreach ($columnArr as $currentColumn) {
                        $address = $currentColumn . $currentRow;
                        $cellValue = $currentSheet->getCell($address)->getValue();
                        $cellValue = trim($cellValue);
                        Log::error('$currentColumn==='.$currentColumn.'==$currentRow::'.$currentRow.'---$cellValue-v：' . var_export($cellValue,true), [], 'importReceipt');
                        if(isset($fieldsMap[$k]) && isset($params['fieldsRowNum']) && $currentRow >= $params['fieldsRowNum']){
                            Log::error('isset---$fieldsMap==='.var_export($fieldsMap,true), [], 'importReceipt');
                            if (isset($fieldsMap[$k][$cellValue])) {
                                $title[$currentColumn] = $fieldsMap[$k][$cellValue];
                            }else{
                                if(isset($title[$currentColumn]) && $title[$currentColumn]){
                                    if ($callBack) {
                                        $cellValue = $callBack($currentRow, $title[$currentColumn], $cellValue);
                                    }
                                    //txb 2018.9.13 处理发票管理，导入的发票时间不正确的问题
                                    if( (stripos($title[$currentColumn],"_time") !== false || stripos($title[$currentColumn],"_date") !== false) && !empty($cellValue) && $modifyTime ){
                                        if( stripos($cellValue,"-") === false){
                                            $tmpValue = \PHPExcel_Shared_Date::ExcelToPHP($cellValue);
                                            $cellValue = gmdate('Y-m-d H:i:s', $tmpValue);
                                        }
                                    }
                                    $data[$k][$currentRow][$title[$currentColumn]] = $cellValue;
                                }elseif(isset($params['ignore_all']) && $params['ignore_all'] ==1){
                                    $data[$k][$currentRow][] = $cellValue;
                                }
                            }
                        }elseif(!isset($fieldsMap[$k]) && isset($params['ignore']) && $params['ignore'] ==1) {
                            Log::error('ignore==='.$params['ignore'], [], 'importReceipt');
                            if (isset($title[$currentColumn])) {
                                if ($callBack) {
                                    $cellValue = $callBack($currentRow, $title[$currentColumn], $cellValue);
                                }
                                $data[$k][$currentRow][$title[$currentColumn]] = $cellValue;
                            } else {
                                $data[$k][$currentRow][] = $cellValue;
                            }
                        }
                    }

                    Log::error('$title--'. var_export($title,true).'excel==data' . var_export($data,true), [], 'importReceipt');

                    //校验行中的数据
                    if ($currentRow > 1 && $callBack2) {
                        $callBack2($data[$k][$currentRow], $currentRow);
                    }
                }

                if (!$title && (!isset($params['ignore']) || !$params['ignore'])) {
                    throw new \RuntimeException('导入模板不匹配', 2);
                }
            }
        }

        return $data;
    }

    static private function makeAllColumns($highestColumnNum = NULL)
    {
        $columnArr = [];
        foreach (range('A', 'Z') as $v) {
            $columnArr[] = $v;
        }
        foreach (range('A', 'Z') as $v) {
            foreach (range('A', 'Z') as $vv) {
                $columnArr[] = $v . $vv;
            }
        }

        return $highestColumnNum ? array_slice($columnArr, 0, $highestColumnNum) : $columnArr;
    }
}
