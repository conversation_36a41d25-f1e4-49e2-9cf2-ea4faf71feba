<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/1/11/011
 * Time: 17:12
 */

namespace Framework\Excel;

use Framework\Helper;
use Framework\Log;
use PHPExcel;
use PHPExcel_IOFactory;
use PHPExcel_Writer_Excel2007;
use PHPExcel_Style_Border;

/**
 * Class ExcelWriter
 * @package Framework\Excel
 */
class ExcelWriter
{
    /**
     * @example
     $exportData = [
        'fileName'	=>	'文件名',
        'sheetName'	=>	'Sheet名',
        'title'	=>	[
            'main_no' =>  '主卡号',
            'oil_com' =>  '油卡类型',
            'active_time' =>  '开卡时间',
        ],
        'data'	=>	[
            (object)['title'=>'这是一条测试数据1','content'=>'这是测试数据1的内容'],
            (object)['title'=>'这是一条测试数据2','content'=>'这是测试数据2的内容'],
            (object)['title'=>'这是一条测试数据3','content'=>'这是测试数据3的内容'],
            (object)['title'=>'这是一条测试数据4','content'=>'这是测试数据4的内容'],
            (object)['title'=>'这是一条测试数据5','content'=>'这是测试数据5的内容'],
            (object)['title'=>'这是一条测试数据6','content'=>'这是测试数据6的内容'],
        ],
        //多sheet设置
        'multiSheet' => [
            [
            'sheetName'=> '这是第二个sheet名字',
            'title' => [
                'no'            => '单号2',
                'orgroot'      => '顶级机构2',
                'org_name'            => '机构2',
                'name'            => '机构运营商2',
                'app_time'      => '申请时间2',
                'money'  => '现金充值2',
                'fanli_charge'  => '返利充值2',
                'status'        => '状态2',
                'pay_name'     => '付款人2',
                'pay_type'     => '付款方式2',
                'pay_status'     => '付款状态2',
                'pay_no' => '付款单号2',
                'data_from'    => '数据来源2',
                'true_name'    => '创建人2',
                'remark'        => '备注/内2',
                'remark_work'   => '备注/外2',
                'last_operator'   => '最后修改人2',
                'updatetime'   => '最新更新时间2',
            ],
            'data'      => $_data
        ]
        ];

    ExcelWriter::exportXls($exportData);

    $file = ExcelWriter::exportXls($exportData,function($phpExcelObj,$data,$lineCell){
        if($data['name'] == 'main_no'){
            $phpExcelObj->setCellValueExplicit($lineCell['cellNum'].$lineCell['lineNum'],$data['value'],PHPExcel_Cell_DataType::TYPE_STRING);
        }else{
            $phpExcelObj->setCellValue($lineCell['cellNum'].$lineCell['lineNum'],$data['value']);
        }
    });
     */

    /**
     * xls导出
     * @param array $params
     * fileName string  文件名
     * sheetName    string  页签名
     * filePath string  文件相对路径
     * title    array   excel抬头
     * download boolean 直接导出
     * data array   数据
     *
     * @param callable|NULL $cellCallBack   每个单元格的回调
     * @param callable|NULL $lineCallBack   每一行的回调
     * @return bool|mixed|string
     * @throws \PHPExcel_Exception
     */
    static public function exportXls(array $params,callable $cellCallBack=NULL,callable $lineCallBack=NULL)
    {
        $objPHPExcel = new PHPExcel();

        $activeSheet = $objPHPExcel->getActiveSheet();
        $activeSheet->getProtection()->setSheet(false);

        if(isset($params['title']) && $params['title'] && isset($params['data']) && $params['data']){
            if(isset($params['fileExt']) && strtolower($params['fileExt']) == 'csv'){
                $titleArr = array_values($params['title']);
                $fields = array_keys($params['title']);
                if(isset($params['is_append']) && $params['is_append']){
                    $str = [];
                }else{
                    $str = [implode(",",$titleArr)];
                }
                foreach($params['data'] as $k=>&$v){
                    //foreach ($v as &$item) {
                    //    $item = Helper::trimAll($item);
                    //}

                    $v = (array)$v;
                    $_v = [];
                    foreach($v as &$d){
                        $d = is_null($d) ? '' : $d;
                    }
                    foreach($params['title'] as $fieldName=>$title){
                        if(isset($v[$fieldName])){
                            $_v[] = $v[$fieldName];
                        }else{
                            $_v[] = '';
                        }
                    }
                    $str[] = implode(",", $_v);
                }

                $fileName = mb_convert_encoding($params['fileName'],'GBK','UTF-8');
//                $fileName = iconv("utf-8","gb2312",$params['fileName']);
//                $content = iconv("utf-8","GBK", implode("\r\n",$str));
                $content = mb_convert_encoding(implode("\r\n",$str),'GBK','UTF-8');
                if(isset($params['is_append']) && $params['is_append']){
                    $content = PHP_EOL.$content;
                }
                $fileAllName = isset($params['filePath']) && $params['filePath'] ? $params['filePath'].DIRECTORY_SEPARATOR.$fileName.'.csv' : APP_WWW_ROOT.DIRECTORY_SEPARATOR.'data'. DIRECTORY_SEPARATOR.$fileName.'.csv';
                if(isset($params['is_append']) && $params['is_append']){
                    file_put_contents($fileAllName, $content,FILE_APPEND);
                }else{
                    file_put_contents($fileAllName, $content);
                }

                if(isset($params['download']) && !$params['download']){
                    header('Content-Type: application/csv');
                    header("Content-disposition: attachment; filename={$fileName}.csv");

                }else{
                    Log::error('export-fileAllName'.$fileAllName,[],'export');
                    return $fileAllName;
                }
            }else{
                $i = 'a';
                $titleColumns = [];
                foreach($params['title'] as $k=>$v){
                    $activeSheet->setCellValue($i.'1', $v);
                    $activeSheet->getColumnDimension($i.'1')->setAutoSize(true);
                    $titleColumns[$k] = $i;
                    $i++;
                }

                $j='a';
                foreach($params['data'] as $k=>$v){
                    $num = $k+2;
                    if($v){
                        //如需自行处理，回调逻辑
                        if(!is_null($lineCallBack)){
                            $lineCallBack($activeSheet,$v,$k);
                        }else{
                            foreach($v as $a=>$b){
                                if(!in_array($a,array_keys($params['title']))){
                                    continue;
                                }
                                $_column = $titleColumns[$a];
                                if(!is_null($cellCallBack)){
                                    $cellCallBack(
                                        $activeSheet,
                                        ['name'=>$a,'value'=>$b],
                                        ['lineNum'=>$num,'cellNum'=>$_column]
                                    );
                                }else{
                                    $activeSheet->setCellValue($_column.$num, $b);
                                    //$activeSheet->getColumnDimension($_column.$num)->setAutoSize(true);
                                }
                            }
                        }
                        unset($v);
                    }
                }
                $sheetName = isset($params['sheetName']) && $params['sheetName'] ? strval($params['sheetName']) : strval('Sheet1');
                $activeSheet->setTitle($sheetName);

                //支持多Sheet逻辑处理
                if(isset($params['multiSheet']) && $params['multiSheet']){
                    foreach($params['multiSheet'] as $key=>$val){
                        $activeSheet = $objPHPExcel->createSheet($key+1);
                        $activeSheet->getProtection()->setSheet(false);
                        $sheetName = isset($val['sheetName']) && $val['sheetName'] ? strval($val['sheetName']) : strval('Sheet'.($key+1));
                        $activeSheet->setTitle($sheetName);

                        $i = 'a';
                        $titleColumns = [];
                        foreach($val['title'] as $k=>$v){
                            $activeSheet->setCellValue($i.'1', $v);
                            $activeSheet->getColumnDimension($i.'1')->setAutoSize(true);
                            $titleColumns[$k] = $i;
                            $i++;
                        }

                        foreach($val['data'] as $k1=>$vv){
                            $num = $k1+2;
                            if($vv){
                                //如需自行处理，回调逻辑
                                if(!is_null($lineCallBack)){
                                    $lineCallBack($activeSheet,$vv,$k1);
                                }else{
                                    foreach($vv as $a=>$b){
                                        if(!in_array($a,array_keys($val['title']))){
                                            continue;
                                        }
                                        $_column = $titleColumns[$a];
                                        if(!is_null($cellCallBack)){
                                            $cellCallBack(
                                                $activeSheet,
                                                ['name'=>$a,'value'=>$b],
                                                ['lineNum'=>$num,'cellNum'=>$_column]
                                            );
                                        }else{
                                            $activeSheet->setCellValue($_column.$num, $b);
                                            //$activeSheet->getColumnDimension($_column.$num)->setAutoSize(true);
                                        }
                                    }
                                }
                                unset($vv);
                            }
                        }

                        unset($val);
                    }
                }

                return self::outPutExcel($objPHPExcel,$activeSheet,$params);
            }
        }

        unset($objPHPExcel,$activeSheet);
        return true;
    }

    /**
     * 输出
     * @param $objPHPExcel
     * @param $activeSheet
     * @param $params
     * @return string
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    static private function outPutExcel($objPHPExcel,$activeSheet,$params)
    {

        $fileName = isset($params['fileName']) && $params['fileName'] ? strval($params['fileName']) : date("YmdHis");
        $fileExt = isset($params['fileExt']) && $params['fileExt'] ? strval($params['fileExt']) : 'xlsx';


        if(isset($params['fileExt'])){
            if(strtolower($params['fileExt']) == 'xlsx'){
                $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');  //excel5为xls格式，excel2007为xlsx格式
            }elseif(strtolower($params['fileExt']) == 'xls'){
                $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');  //excel5为xls格式，excel2007为xlsx格式
            }elseif(strtolower($params['fileExt']) == 'csv'){
                $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'CSV');  //excel5为xls格式，excel2007为xlsx格式
                $objWriter->setUseBOM(true);
            }
        }else{
            $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');  //excel5为xls格式，excel2007为xlsx格式
        }

        if(!isset($params['download']) || !$params['download']){
            // excel头参数
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="'.$fileName.'.'.$fileExt.'"');  //日期为文件名后缀
            header('Cache-Control: max-age=0');
            $objWriter->save('php://output');
        }else{
            $basePath = \APP_ROOT.\DIRECTORY_SEPARATOR.'www';
            $defaultFilePath = $basePath.DIRECTORY_SEPARATOR.'download'
                .DIRECTORY_SEPARATOR
                .$fileName.'.'
                .$fileExt;

            $filePath = isset($params['filePath']) && $params['filePath']
                ? trim($params['filePath']).DIRECTORY_SEPARATOR.$fileName.'.'.$fileExt
                : $defaultFilePath;

            $objWriter->save($filePath);

//            return str_replace($basePath,'',$filePath);
            return $filePath;
        }

    }

}