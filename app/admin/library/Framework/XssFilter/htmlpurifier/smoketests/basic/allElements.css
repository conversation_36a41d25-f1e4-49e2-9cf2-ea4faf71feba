div > * {background:#F00; color:#FFF; font-weight:bold; padding:0.2em; margin:0.1em;}
#core-attributes #core-attributes-id,
#core-attributes .core-attributes-class,
#core-attributes div[title='tooltip'],
#core-attributes div[lang='en'],
#core-attributes div[onclick="alert('foo');"],
#module-text abbr,
#module-text acronym,
#module-text div blockquote,
#module-text blockquote[cite='http://www.example.com'],
#module-text br,
#module-text cite,
#module-text code,
#module-text dfn,
#module-text em,
#module-text h1,
#module-text h2,
#module-text h3,
#module-text h4,
#module-text h5,
#module-text h6,
#module-text kbd,
#module-text p,
#module-text pre,
#module-text span q,
#module-text q[cite='http://www.example.com'],
#module-text samp,
#module-text strong,
#module-text var,
#module-hypertext span a,
#module-hypertext a[accesskey='q'],
#module-hypertext a[charset='UTF-8'],
#module-hypertext a[href='http://www.example.com/'],
#module-hypertext a[hreflang='en'],
#module-hypertext a[rel='nofollow'],
#module-hypertext a[rev='index'],
#module-hypertext a[tabindex='1'],
#module-hypertext a[type='text/plain'],
#module-list dl,
#module-list ul,
#module-list ol,
#module-list li,
#module-list dd,
#module-list dt,
.insert-declarations-above
 {background:#008000; margin:0; padding:0.2em;}
#module-text span, #module-text div {padding:0; margin:0.1em;}
#module-list li, #module-list dd, #module-list dt {border:1px solid #FFF;}

/* vim: et sw=4 sts=4 */
