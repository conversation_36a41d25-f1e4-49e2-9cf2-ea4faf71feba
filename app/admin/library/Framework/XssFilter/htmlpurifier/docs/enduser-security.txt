
Security

Like anything that claims to afford security, HTML_Purifier can be circumvented
through negligence of people. This class will do its job: no more, no less,
and it's up to you to provide it the proper information and proper context
to be effective. Things to remember:

1. Character Encoding: see enduser-utf8.html for more info.

2. IDs: see enduser-id.html for more info

3. URIs: see enduser-uri-filter.html

4. CSS: document pending
Explain which CSS styles we blocked and why.

    vim: et sw=4 sts=4
