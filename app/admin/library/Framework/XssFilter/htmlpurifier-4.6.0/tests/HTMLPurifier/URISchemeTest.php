<?php

// WARNING: All the URI schemes are far to relaxed, we need to tighten
// the checks.

class HTMLPurifier_URISchemeTest extends HTMLPurifier_URIHarness
{

    private $pngBase64;

    public function __construct()
    {
        $this->pngBase64 =
            'iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGP'.
            'C/xhBQAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9YGARc5KB0XV+IA'.
            'AAAddEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIFRoZSBHSU1Q72QlbgAAAF1J'.
            'REFUGNO9zL0NglAAxPEfdLTs4BZM4DIO4C7OwQg2JoQ9LE1exdlYvBBeZ7jq'.
            'ch9//q1uH4TLzw4d6+ErXMMcXuHWxId3KOETnnXXV6MJpcq2MLaI97CER3N0'.
            'vr4MkhoXe0rZigAAAABJRU5ErkJggg==';
    }

    protected function assertValidation($uri, $expect_uri = true)
    {
        $this->prepareURI($uri, $expect_uri);
        $this->config->set('URI.AllowedSchemes', array($uri->scheme));
        // convenience hack: the scheme should be explicitly specified
        $scheme = $uri->getSchemeObj($this->config, $this->context);
        $result = $scheme->validate($uri, $this->config, $this->context);
        $this->assertEitherFailOrIdentical($result, $uri, $expect_uri);
    }

    public function test_http_regular()
    {
        $this->assertValidation(
            'http://example.com/?s=q#fragment'
        );
    }

    public function test_http_uppercase()
    {
        $this->assertValidation(
            'http://example.com/FOO'
        );
    }

    public function test_http_removeDefaultPort()
    {
        $this->assertValidation(
            'http://example.com:80',
            'http://example.com'
        );
    }

    public function test_http_removeUserInfo()
    {
        $this->assertValidation(
            'http://<EMAIL>',
            'http://example.com'
        );
    }

    public function test_http_preserveNonDefaultPort()
    {
        $this->assertValidation(
            'http://example.com:8080'
        );
    }

    public function test_https_regular()
    {
        $this->assertValidation(
            'https://<EMAIL>:443/?s=q#frag',
            'https://example.com/?s=q#frag'
        );
    }

    public function test_ftp_regular()
    {
        $this->assertValidation(
            'ftp://<EMAIL>/path'
        );
    }

    public function test_ftp_removeDefaultPort()
    {
        $this->assertValidation(
            'ftp://example.com:21',
            'ftp://example.com'
        );
    }

    public function test_ftp_removeQueryString()
    {
        $this->assertValidation(
            'ftp://example.com?s=q',
            'ftp://example.com'
        );
    }

    public function test_ftp_preserveValidTypecode()
    {
        $this->assertValidation(
            'ftp://example.com/file.txt;type=a'
        );
    }

    public function test_ftp_removeInvalidTypecode()
    {
        $this->assertValidation(
            'ftp://example.com/file.txt;type=z',
            'ftp://example.com/file.txt'
        );
    }

    public function test_ftp_encodeExtraSemicolons()
    {
        $this->assertValidation(
            'ftp://example.com/too;many;semicolons=1',
            'ftp://example.com/too%3Bmany%3Bsemicolons=1'
        );
    }

    public function test_news_regular()
    {
        $this->assertValidation(
            'news:gmane.science.linguistics'
        );
    }

    public function test_news_explicit()
    {
        $this->assertValidation(
            'news:<EMAIL>'
        );
    }

    public function test_news_removeNonPathComponents()
    {
        $this->assertValidation(
            'news://<EMAIL>:80/rec.music?path=foo#frag',
            'news:/rec.music#frag'
        );
    }

    public function test_nntp_regular()
    {
        $this->assertValidation(
            'nntp://news.example.com/alt.misc/42#frag'
        );
    }

    public function test_nntp_removalOfRedundantOrUselessComponents()
    {
        $this->assertValidation(
            'nntp://<EMAIL>:119/alt.misc/42?s=q#frag',
            'nntp://news.example.com/alt.misc/42#frag'
        );
    }

    public function test_mailto_regular()
    {
        $this->assertValidation(
            'mailto:<EMAIL>'
        );
    }

    public function test_mailto_removalOfRedundantOrUselessComponents()
    {
        $this->assertValidation(
            'mailto://<EMAIL>:80/<EMAIL>?subject=Foo#frag',
            'mailto:/<EMAIL>?subject=Foo#frag'
        );
    }

    public function test_data_png()
    {
        $this->assertValidation(
            'data:image/png;base64,'.$this->pngBase64
        );
    }

    public function test_data_malformed()
    {
        $this->assertValidation(
            'data:image/png;base64,vr4MkhoXJRU5ErkJggg==',
            false
        );
    }

    public function test_data_implicit()
    {
        $this->assertValidation(
            'data:base64,'.$this->pngBase64,
            'data:image/png;base64,'.$this->pngBase64
        );
    }

    public function test_file_basic()
    {
        $this->assertValidation(
            'file://user@MYCOMPUTER:12/foo/bar?baz#frag',
            'file://MYCOMPUTER/foo/bar#frag'
        );
    }

    public function test_file_local()
    {
        $this->assertValidation(
            'file:///foo/bar?baz#frag',
            'file:///foo/bar#frag'
        );
    }

    public function test_ftp_empty_host()
    {
        $this->assertValidation('ftp:///example.com', false);
    }

}

// vim: et sw=4 sts=4
