<?php

class HTMLPurifier_AttrDef_URI_EmailHarness extends HTMLPurifier_AttrDefHarness
{

    /**
     * Tests common email strings that are obviously pass/fail
     */
    public function testCore()
    {
        $this->assertDef('<EMAIL>');
        $this->assertDef('  <EMAIL>  ', '<EMAIL>');
        $this->assertDef('<EMAIL>');
        $this->assertDef('<EMAIL>');
        $this->assertDef('<EMAIL>');

        // extended format, with real name
        //$this->assertDef('<EMAIL>%3E');
        //$this->assertDef('Bob Builder <<EMAIL>>');

        // time to fail
        $this->assertDef('bob', false);
        $this->assertDef('bob@home@work', false);
        $this->assertDef('@example.com', false);
        $this->assertDef('bob@', false);
        $this->assertDef('', false);

    }

}

// vim: et sw=4 sts=4
