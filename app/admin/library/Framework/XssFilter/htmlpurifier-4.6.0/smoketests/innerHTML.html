<html>
<head>
    <title>innerHTML smoketest</title>
</head>
<body>
<!--

What we're going to do is use JavaScript to calculate
fixpoints of innerHTML parse and reparsing.  We start with
an input value, encoded in a JavaScript string.

x.innerHTML = input

We then snapshot the DOM state of x, and then perform the
iteration:

intermediate = x.innerHTML
x.innerHTML = intermediate

What inputs are we going to test?

We will generate using the following alphabet:

    a01~!@#$%^&*()_+`-=[]\{}|;':",./<>? (and <space>)



-->
<textarea id="out" style="width:100%;height:100%;"></textarea>
<div id="testContainer" style="display:none"></div>
<script src="innerHTML.js" type="text/javascript"></script>
</body>
</html>
