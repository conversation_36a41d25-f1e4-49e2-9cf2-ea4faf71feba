Output.FixInnerHTML
TYPE: bool
VERSION: 4.3.0
DEFAULT: true
--DESCRIPTION--
<p>
  If true, HTML Purifier will protect against Internet Explorer's
  mishandling of the <code>innerHTML</code> attribute by appending
  a space to any attribute that does not contain angled brackets, spaces
  or quotes, but contains a backtick.  This slightly changes the
  semantics of any given attribute, so if this is unacceptable and
  you do not use <code>innerHTML</code> on any of your pages, you can
  turn this directive off.
</p>
--# vim: et sw=4 sts=4
