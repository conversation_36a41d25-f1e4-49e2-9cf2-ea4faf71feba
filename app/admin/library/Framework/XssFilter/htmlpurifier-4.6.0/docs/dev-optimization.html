<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="description" content="Discusses possible methods of optimizing HTML Purifier." />
<link rel="stylesheet" type="text/css" href="./style.css" />

<title>Optimization - HTML Purifier</title>

</head><body>

<h1>Optimization</h1>

<div id="filing">Filed under Development</div>
<div id="index">Return to the <a href="index.html">index</a>.</div>
<div id="home"><a href="http://htmlpurifier.org/">HTML Purifier</a> End-User Documentation</div>

<p>Here are some possible optimization techniques we can apply to code sections if
they turn out to be slow.  Be sure not to prematurely optimize: if you get
that itch, put it here!</p>

<ul>
    <li>Make Tokens Flyweights (may prove problematic, probably not worth it)</li>
    <li>Rewrite regexps into PHP code</li>
    <li>Batch regexp validation (do as many per function call as possible)</li>
    <li>Parallelize strategies</li>
</ul>

</body></html>

<!-- vim: et sw=4 sts=4
-->
