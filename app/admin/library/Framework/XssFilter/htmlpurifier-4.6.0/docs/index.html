<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="description" content="Index to all HTML Purifier documentation." />
<link rel="stylesheet" type="text/css" href="./style.css" />

<title>Documentation - HTML Purifier</title>

</head>
<body>

<h1>Documentation</h1>

<p><strong><a href="http://htmlpurifier.org/">HTML Purifier</a></strong> has documentation for all types of people.
Here is an index of all of them.</p>

<h2>End-user</h2>
<p>End-user documentation that contains articles, tutorials and useful
information for casual developers using HTML Purifier.</p>

<dl>

<dt><a href="enduser-id.html">IDs</a></dt>
<dd>Explains various methods for allowing IDs in documents safely.</dd>

<dt><a href="enduser-youtube.html">Embedding YouTube videos</a></dt>
<dd>Explains how to safely allow the embedding of flash from trusted sites.</dd>

<dt><a href="enduser-slow.html">Speeding up HTML Purifier</a></dt>
<dd>Explains how to speed up HTML Purifier through caching or inbound filtering.</dd>

<dt><a href="enduser-utf8.html">UTF-8: The Secret of Character Encoding</a></dt>
<dd>Describes the rationale for using UTF-8, the ramifications otherwise, and how to make the switch.</dd>

<dt><a href="enduser-tidy.html">Tidy</a></dt>
<dd>Tutorial for tweaking HTML Purifier's Tidy-like behavior.</dd>

<dt><a href="enduser-customize.html">Customize</a></dt>
<dd>Tutorial for customizing HTML Purifier's tag and attribute sets.</dd>

<dt><a href="enduser-uri-filter.html">URI Filters</a></dt>
<dd>Tutorial for creating custom URI filters.</dd>

</dl>

<h2>Development</h2>
<p>Developer documentation detailing code issues, roadmaps and project
conventions.</p>

<dl>

<dt><a href="dev-progress.html">Implementation Progress</a></dt>
<dd>Tables detailing HTML element and CSS property implementation coverage.</dd>

<dt><a href="dev-naming.html">Naming Conventions</a></dt>
<dd>Defines class naming conventions.</dd>

<dt><a href="dev-optimization.html">Optimization</a></dt>
<dd>Discusses possible methods of optimizing HTML Purifier.</dd>

<dt><a href="dev-flush.html">Flushing the Purifier</a></dt>
<dd>Discusses when to flush HTML Purifier's various caches.</dd>

<dt><a href="dev-advanced-api.html">Advanced API</a></dt>
<dd>Specification for HTML Purifier's advanced API for defining
custom filtering behavior.</dd>

<dt><a href="dev-config-schema.html">Config Schema</a></dt>
<dd>Describes config schema framework in HTML Purifier.</dd>

</dl>

<h2>Proposals</h2>
<p>Proposed features, as well as the associated rambling to get a clear
objective in place before attempted implementation.</p>

<dl>
<dt><a href="proposal-colors.html">Colors</a></dt>
<dd>Proposal to allow for color constraints.</dd>
</dl>

<h2>Reference</h2>
<p>Miscellaneous essays, research pieces and other reference type material
that may not directly discuss HTML Purifier.</p>

<dl>
<dt><a href="ref-devnetwork.html">DevNetwork Credits</a></dt>
<dd>Credits and links to DevNetwork forum topics.</dd>
</dl>

<h2>Internal memos</h2>

<p>Plaintext documents that are more for use by active developers of
the code. They may be upgraded to HTML files or stay as TXT scratchpads.</p>

<table class="table">

<thead><tr>
    <th style="width:10%">Type</th>
    <th style="width:20%">Name</th>
    <th>Description</th>
</tr></thead>

<tbody>

<tr>
    <td>End-user</td>
    <td><a href="enduser-overview.txt">Overview</a></td>
    <td>High level overview of the general control flow (mostly obsolete).</td>
</tr>

<tr>
    <td>End-user</td>
    <td><a href="enduser-security.txt">Security</a></td>
    <td>Common security issues that may still arise (half-baked).</td>
</tr>

<tr>
    <td>Development</td>
    <td><a href="dev-config-bcbreaks.txt">Config BC Breaks</a></td>
    <td>Backwards-incompatible changes in HTML Purifier 4.0.0</td>
</tr>

<tr>
    <td>Development</td>
    <td><a href="dev-code-quality.txt">Code Quality Issues</a></td>
    <td>Enumerates code quality issues and places that need to be refactored.</td>
</tr>

<tr>
    <td>Proposal</td>
    <td><a href="proposal-filter-levels.txt">Filter levels</a></td>
    <td>Outlines details of projected configurable level of filtering.</td>
</tr>

<tr>
    <td>Proposal</td>
    <td><a href="proposal-language.txt">Language</a></td>
    <td>Specification of I18N for error messages derived from MediaWiki (half-baked).</td>
</tr>

<tr>
    <td>Proposal</td>
    <td><a href="proposal-new-directives.txt">New directives</a></td>
    <td>Assorted configuration options that could be implemented.</td>
</tr>

<tr>
    <td>Proposal</td>
    <td><a href="proposal-css-extraction.txt">CSS extraction</a></td>
    <td>Taking the inline CSS out of documents and into <code>style</code>.</td>
</tr>

<tr>
    <td>Reference</td>
    <td><a href="ref-content-models.txt">Handling Content Model Changes</a></td>
    <td>Discusses how to tidy up content model changes using custom ChildDef classes.</td>
</tr>

<tr>
    <td>Reference</td>
    <td><a href="ref-proprietary-tags.txt">Proprietary tags</a></td>
    <td>List of vendor-specific tags we may want to transform to W3C compliant markup.</td>
</tr>

<tr>
    <td>Reference</td>
    <td><a href="ref-html-modularization.txt">Modularization of HTMLDefinition</a></td>
    <td>Provides a high-level overview of the concepts behind HTMLModules.</td>
</tr>

<tr>
    <td>Reference</td>
    <td><a href="ref-whatwg.txt">WHATWG</a></td>
    <td>How WHATWG plays into what we need to do.</td>
</tr>

</tbody>

</table>

</body>
</html>

<!-- vim: et sw=4 sts=4
-->
