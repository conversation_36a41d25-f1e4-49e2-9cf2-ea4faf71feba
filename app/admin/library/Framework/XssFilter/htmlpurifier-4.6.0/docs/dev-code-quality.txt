
Code Quality Issues

Okay, face it.  Programmers can get lazy, cut corners, or make mistakes. They
also can do quick prototypes, and then forget to rewrite them later.  Well,
while I can't list mistakes in here, I can list prototype-like segments
of code that should be aggressively refactored.  This does not list
optimization issues, that needs to be done after intense profiling.

docs/examples/demo.php - ad hoc HTML/PHP soup to the extreme

AttrDef - a lot of duplication, more generic classes need to be created;
a lot of strtolower() calls, no legit casing
    Class - doesn't support Unicode characters (fringe); uses regular expressions
    Lang - code duplication; premature optimization
    Length - easily mistaken for CSSLength
    URI - multiple regular expressions; missing validation for parts (?)
    CSS - parser doesn't accept advanced CSS (fringe)
    Number - constructor interface inconsistent with Integer
Strategy
    FixNesting - cannot bubble nodes out of structures, duplicated checks
        for special-case parent node
    RemoveForeignElements - should be run in parallel with MakeWellFormed
URIScheme - needs to have callable generic checks
    mailto - doesn't validate emails, doesn't validate querystring
    news - doesn't validate opaque path
    nntp - doesn't constrain path

    vim: et sw=4 sts=4
