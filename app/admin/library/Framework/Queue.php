<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/3/25
 * Time: 下午12:42
 */

namespace Framework;


use Framework\DingTalk\DingTalkAlarm;
use Framework\QueueDrive\PersistentStorage;
use Fuel\Service\ExportJobSrv;
use Models\OilDownload;
use Models\OilJobs;
use SchedulerSDK\Machinery;

class Queue
{
    private $channel = 'default';

    private $config = NULL;

    private $serviceName = NULL;

    /**
     * 用户信息
     * @var null
     */
    public $userInfo = NULL;

    /**
     * job参数
     * @var
     */
    public $params;

    /**
     * job信息
     * @var
     */
    public $jobs;

    /**
     * jobId
     * @var
     */
    public $jobId;

    /**
     * 尝试次数，默认1次
     * @var int
     */
    protected $willTries = 1;

    /**
     * @var string
     */
    public $taskName = '';

    /**
     * Queue constructor.
     * @param array $params
     */
    public function __construct($params = [])
    {
        $this->setServiceName();
        if ($params) {
            $this->setParams($params);
        }
    }

    /**
     * @title
     * @desc
     * @param $params
     * @return $this
     * @package Framework
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function setParams($params = [])
    {
        $this->params = $params;

        return $this;
    }

    /**
     * @title
     * @desc
     * @return $this
     * <AUTHOR> @package Framework
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     */
    private function setServiceName()
    {
        $className = get_class($this);
        if (strpos($className, 'Jobs\\') !== FALSE) {
            $this->serviceName = $className;
        }

        return $this;
    }

    /**
     * @title
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package Framework
     * @since
     * @params  type filedName required?
     * @returns mixed
     */
    public function dispatch()
    {
        return $this->pushQueue();
    }

    /**
     * @title
     * @desc
     * @param $channel
     * @return $this
     * @package Framework
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function onQueue($channel)
    {
        $this->channel = $channel;

        return $this;
    }

    /**
     * @title   入队列
     * @desc
     * @return mixed
     * @returns
     * []
     * @returns
     * <AUTHOR> @package Framework
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     */
    public function pushQueue()
    {
        //G7WALLET-2000
        $task_sn = $redirect_url = $message = "";
        global $app;
        $_method = str_replace("Jobs\\", '', $this->serviceName);
        try {
            $res = [];
            //todo 由于需要导出人信息，如果无用户，则不请求导出中心，进行注册
            if (($this->userInfo && stripos(strtolower($this->serviceName), "export") !== false) &&
                ($app->config->exportJob->switch == 1 || in_array($_method, $app->config->exportJob->whiteList))) {
                $res = \Fuel\Service\ExportJobSrv::registerExportTask($this->serviceName, $this->params, $this->userInfo);
                if (!isset($res['task_sn']) || !isset($res['redirect_url']) || empty($res['task_sn']) || empty($res['redirect_url'])) {
                    throw new \RuntimeException("注册导出中心异常，请重试", 2);
                }
                $task_sn = $res['task_sn'];
                $redirect_url = $res['redirect_url'];
                $message = "export-job";

                if ($res['isApprove'] == 1) {
                    //兼容永久权限导出方式
                    $message .= "##" . json_encode(['approve_status' => 0]);
                }
            }

            //让导出的文件的任务与上传文件的任务产生关联
            if (stripos(strtolower($this->serviceName), "submitdownfile") !== false) {
                $message = isset($this->params['id']) ? $this->params['id'] : "";
            }
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode() > 0 ? $e->getCode() : 2);
        }

        try {
            $redis = $this->getQueueInstance();
            $queueArr = [
                'jobId' => $task_sn ? $task_sn : Helper::uuid(),
                'channel' => $this->channel,
                'task_name' => $this->taskName,
                'service' => $this->serviceName,
                'params' => $this->params,
                'tried' => 0,//已尝试次数
                'willTries' => $this->willTries,//默认尝试次数
                'userInfo' => $this->userInfo ? $this->userInfo : new \stdClass(),
            ];

            $taskId = NULL;
            //向数字能源，调度服务，下发任务 ,线上先开启导出
            //if ($app->config->scheduler->switch == 1 || in_array($_method, $app->config->scheduler->goQueueWhite) || stripos(strtolower($_method), "export") !== false) {
            if ( $app->config->scheduler->switch == 1 || in_array($_method, $app->config->scheduler->goQueueWhite) ) {
                $func_time = $_method;
                $queue = $this->channel;
                if (stripos(strtolower($_method), "export") !== false) {
                    $func_time = "ExportDataJob";
                    $queue = "export";
                }
                $job = [
                    'jobId' => $queueArr['jobId'],
                    'userInfo' => $this->userInfo ? $this->userInfo : new \stdClass(),
                    'params' => $this->params
                ];
                $taskId = $this->sendTask2Desp($func_time, $_method, $queue, $job);
            } else {
                $redis->LPUSH($this->channel, serialize($queueArr));
            }

            $dbJobs = [
                'job_id' => $queueArr['jobId'],
                'channel' => $queueArr['channel'],
                'type' => 20,
                'service_name' => $queueArr['service'],
                'data' => \serialize($queueArr['params']),
                'tried' => $queueArr['tried'],
                'tries' => $queueArr['willTries'],
                'task_name' => $queueArr['task_name'],
                'userInfo' => json_encode($queueArr['userInfo']),
            ];
            $jobs = OilJobs::add($dbJobs);

            OilDownload::add([
                'jobs_id' => $queueArr['jobId'],
                'channel' => $queueArr['channel'],
                'project' => $queueArr['task_name'],
                'rate' => 0,
                'createtime' => date("Y-m-d H:i:s"),
                'createuser' => $this->userInfo ? $this->userInfo->id : 1,
                'message' => $message,
                "task_id" => $taskId,
            ]);

        } catch (\Exception $e) {
            Log::error('pushQueueError:' . $e->getMessage() . '|' . strval($e), [], 'queueError');
            throw new \RuntimeException($e->getMessage(), $e->getCode() > 0 ? $e->getCode() : 2);
        }

        $queueArr['redirect_url'] = $redirect_url;
        return (object)$queueArr;
    }

    //下发任务到调度服务
    //由于每个功能的导出类名不同，统一映射导出到一个参数
    public function sendTask2Desp($func_name = "ExportDataJob", $className = "AddCardViceTradesJob", $queue = "addCardViceTradesJob", $params = [])
    {
        $machinerySDK = (new Machinery());
        try {
            $res = $machinerySDK->sendTask("normal", [
                'name' => "cliJob",
                'routing_key' => "gsp_fuel_" . $queue,
                'args' => [
                    [
                        'Name' => 'func_name',
                        'Type' => 'string',
                        'Value' => $func_name
                    ],
                    [
                        'Name' => 'class_name',
                        'Type' => 'string',
                        'Value' => json_encode(["instance_class_name" => $className])
                    ],
                    [
                        'Name' => 'params',
                        'Type' => 'string',
                        'Value' => json_encode($params)
                    ]
                ],
                'retry_count' => 3,
            ]);

            $result = \json_decode($res);
            if (!$result || !isset($result->status) || $result->status != 0 || !isset($result->data) || !$result->data->Signature->UUID) {
                Log::error(__METHOD__ . ":异常", [$res], 'sendTask2Desp');
                throw new \RuntimeException("下发任务失败", 505);
            }
            return $result->data->Signature->UUID;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ":异常", ['data' => $res, 'err' => strval($e)], 'sendTask2Desp');
            (new DingTalkAlarm())->alarmToGroup('下发任务失败', json_encode($res), [], true, false);
            throw new \RuntimeException($e);
        }
    }

    /**
     * @title   设置尝试次数
     * @desc
     * @param int $tries
     * @return $this
     * @package Framework
     * @since
     * @params  int tries   尝试次数
     * @version
     * @level 1
     * <AUTHOR>
    public function setTries($tries = 1)
    {
        if ($tries > 10) {
            throw new \RuntimeException('最大尝试次数不能超过10次', 2);
        }

        $this->willTries = $tries;

        return $this;
    }

    /**
     * @title
     * @desc
     * @param $userInfo
     * @return $this
     * @package Framework
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function setUserInfo($userInfo)
    {
        $this->userInfo = $userInfo;

        return $this;
    }

    public function setTaskName($taskName)
    {
        $this->taskName = $taskName;

        return $this;
    }

    /**
     * @title   设置jobs信息
     * @desc
     * @param $jobs
     * @return $this
     * @returns
     * []
     * @returns
     * @package Framework
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     * <AUTHOR>
    public function setJobs($jobs)
    {
        $this->jobs = $jobs;

        return $this;
    }

    /**
     * @title   获取job任务
     * @desc
     * @param string $channel
     * @returns
     * []
     * @returns
     * <AUTHOR> @package Framework
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     */
    public function getJob($channel = 'default')
    {

        $redis = $this->getQueueInstance(TRUE);
        while (TRUE) {
            try {
                $record = $redis->LPOP($channel);
                if ($record) {
                    $job = unserialize($record);
                    $job = (object)$job;
                    if (isset($job->service) && $job->service) {
                        if (isset($job->userInfo) && $job->userInfo) {
                            global $app;
                            $app->myAdmin = $job->userInfo;
                        }
                        $this->exec($job);
                    } else {
                        throw new \RuntimeException('job中无service,jobInfo:' . var_export($job, TRUE), 2);
                    }
                } else {
                    usleep(500);
                }
            } catch (\Exception $e) {
                Log::error('pushQueueError:' . $e->getMessage() . '|' . strval($e), [], 'queueError');
                if (stripos($e->getMessage(), "read error") !== false) {
                    (new DingTalkAlarm())->alarmToGroup("异步队列失败", $e->getMessage(), [], true, false);
                }
            }
        }
    }

    /**
     * @title   执行
     * @desc
     * @param $job
     * @returns
     * []
     * @returns
     * <AUTHOR> @package Framework
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     */
    private function exec($job)
    {
        $job->type = 20;
        if (isset($job->service) && $job->service) {
            if (isset($job->userInfo) && $job->userInfo) {
                global $app;
                $app->myAdmin = $job->userInfo;
            }

            $job->tried++;

            $isFailed = TRUE;

            try {
                if ($job->tried <= $job->willTries) {
                    $serviceObj = new \ReflectionClass($job->service);
                    $instance = $serviceObj->newInstance($job->params);
                    $instance
                        ->setJobs($job)
                        ->handle();
                    $isFailed = FALSE;
                } else {
                    throw new \RuntimeException('尝试次数已用完，仍然失败。jobID:' . $job->jobId, 2);
                }
            } catch (\Exception $e) {
                $isFailed = FALSE;
                $job->message = $job->message ? $job->message : $e->getMessage();
                $job->exception = $job->exception ? $job->exception : strval($e);

                if ($job->tried > $job->willTries) {
                    (new PersistentStorage())->create($job);
                    throw new \RuntimeException($e->getMessage(), $e->getCode());
                } else {
                    $this->exec($job);
                }
            } finally {
                if ($isFailed) {
                    $job->message = $job->message ? $job->message : '程序异常退出';
                    (new PersistentStorage())->create($job);
                } else {
                    // 删除成功的任务
                    OilJobs::deleteByJobId(['job_id' => $job->jobId]);
                    //todo 如果是导出任务，需要上传文件至导出中心
                    $downInfo = \Models\OilDownload::getFilterInfo(['jobs_id' => $job->jobId]);
                    if ($downInfo) {
                        if (stripos($downInfo->message, "export-job") !== false) {
                            (new \Jobs\SubmitDownFileJob(['id' => $downInfo->id, 'from' => 'export-finish']))
                                ->setTaskName('上传文件至导出中心')
                                ->onQueue('export_statistic')
                                ->setTries(3)
                                ->dispatch();
                        }
                    }
                }
            }
        } else {
            $job->message = 'job中无service';
            (new PersistentStorage())->create($job);
            throw new \RuntimeException('job中无service,jobInfo:' . var_export($job, TRUE), 2);
        }
    }

    /**
     * @title
     * @desc
     * @param bool $pConnect
     * @returns
     * []
     * @returns
     * <AUTHOR> @package Framework
     * @since
     * @params  type filedName required?
     * @version
     * @level 1
     */
    public function getQueueInstance($pConnect = FALSE)
    {
        if (!$this->config) {
            $config = Config::get('queue');
            if (!$config) {
                throw new \RuntimeException('未设置队列驱动', 2);
            }

            $this->config = $config;
        }

        $serviceObj = new \ReflectionClass('Framework\QueueDrive\\' . $config['driver']);
        $instance = $serviceObj->newInstance($this->config);

        return $instance
            ->connect($pConnect)->redisInstance;
    }

    /**
     * @param array $originParams
     * @return bool
     * 执行调度服务的任务
     */
    public function execJob($originParams = [])
    {
        $className = isset($originParams['instance_class_name']) && $originParams['instance_class_name'] ? $originParams['instance_class_name'] : "";
        if (empty($className)) {
            return false;
        }
        $spider_msg_type = isset($originParams['spider_msg_type']) && $originParams['spider_msg_type'] ? $originParams['spider_msg_type'] : "";

        $className = "\Jobs\\" . $className;

        $_tmpJob['jobId'] = isset($originParams['jobId']) && $originParams['jobId'] ? $originParams['jobId'] : "";
        $_tmpJob['spider_msg_type'] = $spider_msg_type;
        $params = $originParams['params'];
        if (isset($originParams['userInfo']) && $originParams['userInfo']) {
            global $app;
            $app->myAdmin = (object)$originParams['userInfo'];
        }

        $job = (object)$_tmpJob;
        try {
            $serviceObj = new \ReflectionClass($className);
            $instance = $serviceObj->newInstance($params);
            $instance
                ->setJobs($job)
                ->handle();
        } catch (\Exception $e) {
            Log::error('执行结果:', ["message" => $e->getMessage(), "code" => $e->getCode()], 'runSchedulerJob');
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        } finally {
            if ($job && !empty($job->jobId)) {
                // 删除成功的任务
                OilJobs::deleteByJobId(['job_id' => $job->jobId]);
                //todo 如果是导出任务，需要上传文件至导出中心
                $downInfo = \Models\OilDownload::getFilterInfo(['jobs_id' => $job->jobId]);
                if ($downInfo) {
                    if (stripos($downInfo->message, "export-job") !== false) {
                        (new \Jobs\SubmitDownFileJob(['id' => $downInfo->id, 'from' => 'export-finish']))
                            ->setTaskName('上传文件至导出中心')
                            ->onQueue('export_statistic')
                            ->setTries(3)
                            ->dispatch();
                    }
                }
            }
        }
        return true;
    }
}

