<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/6/24/024
 * Time: 15:45
 */

namespace Framework;

class Cache
{
    static protected $cacheObject;

    static protected $container = [];

    static private function init($type = '', $redisConfig='')
    {
        $cacheConfig = Config::get('cache.driver');
        $cacheType = $cacheConfig ? $cacheConfig : 'AliMemcached';
        $_cacheType = '\Framework\CacheDriver\\'.$cacheType;

        if(!class_exists($_cacheType)){
            throw new \RuntimeException($cacheType.' Driver does not supported!',3);
        }

        $key = sprintf('%s:%s', $type, $redisConfig);
        if (!isset(self::$container[$key])) {
            $obj = new $_cacheType($type, $redisConfig);
            self::$container[$key] = $obj;
        } else {
            $obj = self::$container[$key];
        }

//        if(!self::$cacheObject) {
//            self::$cacheObject = new $_cacheType($type, $redisConfig);
//        }

        return $obj;
    }

    static public function lock($key,$seconds)
    {
        return self::init()->lock($key,$seconds);
    }

    static public function unlock($key,$lockId)
    {
        return self::init()->unlock($key,$lockId);
    }

    /**
     * @param $key
     * @param $value
     * @param $seconds
     */
    static public function put($key,$value,$seconds)
    {
        return self::init()->put($key,$value,$seconds);
    }

    /**
     * 永久缓存
     * @param $key
     * @param $value
     */
    static public function forever($key, $value)
    {
        return self::init()->forever($key, $value);
    }

    /**
     * 取缓存
     * @param $key
     * @return mixed
     */
    static public function get($key)
    {
        return self::init()->get($key);
    }

    /**
     * @param $key
     * @return mixed
     */
    static public function forget($key)
    {
        return self::init()->forget($key);
    }

    /**
     * 缓存分组，仅支持redis
     * @param $tagName
     * @return mixed
     */
    static public function tag($tagName)
    {
        return self::init()->tag($tagName);
    }

    static public function getAll()
    {
        return self::init()->getAll();
    }

    static public function getInstance($redisConfig='')
    {
        return self::init('', $redisConfig)->getCacheInstance();
    }


}