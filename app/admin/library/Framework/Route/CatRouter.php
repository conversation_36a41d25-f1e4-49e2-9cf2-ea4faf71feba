<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/14/014
 * Time: 10:40
 */

namespace Framework\Route;

use helper;
use Framework\Log;

class CatRouter
{
    /**
     * 当前模块与方法数据信息
     * @var null
     */
    protected $moduleMethod = NULL;

    /**
     * 当前模块名
     * @var null
     */
    protected $currentModule = NULL;

    /**
     * 当前方法名
     * @var null
     */
    protected $currentMethod = NULL;

    /**
     * 当前数据返回格式
     * @var null
     */
    protected $currentFormat = NULL;

    /**
     * 当前请求方式
     * @var null
     */
    protected $currentRequestMethod = NULL;

    /**
     * All of the verbs supported by the router.
     *
     * @var array
     */
    public static $verbs = ['GET', 'HEAD', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'];

    /**
     * 路由池
     * @var array
     */
    protected $routes = [];

    protected $middleware = NULL;

    protected $prefix = 'default';

    public function __construct()
    {

    }

    public function getRoutes()
    {
        return $this->routes;
    }

    /**
     * 必要参数检测
     * @return bool
     */
    public function checkParams()
    {
        if (!isset($_REQUEST['method']) || !$_REQUEST['method']) {
            throw new \RuntimeException('method不能为空', 2);
        }

        $this->getRequestMethod();
        $this->getModuleMethod();
        $this->getMethod();
        $this->getModule();
        $this->getFormat();

        return TRUE;
    }

    /**
     * 调度
     */
    public function dispatcher()
    {
        $this->checkParams();
        $this->setModule();
        $this->parseParams();
    }


    /**
     * 设置默认请求信息
     */
    private function setModule()
    {
        switch (strtoupper($this->currentRequestMethod)) {
            case 'GET':
                $_GET['m'] = $this->currentModule;
                $_GET['f'] = $this->currentMethod;
                $_GET['t'] = $this->currentFormat;
                break;

            //framework中的route均按get获取，因此此处对m\f\t三个参数，按照get传入
            case 'POST':
                $_GET['m'] = $this->currentModule;
                $_GET['f'] = $this->currentMethod;
                $_GET['t'] = $this->currentFormat;
                break;

            default:
                $_POST['m'] = $this->currentModule;
                $_POST['f'] = $this->currentMethod;
                $_POST['t'] = $this->currentFormat;
        }
    }

    /**
     * 获取当前module和method的数组
     * @return array|null
     */
    private function getModuleMethod()
    {
        if (!$_REQUEST['method']) {
            throw new \RuntimeException('method不能为空', 2);
        }

        $actionArr = explode(".", trim($_REQUEST['method']));
        if(count($actionArr) == 4){
            $currentModuleMethod = $actionArr[0].'.'.$actionArr[2].'.'.$actionArr[3];
        }elseif (count($actionArr) == 3) {
            $currentModuleMethod = $_REQUEST['method'];
        }else{
            throw new \RuntimeException('method格式不正确', 2);
        }

        if(!isset($this->routes[$actionArr[0]][$this->currentRequestMethod][$currentModuleMethod]) && !isset
            ($this->routes[$actionArr[0]]['ANY'][$currentModuleMethod])) {
            throw new \RuntimeException('404，您请求的方法不存在', 404);
        }

//        $this->checkHttpAllow($actionArr[0]);

        $currentRoute = isset($this->routes[$actionArr[0]][$this->currentRequestMethod][$currentModuleMethod])
            ? $this->routes[$actionArr[0]][$this->currentRequestMethod][$currentModuleMethod]
            : $this->routes[$actionArr[0]]['ANY'][$currentModuleMethod];

        //1、首先找组，2、有需要的路由，3、查看组里是否有middleware，如有则执行
        if (isset($currentRoute['middleware']) && $currentRoute['middleware']) {
            $middlewareName = $currentRoute['middleware'];
            $middlewareClass = new \ReflectionClass('\Fuel\MiddleWare\\' . $middlewareName);//建立 Person这个类的反射类
            $instance = $middlewareClass->newInstance();//相当于实例化Person 类
            $instance->handle();

        }

        $this->moduleMethod = explode("@", $currentRoute['action']);

        return $this->moduleMethod;
    }

    private function checkHttpAllow($prefix)
    {
        if ((!isset($this->routes[$prefix][$this->currentRequestMethod][$_REQUEST['method']]) && !isset($this->routes[$prefix]['ANY'][$_REQUEST['method']])) || (!$this->routes[$prefix][$this->currentRequestMethod][$_REQUEST['method']] && !$this->routes[$prefix]['ANY'][$_REQUEST['method']])) {
            throw new \RuntimeException('不被允许的Http请求方式', 2);
        }

        return TRUE;
    }

    /**
     * 获取当前模块名
     * @return null
     */
    public function getModule()
    {
        if (!$this->currentModule) {
            $_moduleMethod = $this->getModuleMethod();
            $this->currentModule = $_moduleMethod[0];
        }

        return $this->currentModule;
    }

    /**
     * 获取当前方法名
     * @return null
     */
    public function getMethod()
    {
        if (!$this->currentMethod) {
            $_moduleMethod = $this->getModuleMethod();
            $this->currentMethod = $_moduleMethod[1];
        }

        return $this->currentMethod;
    }

    /**
     * 获取当前数据返回格式
     * @return null
     */
    public function getFormat()
    {
        if (!$this->currentFormat) {
            $this->currentFormat = isset($_REQUEST['format']) && $_REQUEST['format'] ? $_REQUEST['format'] : 'json';
        }

        return $this->currentFormat;
    }

    /**
     * 获取当前请求方式
     * @return null
     */
    public function getRequestMethod()
    {
        if (!$this->currentRequestMethod) {
            $this->currentRequestMethod = $_SERVER['REQUEST_METHOD'];
        }

        return $this->currentRequestMethod;
    }

    /**
     * 添加路由
     * @param $requestMethod
     * @param $uri
     * @param $action
     */
    public function addRoute($requestMethod, $uri, $action)
    {
        $_requestMethod = is_array($requestMethod) ? implode(",", $requestMethod) : $requestMethod;
        $splitUri = explode(".", $uri);

        if(count($splitUri) == 4){
            $_uri = $splitUri[0].'.'.$splitUri[2].'.'.$splitUri[3];
            $this->routes[$this->prefix][$_requestMethod][$_uri] = ['action' => $action, 'middleware' => $this->middleware];
        }else{
            $this->routes[$this->prefix][$_requestMethod][$uri] = ['action' => $action, 'middleware' => $this->middleware];
        }

    }

    public function get($uri, $action)
    {
        $this->addRoute('GET', $uri, $action);
    }

    public function post($uri, $action)
    {
        $this->addRoute('POST', $uri, $action);
    }

    public function any($uri, $action)
    {
        $this->addRoute('ANY', $uri, $action);
    }

    public function group(array $attr, Callable $callback)
    {
        if (isset($attr['prefix']) && $attr['prefix']) {
            $this->prefix = $attr['prefix'];
        }

        if (isset($attr['middleware']) && $attr['middleware']) {
            $this->middleware = $attr['middleware'];
        }
        $callback();
        $this->middleware = NULL;
        $this->prefix = 'default';

    }

    /**
     * 业务参数解包
     */
    private function parseParams()
    {
        $params = new \stdClass();

        if (isset($_REQUEST['data'])) {
            try{
                $params = json_decode(urldecode(trim($_REQUEST['data'])),TRUE);

                global $app;
                if(isset($app->data_from) && $app->data_from && !isset($params['data_from'])){
                    $params['data_from'] = $app->data_from;
                }

                if(strtolower('gasAgent.callbackCenter.resultNotify') == strtolower($_REQUEST['method'])){
                    Log::info('catRoute | json_decode | ',$params, 'CatRouter');
                }

                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \RuntimeException('JSON_FORMAT_ERROR', 3);
                }
            }catch(\Exception $e){
                Log::error('params-data JSON_FORMAT_ERROR | ERROR_CODE:'.$e->getCode(), ['Request'=>$_REQUEST,
                    'Exception'=>strval($e)],'CatRouter');
                throw new \RuntimeException($e->getMessage(), $e->getCode());
            }

            if (isset($params->page_size)) {
                $params->limit = $params->page_size;
                //unset($params->page_size);
            }
            if (isset($params->page_no)) {
                $params->page = $params->page_no;
                //unset($params->page_no);
            }
        }else{
            Log::warning('no data params',[],'CatRouter');
        }

        unset($_GET['sign'], $_GET['timestamp'], $_GET['app_key'], $_GET['method'], $_GET['data'], $_GET['format']);
        unset($_POST['sign'], $_POST['timestamp'], $_POST['app_key'], $_POST['method'], $_POST['data'], $_POST['format']);

        if(isset($_SERVER['HTTP_CONTENT_TYPE']) && strtolower($_SERVER['HTTP_CONTENT_TYPE']) == 'application/json' ){
            $_params = NULL;

            $_params = file_get_contents("php://input");

            if($_params && $params){
                $_tmpParams = json_decode($_params,true);
                if(isset($_tmpParams['data']) && $_tmpParams['data']){
                    $params = array_merge((array)$params, $_tmpParams['data']);
                }else{
                    $params = array_merge((array)$params, $_tmpParams);
                }
            }
        }

        switch (strtoupper($this->currentRequestMethod)) {
            case 'GET':
                $_GET = array_merge((array)$params, $_GET);
                break;

            case 'POST':
                $_POST = array_merge((array)$params, $_POST);
                break;

            default:
                $_POST = array_merge((array)$params, $_POST);
        }

        if(strtolower('gasAgent.callbackCenter.resultNotify') == strtolower($_REQUEST['method'])){
            Log::info('catRoute | parseParams | 进入业务层', [], 'CatRouter');
        }

    }


}