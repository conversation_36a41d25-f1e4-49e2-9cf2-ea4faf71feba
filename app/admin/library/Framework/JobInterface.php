<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-5-22
 * Time: 下午12:32
 */

namespace Framework;


interface JobInterface
{
//    public function __construct()
//    {
//        $this->job = Config::get('job.type');
//    }

    public function pushTask(callable $callBack);

    public function channel($channel);

    public function tries($tries = 0);

    public function delay($delay = 0);

    public function setTaskName($taskName);

    public function exec();

}