<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 2018/4/13
 * Time: 下午6:57
 */

namespace UCenterSDK;


class User extends UCenterAbstract
{
    public function __construct()
    {
        parent::__construct();
    }

    public function searchUserByUserName(array $params)
    {
        $restPath = '/v1/ucenter/User/searchUserByUserName';

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params)
            ->defaultHttpRequest();
    }

    public function commonPost(array $params)
    {
        $restPath = '/v1/ucenter'.$params['method'];

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params['data'])
            ->defaultHttpRequest();
    }

    //根据用户名精确搜索
    public function getUserByUserName(array $params)
    {
        $restPath = '/v1/ucenter/User/getUserByUserName';

        \helper::argumentCheck(['username'],$params);

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params)
            ->defaultHttpRequest();
    }

    //根据用户名精确搜索
    public function getUserByToken(array $params)
    {
        $restPath = '/v1/ucenter/User/getUserByToken';

        \helper::argumentCheck(['_TOKEN'],$params);

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params)
            ->defaultHttpRequest();
    }

    //获取用户详情
    public function getUserById(array $params)
    {
        $restPath = '/v1/ucenter/v1/user/detail';

        \helper::argumentCheck(['uid','appName','appVersion'],$params);

        return $this->setRestPath($restPath)
            ->setRequestType('GET')
            ->setParams($params)
            ->defaultHttpRequest();
    }

    //获取公司详情
    public function getCompanyByName(array $params)
    {
        $restPath = '/v1/ucenter/org/getOrgByName';

        \helper::argumentCheck(['keyword'],$params);

        return $this->setRestPath($restPath)
            ->setRequestType('GET')
            ->setParams($params)
            ->defaultHttpRequest();
    }

    //获取公司详情
    public function getOrgByCode(array $params)
    {
        $restPath = '/v1/ucenter/org/getOrgByCode';

        //\helper::argumentCheck(['orgroot'],$params);

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params)
            ->defaultHttpRequest();
    }

    /*
     * 更改机构信息
     *  orgcode	T文本	是       机构编码
     *  name	T文本	否       机构名称，1~50
     *  contact	T文本	否       联系人，长度50以内
     *  tel	T文本	否       联系电话，长度20以内，手机号码或者固定号码
     *  address	T文本	否       联系地址，成都100以内
     *  remark	T文本	否       备注，长度100以内
     *  appName	T文本	是
     *  appVersion	T文本	是
     */
    public function updateSubOrgan(array $params)
    {
        $restPath = '/v1/ucenter/v1/organ/updateSubOrgan';

        \helper::argumentCheck(['orgcode'],$params);

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params)
            ->defaultHttpRequest();
    }

    //创建机构
    public function createOrgan(array $params)
    {
        $restPath = '/v1/ucenter/organ/createOrgan';

        \helper::argumentCheck(['name','free','project'],$params);

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params)
            ->defaultHttpRequest();
    }

    //创建用户
    public function createUser(array $params)
    {
        $restPath = '/v1/ucenter/user/createUser';

        \helper::argumentCheck(['username','project','orgcode','passwd'],$params);

        $params['roleids'] = '';

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params)
            ->defaultHttpRequest();

    }

    //根据机构名称精确查询
    public function getOrganByName(array $params)
    {
        $restPath = '/v1/ucenter/organ/getOrganByName';

        //\helper::argumentCheck(['name'],$params);

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params)
            ->defaultHttpRequest();

    }

    //修改role的资源
    public function roleResourceUpdate(array $params)
    {
        $restPath = '/v1/ucenter/v1/role/update';

        \helper::argumentCheck(['addResourceIds','appName','appVersion','roleId'],$params);

        return $this->setRestPath($restPath)
            ->setRequestType('POST')
            ->setParams($params)
            ->defaultHttpRequest();

    }



}