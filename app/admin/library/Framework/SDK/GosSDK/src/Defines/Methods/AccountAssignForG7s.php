<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 2017/5/25
 * Time: 17:09
 */

namespace GosSDK\Defines\Methods;


class AccountAssignForG7s {


    /**
     * 下发分配订单
     */
    const SPLIT_ORDER = 'v1/assignOrder/store';

    /**
     * 生成分配单sn
     */
    const ADD_SN_ORDER = 'v1/assignOrder/createSn';

    /**
     * 更新分配单sn
     */
    const UP_SN_ORDER = 'v1/assignOrder/updateSn';

    /**
     * 下发分配订单
     */
    const CREATE_WX = 'v1/accountAssign/createForWx';

    /**
     * 按油量分配
     */
    const LITRE_CHARGE = 'v1/accountAssign/litreChargeCreate';

    /**
     * 获取储油账户可用余额（GasApp专用)
     */
    const GET_OIL_BALANCE = 'gasApp/cashaccount/getOilBalanceByOrg';

    /**
     * 获取分配单列表
     */
    const GET_LIST = 'v1/accountAssign/get_list';

    /**
     * 获取分配单附带分配详情
     */
    const GET_NEW_LIST = 'v1/accountAssign/get_list_with_detail';

    /**
     * 获取分配单详情
     */
    const GET_DETAIL = 'v1/accountAssign/show';

    /**
     * 获取分配单列表
     */
    const ASSIGN_EXPORT = 'v1/accountAssign/getAccountAssignExport';

    /**
     * 获取分配详情列表
     */
    const GET_ASSIGN_DETAIL = 'v1/accountAssign/getAssignDetail';
}