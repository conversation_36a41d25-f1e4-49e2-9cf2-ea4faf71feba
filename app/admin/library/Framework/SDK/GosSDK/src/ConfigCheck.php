<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-3-24
 * Time: 下午2:05
 */

namespace GosSDK;


class ConfigCheck
{
    static protected $checkArr = ['app_id', 'url', 'public_key','callbak_url'];

    static public function check(array $config = [])
    {
        if (!isset($config) || !$config) {
            throw new \RuntimeException('config异常', 2);
        }

        foreach (self::$checkArr as $v) {
            if (!isset($config[$v]) || !$config[$v]) {
                throw new \RuntimeException('config的' . $v . '配置异常', 2);
            }
        }
    }


}