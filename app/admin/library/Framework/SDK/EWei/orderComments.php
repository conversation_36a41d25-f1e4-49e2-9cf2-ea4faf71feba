<?php
/**
 * 客服工单
 * User: zlx66
 * Date: 2016/9/27/027
 * Time: 11:38
 */

namespace Framework\SDK\EWei;

use \Fuel\Request\EweiClient as EWeiClient;

class orderComments extends EWeiAbstract
{
    /**
     * 查看回复
     * @param array $params
     * @return mixed
     */
    public function show(array $params)
    {
        \helper::argumentCheck(['id'], $params);

        $id = $params['id'];
        unset($params['id']);

        return EweiClient::get([
            'method' => 'ticket_comments/' . $id,
            'data'   => $params
        ]);
    }

    /**
     * 工单回复列表
     * @param array $params
     * @return mixed
     */
    public function commentsList(array $params)
    {
        \helper::argumentCheck(['ticket_id'], $params);

        $ticket_id = $params['ticket_id'];
        unset($params['ticket_id']);

        return EweiClient::get([
            'method' => 'tickets/'.$ticket_id.'/ticket_comments',
            'data'   => $params
        ]);
    }

    /**
     * 公开的工单回复列表
     * @param array $params
     * @return mixed
     */
    public function publicCommentsList(array $params)
    {
        \helper::argumentCheck(['ticket_id'], $params);

        $ticket_id = $params['ticket_id'];
        unset($params['ticket_id']);

        return EweiClient::get([
            'method' => 'tickets/'.$ticket_id.'/ticket_comments/public',
            'data'   => $params
        ]);
    }

    /**
     * 工单回复
     * @param array $params
     * @return mixed
     */
    public function addComment(array $params)
    {
        \helper::argumentCheck(['open', 'user', 'ticket'], $params);
        
        if(!is_array($params['ticket']) || !isset($params['ticket']['id'])  || !isset($params['ticket']['status'])){
            throw new \RuntimeException('ticket参数格式不正确', 2);
        }
        $ticket_id = $params['ticket']['id'];

        return EweiClient::post([
            'method' => 'tickets/'.$ticket_id.'/ticket_comments',
            'data'   => $params
        ]);
    }

    /**
     * 客户工单列表
     * @param array $params
     * @return mixed
     */
    static public function customerOrderList(array $params)
    {
        \helper::argumentCheck(['customer_id'], $params);

        $customer_id = $params['customer_id'];
        unset($params['customer_id']);

        return EweiClient::get([
            'method' => 'customers/' . $customer_id . '/tickets',
            'data'   => $params
        ]);
    }

    /**
     * 客服工单列表
     * @param array $params
     * @return mixed
     */
    static public function engineersOrderList(array $params)
    {
        \helper::argumentCheck(['engineer_id'], $params);

        $engineer_id = $params['engineer_id'];
        unset($params['engineer_id']);

        return EweiClient::get([
            'method' => 'engineers/' . $engineer_id . '/tickets',
            'data'   => $params
        ]);
    }

}