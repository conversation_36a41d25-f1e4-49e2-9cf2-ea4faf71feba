<?php
/**
 * 客服工单
 * User: zlx66
 * Date: 2016/9/27/027
 * Time: 11:38
 */

namespace Framework\SDK\EWei;

use \Fuel\Request\EweiClient as EWeiClient;

class orderType extends EWeiAbstract
{
    /**
     * 获取工单类型列表
     * @param array $params
     * @return mixed
     */
    public function getList(array $params=[])
    {
        return EweiClient::get([
            'method' => 'ticket_types',
            'data'   => [
                '_page' =>  1,
                '_count'    =>  100
            ]
        ]);
    }

}