<?php


namespace G7\FeiShu\Service\Inter;


interface FeiShuApprovalInterface
{

    /**
     * 查看审批定义
     *
     * @return mixed
     */
    public function GetApprovalDefine();

    /**
     * 创建审批实例
     *
     * @param $data
     * @return mixed
     */
    public function CreateApproval(array $data);

    /**
     * 获取审批实例详情
     *
     * @param $instance_code
     * @return mixed
     */
    public function GetApprovalDetail($instance_code);


    /**
     * 审批实例上传文件
     *
     * @param $data
     * @return mixed
     */
    public function Upload(array $data);
}