<?php
namespace G7\FeiShu\Service;

use G7\FeiShu\Service\Inter\FeiShuUserInterface;
use RuntimeException;
class UserService extends BaseService implements FeiShuUserInterface
{

    public function __construct($cacheInstance, $configFilePath = '')
    {
        parent::__construct($cacheInstance, $configFilePath);
    }
    /**
     * 根据邮箱获取用户的userId openId
     * 权限开启否则返回的字段中没有userId
     * @param $email
     * @return mixed
     */
    public function GetUserId($email)
    {
        if(empty($email))
        {
            throw new RuntimeException('邮箱不能为空', 2001);
        }
        $accessToken=$this->GetTenantAccessToken();

        $apiParams = [
            'emails' => $email,
        ];
        $result=$this->request($this->config->get('api.getUserId.uri'),$apiParams, $this->config->get('api.getUserId.method'),$accessToken);

        if(isset($result['data']['email_users'][$email]) && !empty($result['data']['email_users'][$email]))
        {
            if(isset($result['data']['email_users'][$email][0]['user_id']) && !empty($result['data']['email_users'][$email][0]['user_id']))
            {
                $user_id=$result['data']['email_users'][$email][0]['user_id'];
                $open_id=$result['data']['email_users'][$email][0]['open_id'];
            }else{
                throw new RuntimeException('通讯录读取权限未开启', 2002);
            }
        }else{
            throw new RuntimeException($email.'邮箱不存在', 2003);
        }
        return [
            'user_id' => $user_id,
            'open_id' => $open_id,
        ];
    }

    /**
     * 获取用户详情
     * 参数 email G7邮箱
     *
     * @param $email
     * @return mixed
     */
    public function GetUserInfo($email)
    {
        if(empty($email))
        {
            throw new RuntimeException('参数邮箱不能为空', 2001);
        }
        $result=$this->GetUserId($email);
        $accessToken=$this->GetTenantAccessToken();
        $apiParams = [
            'user_id_type' => 'user_id'
        ];
        $routeParam='/'.$result['user_id'];
        return $this->request($this->config->get('api.getUserInfo.uri'),$apiParams, $this->config->get('api.getUserInfo.method'),$accessToken,[],$routeParam);
    }

    /**
     * 获取用户详情
     * 参数 email G7邮箱
     *
     * @param $user_id
     * @return mixed
     */
    public function GetUserInfoByUserId($user_id)
    {
        if(empty($user_id))
        {
            throw new RuntimeException('user_id不能为空', 2001);
        }
        $accessToken=$this->GetTenantAccessToken();
        $apiParams = [
            'user_id_type' => 'user_id'
        ];
        $routeParam='/'.$user_id;
        return $this->request($this->config->get('api.getUserInfo.uri'),$apiParams, $this->config->get('api.getUserInfo.method'),$accessToken,[],$routeParam);
    }
}