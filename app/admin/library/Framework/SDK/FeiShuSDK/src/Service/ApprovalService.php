<?php


namespace G7\FeiShu\Service;

use G7\FeiShu\Service\Inter\FeiShuApprovalInterface;
use RuntimeException;
use G7\FeiShu\Validate\BaseValidator;
class ApprovalService extends BaseService implements FeiShuApprovalInterface
{

    public $cacheInstance;

    public function __construct($cacheInstance, $configFilePath = '')
    {
        $this->cacheInstance=$cacheInstance;
        parent::__construct($cacheInstance, $configFilePath);
    }
    /**
     * 获取审批定义
     * 暂不支持定义时审批人为发起人自选模式
     * 返回的定义的form如下
     *"[{\"id\":\"widget16329865493940001\",\"name\":\"充值金额\",\"type\":\"input\"},
     * {\"id\":\"widget16329865609550001\",\"name\":\"充值事由\",\"type\":\"input\"},
     * {\"id\":\"widget16329865764580001\",\"name\":\"附件\",\"type\":\"attachmentV2\"}]"
     *
     * @return mixed
     */
    public function GetApprovalDefine()
    {
        $accessToken=$this->GetTenantAccessToken();
        $apiParams = [
            'approval_code' => $this->config->get('approval_code'),
            "locale"        => "zh_cn",
        ];
        return $this->request($this->config->get('api.getApprovalDefine.uri'),$apiParams, $this->config->get('api.getApprovalDefine.method'),$accessToken);
    }
    /**
     * 创建审批实例
     * email 对应G7的邮箱
     * form  调用审批定义接口获取 形式如下 id  type要与审批定义一致 value为foss工单对应传递的值
     * "[{\"id\":\"widget16329865493940001\",\"type\":\"input\",\"value\":\"ceshi\"},
     * {\"id\":\"widget16329865609550001\",\"type\":\"input\",\"value\":\"ceshi\"},
     * {\"id\":\"widget16329865764580001\",\"type\":\"attachmentV2\",value:"附件的值为上传附件接口后返回的code值"}]"
     * 暂不支持审批人为发起人自选模式创建审批实例
     *
     * 返回创建的实例的code
     * {"code":0,"data":{"instance_code":"1CD27886-F9A4-4971-B1C9-09398D82A7B2"},"msg":""}
     * @param $data
     * @return mixed
     */
    public function CreateApproval(array $data)
    {
        (new BaseValidator())->validate(['email','form'],$data);
        $accessToken=$this->GetTenantAccessToken();
        $user= (new UserService($this->cacheInstance))->GetUserId($data['email']);
        $apiParams = [
            'approval_code' => $this->config->get('approval_code'),
            "user_id"        => $user['user_id'],
            "open_id"        => $user['open_id'],
            "form"           => $data['form'],
        ];
        return $this->request($this->config->get('api.createApproval.uri'),$apiParams, $this->config->get('api.createApproval.method'),$accessToken);
    }
    /**
     * 获取审批实例详情
     * 根据instance_code获取审批实例详情
     *
     * @param $instance_code
     * @return mixed|void
     */
    public function GetApprovalDetail($instance_code)
    {
        if(empty($instance_code))
        {
            throw new RuntimeException('审批实例code不能为空', 2002);
        }
        $accessToken=$this->GetTenantAccessToken();
        $apiParams = [
            "instance_code"        => $instance_code,
        ];
        return $this->request($this->config->get('api.getApprovalDetail.uri'),$apiParams, $this->config->get('api.getApprovalDetail.method'),$accessToken);
    }
    /**
     * 创建审批实例上传文件
     * name  文件名 需包含文件扩展名，如“文件.doc”
     * type  值 image 或 attachment
     * filepath  文件路径
     *
     *
     * 返回
     *"code": "649D1CD2-9460-41A8-BA15-360DE89DE909", 创建审批实例时form type为文件需传入该code值
     *"url": "https://lf3-approval-sign.bytetos.com/lark-approval-attachment/attachment/20211012/7013295227142275074/16fb1ba4-2a02-4bfe-9b4f-cc21b25e6ccf.xlsx?x-expires=1634063194&x-signature=Y3dBjRN%2BmsQqJGJTFOHRtZeRlPU%3D#/obj/obj.xlsx"
     *
     * @param $data
     * @return mixed|void
     */
    public function Upload(array $data)
    {
        (new BaseValidator())->validate(['name','type','filepath'],$data);
        $accessToken=$this->GetTenantAccessToken();
        $header=[
            'Content-Type' =>  'multipart/form-data'
        ];
        $apiParams = [
            [
                'name'  => 'name',
                'contents' => $data['name']
            ],
            [
                'name'  => 'type',
                'contents' => $data['type']
            ],
            [
                'name'  => 'content',
                'contents' => fopen($data['filepath'],'r')
            ],
        ];
        return $this->request($this->config->get('api.upload.uri'),$apiParams, $this->config->get('api.upload.method'),$accessToken,$header,'',true);
    }

}