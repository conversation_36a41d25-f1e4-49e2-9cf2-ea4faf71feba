<?php


namespace G7\FeiShu\Adapter;


use <PERSON><PERSON><PERSON><PERSON>\Config;

class Log
{
    public static function Info($msg, $data)
    {
        self::write('info', $msg, $data);
    }
    
    public static function Debug($msg, $data)
    {
        self::write('debug', $msg, $data);
    }
    
    public static function Error($msg, $data)
    {
        self::write('error', $msg, $data);
    }
    
    private static function write($type, $msg, $data)
    {
        $config = new Config(dirname(__DIR__) . DIRECTORY_SEPARATOR . 'Config');
        
        $path      = $config->get("log.logPath");
        $logLevels = $config->get('log.level');
        
        if (!$logLevels || !in_array(strtolower($type), explode(',', $logLevels))) {
            return;
        }
        if (!file_exists($path)) {
            @mkdir($path, 0777, TRUE);
        }
        
        $content = [
            'timestamp'   => date('Y-m-d H:i:s'),
            'level'       => strtoupper($type),
            'msg'         => $msg,
            'data'        => $data,
            'request_uri' => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '',
        ];
        
        
        file_put_contents($path . date("ymd") . ".log", json_encode($content, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
    }
}