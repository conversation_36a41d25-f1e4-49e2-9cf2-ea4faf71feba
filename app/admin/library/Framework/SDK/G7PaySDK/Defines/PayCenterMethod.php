<?php
/**
*  PayCenterMethod.php
* $Author: 刘培俊 (liu<PERSON><EMAIL>) $
* $Date: 2019/12/3 16:04 $
* CreateBy Phpstorm
*/

namespace G7Pay\Defines;


class PayCenterMethod
{
    /**
     * 获取充值订单
     * /cashdesk-oil/v2/oilAccess/rechargeOrder/{extID}
     */
    const GET_RECHARGE_ORDER = [
        'path'          => '/cashdesk-oil/v2/oilAccess/rechargeOrder',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [],
        ]
    ];

    /**
     * 获取电子账单
     * /cashdesk-oil/v2/oilAccess/rechargeOrder/{extID}/electronicBill
     */
    const GET_ELECTRONIC_Bill = [
        'path'          => '/cashdesk-oil/v2/oilAccess/rechargeOrder',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [
                'optional' => [
                    'returnPic' => 'boolean', // 是否需要返回图片
                ],
            ],
            'body'  => [],

        ]
    ];

    /**
     * 生成充值订单
     * /cashdesk-oil/v2/oilAccess/rechargeOrders
     */
    const GENERATE_RECHARGE_ORDER = [
        'path'          => '/cashdesk-oil/v2/oilAccess/rechargeOrders',
        'requestMethod' => 'POST',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'required'  =>  [
                    'amount'    =>  'integer(uint64)@[1,]',// 充值金额
                    'bankTransType'    =>  'string{ONLINE,OFFLINE}enum.BankTransType',// 交易方式：ONLINE 在线支付；OFFLINE 线下汇款
                    'extID'    =>  'string@[1,64]',// 业务单号
                    'merchantID'    =>  'string(uint64)@[1,]',// 油品收款商户ID
                    'notifyUrl'    =>  'string@[1,]',// 支付结果通知地址
                    'platformOperatorID'    =>  'string(uint64)@[1,]',// 付款运营商ID
                    'transBusinessEntry'    =>  'string{TRANS, TRANS_PRE_CHARGE, ACCOUNT_SETTLE}enum.TransBusinessEntry',// 交易业务类型：TRANS_PRE_CHARGE 预付款充值(收款到预充值账户)；ACCOUNT_SETTLE 账期结算(收款到待结算账户)
                ],
                'optional' => [
                    'receiptSummary' => 'string@[0,200]', // 回单摘要：线下汇款必填
                ]
            ],
        ]
    ];

    /**
     * 支付充值订单
     * /cashdesk-oil/v2/oilAccess/rechargeOrders/{transID}
     */
    const PAY_RECHARGE_ORDER = [
        'path'          => '/cashdesk-oil/v2/oilAccess/rechargeOrders',
        'requestMethod' => 'PUT',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'required'  =>  [
                    'payBankAccountNo' =>  'string@[1,32]',// 支付银行卡号
                    'receiveBankAccountNo' =>  'string@[1,32]',// 收款银行卡号
                ],
            ],

        ]
    ];

    /**
     * 获取充值订单
     * /gw/payAudit/api/v0/bankTransFlow/list
     */
    const GET_BANK_RECORD = [
        'path'          => '/gw/payAudit/api/v0/bankTransFlow/list',
        'requestMethod' => 'POST',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'optional'  =>  [
                    'account' =>  'string@[1,32]',// 账号
                    'beginTime' =>  'string@[1,32]',// 开始日期
                    'endTime' =>  'string@[1,32]',// 结束日期
                    'current' => 'integer(int32)@[0,**********] = 0',//偏移,默认为0
                    'size' => 'integer(int32)@[0,50] = 100',//分页大小，默认为10
                ],
            ],
        ]
    ];

    /**
     * 获取银行流水
     * /gw/payAudit/api/v0/bankTransFlow/list
     */
    const GET_SELF_BANK_RECORD = [
        'path'          => '/gw/payment-center/bank/bankAccount/transFlow',
        'requestMethod' => 'POST',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'optional'  =>  [
                    'account' =>  'string@[1,32]',// 账号
                    'startDate' =>  'string@[1,32]',// 开始日期
                    'endDate' =>  'string@[1,32]',// 结束日期
                    'page' => 'integer(int32)@[0,**********] = 0',//偏移,默认为0
                    'size' => 'integer(int32)@[0,50] = 100',//分页大小，默认为10
                ],
            ],
        ]
    ];

}