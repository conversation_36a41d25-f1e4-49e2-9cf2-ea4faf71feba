<?php
/**
 *  MerchantCenterMethod.php
 * $Author: 刘培俊 (l<PERSON><PERSON><PERSON><PERSON>@g7.com.cn) $
 * $Date: 2019/12/3 16:04 $
 * CreateBy Phpstorm
 * 接口签名地址：https://weconnect.feishu.cn/file/boxcnzrTuHBed632F472p4gijWb
 */

namespace G7Pay\Defines;


class MerchantCenterMethod
{
    /**
     * 商户开户
     */
    const CREATE = [
        'path'          => '/cashdesk-unified-pay/v2/oilAccess/merchants',
        'requestMethod' => 'POST',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'required' => [
                    'merchanInfo' => [
                        'merchantName' => 'string@[0,100]',// 企业名称
                        'certificateCode'    => 'string@[0,32] = ""', // 证件号码(统一社会信用代码)
                        'certificateType'    => 'string{UNITY_SOCIAL_CREDIT_CODE}enum.CertificateType',// 证件类型(和证件号码同时存在) 统一社会信用代码：UNITY_SOCIAL_CREDIT_CODE
                        'openType'           => 'string{GENERAL, OPERATOR }enum.OpenType',// 开户类型(普通商户-油站等/运营商-油品子公司)
                        'operatorMerchantID' => 'string(uint64)@[1,19] = ""',// 平台运营商ID(付款关系关联-运营商开户不需要)
                        'needOpenEasSupplier' => 'booleanstring{FALSE,TRUE} = FALSE',// 是否需要在 EAS 创建供应商
                    ],
                    'cardInfo' => [
                        'accountName' => 'string@[0,100]',// 账户名(与企业名称一致)(绑卡必填)
                        'bankCode' => 'string@[0,32]',// 开户行联行号(绑卡必填)
                        'bankName' => 'string@[0,100]',// 开户行名称
                        'cardNo' => 'string@[0,50]',// 银行账号(绑卡必填)
                    ]
                ],
                'optional' => [
                    'cardInfo' => [
                        'bankCity' => 'string@[0,32]',// 开户所在地区(招商银行线上付款运营商请填写下述支持值)招行支持值：北京,离岸分行,总行离岸中心,广州,上海,天津,重庆,沈阳,南京,武汉,成都,西安,太原,郑州,石家庄,唐山,大连,长春,哈尔滨,呼和浩特,银川,苏州,青岛,宁波,合肥,济南,杭州,温州,福州,泉州,无锡,南通,烟台,东莞,南宁,西宁,长沙,深圳,佛山,南昌,贵阳,昆明,海口,乌鲁木齐,厦门,兰州,香港,总行
                    ]
                ]
            ],
        ]
    ];
    
    /**
     * 通过商户ID查询信息
     */
    const GET_INFO_BY_ID = [
        'path'          => '/cashdesk-unified-pay/v2/oilAccess/merchants',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [],
        ]
    ];
    
    /**
     * 通过商户ID查询信息
     */
    const GET_INFO_BY_NAME = [
        'path'          => '/cashdesk-unified-pay/v2/oilAccess/merchant',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [
                'required' => [
                    'merchantName'    =>  'string@[0,100]',
                ]
            ],
            'body'  => [],
        ]
    ];
    
    /**
     * 绑卡（同一商户绑定多张银行卡）
     * /cashdesk-unified-pay/v2/oilAccess/merchants/{merchantID}/cards
     */
    const BIND_BANK_CARD = [
        'path'          => '/cashdesk-unified-pay/v2/oilAccess/merchants',
        'requestMethod' => 'POST',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'required' => [
                    'accountName' => 'string@[0,100]',// 账户名(与企业名称一致)(绑卡必填)
                    'bankCode' => 'string@[0,32]',// 开户行联行号(绑卡必填)
                    'cardNo' => 'string@[0,50]',// 银行账号(绑卡必填)
                ],
                'optional' => [
                    'bankCity' => 'string@[0,32]',// 开户所在地区(招商银行线上付款运营商请填写下述支持值)招行支持值：北京,离岸分行,总行离岸中心,广州,上海,天津,重庆,沈阳,南京,武汉,成都,西安,太原,郑州,石家庄,唐山,大连,长春,哈尔滨,呼和浩特,银川,苏州,青岛,宁波,合肥,济南,杭州,温州,福州,泉州,无锡,南通,烟台,东莞,南宁,西宁,长沙,深圳,佛山,南昌,贵阳,昆明,海口,乌鲁木齐,厦门,兰州,香港,总行
                    'bankName' => 'string@[0,100]',// 开户行名称
                ]
            ],
        ]
    ];
    
    /**
     * 取银行账户信息
     * /cashdesk-unified-pay/v2/oilAccess/operator/{platformOperatorID}/bankCard/{bankCardNo}
     */
    const GET_BANK_ACCOUNT_INFO = [
        'path'          => '/cashdesk-unified-pay/v2/oilAccess/operator',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => []
        ]
    ];
    
    /**
     * 绑卡信息更新
     * /cashdesk-unified-pay/v2/oilAccess/merchants/{merchantID}/bankCard/{bankCardNo}
     */
    const UPDATE_BIND_BANK_CARD = [
        'path'          => '/cashdesk-unified-pay/v2/oilAccess/merchants',
        'requestMethod' => 'PUT',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'required' => [
                    'accountName' => 'string@[0,100]',// 账户名(与企业名称一致)(绑卡必填)
                    'bankCode' => 'string@[0,32]',// 开户行联行号(绑卡必填)
                    'bankName' => 'string@[0,100]',// 开户行名称
                ],
                'optional' => [
                    'bankCity' => 'string@[0,32]',// 开户所在地区(招商银行线上付款运营商请填写下述支持值)招行支持值：北京,离岸分行,总行离岸中心,广州,上海,天津,重庆,沈阳,南京,武汉,成都,西安,太原,郑州,石家庄,唐山,大连,长春,哈尔滨,呼和浩特,银川,苏州,青岛,宁波,合肥,济南,杭州,温州,福州,泉州,无锡,南通,烟台,东莞,南宁,西宁,长沙,深圳,佛山,南昌,贵阳,昆明,海口,乌鲁木齐,厦门,兰州,香港,总行
                ]
            ],
        ]
    ];
    
    /**
     * 商户注销
     * /cashdesk-unified-pay/v2/oilAccess/merchant/{merchantID}/close
     * /cashdesk-oil/v2/oilAccess/merchant/{merchantID}/close
     */
    const CLOSE_MERCHANT = [
        'path'          => '/cashdesk-oil/v2/oilAccess/merchant',
        'requestMethod' => 'PUT',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [],
        ]
    ];
    
    /**
     * 商户解绑卡
     * /cashdesk-unified-pay/v2/oilAccess/merchants/{merchantID}/cards/{bankAccountID}
     * /cashdesk-oil/v2/oilAccess/merchants/{merchantID}/cards/{bankAccountID}
     */
    const UN_BIND_CARD = [
        'path'          => '/cashdesk-unified-pay/v2/oilAccess/merchants',
        'requestMethod' => 'PUT',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [],
        ]
    ];
}