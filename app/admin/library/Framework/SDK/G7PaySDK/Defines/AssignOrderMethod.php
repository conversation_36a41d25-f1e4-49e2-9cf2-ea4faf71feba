<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-10-18
 * Time: 上午11:08
 */

namespace G7Pay\Defines;


class AssignOrderMethod
{
    /**
     * 直接分配
     */
    const CARD_ALLOC = [
        'path'          => '/cashdesk-oil/v0/user/0/cardAlloc',
        'requestMethod' => 'POST',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'=>[
                'required' => [
                    'extID' => 'string@[1,32]',
                    'totalAmount' => 'integer(int64)@[1,9223372036854776000]',
                    'allocDetail' => 'string'
                ]
            ]
        ]
    ];

    /**
     * 预分配申请
     */
    const CARD_ALLOC_RESERVE = [
        'path'          => '/cashdesk-oil/v0/user/0/cardAlloc/0/reserve',
        'requestMethod' => 'POST',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'=>[
                'required' => [
                    'extID' => 'string@[1,32]',
                    'totalAmount' => 'integer(int64)@[1,9223372036854776000]',
                    'allocDetail' => 'string'
                ]
            ]
        ]
    ];

    /**
     * 预分配失败
     */
    const FAIL = [
        'path'          => '/cashdesk-oil/v0/user/0/cardAlloc/0/reserve/fail',
        'requestMethod' => 'PUT',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'=>[
                'required' => [
                    'billID' => 'string(uint64)@[1,32]',
                    'totalAmount' => 'integer(int64)@[1,9223372036854776000]'
                ]
            ]
        ]
    ];

    /**
     * 预分配成功
     */
    const SUCCESS = [
        'path'          => '/cashdesk-oil/v0/user/0/cardAlloc/0/reserve/success',
        'requestMethod' => 'PUT',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'=>[
                'required' => [
                    'billID' => 'string(uint64)@[1,32]',
                    'totalAmount' => 'integer(int64)@[1,9223372036854776000]'
                ]
            ]
        ]
    ];

    /**
     * 分配撤销
     */
    const REVOKE = [
        'path'          => '/cashdesk-oil/v0/user/0/cardAlloc/0/revoke',
        'requestMethod' => 'PUT',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'=>[
                'required' => [
                    'billID' => 'string(uint64)@[1,32]',
                    'totalAmount' => 'integer(int64)@[1,9223372036854776000]'
                ],
                'optional' => [
                    'comment' => 'string@[0,1024] = ""'
                ]
            ]
        ]
    ];

    const GET_LIST = [
        'path' => '/cashdesk-oil/v0/user/0/cardAlloc',
        'requestMethod' => 'GET',
        'fields' => [
            'url' => [

            ],
            'query' => [
                'optional' => [
                    'billID' => 'string(uint64)@[0,19] = "0"',//支付平台充值单据号
                    'extID' => 'string@[0,32] = ""',//外部单据号
                    'subAccountID' => 'string(uint64)@[0,19] = "0"',//支付平台机构子账户ID
                    'cardSubAccountID' => 'string(uint64)@[0,19] = 0',//偏移,默认为0
                    'offset' => 'integer(int32)@[0,**********] = 0',//偏移,默认为0
                    'size' => 'integer(int32)@[0,50] = 10',//分页大小，默认为10
                    'createTimeStart' => 'string(date-time) = ""',//创建起始时间
                    'createTimeEnd' => 'string(date-time) = ""',//创建截止时间
                ]
            ],
            'body' => [

            ],
        ]
    ];
}