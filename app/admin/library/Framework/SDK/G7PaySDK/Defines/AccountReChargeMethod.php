<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-10-18
 * Time: 上午11:11
 */

namespace G7Pay\Defines;


class AccountReChargeMethod
{
    /**
     * 现金账户充值
     */
    const RECHARGE_TO_FOSS = [
        'path' => '/cashdesk-oil/v0/user/0/g7payRecharge',
        'requestMethod' => 'POST',
        'fields' => [
            'url' => [],
            'query' => [],
            'body' => [
                'required' => [
                    'amount' => 'integer(int64)@[1,9223372036854776000] =',//金额
                    'extID' => 'string@[1,32] = ""',//外部单据号
                    'subAccountID' => 'string(uint64)@[1,19]',//支付平台机构子账户ID
                ],
                'optional' => [
                    'cashAmount' => 'integer(int64)@[0,9223372036854776000] = 0',//充值油量子账户时使用，记录购油成本
                    'comment' => 'string@[1,1024]',//充值备注
                ]
            ],
        ]
    ];

    /**
     * 获取充值记录
     */
    const GET_LIST = [
        'path' => '/cashdesk-oil/v0/user/0/g7payRecharge',
        'requestMethod' => 'GET',
        'fields' => [
            'url' => [

            ],
            'query' => [
                'optional' => [
                    'billID' => 'string(uint64)@[0,19] = "0"',//支付平台充值单据号
                    'extID' => 'string@[0,32] = ""',//外部单据号
                    'subAccountID' => 'string(uint64)@[0,19] = "0"',//支付平台机构子账户ID
                    'offset' => 'integer(int32)@[0,**********] = 0',//偏移,默认为0
                    'size' => 'integer(int32)@[0,50] = 10',//分页大小，默认为10
                    'createTimeStart' => 'string(date-time) = ""',//创建起始时间
                    'createTimeEnd' => 'string(date-time) = ""',//创建截止时间
                ]
            ],
            'body' => [

            ],
        ]
    ];

    /**
     * 撤销充值
     */
    const REVOKE = [
        'path' => '/cashdesk-oil/v0/user/0/g7payRecharge/0/revoke',
        'requestMethod' => 'PUT',
        'fields' => [
            'url' => [],
            'query' => [],
            'body' => [
                'required' => [
                    'amount' => 'integer(int64)@[1,9223372036854776000] =',//金额(充值单金额)
                    'billID' => 'string(uint64)@[1,19]',//支付平台充值单
                ],
                'optional' => [
                    'comment' => 'string@[1,1024]',//充值备注
                ]
            ],
        ]
    ];
}