<?php
/**
 * Created by PhpStorm.
 * User: kevin
 * Date: 17-10-18
 * Time: 上午11:08
 */

namespace G7Pay\Defines;


class CardMethod
{
    /**
     * 卡片创建
     */
    const CREATE = [
        'path'          => '/cashdesk-oil/v0/user/0/cardType/0/card',
        'requestMethod' => 'POST',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'required' => [
                    'cardMerchantID' => 'string(uint64)@[1,19] = ""',
                    'cardNo'         => 'string@r/^[0-9A-Za-z-]{1,64}$/ = ""',
                    'cardTypeID'     => 'string(uint64)@[1,19] = ""',
                    'orgCode'        => 'string@[1,32] = ""',
                ],
                'optional' => [
                    'merchantID' => 'string(uint64)@[0,19] = "0"',
                ],
            ],
        ],
    ];

    /**
     * 多卡片创建
     */
    const CREATE_MUlTI = [
        'path'          => '/cashdesk-oil/v1/user/0/cardType/0/card',
        'requestMethod' => 'POST',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'required' => [
                    'cardInfo'     => 'string(uint64)@[1,19] = ""',
                    'orgCode'        => 'string@[1,32] = ""',
                ],
                'optional' => [
                    'merchantID' => 'string(uint64)@[0,19] = "0"',
                ],
            ],
        ],
    ];

    /**
     * 查询卡片列表
     */
    const GET_LIST = [
        'path'          => '/cashdesk-oil/v0/user/0/cardType/0/card',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [
                'required'  =>  [
                    'orgCode'   =>  'string@[1,32]',
                ],
                'optional'  =>  [
                    'merchantID'    =>  'string(uint64)@[0,19] = "0"',//机构所属运营商ID(中启行业务可以不传，撬装业务必传)
                ]
            ],
            'body'  => [],
        ],
    ];

    /**
     * 卡注销
     */
    const CANCEL = [
        'path'          => '/cashdesk-oil/v0/user/0/cardType/0/card/0/cancel',
        'requestMethod' => 'PUT',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'required' => [
                    'cardID' => 'string(uint64)@[1,19] = ""',
                ],
            ],
        ],
    ];

    /**
     * 卡冻结
     */
    const FREEZE = [
        'path'          => '',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [],
        ],
    ];

    /**
     * 卡片解冻
     */
    const UN_FREEZE = [
        'path'          => '',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [],
        ],
    ];

    /**
     * 补卡
     */
    const REPLACE = [
        'path'          => '',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [],
        ],
    ];

    /**
     * 设置付款账户
     */
    const SET_PAY_ACCOUNT = [
        'path'          => '',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [],
        ],
    ];

    /**
     * 卡变更机构
     */
    const CHANGE_ORG_CODE = [
        'path'          => '/cashdesk-oil/v0/user/0/cardType/0/card/0/alloc',
        'requestMethod' => 'PUT',
        'fields'        => [
            'url'   => [],
            'query' => [],
            'body'  => [
                'required' => [
                    'cardID'  => 'string(uint64)@[1,19] = ""',//卡ID
                    'orgCode' => 'string@[1,32] = ""',//卡ID
                ],
                'optional' => [
                    'merchantID' => 'string(uint64)@[0,19] = "0"',//机构所属运营商ID
                ],
            ],
        ],
    ];


    /**
     * 根据卡id查询卡详情
     */
    const GET_DETAIL_BY_CARD_ID = [
        'path'          => '/cashdesk-oil/v0/user/0/cardType/0/card/0/cardDetail',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [
                'required' => [
                    'cardID'  => 'string(uint64)@[1,19] = ""',//卡ID
                ],
                'optional' => [],
            ],
            'body'  => [],
        ],
    ];

    /**
     * 根据卡vice_no查询卡详情
     */
    const GET_DETAIL_BY_VICE_NO = [
        'path'          => '/cashdesk-oil/v0/user/0/cardType/0/card/0/cardNo',
        'requestMethod' => 'GET',
        'fields'        => [
            'url'   => [],
            'query' => [
                'required' => [
                    'cardTypeID'  => 'string(uint64)@[1,19]',//卡ID
                    'cardNo'  => 'string@r/^[0-9A-Za-z-]{1,64}$/',//卡ID
                ],
                'optional' => [],
            ],
            'body'  => [],
        ],
    ];

}