<?php
/**
 * 卡授信
 * Created by PhpStorm.
 * User: kevin
 * Date: 2017/12/5
 * Time: 上午11:44
 */

namespace G7Pay\Core;

use G7Pay\Library\Tool;

class CardCredit extends G7PayAbstract
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @title   卡授信
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  G7Pay\Core
     * @since
     * @params   type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    public function credit(array $params)
    {
        $methodConfig = MerchantMethod::CREATE;
        $afterParams = Tool::argumentCheck($methodConfig['fields'], $params,\TRUE);

        return $this
            ->setRequestType($methodConfig['requestMethod'])
            ->setRestPath($methodConfig['path'], $afterParams['url'])
            ->setQueryParams($afterParams['query'])
            ->setPostParams($afterParams['body'])
            ->sign()
            ->defaultHttpRequest();
    }

    /**
     * @title   查询授信列表
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package G7Pay\Core
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function getList(array $params)
    {
        //todo 更换
        $methodConfig = MerchantMethod::CREATE;
        $afterParams = Tool::argumentCheck($methodConfig['fields'], $params,\TRUE);

        return $this
            ->setRequestType($methodConfig['requestMethod'])
            ->setRestPath($methodConfig['path'], $afterParams['url'])
            ->setQueryParams($afterParams['query'])
            ->setPostParams($afterParams['body'])
            ->sign()
            ->defaultHttpRequest();
    }

    /**
     * @title   查询授信卡还款记录
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package G7Pay\Core
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function getPayBackList(array $params)
    {
        //todo 更换
        $methodConfig = MerchantMethod::CREATE;
        $afterParams = Tool::argumentCheck($methodConfig['fields'], $params,\TRUE);

        return $this
            ->setRequestType($methodConfig['requestMethod'])
            ->setRestPath($methodConfig['path'], $afterParams['url'])
            ->setQueryParams($afterParams['query'])
            ->setPostParams($afterParams['body'])
            ->sign()
            ->defaultHttpRequest();
    }

}