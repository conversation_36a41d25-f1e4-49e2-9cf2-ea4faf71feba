<?php
/**
 * 卡消费
 * Created by PhpStorm.
 * User: kevin
 * Date: 2017/12/5
 * Time: 上午11:44
 */

namespace G7Pay\Core;

use G7Pay\Defines\AccountRefundMethod;
use G7Pay\Library\Tool;

class AccountRefund extends G7PayAbstract
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @title   中启行退款
     * @desc
     * @version  1.0.0
     * <AUTHOR> @package  G7Pay\Core
     * @since
     * @params   type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * mixed
     * @returns
     */
    public function refundForFoss(array $params)
    {
        $methodConfig = AccountRefundMethod::REFUND_FOR_FOSS;
        $afterParams = Tool::argumentCheck($methodConfig['fields'], $params,\TRUE);

        return $this
            ->setRequestType($methodConfig['requestMethod'])
            ->setRestPath($methodConfig['path'], $afterParams['url'])
            ->setQueryParams($afterParams['query'])
            ->setPostParams($afterParams['body'])
            ->sign()
            ->defaultHttpRequest();
    }

    /**
     * @title   撬装退款
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package G7Pay\Core
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function refundForGas(array $params)
    {
        $methodConfig = AccountRefundMethod::REFUND_FOR_GAS;
        $afterParams = Tool::argumentCheck($methodConfig['fields'], $params,\TRUE);

        return $this
            ->setRequestType($methodConfig['requestMethod'])
            ->setRestPath($methodConfig['path'], $afterParams['url'])
            ->setQueryParams($afterParams['query'])
            ->setPostParams($afterParams['body'])
            ->sign()
            ->defaultHttpRequest();
    }

    /**
     * @title   退款记录查询
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package G7Pay\Core
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function getListForFoss(array $params)
    {
        //todo 更换
        $methodConfig = AccountRefundMethod::GET_LIST_FOR_FOSS;
        $afterParams = Tool::argumentCheck($methodConfig['fields'], $params,\TRUE);

        return $this
            ->setRequestType($methodConfig['requestMethod'])
            ->setRestPath($methodConfig['path'], $afterParams['url'])
            ->setQueryParams($afterParams['query'])
            ->setPostParams($afterParams['body'])
            ->sign()
            ->defaultHttpRequest();
    }

    /**
     * @title   退款记录查询
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package G7Pay\Core
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function getListForGas(array $params)
    {
        //todo 更换
        $methodConfig = AccountRefundMethod::GET_LIST_FOR_GAS;
        $afterParams = Tool::argumentCheck($methodConfig['fields'], $params,\TRUE);

        return $this
            ->setRequestType($methodConfig['requestMethod'])
            ->setRestPath($methodConfig['path'], $afterParams['url'])
            ->setQueryParams($afterParams['query'])
            ->setPostParams($afterParams['body'])
            ->sign()
            ->defaultHttpRequest();
    }

    /**
     * @title   退款单撤销
     * @desc
     * @version
     * @level 1
     * <AUTHOR> @package G7Pay\Core
     * @since
     * @params  type filedName required?
     * @param array $params
     * @return mixed
     * @returns
     * []
     * @returns
     */
    public function revoke(array $params)
    {
        $methodConfig = AccountRefundMethod::REVOKE;
        $afterParams = Tool::argumentCheck($methodConfig['fields'], $params,\TRUE);

        return $this
            ->setRequestType($methodConfig['requestMethod'])
            ->setRestPath($methodConfig['path'], $afterParams['url'])
            ->setQueryParams($afterParams['query'])
            ->setPostParams($afterParams['body'])
            ->sign()
            ->defaultHttpRequest();
    }

}