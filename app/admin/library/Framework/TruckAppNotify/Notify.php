<?php
/**
 * 推送消息给手机管车app
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/5/19/019
 * Time: 18:36
 */

namespace Framework\TruckAppNotify;
use Framework\Config;
use Framework\Mailer\MailSender;
use Fuel\Request\truckManagerClient;
use Framework\SDK\Sign\TruckManager as TruckManagerSign;
class Notify
{
    /**
     * 给手机管车发送消息
     * @param array $params
     * @return mixed
     */
    static public function send(array $params)
    {
        return true;
//        try{
//            $checkFields = ['uid', 'title', 'description'];
//            \helper::argumentCheck($checkFields, $params);
//
//            $nowTime = time();
//            $_apiParams = [
//                'method'    =>  'external.message.sendMessage',
//                'uid'         => $params['uid'],
//                'message'     => "1",
//                'title'       => $params['title'],
//                'description' => $params['description'],
//                'linkurl'     => isset($params['linkurl']) ? $params['linkurl'] : '',
//                'linktext'    => isset($params['linktext']) ? $params['linktext'] : '',
//                'scheduled'   => "0",
//                'appkey'      => Config::get('truckManager.app_key'),
//                'signTime'   => $nowTime,
//            ];
//
//            $apiParams = TruckManagerSign::createSign($_apiParams);
//
//            return truckManagerClient::post($apiParams);
//        }catch (\Exception $e){
//            \Framework\Mailer\MailSender::sendEmail('手机管车消息发送失败',strval($e) ,Config::get('alarmEmailList.list') );
//            \Framework\Log::dataLog('TruckAppNotify -- '.strval($e),'Error');
//        }

    }
}