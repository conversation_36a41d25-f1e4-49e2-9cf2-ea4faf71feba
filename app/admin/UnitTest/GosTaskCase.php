<?php


namespace UnitTest;


use Fuel\Service\GosTaskService;
use Models\OilGosTask;

class GosTaskCase
{
    /**
     * 重试20分钟前还未返回结果的任务
     * @return array
     */
    public function reTry()
    {
        $params = \helper::filterParams();
        $take = isset($params['take']) && $params['take'] ? $params['take'] : 100;
        
        $taskRecord = OilGosTask::where('createtime', '<', date("Y-m-d H:i:s", time() - 20 * 60))
            ->where('status', 20)
            ->orderBy('createtime', 'desc')
            ->take($take)
            ->get();
        
        $data = (new GosTaskService())->reSend($taskRecord);
        
        return $data;
        
    }
}