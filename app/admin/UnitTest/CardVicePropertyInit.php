<?php


namespace UnitTest;

use Fuel\Service\CardViceToGos;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCardMain;
use Models\OilCardVice;

class CardVicePropertyInit
{

    //初始化副卡属性为半托管的
    public function initCardViceBanTuoProperty(){
        Capsule::connection()->beginTransaction();
        try {
            //查询主卡属性为半托管的
            $mainId=OilCardMain::getOneField(['property'=>3],'id')->toArray();
            if(!empty($mainId)){
                echo '主卡半托管属性的共计'.count($mainId);
                //查询主卡下绑定的副卡
                $viceIds=OilCardVice::getPluckFields(['card_main_id_in'=>$mainId],'id');
                if(!empty($viceIds)){
                    //批量更新
                    echo '副卡批量更新的共计'.count($viceIds);
                    if(count($viceIds) > 100){
                        $newIds=array_chunk($viceIds,100);
                        foreach ($newIds as $item){
                            OilCardVice::edits(['property'=>3],['id'=>$item]);
                        }
                    }else{
                        OilCardVice::edits(['property'=>3],['id'=>$viceIds]);
                    }
                }
            }
            Capsule::connection()->commit();
        }catch (\Exception $e){
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
        echo '半托管属性初始化完成';
        //推送gos
        if(isset($viceIds) && !empty($viceIds)){
            if(count($viceIds) > 100){
                $newArr=array_chunk($viceIds,100);
                foreach ($newArr as $item){
                    CardViceToGos::sendBatchUpdateTask($item);
                }
            }else{
                CardViceToGos::sendBatchUpdateTask($viceIds);
            }
        }
        echo '半托管属性推送GOS完成';
    }

    //初始化副卡属性为全托管的
    public function initCardViceQuanTuoProperty(){
        Capsule::connection()->beginTransaction();
        try {
            //查询主卡属性为半托管的
            $mainId=OilCardMain::getOneField(['property'=>4],'id')->toArray();
            if(!empty($mainId)){
                echo '主卡全托管属性的共计'.count($mainId);
                //查询主卡下绑定的副卡
                $viceIds=OilCardVice::getPluckFields(['card_main_id_in'=>$mainId],'id');
                if(!empty($viceIds)){
                    echo '副卡批量更新的共计'.count($viceIds);
                    //批量更新
                    if(count($viceIds) > 100){
                        $newIds=array_chunk($viceIds,100);
                        foreach ($newIds as $item){
                            OilCardVice::edits(['property'=>3],['id'=>$item]);
                        }
                    }else{
                        OilCardVice::edits(['property'=>3],['id'=>$viceIds]);
                    }
                }
            }
            Capsule::connection()->commit();
        }catch (\Exception $e){
            Capsule::connection()->rollBack();
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
        echo '全托管属性初始化完成';
        //推送gos
        if(isset($viceIds) && !empty($viceIds)){
            if(count($viceIds) > 100){
                $newArr=array_chunk($viceIds,100);
                foreach ($newArr as $item){
                    CardViceToGos::sendBatchUpdateTask($item);
                }
            }else{
                CardViceToGos::sendBatchUpdateTask($viceIds);
            }
        }
        echo '全托管属性推送GOS完成';
    }

    //初始化副卡属性为主站卡的
    public function cardViceMainStationPropertyToGos(){
        $viceIds=OilCardVice::getPluckFields(['property'=>4],'id');
        //推送gos
        if(isset($viceIds) && !empty($viceIds)){
            if(count($viceIds) > 100){
                $newArr=array_chunk($viceIds,100);
                foreach ($newArr as $item){
                    CardViceToGos::sendBatchUpdateTask($item);
                }
            }else{
                CardViceToGos::sendBatchUpdateTask($viceIds);
            }
        }
        echo '副卡主站卡推送到gos';
    }

    //初始化副卡属性为虚拟卡的
    public function cardViceUnrealPropertyToGos(){
        $viceIds=OilCardVice::getPluckFields(['property'=>6],'id');
        //推送gos
        if(isset($viceIds) && !empty($viceIds)){
            if(count($viceIds) > 100){
                $newArr=array_chunk($viceIds,100);
                foreach ($newArr as $item){
                    CardViceToGos::sendBatchUpdateTask($item);
                }
            }else{
                CardViceToGos::sendBatchUpdateTask($viceIds);
            }
        }
        echo '副卡虚拟卡推送到gos';
    }


    //初始化副卡属性为站点托管的
    public function cardVicezdtgPropertyToGos(){
        Capsule::connection()->beginTransaction();
        $viceIds=OilCardVice::getPluckFields(['property'=>2],'id');
        //推送gos
        if(!empty($viceIds)){
            if(count($viceIds) > 100){
                $newArr=array_chunk($viceIds,100);
                foreach ($newArr as $item){
                    CardViceToGos::sendBatchUpdateTask($item);
                }
            }else{
                CardViceToGos::sendBatchUpdateTask($viceIds);
            }
        }
        echo '副卡站点托管卡推送到gos';
    }
}