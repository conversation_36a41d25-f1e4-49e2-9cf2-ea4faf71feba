<?php


namespace UnitTest;


use Fuel\Generator\ModelMake as ModelMake;
use helper;

class GenerateModelCase
{
    /**
     * model生成器
     * /inside.php?m=generators&f=makeModel&tbname=oil_card_main
     * 资源：
     * 添加  create|silk-add|false|createBtn
     * 编辑  edit|silk-edit|true|editBtn
     * 删除  remove|silk-delete|true|deleteBtn
     * 导出  exportData|silk-export|false|exportBtn
     */
    public function makeModel()
    {
        $params = helper::filterParams();
        $tbName = isset($params['tbname']) ? $params['tbname'] : '';
        $data = ModelMake::generator($tbName);

        return $data;
    }
}