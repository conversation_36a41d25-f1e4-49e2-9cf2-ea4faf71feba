<?php


namespace UnitTest;


use Framework\SDK\HangXin\ReceiptStock;
use Framework\Config;
use Framework\Log;
use GuzzleHttp\Client;
class SpiderReceiptReturn
{
    protected $getFpByHttpMethod = 'fpCheck/getFpByHttp';

    public  function test_spiderHangXinData($start_time, $end_time)
    {
        $page=1;
        $start_day_timestamp = strtotime('2021-07-01') + ($page - 1) * 2 * 86400;
        $start_time = date("Ymd", $start_day_timestamp);
        $end_day_timestamp = strtotime('2021-11-01') + ($page - 1) * 2 * 86400;
        $end_time = date("Ymd", $end_day_timestamp);

        $params = [
            'receipt_dateGte' => $start_time,
            'receipt_dateLte' => $end_time,
        ];
       $data=$this->getFpByHttp($params);
        var_dump($data);
       return $data;
    }

    public function getFpByHttp($params=[])
    {
        $hxConfig       = Config::get('hangXin');
        $data = null;


        $apiParams = [
            'cyjgfl'    => '1',
            'fplx'      => '01,08',//普通发票电子
            //'gmfnsrsbh' => implode(',', $tax_number_map),
            'kprqq'     => !empty($params['receipt_dateGte']) ? $params['receipt_dateGte'] : date("Ymd", strtotime("-5 day")),
            'kprqz'     => !empty($params['receipt_dateLte']) ? $params['receipt_dateLte'] : date("Ymd"),
        ];

        $jsonParams = \GuzzleHttp\json_encode($apiParams);
        $baseParams = base64_encode($jsonParams);

        $client = new Client(
            [
                'base_uri' => $hxConfig['api_url'],
                'timeout'  => 20.0,
            ]
        );

        try {
            $res = $client->request(
                'POST',
                $this->getFpByHttpMethod,
                [
                    'Accept' => 'application/json',
                    'json'   => ['datas' => $baseParams]
                ]
            );

            $statusCode = $res->getStatusCode();

            if ($statusCode != 200) {
                throw new \RuntimeException('请求航信接口错误', $statusCode);
            }

            $content = $res->getBody()->getContents();
            $result  = \GuzzleHttp\json_decode($content);
            if (isset($result->data) && isset($result->data->datas)) {
                $data = base64_decode($result->data->datas);
                if ($data) {
                    $data = \GuzzleHttp\json_decode($data);
                } else {
                    throw new \RuntimeException('航信接口返回值解析后不符合预期', 4048001);
                }
            } else {
                throw new \RuntimeException('航信接口返回值不符合预期', 4048002);
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "#" . __FUNCTION__, ['exception' => strval($e), 'message' => $e->getMessage()]);
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }

        return $data;
    }

}