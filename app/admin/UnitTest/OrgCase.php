<?php


namespace UnitTest;


use Framework\Excel\ExcelWriter;
use Framework\Log;
use Fuel\Defines\TradesType;
use Fuel\Service\AccountCenter\TransferService;
use Fuel\Service\Credit;
use Fuel\Service\OrgChangeOperatorService;
use Fuel\Service\OrgConfigService;
use Fuel\Service\OrgService;
use Jobs\CloseOrgSignNewJob;
use Jobs\ExportReportDataJob;
use Models\OilCardVice;
use Models\OilCardViceApp;
use Models\OilCardViceTrades;
use Models\OilCreditAccount;
use Models\OilOperators;
use Models\OilOrg;
use Illuminate\Database\Capsule\Manager as Capsule;
use function GuzzleHttp\Psr7\str;


class OrgCase
{
    public function TestCheckOrg()
    {
        $orgRoots = "2000BG,2000T0,200I1A,200133";

        return OilCardViceApp::checkOilOrg($orgRoots);
    }

    public function testSignData()
    {
        /*$fee = OrgChangeOperatorService::getSumUseChange(['pay_company_id'=>54,'org_id'=>650,"operator_id"=>0]);
        print_r($fee);exit;*/
        /*(new CloseOrgSignJob(['id'=>1]))->handle();
        echo 111;exit;*/
        $data = (new OrgService())->initSignReceipt(['orgcode'=>'200I1A']);
        $data = (new OrgService())->setCanSign(["can_change_sign"=>10]);
        $data = (new OrgService())->setCanSign(["can_change_sign"=>20]);
        /*$data = (new OrgService())->initSignReceipt([]);
        //,"orgcode"=>'20007I'
        $data = (new OrgService())->setCanSign(["can_change_sign"=>10]);
        $data = (new OrgService())->setCanSign(["can_change_sign"=>20]);
        print_r($data);exit;*/
    }

    //换签机构名单
    public function changeOperatorOrg()
    {
        $start = time();
        echo "开始时间:".\helper::nowTime()."\r\n";
        $exce = (new ExportReportDataJob())->exceiptOrg;
        array_push($exce,"201XW3");
        $orgSql = "SELECT
	o1.root,
	o.org_name,
	o1.mintime,
	o1.maxtime,
	o1.MODE,
	o1.operator,
	o.receipt_mode,
	o.operators_id 
FROM
	oil_org AS o
	RIGHT JOIN (
	SELECT LEFT
		( orgcode, 6 ) AS root,
		min( createtime ) AS mintime,
		max( createtime ) AS maxtime,
		GROUP_CONCAT( DISTINCT receipt_mode ) AS MODE,
		GROUP_CONCAT( DISTINCT operators_id ) AS operator 
	FROM
		oil_org 
	WHERE
		is_del = 0 
		AND STATUS = 1 
		AND is_test = 1 
GROUP BY
	LEFT ( orgcode, 6 )) AS o1 ON o.orgcode = o1.root
	where o.is_del = 0 
		AND o.STATUS = 1 
		AND o.is_test = 1 and o1.root not in ('".implode("','",$exce)."')";
        $list = Capsule::connection("slave")->select($orgSql);
        if(count($list) == 0){
            exit("机构为空");
        }
        $operatorMap = OilOperators::getIdMapName();
        $companyMap = $this->getCompanyData($exce);
        $chargeMap = $this->getChargeData($exce,$operatorMap);
        $exportData = [];
        foreach ($list as $_val){
            $root = $level = [];
            $op_diff = "一致";
            if( stripos($_val->operator,",") !== false){
                $op_diff = "不一致";
            }
            $mo_diff = "异常";
            if(stripos($_val->MODE,",") !== false){
                $mo_diff = "异常";
            }elseif($_val->MODE == 2){
                $mo_diff = "独立";
                $c = isset($companyMap[$_val->root]) ? $companyMap[$_val->root] : [];
                $root = array_unique(array_column($c,'orgroot'));
                $level = array_unique(array_column($c,"len"));
                if(count($level) == 0 || count($level) == 1){
                    $mo_diff = "标准独立";
                }elseif (count($level) > 1){
                    $mo_diff = "非标独立";
                }
            }elseif($_val->MODE == 1){
                $mo_diff = "集中";
            }
            $charge = isset($chargeMap[$_val->root]) ? $chargeMap[$_val->root] : ["operator_txt"=>"","money"=>0,"operator_id"=>"","last_time"=>"","min_time"=>""];
            $begin = !empty($charge['min_time']) ? $charge['min_time'] : $_val->mintime;
            $trade = $this->getTradeData($_val->root,$begin);
            $exportData[] = [
                "orgcode" => str($_val->root),
                "org_name" => $_val->org_name,
                "createtime" => $_val->mintime,
                "receipt_mode_name" => $mo_diff,
                "company_root" => implode(",",$root),
                "receipt_mode_id" => $_val->MODE,
                "operators_name" => isset($operatorMap[$_val->operators_id]) ? $operatorMap[$_val->operators_id] : "",
                "is_operator_diff" =>$op_diff,
                "charge_txt" => $charge['operator_txt'],
                "charge_money" => $charge['money'],
                "charge_operator_id" => $charge['operator_id'],
                "charge_last_time" => $charge['last_time'],
                "trade_txt" => $trade > 0 ? '不完整' : "完整",
            ];
        }

        if(count($exportData) > 0) {
            $filePath = $this->exportOrgRoot($exportData);
            echo "文件生成时间:".\helper::nowTime()."\r\n";
            echo "文件生成耗时：".(time() - $start)."秒\r\n";
            echo "文件:".$filePath."\r\n";
            $url = (new \commonModel())->fileUploadToOss($filePath);
            echo "OSS生成时间:".\helper::nowTime()."\r\n";
            echo "OSS生成耗时：".(time() - $start)."秒\r\n";
            echo $url;
        }
    }

    //充值数据
    public function getChargeData($exce,$operatorMap = [])
    {
        $sql = "SELECT
	t.orgroot,
	t.fee,
	g.org_name,
	t.maxtime,
	t.mintime,
	t.operator_txt
FROM
	oil_org AS g
	RIGHT JOIN (
	SELECT LEFT
		( o.orgcode, 6 ) AS orgroot,
		sum( arrival_money ) AS fee,
		max( c.createtime ) AS maxtime,
		min( c.createtime ) AS mintime,
		group_concat(DISTINCT IF
		( operator_id IS NULL OR operator_id = 0, \"空\", operator_id ) ) as operator_txt
	FROM
		oil_account_money_charge AS c
		LEFT JOIN oil_org AS o ON c.org_id = o.id 
	WHERE
		c.charge_type IN ( 1, 3, 8 ) 
		and c.status = 1
		and c.no_type = 'CZ'
		and c.arrival_money > 0
	GROUP BY
		LEFT ( o.orgcode, 6 ) 
	ORDER BY
	fee DESC 
	) AS t ON t.orgroot = g.orgcode 
	where t.orgroot not in ('".implode("','",$exce)."');";
        $list = Capsule::connection("slave")->select($sql);
        $map = [];
        foreach ($list as $_val){
            $operator = $_val->operator_txt;
            $isEmpty = false;
            if(stripos($operator,"空") !== false){
                $operator = str_replace("空","",$operator);
                $isEmpty = true;
            }
            $_tmp = explode(",",trim($operator,","));
            $txt = [];
            if(count($_tmp) > 0){
                foreach ($_tmp as $_item){
                    $txt[] = isset($operatorMap[$_item]) ? $operatorMap[$_item] : "";
                }
            }
            if($isEmpty){
                array_push($txt,"空");
            }
            $map[$_val->orgroot] = [
                "last_time"=>$_val->maxtime,
                "min_time"=>$_val->mintime,
                "operator_txt" => implode(",",$txt),
                "operator_id" => $_val->operator_txt,
                "money"=>$_val->fee,
            ];
        }
        return $map;
    }

    //付款公司
    public function getCompanyData($exce)
    {
        $sql = "SELECT id,orgroot,company_name from oil_pay_company where LEFT(orgroot,6) not in ('".implode("','",$exce)."') order by orgroot";
        $list = Capsule::connection("slave")->select($sql);
        foreach ($list as $_val){
            $orgRoot = substr($_val->orgroot,0,6);
            $map[$orgRoot][] = ['len'=>strlen($_val->orgroot),"orgroot"=>$_val->orgroot];
        }
        return $map;
    }

    //交易数据
    public function getTradeData($orgcode = "",$createtime = "")
    {
        if(empty($createtime)){
            Log::error("orgcode:".$orgcode,[],"changeSign_");
            return false;
        }
        $sql = "SELECT
	count( t.id ) as trade_num
FROM
	oil_card_vice_trades AS t FORCE INDEX ( `idx_org_id_createtime` )
	LEFT JOIN oil_org AS o on t.org_id = o.id 
WHERE
	t.card_from NOT IN ( 40, 41 ) 
	AND t.oil_com NOT IN ( 6, 7, 8 ) 
	AND t.trade_type IN (".TradesType::getCashAndSelfOilTradeTypeArr(true).") 
	AND t.createtime >= '".$createtime."' 
	AND t.createtime < '2022-01-01' 
	AND ( t.org_operator_id IS NULL OR t.org_operator_id = 0 ) 
	AND o.orgcode LIKE '".$orgcode."%';";
        $list = Capsule::connection("slave")->select($sql);
        return $list[0]->trade_num;
    }

    public function exportOrgRoot($exportData, $name = "顶级机构名单")
    {
        //导出数据
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;
        $exportData = [
            'filePath' => $realPath . 'data',
            'fileName' => $name . "_" . date("YmdHis") . rand(100, 999),
            'fileExt' => 'xls',
            'sheetName' => $name,
            'download' => 1,
            'title' => [
                'org_name' => '顶级机构名称',
                'orgcode' => '机构编码',
                'createtime' => '机构创建时间',
                'operators_name' => '运营商',
                'is_operator_diff' => '机构树运营商是否一致',
                'receipt_mode_name' => '核算方式',
                'receipt_mode_id' => '核算方式Id',
                "company_root" =>"付款公司机构编码",
                'charge_operator_id' => '充值运营商ID',
                'charge_txt' => '充值是否合格',
                'charge_money' => '充值金额',
                'charge_last_time' => '最后充值时间',
                'trade_txt' => '消费是否完整',
            ],
            'data' => $exportData,
        ];

        $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });

        return $fileUrl;
    }

    //初始化客户后台下单，机构配置
    public function initOrderConf()
    {
        $orgConf = ['200NYE','20000051','200NYE0A','200NYE0F'];
        //onLine
        //$orgList = ['2037CJ','2034BK','203IAI','2004TD'];
        $orgList = OilOrg::getByOrgCodes($orgConf);
        foreach ($orgList as $_item){
            OrgService::setOrder(['org_id'=>$_item->id,'orgcode'=>$_item->orgcode,'org_name'=>$_item->org_name]);
        }
    }

    public function TestOrgCardLimit()
    {
//        $data = (new TransferService())->createTransfer(4,1,9);
//        var_dump($data);exit;

//        $orgcode = '200O21';
//        $openNum = 2;
//        $limitRule = (new OrgConfigService())->getOrgOpenCardLimit($orgcode);
//        $limitRule = ['is_limit'=>1,'open_card_num'=>3];
//        if($limitRule){
//            if($limitRule['is_limit'] == 1 && $limitRule['open_card_num'] == 0){
//                throw new \RuntimeException('该机构限制开卡，请联系客服');
//            }
//
//            //限制开卡数量校验
//            if($limitRule['is_limit'] == 1 && $limitRule['open_card_num'] > 0){
//                //查询已开卡数量
//                $openedCardNum = OilCardVice::getCardNumByOrgCode(['orgcode'=>$orgcode]);
//                if(($openedCardNum + $openNum) >= $limitRule['open_card_num']){
//                    throw new \RuntimeException('该机构限制开卡数量，请联系客服');
//                }
//            }
//        }
        //var_dump((new OrgConfigService())->getOrgOpenCardLimit($orgcode));

        Credit::creditBalanceAlerm('********');
        exit('ok');

        $credit_info = OilCreditAccount::leftJoin('oil_credit_provider','oil_credit_provider.id','=','oil_credit_account.credit_provider_id')
            ->where('oil_credit_provider.is_own',1)
            ->where('oil_credit_account.status',10) //正常
            ->select('oil_credit_account.*')
            ->get();

        var_dump($credit_info->pluck('id')->toArray());exit;

        Credit::creditBalanceAlerm('********');
        exit('ok');

        $data = (new OrgConfigService())->getOrgOpenCardLimit('200O2102');
        var_dump($data['open_share_card_num'] == 0);exit;

        $a = [['orgcode'=>'200133'],['orgcode'=>'200133'],['orgcode'=>'200133'],['orgcode'=>'********'],['orgcode'=>'********']];
        foreach ($a as $key=>$val){
            $org_group[$val['orgcode']][] = $key;
        }
        var_dump($org_group);exit;

        var_dump(OilCreditAccount::getOrgTreeCreditSum($orgcode));exit;

        $title = '包氏集团18_A机构余额不足提醒';
        $content = [
            '时间: '.date('Y-m-d H:i:s'),
            '机构名称: '.'包氏集团18_A',
            '所属顶级: '.'包氏集团18',
            '机构额度: '.'1000000',
            '已用额度: '.'90000',
            '剩余额度: '.'910000',
            '剩余比例: '.'91%',
        ];
        Credit::sendNotify($content,$title);
        exit;
        var_dump(OilOrg::getResField(['orgcode'=>$orgcode],'org_name'));exit;

        $range = (new OrgConfigService)->getOrgConsumeAlarmSetting($orgcode);
        $range_val = $range['progress'];

        $compare_val = 80;

        //找出最接近的一个值
        $select_val = [999=>999];
        foreach ($range_val as $key => $value){
            if($compare_val >= $value && ($compare_val - $value) < array_values($select_val)){
                $select_val = [$key => $compare_val - $value];
            }
        }

        var_dump($range_val[array_keys($select_val)[0]]);
    }
    
    /**
      *  更新机构等级
      *  <AUTHOR> yanglei <<EMAIL>>
      *  @since  :2023年10月18日
      *
     */
    public function updateOrgLevel()
    {
        $orgs = OilOrg::where('crm_id','<>', '')->select(['crm_id'])->get()->toArray();
        
        $crm_ids = [];
        foreach ($orgs as $org){
            if(!in_array($org['crm_id'], $crm_ids)) {
                $crm_ids[] = $org['crm_id'];
            }
        }
        $crm_ids = array_chunk($crm_ids, 100);
        foreach($crm_ids as $ids){
            sleep(1);
            $update_arr = [];
            foreach($ids as $id) {
                $update_arr[$id] = '';
            }
            $id_string = implode(';', $ids);
            try{
                $data = \Fuel\Request\CrmClient::post(
                    $d = [
                        'method' => 'customer/getCustomer',
                        'data' => ['cdcid' => $id_string, 'accountgroup' => 20],
                    ]
                    );
            }catch(\Exception $e){
                $date = date('Y-m-d');
                Log::error($date.":".$id_string.":".$e->getMessage(),[],"updateOrgLevel_");
                continue;
            }
            
            if(!empty($data)) {
                foreach($data as $value){
                    if(key_exists($value->new_accountid, $update_arr)) {
                        $update_arr[$value->new_accountid] = $value->level;
                    }
                }
                
                
                foreach($update_arr as $crm_id => $level) {
                    OilOrg::where('crm_id', $crm_id)->update(['org_level' => $level]);
                }
            }
            
        }
    }
}