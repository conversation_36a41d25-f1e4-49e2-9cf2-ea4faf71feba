<?php


namespace UnitTest;


use Framework\Excel\ExcelWriter;
use Fuel\Request\OilAgentClient;
use Fuel\Service\CustomerCard;
use Fuel\Service\CustomerStream;
use Fuel\Service\MainCatch;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCardViceTrades;
use Models\OilCustomerCard;

class CustomerCardCase
{
    public function syncCard()
    {
        (new CustomerCard())->syncCard();
    }

    //请求代理，取消账户托管
    public function removeAccount()
    {
        $params = \helper::filterParams();
        \helper::argumentCheck(['account_no'], $params);
        $accountInfo = OilCustomerCard::getByAccountNo($params['account_no']);
        if(!$accountInfo){
            throw new \RuntimeException('帐号信息不存在！', 2);
        }
        $sendParams = [
            'account'   => $params['account_no'],
            'cardType'  => $accountInfo->oil_com == 1 ? 'zsh' : 'zsy',
            'actionTag' => 'TDEPPON',
        ];

        //删除代理
        $res = OilAgentClient::post([
            'method' => 'crawl-provider.manage.canceltrust',
            'data'   => $sendParams,
        ]);
        print_r($res);
    }

    public function getIdsByTime()
    {
        $params = \helper::filterParams();
        try {
            (new CustomerCard())->getIdsByTime($params);
//            (new CustomerCard())->getTradesByIds([1428711,1428712,1428713]);
        } catch (\Exception $e) {
            echo strval($e);
        }
    }

    public function getIdsByIds()
    {
        $map = [********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,13708594,13708596,13708598,13708600,13708601,13708603,13708605,13708607,13708609,13708611,13708613,13708615,13708617,13708619,13708621,13708623,13708625,13708626,13708628,13708630,13708633,13708635,13708637,13708638,13708639,13708641,13708642,13708644,13708646,13708648,13708651,13708653,13708655,13708656,13708659,13708661,13708663,13708666,13708668,13708670,13708671,13708673,13708674,13708676,13708678,13708680,13708682,13708684,13708686,13708687,13708689,13708692,13708693,13708695,13708697,13708700,13708702,13708704,13708706,13708708,13708710,13708714,13708716,13708718,13708721,13708723,13708726,13708728,13708730,13708732,13708734,13708736,13708738,13708741,13708743,13708746,13708748,14256152,14256153,14256155,14256156,14256158,14256159,14304284,14304285,14304286,14304287,14304288,14304289,14358701,14358703,14358704,14358706,14358707,14358710,14358711,14358713,14358714,14358715,14358717,14358720,14358722,14358723,14358725,14358726,14358728,14358730,14358732,14358733,14358735,14358737,14358740,14358742,14358745,14358747,14358749,14358751,14358753,14358754,14358756,14358759,14358760,14358762,14358763,14358765,14358767,14358770,14358772,14358774,14358776,14358777,14358778,14358781,14358783,14358785,14358786,14358787,14358789,14358791,14358793,14358795,14358797,14358799,14358801,14358803,14358804,14358806,14358808,14358809,14358811,14358813,14358816,14358818,14358820,14358823,14358825,14358826,14358828,14358831,14358833,14358836,14358838,14358840,14358842,14358844,14358847,14358849,14358850,14358852,14358853,14358855,14358856,14358859,14358861,14358863,14358865,14358867,14358869,14358871,14358873,14358875,14358878,14358880,14358882,14358883,14358887,14358889,14358891,14358894,14358896,14358898,14358900,14358902,14358903,14358905,14358908,14358910,14358913,14358915,14358917,14358920,14358922,14358924,14358926,14358929,14358931,14358932,14389585,14389586,14389587,14389588];
        (new CustomerCard())->getTradesByIds($map);

    }

    public function getIdsByTimeByRange()
    {
        try {
            $start           = '2020-01-01 00:00:00';
            $start_timestamp = strtotime($start);
            for ($i = 0; $i < 200; $i++) {
                $params['start_time'] = date("Y-m-d", $start_timestamp + $i * 86400) . ' 00:00:00';
                $params['end_time']   = date("Y-m-d", $start_timestamp + $i * 86400) . ' 23:59:59';
                (new CustomerCard())->getIdsByTime($params);

                sleep(2);
            }

//            (new CustomerCard())->getTradesByIds([1428711,1428712,1428713]);
        } catch (\Exception $e) {
            echo strval($e);
        }
    }

    /*
     * 托管账号密码解密
     */
    public function passwordDeCrypt()
    {
        $password = '';
        $account_no = 'bs13688009765';
        $accountInfo = OilCustomerCard::getByAccountNo($account_no);

        if($accountInfo){
            $password = (new CustomerCard())->passwordDeCrypt($accountInfo->password);
        }else{
            echo 'no account_no';
        }

        echo $password;exit;
    }

    //托管卡数据汇总
    public function summaryCustomerData()
    {
        $condition['status'] = 1;
        $condition['deletedIsNull'] = 1;
        $condition['_export'] = 1;
        $accountList = OilCustomerCard::getList($condition);
        if(count($accountList) == 0){
            die("托管账号为空");
        }
        $dataMap = [];
        $orgMap = [];
        foreach ($accountList as $_item){
            $orgRoot = substr($_item->orgcode,0,6);
            $orgMap[$orgRoot] = $_item->org_name;
            $dataMap[$orgRoot][] = $_item->account_no;
        }
        if(count($dataMap) == 0){
            die("托管账户为空");
        }
        $exportData = [];
        foreach ($dataMap as $root => $accountNo){
            echo "root:".$root.",account-Num:".count($accountNo)."\r\n";
            if(count($accountNo) == 0){
                continue;
            }
            $oneItem["orgRoot"] = strval($root);
            $oneItem["orgName"] = isset($orgMap[$root]) && $orgMap[$root] ? $orgMap[$root] : "";
            $oneItem['money'] = $oneItem['num'] = $oneItem['viceNum'] = $oneItem['mainNum'] = 0;
            $trades = $this->getViceTrades($accountNo,$root);
            if($trades && count($trades[0]) > 0){
                $oneItem['money'] = $trades[0]->money;
                $oneItem['num'] = $trades[0]->num;
                $oneItem['viceNum'] = $trades[0]->viceNum;
                $oneItem['mainNum'] = $trades[0]->mainNum;
            }
            $exportData[] = $oneItem;
            //print_r($oneItem);
            //break;
        }
        if(count($exportData) > 0) {
            $fileUrl = $this->exportExcel($exportData);
            echo "localFile:\r\n";
            echo $fileUrl."\r\n";
            $url = (new \commonModel())->fileUploadToOss($fileUrl);
            echo "ossUrl:\r\n";
            echo $url."\r\n";
        }
        echo "success";
    }

    public function exportExcel($result = [])
    {
        $realPath = APP_WWW_ROOT . DIRECTORY_SEPARATOR;

        $filePath = $realPath . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'customer_card' . DIRECTORY_SEPARATOR;
        if (!is_dir($filePath)) {//创建目录
            mkdir($filePath, 0777);
        }
        $exportData = [
            'filePath'  => $realPath . 'data' . DIRECTORY_SEPARATOR . 'customer_card',
            'fileName'  => '托管客户数据'.date("YmdHis"),
            'fileExt'   => 'xls',
            'sheetName' => '托管客户数据',
            'download'  => 1,
            'title'     => [
                'orgRoot' => '机构编码',
                'orgName'     => '机构名称',
                'mainNum'          => '主卡数量',
                'viceNum'      => '副卡数量',
                'num'            => '累计交易笔数',
                'money'        => '累计消费',
            ],
            'data'      => $result,
        ];

        $fileUrl = ExcelWriter::exportXls($exportData, function ($phpExcelObj, $data, $lineCell) {
            $phpExcelObj->setCellValue($lineCell['cellNum'] . $lineCell['lineNum'], $data['value']);
        });

        return $fileUrl;
    }

    public function getViceTrades($accountNo = [],$orgRoot = "")
    {
        $sql = "select sum(trades.trade_money) as money,count(trades.id) as num,count(distinct trades.vice_no) as viceNum,count(distinct main.main_no) as mainNum 
            from oil_card_vice_trades as trades 
            left join oil_org as org on org.id = trades.org_id
            left join oil_card_vice as vice on trades.vice_no = vice.vice_no
            left join oil_card_main as main on vice.card_main_id = main.id
            where trades.card_from in (40,41) and org.orgcode like '".$orgRoot."%'";
        if(count($accountNo) > 0){
            $sql .= " and main.account_name in ('".implode("','",$accountNo)."')";
        }
        $sql .= " order by trades.createtime desc";
        $result = Capsule::connection('online_only_read')->select($sql);
        return $result;
    }

    public function addMainCard(){
        (new MainCatch())->addMain2Dsp();
    }
}