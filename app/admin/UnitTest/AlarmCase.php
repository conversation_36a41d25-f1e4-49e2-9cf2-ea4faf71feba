<?php


namespace UnitTest;


//use Framework\Cache;
use Framework\DingTalk\DingTalkAlarm;
use Framework\Log;
use Framework\Mailer\MailSender;
use Fuel\Defines\AccountBalanceAlarm;
use Fuel\Defines\OilCom;
use Fuel\Request\OilAgentClient;
use Fuel\Service\AccountCharge;
use Fuel\Service\ApplySplit2Receipt;
use Fuel\Service\CardTradeService;
use Fuel\Service\CardVice;
use Fuel\Service\ClaimMoney;
use Fuel\Service\Credit;
use Fuel\Service\ExportJobSrv;
use Fuel\Service\FanliSrv;
use Fuel\Service\OperatorReceipt;
use Fuel\Service\OrgChangeOperatorService;
use Fuel\Service\OrgService;
use Fuel\Service\OrgSignService;
use Fuel\Service\RebateCaculateService;
use Fuel\Service\ReceiptSplitApply;
use Fuel\Service\TradeReceiptInvoiceRed;
use Fuel\Service\TradesService;
use Fuel\Service\UpstreamRebate\PolicyService;
use Illuminate\Database\Capsule\Manager as Capsule;
use Jobs\AddReceiptApplyDetailsJob;
use Jobs\MarkTradeFanliJob;
use Models\OilAccountMoneyFanliRecords;
use Models\OilCardVice;
use Models\OilCardViceTrades;
use Jobs\SplitSaleReceiptJob;
use Jobs\ExportReportDataJob;
use Jobs\ReceiptApplyInternalJob;
use Models\OilOperatorDayTrades;
use Models\OilOrg;
use Models\OilOrgChangeOperatorLog;
use Models\OilPayCompany;
use Models\OilReceiptApplyDetailsCombine;

class AlarmCase
{

    public function TestFanliAccount()
    {
        $arr[2177] =[18256=>30,18257=>20];
        ApplySplit2Receipt::checkReceiptSale($arr);
        exit;
/*        $_params = OilCardViceTrades::getById(['id'=>********]);
        (new MarkTradeFanliJob(['trade'=>$_params,'flag'=>1]))->handle();
        $_params = OilCardViceTrades::getById(['id'=>********]);
//        (new MarkTradeFanliJob(['trade'=>$_params,'flag'=>2]))->handle();
        exit;*/

        $org_id = 348;
        $org_name = "测试水电费";
        $money = 1000;
        $no = date("YmdHis",time());
        $useFanli = FanliSrv::getUseFanliByMoney([
            'money' => $money,
            "org_id" => $org_id
        ]);

        print_r($useFanli);exit;

        if(bccomp($useFanli['use_fanli'],0,2) > 0) {
            OilAccountMoneyFanliRecords::updateFanliById($useFanli["money_id"],$useFanli['use_fanli'],3,[
                "org_id" => $org_id,
                "after_money" => $useFanli['after_money'],
                "total_fee" => $money,
                "org_name" => $org_name,
                "no" => $no,
                "sn" => $no,
                "rate" => $useFanli['rate'],
                "account" => $useFanli['account'],
            ],true,false);
        }
        print_r($useFanli);
    }

    public function testSql()
    {
        (new ReceiptApplyInternalJob())->applyDetailsCombine(2288,"*****************",14);
        exit;
        //(new ReceiptApplyInternalJob())->applyDetailsCombine(2028,"*****************",21);
        //(new ReceiptApplyInternalJob())->applyDetailsCombine(2030,"*****************",13);
        //exit;

        (new SplitSaleReceiptJob([
            'ids' => '1970',   'amount_type' => '1',   'is_internal' => '1',   'open_channel' => '30',   'jobs_id' => '********-19be-49b4-b652-491cf3c361cc',
        ]))->handle();
        exit;

        $billSql = "SELECT
     sum(debt_money - IFNULL(serviceAmount,0)) as 'total_credit_money',
     org.orgcode as org_code,
     org.id as org_id
FROM
    oil_credit_bill bill
    left join oil_credit_account account on bill.account_id = account.id
    left join oil_org org on org.id = account.org_id
WHERE
    left(org.orgcode,6) != '201XW3'
    and left(org.orgcode,6) like '203TC6%' 
    AND bill.bill_time >= '2022-01-01'
    AND bill.bill_time < '2023-01-01'
    group by org.orgcode";
        $data = Capsule::connection('slave')->select($billSql);
        $m = 0;
        foreach ($data as $_val){
            $sql = "select 
    sum(repay.repay_money) as repaid_money 
from 
    oil_credit_repay as repay 
    left join oil_credit_bill as bill on repay.third_id = bill.id
where 
    repay.status = 1 and repay.org_id =".$_val->org_id." and repay.createtime < '2023-01-01' and repay.createtime >= '2022-01-01'";
            $record = Capsule::connection('slave')->select($sql);
            foreach ($record as $_one){
                $m += $_one->repaid_money;
            }
        }
        echo "还款金额：".$m;
    }
    public function healthAlarm()
    {

        (new \Fuel\Service\ClaimMoney())->UpdateBankRecords("G2563900019170C");
        exit;
        (new ReceiptSplitApply())->addReceiptTrades(1779,['new_sale_id'=>17229, 'unit_model'=>"吨", 'receipt_project'=>"天然气类"]);
        exit;

        /*$sendParams = [
            'account'   => 'Y736931449',
            'cardType'  => 'zsh',
            'actionTag' => 'TDEPPON',
        ];

        //删除代理
        $res = OilAgentClient::post([
            'method' => 'crawl-provider.manage.canceltrust',
            'data'   => $sendParams,
        ]);
        print_r($res);exit;*/

        //TradeReceiptInvoiceRed::addReceiptInvoiceTrade(["id"=>14555,"trade_money"=>-101,"oil_com"=>20,"org_id"=>7084,"org_operator_id"=>2,"oil_name"=>"0#柴油国六"],501);
        /*$condition = [
            'card_no' => "****************",
            'station_id' => "08c522f6cc0b11eabea9f274353b6cab",
            'oil_name_val' => "柴油",
            'except_ids'  => "",
        ];
        $res = (new CardTradeService())->checkStationByCardNoAndStation($condition);
        print_r($res);exit;*/

        $list = TradesService::getTradeType("电子加油,交易撤销,预约加油");
        print_r($list);exit;

        (new ReceiptSplitApply())->testExportData();
        exit;

        $orgList = ["204CS1","201C8M","2029YW","201Z0I"];
        foreach ($orgList as $orgcode) {
            $condition['orgcode'] = $orgcode;
            $condition['org_operator_id'] = 2;
            $condition['group_len'] = strlen($condition['orgcode']);
            $income = OrgChangeOperatorService::getSumCharge($condition);
            if ($income == -110) {
                print_r("描述：现金充值单存在付款公司为空的数据！！！");
                continue;
            }
            $condition['end_time'] = \helper::nowTime();
            $trade = OrgChangeOperatorService::getSumTrades($condition);
            $diff_fee = $income - $trade['money'];
            Log::error("换签差额", [['income' => $income, "trade" => $trade['money'], "diff" => $diff_fee]], "alarm");
            if (bccomp($diff_fee, 0, 2) < 0) {
                print_r("描述：交易数据大于充值数据不合规！！！");
                continue;
            }
        }
        exit;

        $change = OilOrgChangeOperatorLog::getChangeList(['org_code'=>'200021']);
        print_r($change);exit;

        $str = OperatorReceipt::getCanReceiptTime(['up_operator_id'=>6, 'down_operator_id'=>2, 'oil_supplier_id'=>14]);
        print_r($str);
        exit;
        /*OilOrg::updateExclusiveCustom(['id'=>7213,'belongto_saler_id'=>'407520387237152224','belongto_saler'=>'张志猛1']);
        exit;*/
        //(new PolicyService())->finalAction(["month"=>"2022-10","suit_obj"=>1]);
        /*$tt  = [];
        (new RebateCaculateService())->isExistNoCalculateTrade(['month' => '2022-07', "suit_obj" => 1], $tt);
        exit;*/
        /*$idArr = [1075];
        (new \Jobs\SplitSaleReceiptJob(['ids' => $idArr]))->handle();
        exit;*/
        //AccountBalanceAlarm::alarm(303049,21,"200NW501");
        //exit;
        $content = [];
        $content[] = "#### " . date("Y年m月d日") . "——健康打卡提醒";
        $content[] = "#### 请及时汇报健康情况~";
        $content[] = "> 时间：" . date("Y-m-d H:i:s");
        return (new DingTalkAlarm())->sendDingTalkByConfig('fossDeveloperGroup', "健康打卡提醒", implode("\n", $content), [], true);
//		return (new DingTalkAlarm())->sendDingTalkByConfig('fossWarnGroup',"健康打卡提醒测试",implode("\n",$content),[],true);
    }

    public function testSend()
    {

        //(new ExportReceiptReturnJob(['progress'=>30,'receipt_dateLte'=>'2022-09-01','seller_nameLike'=>'宁夏新凯能源有限公司']))->handle();
        //exit;
        $res = AccountCharge::sendNotify(['付款单：FK123123123']);

        return $res;
    }

    public function testSendEmail()
    {
        $emailRootTitle = 'G7用油机构额度不足，请尽快充值';
        $emailRootContent = '尊敬的客户您好：
                        为了不影响您的用油体验，请您尽快充值。';
        MailSender::sendNow($emailRootTitle, $emailRootContent, ["<EMAIL>"]);

        echo 'send success';
    }

    public function testCache()
    {
//        $key = 'all_org_alerm_val200NYG_7000.01';
//        Cache::forget($key);

        $arr = ["35", "36", "37", "38", "39", "40", "41", "42", "43", "44"];
        $a = 40.005;
        $res = Credit::selectMin($arr, $a);
        echo '<pre>';
        var_dump($res);
        exit;
    }

    //注册导出
    public function testExport()
    {
        $user = new \stdClass();
        $user->user_name = "what";
        $user->id = "1";
        $reuslt = ExportJobSrv::registerExportTask("test", [], $user);
        print_r($reuslt);
        exit;
    }

    //提交导出
    public function submitFile()
    {
        $_params['task_sn'] = "ea15c5e1-6d17-41fb-a047-541ee0175318";
        $_params['type'] = 1;
        $_params['export_result_code'] = 0;
        $_params['url'] = "https://oss.aliyuncs.com/gsp-fs/fuel/license/20200324/card_trades_20200324_21_13_41884.zip";
        $reuslt = ExportJobSrv::submitExportFile($_params);
        print_r($reuslt);
    }

    public function queryResutl()
    {
        $_params['task_sn'] = "ea15c5e1-6d17-41fb-a047-541ee0175318";
        $reuslt = ExportJobSrv::querySubmitResult($_params);
        print_r($reuslt);
    }

    public function checkLimit()
    {
        CardVice::getLimitDistance("", []);
    }

}