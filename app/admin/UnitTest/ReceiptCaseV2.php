<?php

namespace UnitTest;
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 19-7-25
 * Time: 上午11:46
 */

use Framework\DingTalk\DingTalkAlarm;
use Fuel\Defines\OilType;
use Fuel\Defines\ReceiptConfig;
use Fuel\Service\ReceiptSplit;
use Fuel\Service\ReceiptSplitApply;
use Illuminate\Database\Capsule\Manager as Capsule;
use Models\OilCardViceTrades;
use Models\OilOrg;
use Models\OilReceiptApply;
use Models\OilReceiptApplyDetails;
use Framework\Log;
use Models\OilTypeNo;

class ReceiptCaseV2
{
    /**
     * 已开票消费退款处理
     * @param $trade
     * @return array
     */
    public function receiptTradeReject(){
        global $app;

        $params = \helper::filterParams();
        \helper::argumentCheck(['no'], $params);

        $_noStr = strval($params['no']);
        $trade = OilCardViceTrades::getOneInfo(['api_id' => $_noStr]);

        $org=OilOrg::getById(['id'=>$trade->org_id]);

        if($org->orgcode == ""){
            throw new \RuntimeException("机构信息不存在,无法撤销",2);
        }

        if (!in_array(substr($org->orgcode,0,6),$app->config->receipt->cancelOrgList)){
            throw new \RuntimeException("机构不在白名单配置,无法撤销",2);
        }

        $str = "订单号：".$trade->api_id;

        //获取已开票消费的发票申请明细
        $applyList = OilReceiptApplyDetails::getApplyByTrade(['trades_id'=>$trade->id]);
        if(count($applyList) == 0){
            throw new \RuntimeException($str.",已开票，无开票明细,无法撤销",2);
        }
        //对需要替换的消费开票明细处理
        $sql=[];
        $api_id=[$trade->api_id];
        foreach ($applyList as $k=>$item) {
            Log::error('原开票消费信息', [
                "receipt_money"=>$item['receipt_money'],'detail_id'=>$item['detail_id'],'oil_name'=>$item['oil_name'],'org_id'=>$item['org_id'],'apply_id'=>$item['id'],'apply_no'=>$item['no']
            ], 'receiptTradeReject');
            //获取油品种类信息
            $oilTypeNos = OilTypeNo::getList([
                '_export' => 1,
                'oil_no'  => $item['oil_name'],
            ])->toArray();
            if(!empty($oilTypeNos)){
                $oilType = $oilTypeNos[0]['oil_type'];
            }else{
                throw new \RuntimeException($str."油品种类不明确,无法撤销",2);
            }
            //查询同机构同油品种类的未被占用流水的消费 按金额正序排序
            $condition['oil_type'] = $oilType;
            $condition['org_id'] = $item['org_id'];
            $condition['api_id'] = $api_id;
//            $condition['moneyGt'] = 300;
            $newTrades = $this->getTrades($condition);
            //将查询结果分组 规格型号相同的A组   不同规格型号的B组
            $sameOilNameTrades =[];
            $sameOilNoTrades=[];
            foreach ($newTrades as $newTrade){
                if ($newTrade->oil_name ==  $item['oil_name']) {
                    $sameOilNameTrades[]=$newTrade;
                }else{
                    $sameOilNoTrades[]=$newTrade;
                }
            }
            //判断同规格型号的数组是否为空
            if(!empty($sameOilNameTrades)){
                // 从A组$sameOilNameTrades 查找仅需拆分的流水单即 新流水的开票金额大于等于开票明细金额 结果res
                $res=$this->cancelTradeSql($sameOilNameTrades,$item);
                if(!empty($res['sql'])){
                    foreach ($res['sql'] as $value){
                        $sql[]=$value;
                    }
                }
                if(!empty($res['api_id'])){
                    foreach ($res['api_id'] as $v){
                        $api_id[]=$v;
                    }
                }

            }else{
                //则从B组$sameOilNoTrades 查找仅需拆分的流水单即 新流水的开票金额大于等于开票明细金额 结果res
                $res=$this->cancelTradeSql($sameOilNoTrades,$item);

                if(!empty($res['sql'])){
                    foreach ($res['sql'] as $value){
                        $sql[]=$value;
                    }
                }
                if(!empty($res['api_id'])){
                    foreach ($res['api_id'] as $v){
                        $api_id[]=$v;
                    }
                }
            }
        }

        $sql[] = "update oil_card_vice_trades set is_open_invoice = null,receipt_remain = trade_money-use_fanli_money where id = " . $trade->id;


        return  $sql;

//        Capsule::connection()->beginTransaction();
//        try {
//            print_r($sql);
//            $upSqlStr = implode(";",$sql);
//            //echo $upSqlStr;exit;
//            $result = Capsule::connection()->getPdo()->exec($upSqlStr);
//            print_r($result);
//            Capsule::connection()->commit();
//        }catch (\Exception $exception){
//            Capsule::connection()->rollBack();
//        }

//        echo "success";

    }

    public function getTrades($params = [])
    {
        $sql = "SELECT d.id,api_id,receipt_remain,oil_name,trade_money,trade_num,use_fanli_money,fanli_money from oil_card_vice_trades as d left join  oil_type_no as t on d.oil_name =  t.oil_no where d.is_open_invoice is null and d.receipt_remain > 0 and d.cancel_sn is null";

        if(isset($params['oil_type'])){
            $where[] = "t.oil_type = '".$params['oil_type']."'";
        }
        if(isset($params['org_id'])){
            $where[] = "d.org_id = ".$params['org_id'];
        }
        if(isset($params['moneyGt'])){
            $where[] = "d.receipt_remain >= ".$params['moneyGt'];
        }

        if(count($where) > 0){
            $sql .= " and ". implode(" and ",$where);
        }
        $sql .= " and api_id not in ('".implode("','",$params['api_id'])."')";
        $sql .= " order by receipt_remain asc";
        echo "\r\n".$sql."\r\n";
        $result = Capsule::connection()->select($sql);
        return $result;
    }


    public function cancelTradeSql($tradesArr,$applyDetail){
        //不为空，则从$tradesArr 查找仅需拆分的流水单即 新流水的开票金额大于等于开票明细金额
        //取数组end
        $sql=[];
        $api_id=[];
        $end=count($tradesArr)-1;
        $maxTrade =$tradesArr[$end];
        if($maxTrade->receipt_remain >= $applyDetail['receipt_money']){
            //存在拆分的流水 -》拆分
            //新流水同开票金额做减法计算
            if(bccomp($maxTrade->receipt_remain,$applyDetail['receipt_money'],2) > 0){
                $left_money = bcsub($maxTrade->receipt_remain,$applyDetail['receipt_money'],2);
                $invoce = 20;
            }elseif(bccomp($maxTrade->receipt_remain,$applyDetail['receipt_money'],2) == 0){
                $invoce = 10;
                $left_money = 0;
            }
            $api_id[]=$maxTrade->api_id;
            $sql[] = "update oil_card_vice_trades set is_open_invoice = " . $invoce . ",receipt_remain = " . $left_money . " where id = " . $maxTrade->id;
            $sql[] = "update oil_receipt_apply_details set trades_id = " . $maxTrade->id . ",trade_money=" . $maxTrade->trade_money . " where id = " . $applyDetail['detail_id'];
        }else{
            //不存在拆分的流水-》 组合 -》 金额 从小到大 进行组合
            $plusMoney=0;
            $useTrades=[];
            foreach ($tradesArr as $sameOilNameTrade ) {
                if ($plusMoney  < $applyDetail['receipt_money']){
                    $plusMoney +=$sameOilNameTrade->receipt_remain;
                    $useTrades[]=$sameOilNameTrade;
                }else{
                    break;
                }
            }
            if ($plusMoney >= $applyDetail['receipt_money']) {
                // 组合累加值 金额大于等于 开票明细金额
                $endUseTradesKey=count($useTrades)-1;
                $lastMoney=0;
                foreach ($useTrades as $key=>$useTrade){
                    if($key != $endUseTradesKey){
                        $invoce = 10;
                        $left_money = 0;
                        $lastMoney +=$useTrade->trade_money;
                        $api_id[]=$useTrade->api_id;
                        $sql[] = "update oil_card_vice_trades set is_open_invoice = " . $invoce . ",receipt_remain = " . $left_money . " where id = " . $useTrade->id;
                        $sql[] = "insert into  oil_receipt_apply_details(receipt_apply_id,trade_num,origin_trade_num,receipt_money,trade_money,trades_id,fanli_money,use_fanli_money) values(" . $applyDetail['id'] . "," . $useTrade->trade_num ."," . $useTrade->trade_num ."," . $useTrade->receipt_remain ."," . $useTrade->trade_money . "," . $useTrade->id ."," . $useTrade->fanli_money ."," . $useTrade->use_fanli_money .")";
                    }else{
                        //最后一条数据进行减法计算
                        $lastReceiptMoney=$applyDetail['receipt_money']-$lastMoney;
                        if(bccomp($useTrade->receipt_remain,$lastReceiptMoney,2) > 0){
                            $left_money = bcsub($useTrade->receipt_remain,$lastReceiptMoney,2);
                            $invoce = 20;
                        }elseif(bccomp($useTrade->receipt_remain,$lastReceiptMoney,2) == 0){
                            $invoce = 10;
                            $left_money = 0;
                        }
                        $api_id[]=$useTrade->api_id;
                        $sql[] = "update oil_card_vice_trades set is_open_invoice = " . $invoce . ",receipt_remain = " . $left_money . " where id = " . $useTrade->id;
                        $sql[] = "update oil_receipt_apply_details set trades_id = " . $useTrade->id. ",receipt_money=" . $lastReceiptMoney  . ",trade_money=" . $useTrade->trade_money . " where id = " . $applyDetail['detail_id'];
                    }
                }

            }else{
                //组合累加值 金额小于 开票明细金额  ,抛出异常此消费已开票，且无可替换流水，无法撤销；
                throw new \RuntimeException("此消费已开票，且无可替换流水，无法撤销",2);
            }

        }
        return ['sql'=>$sql,'api_id'=>$api_id];
    }
}

