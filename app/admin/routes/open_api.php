<?php
/**
 * Created by PhpStorm.
 * User: zlx66
 * Date: 2016/11/26/026
 * Time: 9:55
 */

//油品门户
$router->group(['prefix' => 'open_api', 'middleware' => 'openApiAuth'], function () use ($router) {
    //油品邮递地址相关
    $router->post('open_api.oilorgaddr.search', 'oil_org_addr@getlist'); //获取油品地址 ok

    $router->post('open_api.cardmain.search', 'oil_card_main@search');
    $router->post('open_api.cardvice.search', 'oil_card_vice@search');
    $router->post('open_api.cardvice.balanceRefreshForG7s', 'oil_card_vice@balanceRefreshForG7s');

    //统计视图，微信页面卡片列表
    $router->any('open_api.zqx.cardvice.getlist', 'oil_card_vice@getList');

    //油品收卡地址详情
    $router->any('open_api.zqx.oilorgaddr.show', 'oil_org_addr@show'); //油品地址详情 ok


    $router->post('open_api.oilorgaddr.store', 'oil_org_addr@store'); //油品地址存储 ok
    $router->post('open_api.oilorgaddr.update', 'oil_org_addr@update'); //油品地址更新 ok
    $router->post('open_api.oilorgaddr.delete', 'oil_org_addr@delete'); //油品地址删除 ok
    $router->post('open_api.oilorgaddr.getContactIdByAddrId', 'oil_org_addr@getContactIdByAddrId'); //根据addr_id 得到负责人id

    //获取省市区数据
    $router->post('open_api.oilcity.search', 'oil_city@getAllCityData'); //获取所有数据城市信息 ok
    $router->post('open_api.oilcity.getProvinceData', 'oil_city@getProvinceData'); //获取省信息数据 ok
    $router->post('open_api.oilcity.getSubCityByCode', 'oil_city@getSubCityByCode'); //根据code获取子城市信息 ok

    //获取省市县数据
    $router->any('open_api.zqx.oilcity.getProvince', 'oil_city@getProvince');


    //获取油品类型
    $router->post('open_api.zqx.cardmain.getOilCom', 'oil_card_main@getApiOilCom'); //已自测

    //开卡申请
    $router->post('open_api.cardviceapp.search', 'oil_card_vice_app@getList'); //ok

    //新建开卡工单
    $router->post('open_api.cardviceapp.store', 'oil_card_vice_app@store'); //ok

    //开卡工单详情
    $router->post('open_api.cardviceapp.show', 'oil_card_vice_app@show'); //ok

    //卡片相关
    //副卡信息列表
    $router->post('open_api.cardvice.search', 'oil_card_vice@getList'); //ok

    //副卡详情
    $router->post('open_api.cardvice.show', 'oil_card_vice@show'); //ok

    /**
     * 根据卡号获取副卡信息
     * author lpj
     */
    $router->post('open_api.cardvice.getInfoByCardNo', 'oil_card_vice@getInfoByCardNo');

    /**
     * 验证转油卡信息
     * author lpj
     */
    $router->post('open_api.cardvice.validateTransferCard', 'oil_card_vice@validateTransferCard');

    /**
     * 处理转油交易
     * author lpj
     */
    $router->post('open_api.cardvice.handleCardTransferTrade', 'oil_card_vice@handleCardTransferTrade');

    /**
     * 转油轮询
     * author lpj
     */
    $router->post('open_api.cardvice.queryCardTransfer', 'oil_card_vice@queryCardTransfer');

    /**
     * 检查转油锁信息
     * author lpj
     */
    $router->post('open_api.cardvice.checkOilTransferLock', 'oil_card_vice@checkOilTransferLock');

    //修改副卡
    $router->post('open_api.cardvice.update', 'oil_card_vice@modify'); //ok

    //手机车队卡，月消费记录统计
    $router->any('open_api.zqx.cardvicetrades.getTradesMonth', 'oil_card_vice_trades@getTradesMonth');


    //手机车队卡，月消费记录列表
    $router->any('open_api.zqx.cardvicetrades.getTradesAll', 'oil_card_vice_trades@getTradesAll'); //月消费记录，默认当前本月 OKay


    $router->any('open_api.cardvicetrades.getCatFuelTradeType', 'oil_card_vice_trades@getCatFuelTradeType'); //月消费记录，默认当前本月 OKay

    //返利
    $router->post('open_api.cardvicetrades.getFanliMonth', 'oil_card_vice_trades@getFanliMonth'); //月消费记录，默认当前本月 OKay
    $router->post('open_api.cardvicetrades.getFanliAll', 'oil_card_vice_trades@getFanliAll'); //月消费记录，默认当前本月 OKay

    $router->post('open_api.cardvicetrades.getFanliBlanceMonth', 'oil_card_vice_trades@getFanliBlanceMonth'); //月消费记录，默认当前本月 OKay
    $router->post('open_api.cardvicetrades.getFanliBlanceAll', 'oil_card_vice_trades@getFanliBlanceAll'); //月消费记录，默认当前本月 OKay
    $router->post('open_api.cardvicetrades.jifenFanliMonth', 'oil_card_vice_trades@jifenFanliMonth'); //本月积分返利（按上月消费记录返利）
    $router->post('open_api.cardvicetrades.cashFanliMonth', 'oil_card_vice_trades@cashFanliMonth'); //本月现金返利（按上月消费记录返利）
    //通过时间增量获取加油记录
    $router->post('open_api.oil_card_vice_trades.getListByUpdatetime', 'oil_card_vice_trades@getListByUpdatetime');
    $router->post('open_api.oil_card_vice_trades.getIncrementListById', 'oil_card_vice_trades@getIncrementListById');

    //机构账户信息
    $router->post('open_api.accountmoney.myaccountinfo', 'oil_account_money@myAccountInfo'); //已自测

    //获取机构的现金授信账户的可用金额
    $router->post('open_api.accountmoney.getAccountUseMoney', 'oil_account_money@getAccountUseMoney');

    //机构账户信息(接口专用)
    $router->post('open_api.accountmoney.getlist', 'oil_account_money@getList');
    $router->post('open_api.accountmoney.getAccountMoneyBalance', 'oil_account_money@getAccountMoneyBalance');

    //账户操作记录
    $router->post('open_api.accountmoney.getaccountlog', 'oil_account_money@getAccountLog');

    //探测是否开通返回三个状态值完全未开 ，已开无数据，已开有数据
    //手机车队卡，探测油卡机构开通情况
    $router->post('open_api.zqx.org.probe', 'oil_org@probeOrg'); //ok
    //http://dev.zqx.chinawayltd.com/api.php?method=open_api.org.probe&app_key=123123&sign=D3C378D91672C4680AC3B2A11A3AF09D&timestamp=*********&data={%22orgcode%22:%22200I1A%22}

    //机构开通无阻碍
    //手机车队卡，油卡机构开通
    $router->post('open_api.zqx.org.openOrg', 'oil_org@openOrg');


    $router->post('open_api.api_oil.getOilOrgs', 'api_oil@getOilOrgs');

    //門戶油卡類型
    $router->post('open_api.profiles.getG7sOilCom', 'oil_card_main@getG7sOilCom');

    //门户获取壹号卡列表
    $router->post("open_api.api_oil.getFirstCard", 'oil_card_main@getG7sFirstOilCom');

    //充值操作
    $router->post('open_api.oilaccountmoneycharge.store', 'oil_account_money_charge@store');
    $router->post('open_api.oilaccountmoneycharge.search', 'oil_account_money_charge@getList');
    $router->post('open_api.oilaccountmoneycharge.show', 'oil_account_money_charge@show');
    $router->post('open_api.oilaccountmoneycharge.getChargeMonth', 'oil_account_money_charge@getChargeMonth');
    $router->post('open_api.oilaccountmoneycharge.getChargeAll', 'oil_account_money_charge@getChargeAll');
    $router->post('open_api.oilaccountmoneycharge.changeClose', 'oil_account_money_charge@changeClose');
    $router->post('open_api.oilaccountmoneycharge.appQuickPay', 'oil_account_money_charge@appQuickPay');
    $router->post('open_api.oilaccountmoneycharge.paymentStausQuery', 'oil_account_money_charge@paymentStausQuery');

    //发票抬头
    $router->post('open_api.oilreceipttitle.receiptSearch', 'oil_receipt_title@receiptSearch');
    $router->post('open_api.oilreceipttitle.show', 'oil_receipt_title@show');
    $router->post('open_api.oilreceipttitle.receiptAdd', 'oil_receipt_title@receiptAdd');
    $router->post('open_api.oilreceipttitle.receiptUpdate', 'oil_receipt_title@receiptUpdate');
    $router->post('open_api.oilreceipttitle.receiptAddApp', 'oil_receipt_title@createTitle');
    $router->post('open_api.oilreceipttitle.receiptUpdateApp', 'oil_receipt_title@updateTitle');
    $router->post('open_api.oilreceipttitle.receiptDelete', 'oil_receipt_title@receiptDelete');
    $router->post('open_api.oilreceiptquota.getOrgQuota', 'oil_receipt_quota@getOrgQuota');

    //发票申请
    $router->post('open_api.oilreceiptapply.receiptSearch', 'oil_receipt_apply@receiptSearch');
    $router->post('open_api.oilreceiptapply.show', 'oil_receipt_apply@show');
    $router->post('open_api.oilreceiptapply.receiptSearchDataExport', 'oil_receipt_apply@searchDataExport');
    $router->post('open_api.oilreceiptapply.receiptAdd', 'oil_receipt_apply@receiptAdd');
    $router->post('open_api.oilreceiptapply.receiptDelete', 'oil_receipt_apply@receiptDelete');
    $router->post('open_api.oilreceiptapply.receiptEdit', 'oil_receipt_apply@receiptEdit');
    $router->post('open_api.oilreceiptapply.maxAmount', 'oil_receipt_apply@maxAmount');
    $router->post('open_api.oilreceiptapply.getReceiptMin', 'oil_receipt_apply@minAmount');
    $router->post('open_api.oilreceiptapply.getReceiptAll', 'oil_receipt_apply@getReceiptAll'); //获取发票记录月统计
    $router->post('open_api.oilreceiptapply.getReceiptMonth', 'oil_receipt_apply@getReceiptMonth'); //单月查询发票申请记录


    //平台端//////////////////////////////////////////////////////////////////////////////////////////
    $router->post('open_api.api_oil.getMsgByCode', 'api_oil@getMsgByCode');
    $router->post('open_api.api_oil.exitMsg', 'api_oil@exitMsg');
    $router->post('open_api.api_oil.getOilContact', 'api_oil@getOilContact');
    $router->post('open_api.api_oil.addOilContact', 'api_oil@addOilContact');
    $router->post('open_api.api_oil.updateOilContact', 'api_oil@updateOilContact');
    $router->post('open_api.api_oil.delOilContact', 'api_oil@delOilContact');
    $router->post('open_api.api_oil.getOilAddr', 'api_oil@getOilAddr');
    $router->post('open_api.api_oil.addOilAddr', 'api_oil@addOilAddr');
    $router->post('open_api.api_oil.updateOilAddr', 'api_oil@updateOilAddr');
    $router->post('open_api.api_oil.delOilAddr', 'api_oil@delOilAddr');
    $router->post('open_api.api_oil.addOilOrg', 'oil_org@addOrg');
    $router->post('open_api.api_oil.getOilOrgs', 'api_oil@getOilOrgs');
    $router->post('open_api.api_oil.getOilAccountMoney', 'api_oil@getOilAccountMoney');
    $router->post('open_api.api_oil.addViceCards', 'api_oil@addViceCards');
    $router->post('open_api.api_oil.editViceCardsFiles', 'api_oil@editViceCardsFiles');
    $router->post('open_api.api_oil.editViceCardsDriverTel', 'api_oil@editViceCardsDriverTel');
    $router->post('open_api.api_oil.getViceCardsApps', 'api_oil@getViceCardsApps');
    $router->post('open_api.api_oil.formatCardNum', 'api_oil@formatCardNum');
    $router->post('open_api.api_oil.getViceCardsExport', 'api_oil@getViceCardsExport');
    $router->post('open_api.api_oil.getViceCardsAppDetailsExport', 'api_oil@getViceCardsAppDetailsExport');
    $router->post('open_api.api_oil.getViceCardsAppDetails', 'api_oil@getViceCardsAppDetails');
    $router->post('open_api.api_oil.getOilAccountJifen', 'api_oil@getOilAccountJifen');
    $router->post('open_api.api_oil.getOilAccountJifenAssign', 'api_oil@getOilAccountJifenAssign');
    $router->post('open_api.api_oil.getOilAccountJifenDetails', 'api_oil@getOilAccountJifenDetails');
    $router->post('open_api.api_oil.getOilAccountMoneyDetails', 'api_oil@getOilAccountMoneyDetails');
    $router->post('open_api.api_oil.accountMoneyCharge', 'api_oil@accountMoneyCharge');
    $router->post('open_api.api_oil.getAccountMoneyCharges', 'api_oil@getAccountMoneyCharges');
    $router->post('open_api.api_oil.getAccountMoneyChargesExport', 'api_oil@getAccountMoneyChargesExport');
    $router->post('open_api.api_oil.getAccountMoneyTransfer', 'api_oil@getAccountMoneyTransfer');
    $router->post('open_api.api_oil.getAccountMoneyTransferExport', 'api_oil@getAccountMoneyTransferExport');
    $router->post('open_api.api_oil.getCardExpenseTrader', 'api_oil@getCardExpenseTrader');
    $router->post('open_api.api_oil.getAccountAssign', 'api_oil@getAccountAssign');
    $router->post('open_api.api_oil.getcardone', 'api_oil@getcardone');
    $router->post('open_api.api_oil.getAccountAssignDetails', 'api_oil@getAccountAssignDetails');
    $router->post('open_api.api_oil.getAccountAssignExport', 'api_oil@getAccountAssignExport');
    $router->post('open_api.api_oil.getAccountAssignDetailsExport', 'api_oil@getAccountAssignDetailsExport');
    $router->post('open_api.api_oil.accountAssign', 'api_oil@accountAssign');
    $router->post('open_api.api_oil.litreAssign', 'api_oil@litreAssign');
    //查询分配是否已创建
    $router->post("open_api.api_oil.checkAssignOrder", "api_oil@checkAccountAssign");

    $router->post('open_api.api_oil.getViceCardInfoByJifen', 'api_oil@getViceCardInfoByJifen');
    $router->post('open_api.api_oil.getMainCardJifen', 'api_oil@getMainCardJifen');
    $router->post('open_api.api_oil.getViceCardInfo', 'api_oil@getViceCardInfo');
    $router->post('open_api.api_oil.searchcard', 'api_oil@searchcard');
    $router->post('open_api.api_oil.searchViceNo', 'api_oil@searchViceNo');//g7s通用油卡卡号搜索
    $router->post('open_api.oil_card_vice_snapshot.searchViceNoForTrades', 'oil_card_vice_snapshot@searchViceNo');//针对消费记录车牌号查询
    $router->post('open_api.api_oil.searchViceTrades', 'api_oil@searchViceTrades');//g7s查询油卡卡号（为消费查询和油卡消费分析定制）
    $router->post('open_api.api_oil.getOilCom', 'api_oil@getOilCom');
    $router->post('open_api.api_oil.getAccountJifenBalance', 'oil_account_jifen_records@getAccountJifenBalance');
    $router->post('open_api.api_oil.getAccountMoneyBalance', 'api_oil@getAccountMoneyBalance');
    $router->post('open_api.api_oil.getRegisterInfo', 'api_oil@getRegisterInfo');
    $router->post('open_api.api_oil.saveRegister', 'api_oil@saveRegister');
    $router->post('open_api.api_oil.createNew', 'api_oil@createNew');
    $router->post('open_api.api_oil.closeRegister', 'api_oil@closeRegister');
    $router->post('open_api.api_oil.reCreateFull', 'api_oil@reCreateFull');
    $router->post('open_api.api_oil.checkTimeIsInArea', 'api_oil@checkTimeIsInArea');
    $router->post('open_api.api_oil.closeRegisterForOpen', 'api_oil@closeRegisterForOpen');
    $router->post('open_api.api_oil.refreshSearch', 'api_oil@refreshSearch');
    $router->post('open_api.api_oil.moneyCharge', 'api_oil@moneyCharge');
    $router->post('open_api.api_oil.jifenCharge', 'api_oil@jifenCharge');
    $router->post('open_api.api_oil.totalSum', 'api_oil@totalSum');
    $router->post('open_api.api_oil.getTradesByOrgCodePerMonth', 'api_oil@getTradesByOrgCodePerMonth');
    $router->post('open_api.api_oil.getRegisterRecord', 'api_oil@getRegisterRecord');
    $router->post('open_api.api_oil.accountMoneyTransfer', 'api_oil@accountMoneyTransfer');
    $router->post('open_api.api_oil.accountTransfer', 'oil_account_money_transfer@addTransfer');
    $router->post('open_api.api_oil.accountTransferAudit', 'oil_account_money_transfer@cardAudit');
    $router->post('open_api.api_oil.getMethods', 'api_oil@getMethods');
    $router->post('open_api.api_oil.checkPower', 'api_oil@checkPower');
    $router->post('open_api.api_oil.mkFrameDir', 'api_oil@mkFrameDir');
    $router->post('open_api.api_oil.checkSource', 'api_oil@checkSource');
    $router->post('open_api.api_oil.open', 'api_oil@open');
    $router->post('open_api.api_oil.getFormData', 'api_oil@getFormData');
    $router->post('open_api.api_oil.subReplace', 'api_oil@subReplace');
    $router->post('open_api.api_oil.loadModel', 'api_oil@loadModel');
    $router->post('open_api.api_oil.setSuperVars', 'api_oil@setSuperVars');
    $router->post('open_api.api_oil.assign', 'api_oil@assign');
    $router->post('open_api.api_oil.clear', 'api_oil@clear');
    $router->post('open_api.api_oil.parse', 'api_oil@parse');
    $router->post('open_api.api_oil.fetch', 'api_oil@fetch');
    $router->post('open_api.api_oil.display', 'api_oil@display');
    $router->post('open_api.api_oil.render', 'api_oil@render');
    $router->post('open_api.api_oil.createLink', 'api_oil@createLink');
    $router->post('open_api.api_oil.inlink', 'api_oil@inlink');
    $router->post('open_api.api_oil.locate', 'api_oil@locate');
    $router->post('open_api.oilreceiptapply.getList', 'oil_receipt_apply@receiptSearch');
    $router->post('open_api.card.editAccount', 'oil_card_vice@editCard');
    $router->post('open_api.card.setCode', 'oil_card_vice@setCode');
    $router->post('open_api.card.setLock', 'oil_card_vice@setLock');
    $router->post('open_api.card.setUnLock', 'oil_card_vice@setUnLock');
    $router->post('open_api.card.searchAccount', 'oil_pool_account@accountSearch');
    $router->post('open_api.card.accountDetailsSearch', 'oil_pool_account@accountDetailsSearch');

    $router->post('open_api.card.exportTradesForG7s', 'api_oil@exportTradesForG7s');

    $router->post('open_api.api_oil.gasAssignApp', 'oil_gas_assign_app@add');
    $router->post('open_api.api_oil.getGasMoneyOrg', 'oil_gas_assign_app@getGasMoneyOrg');
    $router->post('open_api.api_oil.getMoneyFromOrgUseCash', 'oil_gas_assign_app@getMoneyFromOrgUseCash');
    $router->post('open_api.api_oil.registerTruckNo', 'oil_card_vice@registerTruckNo');
    $router->post('open_api.api_oil.getGasAccount', 'oil_account_money@getGasAccount');
    //手机管车同步接口
    $router->post('open_api.oilcardvicetrades.getTradesList', 'oil_card_vice_trades@getTradesList');
    $router->post('open_api.oilaccountassign.synGspAccountAssign', 'oil_account_assign@synGspAccountAssign');
    $router->post('open_api.oilaccountmoneycharge.synGspAccountCharge', 'oil_account_money_charge@synGspAccountCharge');
    $router->post('open_api.oilcardvice.getCardList', 'oil_card_vice@getCardList');
    $router->post('open_api.account_assign_details.getDetailList', 'oil_account_assign@getDetailList');

    //探测指定机构是否有充值卡或公司卡
    $router->any('open_api.oilcardvice.checkHasSkidCard', 'oil_card_vice@checkHasSkidCard');

    //获取机构关注二维码
    $router->any('open_api.oilorgqrcode.getQrCodeByOrgCode', 'oil_wx_qrcode@getQrCodeByOrgCode');
    $router->any('open_api.oilorgqrcode.getQrListByOrgcode', 'oil_org_wx@getlist');
    $router->any('open_api.oilorgqrcode.changeWxStatus', 'oil_org_wx@changeWxStatus');
    $router->any('open_api.oilorgqrcode.wxRemove', 'oil_org_wx@delete');

    //微信管理
    $router->any('open_api.tempassign.getlist', 'oil_temp_assign@getlist');
    $router->any('open_api.tempassign.create', 'oil_temp_assign@create');
    $router->any('open_api.tempassign.edit', 'oil_temp_assign@edit');
    $router->any('open_api.tempassign.remove', 'oil_temp_assign@remove');

    //卡片设置综合
    $router->post('open_api.api_oil.cardset', 'oil_card_vice@cardSet');

    //根据orgcode获得充值运营商信息
    $router->post('open_api.api_oil.getOperatorsInfo', 'oil_operators@getByOrgcode');

    //机构月统计报表
    $router->post('open_api.api_oil.getOrgMonthStatisticData', 'oil_org@getOrgMonthStatisticData');

    //机构消费统计
    $router->post('open_api.api_oil.getOrgTradeAnalysis', 'oil_org@getOrgTradeAnalysis');
    //油卡消费统计
    $router->post('open_api.api_oil.getCardTradeAnalysis', 'oil_card_vice@getCardTradeAnalysis');
    //车辆消费统计
    $router->post('open_api.api_oil.getTruckTradeAnalysis', 'oil_card_vice@getTruckTradeAnalysis');
    $router->post('open_api.oil_card_vice.getTruckNo', 'oil_card_vice@getTruckNo');
    $router->post('open_api.oil_card_vice.getDriverName', 'oil_card_vice@getDriverName');

    //众邦1号卡验证卡账户
    $router->post('open_api.cardvice.CheckCardAccount', 'oil_card_vice@checkCardAccount');

    //微信油卡相关
    $router->post('open_api.api_oil.accountAssignForWx', 'api_oil@accountAssignForWx');
    $router->post('open_api.api_oil.accountMoneyChargeForWx', 'api_oil@accountMoneyChargeForWx');
    $router->post('open_api.api_oil.getMoneyChargeForWx', 'api_oil@getMoneyChargeForWx'); //近10天的充值记录
    $router->post('open_api.api_oil.accountMoneyChargeRemoveForWx', 'api_oil@accountMoneyChargeRemoveForWx'); //近10天的充值记录

    //标签
    $router->post('open_api.oil_tags.getList', 'oil_tags@getList');
    $router->post('open_api.oil_tags.remove', 'oil_tags@remove');
    //副卡标签
    $router->post('open_api.oil_tags.viceTagAdd', 'oil_tags@viceTagAdd');
    //获取副卡标签
    $router->post('open_api.oil_vice_tags.getList', 'oil_vice_tags@getList');
    //根据标签获取副卡号
    $router->post('open_api.oil_vice_tags.getViceNoByTagId', 'oil_vice_tags@getViceNoByTagId');

    //卡片设置车牌号搜索
    $router->post('open_api.oil_card_vice.getTruckNoForVice', 'oil_card_vice@getTruckNoForVice');

    //授信账户列表
    $router->post('open_api.oil_credit_account.getList', 'oil_credit_account@getList');
    //授信账单列表
    $router->post('open_api.oil_credit_bill.getList', 'oil_credit_bill@getList');
    $router->post('open_api.oil_credit_account.getCreditProviderByOrgcode', 'oil_credit_account@getCreditProviderByOrgcode');
    //授信账单数据
    $router->post('open_api.oil_credit_bill.getBillDataForG7s', 'oil_credit_bill@getBillDataForG7s');
    //扣款账户清单
    $router->post('open_api.oil_account.getDebitAccount', 'oil_account_assign@getDebitAccount');
    //授信账户流水列表
    $router->post('open_api.oil_credit_account_records.getList', 'oil_credit_account_records@getList');
    //获取授信账户可用余额
    $router->post('open_api.oil_account.getCreditAcountBalance', 'oil_account_assign@getCreditAcountBalance');

    //获取G7Pay授信账户可用余额
    $router->post('open_api.oil_account.getCreditBalanceByOrgCode', 'oil_credit_account@getCreditBalanceByOrgCode');

    //查询机构是否有授信能力及可用余额
    $router->post('open_api.oil_account.checkCreditByOrgCode', 'oil_credit_account@checkCreditByOrgCode');

    //授信充值
    $router->post('open_api.creditaccount.moneycharge', 'oil_account_money_charge@creditMoneyCharge');

    //获取G7Pay授信账户可用余额
    $router->post('open_api.oil_account.getCreditBalanceByOrgCode', 'oil_credit_account@getCreditBalanceByOrgCode');

    //获取还款工单
    $router->post('open_api.oil_credit_bill.creditRepayList', 'oil_credit_bill@creditRepayList');

    //获取还款工单详情
    $router->post('open_api.oil_credit_bill.creditRepayDetail', 'oil_credit_bill@creditRepayDetail');

    //微信回调接口
    $router->post('open_api.pay.wxPayReceiveForG7s', 'pay@wxPayReceiveForG7s');

    //可疑套现记录根据
    $router->any('open_api.oil_trades_out.getListByGroup', 'oil_trades_out@getListByGroup');
    $router->any('open_api.oil_trades_out.getList', 'oil_trades_out@getList');

    //挂失卡工单
    $router->any('open_api.oil_card_loss.create', 'oil_card_loss@create');
    $router->any('open_api.oil_card_loss.getList', 'oil_card_loss@getList');

    //卡信息变更工单
    $router->any('open_api.oil_card_change.createForG7s', 'oil_card_change@createForG7s');
    $router->any('open_api.oil_card_change.getList', 'oil_card_change@getList');
    //卡信息变更工单--新增
    $router->any('open_api.oil_card_vice.getAddrByCards', 'oil_card_vice@getAddrByCards');
    $router->any('open_api.oil_card_change.getChangeTypeForG7s', 'oil_card_change@getChangeTypeForG7s');
    $router->any('open_api.oil_card_change.createForBatch', 'oil_card_change@createForBatch');
    $router->any('open_api.oil_card_change.exportForG7s', 'oil_card_change@exportForG7s');

    //申请卡-引流
    $router->any('open_api.oil_card_apply.getList', 'oil_card_apply@getlist');
    $router->any('open_api.oil_card_apply.create', 'oil_card_apply@create');
    $router->any('open_api.oil_card_apply.edit', 'oil_card_apply@edit');
    $router->any('open_api.oil_card_apply.remove', 'oil_card_apply@remove');

    //数据托管卡
    $router->any('open_api.oil_customer_card.getList', 'oil_customer_card@getList');
    $router->any('open_api.oil_customer_card.probeAccount', 'oil_customer_card@probeAccount');
    $router->any('open_api.oil_customer_card.confirmTrusteeship', 'oil_customer_card@confirmTrusteeship');
    $router->any('open_api.oil_customer_card.editAccount', 'oil_customer_card@editAccount');
    $router->any('open_api.oil_customer_card.removeAccount', 'oil_customer_card@removeAccount');
    $router->any('open_api.oil_customer_card.getMainCardByAccountNo', 'oil_customer_card@getMainCardByAccountNo');
    $router->any('open_api.oil_customer_card.getQrByOrgcode', 'oil_customer_card@getQrByOrgcode');
    $router->any('open_api.oil_customer_card.sendSms', 'oil_customer_card@sendSms');
    $router->any('open_api.oil_customer_card.loginZsh', 'oil_customer_card@loginZsh');
    $router->any('open_api.oil_customer_card.zshDeleteAccount', 'oil_customer_card@zshDeleteAccount');

    //卡登记
    $router->any('open_api.oil_card_register.registerTruckNoAndViceNo', 'oil_card_register@registerTruckNoAndViceNo');
    $router->any('open_api.oil_card_register.unBindRegisterByViceNo', 'oil_card_register@unBindRegisterByViceNo');

    // 卡申请 识别图片
    $router->any('open_api.oil_card_apply_details.recognizeLicence', 'oil_card_apply_details@recognizeLicence');
    $router->any('open_api.oil_card_apply_details.updateLicence', 'oil_card_apply_details@updateLicence');
    $router->any('open_api.oil_card_apply_details.recognizeIDCard', 'oil_card_apply_details@recognizeIDCard');
    $router->any('open_api.oil_card_apply_details.getById', 'oil_card_apply_details@show');
    $router->any('open_api.oil_card_apply_details.nextStep', 'oil_card_apply_details@nextStep');
    $router->any('open_api.oil_card_apply_details.getImageList', 'oil_card_apply_details@getImageList');
    $router->any('open_api.oil_card_apply_details.delImage', 'oil_card_apply_details@delImage');
    $router->any('open_api.oil_card_apply_details.findNoFinish', 'oil_card_apply_details@findNoFinish');
    $router->any('open_api.oil_card_apply_details.getApplyById', 'oil_card_apply_details@getApplyById');
    $router->any('open_api.oil_card_apply_details.getLastAddress', 'oil_card_apply_details@getLastAddress');

    //预开卡单列表查询
    $router->any('open_api.oil_card_apply.getApiListData', 'oil_card_apply@getApiListData');
    $router->any('open_api.oil_card_apply.showApi', 'oil_card_apply@showApi');
    $router->any('open_api.oil_card_apply.remove', 'oil_card_apply@remove');
    $router->any('open_api.oil_card_register.registerTruckNoAndViceNo', 'oil_card_register@registerTruckNoAndViceNo');
    $router->any('open_api.oil_card_register.unBindRegisterByViceNo', 'oil_card_register@unBindRegisterByViceNo');

    //挂失卡
    $router->any('open_api.oil_card_loss.getList', 'oil_card_loss@getList');
    $router->any('open_api.oil_card_loss.create', 'oil_card_loss@create');
    $router->any('open_api.oil_card_loss.getCashAccountNoList', 'oil_account_money@getCashAccountNoList');

    //账单月统计
    $router->any('open_api.oil_credit_bill.getStatisticsByOrgCode', 'oil_credit_bill@getStatisticsByOrgCode');
    $router->any('open_api.oil_credit_bill.getStatisticsByOrgCodeExport', 'oil_credit_bill@getStatisticsByOrgCodeExport');
    $router->any('open_api.oil_credit_bill.exportForG7s', 'oil_credit_bill@exportForG7s');
    $router->any('open_api.oil_credit_bill.getOrgCreditInfo', 'oil_credit_bill@getOrgCreditInfo');

    //挂失卡loss
    $router->any('open_api.oil_card_loss.exportForG7s', 'oil_card_loss@exportForG7s');

    //主卡的账户状态
    $router->post('open_api.cardmain.getCardMainAccountStatus', 'oil_card_main@getCardMainAccountStatus');

    //返回主站维护的主卡列表
    $router->any("open_api.cardmain.closeMainCard", "oil_card_main@getCloseMainList");

    //高德地图路线规划
    $router->any("open_api.pathLine.polyline", "oil_pathpoint@polyline");

    //获取代理商佣金记录
    $router->any("open_api.agent.commissionList", "oil_agent_commission@getList");
    //获取代理商开通的机构
    $router->any("open_api.agent.getOpenOrg", "oil_agent_commission@getRootOrg");

    //自动认领
    $router->post('open_api.claim.autoClaim', 'oil_bank_records@autoClaim');

    //////////////////////////////////////////销售工具////////////////////////////////////////////////

    //$router->post('open_api.company.create', 'oil_company@createByG7s');
    $router->post('open_api.company.getByCreditCode', 'oil_company@getByCreditCode');
    $router->post('open_api.company.getListBySalesMen', 'oil_company@getListBySalesMen');
    $router->post('open_api.company.Show', 'oil_company@show');
    $router->post('open_api.company.Login', 'oil_company@login'); //是否考虑做到gos
    $router->post('open_api.company.getOrgInfoByUsername', 'oil_company@getOrgInfoByUsername'); //是否考虑做到gos
    $router->post('open_api.company.getOrgInfoByCompanyName', 'oil_company@getOrgInfoByCompanyName'); //是否考虑做到gos

    //////////////////////////////////////////众邦zbank////////////////////////////////////////////////
    $router->post('open_api.card.getZbankOpenNum', 'oil_card_vice_app@getZbankOpenNum'); //根据orgcode获取众邦可开户数量


    //////////////////////////////////////////自助发卡////////////////////////////////////////////////
    $router->post('open_api.auto.operationTemplate', 'oil_card_apply_template@create');//新建或修改
    $router->post('open_api.auto.updateTemplateStatus', 'oil_card_apply_template@updateStatus');//作废
    $router->any('open_api.auto.detailInfo', 'oil_card_apply_template@show');//获取详情
    $router->any('open_api.auto.listTemplate', 'oil_card_apply_template@getList');//获取列表


    $router->any('open_api.precard.list', 'oil_pre_card_apply@getAuditList');//获取待审核列表
    $router->post('open_api.precard.create', 'oil_pre_card_apply@create');//司机申办卡片
    $router->post('open_api.precard.audit', 'oil_pre_card_apply@auditBy');//审核预开卡申请单
    $router->post('open_api.precard.reject', 'oil_pre_card_apply@unAuditBy');//驳回预开卡申请单

    //1号共享卡扣款账户选择列表
    $router->post('open_api.card.oneShareCardDeductionType', 'oil_card_vice@oneShareCardDeductionType'); //根据orgcode获取1号共享卡扣款账户选择列表

    //卡授信账户列表
    $router->post('open_api.card.getCardBalanceList', 'oil_account_money@getCardBalanceList');//卡授信账户列表
    $router->post('open_api.card.changeCardAccountNo', 'oil_card_vice@changeCardAccountNo');//卡设置扣款账户
    $router->post('open_api.card.getDriverAllCardAccount', 'oil_card_vice@getDriverAllCardAccount');//扫码加油所有卡账户列表
    $router->post('open_api.card.getServiceMoney', 'oil_account_assign@getServiceMoney');//获预支付取服务费

    //获取卡对应的扣款账户状态
    $router->post('open_api.card.getCardAccountStatus', 'oil_card_vice@getCardAccountStatus');//获预支付取服务费

    // 机构卡片返利设置 lpj add
    $router->post('open_api.card.saveOrgFanliSetting', 'oil_orgfanli_setting@saveOrgFanliSetting');//创建机构卡片返利设置
    $router->any('open_api.card.getOrgFanliSetting', 'oil_orgfanli_setting@getOrgFanliSetting'); // 获取机构返利设置信息
    $router->any('open_api.card.checkOrgFanliSetting', 'oil_orgfanli_setting@checkOrgFanliSetting'); // 检查机构返利设置

    $router->any('open_api.org.getGspSubOrgs', 'oil_org@getSubOrgs'); // 查询本级及其所有子级机构

});
