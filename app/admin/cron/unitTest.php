<?php
/**
 * Created by PhpStorm.
 * User: tim
 * Date: 19-7-25
 * Time: 上午11:49
 */

/* composer autoloader */
define('APP_ROOT', realpath(dirname(__DIR__)));
define('APP_CONFIG', APP_ROOT . DIRECTORY_SEPARATOR . 'config');
define('APP_MODULE_ROOT', APP_ROOT . DIRECTORY_SEPARATOR . 'module');
define('APP_WWW_ROOT', APP_ROOT . DIRECTORY_SEPARATOR . 'www');
define('APP_LIB_ROOT', APP_ROOT . DIRECTORY_SEPARATOR . 'library');
define('APP_SERVICE_ROOT', APP_LIB_ROOT . DIRECTORY_SEPARATOR . 'Fuel'.DIRECTORY_SEPARATOR.'Service');
define('GOS_SDK_CONFIG', APP_CONFIG . DIRECTORY_SEPARATOR . 'gosSdk.php');
require_once APP_ROOT . DIRECTORY_SEPARATOR . 'vendor' . DIRECTORY_SEPARATOR . 'autoload.php';

/* 包含必须的类文件。*/
include_once dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR
    . 'dubhe-client-php' . DIRECTORY_SEPARATOR . 'Du.php';
//include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'error.class.php';
include_once APP_LIB_ROOT . DIRECTORY_SEPARATOR . 'Framework'.DIRECTORY_SEPARATOR.'Exception'.DIRECTORY_SEPARATOR.'RegisterErrorHandler.php';

include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'router.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'control.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'model.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'helper.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'admin' . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'base.control.class.php';
include dirname(dirname(dirname(dirname(__FILE__)))) . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'admin' . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'cliargs.php';

require_once dirname(dirname(__FILE__)) . DIRECTORY_SEPARATOR . 'module' . DIRECTORY_SEPARATOR . 'client' . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'config.inc.php';


// console init framework env
CliArgs::initFrameworkEnv();
CliArgs::loadArg($argv, $argc);

/* 实例化路由对象，并加载配置，连接到数据库，加载common模块。*/
$app = router::createApp('gos', dirname(dirname(__FILE__)));
$config = $app->loadConfig('common');
$common = $app->loadCommon();
define('API_ENV', $config->api_env); //api环境

$moduleMap = [];
$pathInfo  = isset($argv[1]) ? $argv[1] : [];
if ($pathInfo) {
    $pathMap = explode("&", $pathInfo);
    if ($pathMap) {
        foreach ($pathMap as $v) {
            $_module = explode("=", $v);
            if (count($_module) == 2) {
                $moduleMap[$_module[0]] = $_module[1];
            }
        }
    }
}

try {
    if (!class_exists("\\UnitTest\\" . $moduleMap["m"])) {
        throw new \RuntimeException($moduleMap["m"] . ":不存在", 404001);
    }

    $classObj = new \ReflectionClass("\\UnitTest\\" . $moduleMap["m"]);
    $instance = $classObj->newInstance();
    $functionName = $moduleMap['f'];
    $result = $instance->$functionName();

    echo json_encode(['code'=>0,'data'=>$result,'msg'=>'']);
    die;
} catch (\Exception $e) {
    echo json_encode(['code' => $e->getCode(), 'msg' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
    die;
}

/* 获取系统时间，微秒为单位。*/
function _getTime()
{
    list($usec, $sec) = explode(" ", microtime());

    return ((float)$usec + (float)$sec);
}