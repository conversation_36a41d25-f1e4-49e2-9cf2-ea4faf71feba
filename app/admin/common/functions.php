<?php
use Framework\Config;
use Illuminate\Database\Capsule\Manager as Capsule;
/**
 * @desc 判断用户身份
 * @return array
 * */
function authCheck(){

//    $cookieValue    = session_id();
//    $user_info      = \Framework\Session::get($cookieValue);
    global $app;
    $user_info = $app->myAdmin;
    $user_type      = $user_info->user_type;
    $operator_id    = $user_info->user_type && $user_info->operator_id ? $user_info->operator_id : 0;

    return [$user_type,$operator_id];
}


function dataRangeSqlObj(Illuminate\Database\Eloquent\Builder &$sqlObj,string $table,$type=''){
    list($user_type,$operator_id)  = authCheck();
    if ($user_type == 0 || $operator_id == 0){//平台用户不做数据范围限制
        return $sqlObj;
    }

    $config = \Fuel\Defines\DataRange::getSqlObject();
    $type  = $type ?: 'default';
    if (empty($config[$table][$type])){//数据表不做数据范围限制
        return $sqlObj;
    }
    foreach ($config[$table][$type] as $field){
        $sqlObj->where($field ,'=',$operator_id);
    }
    return $sqlObj;

}

/**
 * @desc 从配置文件获取数据范围查询条件
 * @param model $model 数据模型
 * @param string $table 标识-通常是表名
 * @param string $type 标识
 * @return string
 * */
function dataRangeSqlStr(string $tableName,$version='',$value = ''){

    list($user_type,$operator_id)  = authCheck();
    if ($user_type == 0){//平台用户不做数据范围限制
        return '';
    }

    $config = \Fuel\Defines\DataRange::getSqlString();

    $version  = $version ?: 'default';
    if (empty($config[$tableName][$version])){//未获取到配置，不做数据范围限制
        return '';
    }

    $sql_where = eval($config[$tableName][$version]);

    return ' '.$sql_where.' ';
}


/**
 * @desc 获取省份配置字典
 * */
function provinceDict($cache = true){

    $key = 'province_dict_foss';

    $dict =  \Framework\Cache::get($key);

    if ($cache == true && $dict){
        return json_decode($dict,true);
    }

    $province = Capsule::connection()->table('oil_provinces')->pluck('province','id');

    if (!empty($province)){
        \Framework\Cache::put($key,json_encode($province,256),3600);
    }
    return $province;

}


