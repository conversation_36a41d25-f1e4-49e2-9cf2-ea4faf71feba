/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_download';
var pagesize = 50;

/**
 * 生成URL
 */
function getUrl(m, f) {
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}

var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName, 'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id", "jobs_id", "rate", "channel", "project", "filename", "filetype", "filesize", "url", "down_count", "status",
            "_status", "message", "queue_num", "downtime", "createtime", "updatetime", "createuser",]
    ),
});