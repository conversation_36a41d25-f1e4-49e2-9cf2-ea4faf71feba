function GetDate($flag) {
    var dd = new Date();
    var y = dd.getFullYear();
    var m = dd.getMonth() + 1;//获取当前月份的日期
    var d = '';
    if ($flag == 1) {
        d = "01";
    } else if ($flag == 2) {
        d = dd.getDate();
    } else if ($flag == 3) {
        var now = new Date();
        var date = new Date(now.getTime() - 24 * 3600 * 1000);
        y = date.getFullYear();
        m = date.getMonth() + 1;
        d = date.getDate();
    }
    if (m.toString().length == 1) {
        m = '0' + m;
    }
    if (d.toString().length == 1) {
        d = '0' + d;
    }

    return y + "-" + m + "-" + d;
}

var top_panel = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 70,
    items:
        [
            {//第一行
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '机构：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 180,
                            listWidth: 350,
                            id: 'org_ids',
                            triggerAction: 'all',
                            allowBlank: false,
                            blankText: '不能为空',
                            mode: 'remote',
                            minChars: 2,
                            queryParam: 'keyword',
                            hiddenName: 'org_id',
                            displayField: 'org_name',//显示的值
                            valueField: 'id',//后台接收的key
                            store: getOilOrg,
                            forceSelection: true,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                            listeners: {
                                'select': function (combo, record, index) {
                                    Ext.getCmp('orgcode').setValue(record.data.orgcode);
                                },
                            },
                        }),
                        {
                            xtype: 'textfield',
                            hidden: true,
                            id: 'orgcode',
                            name: 'hiddenOrgCode',
                            width: 180
                        },

                        {
                            xtype: 'displayfield',
                            value: '每日开票：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 114,
                            hiddenName: "is_recepit_nowtime",
                            editable: true,
                            emptyText: '全部',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'name',
                            valueField: 'value',
                            store: new Ext.data.SimpleStore({
                                fields: ['name', 'value'],
                                data: [['全部', ''], ['否', '1'], ['是', '2']]
                            })
                        },


                        {
                            xtype: 'displayfield',
                            value: '首次开票：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 114,
                            hiddenName: "is_first_receipt_apply",
                            editable: true,
                            emptyText: '全部',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'name',
                            valueField: 'value',
                            store: new Ext.data.SimpleStore({
                                fields: ['name', 'value'],
                                data: [['全部', ''], ['否', '1'], ['是', '2']]
                            })
                        },
                        {
                            xtype: 'displayfield',
                            value: '开票资料：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 80,
                            hiddenName: "is_have_corp",
                            editable: true,
                            emptyText: '请选择..',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'name',
                            valueField: 'value',
                            store: new Ext.data.SimpleStore({
                                fields: ['name', 'value'],
                                data: [['全部', ''], ['否', '1'], ['是', '2']]
                            })
                        },
                        {
                            xtype: 'displayfield',
                            value: '销售运营商：',
                            width: 80,
                        },
                        {
                            xtype: 'combo',
                            width: 220,
                            id: 'operators_id',
                            hiddenName: "operators_id",
                            editable: true,
                            emptyText: '请选择..',
                            triggerAction: 'all',
                            displayField: 'company_name',
                            valueField: 'id',
                            store: new Ext.data.Store({
                                url: '../inside.php?t=json&m=oil_operators&f=getlist&c_type=1&status=1',
                                reader: new Ext.data.JsonReader({
                                    totalProperty: 'data.total',
                                    root: 'data.data'
                                }, ['id', 'name','company_name'])
                            })
                        },
                        /*{
                            xtype: 'displayfield',
                            value: '专属客服：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 80,
                            hiddenName: "exclusive_custom",
                            value: '',
                            triggerAction: 'all',
                            forceSelection: true,
                            emptyText: '请选择..',
                            displayField: 'value',
                            valueField: 'key',
                            store: getExclusiveCustom
                        }),*/


                    ]
            },
            {//第2行
                xtype: 'compositefield',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '付款公司：',
                        width: 60,
                    },
                    {
                        xtype: 'textfield',
                        id: 'pay_company_name',
                        name: 'pay_company_name',
                        width: 180
                    },
                    {
                        xtype: 'displayfield',
                        value: '消费时间：',
                        width: 60,
                    },
                    {
                        xtype: "datefield",
                        id: "s_start_time",
                        name: 'createtimeGe',
                        format: 'Y-m-d H:i:s',
                        width: 140
                    },
                    {
                        xtype: 'displayfield',
                        value: '~'
                    },
                    {
                        xtype: "datefield",
                        id: "s_end_time",
                        name: 'createtimeLe',
                        format: 'Y-m-d H:i:s',
                        width: 140
                    },
                    {
                        xtype: 'displayfield',
                        value: '测试机构：',
                        width: 60,
                    },
                    {
                        xtype: 'combo',
                        width: 80,
                        hiddenName: "is_test",
                        id: "s_is_test",
                        editable: true,
                        emptyText: '请选择..',
                        mode: 'local',
                        triggerAction: 'all',
                        displayField: 'name',
                        valueField: 'value',
                        store: new Ext.data.SimpleStore({
                            fields: ['name', 'value'],
                            data: [['全部', ''], ['否', '1'], ['是', '2']]
                        })
                    },
                    {
                        xtype: 'displayfield',
                        value: '&nbsp;',
                        width: 60,
                    },
                    {
                        xtype: 'button',
                        text: '查询',
                        handler: function () {
                            var orgId = Ext.getCmp('org_ids').getValue();
                            if (orgId.length <= 0) {
                                Ext.MessageBox.alert("提示", "请选择机构");
                                return false;
                            }
                            main_store.removeAll();//移除原来的数据
                            main_store.load();//加载新搜索的数据
                        }
                    },
                    {
                        xtype: 'button',
                        text: '重置',
                        style: 'padding-left : 10px;',
                        handler: function () {
                            top_panel.getForm().reset();
                            main_store.removeAll();//移除原来的数据
                            //main_store.load();//加载新搜索的数据
                            //btnInit();
                        }
                    },
                ]
            }
        ]
});