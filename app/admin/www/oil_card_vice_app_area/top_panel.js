var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 50,
	items  : 
	[
	     {
			xtype : 'compositefield',
			items : 
			[
				{
					xtype: 'displayfield',
					value: '油卡类型：'
				},
				{
					xtype: 'combo',
					width: 120,
					name: 'oil_com',
					id: "oil_com",
					hiddenName: 'oil_com',
					mode: 'local',
					triggerAction:'all',
					displayField: 'oil_com',
					valueField: 'key',
					store: new Ext.data.SimpleStore({
						fields: ['oil_com', 'key'],
						data: [['中石油', '2'],['中石化', '1'],['柴油专用卡', '52'],['电子卡-中石油', '26']]
					})
				},
			
				{
					xtype:'button',
					text:'查询',
					style : 'padding-left : 10px;',
					handler: function()
					{
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
					}
				},
				{
					xtype: 'button',
					text: '重置',
					style: 'padding-left : 10px;',
					handler: function () {
						top_panel.getForm().reset();
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
						btnInit();
					}
				},
			]
		 }	
	]
});