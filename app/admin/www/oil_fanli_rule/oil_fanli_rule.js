/**
 * 全局类
 */
var crow = "";
function oiltest() {
    //列表表格
    this.getGridPanel = function () {
        var _sm =new Ext.grid.RowSelectionModel({
            // singleSelect: false,
            listeners: {
                selectionchange: function(data) {
                    // btnagree,btndisagree,btndisabled
                    var countNum = data.getCount();
                    if (countNum){
                        (Ext.getCmp('btndel')) ? Ext.getCmp('btndel').enable() : '';//删除按钮
                        (Ext.getCmp('btnagree')) ? Ext.getCmp('btnagree').enable() : '';//通过按钮
                        (Ext.getCmp('btndisagree')) ? Ext.getCmp('btndisagree').enable() : '';//销审按钮
                        (Ext.getCmp('btndisabled')) ? Ext.getCmp('btndisabled').enable() : '';//禁用按钮

                        var sm   = grid_list.getSelectionModel();
                        var data = sm.getSelections();
                        crow = data;

                        switch (data[0].data.status){
                            case 1: //生效
                                (Ext.getCmp('btndel')) ? Ext.getCmp('btndel').disable() : '';//删除按钮
                                (Ext.getCmp('btnagree')) ? Ext.getCmp('btnagree').disable() : '';//通过按钮
                                break;
                            case 2: //失效
                                (Ext.getCmp('btnagree')) ? Ext.getCmp('btnagree').disable() : '';//通过按钮
                                (Ext.getCmp('btndisagree')) ? Ext.getCmp('btndisagree').disable() : '';//销审按钮
                                (Ext.getCmp('btndisabled')) ? Ext.getCmp('btndisabled').disable() : '';//禁用按钮
                                break;
                            default: //待审核
                                (Ext.getCmp('btndisagree')) ? Ext.getCmp('btndisagree').disable() : '';//销审按钮
                                (Ext.getCmp('btndisabled')) ? Ext.getCmp('btndisabled').disable() : '';//禁用按钮
                        }
                    }else{
                        (Ext.getCmp('btndel')) ? Ext.getCmp('btndel').disable() : '';//删除按钮
                        (Ext.getCmp('btnagree')) ? Ext.getCmp('btnagree').disable() : '';//通过按钮
                        (Ext.getCmp('btndisagree')) ? Ext.getCmp('btndisagree').disable() : '';//销审按钮
                        (Ext.getCmp('btndisabled')) ? Ext.getCmp('btndisabled').disable() : '';//禁用按钮
                    }
                }
            }
        });

        var grid_list = new Ext.grid.GridPanel({
            title	 :'客户返利规则维护',
            region	 : 'center',
            loadMask : true,
            cm : new Ext.grid.ColumnModel([
                {header : '返利编号',dataIndex : 'rule_no',width : 100},
                {header : '规则名称',dataIndex : 'rule_name',width : 200},
                {header : '状态',dataIndex : 'status_txt',width : 100,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                        var status = record.data.status;
                        if(status == 2){//按阶梯返利
                            return '<a href="javascript:void(0)" style="color:red">'+record.data.status_txt+'</a>';
                        }else if(status == 1){
                            return '<a href="javascript:void(0)" style="color:green">'+record.data.status_txt+'</a>';
                        }else{
                            return record.data.status_txt;
                        }
                }},
                {header : '返还形式',dataIndex : 'way_txt',width : 180},
                {header : '适用对象',dataIndex : 'object_txt',width : 180},
                {header : '适用对象周期',dataIndex : 'expire_time',width : 200},
                {header : '操作日志',dataIndex : 'log_num',width : 100},
            ]),
            listeners: {
                "rowclick" : rowClick
            },
            store : main_store,
            sm :_sm,
            //分页
            bbar: new Ext.PagingToolbar({
                plugins: new Ext.ux.plugin.PagingToolbarResizer,
                pageSize: pagesize,
                displayInfo : true,
                displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
                emptyMsg : '没有符合条件的记录',
                store : main_store
            }),
            tbar : []
        });

        return grid_list;
    };

    //关闭窗口
    this.closeWin = function (id) {
        if (!id) return;
        var win = Ext.getCmp(id);
        win.destroy();
    };

    var _getWindow = function (config) {
        var width = config.width,
            height = config.height,
            title = config.title,
            id = config.id,
            button = config.button;

        button.push({
            text: '取消',
            id: 'button_cancer',
            handler: function () {
                var oilTest = new oiltest();
                oilTest.closeWin(id);
            }
        });
        var windows = new Ext.Window({
            layout: 'border',
            region: 'center',
            width: width,
            modal: true,//遮挡窗口后的内容
            id: id,
            height: height,
            title: title,
            closeAction: 'destroy',
            buttons: button
        });

        return windows;
    };

    //添加
    this.add = function () {
        var windows = _getWindow({
            title: '添加-oil_fanli_rule',
            id: 'add_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'add-form',
            items: addPanel.getPanel('add-form', 'add_win')
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };

    //编辑
    this.update = function () {
        var sm   = grid_list.getSelectionModel();
        var data = sm.getSelections();
        data[0] ? id = data[0].get("id") : '';

        var windows = _getWindow({
            title: '编辑-oil_fanli_rule',
            id: 'update_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'update-form',
            items: addPanel.getPanel('update-form', 'update_win',id)
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init(data[0]);
    };

    //删除
    this.delete = function () {
        Ext.MessageBox.confirm('删除', '删除之后该条纪录将不再显示，确定要删除？', function showResult(btn){
            if (btn == 'yes')
            {
                operationFanliRule("isDel");
            }
        });
    };
    //导出
    this.export = function () {
        /*Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn == 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = '/inside.php?t=json&m='+ controlName +'&f=getList&_export=1'+'&'+params;
                }
            });*/
        var params = top_panel.getForm().getValues();
        params._export = 1;
        Ext.Ajax.request({
            url:'/inside.php?t=json&m='+ controlName +'&f=getList',
            method:'post',
            params:params,
            success: function sFn(response,options)
            {
                var url = Ext.decode(response.responseText).data.redirect_url;
                console.log(url);
                window.open(url);

                var msg = Ext.decode(response.responseText).msg;
                Ext.Msg.alert('提示',msg);
            }
        });
    };

    //导入
    this.import = function () {
        var form = new Ext.form.FormPanel({
            baseCls: 'x-plain', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
            items: [
                {
                    xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
                    id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
                    anchor: '100%'
                }
            ]
        });
        var importData  = new Ext.Window({
            title: '导入返利规则', width: 400, height: 105, minWidth: 300, minHeight: 100, modal: true,
            layout: 'fit', plain: true, bodyStyle: 'padding:5px;', buttonAlign: 'center', items: form,
            buttons: [{
                text: '导入',
                handler: function () {
                    if (form.form.isValid()) {
                        if (Ext.getCmp('userfile').getValue() == '') {
                            Ext.Msg.alert('系统提示', '请选择你要上传的文件');
                            return;
                        }
                        form.getForm().submit({
                            url: '/inside.php?t=json&m=oil_fanli_rule&f=batchImport',
                            success: function (form, action) {
                                main_store.removeAll();
                                main_store.load();
                                Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).msg);
                                importData.close();
                            },
                            failure: function (form, action) {
                                Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).msg);
                            }
                        })
                    }
                }
            }, {
                text: '关闭',
                handler: function () {
                    importData.close();
                }
            }]
        });
        importData.show();
    };

    //探测
    this.explore = function () {
        Ext.MessageBox.confirm('探测未维护返利规则', "确定要探测吗？", function showResult(btn) {
                if (btn == 'yes') {
                    Ext.Ajax.request({
                        url:'/inside.php?t=json&m='+ controlName +'&f=checkPolicy',
                        method:'post',
                        params:params,
                        success: function sFn(response,options)
                        {
                            var msg = Ext.decode(response.responseText).msg;
                            Ext.Msg.alert('提示',msg);
                        }
                    });
                }
            }
        );
    };
    //通过
    this.agree = function () {
        Ext.MessageBox.confirm('导出', "确定要通过吗？", function showResult(btn) {
                if (btn == 'yes') {
                    operationFanliRule("agree");
                }
            }
        );
    };
    //销审
    this.disagree = function () {
        Ext.MessageBox.confirm('导出', "确定要销审吗？", function showResult(btn) {
                if (btn == 'yes') {
                    operationFanliRule("disagree");
                }
            }
        );
    };
    //禁用
    this.disabledRule = function () {
        Ext.MessageBox.confirm('导出', "确定要禁用吗？", function showResult(btn) {
                if (btn == 'yes') {
                    operationFanliRule("disabled");
                }
            }
        );
    };
    //下载模板
    this.downTemplate = function (){
        Ext.MessageBox.confirm('返利规则模板下载', '确认下载?', function showResult(btn) {
            if (btn == 'yes') {
                window.location.href = '/download/templates/fanlirule.xls';
                // window.location.href = '../inside.php?t=json&m=oil_fanli_rule&f=tpldownload';
            }
        });
    }
}
var oilTest = new oiltest();

function operationFanliRule(type)
{
    var ids = crow[0].data.id;
    /*for (var i = 0; i < data.length; i++) {
        if (ids === '') {
            ids = data[i].get('id');
        } else {
            ids += ',' + data[i].get('id');
        }
    }*/
    Ext.Ajax.request({
        url:getUrl(controlName,'operationFanli'),
        method:'post',
        params:{ids:ids,type:type},
        success: function sFn(response,options)
        {
            var result = Ext.decode(response.responseText);
            if(result.code == 0) {
                main_store.removeAll();
                main_store.load();
                Ext.Msg.alert('系统提示', result.msg);
            }else{
                Ext.Msg.alert('系统提示', result.msg);
            }
        }
    });
}