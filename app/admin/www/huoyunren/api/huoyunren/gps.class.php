<?php

if(!defined('WEB_ROOT')) {
	define('WEB_ROOT', substr(dirname(__FILE__), 0, -13));
}

/**
 * GPS 回放 跟踪
 *
 */
class gps {	
	public $gpsno=0;
	private $tpl= '';
	private $client=NULL;
	private static $mmap = array('mbar','mapabc','baidu');
		
	public function __construct() {
		error_reporting(0);
//		$this->gpsno = (int)trim($gpsno);		
		$this->tpl   = WEB_ROOT.'./template/api/';	
		
	}
	/**
	 * 跟踪显示
	 *
	 * @param string $remark
	 */

	public function follow($gpsno='',$gpsid='',$title='',$remark='',$zoom=13,$mmap='',$postmarker=0,$message='',$map=''){
		//模板显示提示信息
		$pmarker    = $postmarker ? $postmarker : 0;//标点
		$cache = 0 ;
		if($gpsno == $title)
			$cache=1;
		$info = array(
			'gpsno'=>$gpsno,
			'gpsid'=>$gpsid,
			'title'=>trim($title),
			'remark'=>$remark,
			'message'=>$message,
			'postmarker'=>$pmarker,
			'cache'=>$cache,
			'map'=>$map ? $map :'google'
		);
		$info=self::charseticonv($info);//Story #45390
		$mmap = trim($mmap);
		$mapkey = $this->getMapKey($mmap);
		if($mmap && in_array($mmap,self::$mmap))
			$tpl = $this->tpl.'./follow_'.$mmap.'.htm';	
		else 
			$tpl = $this->tpl.'./follow.htm';
			
		include($tpl);
	}
	
	/**
	 * 回放显示
	 *
	 * @param string $title
	 * @param string $starttime
	 * @param string $endtime
	 * @param string $remark
	 * @param int $autoplay
	 */

	public function replay($gpsno='',$gpsid='',$title='',$autoplay=1,$start_time='',$end_time='',$min_time='',$max_time='',$remark='',$zoom=13,$mmap='',$postmarker=0,$message='',$map='',$idistance=0,$carnum='',$trucktype=0) {
		$_starttime = $start_time ? date('Y-m-d H:i',strtotime($start_time)) : date('Y-m-d',time()-24*3600).' 00:00';
		$_endtime   = $end_time   ? date('Y-m-d H:i',strtotime($end_time)) : date('Y-m-d H:i',time());
		$pmarker    = $postmarker ? $postmarker : 0;//标点
		$_min_time = $min_time ? strtotime($min_time) : (strtotime($_starttime)-7*24*3600);
		$_max_time = $max_time ? strtotime($max_time) : time();

		$info = array(
			'gpsid'=>$gpsid,
		    'gpsno'=>$gpsno,
			'title'=>trim($title),
			'carnum'=>$carnum,
			'trucktype'=>$trucktype,
			'starttime'=>$_starttime,
			'endtime' =>$_endtime,
			'autoplay'=>$autoplay,
			'remark'=>$remark,
			'message'=>$message,
			'min_time'=>$_min_time,//最小时间，能回放的最小时间，默认前7天，例：2010-10-01 12:30:00
			'max_time'=>$_max_time,//最大时间，能回放的最大时间，默认当天，例：2010-10-03 23:30:21
			'postmarker'=>$pmarker,
			'idistance'=>$idistance,
			'map'=>$map ? $map : 'google'
		);

		$info=self::charseticonv($info);
		$mmap = trim($mmap);
		$mapkey = $this->getMapKey($mmap);		
		if($mmap && in_array($mmap,self::$mmap))
			$tpl = $this->tpl.'./replay_'.$mmap.'.htm';
		else 
			$tpl = $this->tpl.'./replay.htm';			
		include($tpl);
	}
	
	/**
	 * 
	 * IPS2 发车计划回放 
	 * liyonghua 2012-09-14
	 * @param unknown_type $gpsno
	 * @param unknown_type $gpsid
	 * @param unknown_type $title
	 * @param unknown_type $autoplay
	 * @param unknown_type $start_time
	 * @param unknown_type $end_time
	 * @param unknown_type $min_time
	 * @param unknown_type $max_time
	 * @param unknown_type $remark
	 * @param unknown_type $zoom
	 * @param unknown_type $mmap
	 * @param unknown_type $postmarker
	 * @param unknown_type $message
	 */
	public function postline($gpsno='',$gpsid='',$title='',$autoplay=1,$start_time='',$end_time='',$min_time='',$max_time='',$remark='',$zoom=13,$mmap='',$postmarker=0,$message='',$map='') {
	
		$_starttime = $start_time ? date('Y-m-d H:i',strtotime($start_time)) : date('Y-m-d',time()-24*3600).' 00:00';
		$_endtime   = $end_time   ? date('Y-m-d H:i',strtotime($end_time)) : date('Y-m-d H:i',time());
		$pmarker    = $postmarker ? $postmarker : 0;//标点
		$_min_time = $min_time ? strtotime($min_time) : (strtotime($_starttime)-7*24*3600);
		$_max_time = $max_time ? strtotime($max_time) : time();
		
		$info = array(
			'gpsid'=>$gpsid,
		    'gpsno'=>$gpsno,
			'title'=>trim($title),
			'starttime'=>$_starttime,
			'endtime' =>$_endtime,
			'autoplay'=>$autoplay,
			'remark'=>$remark,
			'message'=>$message,
			'min_time'=>$_min_time,//最小时间，能回放的最小时间，默认前7天，例：2010-10-01 12:30:00
			'max_time'=>$_max_time,//最大时间，能回放的最大时间，默认当天，例：2010-10-03 23:30:21
			'postmarker'=>$pmarker,
			'map'=>$map ? $map : 'google'
		);
		$mmap = trim($mmap);
		$mapkey = $this->getMapKey($mmap);			
		$tpl = $this->tpl.'./replay_postline_baidu.htm';			
		include($tpl);
	}
	/**
	 * 中国邮政 回放
	 *
	 * @param string $title
	 * @param string $starttime
	 * @param string $endtime
	 * @param string $remark
	 * @param int $autoplay
	 */
	public function postreplay($gpsno,$title='',$autoplay=0,$start_time='',$end_time='',$min_time='',$max_time='',$remark='',$zoom=12) {
		
		$_starttime = $start_time ? date('Y-m-d H:i',strtotime($start_time)) : date('Y-m-d',time()-24*3600).' 00:00';
		$_endtime   = $end_time   ? date('Y-m-d H:i',strtotime($end_time)) : date('Y-m-d H:i',time());
		
		$_min_time = $min_time ? strtotime($min_time) : (strtotime($_starttime)-45*24*3600);//45天之前的数据
		$_max_time = $max_time ? strtotime($max_time) : time();
		
		$info = array(
		    'gpsno'=>$gpsno,
			'title'=>trim($title),
			'starttime'=>$_starttime,
			'endtime' =>$_endtime,
			'autoplay'=>$autoplay,
			'remark'=>$remark,
			'min_time'=>$_min_time,//最小时间，能回放的最小时间，默认前45天，例：2010-10-01 12:30:00
			'max_time'=>$_max_time//最大时间，能回放的最大时间，默认当天，例：2010-10-03 23:30:21
		);
		$mapkey = $this->getMapKey();		
		include($this->tpl.'./postreplay.htm');
	}
	
	/**
	 * 回放数据
	 *
	 * @param int $gpsno
	 * @param string $starttime
	 * @param string $endtime
	 * @param int $page
	 * @return unknown
	 */
	public function getReplayData (){
		$gpsno = $_GET['gpsno'] ? (int)trim($_GET['gpsno']) :0;
		$gpsno = self::charseticonv($gpsno);
		$trucktype = $_GET['trucktype'];
		$carnum = $_GET['carnum'];
		$gpsid = isset($_GET['gpsid']) ? $_GET['gpsid'] :'';
		$starttime = trim($_GET['starttime']).':00';
	    $endtime = trim($_GET['endtime']).':00';
	    $idistance = $_GET['idistance'] ? (int)$_GET['idistance'] : 0;
		$page  = $_GET['page'] ? (int)$_GET['page'] : 1;
		$postarr = array(
				'gpsno'=>$gpsno,
				'from'=>$starttime,
				'to'=>$endtime,
				'idistance'=>$idistance,
				'page_no'=>$page,
				'page_size'=>1000
				);
		if($trucktype == 1){
			unset($postarr['gpsno']);
			$postarr['carnum'] = trim($carnum);
		}
		if($gpsid)
			$postarr['imei'] = $gpsid;
		if($_GET['map']=='baidu')
			$postarr['map'] = 'baidu';
		$client = $this->loadClient();
		$objresult = $client->gpsHistory($postarr);
		$objresult=$this->joindata($postarr,$objresult);
		$obj = json_decode($objresult);
		$code = $obj->code;
		if($code) {
			echo $objresult;
			$this->datalog(',gpsHistory,'.$obj->message.',',var_export($postarr,true));
		} else {			
			$info = array();			
			$info['code'] = $obj->code;
			$info['page'] = (int)$obj->data->thisPageNumber;
			$info['allpage'] = (int)$obj->data->lastPageNumber;			
			$str = (string)$obj->data->result;
			$info['data'] = explode(';',$str);		
			echo json_encode($info);				
		}
		exit;
	}
	/**
	 * 将温度湿度数据追加到gps数据中
	 *
	 * @param array $postarr   			请求接口参数 与gpsHistory()接口保持一致
	 * @param string $objresult	  		gpsHistory()的返回值，对其进行数据追加
	 */
	public function joindata($postarr,$objresult){
		//请求接口
		$result = $this->client->dailyGpsstatus(array('gpsno' =>$postarr['gpsno'], 'from'=>$postarr['from'],'to'=>$postarr['to'],'limit'=>10000));
		$data1=explode(';',json_decode($objresult)->data->result);   
		$data2=json_decode($result)->data;
		//获取gps信息的时间范围
		$mintime='';
		$maxtime='';
		$temptime=0;
		$current=0;
		foreach ($data1 as $k=>$v) {
			$arr=explode(',',$v);
			$temptime=$arr[2]+$temptime;
			if($k==0){
				$mintime=(int)$arr[2];
			}
		}
		$maxtime=$temptime;

		//对温度湿度信息进行塞选并格式化
		$arrdata=array();
		foreach ($data2 as $v) {
			if($v->time/1000>$maxtime||$v->time/1000<$mintime){
				continue;
			}
			$temps=explode(',',$v->properties);
			$temperature='';
			$humidity='';
			foreach ($temps as $temp) {
				$k=explode(':',$temp);
				if($k[0]=='t1'||$k[0]=='t2'||$k[0]=='t3'||$k[0]=='t4'){
					$temperature.=$k[0].':'.$k[1].' ';
				}
				if($k[0]=='humi'){
					$humidity=$k[1];
				}
			}
			$arrdata[$v->time/1000]=trim($temperature,'~').','.$humidity;
		}

		//将温度湿度信息和GPS信息以时间最相近绝对值进行匹配
		$temptime=0;
		foreach ($data1 as $k=>$v) {
			$arr=explode(',',$v);
			$temptime=$arr[2]+$temptime;
			//超过1天的温度值没有参考意义
			$minabs=3600*24;
			$adddata='0,0';
			foreach ($arrdata as $key => $value) {
				$abs=abs($temptime-$key);
				if($abs<$minabs){
					$minabs=$abs;
					$adddata=$value;
				}else{
					continue;
				}
			}
			$data1[$k]=$v.','.$adddata;
		}
		//处理结果集
		$res=json_decode($objresult);
		$res->data->result=implode($data1,';');
		return json_encode($res);
	}
	
	/**
	 * 
	 * google 地图编码拆线
	 */
	public function getPolyline() {		
		$gpsno = $_GET['gpsno'] ? (int)trim($_GET['gpsno']) :0;
		$gpsid = isset($_GET['gpsid']) ? $_GET['gpsid'] :'';
		$starttime = trim($_GET['starttime']).':00';
	    $endtime = trim($_GET['endtime']).':00';	    
		$postarr = array(				
				'gpsno'=>$gpsno,
				'from'=>$starttime,
				'to'=>$endtime
				);
		if($gpsid)
			$postarr['imei'] = $gpsid;
		if($_GET['map']=='baidu')
			$postarr['map'] = 'baidu';
		$client = $this->loadClient();
		$objresult = $client->dailyEncode($postarr);		
		$obj = json_decode($objresult);
		$code = $obj->code;		
		if($code) {
			echo '100|><|'.(string)$obj->message;					
			$this->datalog(',dailyEncode,'.$obj->message.',',var_export($postarr,true));
		} else {
			$str = stripslashes((string)$obj->data->points).'|><|'.(string)$obj->data->levels;
		}
		unset($objresult,$obj);
		echo $str;	
		exit;
	}
	/**
	 * 跟踪数据获取
	 * {"message":null,"data":{"speed":0,"distance":0,"lng":116.3126648,"lat":40.0269077,"course":359,"time":1314157492000,"status":0},"params":null,"code":0}
	 * @return json
	 */
	public function getFollowData() {
		$postarr = array();
		$gpsno = (int)$_GET['gpsno'] ? (int)$_GET['gpsno'] : 0;
		$gpsno = self::charseticonv($gpsno);
		if ($gpsno) 
			$postarr['gpsnos'] = $gpsno;
		else {
			$gpsid = isset($_GET['gpsid']) && strlen($_GET['gpsid'])>5 ? $_GET['gpsid'] :'0';
			$postarr['gpsids'] = $gpsid;
		}
		$client = $this->loadClient();
		$newobj = new stdClass();
//		$objresult = $client->gpsFollow($postarr);
//		echo $objresult;
		if($_GET['cache']=='1')
			$postarr['cache'] = '-1';
		if($_GET['map']=='baidu')
			$postarr['map'] = 'baidu';
		$result = $client->gpsCurrents($postarr);
		$result = json_decode($result);
		if($result->code)			
			$this->datalog(',gpsGetcurrents,'.$result->message.','.var_export($postarr,true));
		else {						
			$newobj->code = $result->code;
			$newobj->message= $result->message;		
			$newobj->data = $result->data[0];
			$newobj->data->time2 = date('Y-m-d H:i:s',$result->data[0]->time/1000);
			$time = $result->data[0]->time/1000;
			$properties  = $result->data[0]->properties;
			if(time()-$time>1200){
				$newobj->data->speed = -1; 
			}
			$status = $result->data[0]->status;
			$temps=explode(',',$properties);
			$acc = '';$_acc=0;
			$temperature='';
			$humidity='';
			foreach ($temps as $temp) {
				$k=explode(':',$temp);
				if($k[0]=='acc'){
					$_acc=$k[1];
					if($_acc&&$status==0)
						$acc = '/ACC开';	
					elseif($status!=1)
						$acc = '/ACC关';
				}
				if($k[0]=='t1'||$k[0]=='t2'||$k[0]=='t3'||$k[0]=='t4'){
					$temperature.=$k[0].':'.$k[1].' ';
				}
				if($k[0]=='humi'){
					$humidity=$k[1];
				}
			}
			$newobj->data->acc = $acc;
			$newobj->data->temperature=$temperature;
			$newobj->data->humidity=$humidity;
		}
		echo json_encode($newobj);			
		exit;	
	}	
	
	private function loadClient() {
		if(!$this->client) {
			require_once(WEB_ROOT.'./client/Client.php');
			$this->client = new Client();
		}		
		return $this->client;
	}
	
	/**
	 * 
	 * 对应地图的 key
	 * @param string $mmap
	 */
	private function getMapKey($mmap='') {
		$mkey='';
		require_once(WEB_ROOT.'./api/function_mapkey.php');
		switch ($mmap) {		
			case 'mbar':
				$mkey = 'aCW9cItqL6Ppc7g9ZX4eb79hOIDyOIDhMHTsMhT7TzT6NeDyMyT=@yq4ytIq4NeZDIyMZIOTcDTqHI6T9IZtIIhLIeL9y@y@@eaZHZOIyttITy4TTcyzL4OIT4yIFCT=';
			break;
			case 'mapabc':
//				 $mkey ='c587daabd17eb8df75909221f4558db84576483168de8f15b2fff68a1a7c4044f6537f5b3be951f6';
				 $mkey = $mapabckey;
			break;
			default:
				$mkey = $mapkey;
			break;	
		}	
		return  $mkey;
	}
	
	/**
	 * 地图划线
	 *
	 * @param int $gpsno
	 * @param string $title
	 * @param string $start_time
	 * @param string $end_time
	 * @param int $zoom
	 */
	public function ipsline($gpsno,$start_time='',$end_time='',$zoom=13) {		
		$_starttime = $start_time ? date('Y-m-d H:i',strtotime($start_time)) : date('Y-m-d',time()-24*3600).' 00:00';
		$_endtime   = $end_time   ? date('Y-m-d H:i',strtotime($end_time)) : date('Y-m-d H:i',time());		
		
		$info = array(
		    'gpsno'=>$gpsno,			
			'starttime'=>$_starttime,
			'endtime' =>$_endtime		
		);
		$mapkey = $this->getMapKey();		
		include($this->tpl.'./ips.htm');
	}
	
	/**
	 * stdClass Object
		(
		    [message] => 
		    [data] => stdClass Object
		        (
		            [time] => 1293609928000
		            [distance] => 0
		            [speed] => 0
		            [course] => 0
		            [lat] => 38.3624917
		            [lng] => 115.0680163
		            [place] => stdClass Object
		                (
		                    [address] => 中国河北省保定市定州市和平东路
		                    [name] => 
		                    [type] => 
		                    [match] => 0
		                    [longitude] => 115.0511277
		                    [latitude] => 38.3553528
		                    [city] => 保定市
		                    [province] => 河北省
		                    [county] => 定州市
		                    [cityCode] => 
		                    [fullAddress] => 河北省保定市定州市 中国河北省保定市定州市和平东路
		                )		
		        )		
		    [params] => 
		    [code] => 0
		)
	 * @param unknown_type $gpno
	 * @param unknown_type $time
	 * @param  $code 1 解析地址 否则不解析
	 * @return unknown
	 */
	public function locate($gpsno=0,$time='',$code=0)
	{
		$setarr = $info = array();
		if($gpsno) 
		{
			if(!$time) 
				$time = date('Y-m-d H:i:s',time());
			else 	
				$time = date('Y-m-d H:i:s',strtotime($time));
			$setarr = array(
				'gpsno'=>trim($gpsno),
				'time'=>$time
			);
			if($code) 
				$setarr['code']=1;
			$client = $this->loadClient();
			$result = $client->gpsLocate($setarr);
			$obj = json_decode($result);
			
			if($obj->code)			
				$this->datalog(',gpsLocate,'.$obj->message.',',var_export($setarr,true));
			$info = $obj->data;
		}
		
		echo json_encode($info);
		exit;
	}
	/**
	 * 
	 * 日志记录
	 * @param string $content
	 * @param string $type
	 */
	
	
	private function datalog($content,$type='mysql_') {			
		return;
		//文件名称 1--7 
		$basePath = dirname(dirname(__FIFLE__)).'./data/mysqllog/';	
		$logfile  = $basePath.$type.date('N',time()).'.php';		
		//文件最后修改时间
		$lastModify = filemtime($logfile);
		$diff = time() - $lastModify;
		//非今天文件 删除
		if($diff>86400){
			$filepath = glob($logfile.'*');
				foreach ($filepath as $unfile) {				
						@unlink($unfile);
				}	
		} elseif(@filesize($logfile) >= 2048000) {	
			//2M 分文件
			$logfilebak = $logfile.'_'.date('His').'_bak.php';
			@rename($logfile, $logfilebak);		
		}
		
		$logstr = '跟踪回放gps.class.php|'.$content;		
		$mtime = explode(' ', microtime());
		$totaltime = number_format(($mtime[1] + $mtime[0] - $this->_runtime), 4);
		
		$url=' '.$_SERVER['SCRIPT_NAME'].'?'.$_SERVER['QUERY_STRING'];      
	    $newlog = date('Y-m-d H:i:s',time()).' '.$totaltime.' '.$logstr.' '.$url;
			    
		if($fp = @fopen($logfile, 'a')) {
			@flock($fp, 2);
			fwrite($fp, "<?PHP exit;?>\t".str_replace(array('<?', '?>', "\r", "\n",), '', $newlog)."\t\n");
			fclose($fp);
		}
		unset($content,$logstr,$newlog);
	}
	/**
     * 编码转换
     *
     * @param unknown_type $str
     * @param unknown_type $char
     * @return unknown
     */
    static public function charseticonv($str) {
        if(is_array($str)) {
            foreach ($str as $k=>$v) {
                $str[$k] = self::charseticonv($v);
            }
        } else {
        	if(json_encode($str)=='null')
            $str=iconv('gbk','utf-8',trim($str));
        }
        return $str;
    }
}
?>