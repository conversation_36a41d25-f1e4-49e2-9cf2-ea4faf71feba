<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title><?=$info['title']?>-回放</title>
<style type="text/css">
body, th, td, input, select, textarea, button { font: 12px/1.5em "lucida grande",tahoma,verdana,arial,sans-serif; margin:0;padding:0;}
a {color:#1A66B3;text-decoration:none;}
a:visited {color:#1A66B3;}
a:hover {color:#EF3838;text-decoration:underline;}
#calendar td {font-weight:bold;}
.calendar_default, .calendar_default a:link, .calendar_default a:visited {color:#0099CC;}
.chepai{float:left;width:100px;height:25px;line-height:25px; margin-top:5px;color:#000;text-align:center; background:url(../../public/image/bg_chepai.gif) no-repeat left top; float:left}
.chepai a{color:#000;}
.f16 {font-size:15px; font-weight:bold; font-family:"Microsoft YaHei","Microsoft JhengHei","宋体"}
</style>
<script src="http://ditu.google.cn/maps?file=api&amp;v=2&amp;key=<?=$mapkey?>&hl=zh-CN" type="text/javascript"></script>
<SCRIPT language=javascript src="../../public/js/lib/jquery.min.js" type="text/javascript"></SCRIPT>
<script language="javascript" type="text/javascript" src="../../public/js/minfo/markermanager.js"></script>
<script src="../../public/js/blackbird.js" type="text/javascript"></script>
<script language="javascript">
	var jq = jQuery.noConflict(),notShowDis=1; 
	var tpltype='replay',gpsid='<?=$info['gpsid']?>',gpsno='<?=$info['gpsno']?>',remark='<?=$info['remark']?>',showtitle='<?=$info['title']?>',autoplay='<?=$info['autoplay']?>',message='<?=$info['message']?>';	
	var ZOOM=<?=$zoom?>,loadNum=500,maxTime='<?=$info['max_time']?>',minTime='<?=$info['min_time']?>',postmarker='<?=$info['postmarker']?>';
	var mini = true;
	var cache = 0;
	
function checkTime(t) {
	var _cStime = getCouSeconds(jq('#starttime').val());
	var _cEtime = getCouSeconds(jq('#endtime').val());
	replayStop();
	if(_cStime>_cEtime){
		alert('开始时间不能大于结束时间');		
		return;
	} 
	/*else if(_cEtime-_cStime>7*24*3600){	    
		alert('只能回放7天内的轨迹数据');
		return;	  
	}
	
	else if(_cStime<minTime){
	    var _time = stampToTime(minTime*1000);
		alert('回放开始时间不能小于 '+_time);
		return;	  
	} else if(_cEtime>maxTime) {
		var _time = stampToTime(maxTime*1000);
		alert('回放结束时间不能大于 '+_time);
		return;	  
	}*/
	if(t==1) {
		getReplayData();
	} else if(t==2){
		replayStart();
	}
	
}
</script>
<script type="text/javascript" src="../../public/js/google_distance.js"></script>
<script type="text/javascript" src="../../public/js/minfo/popupmarker.js"></script>
<script type="text/javascript" src="../../public/js/minfo/script_map.js" ></script>
<script type="text/javascript" src="../../public/js/gps_tr_google.js?20120322"></script>
<script type="text/javascript" src="../../public/js/source/script_common_noframe.js"></script>
</head>
<body onUnload="GUnload()">
<div id="append_parent"></div>
<script type="text/javascript" charset="utf-8" src="../../public/js/source/script_calendar.js"></script>
<div style="height: 37px; background:#ACBEDA url(../../public/image/gps_bg2.gif) repeat-x left top; border-bottom:1px solid #6F8CB9;">
	<div style="float:left; height:37px; width:215px; padding-left:10px; background:url(../../public/image/gps_bg3.gif) no-repeat left top;">
	  <span style="float:left; display:block;line-height:34px; font-size:14px; font-weight:bold;color:#fff;">回放目标：</span>
		<div class="chepai"><span class="f16" id="carnum"><?=$info['title']?></span></div>
	</div>
	<div style="float: left;margin-top:6px;">      
		频率
		<select name="freq" id="freq">
			<option value="10000">正常-10</option>
			<option value="5000">2倍正常-5</option>
			<option value="3000">快-3</option>
			<option value="1000">比较快-1</option>
			<option value="500">很快-0.5</option>
			<option value="100" selected>非常快-0.1</option>
			<option value="10">快得不得了</option>
		</select>
		时间段
		<input type="text" name="starttime" id="starttime" value="<?=$info['starttime']?>"  onclick="showcalendar(event,this,1,'<?php echo date('Y-m-d');?>', '<?php echo date('Y-m-d', time() + 3600 * 24 * 60);?>');"/>
	  至
		<input type="text" name="endtime" id="endtime" value="<?=$info['endtime']?>"  onclick="showcalendar(event,this,1,'<?php echo date('Y-m-d');?>', '<?php echo date('Y-m-d', time() + 3600 * 24 * 60);?>')" />

		<input class="submit" value="播放" style="width:60px; height:24px;text-indent:80px;background:url(../../public/image/gps_play.gif) no-repeat left top; border:none" type="button" onClick="checkTime('1');" id="btnDataStart"/>

		<input class="submit" value="播放" style="display:none;width:60px; height:24px;text-indent:80px;background:url(../../public/image/gps_play.gif) no-repeat left top; border:none" type="button" onClick="checkTime('2');" id="btnStart" />
		<input class="submit" value="暂停" style="width:60px; height:24px;text-indent:80px;background:url(../../public/image/gps_stop.gif) no-repeat left -24px; border:none" type="button" onClick="replayStop()" id="btnStop" />
	 </div>
	 <div style="float: left;margin-left:20px;margin-top:7px; background:#A80000;color:white" id="dataload"></div>
 </div>
<div id="map" style="HEIGHT:95%;WIDTH:100%;Z-INDEX:1;"></div> 
<script language="javascript">
var _WW = document.body.clientWidth;	
var _HH = document.body.clientHeight;
if(_WW <=500 || _HH <= 500)
	mini = false;
</script>
</body>
</html>