/*
 * google地图 邮政回放
 * @2011-03-31 liyonghua<<EMAIL>>
 */ 
var map=null,marker,Glat=0,len=0,Glng=0,timer=null,autoTimer=null,isFirst=1;
var Lines=new Array();//线路JSON数组
var trafficInfo,toggleState=0;
var startpoint,endpoint;//开始点 结束点
var page=1,replayData=new Array(),lst='',est='',rLat=0,rLng=0,rTime=0,rNum=1,runTime=0,rDis=0,stopTime=0,isShow=true,lastTime='';
var linePage=0,allPage=0,quickly=false,codeline = new Array(),nextsite=0,slasttime=null;
var msg = '所选时间段内无相关记录',endmsg = '已播放完毕';

function initialize() {
	if (GBrowserIsCompatible()) {
		map = new google.maps.Map(document.getElementById("map"));
		map.setCenter(new GLatLng(39.904469,116.411476), ZOOM);			
		var customUI = map.getDefaultUI();       
        customUI.maptypes.hybrid = true;
        map.setUI(customUI);
		if(mini) {
			map.addControl(new GOverviewMapControl());//鸟瞰			
		}
	}   
    quickBack();
	if(tpltype=='replay'){
        if(autoplay==1) 
			getReplayData();	
	}	
}

//API 回放ajax获取数据
function getReplayData(){
	quickly = false;
	var starttime  = jq('#starttime').val();
	var endtime    = jq('#endtime').val();
	if(page==1) {
		jq("#dataload").html("数据载入中,请稍候...");
		lst = starttime;
		est = endtime;
		initData();		
	}		
	jq.ajax({ 
		url: "router.php?method=huoyunren.gps.getReplayData",
		type: "GET", 
		data:"gpsno="+gpsno+"&starttime="+lst+"&endtime="+est+"&page="+page+"&_rnd="+Math.random(),
		dataType: "json", 
		success: function(json){
			if(json && json.code==0){				
				var _data = json.data;
				var _len = _data.length
				 if (_len>1){
					replayData.push(_data);					 
				 } 
								
				//第一次请求定位中心点
				if(page==1 && _len >1){					
					//map.removeOverlay(marker);
					map.clearOverlays();					
					var _info = splitInfo(_data[0]);
					rLat = _info.lat;
					rLng = _info.lng;
					var point=new GLatLng(rLat,rLng);
					map.setCenter(point);
					rTime = parseInt(_info.difftime);
					lastTime = rTime;
					var _rTime = eval(rTime*1000);
					var title="目标："+showtitle+"<br>状态："+_info.speed+"KM/H<br>时间："+stampToTime(_rTime);
					if(remark){
						title+="<br>备注："+remark;
					}
					marker=createMarker(point,title,_info.speed,1,1);
					map.addOverlay(marker);
					marker.showPopup();
				}	
				allPage = json.allpage;					
			} else {
				alert(json.message);
			}
		},
		complete:function()
		{ 
			if(page==1) {
				jq("#dataload").html("数据载入完毕");
				jq("#btnDataStart").hide();
				jq("#btnStart").show();
				getPolyline(lst,est);				
				replayStart();				
			}
			//加载成功之后、分页加载数据
			page+=1;
			/*if(page<=allPage) {
                window.setTimeout("getReplayData()",5000);				
			}*/
		}
	}); 
}

function getPolyline(plstime,pletime,gocenter){
	jq.ajax({ 
		url: "router.php?method=huoyunren.gps.getPolyline",
		type: "GET", 
		data:"gpsno="+gpsno+"&starttime="+plstime+"&endtime="+pletime+"&_rnd="+Math.random(),
		dataType: "string", 
		success: function(json){
            drawEncodedPolyline(json,gocenter);
		}
	})
}

function replayDrawLine(){
	if(replayData.length<1) {		
		alert(msg);		
		replayStop();
		return;
	}
	jq("#dataload").html("轨迹正在回放中...");
   // alert(replayData.length);
	var lines = replayData[linePage];

    if( lines && rNum<lines.length-1){
		var _curDot = lines[rNum];
		rNum++;
		var lct = splitInfo(_curDot)		
		var lat = rLat+lct.lat;
		var lng = rLng+lct.lng;
		rLat = lat;
		rLng = lng;
		rDis+=parseInt(lct.distance);
		rTime+=parseInt(lct.difftime);
		
        var point=new GLatLng(lat, lng);
		//判断是否超出范围
		if(!map.getBounds().containsLatLng(point)) {
		   map.panTo(point);
		}

        if(lct.difftime>30) {			
			markStopPoint(point,lct.difftime,rTime);
			stopTime+=parseInt(lct.difftime);
		} else {
			runTime+=parseInt(lct.difftime);
		}

		lastTime = rTime;
		var _rTime = eval(rTime*1000);
		var title="速度："+lct.speed+"公里/小时<br>方向："+getHangXiang(lct.direction,lct.speed)+"<br>时间："+stampToTime(_rTime);
		if(notShowDis==1) title+="<br>里程："+formatDistance(Math.round(rDis/100));
        showMarker(title,point);
		//加载下页数据
		if(rNum==loadNum && page<=allPage){
			getReplayData();
		}

	}else if(linePage<replayData.length-1){
		//每页数据第一条
		linePage+=1;
		rNum=1;
		var lines = replayData[linePage];
		var _info = splitInfo(lines[0]);
		rLat = _info.lat;
		rLng = _info.lng;
		rTime = parseInt(_info.difftime);
		lastTime = rTime;
		rDis+=parseInt(_info.distance);
		var _rTime = eval(rTime*1000);
		var title="速度："+_info.speed+"公里/小时<br>方向："+getHangXiang(_info.direction,_info.speed)+"<br>时间："+stampToTime(_rTime);
		if(notShowDis==1) title+="<br>里程："+formatDistance(Math.round(rDis/100));
		var point=new GLatLng(rLat, rLng);
        showMarker(title,point);

	} else {
		//var title="行驶时间："+exchange_time(runTime)+"<br>停留时间："+exchange_time(stopTime);
		//if(notShowDis==1) title+="<br>总里程："+formatDistance(Math.round(rDis/100));
		//var point=new GLatLng(rLat, rLng);
       // showMarker(title,point);
	   var _msg = '轨迹回放完毕';
	   jq("#dataload").html(_msg);
	   alert(_msg);		
	   replayStop();
	   return;
	}
}

function replayStart() {
	jq("#dataload").html("数据载入中,请稍候..."); 
	jq("#btnStart").attr("disabled","disabled");
	jq("#btnStart").css("background-position","left -24px");	
	jq("#btnStop").removeAttr("disabled");
	jq("#btnStop").css("background-position","left top");   
	var starttime  = jq('#starttime').val();
	var endtime    = jq('#endtime').val();
	if(lst!=starttime || est != endtime || quickly===true) {
		page=1;
		map.clearOverlays();
		getReplayData();
		return;
	} else {
		timer=window.setInterval("replayDrawLine()",jq('#freq').val());	
	}
	
}

function replayStop() {
	if (typeof(timer) == "number") { 
		jq("#dataload").html("轨迹回放暂停中...");
		 window.clearInterval(timer);
		 jq("#btnStop").attr("disabled","disabled");		
		 jq("#btnStart").removeAttr("disabled");
         jq("#btnStop").css("background-position","left -24px");
		 jq("#btnStart").css("background-position","left top");	
	}
}
 // 添加编码折线	
function drawEncodedPolyline(info,gocenter) {
	var _go = gocenter || null;
	var str = info.split("|><|");
	var encodedPoints = str[0];
    var encodedLevels = str[1];	

	if(str[0]==100) {
		alert(str[1]);
	} else if(encodedLevels.length<6) {
		return;
	}
    
    var encodedPolyline = new GPolyline.fromEncoded({
                    color: "#33FF00",
                    weight: 6,
                    points: encodedPoints,
                    levels: encodedLevels,
                    zoomFactor: 32,
                    numLevels: 2
            });
	var lineid = 'Polyline_'+nextsite;
	encodedPolyline.id = lineid;	
	codeline.push(encodedPolyline);
	//线的最后一点
	if(_go) {
		var latlng = encodedPolyline.getBounds().getSouthWest();	
		var point = new GLatLng(latlng.y,latlng.x)
		map.setCenter(point);		
	}
    map.addOverlay(encodedPolyline);	
	//encodedPolyline.remove();
}

function markStopPoint(point,difftime,sTime) {	
	var _lastTime = eval(lastTime*1000);
	var _sTime =  eval(sTime*1000);
	var title = "<font size='2'>停留时长："+exchange_time(difftime)+"<br>开始："+stampToTime(_lastTime)+"<br>结束："+stampToTime(_sTime)+"</font>";	
	var stopMarker = createMarker(point, title, 0, 1, 1);
	map.addOverlay(stopMarker);
	stopMarker.hidePopup();
}

function showMarker(title, point)
{
    if (isShow && typeof(marker) == "object")
    {
        marker.setText(title);
        marker.setLatLng(point);
        marker.showPopup();
        //isShow = false;
    }
}
function splitInfo(i) {
	var info = new Array();
	var _i = i.split(',');	
	info['lng']= eval(_i[0]/10000000);
	info['lat']= eval(_i[1]/10000000);
	info['speed']= _i[3];
	info['difftime']= _i[2];
	info['direction']= _i[4];
	info['distance']= _i[5];
	return info;    
}

function initData() {
	page=1,replayData=new Array(),rLat=0,rLng=0,rTime=0,rNum=1,runTime=0,rDis=0,stopTime=0,lastTime='',linePage=0;
	codeline = new Array(),nextsite=0,slasttime=null;
}

function quickPlay() {
	if(!gpsno)
		return;
	quickly = true;
	map.clearOverlays();
	lst  = jq('#starttime').val();
	est  = jq('#endtime').val();	
	//开始点
	jq.ajax({ 
		url: "router.php?method=huoyunren.gps.locate",
		type: "GET", 
		data:"gpsno="+gpsno+"&time="+lst+"&_rnd="+Math.random(),
		dataType: "json", 
		success: function(json){
			if(json!=null) {				
				startpoint = new GLatLng(json.lat,json.lng);			
				map.setCenter(startpoint);	
				initMarker(startpoint,'start','始点');
			}
		}
	});	
	jq.ajax({ 
		url: "router.php?method=huoyunren.gps.locate",
		type: "GET", 
		data:"gpsno="+gpsno+"&time="+est+"&_rnd="+Math.random(),
		dataType: "json", 
		success: function(json){
			if(json!=null) {				
				endpoint = new GLatLng(json.lat,json.lng);				
				initMarker(endpoint,'stop','终点');					
			}
		}
	});	
	getPolyline(lst,est);
	nextsite=0,slasttime;
}

function quickBack() {
	if(!gpsno)
		return;
	map.clearOverlays();
	lst  = jq('#starttime').val();	
	quickly = true;
	//开始点
	jq.ajax({ 
		url: "router.php?method=huoyunren.gps.locate",
		type: "GET", 
		data:"gpsno="+gpsno+"&time="+lst+"&_rnd="+Math.random(),
		dataType: "json", 
		success: function(json){
			if(json!=null) {				
				startpoint = new GLatLng(json.lat,json.lng);			
				map.setCenter(startpoint);	
				initMarker(startpoint,'start','始点');
			}
		}
	});		
	nextsite=0,slasttime;
}

//站点播放
function sitePlay(){
	var sinfo = siteinfo;
	var sites = sinfo.length;
	if(sites>0) {		
		if(slasttime==null) {	
			codeline = new Array();
			slasttime = jq('#starttime').val();
			map.clearOverlays();
			initMarker(startpoint,'start','始点');
			siteMarker(sinfo);
		}
		if(nextsite < sites) {
			var _nexttime = sinfo[nextsite].time;			
			getPolyline(slasttime,_nexttime,true);
			slasttime = _nexttime;
			nextsite++;
		} else if(nextsite == sites) {			
			var _nexttime = jq('#endtime').val();			
			getPolyline(slasttime,_nexttime,true);
			nextsite++;
		} else {
			alert(endmsg);
		}
	} else {
		 quickPlay();
	}
}
//站点播放回放
function siteBack() {
	var sinfo = siteinfo;
	if(nextsite>0) {
		nextsite--;			
		var _code = codeline[nextsite];	
		if(_code!=null || typeof(_code)!=='undefined') {
			var latlng = _code.getBounds().getNorthEast();
			log.debug(_code);
			var point = new GLatLng(latlng.y,latlng.x);
			map.setCenter(point);		
			_code.remove();
		}
		log.debug(nextsite);
			if(nextsite>0) {
				var _site = nextsite-1;
				slasttime = sinfo[_site].time;
			} else {				
				slasttime=null;
				nextsite = 0;
			}
		log.debug(slasttime);
	} else {		
		slasttime = null;
		nextsite  = 0;
		alert('已回退到尽头,无法再回退了!');
	}
}
//站点标记
function siteMarker(sites) {
	var _len = sites.length;
	if(_len>0) {
		for (var i=0;i<_len ;i++ )
		{
			var point = new GLatLng(sites[i].lat,sites[i].lng);
			if(sites[i].imgurl)
				imgurl = sites[i].imgurl;
			else 
				imgurl = 'green-dot';
			initMarker(point,imgurl,sites[i].name);
		}
	}
}
//初始始终位置图标
function initMarker(point,img,html){	
    var blueIcon = new GIcon(G_DEFAULT_ICON); 
	blueIcon.image = "../../public/image/dot/"+img+".png";
	blueIcon.iconSize= new GSize(24,34);
	markerOptions = { icon:blueIcon }; 
	//var point = new GLatLng(lat,lng);
	var marker =new GMarker(point, markerOptions);
	GEvent.addListener(marker, "click", function() {		
		map.openInfoWindowHtml(point,html);		
	});	
	map.addOverlay(marker);		
}