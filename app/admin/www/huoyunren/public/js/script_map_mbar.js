function getIcon(type)//取得图标
{
	var img = getIconUrl(type);
	var tinyIcon = new MIcon(img,12,20);	
	//tinyIcon.infoWindowAnchor = new GPoint(3, 0);//信息窗口在此图标上的锚定点相对于图标图像左上角的像素坐标。
	return tinyIcon;
}
function getIconUrl(type) {//取得图标
	return "/huoyunren/public/image/map/mm_20_"+type+".png";
}
function getSpeedType(speed)
{
	var type;
	if(speed<0)//离线
	{
		type=0;
	}
	else if(speed<5)
	{
		type=1;
	}
	else if(speed>=5&&speed<80)
	{
		type=2;
	}
	else if(speed>=80&&speed<120)
	{
		type=3;
	}
	else if(speed>=120)
	{
		type=4;
	}
	return type;
}
function createMarker(point,title,speed,showfun,hidefun) {//创建标记
	var type=getSpeedType(speed);	
	var marker = new MMarker(point, getIcon(type),new MInfoWindow("",title));
	
	return marker;
}
function createLine(point1,point2,speed){//创建线路	
	var lat1=point1.lat;
	var lng1=point1.lon;
	var lat2=point2.lat;
	var lng2=point2.lon; 
	
	if(lat1==lat2&&lng1==lng2)//过滤完全相同的点
	{
		return;
	}  	
	var type=getSpeedType(speed);	
	var color;
	switch(type)
	{
		case 2:
			color="#00ff00";
			break;
		case 3:
			color="#FF00FF";
			break;
		case 4:
			color="#ff0000";
			break;
		default:
			color="#000000";
			break;
	}
	var br = new MBrush();
		br.arrow = true;
		br.color = color;
		br.lineSize=1;
		br.type = 'normal';

	var polyline = new MPolyline([
	  point1,
	  point2
	 ],br);
	
    return polyline;
} 

function getHangXiang(dusu, sudu) {//行驶方向
	if(getHangXiang.arguments.length == 2){
		//if(sudu < 10)
		//	return "......";
	}
	var z = 5;
	var fz = 10;
	var p = 30;

	var hangxiang = [ "正北向", "北向", "东北向偏北", "东北向", "东北向偏东", "正东向", "东向",
			"东南向偏东", "东南向", "东南向偏南", "正南向", "南向", "西南向偏南", "西南向", "西南向偏西",
			"正西向", "西向", "西北向偏西", "西北向", "西北向偏北" ];
	var qujian1 = [ z, fz, fz, p, 90 - p, 90 - z, 90 - fz, 90 + fz, 90 + p,
			180 - p, 180 - z, 180 - fz, 180 + fz, 180 + p, 270 - p, 270 - z,
			270 - fz, 270 + fz, 270 + p, 360 - p ];
	var qujian2 = [ 360 - z, 360 - fz, p, 90 - p, 90 - fz, 90 + z, 90 + fz,
			90 + p, 180 - p, 180 - fz, 180 + z, 180 + fz, 180 + p, 270 - p,
			270 - fz, 270 + z, 270 + fz, 270 + p, 360 - p, 360 - fz ];

	for ( var i = 0; i < qujian1.length; i++) {
		if (i == 0 || i == 1) {
			if (qujian2[i] <= dusu || dusu <= qujian1[i])
				return hangxiang[i];
		} else {
			if (qujian1[i] <= dusu && dusu <= qujian2[i])
				return hangxiang[i];
		}
	}
	return dusu;
}

function formatDistance(len)//里程 
{
	len = Math.round(len);
	if (len <= 1000) 
	{
		return len + ' ' + '米';
	} 
	else if (len <= 1000000) 
	{
		return len / 1000 + ' ' + '公里';
	}
	return Math.round(len / 1000) + ' ' + '公里';
}
function exchange_time(msecond) {//时间
	var dd;
	var hh;
	var mm;
	var ss;
	//var strTip = "";
	datesub = msecond;
	dd = Math.round(datesub / 86400 + 0.5) - 1;
	hh = Math.round((datesub - dd * 86400) / 3600 + 0.5) - 1;
	mm = Math.round((datesub - dd * 86400 - hh * 3600) / 60 + 0.5) - 1;
	ss = Math.round(datesub - dd * 86400 - hh * 3600 - mm * 60);
	var strtip = "";
	if (dd > 0)
		strtip = strtip + dd + "天";
	if (hh > 0)
		strtip = strtip + hh + "小时";
	if (mm > 0){
		strtip = strtip + mm + "分";
		if(dd > 0)
			return strtip;
	}
	if (ss > 0)
		strtip = strtip + ss + "秒";
	return strtip;
}
function getStateInText(sudu) {
	if (sudu<0)
		return "离线";
	if (sudu<5)
		return "静止";
	if (sudu>=5&&sudu < 80)
		return "正常行驶中";
	if (120 > sudu && sudu >= 80)
		return "快速行驶中";
	return "超速行驶中";
}
//得到两点时间之差
function getTimeDiff(thisTime, lastTime){
	var timeDiff =  (new Date(thisTime.replace(/-/g, "/")).getTime() - new Date(
			(lastTime.split("."))[0].replace(/-/g, "/")).getTime()) / 1000;
	if(timeDiff <= 0)
		timeDiff = 1;
	return timeDiff;
}
function getCouSeconds(strDate)//返回秒数
{
	return parseInt(new Date(Date.parse(strDate.replace(/-/g,"/"))).getTime()/1000);
}
function getCarStatus(status,speed){
	if(status==0) return '离线';
	else if(status==1 && speed>5 && speed<60) return '行驶';
	else if(status==1 && speed>=60 && speed<120) return '快速';
	else if(status==1 && speed>=120) return '超速';
	else return '静止';
}
//时间戳转化为年月日时分钞
function stampToTime(t){
	if(!t) return;
	var _t = parseInt(t);
	var mydate = new Date(_t);
	var str = mydate.getFullYear()+'-'+strTimeLen(eval(mydate.getMonth()+1))+'-'+strTimeLen(mydate.getDate());
	    str+=' '+strTimeLen(mydate.getHours())+':'+strTimeLen(mydate.getMinutes(),2)+':'+strTimeLen(mydate.getSeconds());
    return str;
}
function strTimeLen(value, length) {   
    var zeros = '';
    if (!length) length = 2;  
    value = String(value);  
    for (var i = 0; i < (length - value.length); i++) {   
          zeros += '0';   
    }   
    return zeros + value;  
}    