/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_invoice_import_statistics';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f + '&invoice_type=1';
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id",'invoice_type', 'orgname', 'orgcode', 'business_time','oil_type',
            'm_quantity', 'm_quantity_province_out', 'y_quantity','y_quantity_province_out',
            'm_amount', 'm_amount_province_out', 'y_amount', 'y_amount_province_out',
            'm_quantity_statistics','m_quantity_statistics_province_out',
            'y_quantity_statistics', 'y_quantity_statistics_province_out',
            'not_count_month', 'import_status',
            'creator_name', 'last_operator', 'createtime', 'updatetime','operator_name' ,'business_time_ym',
            'month_text_notice'       ]
    )
});