var top_panel = "";

function packTop(val){
    top_panel = new Ext.form.FormPanel({
        region : 'north',
        hideLabels : true,
        bodyStyle : 'padding: 10px',
        height : 50,
        items  :
            [
                {
                    xtype : 'compositefield',
                    items :
                        [
                            {
                                xtype: 'displayfield',
                                value: '账单单号：'
                            },
                            {
                                xtype : 'textfield',
                                id    : 'order_no',
                                name  : 'order_no',
                                width : 200
                            },
                            {
                                xtype: 'displayfield',
                                value: '主卡运营商：'
                            },
                            new Ext.form.ComboBox({
                                width: 120,
                                id: 'operators_id',
                                hiddenName: 'operators_id',
                                value: '',
                                triggerAction: 'all',
                                forceSelection: true,
                                store: getOperators,
                                emptyText: '请选择..',
                                valueField: 'key',
                                displayField: 'value',
                                enableKeyEvents: true,
                            }),
                            {
                                xtype: 'displayfield',
                                value: '结算状态：'
                            },
                            {
                                xtype : 'combo',
                                hiddenName  : 'settle',
                                id: 'settle_id',
                                mode  : 'local',
                                width: 80,
                                emptyText: '请选择..',
                                triggerAction : 'all',
                                forceSelection: true,
                                displayField  : 'value',
                                valueField    : 'key',
                                value  : '',
                                store  : new Ext.data.SimpleStore({
                                    fields: ['key','value'],
                                    data: [['1','已结算'], ['2','待结算']]
                                }),
                            },
                            {
                                xtype: 'displayfield',
                                value: '账单日：'
                            },
                            {
                                xtype: "datefield",
                                id: "day_start",
                                name: 'day_start_name',
                                format: 'Y-m-d',
                                width: 130
                            },
                            {
                                xtype: 'displayfield',
                                value: '—'
                            },
                            {
                                xtype: "datefield",
                                id: "day_end",
                                name: 'day_end_name',
                                format: 'Y-m-d',
                                width: 130
                            },
                            {
                                xtype:'button',
                                text:'查询',
                                style : 'padding-left : 10px;',
                                handler: function()
                                {
                                    main_store.removeAll();//移除原来的数据
                                    main_store.load();//加载新搜索的数据
                                }
                            },
                            {
                                xtype: 'button',
                                text: '重置',
                                style: 'padding-left : 10px;',
                                handler: function () {
                                    top_panel.getForm().reset();
                                    main_store.removeAll();//移除原来的数据
                                    main_store.load();//加载新搜索的数据
                                    btnInit();
                                }
                            },

                            {
                                xtype: 'displayfield',
                                value: '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',
                            },
                            {
                                xtype: 'displayfield',
                                value: "资金池余额："+val.cash_fee+'元',
                            },
                            {
                                xtype: 'displayfield',
                                value: '待结算总额：'+val.nopay_fee+'元',
                            },{
                                xtype: 'displayfield',
                                value: '差额：'+val.balance+'元',
                            },
                            {
                                xtype: 'displayfield',
                                value: '已结算总额：'+val.haspay_fee+'元',
                            },
                        ]
                }
            ]
    });
}

