/**
 * 获取日期函数
 * @param $flag 1 为月初，2为当前日期
 * @returns {String}
 */
function GetDateStr($flag) {
    var dd = new Date();
    var y = dd.getFullYear();
    var m = dd.getMonth() + 1;//获取当前月份的日期
    if (m.toString().length == 1) {
        m = '0' + m;
    }
    var d = '';
    var one = '';
    if ($flag == 1) {
        one = dd.getDate();
        if (one > 4) {
            one = one - 4;
        } else {
            one = 1;
        }
        if (one.toString().length == 1) {
            d = "0" + one;
        } else {
            d = one;
        }
    } else {
        d = dd.getDate();
        if (d.toString().length == 1) {
            d = '0' + d;
        }
    }

    return y + "-" + m + "-" + d;
}

var top_panel = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 90,
    items: [
        {//第1行
            xtype: 'compositefield',
            items: [
                {xtype: 'hidden', id: 'loadPageFlag', value: true},
                {
                    xtype: 'displayfield',
                    value: '单号：',
                    width: 60,
                },
                {
                    xtype: 'textfield',
                    id: 's_no',
                    name: 'no',
                    width: 135,
                },

                {
                    xtype: 'displayfield',
                    value: '申请时间：',
                    width: 60,
                },
                {
                    xtype: "datefield",
                    id: "s_start_time",
                    name: 'start_time',
                    format: 'Y-m-d',
                    value: GetDateStr(1),
                    width: 140,
                    editable: false
                },
                {xtype: 'displayfield', value: '~'},
                {
                    xtype: "datefield",
                    id: "s_end_time",
                    name: 'end_time',
                    format: 'Y-m-d',
                    value: GetDateStr(2),
                    width: 140,
                    editable: false
                },
                {
                    xtype: 'displayfield',
                    value: '客户机构：',
                    width: 60,
                },
                new Ext.form.ComboBox({
                    width: 200,
                    listWidth: 350,
                    id: 's_org_id',
                    hiddenName: 'orgcode',
                    triggerAction: 'all',
                    forceSelection: true,
                    mode: 'remote',
                    queryParam: 'keyword',
                    minChars: 2,
                    displayField: 'org_name',//显示的值
                    valueField: 'orgcode',//后台接收的key
                    store: getOilOrgNew,
                    emptyText: '请选择..',
                    enableKeyEvents: true
                }),
                {
                    xtype: 'button',
                    text: '查询',
                    style: 'padding-left : 10px;',
                    handler: function () {
                        assignStore.removeAll();//移除原来的数据
                        assignStore.load();//加载新搜索的数据

                        detailStore.removeAll();
                        btnInit();
                    }
                },
                {
                    xtype: 'button',
                    text: '重置',
                    style: 'padding-left : 10px;',
                    handler: function () {
                        top_panel.getForm().reset();
                        Ext.getCmp('s_org_id').setValue('');

                        assignStore.removeAll();//移除原来的数据
                        assignStore.load();//加载新搜索的数据

                        getDebitAccountForSearch.removeAll();
                        getDebitAccountForSearch.load();

                        detailStore.removeAll();
                        btnInit();
                    }
                }
            ]
        },
    ]
});