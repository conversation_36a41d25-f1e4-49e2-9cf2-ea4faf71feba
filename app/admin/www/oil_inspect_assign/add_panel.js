var add_win;//添加窗口
var add_update_id = '';//判断是添加还是修改
var detail_edit = false; //记录detail是否有改动
var url = '';//修改时表单提交的url

var detail_record = Ext.data.Record.create([{name: 'id', type: 'string'}]);
//缓存的
var detail_record_store_main = new Ext.data.GroupingStore({
    reader: detail_record,
    proxy: new Ext.data.MemoryProxy(detail_record), AutoLoad: true, sortInfo: {}
});

var detail_add_editor = new Ext.ux.grid.RowEditor({
    saveText: '确定',
    clicksToEdit: 2,
    listeners: {
        'beforeedit': function (editObject, rowIndex) {
            if (Ext.getCmp('org_id').getValue() == '') {
                Ext.MessageBox.alert('提示', '请先选择机构');
                return false;
            }
            var status = this.grid.getStore().getAt(rowIndex).get('status');
            if (status == '主站分配完成' || Ext.getCmp('status').getValue() == '已审核') {
                return false;
            } else {
                Ext.getCmp('add_detail').disable();
            }
            Ext.getCmp('detail_vice_no').allowBlank = false;
            detail_edit = true;
            Ext.getCmp('del_detail').disable();
            Ext.getCmp('btn_save').disable();
            Ext.getCmp('btn_cancel').disable();
        },
        'validateedit': function (rd, rec, reced, idx) {
            var assign_money = Ext.getCmp('detail_assign_money').getValue();
            var assign_jifen = Ext.getCmp('detail_assign_jifen').getValue();

            assign_money = assign_money ? parseFloat(assign_money) : 0;
            assign_jifen = assign_jifen ? parseFloat(assign_jifen) : 0;

            var vice_no = Ext.getCmp('detail_vice_no').getValue();

            var org_id = Ext.getCmp('org_id').getValue();
            var orgcode = '';
            var orgname = '';

            for (var i = 0; i < getOilOrgNew.getCount(); i++) {
                if (getOilOrgNew.getAt(i).get('id') == org_id) {
                    orgcode = getOilOrgNew.getAt(i).get('orgcode');
                    orgname = getOilOrgNew.getAt(i).get('orgname');
                }
            }

            if (orgcode) {
                Ext.Ajax.request({
                    url: '../inside.php?t=json&m=oil_account_assign&f=getInfoByOrgViceNo',
                    params: {vice_no: vice_no, orgcode: orgcode, org_id: org_id},
                    method: 'POST',
                    success: function (response, options) {
                        var result = Ext.decode(response.responseText);
                        var record = detail_record_store_main.getAt(idx);

                        if (result) {
                            if ((assign_jifen == '' || assign_jifen == 0) && (assign_money == '' || assign_money == 0)) {
                                Ext.MessageBox.alert('提示', '分配金额与分配积分不能同时为0或空');

                                record.set('vice_no', '');

                                detail_add_editor.startEditing(idx);
                            } else {
                                record.set('oil_com', result.oil_com);
                                record.set('org_name', result.org_name);
                                record.set('truck_no', result.truck_no);
                                record.set('fanli_region', result.province);
                                record.set('main_no', result.main_no);
                                record.set('jifen', result.jifen);
                                record.set('vice_id', result.id);
                                record.set('vice_status', result.vice_status);
                                if ((record.get('oil_com') == '中石油' || record.get('oil_com') == '电子卡-中石油' || record.get('oil_com') == '中石化') && !assign_jifen) {
                                    record.set('status', '主站待分配');
                                } else {
                                    record.set('status', '待审核');
                                }

                                Ext.getCmp('del_detail').enable();
                                Ext.getCmp('add_detail').enable();
                                Ext.getCmp('btn_save').enable();
                                Ext.getCmp('btn_cancel').enable();
                            }
                        } else {
                            if (record) {
                                record.set('org_name', '');
                                record.set('oil_com', '');
                                record.set('truck_no', '');
                                record.set('fanli_region', '');
                                record.set('main_no', '');
                                record.set('jifen', '');
                                record.set('vice_id', '');
                                record.set('vice_no', '');

                                Ext.MessageBox.alert('提示', '机构[' + orgname + ']下无此卡号或不支持撬装卡分配');
                                detail_add_editor.startEditing(idx);
                                return false;
                            }

                        }
                    }
                });
            }
        },
        'afteredit': function (rd, rec, reced, idx) {
            Ext.getCmp('del_detail').enable();
            Ext.getCmp('add_detail').enable();
            Ext.getCmp('btn_save').enable();
            Ext.getCmp('btn_cancel').enable();
        },
        canceledit: function (rd, rec, reced, idx) {
            var num = detail_record_store_main.data.length;

            if (num > 0) {
                for (var i = 0; i < num; i++) {
                    if (detail_record_store_main.getAt(i)) {
                        if (!detail_record_store_main.getAt(i).get('vice_no')) {
                            Ext.getCmp('detail_vice_no').allowBlank = true;
                            var s = detail_add_panel.getSelectionModel().getSelections();
                            detail_record_store_main.remove(s[0]);
                        }
                    }
                }
            }

            if (detail_record_store_main.data.length == 0) {
                Ext.getCmp('del_detail').disable();
            }

            Ext.getCmp('btn_save').enable();
            Ext.getCmp('btn_cancel').enable();
            Ext.getCmp('add_detail').enable();
        },
    }
});


function detail_main_rand_num(n) {
    var rnd = "";
    for (var i = 0; i < n; i++)
        rnd += Math.floor(Math.random() * 10);
    return rnd;
}

//详情添加行事件
function detail_grid_cell_add() {
    Ext.getCmp('btn_save').disable();
    Ext.getCmp('btn_cancel').disable();

    detail_edit = true;
    var e = new detail_record({
        id: detail_main_rand_num(10)
    });
    detail_add_editor.stopEditing();
    detail_record_store_main.add(e);
    detail_add_panel.getView().refresh();
    detail_add_panel.getSelectionModel().selectRow(detail_record_store_main.getCount() - 1);
    detail_add_editor.startEditing(detail_record_store_main.getCount() - 1);
}

var _sm_detail = new Ext.grid.RowSelectionModel({
    listeners: {
        selectionchange: function (data) {
            var count = data.getCount();
            var status = count ? data.selections.items[0].data.status : '';
            if (count && status != '主站分配完成' && Ext.getCmp('status').getValue() != '已审核') {
                Ext.getCmp('del_detail').enable();
            } else {
                Ext.getCmp('del_detail').disable();
            }
        }
    }
});

var detail_add_panel = new Ext.grid.GridPanel({
    store: detail_record_store_main,
    region: 'south',
    title: '分配申请明细',
    height: 320,
    width: 954,
    style: 'margin-top: 15px',
    autoScroll: true,
    enableColumnResize: false,
    sm: _sm_detail,
    plugins: [detail_add_editor],
    tbar: [
        {
            iconCls: 'silk-add',
            text: '增行',
            id: 'add_detail',
            handler: function () {
                if (Ext.getCmp('org_id').getValue() == '') {
                    Ext.MessageBox.alert('提示', '请先选择机构');
                    return false;
                }
                Ext.getCmp('detail_vice_no').allowBlank = false;
                detail_grid_cell_add();
            }
        },
        {
            iconCls: 'silk-delete',
            text: '删行',
            id: 'del_detail',
            disabled: true,
            handler: function () {
                detail_edit = true;
                var s = detail_add_panel.getSelectionModel().getSelections();
                for (var i = 0, r; r = s[i]; i++) {
                    detail_record_store_main.remove(r);
                }

                Ext.getCmp('del_detail').disable();
            }
        },
        {
            iconCls: 'silk-export',
            id: 'import',
            text: '导入',
            handler: function () {
                var org_id = Ext.getCmp('org_id').getValue();
                var orgcode = '';
                var orgname = '';

                if (org_id == '') {
                    Ext.MessageBox.alert('提示', '请先选择机构');
                    return false;
                }
                for (var i = 0; i < getOilOrgNew.getCount(); i++) {
                    if (getOilOrgNew.getAt(i).get('id') == org_id) {
                        orgcode = getOilOrgNew.getAt(i).get('orgcode');
                        orgname = getOilOrgNew.getAt(i).get('orgname');
                        break;
                    }
                }

                var form = new Ext.form.FormPanel({
                    baseCls: 'x-plain', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
                    items: [
                        {
                            xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
                            id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
                            anchor: '100%'
                        }
                    ]
                });
                var import_win = new Ext.Window({
                    title: '批量导入', width: 400, height: 105, minWidth: 300, minHeight: 100, modal: true,
                    layout: 'fit', plain: true, bodyStyle: 'padding:5px;', buttonAlign: 'center', items: form,
                    buttons: [{
                        text: '导入',
                        handler: function () {
                            if (form.form.isValid()) {
                                if (Ext.getCmp('userfile').getValue() == '') {
                                    Ext.Msg.alert('系统提示', '请选择你要上传的文件');
                                    return;
                                }
                                form.getForm().submit({
                                    url: '/inside.php?t=json&m=oil_account_assign&f=import',
                                    params: {
                                        orgcode: orgcode, org_id: org_id, orgname: orgname
                                    },
                                    success: function (form, action) {
                                        var r = Ext.decode(action.response.responseText).data;
                                        if (r.length > 0) {
                                            for (var i = 0; i < r.length; i++) {

                                                var _rec = new detail_record({
                                                    vice_id: r[i].vice_id,
                                                    vice_no: r[i].vice_no,
                                                    truck_no_custom: r[i].truck_no_custom,
                                                    truck_no: r[i].truck_no,
                                                    org_name: r[i].org_name,
                                                    assign_money: r[i].assign_money != 0 ? r[i].assign_money : '',
                                                    assign_jifen: r[i].assign_jifen != 0 ? r[i].assign_jifen : '',
                                                    oil_com: r[i].oil_com,
                                                    fanli_region: r[i].fanli_region,
                                                    main_no: r[i].main_no,
                                                    jifen: r[i].jifen,
                                                    vice_status: r[i].vice_status,
                                                    status: '主站待分配'
                                                });

                                                detail_record_store_main.add(_rec);

                                                Ext.MessageBox.alert('提示', '导入成功');
                                            }
                                        } else {
                                            Ext.MessageBox.alert('提示', '表格中没有有效数据');
                                        }

                                        import_win.close();
                                    },
                                    failure: function (form, action) {
                                        Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).msg);
                                    }
                                })
                            }
                        }
                    }, {
                        text: '关闭',
                        handler: function () {
                            import_win.close();
                        }
                    }]
                });
                import_win.show();
            }
        },
        {
            iconCls: 'silk-add',
            text: '模板下载',
            id: 'tpldownload',
            handler: function () {
                Ext.MessageBox.confirm('模板下载', '确认下载?', function showResult(btn) {
                    if (btn == 'yes') {
                        window.location.href = '/download/templates/accountAssign.xlsx';
                    }
                });
            }
        }
    ],
    columns: [
        new Ext.grid.RowNumberer({header: '序号', width: 35}),
        {
            id: 'vice_no',
            header: '<span style="color: red">*</span> 卡号',
            dataIndex: 'vice_no',
            width: 150,
            editor: new Ext.form.TextField({
                id: 'detail_vice_no',
                regex: /^\d{1,20}$/,    //Task #53151
                regexText: '卡号格式不正确',
                enableKeyEvents: true,
                allowBlank: false,
                blankText: '卡号必填',
                listeners: {
                    keyup: function (field, e) {
                        if (e.keyCode == 40) {    //向下键
                            detail_grid_cell_add();
                        }
                    },
                }
            })
        },
        {
            id: 'truck_no_custom',
            header: '自录入车牌',
            dataIndex: 'truck_no_custom',
            width: 80,
            editor: new Ext.form.TextField({
                id: 'detail_truck_no_custom',
            }),
            renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                if (value == 'null') {
                    return '';
                } else {
                    return value;
                }
            }
        },
        {
            id: 'truck_no',
            header: '车牌号',
            dataIndex: 'truck_no',
            width: 80,
            editor: new Ext.form.TextField({
                id: 'detail_truck_no',
                disabled: true
            })
        },
        {
            id: 'assign_money',
            header: '分配金额',
            dataIndex: 'assign_money',
            width: 80,
            editor: new Ext.form.NumberField({
                id: 'detail_assign_money',
                enableKeyEvents: true,
                listeners: {
                    keyup: function (field, e) {
                        if (e.keyCode == 40) {    //向下键
                            detail_grid_cell_add();
                        }
                    }
                }
            })
        },
        {
            id: 'assign_jifen',
            header: '分配积分',
            dataIndex: 'assign_jifen',
            width: 80,
            editor: new Ext.form.NumberField({
                id: 'detail_assign_jifen',
                enableKeyEvents: true,
                listeners: {
                    keyup: function (field, e) {
                        if (e.keyCode == 40) {    //向下键
                            detail_grid_cell_add();
                        }
                    }
                }
            })
        },
        {
            id: 'oil_com',
            header: '油卡类型',
            dataIndex: 'oil_com',
            width: 60,
            editor: new Ext.form.TextField({
                id: 'detail_oil_com',
                disabled: true
            })
        },
        {
            id: 'org_name',
            header: '所属机构',
            dataIndex: 'org_name',
            width: 120,
            editor: new Ext.form.TextField({
                id: 'detail_org_name',
                disabled: true
            })
        },
        {
            id: 'fanli_region',
            header: '积分可用地区',
            dataIndex: 'fanli_region',
            width: 100,
            editor: new Ext.form.TextField({
                id: 'detail_fanli_region',
                disabled: true
            })
        },
        {
            id: 'main_no',
            header: '主卡号',
            dataIndex: 'main_no',
            width: 150,
            editor: new Ext.form.TextField({
                id: 'detail_main_no',
                disabled: true
            })
        },
        {
            id: 'jifen',
            header: '积分账户余额',
            dataIndex: 'jifen',
            width: 100,
            editor: new Ext.form.TextField({
                id: 'detail_jifen',
                disabled: true
            })
        },
        {
            id: 'status',
            header: '状态',
            dataIndex: 'status',
            width: 90,
            editor: new Ext.form.TextField({
                id: 'detail_status',
                disabled: true
            })
        },
        {
            id: 'detail_remark_work',
            header: '备注',
            dataIndex: 'detail_remark_work',
            width: 90,
            editor: new Ext.form.TextField({
                id: 'detail_remark_work',
                disabled: false
            })
        },
        {
            id: 'vice_status',
            header: '卡号状态',
            hidden: true,
            dataIndex: 'vice_status',
            width: 90,
            editor: new Ext.form.TextField({
                id: 'vice_status',
                disabled: true
            })
        },
    ]
})

var add_form_panel = new Ext.form.FormPanel({
    region: 'center',
    hideLabels: true,
    trackResetOnLoad: true,
    bodyStyle: 'padding: 10px',
    height: 60,
    items: [
        {// 第一行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '单号：',
                    style: 'padding-left: 24px'
                },
                {
                    xtype: 'displayfield',
                    id: 'no',
                    width: 150,
                    value: ''
                },
                {
                    xtype: 'displayfield',
                    value: '付款机构：',
                    style: 'padding-left: 12px'
                },
                new Ext.form.ComboBox({
                    width: 150,
                    id: 'org_id',
                    hiddenName: 'org_id',
                    triggerAction: 'all',
                    forceSelection: true,
                    mode: 'remote',
                    queryParam:'keyword',
                    minChars:2,
                    displayField: 'org_name',//显示的值
                    valueField: 'id',//后台接收的key
                    store: getOilOrgNew,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        select: function (combo, record, index) {
                            var org_id = Ext.getCmp('org_id').getValue();
                            Ext.Ajax.request({
                                url: '../inside.php?t=json&m=oil_account_assign&f=getOrgMoney',
                                params: {org_id: org_id},
                                method: 'POST',
                                success: function (response, options) {
                                    console.log(Ext.decode(response.responseText).data);
                                    Ext.getCmp('frozen_money').setValue('');
                                    if (response.responseText) {
                                        Ext.getCmp('frozen_money').setValue(Ext.decode(response.responseText).data);
                                    } else {
                                        Ext.getCmp('frozen_money').setValue('');
                                    }
                                }
                            });

                            Ext.getCmp('account_no').enable();//激活扣款账户
                            getDebitAccount.removeAll();
                            getDebitAccount.load({params:{org_id: org_id, flag:'zqx', action:'addOrUpdate'},callback:function () {
                                var firstValue = getDebitAccount.getAt(0).get('account_no');
                                console.log('firstValue--',firstValue)
                                Ext.getCmp('account_no').setValue(firstValue);//设置默认扣款账户
                            }});
                        }
                    }
                }),
                /*new Ext.form.ComboBox({
                    width: 150,
                    id: 'org_id',
                    triggerAction: 'all',
                    allowBlank: false,
                    blankText : '不能为空',
                    mode: 'local',
                    minChars:2,
                    queryParam:'keyword',
                    hiddenName:'org_id',
                    displayField: 'org_name',//显示的值
                    valueField: 'id',//后台接收的key
                    store: getOilOrg,
                    forceSelection: true,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        'focus': function () {
                            getOilOrg.load();
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                        select: function (combo, record, index) {
                            var org_id = Ext.getCmp('org_id').getValue();
                            Ext.Ajax.request({
                                url: '../inside.php?t=json&m=oil_account_assign&f=getOrgMoney',
                                params: {org_id: org_id},
                                method: 'POST',
                                success: function (response, options) {
                                    if (response.responseText) {
                                        Ext.getCmp('frozen_money').setValue(Ext.decode(response.responseText).data);
                                    } else {
                                        Ext.getCmp('frozen_money').setValue();
                                    }
                                }
                            });

                            Ext.getCmp('account_no').enable();//激活扣款账户
                            getDebitAccount.removeAll();
                            getDebitAccount.load({params:{org_id: org_id, flag:'zqx', action:'addOrUpdate'},callback:function () {
                                var firstValue = getDebitAccount.getAt(0).get('account_no');
                                console.log('firstValue--',firstValue)
                                Ext.getCmp('account_no').setValue(firstValue);//设置默认扣款账户
                            }});
                        }
                    }
                }),*/
                {xtype: 'displayfield', value: ' <font color=red>*</font>'},
                {
                    xtype: 'displayfield',
                    value: '分配总金额：',
                    style: 'padding-left: 12px'
                },
                {
                    xtype: 'numberfield',
                    id: 'money_total',
                    width: 150,
                    value: ''
                },
                {
                    xtype: 'displayfield',
                    value: '分配总积分：',
                    style: 'padding-left: 24px'
                },
                {
                    xtype: 'numberfield',
                    id: 'jifen_total',
                    width: 145,
                    value: ''
                }
            ]
        },
        {// 第二行
            xtype: 'compositefield',
            style: 'margin-bottom: 10px',
            items: [
                {
                    xtype: 'displayfield',
                    value: '分配卡数：'
                },
                {
                    xtype: 'numberfield',
                    id: 'assign_num',
                    allowDecimals: false,
                    minValue: 1,
                    width: 150
                },
                {
                    xtype: 'displayfield',
                    value: '扣款账户：',
                    style: 'padding-left: 10px',
                },
                new Ext.form.ComboBox({
                    width: 150,
                    id: 'account_no',
                    hiddenName:'account_no',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    mode: 'local',
                    displayField: 'name',//显示的值
                    valueField: 'account_no',//后台接收的key
                    store: getDebitAccount,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        // 'focus': function () {
                        //     getDebitAccount.load({params:{org_id: Ext.getCmp('org_id').getValue()}});
                        // },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                        select: function (combo, record, index) {
                            var account_no = record.get('account_no');
                            setFrozenMoney(account_no);//设置可分配金额
                            if(account_no == '106' || account_no == '308'){
                                Ext.getCmp('frozen_money').setValue("");
                            }
                        }
                    }
                }),
                {
                    xtype: 'displayfield',
                    value: '可分配金额：',
                    style: 'padding-left: 20px'
                },
                {
                    xtype: 'displayfield',
                    id: 'frozen_money',
                    value: '',
                    width: 150
                },
                {
                    xtype: 'displayfield',
                    value: '状态：',
                    style: 'padding-left: 60px'
                },
                {
                    xtype: 'displayfield',
                    id: 'status',
                    value: '',
                    width: 150
                },
            ]
        },
        {
            xtype: 'compositefield',
            style: 'margin-bottom: 10px',
            items: [
                {
                    xtype: 'displayfield',
                    value: '申请时间：'
                },
                {
                    xtype: 'datefield',
                    id: 'apply_time_field',
                    width: 170,
                    fieldLabel: '申请时间',
                    format: 'Y-m-d H:i:s',
                    name: 'apply_time',
                    allowBlank: false,
                    blankText: '不能为空'
                },
                {
                    xtype: 'displayfield',
                    value: '数据来源：',
                    style: 'padding-left: 36px'
                },
                {
                    xtype: 'hidden',
                    id: 'assign_sn',
                    name: 'assign_sn'
                },
                {
                    xtype: 'displayfield',
                    id: 'data_from',
                    width: 150,
                    value: ''
                }
            ]
        },
        {// 第四行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '备注/内：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype: 'textfield',
                    id: 'remark',
                    width: 600
                },
            ]
        },
        {// 第五行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '备注/外：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype: 'textfield',
                    id: 'remark_work',
                    width: 600
                },
            ]
        },
        detail_add_panel
    ],
    buttons: [{
        text: "保存",
        id: 'btn_save',
        listeners: {
            click: function () {
                var _this = this;
                if (detail_record_store_main.data.length > 0) {
                    var rst;
                    for (var i = 0; i < detail_record_store_main.data.length; i++) {
                        var rec = detail_record_store_main.getAt(i);
                        if(rec.get('vice_status') != '使用') {
                            rst = rec.get('vice_status');
                        }
                    }
                    if(rst){
                        Ext.Msg.show({
                            title: '提示',
                            msg: '分配单中存在非使用状态的卡，是否继续进行？',
                            buttons: {
                                yes: '是',
                                no: '否',
                            },
                            fn: function (e) {
                                if (e == 'yes') {
                                    if (check_input()) {
                                        var _form = _this.ownerCt.ownerCt.getForm();
                                        if (_form.isValid()) {
                                            if (add_update_id) {
                                                //修改
                                                Ext.Ajax.request({
                                                    url: '../inside.php?t=json&m=oil_account_assign&f=checkLastAssign',
                                                    waitMsg: 'Saving Data...',
                                                    params: {details: getDetailStr()},
                                                    success: function (resp) {
                                                        var result = Ext.decode(resp.responseText);

                                                        if(result.code < 0){
                                                            Ext.Msg.confirm('系统提示', '该副卡('+result.data+')24小时内已提交圈回申请，确认继续提交？', function (btn) {
                                                                if (btn == 'yes') {
                                                                    updateData(_form,add_update_id);
                                                                }else{
                                                                    grid_list.getSelectionModel().clearSelections();
                                                                    grid_list.getSelectionModel().selectFirstRow();
                                                                    detailStore.removeAll();

                                                                    var cell = grid_list.getSelectionModel().getSelections()
                                                                    var row_index = grid_list.getStore().indexOf(cell[0]);
                                                                    rowClick(grid_list, row_index);
                                                                    btnInit([10]);
                                                                    add_win.hide();
                                                                }
                                                            });
                                                        }else{
                                                            //新增
                                                            updateData(_form,add_update_id);
                                                        }
                                                    },
                                                    failure: function (resp) {
                                                        Ext.Msg.alert('系统提示', "系统错误");
                                                    },
                                                });
                                            }
                                            else {
                                                //新增
                                                Ext.Ajax.request({
                                                    url: '../inside.php?t=json&m=oil_account_assign&f=checkLastAssign',
                                                    waitMsg: 'Saving Data...',
                                                    params: {details: getDetailStr()},
                                                    success: function (resp) {
                                                        var result = Ext.decode(resp.responseText);
                                                        if(result.code < 0){
                                                            Ext.Msg.confirm('系统提示', '该副卡('+result.data+')24小时内已提交圈回申请，确认继续提交？', function (btn) {
                                                                if (btn == 'yes') {
                                                                    addData(_form);
                                                                }else{
                                                                    grid_list.getSelectionModel().clearSelections();
                                                                    grid_list.getSelectionModel().selectFirstRow();
                                                                    detailStore.removeAll();

                                                                    var cell = grid_list.getSelectionModel().getSelections()
                                                                    var row_index = grid_list.getStore().indexOf(cell[0]);
                                                                    rowClick(grid_list, row_index);
                                                                    btnInit([10]);
                                                                    add_win.hide();
                                                                }
                                                            });
                                                        }else{
                                                            //新增
                                                            addData(_form);
                                                        }
                                                    },
                                                    failure: function (resp) {
                                                        Ext.Msg.alert('系统提示', "系统错误");
                                                    },
                                                });
                                            }
                                        }
                                    }
                                }
                            },
                        });
                    }else{
                        if (check_input()) {
                            var _form = _this.ownerCt.ownerCt.getForm();
                            if (_form.isValid()) {
                                if (add_update_id) {
                                    //修改
                                    Ext.Ajax.request({
                                        url: '../inside.php?t=json&m=oil_account_assign&f=checkLastAssign',
                                        waitMsg: 'Saving Data...',
                                        params: {details: getDetailStr()},
                                        success: function (resp) {
                                            var result = Ext.decode(resp.responseText);
                                            if(result.code < 0){
                                                Ext.Msg.confirm('系统提示', '该副卡('+result.data+')24小时内已提交圈回申请，确认继续提交？', function (btn) {
                                                    if (btn == 'yes') {
                                                        updateData(_form,add_update_id);
                                                    }else{
                                                        grid_list.getSelectionModel().clearSelections();
                                                        grid_list.getSelectionModel().selectFirstRow();
                                                        detailStore.removeAll();

                                                        var cell = grid_list.getSelectionModel().getSelections()
                                                        var row_index = grid_list.getStore().indexOf(cell[0]);
                                                        rowClick(grid_list, row_index);
                                                        btnInit([10]);
                                                        add_win.hide();
                                                    }
                                                });
                                            }else{
                                                updateData(_form,add_update_id);
                                            }
                                        },
                                        failure: function (resp) {
                                            Ext.Msg.alert('系统提示', "系统错误");
                                        },
                                    });
                                }
                                else {
                                    //新增
                                    Ext.getCmp('btn_save').disable();
                                    Ext.Ajax.request({
                                        url: '../inside.php?t=json&m=oil_account_assign&f=checkLastAssign',
                                        waitMsg: 'Saving Data...',
                                        params: {details: getDetailStr()},
                                        success: function (resp) {
                                            var result = Ext.decode(resp.responseText);
                                            if(result.code < 0){
                                                Ext.Msg.confirm('系统提示', '该副卡('+result.data+')24小时内已提交圈回申请，确认继续提交？', function (btn) {
                                                    if (btn == 'yes') {
                                                        addData(_form);
                                                    }else{
                                                        Ext.getCmp('btn_save').enable();
                                                        grid_list.getSelectionModel().clearSelections();
                                                        grid_list.getSelectionModel().selectFirstRow();
                                                        detailStore.removeAll();

                                                        var cell = grid_list.getSelectionModel().getSelections()
                                                        var row_index = grid_list.getStore().indexOf(cell[0]);
                                                        rowClick(grid_list, row_index);
                                                        btnInit([10]);
                                                        add_win.hide();
                                                    }
                                                });
                                            }else{
                                                //新增
                                                addData(_form);
                                            }
                                        },
                                        failure: function (resp) {
                                            Ext.getCmp('btn_save').enable();
                                            Ext.Msg.alert('系统提示', "系统错误");
                                        },
                                    });
                                }
                            }
                        }
                    }
                }else{
                    Ext.Msg.alert('提示','分配明细不能为空');
                }

            }

        }
    }, {
        text: "取消",
        'id': 'btn_cancel',
        handler: function () {
            var edit = this.ownerCt.ownerCt.getForm().isDirty();
            if (edit || detail_edit) {
                Ext.Msg.show({
                    title: '提示',
                    msg: '有未保存的数据，是否保存？',
                    buttons: {
                        yes: '是',
                        no: '否',
                        cancel: '取消'
                    },
                    fn: function (e) {
                        if (e == 'yes') {
                            Ext.getCmp('btn_save').fireEvent('click');
                        }
                        if (e == 'no') {
                            add_win.hide();
                        }
                    }
                });
            } else {
                add_win.hide();
            }
        }
    }]
});

function addData(_form){
    Ext.getCmp('btn_save').disable();
    _form.submit({
        url: '../inside.php?t=json&m=oil_account_assign&f=add',
        params: {
            a_org_id: Ext.getCmp('org_id').getValue(),
            a_money_total: Ext.getCmp('money_total').getValue(),
            a_jifen_total: Ext.getCmp('jifen_total').getValue(),
            a_assign_num: Ext.getCmp('assign_num').getValue(),
            a_remark: Ext.getCmp('remark').getValue(),
            a_remark_work: Ext.getCmp('remark_work').getValue(),
            details: getDetailStr()
        },
        waitMsg: 'Saving Data...',
        success: function (resp, opts) {
            add_win.hide();
            Ext.MessageBox.alert("消息!", Ext.decode(opts.response.responseText).msg);
            assignStore.reload({
                callback: function () {
                    grid_list.getSelectionModel().clearSelections();
                    grid_list.getSelectionModel().selectFirstRow();
                    detailStore.removeAll();

                    var cell = grid_list.getSelectionModel().getSelections()
                    var row_index = grid_list.getStore().indexOf(cell[0]);
                    rowClick(grid_list, row_index);
                    btnInit(cell[0].get('status'));
                }
            });
        },
        failure: function (resp, opts) {
            Ext.MessageBox.alert("消息!", Ext.decode(opts.response.responseText).msg);
            Ext.getCmp('btn_save').enable();
        }
    });
}

function updateData(_form,add_update_id){
    _form.submit({
        url: url,
        params: {
            id: add_update_id,
            a_org_id: Ext.getCmp('org_id').getValue(),
            a_money_total: Ext.getCmp('money_total').getValue(),
            a_jifen_total: Ext.getCmp('jifen_total').getValue(),
            a_apply_time: Ext.getCmp('apply_time_field').getValue(),
            a_assign_num: Ext.getCmp('assign_num').getValue(),
            a_remark: Ext.getCmp('remark').getValue(),
            a_remark_work: Ext.getCmp('remark_work').getValue(),
            a_status: getAssignNoStatus(),
            details: getDetailStr()
        },
        waitMsg: 'Saving Data...',
        success: function (resp, opts) {
            add_win.hide();
            Ext.MessageBox.alert("消息!", Ext.decode(opts.response.responseText).msg);
            detailStore.removeAll();
            assignStore.removeAll();
            assignStore.load();
            btnInit();
        },
        failure: function (resp, opts) {
            Ext.MessageBox.alert("消息!", Ext.decode(opts.response.responseText).msg);
        }
    });
}

//获取详情数据
function getDetailStr() {
    var string = '';
    if (detail_record_store_main.data.length > 0) {
        for (var i = 0; i < detail_record_store_main.data.length; i++) {
            var rec = detail_record_store_main.getAt(i);
            (string) ? string = string + '|' : '';
            string = string + rec.get('vice_no') + '#' +
                rec.get('assign_money') + '#' +
                rec.get('assign_jifen') + '#' +
                rec.get('truck_no') + '#' +
                rec.get('truck_no_custom') + '#' +
                rec.get('status')+ '#' +
                rec.get('vice_status') + '#' +
                rec.get('detail_remark_work');
        }
    }
    return string;
}

//获取工单状态
function getAssignNoStatus() {
    var wait_num = 0;
    var len = detail_record_store_main.data.length;
    if (len > 0) {
        for (var i = 0; i < len; i++) {
            var rec = detail_record_store_main.getAt(i);
            if (rec.get('status') == '主站分配失败') {
                return -10;
            }else if (rec.get('status') == '主站分配中') {
                return 8;
            }else if (rec.get('status') == '主站待分配') {
                wait_num++;
            }
        }
    }
    if (wait_num) {
        return 15;
    } else {
        return 10;
    }
}

//主窗体
if (!add_win) {
    add_win = new Ext.Window({
        layout: "border",
        width: 1000,
        height: 565,
        title: '分配申请维护',
        closeAction: 'hide',
        plain: true,
        modal: true,
        items: [add_form_panel]
    });
}

function setFrozenMoney(account_no) {
    if(account_no.indexOf('108') != -1){//现金账户
        Ext.getCmp('org_id').fireEvent('select');
    }else if(account_no.indexOf('208') != -1){//授信账户
        Ext.Ajax.request({
            url: '../inside.php?t=json&m=oil_account_assign&f=getCreditAcountBalance',
            params: {account_no: account_no},
            method: 'POST',
            success: function (response, options) {
                if (response.responseText) {
                    Ext.getCmp('frozen_money').setValue(Ext.decode(response.responseText).data.use_money);
                } else {
                    Ext.getCmp('frozen_money').setValue();
                }
            }
        });
    }else if(account_no.indexOf('308') != -1){//托管主卡账户
        Ext.getCmp('frozen_money').setValue();
    }
}
//添加
function add() {
    add_update_id = '';
    detail_edit = false;
    var curDate = new Date().format('Y-m-d H:i:s');

    initFormInput(0);//初始化表单控件
    Ext.getCmp('account_no').disable();

    add_form_panel.getForm().setValues([
        {id: 'no', value: ''},
        {id: 'org_id', value: ''},
        {id: 'account_no', value: ''},
        {id: 'money_total', value: ''},
        {id: 'jifen_total', value: ''},
        {id: 'assign_num', value: ''},
        {id: 'status', value: '待审核'},
        {id: 'frozen_money', value: ''},
        {id: 'vice_status', value: ''},
        {id: 'data_from', value: 'GSP'},
        {id: 'remark', value: ''},
        {id: 'remark_work', value: ''},
        {id: 'apply_time', value: curDate}
    ]);

    Ext.getCmp('add_detail').enable();
    Ext.getCmp('del_detail').disable();
    Ext.getCmp('btn_save').enable();
    Ext.getCmp('btn_cancel').enable();

    detail_record_store_main.removeAll();
    add_win.show();
    add_form_panel.getForm().reset();

    Ext.getCmp('detail_vice_no').allowBlank = true;

    Ext.Ajax.request({
        url: '../inside.php?t=json&m=oil_account_assign&f=getSn',
        params: {},
        method: 'POST',
        success: function (resp, opts) {
            console.log(Ext.decode(resp.responseText))
            Ext.getCmp('assign_sn').setValue(Ext.decode(resp.responseText).data)
        },
        failure: function (resp, opts) {

        }
    });
}



function autoAssign() {
    var sm = grid_list.getSelectionModel();
    var data = sm.getSelections();
    var status = false;

    console.log('data.length',data.length);
    if(data.length > 0 ){
        data.forEach(function (v,k) {
            if(v['json']['customer_audit'] == '1'){
                status = true;
            }
        });
    }
    if (status) {
        console.log('当前分配单为客户审核模式，确认操作吗？');
        Ext.MessageBox.confirm('提示', '当前分配单为客户审核模式，确认操作吗？', function showResult(btn) {
            if(btn == 'yes'){
                autoAssign2();
            }
        });
    }else{
        autoAssign2();
    }
}
function autoAssign2(){
    Ext.getCmp('btnAuto').disable();
    var sm = grid_list.getSelectionModel();
    var data = sm.getSelections();

    Ext.Ajax.request({
        url: '../inside.php?t=json&m=oil_account_assign&f=chekeFaild',
        params: {assign_id: data[0].id},
        method: 'POST',
        success: function (resp, opts) {
            flag = Ext.decode(resp.responseText).data;
            if(flag){
                //提示
                Ext.MessageBox.confirm('提示', '此单含有“分配超时”的子卡，请先去主站核实。是否确认再次下发 ？', function showResult(btn){
                    if (btn == 'yes')
                    {
                        autoAssignAction();
                    }else{
                        Ext.getCmp('btnAuto').enable();
                    }
                });
            }else{
                //不提示
                autoAssignAction();
            }
        },
        failure: function (resp, opts) {
            Ext.MessageBox.alert("消息!", Ext.decode(resp.responseText).msg);
        }
    });

}

function autoAssignAction() {
    var sm = grid_list.getSelectionModel();
    var data = sm.getSelections();
    var status = false;

    console.log('data.length',data.length);
    if(data.length > 0 ){
        data.forEach(function (v,k) {
            if(v['json']['customer_audit'] == '1'){
                status = true;
            }
        });
    }
    if (status) {
        console.log('当前分配单为客户审核模式，确认操作吗？');
        Ext.MessageBox.confirm('提示', '当前分配单为客户审核模式，确认操作吗？', function showResult(btn) {
            if(btn == 'yes'){
                autoAssignAction2();
            }
        });
    }else{
        autoAssignAction2();
    }
}
//主站自动分配
function autoAssignAction2() {
    Ext.getCmp('btnAuto').disable();
    var sm = grid_list.getSelectionModel();
    var data = sm.getSelections();
    var ids  = detailStore.data.items;
    var arr = [];



    if(ids.length > 0){
        ids.forEach(function(val){
            if(val.json.vice_status != '使用'){
                arr.push(val.json.vice_status);
            }
        });
        if(arr.length > 0){
            Ext.MessageBox.confirm('提示', '分配单中存在非使用状态的卡，是否继续进行？', function showResult(btn){
                if (btn == 'yes')
                {
                    //通过
                    Ext.Ajax.request({
                        url: '../inside.php?t=json&m=oil_account_assign&f=manualAssign',
                        params: {assign_id: data[0].id},
                        method: 'POST',
                        success: function (resp, opts) {
                            Ext.MessageBox.alert("消息!", Ext.decode(resp.responseText).msg);
                            // assignStore.reload({
                            //     callback: function () {
                            //         _sm.getSelected().data;
                            //         var cell = grid_list.getSelectionModel().getSelections()
                            //         var row_index = grid_list.getStore().indexOf(cell[0]);
                            //         rowClick(grid_list, row_index);
                            //     }
                            // });
                            assignStore.removeAll();
                            assignStore.load();//刷新页面
                            detailStore.removeAll();
                            btnInit();
                        },
                        failure: function (resp, opts) {
                            Ext.MessageBox.alert("消息!", Ext.decode(resp.responseText).msg);
                        }
                    });
                }
            });
        }else{
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=oil_account_assign&f=manualAssign',
                params: {assign_id: data[0].id},
                method: 'POST',
                success: function (resp, opts) {
                    Ext.MessageBox.alert("消息!", Ext.decode(resp.responseText).msg);
                    assignStore.reload({
                        callback: function () {
                            _sm.getSelected().data;
                            var cell = grid_list.getSelectionModel().getSelections()
                            var row_index = grid_list.getStore().indexOf(cell[0]);
                            rowClick(grid_list, row_index);
                        }
                    });
                    btnInit(20);
                },
                failure: function (resp, opts) {
                    Ext.MessageBox.alert("消息!", Ext.decode(resp.responseText).msg);
                }
            });
        }
    }else{
        //通过
        Ext.Ajax.request({
            url: '../inside.php?t=json&m=oil_account_assign&f=manualAssign',
            params: {assign_id: data[0].id},
            method: 'POST',
            success: function (resp, opts) {
                Ext.MessageBox.alert("消息!", Ext.decode(resp.responseText).msg);
                assignStore.reload({
                    callback: function () {
                        _sm.getSelected().data;
                        var cell = grid_list.getSelectionModel().getSelections()
                        var row_index = grid_list.getStore().indexOf(cell[0]);
                        rowClick(grid_list, row_index);
                    }
                });
                btnInit(20);
            },
            failure: function (resp, opts) {
                Ext.MessageBox.alert("消息!", Ext.decode(resp.responseText).msg);
            }
        });
    }
}
/**
 * 初始化表单控件
 */
function initFormInput(status, account_type) {
    if (( (status < 1 && status != -8) || status == 15) && account_type != 20) {
        url = '../inside.php?t=json&m=oil_account_assign&f=update';
        Ext.getCmp('org_id').enable();
        Ext.getCmp('account_no').enable();
        Ext.getCmp('money_total').enable();
        Ext.getCmp('jifen_total').enable();
        Ext.getCmp('assign_num').enable();
        Ext.getCmp('vice_status').enable();
        Ext.getCmp('apply_time_field').enable();

        Ext.getCmp('add_detail').enable();
        Ext.getCmp('del_detail').disable();
        Ext.getCmp('import').enable();
    } else if(status == -8) {
        url = '../inside.php?t=json&m=oil_account_assign&f=updateForRemark';
        Ext.getCmp('org_id').disable();
        // Ext.getCmp('account_no').disable();
        Ext.getCmp('money_total').enable();
        Ext.getCmp('jifen_total').disable();
        Ext.getCmp('assign_num').disable();
        Ext.getCmp('vice_status').disable();
        Ext.getCmp('apply_time_field').disable();

        Ext.getCmp('add_detail').disable();
        Ext.getCmp('del_detail').disable();
        Ext.getCmp('import').disable();
    }else {
        url = '../inside.php?t=json&m=oil_account_assign&f=updateForRemark';
        Ext.getCmp('org_id').disable();
        // Ext.getCmp('account_no').disable();
        Ext.getCmp('money_total').disable();
        Ext.getCmp('jifen_total').disable();
        Ext.getCmp('assign_num').disable();
        Ext.getCmp('vice_status').disable();
        Ext.getCmp('apply_time_field').disable();

        Ext.getCmp('add_detail').disable();
        Ext.getCmp('del_detail').disable();
        Ext.getCmp('import').disable();
    }
}
//修改
function update() {

    detail_edit = false;

    var sm = grid_list.getSelectionModel();
    var data = sm.getSelections();

    data[0] ? add_update_id = data[0].get("id") : '';
    var status = data[0] ? data[0].get("status") : '';
    var account_type = data[0] ? data[0].get("account_type") : '';
    var no_type = data[0] ? data[0].get("no_type") : '';

    //txb 如果是主站圈回的单子，换弹出框
    if(no_type == 'QH'){
        updateturn();
        return false;
    }

    initFormInput(status, account_type);//初始化表单控件

    var num = data.length;
    var record = "";
    for (var i = 0; i < num; i++) {
        record = data[i].data;
        if (record.id == add_update_id) {
            //延迟50毫秒显示数据，不然时间显示不出来
            function fn() {
                var status = '';
                if (record.status == '-1') {
                    status = '已驳回';
                } else if (record.status == '0') {
                    status = '待审核'
                } else if (record.status == '1') {
                    status = '已审核'
                }

                var data_from = '';
                if (record.data_from == '1') {
                    data_from = 'GSP';
                } else if (record.data_from == '2') {
                    data_from = 'WEB'
                } else if (record.data_from == '3') {
                    data_from = 'APP'
                }

                add_form_panel.getForm().setValues([
                    {id: 'no', value: record.no},
                    {id: 'org_id', value: record.org_id},
                    {id: 'money_total', value: (record.money_total != 0 ? record.money_total : '')},
                    {id: 'jifen_total', value: (record.jifen_total != 0 ? record.jifen_total : '')},
                    {id: 'assign_num', value: record.assign_num},
                    {id: 'status', value: status},
                    {id: 'vice_status', value: record.vice_status},
                    {id: 'data_from', value: data_from},
                    {id: 'apply_time_field', value: record.apply_time},
                    {id: 'remark', value: record.remark},
                    {id: 'remark_work', value: record.remark_work},
                ]);

                setFrozenMoney(record.account_no);//设置可分配金额

                //设置扣款账户
                var pm = {org_id: record.org_id,flag:'zqx'};
                if(account_type != 20){//非授信账户
                    pm.action = 'addOrUpdate';
                }
                getDebitAccount.removeAll();
                getDebitAccount.load({params:pm,callback:function () {
                    Ext.getCmp('account_no').setValue(record.account_no);
                    if((record.status >= 1 && record.status != 15) || account_type == 20){
                        Ext.getCmp('account_no').disable();
                    }
                }});

                var tmpOrgId = record.org_id;
                getOilOrgNew.load({
                    params:{id: tmpOrgId},
                    callback:function () {
                        add_form_panel.getForm().setValues([
                            {id: 'org_id', value: tmpOrgId },
                        ]);
                    }
                });

                detail_record_store_main.removeAll();
                Ext.Ajax.request({
                    url: '../inside.php?t=json&m=oil_account_assign&f=getDetailInfo',
                    params: {assign_id: record.id,u:1},
                    method: 'POST',
                    success: function (response, options) {
                        var obj = Ext.decode(response.responseText).data;
                        if (obj) {
                            if (obj.length > 0) {
                                for (var i = 0; i < obj.length; i++) {
                                    var rec = obj[i];

                                    var _r = new detail_record({
                                        vice_id: rec.card_vice_id,
                                        vice_no: rec.vice_no,
                                        truck_no_custom: rec.truck_no_custom,
                                        truck_no: rec.truck_no,
                                        org_name: rec.org_name,
                                        assign_money: rec.assign_money != 0 ? rec.assign_money : '',
                                        assign_jifen: rec.assign_jifen != 0 ? rec.assign_jifen : '',
                                        oil_com: rec.oil_com,
                                        fanli_region: rec.province,
                                        main_no: rec.main_no,
                                        jifen: rec.jifen,
                                        status: rec.status,
                                        vice_status: rec.vice_status,
                                        detail_remark_work:rec.remark_work,
                                    });

                                    detail_record_store_main.add(_r);
                                }
                            }
                        }
                    }
                });
            }

            var task = new Ext.util.DelayedTask(fn);
            task.delay(50);
            break;
        }
    }

    add_win.show();
    add_form_panel.getForm().reset();
    Ext.getCmp('detail_vice_no').allowBlank = true;
}

//表单验证
function check_input() {
    var flag = true;

    var msg = '';
    var org_id = Ext.getCmp('org_id').getValue();

    if (org_id == '') {
        msg += msg ? '<br>机构必选' : '机构必选';
        flag = false;
    }

    var assign_num = parseInt(Ext.getCmp('assign_num').getValue());
    var money_total = Ext.getCmp('money_total').getValue();
    money_total = money_total ? parseFloat(money_total) : 0;
    var jifen_total = Ext.getCmp('jifen_total').getValue() ? parseFloat(Ext.getCmp('jifen_total').getValue()) : 0;
    // if(!money_total && !jifen_total){
    //     Ext.MessageBox.alert('提示', '分配总金额和分配总积分不能均为空');
    //     return false;
    // }
    if(money_total && jifen_total){
        Ext.MessageBox.alert('提示', '金额和积分不能同时分配');
        return false;
    }
    var detail_num = detail_record_store_main.getCount();

    var vice_arr = new Array();
    var frozen_money = 0;
    var jifen = 0;
    if (detail_record_store_main.getCount() > 0) {
        for (var i = 0; i < detail_record_store_main.getCount(); i++) {
            var rec = detail_record_store_main.getAt(i).data;
            vice_arr[i] = rec.vice_no;
            if (rec.assign_money) {
                frozen_money += parseFloat(rec.assign_money);
            }
            if (rec.assign_jifen) {
                jifen += parseFloat(rec.assign_jifen);
            }
        }
    }

    frozen_money = frozen_money.toFixed(2);
    jifen = jifen.toFixed(2);

    if (vice_arr) {
        var viceArr = vice_arr.sort();
        for (var j = 0; j < vice_arr.length; j++) {
            if (viceArr[j] == viceArr[j + 1]) {
                msg += '卡号重复，保存失败';
                flag = false;
                break;
            }
        }
    }

    if (assign_num != detail_num) {
        Ext.MessageBox.alert('提示', '保存失败，请检查录入数据');
        return false;
    }

    if (jifen != jifen_total || frozen_money != money_total) {
        Ext.MessageBox.alert('提示', '保存失败，请检查录入数据');
        return false;
    }

    if (flag === false) {
        Ext.MessageBox.alert('提示', msg);
    }
    return flag;
}