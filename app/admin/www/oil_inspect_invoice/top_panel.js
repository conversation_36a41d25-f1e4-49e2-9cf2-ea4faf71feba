var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 40,
	items  : 
	[
	     {
			xtype : 'compositefield',
			items : 
			[
				{
					xtype: 'displayfield',
					value: '供应商签约主体：'
				},
				new Ext.form.ComboBox({
					width: 220,
					id: 'up_operator_id',
					hiddenName: "up_operator_id",
					allowBlank: false,
					blankText: '不能为空',
					mode: 'local',
					value: '',
					fieldLabel: '供应商签约主体',
					triggerAction: 'all',
					forceSelection: true,
					emptyText: '请选择..',
					displayField: 'company_name',
					valueField: 'id',
					store: b_operator
				}),
				requireTip,
				{
					xtype: 'displayfield',
					value: '客户签约主体：',
				},
				new Ext.form.ComboBox({
					width: 220,
					id: 'down_operator_id',
					hiddenName: "down_operator_id",
					allowBlank: false,
					blankText: '不能为空',
					mode: 'local',
					value: '',
					fieldLabel: '销售类运营商',
					triggerAction: 'all',
					forceSelection: true,
					emptyText: '请选择..',
					displayField: 'company_name',
					valueField: 'id',
					store: c_operator
				}),
				requireTip,
				{
					xtype: 'displayfield',
					value: '油品类型：'
				},
				new Ext.form.ComboBox({
					width: 120,
					id: 'oil_super_id',
					hiddenName: "oil_super_id",
					allowBlank: true,
					blankText: '不能为空',
					mode: 'local',
					value: '',
					fieldLabel: '油品类型',
					triggerAction: 'all',
					forceSelection: true,
					emptyText: '请选择..',
					displayField: 'name',
					valueField: 'id',
					store: oil_type
				}),
				{
					xtype:'button',
					text:'查询',
					style : 'padding-left : 10px;',
					handler: function()
					{
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
					}
				},
				{
					xtype: 'button',
					text: '重置',
					style: 'padding-left : 10px;',
					handler: function () {
						top_panel.getForm().reset();
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
						btnInit();
					}
				},

			]
		 },
	]
});

function GetDate($flag) {
	var dd = new Date();
	var y = dd.getFullYear();
	var m = dd.getMonth() + 1;//获取当前月份的日期
	var d = '';
	if ($flag == 1) {
		d = "01";
	} else if ($flag == 2) {
		d = dd.getDate();
	} else if ($flag == 3) {
		var now = new Date();
		var date = new Date(now.getTime() - 1 * 24 * 3600 * 1000);
		y = date.getFullYear();
		m = date.getMonth() + 1;
		d = date.getDate();
	}
	if (m.toString().length == 1) {
		m = '0' + m;
	}
	if (d.toString().length == 1) {
		d = '0' + d;
	}

	return y + "-" + m + "-" + d;
}