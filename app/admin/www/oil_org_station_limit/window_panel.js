//定义列表
var oil_station_list = new Ext.grid.ColumnModel({
    defaults: {
        width: 120,
        align: 'left',
        sortable: false
    },
    columns: [
        {header: '<div align=center>名称</div>', dataIndex: 'station_name', sortable: true, width: 250, align: 'right'},
        {header: '<div align=center>品牌</div>', dataIndex: 'station_brand_name', sortable: true, width: 150, align: 'right'},
        {header: '<div align=center>所在省/市</div>', dataIndex: 'province_city_name', sortable: true, width: 150, renderer:function(value, cellMeta, record) {
                return record.data.provice_name+"/"+record.data.city_name;
            }},
        {header: '<div align=center>油站地址</div>', dataIndex: 'address', sortable: true, width: 250},
    ]
});

var gridOilStationDetailList = new Ext.grid.GridPanel({
    loadMask: true,
    enableColumnMove: false,
    enableColumnResize: true,
    height: 280,
    region: 'south',
    title: '',
    stripeRows: true,//斑马颜色
    autoScroll: true,
    store: getStationLimitDetail,
    cm: oil_station_list,
    tbar: [],
    //分页
    // bbar: pageTool,
});

//资金账户信息
var panel_data = new Ext.Panel({
    region: "center",
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    buttonAlign: 'center',
    height: 450,
    autoScroll: true,
    trackResetOnLoad: true,
    labelWidth: 70,
    border: false,
    items: [
        new Ext.form.FieldSet({
            title: '',
            columnWidth: .1,
            autoHeight: true,
            layout: 'column',
            border: false,
            anchor: '100%',
            items: [
                {
                    xtype: 'compositefield',
                    items: [
                        {
                            xtype: 'displayfield',
                            id: 'org_info',
                            style : 'padding: 10px 0;',
                            value: '<span style="width: 80px;display: inline-block;text-align: right;">机构：</span>',
                        }
                    ]
                },
                {
                    xtype: 'compositefield',
                    items: [
                        {
                            xtype: 'displayfield',
                            id: 'availableSupplier',
                            height: 85,
                            style : 'padding: 10px 0px;',
                            value: '<span style="width: 80px;display: inline-block;text-align: right;">可用供应商：</span>',
                        }
                    ],
                },
                {
                    xtype: 'compositefield',
                    items: [
                        {
                            xtype: 'displayfield',
                            id: 'availableBrand',
                            height: 85,
                            style : 'padding: 10px 0px;',
                            value: '<span style="width: 80px;display: inline-block;text-align: right;">可用品牌：</span>',
                        }
                    ],
                    autoHeight: true
                },
                {
                    xtype: 'compositefield',
                    items: [
                        {
                            xtype: 'displayfield',
                            id: 'availableOilName',
                            height: 65,
                            style : 'padding: 10px 0px;',
                            value: '<span style="width: 110px;display: inline-block;text-align: right;">可用商品品类：</span>',
                        }
                    ],
                    autoHeight: true
                },
                {
                    xtype: 'compositefield',
                    items: [
                        {
                            xtype: 'displayfield',
                            id: 'availableRebate',
                            height: 65,
                            style : 'padding: 10px 0px;',
                            value: '<span style="width: 110px;display: inline-block;text-align: right;">可用油站返利档位：</span>',
                        }
                    ],
                    autoHeight: true
                },
                {
                    xtype: 'compositefield',
                    items: [
                        {
                            xtype: 'displayfield',
                            id: 'availableStation',
                            style : 'padding: 10px 0px;',
                            value: '<span style="width: 80px;display: inline-block;text-align: right;">可用油站：</span>',
                        }
                    ]
                }
            ]
        }),
        gridOilStationDetailList
    ],
});

