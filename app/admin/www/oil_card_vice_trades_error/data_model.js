/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_card_vice_trades_error';
var pagesize = 50;

/**
 * 生成URL
 */
function getUrl(m, f) {
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}

var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName, 'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id", "dateCreated", "lastUpdated", "operator", "dataUpdated", "parentCard", "cardNo", "tradeId", "reward", "holders",
            "amount", "balance", "extype", "address", "addressPadding", "fueldate", "model", "count", "price", "cardType",
            "tradeStatus", "accountType", "status", "reason", "createtime", "updatetime", "deleted_at","card_from_text"]
    ),
});