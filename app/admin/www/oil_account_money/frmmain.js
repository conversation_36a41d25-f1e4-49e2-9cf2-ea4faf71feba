Ext.onReady(function(){	
	Ext.Ajax.request({
		url:'../inside.php?t=json&m=login&f=getConfig',
		method:'post',
		success: function(resp,opts) { 
			var respText = Ext.util.JSON.decode(resp.responseText); 
			if(!respText.id) {
				window.location.href = '../login/';
			}
			else {				
				var main_panel = new Ext.TabPanel(
						{xtype:"tabpanel",
						region:"center",
						items:[grid_service]
				});
				main_panel.setActiveTab(grid_service);
				
				new Ext.Viewport(
					{enableTabScroll:true,
						layout:"border",
						items:[
						       top_panel,
						       main_panel,
						       ]}
				);
				store_main.addListener('beforeload',function(){
					this.baseParams = top_panel.getForm().getValues();
					this.baseParams.start = 0;
					this.baseParams.limit = pageTool.pageSize;
				});
				store_main.load();
				editSource('oil_account_money',grid_service);
			}
		}
	});
});