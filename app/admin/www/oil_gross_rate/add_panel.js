/**
 * 定义添加组件容器类
 */
var addPanel = {
	id:'',
	formId:'',
	winId:'',
	//初始化容器
	init: function (params) {
		if(this.id){//编辑
			Ext.getCmp(this.formId).getForm().loadRecord({data: params.data});
		}
	},
	//获取组件容器
	getPanel: function (formId, winId, id) {
		this.id = id;
		this.formId = formId;
		this.winId = winId;
		var panel = [
			{// 第一行
				xtype : 'compositefield',
				items :
					[
						{
							xtype: 'displayfield',
							value: '类&nbsp;&nbsp;&nbsp;&nbsp;别：'
						},
                        {
                            xtype: 'combo',
                            hiddenName: 'type',
                            id: 'type_add_id',
                            mode: 'local',
                            width: 100,
                            emptyText: '请选择..',
                            triggerAction: 'all',
                            forceSelection: true,
                            allowBlank:false,
                            displayField: 'key',
                            valueField: 'value',
                            value: '',
                            store: new Ext.data.SimpleStore({
                                fields: ['value', 'key'],
                                data: [['1', '传统油卡'],['2', '电子油卡']]
                            }),
                        },
						{xtype: 'displayfield',value: ' <font color=red>*</font>'},
					]
			},
            {// 第二行
                xtype : 'compositefield',
                style:'padding-top:5px;',
                items :
                    [
                        {
                            xtype: 'displayfield',
                            value: '毛利率：'
                        },
                        {
                            xtype: 'textfield',
                            id: 'rate_fee',
                            width: 150,
                            allowBlank:false,
                            blankText: "毛利率不能为空！",
                            regex: /^\d+(\.\d{1,2})?$/,
                            regexText: '只能输入数字',
                            hiddenName: 'rate_fee',
                        },
                        {xtype: 'displayfield',value: ' <font color=red>*</font>'},
                    ]
            },
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: '备&nbsp;&nbsp;&nbsp;&nbsp;注：'
						},
						{
							xtype:"textarea",
							id:"remark",
							width:350,
							height : 120
						},
					]
			}
		];

		return panel;
	},
	//提交表单
	submit: function () {
		var _form = Ext.getCmp(this.formId).getForm();
		var that = this;
		if (_form.isValid()) {
			var url = '';
			if (this.id) {
				url = getUrl(controlName, 'edit');
			} else {
				url = getUrl(controlName, 'create');
			}
			_form.submit({
				url: url,
				waitMsg: 'Saving Data...',
				params:{id:that.id},
				success: function (form, action) {
					Ext.MessageBox.alert("消息!", action.result.msg);
					main_store.removeAll();
					main_store.load();
					oilTest.closeWin(that.winId)
				},
				failure: function (form, action) {
					Ext.MessageBox.alert("消息!", action.result.msg);
				}
			});
		} else {
			Ext.MessageBox.alert("提示", '输入有误，请检查');
		}
	}
}