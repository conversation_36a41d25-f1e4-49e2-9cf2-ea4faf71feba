//交易类型列表
var getTradeType = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_card_vice_trades&f=getTradeType', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['key', 'value'])
});

/**
 * 获取日期函数
 * @param $flag 1 为月初，2为当前日期，3为5天前的日期
 * @returns {String}
 */
function GetDate($flag) {
    var dd = new Date();
    var y = dd.getFullYear();
    var m = dd.getMonth() + 1;//获取当前月份的日期
    var d = '';
    if ($flag == 1) {
        d = "01";
    } else if ($flag == 2) {
        d = dd.getDate();
    } else if ($flag == 3) {
        var now = new Date();
        var date = new Date(now.getTime() - 5 * 24 * 3600 * 1000);
        y = date.getFullYear();
        m = date.getMonth() + 1;
        d = date.getDate();
    }
    if (m.toString().length == 1) {
        m = '0' + m;
    }
    if (d.toString().length == 1) {
        d = '0' + d;
    }

    return y + "-" + m + "-" + d;
}

//油品机构列表
var getOilOrg = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_card_vice&f=getOilOrg', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: ''
    }, ['orgcode', 'org_name'])
});
getOilOrg.load();

//积分可用地区（全国+省/直辖市)
var getFanliProvince = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_card_vice&f=getProvince', method: "POST"}),
    autoLoad: false,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: ''
    }, ['id', 'province']),
    listeners: {
        'beforeload': function () {
            this.baseParams.all = true;
        }
    }
});


var top_panel = new Ext.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 15px',
    height: 115,
    items: [
        {
            xtype: 'compositefield',
            items: [
                {xtype: 'hidden', id: 'loadPageFlag', value: true},
                {xtype: 'displayfield', value: '主卡号：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_main_no',
                    name: 'main_no',
                    width: 220,
                },
                {xtype: 'displayfield', value: '持卡人：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_card_owner',
                    name: 'card_owner',
                    width: 100,
                },
                {xtype: 'displayfield', value: '油卡类型：', width: 60, style: 'text-align: right'},
                {
                    xtype: 'combo',
                    hiddenName: 'oil_com',
                    id: 's_oil_com',
                    width: 100,
                    triggerAction: 'all',
                    forceSelection: true,
                    value: '',
                    displayField: 'oil_com',
                    valueField: 'id',
                    store: new Ext.data.Store({
                        url: '../inside.php?t=json&m=oil_card_main&f=getOilComList',
                        reader: new Ext.data.JsonReader({}, ['id', 'oil_com'])
                    }),
                },
                {xtype: 'displayfield', value: '积分可用地区：'},
                new Ext.form.ComboBox({
                    width: 120,
                    id: 's_fanli_region',
                    hiddenName: 'regions_name',
                    mode: 'local',
                    value: '',
                    buffer: 200,
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'province',//显示的值
                    valueField: 'province',//后台接收的key
                    store: getFanliProvince,
                    emptyText: '',
                    enableKeyEvents: true,
                    listeners: {
                        'focus': function () {
                            getFanliProvince.load();
                        },
                        'expand': function () {
                            getFanliProvince.load();
                        },
                    }
                }),
                {xtype: 'displayfield', value: '油品：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_oil_name',
                    name: 'oil_name',
                    width: 100,
                    form_column: 'a.oil_name',
                    form_column_operator: 'like',
                },
                {xtype: 'displayfield', value: '油品类型：', style: 'text-align: right'},
                new Ext.form.ComboBox({
                    width: 80,
                    id: 's_oil_type',
                    mode: 'local',
                    hiddenName: 'oil_type',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'value',//显示的值
                    valueField: 'key',//后台接收的key
                    emptyText: '',
                    enableKeyEvents: true,
                    store: new Ext.data.SimpleStore({
                        fields: ['value', 'key'],
                        data: [['空', '000'], ['汽油', '1'], ['柴油', '2'], ['非油产品', '3']]
                    })
                }),
                {xtype: 'displayfield', value: '地点：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_trade_place',
                    name: 'trade_place',
                    width: 90,
                },
            ]
        },
        {//第2行
            xtype: 'compositefield',
            style: 'padding-top:5px;',
            items: [
                //机构要只展示一级、二级
                {
                    xtype: 'displayfield',
                    value: '机构：',
                    style: 'padding-left : 12px;'
                },
                new Ext.form.ComboBox({
                    width: 238,
                    id: 's_oil_org',
                    hiddenName: 'orgcode',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    mode: 'local',
                    displayField: 'org_name',//显示的值
                    valueField: 'orgcode',//后台接收的key
                    store: getOilOrg,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        'focus': function () {
                            getOilOrg.load();
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                    }
                }),
                {
                    xtype: "checkbox",
                    id: "org_flag",
                    name: 'org_flag',
                    checked: true,
                },
                {
                    xtype: 'displayfield',
                    value: '(包含下级)',
                },
                {xtype: 'displayfield', value: '卡号：', style: 'padding-left:12px;'},
                {
                    xtype: 'textfield',
                    width: 120,
                    name: 'vice_no',
                    id: 's_vice_no'
                },
                {
                    xtype:'button',
                    text:'多选',
                    listeners:{
                        'click':function () {
                            show_search_win('请手动输入卡号，以回车换行：','s_vice_no', function () {
                                store_main.removeAll();
                                store_main.load();
                            });//search_win.js 里
                        }
                    }
                },
                {xtype: 'displayfield', value: '消费地区：'},
                new Ext.form.ComboBox({
                    width: 100,
                    id: 's_consume_region',
                    hiddenName: 'consume_region',
                    mode: 'local',
                    value: '',
                    buffer: 200,
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'province',//显示的值
                    valueField: 'id',//后台接收的key
                    store: getFanliProvince,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        'focus': function () {
                            getFanliProvince.load({params: {ifSupportNull: true}});
                        },
                        'expand': function () {
                            getFanliProvince.load({params: {ifSupportNull: true}});
                        }
                    }
                }),


                {xtype: 'displayfield', value: '交易时间：'},
                {
                    xtype: "datefield",
                    id: "s_start_time",
                    name: 'tradetimeGe',
                    format: 'Y-m-d',
                    value:GetDate(3),
                    width: 100,
                },
                {xtype: 'displayfield', value: '-'},
                {
                    xtype: "datefield",
                    id: "s_end_time",
                    name: 'tradetimeLe',
                    format: 'Y-m-d',
                    value:GetDate(2),
                    width: 100,
                }

            ]
        },
        {//第3行
            xtype: 'compositefield',
            style: 'padding-top:5px;',
            items: [
                {xtype: 'displayfield', value: '交易类型：', style: 'text-align: right'},
                new Ext.form.ComboBox({
                    width: 100,
                    id: 's_trade_type',
                    hiddenName: 'trade_type',
                    mode: 'local',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'value',//显示的值
                    valueField: 'key',//后台接收的key
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    store: getTradeType
                }),
                {
                    xtype: 'displayfield',
                    value: '计算返利：'
                },
                {
                    xtype: 'combo',
                    hiddenName: 'is_fanli',
                    id: 's_is_fanli',
                    mode: 'local',
                    width: 80,
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'value',
                    valueField: 'key',
                    value: '',
                    store: new Ext.data.SimpleStore({
                        fields: ['key', 'value'],
                        data: [['0', '未算'], ['1', '已算']]
                    }),
                },
                {
                    xtype: 'button',
                    text: '查询',
                    style: 'padding-left : 23px;',
                    handler: function () {
                        /*if (Ext.getCmp('s_start_time').getValue() == '' || Ext.getCmp('s_end_time').getValue() == '') {
                            Ext.Msg.alert('提示', '交易时间不能为空');
                        } else {
                            store_main.removeAll();
                            store_main.load();
                        }*/
                        store_main.load();
                    }
                },
                {
                    xtype: 'button',
                    text: '重置',
                    style: 'padding-left : 10px;',
                    handler: function () {
                        Ext.getCmp('s_main_no').setValue('');
                        Ext.getCmp('s_card_owner').setValue('');
                        Ext.getCmp('s_oil_com').setValue('');
                        Ext.getCmp('s_fanli_region').setValue('');
                        Ext.getCmp('s_oil_org').setValue('');
                        Ext.getCmp('org_flag').setValue(true);
                        Ext.getCmp('s_vice_no').setValue('');
                        Ext.getCmp('s_start_time').setValue(GetDate(3));
                        Ext.getCmp('s_end_time').setValue(GetDate(2));
                        Ext.getCmp('s_trade_type').setValue();
                        Ext.getCmp('s_is_fanli').setValue();
                        store_main.removeAll();//移除原来的数据
                        store_main.load();//加载新搜索的数据
                    }
                },
            ]
        }
    ]
});

