/**
 * 获取日期函数
 * @param $flag 1 为上月初，2为上月最后一天
 * @returns {String}
 */
function GetDateStr($flag)
{
	var dd = new Date();
	var y = dd.getFullYear();
	var m = dd.getMonth();//获取当前月份的日期
	if(m.toString().length==1){
		m = '0'+m;
	}
	var d = '';
	if($flag == 1)
		d = "01";
	else
	{
		var new_date = new Date(y,m,1);//取当年当月中的第一天
		d = (new Date(new_date.getTime()-1000*60*60*24)).getDate();
		if(d.toString().length==1){
			d = '0'+d;
		}
	}

	return y+"-"+m+"-"+d;
}

//废弃
function showDel()
{
    Ext.MessageBox.confirm('提示', '确定要废弃吗？', function showResult(btn) {
            if (btn == 'yes') {
                var myMask = new Ext.LoadMask(Ext.getBody(),{msg:"正在处理中..."});
                myMask.show();
                var data =grid_service.getSelectionModel().getSelections();
                if(data.length > 0){
                    var ids = '';
                    for(var i = 0; i < data.length; i++){
                        ids += ids ? ',' + data[i].get('id') : data[i].get('id');
                    }
                }

                Ext.Ajax.request({
                    url: '../inside.php?t=json&m='+controlName+'&f=discardTradeRecords',
                    params:{ids:ids},
                    method: 'POST',
                    success: function (resp, opt) {
                        var result = Ext.decode(resp.responseText);
                        if(result.code == 0){
                            store_main.removeAll();
                            store_main.load();
                        }
                        myMask.hide();
                        Ext.Msg.alert('提示',result.msg);
                    },
                    failure: function () {
                        //alert('2222')
                    }
                })
            }
        }
    );
}

//移入正式库
function moveToTrades()
{
    Ext.MessageBox.confirm('提示', '确定要移入正式库吗？', function showResult(btn) {
            if (btn == 'yes') {
                var myMask = new Ext.LoadMask(Ext.getBody(),{msg:"正在处理中..."});
                myMask.show();
                var data =grid_service.getSelectionModel().getSelections();
                if(data.length > 0){
                    var ids = '';
                    for(var i = 0; i < data.length; i++){
                        ids += ids ? ',' + data[i].get('id') : data[i].get('id');
                    }
                }

                Ext.Ajax.request({
                    url: '../inside.php?t=json&m='+controlName+'&f=moveToTrades',
                    params:{ids:ids},
                    method: 'POST',
                    success: function (resp, opt) {
                        var result = Ext.decode(resp.responseText);
                        if(result.code == 0){
                            store_main.removeAll();
                            store_main.load();
                        }
                        myMask.hide();
                        Ext.Msg.alert('提示',result.msg);
                    },
                    failure: function () {
                        //alert('2222')
                    }
                })
            }
        }
    );
}

//导出异常数据
function importException(){
    Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
            if (btn === 'yes') {
                var params = top_panel.getForm().getValues(true);
                window.location.href = '../inside.php?t=json&m='+controlName+'&f=getList&_export=1&'+params;
            }
        }
    );
}
/**************************** 编辑 **************************/
var smartwin1;//窗体
var add_win;//添加窗口
//主容器
Ext.QuickTips.init();

var panel_add = new Ext.FormPanel({
    region: "center",
    hideLabels: true,
    fileUpload: true,
    bodyStyle: 'padding: 10px',
    buttonAlign: 'center',
    width: 100,
    height: 50,
    minWidth: 200,
    minHeight: 100,
    trackResetOnLoad: true,
    labelWidth: 70,
    defaultType: 'textfield',
    border: false,
    items: [
        {
            xtype: 'compositefield',
            items: [
                {xtype: 'displayfield', value: '油品：', style:'text-align: right'},
                {
                    xtype : 'textfield',
                    id    : 'edit_oil_name',
                    name    : 'oil_name',
                    width : 150,
                    form_column: 'a.oil_name',
                    form_column_operator: 'like',
                },
            ]
        },
        {
            xtype: 'compositefield',
            style: 'padding-top: 10px;',
            items: [
                {xtype: 'displayfield', value: '地点：', style:'text-align: right'},
                {
                    xtype : 'textfield',
                    id    : 'edit_trade_place',
                    name    : 'trade_place',
                    width : 150,
                    form_column: 'a.trade_place',
                    form_column_operator: 'like',
                },
            ]
        },
    ],
    buttons: [
        {
            text: '保存',
            handler: function () {
                formSubmit();
            }
        },{
            text: '取消',
            handler: function () {
                smartwin1.hide();
            }
        }],

})
//弹出窗体
if (!smartwin1) {//主窗体
    smartwin1 = new Ext.Window({
        layout:"border",
        width:300,
        height:150,
        title:'信息编辑',
        closeAction:'hide',
        modal:true,
        plain: true,
        items:[panel_add],
    });
}

//编辑
function showEdit(){
    var sm   = grid_service.getSelectionModel();
    var data = sm.getSelections();
    data[0] ? add_update_id = data[0].get("id") : '';
    //修改
   var num = store_main.getCount();//获取总个数
    for (var i = 0; i < num; i++) {
        var record = store_main.getAt(i);
        if (record.get('id') == add_update_id) {
            panel_add.getForm().setValues([
                {id: 'oil_name', value: record.get('oil_name')},
                {id: 'trade_place', value: record.get('trade_place')},
            ]);
        }
    }
    smartwin1.show();
}
function formSubmit(){
    var _form = panel_add.getForm();
    if (add_update_id) {
        _form.submit({
            url: '../inside.php?t=json&m=oil_card_vice_trades&f=update',
            params: {
                id: add_update_id,
                oil_name: Ext.getCmp('edit_oil_name').getValue(),
                trade_place: Ext.getCmp('edit_trade_place').getValue()
            },
            waitMsg: 'Saving Data...',
            success: function (form, action) {
                Ext.MessageBox.alert("系统提示!", action.result.msg);
                smartwin1.hide();
                store_main.load();

            },
            failure: function (form, action) {
                Ext.MessageBox.alert("消息!", action.result.msg);
            }
        });
    }
}