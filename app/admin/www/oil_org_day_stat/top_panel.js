var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 70,
	items  : 
	[
	     {
			xtype : 'compositefield',
			items : 
			[
				{
					xtype: 'displayfield',
					value: '客户名称：'
				},
				new Ext.form.ComboBox({
					width: 180,
					hiddenName: 'orgroot',
					triggerAction: 'all',
					forceSelection: true,
					mode: 'remote',
					queryParam:'keyword',
					minChars:2,
					displayField: 'org_name',//显示的值
					valueField: 'orgcode',//后台接收的key
					store: getOilOrg,
					emptyText: '请选择..',
					enableKeyEvents: true
				}),
				{
					xtype: 'displayfield',
					value: '当日归属销售：'
				},
				{
					xtype : 'textfield',
					id    : 'aplowneridname',
					name  : 'aplowneridname_lk',
					width : 100
				},
				{
					xtype: 'displayfield',
					value: '当日归属省区：'
				},
				{
					xtype : 'textfield',
					id    : 'new_department',
					name  : 'new_department_lk',
					width : 100
				},
				{
					xtype: 'displayfield',
					value: '当日归属大区：'
				},
				{
					xtype : 'textfield',
					id    : 'new_area',
					name  : 'new_area_lk',
					width : 100
				},
				{
					xtype: 'displayfield',
					value: '当日销售事业部：'
				},
				{
					xtype : 'textfield',
					id    : 'oil_cross_centername',
					name  : 'oil_cross_centername_lk',
					width : 100
				},
				{
					xtype: 'displayfield',
					value: '油品支持人员：'
				},
				{
					xtype : 'textfield',
					id    : 'new_fossilsupporter',
					name  : 'new_fossilsupporter_lk',
					width : 100
				},

				{
					xtype:'button',
					text:'查询',
					style : 'padding-left : 10px;',
					handler: function()
					{
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
					}
				},
				{
					xtype: 'button',
					text: '重置',
					style: 'padding-left : 10px;',
					handler: function () {
						top_panel.getForm().reset();
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
						btnInit();
					}
				},
			]
		 },
		{
			xtype : 'compositefield',
			items :
				[
					{
						xtype: 'displayfield',
						value: '报告日期：'
					},
					{
						xtype: "datefield",
						id: "report_dateGe",
						name: "report_dateGe",
						value:GetDateStr(1),
						format: 'Y-m-d',
						width: 100,
						listeners: {
							"select": function(mult, e) {
								var date1 = Ext.getCmp('report_dateGe').getValue();
								var date2 = Ext.getCmp('report_dateLe').getValue();

								var  startDate = Date.parse(date1);
								var  endDate = Date.parse(date2);
								var days=(endDate - startDate)/(1*24*60*60*1000);

								if(days > 90){
									Ext.Msg.alert('提示', '时间区间不能为超过90天！');
									Ext.getCmp('report_dateGe').setValue(GetDateStr(1));
								}

							}
						}
					},
					{
						xtype: 'displayfield',
						value: '—'
					},
					{
						xtype: "datefield",
						id: "report_dateLe",
						name: "report_dateLe",
						value:GetDateStr(2),
						format: 'Y-m-d',
						width: 100,
						listeners: {
							"select": function(mult, e) {
								var date1 = Ext.getCmp('report_dateGe').getValue();
								var date2 = Ext.getCmp('report_dateLe').getValue();

								var  startDate = Date.parse(date1);
								var  endDate = Date.parse(date2);
								var days=(endDate - startDate)/(1*24*60*60*1000);

								if(days > 90){
									Ext.Msg.alert('提示', '时间区间不能为超过90天！');
									Ext.getCmp('report_dateLe').setValue(GetDateStr(2));
								}

							}
						}
					},
				]
		}
	]
});