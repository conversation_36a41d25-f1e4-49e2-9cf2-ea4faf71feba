let controlName = 'oil_own_station';
let pageStart = 0;
let pageSize = 50;
let pagesize = 50;

/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}

// 获取采购渠道列表
let getOilSupplier = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({
        url: '../inside.php?t=json&m=oil_station_supplier&f=getOilSupplier',
        method: "POST"
    }),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id', 'supplier_name'])
});

// 获取油站服务区列表
let getOilStationAreas = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_station_area&f=getBindArea', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id', 'code', 'name', 'full_name'])
});

// 油站卡明细
let detailCardStore = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_station_card&f=getList', method: "POST"}),
    //params:{start:0,limit:5},
    reader: new Ext.data.JsonReader({
            totalProperty: 'data.total',
            root: 'data.data'
        },
        ['id', 'code', 'vice_no', 'oil_com', 'oil_com_value', 'card_from', 'card_from_value', 'card_main_no','is_edit',
            'card_main_id', 'vice_balance', 'card_main_balance', 'last_operator', 'createtime','property_txt','property']
    )
});

let getOilStations = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName, 'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id", "full_name", "station_name", "station_code"]
    ),
});

// 搜索卡号
var storeSearchViceNos = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_station_card&f=searchViceNos', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['vice_no'])
});

let getNewOilStations = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName, 'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id", "station_name", "station_code", "service_area_statistics_type_name","status_name"]
    ),
});

let main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName, 'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id", "station_name", "station_code", "contact", "service_area_statistics_type_name", "contact_phone",
            "card_count", "area_id", "area_name", "status", "status_name", "supplier_id", "supplier_name", "creator",
            "createtime", "auditor", "audit_time", "modifier", "updatetime", "admin_mark",'proxy_txt',"is_proxy","is_edit"]
    ),
});
