/***************************
 *  调度中心
 ***************************/
function createForCharge() {
    oilTest.createForCharge();
}

function createForOther() {
    oilTest.createForOther();
}

//充值模版认领
function chargeForTemplate() {
    oilTest.chargeForTemplate();
}

//撤销认领
function cancelClaim() {
    oilTest.cancelClaim();
}

//添加
function create() {
    oilTest.add();
}
//编辑
function edit() {
    oilTest.update();
}
//删除
function remove(){
    oilTest.delete();
}
//导出
function exportData() {
    oilTest.export();
}
//刷新数据
function refreshData() {
    oilTest.refreshData();
}

//自动认领设置
function autoSet()
{
    oilTest.audoSwitch();
}

//上游认领
function createUpstream() {
    oilTest.createUpstream();
}