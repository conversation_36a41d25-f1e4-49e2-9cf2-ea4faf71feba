#msgBox {
    display: none;
    position: absolute;
    right: 10px;
    bottom: 10px;
    z-index: 99999;
    width: 310px;
    border-radius: 2px;
    font-size: 13px;
}
#msgTitle {
    height: 18px;
    line-height: 18px;
    width: 300px;
    font-size: 14px;
    padding: 5px;
    color: #fff;
}
#msgContent {
    width: 300px;
    padding: 6px 5px;
    color: #222222;
}
#msgClose {
    position: absolute;
    right: 5px;
    top: 0;
    height: 18px;
    line-height: 18px;
    padding: 5px;
    color: #fff;
    cursor: pointer;
}

/**
蓝色主题
 */
.msgBox-default {
    border: 1px #3b97d7 solid;
}

.msgBox-default .msgTitle {
    background: rgba(0, 123, 169, 0.9);
    border-bottom: 1px #006d96 solid;
    color: #fff;

}
.msgBox-default .msgContent {
    background-color: rgba(204, 241, 255, 0.9);
}

/**
橙色主题
 */
.msgBox-warning {
    border: 1px #ff8a44 solid;
}

.msgBox-warning .msgTitle {
    background: rgba(255, 88, 36, 1);
    border-bottom: 1px #e17534 solid;
}

.msgBox-warning .msgContent {
    background-color: rgba(255, 228, 220, 0.9);
}

/**
红色主题
 */
.msgBox-error {
    border: 1px #6a0025 solid;
}

.msgBox-error .msgTitle {
    background: rgba(106, 0, 37, 1);
    border-bottom: 1px #610123 solid;
}

.msgBox-error .msgContent {
    background-color: rgba(255, 219, 232, 0.9);
}