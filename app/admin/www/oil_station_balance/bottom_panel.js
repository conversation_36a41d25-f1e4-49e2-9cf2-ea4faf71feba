var bottomTitle = '';

var detail_grid = new Ext.grid.GridPanel({
    region:"center",
    loadMask: true,
    store: detailStore,
    cm: new Ext.grid.ColumnModel([
        new Ext.grid.RowNumberer({header: '序号', width: 35}),
        {header: '站点名称', dataIndex: 'station_name', sortable: true, width: 240},
        {header: '站点编码', dataIndex: 'station_code', sortable: true, width: 180},
        {header: '合作方式', dataIndex: 'cooperation_type_value', sortable: true, width: 100},
        {header : '出账方式',dataIndex : 'bill_type_value',width : 100},
        {header : '所属服务区',dataIndex : 'station_area',width : 240},
        {header : '服务区编码',dataIndex : 'area_code',width : 100},
        {header: '所属供应商', dataIndex: 'supplier_name', sortable: true, width: 380},
        {header: '供应商ID', dataIndex: 'supplier_id', sortable: true, width: 100},
    ]),
    bbar: new Ext.PagingToolbar({
        pageSize: 100,
        store: detailStore,
        displayInfo: true,
        displayMsg: '显示第{0}条到{1}条记录,一共{2}条',
        emptyMsg: '没有记录'
    })
});

var detail_card_grid = new Ext.grid.GridPanel({
    region:"center",
    loadMask: true,
    store: detailCardStore,
    cm: new Ext.grid.ColumnModel([
        new Ext.grid.RowNumberer({header: '序号', width: 35}),
        {header: '卡号', dataIndex: 'vice_no', sortable: true, width: 240},
        {header: '卡类型', dataIndex: 'card_from_value', sortable: true, width: 240},
        {header : '卡品牌',dataIndex : 'oil_com_value',width : 150},
        {header : '卡片余额',dataIndex : 'vice_balance',width : 150},
        {header: '对应主卡号', dataIndex: 'card_main_no', sortable: true, width: 150},
        {header : '主卡余额',dataIndex : 'card_main_balance',width : 150},
        {header: '所属站点', dataIndex: 'station_name', sortable: true, width: 240},
        {header: '站点编码', dataIndex: 'code', sortable: true, width: 140},
        // {header : '所属服务区',dataIndex : 'receipt_title',width : 300},
        // {header: '服务区编码', dataIndex: 'start_time', sortable: true, width: 140},
        // {header: '所属供应商', dataIndex: 'end_time', sortable: true, width: 140},
        // {header: '供应商ID', dataIndex: 'is_on_value', width: 100},
    ]),
    bbar: new Ext.PagingToolbar({
        pageSize: 100,
        store: detailCardStore,
        displayInfo: true,
        displayMsg: '显示第{0}条到{1}条记录,一共{2}条',
        emptyMsg: '没有记录'
    }),
    sm: new Ext.grid.RowSelectionModel({
        singleSelect: true,
        listeners: {
            selectionchange: function(data) {
                if (data.getCount()){
                    (Ext.getCmp('stationCard_remove')) ? Ext.getCmp('stationCard_remove').enable() : '';//删除按钮
                    (Ext.getCmp('stationCard_update')) ? Ext.getCmp('stationCard_update').enable() : '';//修改按钮
                }else{
                    (Ext.getCmp('stationCard_remove')) ? Ext.getCmp('stationCard_remove').disable() : '';//删除按钮
                    (Ext.getCmp('stationCard_update')) ? Ext.getCmp('stationCard_update').disable() : '';//修改按钮
                }
            }
        }
    }),
    tbar: new Ext.Toolbar({
        items: [
            {text: '添加', iconCls: 'silk-add', id: 'stationCard_add', disabled: true, handler: function(){
                stationCardAdd();
            }},
            // {text: '编辑', iconCls: 'silk-edit', id: 'stationCard_update', disabled: true, handler: function(){
            //     stationCardUpdate();
            // }},
            {text: '删除', iconCls: 'silk-delete', id: 'stationCard_remove', disabled: true, handler: function(){
                stationCardRemove();
            }},
        ]
    })
});

//加载底部数据
function loadBottomData(flag) {
    if(flag === '油站详情'){
        detailStore.removeAll();
        detailStore.baseParams = {'balance_id':crow.id,'start':0,'limit':500};
        detailStore.load();
        //
        // if (crow.cooperation_type == 10) {
        //     detail_grid.getColumnModel().setColumnHeader(1, '运营商码');
        // } else if (crow.cooperation_type  == 20) {
        //     return detail_grid.getColumnModel().setColumnHeader(1, '油站编码');
        // } else if (crow.cooperation_type == 30) {
        //     detail_grid.getColumnModel().setColumnHeader(1, '主卡卡号');
        // }

    }else if(flag === '卡片详情'){
        console.log('切换======');
        console.log(crow);
        detailCardStore.removeAll();
        detailCardStore.baseParams = {'balance_id':crow.id,'start':0,'limit':500};
        detailCardStore.load();
    }
}

//底部表格
var bottom_panel = new Ext.TabPanel({
    region:"south",
    activeTab:0,
    frame:true,
    height:210,
    items:
        [
            {
                xtype:'panel',
                id:'a_panel',
                title: '油站详情',
                layout:'border',
                hidden:true,
                items:[
                    detail_grid,
                ]
            },
            {
                xtype:'panel',
                title: '卡片详情',
                layout:'border',
                items:[
                    detail_card_grid,
                ]
            },
        ],
    listeners:{
        tabchange:function(tp,p){
            bottomTitle = p.title;
            if(crow){
                loadBottomData(p.title);
            }

        }
    }
});

function stationCardWindow() {

    var preData  = arguments[0],
        status = 0;

    if(crow.cooperation_status == 2){
        Ext.MessageBox.alert("系统提示", '该供应商已停止合作');
        return false;
    }

    if(preData){ //编辑操作
        status = 1;
        var url = '../inside.php?t=json&m=oil_station_card&f=edit',
            submitParams = {balance_id: crow.id, id: preData.id};
    }else{ //新增操作
        var url = '../inside.php?t=json&m=oil_station_card&f=createByBalance',
            submitParams  = {balance_id: crow.id};
    }
    console.log('==ggggg==');
    console.log(preData);

    var stationCardForm = new Ext.form.FormPanel({
        hideLabels: true,
        buttonAlign: 'center',
        width: 210,
        frame: true,
        bodyStyle: 'background-color: white; padding: 10px',
        region: 'center',
        items: [
            {// 第一行
                xtype: 'compositefield',
                style: 'margin-bottom: 5px;',
                items: [
                    {
                        xtype: 'compositefield',
                        items: {
                            xtype: 'displayfield',
                            style: 'font-size:14px;font-weight:bold;color:gray;',
                            id: 'title'
                        }
                    }
                ]
            },
            {
                xtype: 'compositefield',
                style: 'margin-bottom: 5px;margin-top:15px;',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '副卡卡号：',
                        style: 'padding-left: 0px;'
                    },
                    {
                        xtype: 'textfield',
                        id: 'vice_no',
                        width: 260,
                        allowBlank:false,
                        value: '',
                    },
                    {xtype: 'displayfield', value: ' <font color=red>*</font>'},
                ]
            }
        ],
        buttons:[
            {
                text: '确定',
                handler: function(){
                    var _form = stationCardForm.getForm();
                    if(_form.isValid()){
                        _form.submit({
                            url     : url,
                            waitMsg : 'Saving Data...',
                            success : function(form, action){
                                Ext.MessageBox.alert("系统提示", action.result.msg);
                                stationCardWindow.hide();
                                detailCardStore.load({params:{'balance_id': crow.id}});
                                //main_store.removeAll();//移除原来的数据
                                //main_store.load();//加载新搜索的数据
                                enterSearch();
                            },
                            failure : function (form, action){
                                Ext.MessageBox.alert("系统提示", action.result.msg);
                            },
                            params: submitParams
                        });
                    }
                }
            },
            {
                text: '取消',
                handler: function() {
                    stationCardWindow.hide();
                }
            }
        ]
    });

    var stationCardWindow = new Ext.Window({
        layout:"border",
        width: 400,
        height: 180,
        title:'添加副卡',
        closeAction:'destroy',
        plain: true,
        modal: true,
        items:[stationCardForm]
    });
    stationCardWindow.show();

    if (status == 0) {
        stationCardForm.getForm().reset();
    }

    stationCardForm.getForm().loadRecord({data : preData});
    stationCardForm.getForm().setValues([
        {id: 'id', value: crow.id},
        {id: 'title', value: crow.title},
    ]);
}

function stationCardAdd() {
    stationCardWindow();
}

//删除
function stationCardRemove(){
    Ext.Msg.confirm('系统提示', '确定删除该副卡吗？', function(btn){
        if(btn == 'yes'){
            var id = detail_card_grid.getSelectionModel().getSelections()[0].data.id;
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=oil_station_card&f=remove',
                success: function(resp){
                    console.log('==删除返回结果==');
                    console.log(resp);
                    var result = Ext.decode(resp.responseText);
                    console.log(result);
                    if (result.code != 0 || result.success != true) {
                        Ext.Msg.alert('系统提示', result.msg);
                        return false;
                    }
                    Ext.Msg.alert('系统提示', '删除成功');
                    detailCardStore.load({params:{'balance_id': crow.id}});
                    enterSearch();
                },
                failure: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                },
                params: { ids: id }
            });
        }
    });
}

//点击列表，获取底部数据
function rowClick(thisGrid,rowIndex,e) {
    console.log('点击========');
    console.log(bottomTitle);
    if(bottomTitle){
        loadBottomData(bottomTitle);
    }
}
