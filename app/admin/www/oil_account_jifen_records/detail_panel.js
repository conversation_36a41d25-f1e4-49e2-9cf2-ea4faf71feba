var detailwin;//扫描窗体
var date = new Date();  //当前日期
var monthStart = new Date(date.getFullYear(), date.getMonth(), 1);  //当前日期第一天

var detail_top_panel = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 50,
    items: [
        {// 第一行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '交易方向：'
                },
                {
                    xtype: 'combo',
                    hiddenName: 'trade_type',
                    id: 'trade_type',
                    mode: 'local',
                    width: 80,
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'name',
                    valueField: 'id',
                    store: new Ext.data.SimpleStore({
                        fields: ['id', 'name'],
                        data: [['1', '收入'], ['-1', '支出']]
                    }),
                },
                {
                    xtype: 'displayfield',
                    value: '单据类型：'
                },
                {
                    xtype: 'combo',
                    hiddenName: 'no_type',
                    id: 'no_type',
                    width: 100,
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'name',
                    valueField: 'id',
                    store: new Ext.data.Store({
                        url: '../inside.php?t=json&m='+controlName+'&f=getNoTypeStatus',
                        reader: new Ext.data.JsonReader({
                            totalProperty:'',
                            root:'data'
                        }, ['id', 'name'])
                    }),
                    listeners:{
                        'focus':function (combo) {
                            combo.getStore().load();
                        }
                    }
                },
                {
                    xtype: 'displayfield',
                    value: '单据号：'
                },
                {
                    xtype: 'textfield',
                    name: 'no',
                    id: 'no',
                    width: 110,
                },
                {
                    xtype: 'displayfield',
                    value: '查询日期：',
                },
                {
                    xtype: "datefield",
                    width: 100,
                    id: 's_start_time',
                    name:'start_time',
                    value: monthStart,
                    format: 'Y-m-d',
                },
                {
                    xtype: 'displayfield',
                    value: '~'
                },
                {
                    xtype: "datefield",
                    width: 100,
                    id: 's_end_time',
                    name:'end_time',
                    value: date,
                    format: 'Y-m-d',
                },
                {
                    xtype: 'button',
                    text: '查询',
                    style: 'padding-left : 10px;',
                    handler: function () {
                        jifenRecordsStore.removeAll();
                        jifenRecordsStore.load();
                    }
                },
                {
                    xtype: 'button',
                    text: '重置',
                    style: 'padding-left : 10px;',
                    handler: function () {
                        Ext.getCmp('s_start_time').setValue(monthStart);
                        Ext.getCmp('s_end_time').setValue(date);
                        Ext.getCmp('trade_type').setValue('');
                        Ext.getCmp('no_type').setValue('');
                        Ext.getCmp('no').setValue('');
                        jifenRecordsStore.removeAll();
                        jifenRecordsStore.load();
                    }
                },
            ]
        },
    ]
});

//积分账户明细
var detail_grid = new Ext.grid.GridPanel({
    store: jifenRecordsStore,
    loadMask: true,
    region: 'south',
    height: 320,
    cm: new Ext.grid.ColumnModel({
        defaults : {
            width : 120,
            align : 'left',
            sortable : false
        },
        columns : [
        new Ext.grid.RowNumberer(),
        {header: '<div align=center>交易方向</div>', dataIndex: 'trade_type', width: 60, renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
        	if(value == '1'){
                return '收入';
            }else if(value == '-1'){
                return '支出';
            }else{
                return '';
            }
        }},
        {header: '<div align=center>发生积分</div>', dataIndex: 'jifen', width: 120,align:'right'},
        {header: '<div align=center>结余积分</div>', dataIndex: 'after_jifen', width: 120,align:'right'},
        {header: '<div align=center>单据类型</div>', dataIndex: 'no_type_name', width: 60},
        {header: '<div align=center>单据号</div>', dataIndex: 'no', width: 100,},
        {header: '<div align=center>单据日期</div>', dataIndex: 'createtime', width: 140,},
        {header: '<div align=center>备注</div>', dataIndex: 'remark', width: 300, renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
            var ss=record.data['remark'];
            if(ss){
                return '<a title="'+ss+'">'+ss+'</a>';
            } else {
                return '';
            }
        }},
        {header: '<div align=center>操作人</div>', dataIndex: 'operator_name', sortable: true, width: 100},
    ]}),
    //分页
    bbar: new Ext.PagingToolbar({
        plugins: new Ext.ux.plugin.PagingToolbarResizer,
        pageSize: pagesize,
        displayInfo: true,
        displayMsg: '当前记录数: {0} - {1} 总记录数: {2}',
        emptyMsg: '没有符合条件的记录',
        store: jifenRecordsStore
    }),
    tbar: []
});
//主容器
var detail_panel = new Ext.Panel({
    region: "center",
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    items: [
        new Ext.form.FieldSet({
            title: '',
            columnWidth: .1,
            height: 60,
            layout: 'column',
            border: true,
            anchor: '100%',
            items: [{// 第一行
                xtype: 'compositefield',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '机构名称：',
                    },
                    {
                        xtype: 'displayfield',
                        id: 'org_name',
                        width: 150,
                        value: ''
                    },
                    {
                        xtype: 'displayfield',
                        value: '油卡类型：',
                        style: 'padding-left: 280px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'oil_com',
                        width: 150,
                        value: ''
                    },
                    {
                        xtype: 'displayfield',
                        value: '返利地区：',
                        style: 'padding-left: 24px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'fanli_region',
                        width: 150,
                        value: ''
                    },
                ]
            },
                {// 第二行
                    xtype: 'compositefield',
                    items: [
                        {
                            xtype: 'displayfield',
                            value: '主卡号：',
                            style: 'padding-left: 12px'
                        },
                        {
                            xtype: 'displayfield',
                            id: 'main_no',
                            width: 150,
                            value: ''
                        },

                        {
                            xtype: 'displayfield',
                            value: '积分余额：',
                            style: 'padding-left: 24px'
                        },
                        {
                            xtype: 'displayfield',
                            id: 'jifen',
                            width: 150,
                            value: ''
                        },
                        {
                            xtype: 'displayfield',
                            value: '累计积分返利：',
                            style: 'padding-left: 12px'
                        },
                        {
                            xtype: 'displayfield',
                            id: 'fanli_total',
                            width: 150,
                            value: ''
                        },
                        {
                            xtype: 'displayfield',
                            value: '累计积分分配：',
                        },
                        {
                            xtype: 'displayfield',
                            id: 'assign_total',
                            width: 150,
                            value: ''
                        },
                    ]
                },]
        }),
        detail_top_panel,
        detail_grid
    ],
});
//弹出窗体
if (!detailwin) {//主窗体
    detailwin = new Ext.Window({
        layout: "border",
        width: 1000,
        height: 500,
        title: '积分账户明细',
        closeAction: 'hide',
        plain: true,
        modal: true,
        items: [detail_panel]
    });
}