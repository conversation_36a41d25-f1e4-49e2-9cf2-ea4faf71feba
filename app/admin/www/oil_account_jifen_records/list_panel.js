jifenStore.load();
var _sm =new Ext.grid.RowSelectionModel({
	singleSelect: true,
	listeners: {
		selectionchange: function(data) {
			if (data.getCount())
			{
                (Ext.getCmp('btnDetailShow')) ? Ext.getCmp('btnDetailShow').enable() : '';
			} else{
                (Ext.getCmp('btnDetailShow')) ? Ext.getCmp('btnDetailShow').disable() : '';
            }
	    }
    }
});

var grid_list = new Ext.grid.GridPanel({
	title	 :'积分账户查询',
	region	 : 'center',
	loadMask : true,
	cm : new Ext.grid.ColumnModel({ defaults : {
        width : 120,
        align : 'right',
        sortable : false
    },  columns:[
        new Ext.grid.RowNumberer({'header': '序号', width: 35, align : 'left'}),
		{header : '<div align=center>账号</div>',dataIndex : 'account_no',width : 100,align : 'left'},
        {header : '<div align=center>顶级机构</div>',dataIndex : 'orgroot',width : 220,align : 'left'},
		{header : '<div align=center>机构名称</div>',dataIndex : 'org_name',width : 220,align : 'left'},
		{header : '<div align=center>油卡类型</div>',dataIndex : 'oil_com',width : 100,align : 'left'},
		{header : '<div align=center>积分可用地区</div>',dataIndex : 'province',width : 100,align : 'left'},
		{header : '<div align=center>主卡</div>',dataIndex : 'main_no',width : 150,align : 'left'},
	    {header : '<div align=center>积分余额</div>',dataIndex : 'jifen',width : 100},
		{header : '<div align=center>累计返利积分</div>',dataIndex : 'fanli_total',width : 100},
        {header : '<div align=center>累计分配积分</div>',dataIndex : 'assign_total',width : 100},
	]}),
	store : jifenStore,
	sm :_sm,
	//固定表格的宽度 注释掉，显示宽度滚动条
	viewConfig:{
		forceFit:true
	},
	
	//分页
	bbar: new Ext.PagingToolbar({
		plugins: new Ext.ux.plugin.PagingToolbarResizer,
		pageSize: pagesize,
		displayInfo : true,
		displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',   
		emptyMsg : '没有符合条件的记录',   
		store : jifenStore
	}),
	tbar : []	
});

//修改油卡类型的列宽
Ext.Ajax.request({
    url: '../inside.php?t=json&m=oil_card_main&f=getOilComWidth',
    method: 'post',
    params: {},
    success: function(response, options){
        var oil_com_index = false;
        grid_list.getColumnModel().getColumnsBy(function(columnConfig, index){
            if (columnConfig.dataIndex == 'oil_com') {
                oil_com_index = index;
                return true;
            }
        });
        if (oil_com_index !== false) {
            grid_list.getColumnModel().setColumnWidth(oil_com_index, Ext.decode(response.responseText).width);
        }
    }
});

//列表表格
var main_tab_panel = new Ext.TabPanel({
	xtype   : "tabpanel",
	region  : "center",
	id		: 'sale_firm_tab',
	items:
	[
		grid_list
	]
});
main_tab_panel.setActiveTab(grid_list);//设置特定的tab为活动面板

//积分账户明细
function detailShow(){
    var sm   = grid_list.getSelectionModel();
    var data = sm.getSelections();
    data[0] ? add_update_id = data[0].get("id") : '';
    var num = jifenStore.getCount();
    for (var i = 0; i < num; i++) {
        var record = jifenStore.getAt(i).data;
        if (record.id == add_update_id) {
            //延迟50毫秒显示数据，不然时间显示不出来
            function fn() {
                Ext.getCmp('org_name').setValue(record.org_name);
                Ext.getCmp('oil_com').setValue(record.oil_com);
                Ext.getCmp('fanli_region').setValue(record.province);
                Ext.getCmp('main_no').setValue(record.main_no);
                Ext.getCmp('jifen').setValue(record.jifen);
                Ext.getCmp('fanli_total').setValue(record.fanli_total);
                Ext.getCmp('assign_total').setValue(record.assign_total);

                Ext.getCmp('trade_type').setValue('');
                Ext.getCmp('no_type').setValue('');
                Ext.getCmp('no').setValue('');
                jifenRecordsStore.removeAll();
                jifenRecordsStore.load();
            }

            var task = new Ext.util.DelayedTask(fn);
            task.delay(50);
            break;
        }
    }

    detailwin.show();
}

//导出
function exportData() {
    Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
            if (btn == 'yes') {
                var params = top_panel.getForm().getValues(true);
                window.open('/inside.php?t=json&m='+ controlName +'&f=search&_export=1'+'&'+params);
            }
        }
    );
}

