var crow;

main_store.load();
var _sm =new Ext.grid.CheckboxSelectionModel({
	listeners: {
		selectionchange: function(data) {
		    var statusArr = new Array();
			if (data.getCount()){

                (Ext.getCmp('btnUnAudit')) ? Ext.getCmp('btnUnAudit').enable() : '';//作废

                var sm   = grid_list.getSelectionModel();
				var info = sm.getSelections();
				for (var i =0 ;i < info.length;i++){
                    statusArr[i] = info[i].data.receipt_status;
                }
                crow = info[0].data;
				// var status = crow.receipt_status;
               // btnInit(statusArr);

                getReceiptTitle.removeAll();
                if (crow.is_internal == EXTERNAL) {
                    getReceiptTitle.baseParams = {receipt_type: crow.receipt_type,orgcode:crow.orgcode,status:1,_export:1};
                    getReceiptTitle.load();
                }
            }else{
                (Ext.getCmp('btnExportTradeDetails')) ? Ext.getCmp('btnExportTradeDetails').disable() : '';//导出消费明细
                (Ext.getCmp('btnShow')) ? Ext.getCmp('btnShow').disable() : '';//查看
                (Ext.getCmp('btnUnAudit')) ? Ext.getCmp('btnUnAudit').disable() : '';//作废
                (Ext.getCmp('sendEmail')) ? Ext.getCmp('sendEmail').disable() : '';//发邮件
                btnInit();
            }
	    }
    }
});

//按钮初始化
function btnInit(statusStrs){
    console.log(statusStrs);
    if(statusStrs && statusStrs.length == 1){
        (Ext.getCmp('btnExportTradeDetails')) ? Ext.getCmp('btnExportTradeDetails').enable() : '';//导出消费明细
        (Ext.getCmp('btnShow')) ? Ext.getCmp('btnShow').enable() : '';//查看
    }else{
        (Ext.getCmp('btnExportTradeDetails')) ? Ext.getCmp('btnExportTradeDetails').disable() : '';//导出消费明细
        (Ext.getCmp('btnShow')) ? Ext.getCmp('btnShow').disable() : '';//查看
    }
    // if(status !== undefined && status !== 10){
    if(statusStrs && statusStrs.length == 1 && statusStrs.indexOf(10) < 0){
        (Ext.getCmp('btnUpdate')) ? Ext.getCmp('btnUpdate').enable() : '';//编辑
    }else{
        (Ext.getCmp('btnExportTradeDetails')) ? Ext.getCmp('btnExportTradeDetails').disable() : '';//导出消费明细
        (Ext.getCmp('btnUpdate')) ? Ext.getCmp('btnUpdate').disable() : '';//编辑
    }
    if(statusStrs && statusStrs.length == 1){
        (Ext.getCmp('btnviewdetail')) ? Ext.getCmp('btnviewdetail').enable() : '';//明细
    }
    //txb 修改 有开票中的 就显示 确认开票
    if(statusStrs && statusStrs.indexOf(15) >= 0){
        // Ext.getCmp('btnConfirmInvoice').enable();
        (Ext.getCmp('btnConfirmInvoice')) ? Ext.getCmp('btnConfirmInvoice').enable() : '';
    }else{
        // Ext.getCmp('btnConfirmInvoice').disable();
        (Ext.getCmp('btnConfirmInvoice')) ? Ext.getCmp('btnConfirmInvoice').disable() : '';
    }

    if(statusStrs && statusStrs.indexOf(0) >= 0){
        (Ext.getCmp('btnAuditBy')) ? Ext.getCmp('btnAuditBy').enable() : '';//通过
    }else{
        (Ext.getCmp('btnAuditBy')) ? Ext.getCmp('btnAuditBy').disable() : '';//通过
    }

    if(statusStrs && statusStrs.indexOf(-1) >= 0){
        (Ext.getCmp('remove')) ? Ext.getCmp('remove').enable() : '';//删除
    }else{
        (Ext.getCmp('remove')) ? Ext.getCmp('remove').disable() : '';//删除
    }

    // (Ext.getCmp('btnConfirmInvoice')) ? Ext.getCmp('btnConfirmInvoice').disable() : '';//确认开票

    // if(status === -1 || status === 10 || status === undefined){
    if(statusStrs && statusStrs.length == 1 && statusStrs.indexOf(-1) <0 && statusStrs.indexOf(10) < 0 ){
        (Ext.getCmp('btnUnAudit')) ? Ext.getCmp('btnUnAudit').enable() : '';//作废
    }else{
        (Ext.getCmp('btnUnAudit')) ? Ext.getCmp('btnUnAudit').disable() : '';//作废
    }

    // if(status === 20 || status === 30){
    if(statusStrs && statusStrs.length == 1 && (statusStrs.indexOf(20) >= 0 || statusStrs.indexOf(30) >= 0)){
        (Ext.getCmp('btnExpress')) ? Ext.getCmp('btnExpress').enable() : '';//快递信息
    }else{
        (Ext.getCmp('btnExpress')) ? Ext.getCmp('btnExpress').disable() : '';//快递信息
    }

    /*if(status === 1){
        (Ext.getCmp('btnDelete')) ? Ext.getCmp('btnDelete').disable() : '';//删除
        (Ext.getCmp('btnAuditBy')) ? Ext.getCmp('btnAuditBy').disable() : '';//通过
    }else if(status === 0){
        (Ext.getCmp('btnDelete')) ? Ext.getCmp('btnDelete').enable() : '';//删除

    }else{
        (Ext.getCmp('btnDelete')) ? Ext.getCmp('btnDelete').disable() : '';//删除

    }*/

    // if(status === 1){
    if (statusStrs && statusStrs.indexOf(1) >= 0) {

        (Ext.getCmp('btnExportDetail')) ? Ext.getCmp('btnExportDetail').enable() : '';//导出开票信息

        // var info = getSelectedOilTypeInfo();
        // if (info.urea > 0 && info.not_urea === 0) {
        //     // 只有尿素，无非尿素油品
        //     (Ext.getCmp('btnExportDetail')) ? Ext.getCmp('btnExportDetail').enable() : '';//导出开票信息
        // } else if (info.urea > 0 && info.not_urea > 0) {
        //     // 有尿素，也有非尿素油品
        //     (Ext.getCmp('btnExportDetail')) ? Ext.getCmp('btnExportDetail').disable() : '';//导出开票信息
        // } else if (info.urea === 0 && info.not_urea === 1) {
        //     (Ext.getCmp('btnExportDetail')) ? Ext.getCmp('btnExportDetail').enable() : '';//导出开票信息
        // } else {
        //     (Ext.getCmp('btnExportDetail')) ? Ext.getCmp('btnExportDetail').disable() : '';//导出开票信息
        // }
    }else{
        (Ext.getCmp('btnExportDetail')) ? Ext.getCmp('btnExportDetail').disable() : '';//导出开票信息
    }

    /*if(status === 15){
        (Ext.getCmp('btnConfirmInvoice')) ? Ext.getCmp('btnConfirmInvoice').enable() : '';//确认开票
    }else{
        (Ext.getCmp('btnConfirmInvoice')) ? Ext.getCmp('btnConfirmInvoice').disable() : '';//确认开票
    }*/
    let sendEmailEnable = true;
    (Ext.getCmp('sendEmail')) ? Ext.getCmp('sendEmail').disable() : '';
    if (statusStrs) {
        $.each(statusStrs, function (k, v) {
            if (v !== 20) {
                sendEmailEnable = false
            }
        })
    } else {
        sendEmailEnable = false
    }
    if (sendEmailEnable) {
        (Ext.getCmp('sendEmail')) ? Ext.getCmp('sendEmail').enable() : '';
    }
}

function getSelectedOilTypeInfo()
{
    var oilType = {
        "urea": 0,
        "not_urea": 0
    };

    var sm   = grid_list.getSelectionModel();
    var info = sm.getSelections();
    for (var i =0 ; i < info.length; i++) {
        var record = info[i].data;
        if (record.receipt_status != 1) {
            continue;
        }

        if (record.oil_type != 6) {
            oilType.not_urea ++;
        } else {
            oilType.urea ++;
        }
    }

    return oilType;
}

var grid_list = new Ext.grid.GridPanel({
	title	 :'发票申请',
	region	 : 'center',
	loadMask : true,
	cm : new Ext.grid.ColumnModel([
		{header : '申请单号',dataIndex : 'no',width : 130},
		{header : '发票抬头(购买方)',dataIndex : 'corp_name',width : 150},
		{header : '开票类型',dataIndex : 'receipt_type',width : 70,align:"center"},
        {header : '油品种类',dataIndex : 'oil_classify',width : 70,align:"center"},
        {header : '申请金额',dataIndex : 'receipt_amount',width : 120,align:"right"},
		{header : '申请时间',dataIndex : 'apply_time',width : 140,align:"center"},
		{header : '联系人',dataIndex : 'addr_name',width : 100},
		{header : '手机号',dataIndex : 'addr_mobile',width : 100},
		{header : '收件地址',dataIndex : 'address',width : 140},
        {header : '销售方',dataIndex : 'seller_name',width : 240},
        {header : '纳税人识别号(销售方)',dataIndex : 'seller_taxpayer_no',width : 150}
	]),
	store : main_store,
	//分页
	bbar: new Ext.PagingToolbar({
		plugins: new Ext.ux.plugin.PagingToolbarResizer,
		pageSize: pagesize,
		displayInfo : true,
		displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
		emptyMsg : '没有符合条件的记录',
		store : main_store
	}),
	tbar : []
});

//列表表格
var main_tab_panel = new Ext.TabPanel({
	xtype   : "tabpanel",
	region  : "center",
	id		: 'sale_firm_tab',
	items:
	[
		grid_list
	]
});
main_tab_panel.setActiveTab(grid_list);//设置特定的tab为活动面板

///通过/作废
function execute(url) {
    var data = grid_list.getSelectionModel().getSelections();

    console.log('data',data);
    if(url === 'reject'){
        if(data.length > 1){
            Ext.MessageBox.alert('提示', '只能选择一条');
            return false;
        }
    }
    var ids = '';
    var pay_company_ids = '';
    if(data.length > 100){
        Ext.MessageBox.alert('提示', '最多选择100条数据');
        return false;
    }

    if(data.length > 0){
        for(var i = 0; i < data.length; i++){
            if(ids === ''){
                ids = data[i].get('id');
                pay_company_ids = data[i].get('pay_company_id')
            }else{
                ids += ',' + data[i].get('id');
                pay_company_ids += ',' + data[i].get('pay_company_id');
            }
        }
    }

    if(url === 'auditBy'){

        for(var i = 0; i < data.length; i++){
            if (data[i].data.receipt_status != 0) {
                Ext.MessageBox.alert('提示', '选择存在非待审核状态工单');
                return false;
            }
        }
        //
        var alertMsg = '是否审核通过吗？'
    }else if(url === 'confirmInvoice'){
        var alertMsg = '确认开发票？';
    }else if(url === 'reject'){
        var alertMsg = '是否作废当前数据？';
    }else{
        Ext.Msg.alert('提示','未知的方法'+url);
        return;
    }

    Ext.MessageBox.confirm('提示', alertMsg, function showResult(btn){
        if (btn === 'yes'){
            if(url === 'auditBy'){
                // //校验是否签约合同
                Ext.Ajax.request({
                    url: '../inside.php?t=json&m=oil_receipt_apply&f=checkContractForAudit',
                    method: 'post',
                    params: {pay_company_ids: pay_company_ids},
                    success: function (resp, opts) {
                        console.log(Ext.decode(resp.responseText));
                        if (Ext.decode(resp.responseText).code != 0) {
                            // Ext.MessageBox.alert("系统提示", Ext.decode(resp.responseText).msg);
                            Ext.Msg.confirm('系统提示', Ext.decode(resp.responseText).msg+'，是否要强制开票？', function (btn) {
                                if (btn == 'yes') {
                                    console.log('确认');
                                    Ext.Ajax.request({
                                        url:getUrl(controlName,url),
                                        method:'post',
                                        params:{ id : ids},
                                        success: function sFn(response,options){
                                            main_store.removeAll();
                                            main_store.load();
                                            Ext.Msg.alert('系统提示', Ext.decode(response.responseText).msg);
                                        },
                                        failure: function (response, options) {
                                            alert('222');
                                        }
                                    });
                                } else {
                                    console.log('cancel')
                                    //todo
                                }
                            });
                        }else{
                            Ext.Ajax.request({
                                url:getUrl(controlName,url),
                                method:'post',
                                params:{ id : ids},
                                success: function sFn(response,options){
                                    main_store.removeAll();
                                    main_store.load();
                                    Ext.Msg.alert('系统提示', Ext.decode(response.responseText).msg);
                                },
                                failure: function (response, options) {
                                    alert('222');
                                }
                            });
                        }
                    }
                });
            }else{
                Ext.Ajax.request({
                    url:getUrl(controlName,url),
                    method:'post',
                    params:{ id : ids},
                    success: function sFn(response,options){
                        main_store.removeAll();
                        main_store.load();
                        Ext.Msg.alert('系统提示', Ext.decode(response.responseText).msg);
                    },
                    failure: function (response, options) {
                        alert('222');
                    }
                });
            }
        }
    })

}

//导出明细
function exportDetails() {
    var data = grid_list.getSelectionModel().getSelections();

    var ids = '';
    if(data.length > 100){
        Ext.MessageBox.alert('提示', '最多选择100条数据');
        return false;
    }

    var oil_type;
    var is_internal;

    if(data.length > 0){
        var resArr = [];
        var json = {};
        for(var i = 0; i < data.length; i++){
            if(ids === ''){
                ids = data[i].get('id');
            }else{
                ids += ',' + data[i].get('id');
            }

            oil_type = data[i].get('oil_type');

            //新增内外部票混合多选的判断处理
            if(!json[data[i].get('is_internal')]){
                resArr.push(data[i].get('is_internal'));
                json[data[i].get('is_internal')] = 1;
            }
        }

        //校验内外部票混合2外1内
        if(resArr.length > 1){
            Ext.MessageBox.alert('提示', '内外部票不能混合选择');
            return false;
        }
        if(resArr.length == 1 && resArr[0] == 1 && data.length > 1)
        {
            Ext.MessageBox.alert('提示', '内部票不能选择多条');
            return false;
        }
        is_internal = resArr[0];
    }

    if (ids !== '') {
        if (is_internal == 1) {
            Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                    if (btn === 'yes') {
                        getApplyConsumeOilTypeStatisticNew();
                    }
                }
            );
        } else {
            Ext.Ajax.request({
                url:getUrl(controlName,'tonUnitInvoiceWhiteCheck'),
                method:'post',
                params:{ ids : ids},
                success: function sFn(response,options){
                    var result = Ext.decode(response.responseText);
                    if (result.code === 0) {
                        if (result.data === true && oil_type !== 6) {
                            Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                                    if (btn === 'yes') {
                                        getApplyConsumeOilTypeStatistic();
                                    }
                                }
                            );
                        } else {
                            getApplyConsumeOilTypeStatisticNewNew()
                        }
                    }else{
                        Ext.MessageBox.alert("系统提示", result.msg);
                    }
                },
                failure: function (response, options) {
                    Ext.MessageBox.alert("系统提示", '验证吨票白名单失败，请稍后重试');
                }
            });
        }
    } else {
        Ext.MessageBox.alert('提示', '请选中导出内容');
    }
}

var consume_statistic_form = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 50,
    items: [
        {//搜索框
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '选择发票申请开具单位：'
                },
                {
                    xtype: 'combo',
                    width: 100,
                    id: 'i_unit',
                    hiddenName: 'unit',
                    mode: 'local',
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'name',
                    allowBlank: false,
                    valueField: 'id',
                    store: new Ext.data.JsonStore({data:[
                            {id: 1, name:"按升开具"},
                            {id: 2, name:"按吨开具"},
                        ],
                        fields: ['id', 'name']
                    })
                },
                {
                    xtype: 'displayfield',
                    value: '开票渠道：',
                    width: 60,
                },
                {
                    xtype: 'combo',
                    width: 80,
                    id: "d_open_channel",
                    hiddenName: 'd_open_channel_name',
                    mode: 'remote',
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'name',
                    valueField: 'value',
                    store: getReceiptOpenChannelList,
                },
            ]
        },
    ]
})
var consume_statistic_panel_data = new Ext.Panel({
    region: "center",
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    buttonAlign: 'center',
    height: 250,
    trackResetOnLoad: true,
    labelWidth: 70,
    border: false,
    items: [
        new Ext.grid.GridPanel({
            loadMask: true,
            enableColumnMove: false,
            enableColumnResize: true,
            height: 200,
            region: 'south',
            title: '发票申请中捞取消费归类统计：',
            stripeRows: true,//斑马颜色
            autoScroll: true,
            store: store_consume_statistic,
            cm: new Ext.grid.ColumnModel({
                defaults: {
                    width: 120,
                    align: 'left',
                    sortable: false
                },
                columns: [
                    {
                        header: '二级油品类型',
                        dataIndex: 'second_oil_type_value',
                        width: 160,
                        align: 'right'
                    },
                    {header: '升数/kg', dataIndex: 'l_kg', width: 160, align: 'right'},
                    {header: '吨数', dataIndex: 'ton', width: 160, align: 'right'}
                ]
            }),
            tbar: [],
        }),
        consume_statistic_form
    ],
    buttons: [
        {
            text: "保存",
            id: 'btn_save_itl',
            listeners: {
                click: function () {

                    var unit = Ext.getCmp('i_unit').getValue();
                    if (unit === '') {
                        Ext.MessageBox.alert("系统提示", '请选择发票申请开具单位');
                        return;
                    }

                    let open_channel = Ext.getCmp('d_open_channel').getValue();
                    if (!open_channel) {
                        Ext.MessageBox.alert("系统提示", '请选择开票渠道');
                        return false;
                    }

                    Ext.Ajax.request({
                        url:getUrl(controlName,'exportData'),
                        method:'post',
                        params:{ ids : export_invoice_id, unit: unit, open_channel: open_channel},
                        success: function sFn(response,options){
                            var result = Ext.decode(response.responseText);
                            if(result.code === 0){
                                main_store.removeAll();
                                main_store.load();
                                consumeStatisticWin.hide();

                                var url = Ext.decode(response.responseText).data.redirect_url;
                                window.open(url);

                                Ext.MessageBox.alert("系统提示", result.msg);
                            }else{
                                Ext.MessageBox.alert("系统提示",  result.msg);
                            }
                        },
                        failure: function (response, options) {
                            alert('222');
                        }
                    });
                }
            }
        },
        {
        text: "取消",
        'id': 'btn_cancel_itl',
            handler: function () {
                consumeStatisticWin.hide();
            }
        }
    ]
});

var consume_statistic_form_new = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 70,
    items: [
        {//搜索框
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '选择发票面版：'
                },
                {
                    xtype: 'combo',
                    width: 100,
                    id: 'i_amount_type',
                    hiddenName: 'amount_type',
                    mode: 'local',
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'name',
                    allowBlank: false,
                    valueField: 'id',
                    store: new Ext.data.JsonStore({data:[
                            {id: 1, name:"十万版"},
                            {id: 2, name:"百万版"},
                        ],
                        fields: ['id', 'name']
                    }),
                    listeners: {
                        select: function(combo, value, i){
                            changeAmontType(value.get('id'))
                        }
                    }
                },
                {
                    xtype: 'displayfield',
                    value: '选择发票申请开具单位：'
                },
                {
                    xtype: 'combo',
                    width: 100,
                    id: 'i_unit_temp',
                    hiddenName: 'unit',
                    mode: 'local',
                    triggerAction: 'all',
                    emptyText: '请选择..',
                    forceSelection: true,
                    displayField: 'name',
                    allowBlank: false,
                    valueField: 'id',
                    store: new Ext.data.JsonStore({data:[
                            {id: 1, name:"混合单位"},
                        ],
                        fields: ['id', 'name']
                    })
                },
            ]
        },
        {//搜索框
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '开票渠道：',
                    width: 60,
                },
                {
                    xtype: 'combo',
                    width: 80,
                    id: "d_open_channel_new",
                    hiddenName: 'd_open_channel_new_name',
                    mode: 'remote',
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'name',
                    valueField: 'value',
                    store: getReceiptOpenChannelList,
                },
            ]
        }
    ]
})
var consume_statistic_panel_data_new = new Ext.Panel({
    region: "center",
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    buttonAlign: 'center',
    height: 280,
    trackResetOnLoad: true,
    labelWidth: 70,
    border: false,
    items: [
        new Ext.grid.GridPanel({
            loadMask: true,
            enableColumnMove: false,
            enableColumnResize: true,
            height: 200,
            region: 'south',
            title: '发票申请中捞取消费归类统计：',
            stripeRows: true,//斑马颜色
            autoScroll: true,
            store: store_consume_statistic_new,
            cm: new Ext.grid.ColumnModel({
                defaults: {
                    width: 120,
                    align: 'left',
                    sortable: false
                },
                columns: [
                    {
                        header: '油品类型',
                        dataIndex: 'oil_sec_type_name',
                        width: 90,
                        align: 'right'
                    },
                    {header: '统计方式', dataIndex: 'unit', width: 90, align: 'right',renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                            if(record.data.unit == '空'){
                                return '<span style="color:red;">'+value+'</span>';
                            }else{
                                return value;
                            }
                        }},
                    {header: '数量', dataIndex: 'trade_num', width: 90, align: 'right'},
                    {header: '开票金额', dataIndex: 'receipt_money', width: 90, align: 'right'},
                    {header: '拆分张数', dataIndex: 'split_num', width: 90, align: 'right'}
                ]
            }),
            tbar:[
                {
                    iconCls: 'silk-export',
                    text: '导出空缺单位供应商',
                    id: 'export_supplier_unit',
                    disabled: false,
                    handler: function () {
                        Ext.MessageBox.confirm('提示', '确认导出?', function showResult(btn) {
                            if (btn == 'yes') {
                                Ext.Ajax.request({
                                    url: '../inside.php?t=json&m=oil_receipt_apply&f=exportEmptySupplierUnit&ids='+export_invoice_id,
                                    method: 'get',
                                    // params: params,
                                    success: function sFn(response, options) {
                                        // if(options.result.code == 0){
                                        //     window.location.href = options.result.data;
                                        // }else{
                                        //     Ext.MessageBox.alert("系统提示", '操作失败');
                                        // }

                                        //Ext.getCmp('export_supplier_unit').enable();

                                        var url = Ext.decode(response.responseText).data;
                                        console.log(url);
                                        if(url){
                                            window.open(url);
                                        }

                                        var msg = Ext.decode(response.responseText).msg;
                                        Ext.Msg.alert('提示', msg);
                                    },
                                    failure: function (response, options) {
                                        Ext.MessageBox.alert('提示', '失败，请刷新页面重试');
                                    }
                                });

                                //window.location.href = getUrl(controlName,'exportEmptySupplierUnit') + '&ids=' + export_invoice_id;
                            }
                        });
                    }
                }
            ],
        }),
        consume_statistic_form_new
    ],
    buttons: [
        {
            text: "保存",
            id: 'btn_save_itl',
            listeners: {
                click: function () {

                    var unit = Ext.getCmp('i_amount_type').getValue();
                    if (unit === '') {
                        Ext.MessageBox.alert("系统提示", '请选择发票申请开具单位');
                        return;
                    }

                    let open_channel = Ext.getCmp('d_open_channel_new').getValue();
                    if (!open_channel) {
                        Ext.MessageBox.alert("系统提示", '请选择开票渠道');
                        return false;
                    }

                    Ext.Ajax.request({
                        url:getUrl(controlName,'exportData'),
                        method:'post',
                        params:{ ids : export_invoice_id, amount_type: unit,is_internal: 1, open_channel: open_channel},
                        success: function sFn(response,options){
                            var result = Ext.decode(response.responseText);
                            if(result.code === 0){
                                main_store.removeAll();
                                main_store.load();
                                consumeStatisticWinNew.hide();

                                var url = Ext.decode(response.responseText).data.redirect_url;
                                window.open(url);

                                Ext.MessageBox.alert("系统提示", result.msg);
                            }else{
                                Ext.MessageBox.alert("系统提示", result.msg);
                            }
                        },
                        failure: function (response, options) {
                            Ext.MessageBox.alert("系统提示", '请求失败');
                        }
                    });
                }
            }
        },
        {
            text: "取消",
            'id': 'btn_cancel_itl',
            handler: function () {
                consumeStatisticWinNew.hide();
            }
        }
    ]
});

var consume_statistic_form_new_new = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    items: [
        {//搜索框
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '开票渠道：',
                    width: 60,
                },
                {
                    xtype: 'combo',
                    width: 80,
                    id: "d_open_channel_new_new",
                    hiddenName: 'd_open_channel_new_new_name',
                    mode: 'remote',
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'name',
                    valueField: 'value',
                    store: getReceiptOpenChannelList,
                },
            ]
        }
    ]
})
var consume_statistic_panel_data_new_new = new Ext.Panel({
    region: "center",
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    buttonAlign: 'center',
    trackResetOnLoad: true,
    labelWidth: 70,
    border: false,
    items: [
        consume_statistic_form_new_new
    ],
    buttons: [
        {
            text: "取消",
            'id': 'btn_cancel_itl',
            handler: function () {
                consumeStatisticWinNewNew.hide();
            }
        },
        {
            text: "确定导出",
            id: 'btn_save_itl',
            listeners: {
                click: function () {

                    let open_channel = Ext.getCmp('d_open_channel_new_new').getValue();
                    if (!open_channel) {
                        Ext.MessageBox.alert("系统提示", '请选择开票渠道');
                        return false;
                    }
                    let data = grid_list.getSelectionModel().getSelections(), ids = '';
                    for(let i = 0; i < data.length; i++){
                        if(ids === ''){
                            ids = data[i].get('id');
                        }else{
                            ids += ',' + data[i].get('id');
                        }
                    }
                    Ext.Ajax.request({
                        url:getUrl(controlName,'exportData'),
                        method:'post',
                        params:{ ids : ids, open_channel: open_channel},
                        success: function sFn(response,options){
                            var result = Ext.decode(response.responseText);
                            if(result.code === 0){
                                main_store.removeAll();
                                main_store.load();
                                consumeStatisticWinNewNew.hide();

                                var url = Ext.decode(response.responseText).data.redirect_url;
                                window.open(url);

                                Ext.MessageBox.alert("系统提示", result.msg);
                            }else{
                                Ext.MessageBox.alert("系统提示", result.msg);
                            }
                        },
                        failure: function (response, options) {
                            Ext.MessageBox.alert("系统提示", '请求失败');
                        }
                    });
                }
            }
        },
    ]
});

//弹出窗体
var consumeStatisticWin;
if (!consumeStatisticWin) {//主窗体
    consumeStatisticWin = new Ext.Window({
        layout: "border",
        width: 530,
        height: 350,
        title: '导出开票信息',
        closeAction: 'hide',
        modal: true,
        plain: true,
        items: [consume_statistic_panel_data],
    });
}

var consumeStatisticWinNew;
if (!consumeStatisticWinNew) {//主窗体
    consumeStatisticWinNew = new Ext.Window({
        layout: "border",
        width: 530,
        height: 370,
        title: '导出开票信息',
        closeAction: 'hide',
        modal: true,
        plain: true,
        items: [consume_statistic_panel_data_new],
    });
}

//弹出窗体
var consumeStatisticWinNewNew;
if (!consumeStatisticWinNewNew) {//主窗体
    consumeStatisticWinNewNew = new Ext.Window({
        layout: "border",
        width: 230,
        height: 150,
        title: '导出开票信息',
        closeAction: 'hide',
        modal: true,
        plain: true,
        items: [consume_statistic_panel_data_new_new],
    });
}

var export_invoice_id = 0;

function checkouMultSelect()
{
    var res = [];
    var json = {};
    for(var i = 0; i < this.length; i++){
        if(!json[this[i]]){
            res.push(this[i]);
            json[this[i]] = 1;
        }
    }
    return res;
}

function getApplyConsumeOilTypeStatistic()
{
    var param = {
        "id" : 0
    };
    var sm   = grid_list.getSelectionModel();
    var info = sm.getSelections();
    for (var i =0 ; i < info.length; i++) {
        var record = info[i].data;
        if (record.receipt_status !== 1) {
            continue;
        }

        param.id = record.id;
    }

    if (param.id === 0) {
        alert('fail');
        return;
    }

    export_invoice_id = param.id;

    store_consume_statistic.baseParams.id = param.id;
    store_consume_statistic.load();
    consumeStatisticWin.show();
    consume_statistic_form.getForm().setValues([
        {
            'id': 'd_open_channel', value: 30,
        }
    ])
}

function getApplyConsumeOilTypeStatisticNew()
{
    var param = {
        "id" : 0
    };
    var sm   = grid_list.getSelectionModel();
    var info = sm.getSelections();
    for (var i =0 ; i < info.length; i++) {
        var record = info[i].data;
        if (record.receipt_status !== 1) {
            continue;
        }

        param.id = record.id;
    }

    if (param.id === 0) {
        alert('fail');
        return;
    }

    export_invoice_id = param.id;

    store_consume_statistic_new.baseParams.id = param.id;
    store_consume_statistic_new.load();

    Ext.getCmp('i_unit_temp').disable();
    consumeStatisticWinNew.show();
    consume_statistic_form_new.getForm().setValues([
        {
            id: 'd_open_channel_new', value: 30,
        },
        {
            id: 'i_unit_temp', value: '1',
        }
    ])
}

function getApplyConsumeOilTypeStatisticNewNew()
{
    consumeStatisticWinNewNew.show()
    consume_statistic_form_new_new.getForm().setValues([
        {
            'id': 'd_open_channel_new_new', value: 30,
        }
    ])
}

function changeAmontType(type)
{
    var param = {
        "id" : 0
    };
    var sm   = grid_list.getSelectionModel();
    var info = sm.getSelections();
    for (var i =0 ; i < info.length; i++) {
        var record = info[i].data;
        if (record.receipt_status !== 1) {
            continue;
        }

        param.id = record.id;
    }

    if (param.id === 0) {
        alert('fail');
        return;
    }

    export_invoice_id = param.id;

    store_consume_statistic_new.baseParams.id = param.id;
    store_consume_statistic_new.baseParams.amount_type = type;
    store_consume_statistic_new.load();
}

//导出快递
function exportExpress() {
    var data = grid_list.getSelectionModel().getSelections();

    var ids = '';
    if(data.length > 100){
        Ext.MessageBox.alert('提示', '最多选择100条数据');
        return false;
    }

    if(data.length > 0){
        for(var i = 0; i < data.length; i++){
            if(ids === ''){
                ids = data[i].get('id');
            }else{
                ids += ',' + data[i].get('id');
            }
        }
    }

    if(ids !== ''){
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn === 'yes') {
                    window.location.href = getUrl(controlName,'exportExpress') + '&ids=' + ids;
                }
            }
        );
    }else{
        Ext.MessageBox.alert('提示', '请选中导出内容');
    }
}

//导出消费明细
function exportTradeDetail() {
    var data = grid_list.getSelectionModel().getSelections();

    var ids = '';
    if(data.length > 0){
        ids = data[0].get('id');
    }

    if(ids !== ''){
        Ext.MessageBox.confirm('导出', "确定要导出消费明细吗？", function showResult(btn) {
                if (btn === 'yes') {
                    Ext.Ajax.request({
                        url:getUrl(controlName,'exportTradeDetail'),
                        method:'post',
                        params:{ ids : ids,_export:1},
                        success: function sFn(response,options){
                            var result = Ext.decode(response.responseText);

                            var url = Ext.decode(response.responseText).data.redirect_url;
                            console.log("----跳转申请地址---",url);
                            window.open(url);

                            if(result.code === 0){
                                main_store.removeAll();
                                main_store.load();
                                Ext.MessageBox.alert("系统提示", result.msg);
                            }else{
                                Ext.MessageBox.alert("系统提示", '操作失败');
                            }
                        },
                        failure: function (response, options) {
                            alert('失败');
                        }
                    });
                }
            }
        );
    }else{
        Ext.MessageBox.alert('提示', '请选中导出内容');
    }
}

function exportData() {
    var ids  = main_store.data.keys;
    console.log('ids--',ids);
    if(ids.length > 0){
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn === 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = getUrl(controlName,'receiptSearch') + '&_export=1' + '&' + params;
                }
            }
        );
    }
}

/**
 * 导入快递
 */
function importExpress() {
    var form = new Ext.form.FormPanel({
        baseCls: 'x-plain', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
        items: [
            {
                xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
                id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
                anchor: '100%'
            }
        ]
    });
    var fanliBoot = new Ext.Window({
        title: '批量导入',
        width: 400,
        height: 105,
        minWidth: 300,
        minHeight: 100,
        closeAction: 'destroy',
        modal: true,
        layout: 'fit',
        plain: true,
        bodyStyle: 'padding:5px;',
        buttonAlign: 'center',
        items: form,
        buttons: [{
            text: '导入',
            handler: function () {
                if (form.form.isValid()) {
                    if (Ext.getCmp('userfile').getValue() === '') {
                        Ext.Msg.alert('系统提示', '请选择你要上传的文件');
                        return;
                    }
                    form.getForm().submit({
                        url: getUrl(controlName,'importExpress'),
                        success: function (form, action) {
                            var r = Ext.decode(action.response.responseText).msg;
                            Ext.MessageBox.alert('提示', r);
                            fanliBoot.destroy();
                            main_store.removeAll();
                            main_store.load();
                        },
                        failure: function (form, action) {
                            var r = Ext.decode(action.response.responseText).msg;
                            Ext.MessageBox.alert('提示', r);
                        }
                    })
                }
            }
        }, {
            text: '关闭',
            handler: function () {
                fanliBoot.destroy();
            }
        }]
    });
    fanliBoot.show();
}


//---创建表单及表单POST提交---开始
var extend = function (a, b) {
    var n;
    if (!a) {
        a = {};
    }
    for (n in b) {
        a[n] = b[n];
    }
    return a;
};

var css = function (el, styles) {
    extend(el.style, styles);
};

var createElement = function (tag, attribs, styles, parent) {
    var el = document.createElement(tag);
    if (attribs) {
        extend(el, attribs);
    }
    if (styles) {
        css(el, styles);
    }
    if (parent) {
        parent.appendChild(el);
    }
    return el;
}

var post = function (url, data) {
    var name,
        form;

    // create the form
    form = createElement('form', {
        method: 'post',
        action: url,
        enctype: 'multipart/form-data',
        target: '_blank'
    }, {
        display: 'none'
    }, document.body);

    // add the data
    for (name in data) {
        createElement('input', {
            type: 'hidden',
            name: name,
            value: data[name]
        }, null, form);
    }

    // submit
    form.submit();
}
//---创建表单及表单POST提交---结束
