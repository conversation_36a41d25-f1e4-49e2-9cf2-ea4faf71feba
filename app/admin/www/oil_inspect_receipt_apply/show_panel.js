var show_win;//添加窗口

var show_form_panel = new Ext.form.FormPanel({
    region: 'center',
    labelAlign: 'right',
    labelWidth: 120,
    trackResetOnLoad: true,
    frame: true,
    bodyStyle: 'background-color: white;padding-top:10px;',
    height: 240,
    items: [
        {//第1行
            layout: "column",
            items: [
                {
                    columnWidth: 0.25,
                    layout: "form",
                    items: {
                        xtype: 'displayfield',
                        fieldLabel: '单号',
                        name:'no'
                    }
                }, {
                    columnWidth: 0.25,
                    layout: "form",
                    items: {
                        xtype: 'displayfield',
                        fieldLabel: '申请机构',
                        name:'org_name'
                    }
                }, {
                    columnWidth: 0.25,
                    layout: "form",
                    items: {
                        xtype: 'displayfield',
                        fieldLabel: '开票类型',
                        name:'receipt_type'
                    }
                },{
                    columnWidth: 0.25,
                    layout: "form",
                    items: {
                        xtype: 'displayfield',
                        fieldLabel: '申请日期',
                        name:'apply_time'
                    }
                }]
        },
        {//第2行
            layout: "column",
            items: [
                {
                    columnWidth: 0.25,
                    layout: "form",
                    items: {
                        xtype: 'displayfield',
                        fieldLabel: '发票抬头',
                        name:'corp_name'
                    }
                }, {
                    columnWidth: 0.25,
                    layout: "form",
                    items: {
                        xtype: 'displayfield',
                        fieldLabel: '开票金额',
                        name:'receipt_amount'
                    }
                }]
        },
        {//第3行
            layout: "column",
            items: [
                {
                    columnWidth: 0.25,
                    layout: "form",
                    items: {
                        xtype: 'displayfield',
                        fieldLabel: '负责人',
                        id:'contact_name',
                        name:'contact_name'
                    }
                }, {
                    columnWidth: 0.3,
                    layout: "form",
                    items: {
                        xtype: 'displayfield',
                        fieldLabel: '接收人',
                        id:'name_mobile',
                        name:'name_mobile'
                    }
                }, {
                    layout: "form",
                    items: {
                        xtype: 'displayfield',
                        fieldLabel: '收件地址/收票邮箱',
                        name:'address'
                    }
                }]
        },
        {//第4行
            layout: "column",
            items: [
                {
                    columnWidth: 1,
                    layout: "form",
                    items: {
                        xtype: "displayfield",
                        fieldLabel: "后台备注",
                        name: 'admin_remark'
                    }
                }]
        },
        {//第5行
            layout: "column",
            items: [
                {
                    columnWidth: 1,
                    layout: "form",
                    items: {
                        xtype: "displayfield",
                        fieldLabel: "客户备注",
                        name: 'custom_remark'
                    }
                }]
        }
    ]
});

//主窗体
if (!show_win) {
    show_win = new Ext.Window({
        width: 1100,
        height: 240,
        title: '发票申请查看',
        closeAction: 'hide',
        plain: true,
        modal: true,
        items: [show_form_panel]
    });
}

function show() {
    show_win.show();
    Ext.Ajax.request({
        url: '/inside.php?t=json&m=oil_receipt_apply&f=receiptSearch',
        async: false,
        params: {id: crow.id},
        success: function (resp, opts) {
            var data = Ext.decode(resp.responseText).data;
            if (data.data.length > 0) {
                crow = data.data[0];

                if(crow.address){
                    crow.address = crow.address+'（'+crow.email+'）';
                }
                show_form_panel.getForm().loadRecord({data: crow});
                if (crow.is_internal == INTERNAL) {
                    show_form_panel.getForm().setValues([
                        {id: 'name_mobile', value: crow.addr_name + ' ' + crow.addr_mobile}
                    ]);
                } else {
                    getOrgContact.load({
                        params:{orgcode:crow.orgcode,_export:1},
                        callback:function (data) {
                            console.log('len--',data.length)
                            for(var i=0;i<data.length;i++){
                                console.log('key--',data[i])
                                if(data[i].get('id') === crow.org_contact_id){
                                    show_form_panel.getForm().setValues([
                                        {id: 'contact_name', value: data[i].get('contact_name')}
                                    ]);
                                }
                            }
                        }
                    });
                    getOrgAddr.load({
                        params:{orgcode:crow.orgcode,_export:1},
                        callback:function (data) {
                            for(var i=0;i<data.length;i++){
                                if(data[i].get('id') === crow.org_addr_id){
                                    show_form_panel.getForm().setValues([
                                        {id: 'name_mobile', value: data[i].get('name_mobile')}
                                    ]);
                                }
                            }
                        }
                    });
                }

            }
        }
    });
}

var send_email_table = new Ext.grid.EditorGridPanel({
    store: sendEmailSore,
    region: 'south',
    style: 'margin-top: 15px',
    height: 230,
    autoScroll: true,
    enableColumnResize: true,
    plugins: [],
    tbar: [],
    columns: [
        new Ext.grid.RowNumberer({header: '序号', width: 35}),
        {
            id: 'id',
            header: '唯一',
            dataIndex: 'id',
            width: 80,
            hidden: true,
        },
        {
            id: 'no',
            header : '申请单号',
            dataIndex : 'no',
            width : 130
        },
        {
            id: 'org_code',
            header: '机构编码',
            dataIndex: 'org_code',
            width: 100,
        },
        {
            id: 'org_name',
            header: '申请机构',
            dataIndex: 'org_name',
            width: 150,
        },
        {
            id: 'corp_name',
            header: '发票抬头',
            dataIndex: 'corp_name',
            width: 140,
        },
        {
            id: 'receipt_num',
            header: '蓝票数量',
            dataIndex: 'receipt_num',
            width: 80,
        },
        {
            id: 'email',
            header: '<span style="color: red">*</span>收件箱',
            dataIndex: 'email',
            width: 123,
            editor: new Ext.form.TextField({
                id: 's_email',
                allowBlank: false,
                blankText: "邮箱不能为空",
                vtype:"email",
                vtypeText: '邮箱格式不正确',
                enableKeyEvents: true,
                msgTarget: 'qtip'
            })
        }
    ]
})

var send_email_form = new Ext.form.FormPanel({
    region: 'center',
    hideLabels: true,
    trackResetOnLoad: true,
    bodyStyle: 'padding: 10px',
    items: [
        send_email_table
    ],
    buttons: [{
        text: "保存",
        id: 'btn_save',
        listeners: {
            click: function () {
                let submitData = []
                for (var i = 0; i < sendEmailSore.getCount(); i++) {
                    if (sendEmailSore.getAt(i).data['email'] === "") {
                        Ext.MessageBox.alert("系统提示", "请检查表单是否填写完整及正确")
                        return false
                    }
                    submitData.push({
                        "receipt_apply_id": sendEmailSore.getAt(i).data.id,
                        "email": sendEmailSore.getAt(i).data.email,
                    })
                }
                send_email_form.getForm().submit({
                    url: '../inside.php?t=json&m=oil_receipt_apply&f=receiptSendEmail',
                    params: {
                        content: JSON.stringify(submitData)
                    },
                    waitMsg: '处理中...',
                    success: function (resp, opts) {
                        var data = opts.result;
                        if (data.hasOwnProperty("code") && data.code != 0) {
                            Ext.MessageBox.alert("系统提示", data.msg || '网络异常，请稍后再试')
                            return false
                        }

                        Ext.MessageBox.alert("系统提示", "邮件发送成功")
                        main_store.reload()
                        sendEmailSore.removeAll()
                        sendEmailWin.hide()
                    },
                    failure: function (resp, opts) {
                        Ext.MessageBox.alert("系统提示", Ext.decode(opts.response.responseText).msg)
                    }
                })
            }
        }
    }, {
        text: "取消",
        handler: function () {
            sendEmailSore.removeAll()
            sendEmailWin.hide();
        }
    }]
});

var sendEmailWin = new Ext.Window({
    layout: "border",
    width: 810,
    height: 350,
    title: '发邮件',
    closeAction: 'hide',
    plain: true,
    modal: true,
    listeners: {
        'hide': function () {
            sendEmailSore.removeAll()
            sendEmailWin.hide()
        }
    },
    items: [send_email_form]
});

function sendEmail() {
    const sm   = grid_list.getSelectionModel(), info = sm.getSelections()
    let receiptApplyIds = []
    for (let i =0 ;i < info.length;i++){
        receiptApplyIds.push(info[i].data.id)
    }
    sendEmailSore.load({
        params: {
            ids: receiptApplyIds.join(",")
        }
    })
    sendEmailWin.show()
}

var send_email_detail_table = new Ext.grid.GridPanel({
    store: sendEmailDetailSore,
    region: 'center',
    height: 230,
    autoScroll: true,
    enableColumnResize: true,
    plugins: [],
    tbar: [],
    columns: [
        {
            id: 'createtime',
            header : '操作时间',
            dataIndex : 'createtime',
            width : 140
        },
        {
            id: 'creator_user',
            header: '操作账号',
            dataIndex: 'creator_user',
            width: 160,
        },
        {
            id: 'creator_name',
            header: '操作人',
            dataIndex: 'creator_name',
            width: 160,
        },
        {
            id: 'send_email',
            header: '发送邮箱',
            dataIndex: 'send_email',
            width: 123,
        }
    ]
})
var showSendEmailWin = new Ext.Window({
    layout: "border",
    width: 610,
    height: 350,
    title: '操作记录',
    closeAction: 'hide',
    plain: true,
    modal: true,
    listeners: {
        'hide': function () {
            sendEmailDetailSore.removeAll()
            showSendEmailWin.hide()
        }
    },
    items: [send_email_detail_table]
});

function showSendEmailDetail(sendCycle, receiptApplyId) {
    if (sendCycle <= 0) {
        return false
    }
    sendEmailDetailSore.load({
        params: {
            id: receiptApplyId
        }
    })
    showSendEmailWin.show()
}
