/**
 * 定义添加组件容器类
 */
let add_win;
const addPanel = new Ext.form.FormPanel({
	region: 'center',
	hideLabels: true,
	bodyStyle: 'padding: 10px',
	height: 180,
	width: 350,
	items:
		[
			{// 第二行
				xtype: 'compositefield',
				style: 'padding-top:5px;',
				items:
					[
						{
							xtype: 'displayfield',
							value: '内 容：'
						},
						{
							xtype: "textarea",
							id: "remark",
							width: 250,
							height: 80,
							maxLength: 100,
							maxLengthText: '最多可以输入100个文字',
							allowBlank: true,
						},
					]
			}
		],
	buttons: [{
		text: "确定",
		handler: function () {
			const that = this;
			let _form = addPanel.getForm();
			let url;
			if (_form.isValid()) {
				url = getUrl(controlName, 'edit');
				_form.submit({
					url: url,
					waitMsg: '操作中...',
					params: {id: grid_list.getSelectionModel().getSelections()[0].get("id")},
					success: function (form, action) {
						if (action.result && action.result.code === 0) {
							add_win.hide();
						}
						Ext.MessageBox.alert("系统提示", action.result.msg);
						main_store.removeAll();
						main_store.load();
						oilTest.closeWin(that.winId)
					},
					failure: function (form, action) {
						Ext.MessageBox.alert("系统提示", action.result.msg);
					}
				});
			} else {
				Ext.MessageBox.alert("系统提示", '输入有误，请检查');
			}
		}
	}, {
		text: "取消",
		handler: function () {
			add_win.hide();
		}
	}]
});
//添加窗口
if (!add_win) {
	add_win = new Ext.Window({
		layout: "border",
		width: 370,
		height: 180,
		title: '添加备注',
		closeAction: 'hide',
		plain: true,
		items: [addPanel]
	});
}