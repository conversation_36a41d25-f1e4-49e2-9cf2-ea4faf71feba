var add_win;//添加窗口
var add_update_id = '';//判断是添加还是修改
var detail_edit = false; //记录detail是否有改动
var main_no   = '';
var row_index = '';  //当前选中的行index
var isStopEditDetail = '';//是否禁止编辑开卡明细
var isCopy = 0;
var settle_obj = ''; //核算主体
var addInner_update_id = '';//内部交易判断是添加还是修改
var addInner_win;
function resetLeftMoney() {
    var tmpMoney = 0.00;
    var totalAmount = Ext.getCmp("amount").getValue();
    for (var i = 0; i < detail_record_store_main.data.length; i++) {
        var rec = detail_record_store_main.getAt(i);
        tmpMoney = accAdd(tmpMoney, rec.get('pay_amount'));
    }
    tmpMoney = (isNaN(tmpMoney)) ? 0.00 : tmpMoney;
    var leftMoney = accSub(totalAmount,tmpMoney);
    Ext.getCmp('leftMoney').setValue(leftMoney+'元');
}

function accAdd(num1,num2){
    var r1,r2,m;
    try{
        r1 = num1.toString().split('.')[1].length;
    }catch(e){
        r1 = 0;
    }
    try{
        r2=num2.toString().split(".")[1].length;
    }catch(e){
        r2=0;
    }
    m=Math.pow(10,Math.max(r1,r2));
    // return (num1*m+num2*m)/m;
    return Math.round(num1*m+num2*m)/m;
}

// 两个浮点数相减
function accSub(num1,num2){
    var r1,r2,m;
    try{
        r1 = num1.toString().split('.')[1].length;
    }catch(e){
        r1 = 0;
    }
    try{
        r2=num2.toString().split(".")[1].length;
    }catch(e){
        r2=0;
    }
    m=Math.pow(10,Math.max(r1,r2));
    n=(r1>=r2)?r1:r2;
    return (Math.round(num1*m-num2*m)/m).toFixed(n);
}

function getDetailStr() {
    var string = '';
    if (detail_record_store_main.data.length > 0) {
        for (var i = 0; i < detail_record_store_main.data.length; i++) {
            var rec = detail_record_store_main.getAt(i);
            (string) ? string = string + '|' : '';
            string = string + rec.get('service_area') + '#' +
                rec.get('pay_amount') + '#' +
                rec.get('remark') + '#';
        }
    }

    return string;
}

function checkDetailMoney(moeny) {
    var returnFlag = 0;
    var tmpMoney = 0.00;
    if (detail_record_store_main.data.length > 0) {
        for (var i = 0; i < detail_record_store_main.data.length; i++) {
            var rec = detail_record_store_main.getAt(i);
            tmpMoney = accAdd(tmpMoney, rec.get('pay_amount'));
        }
        if (isNaN(tmpMoney)) {
            returnFlag = -10;
        }
        if (tmpMoney > moeny) {
            returnFlag = 10;
        }
        if (tmpMoney < moeny) {
            returnFlag = 20;
        }
    }

    return returnFlag;
}

function checkServiceArea() {
    var result = 0;
    if (detail_record_store_main.data.length > 0) {
        for (var i = 0; i < detail_record_store_main.data.length; i++) {
            var rec = detail_record_store_main.getAt(i);
            var tmpArea = rec.get('service_area');
            for (var j = 0; j < detail_record_store_main.data.length; j++) {
                if (tmpArea == detail_record_store_main.getAt(j).get('service_area') && i != j) {
                    result = 10;
                }
            }
        }
    } else {
        result = 20
    }
    return result;
}

var detail_record = Ext.data.Record.create([{name: 'id', type: 'string'}]);
//缓存的
var detail_record_store_main = new Ext.data.GroupingStore({
    reader: detail_record,
    proxy: new Ext.data.MemoryProxy(detail_record), AutoLoad: true, sortInfo: {}
});

var detail_add_editor = new Ext.ux.grid.RowEditor({
    saveText: '确定',
    clicksToEdit: 2,
    listeners: {
        'beforeedit': function (rd, idx) {
            if(Ext.getCmp('_status').getValue() >= 1){
                return false;
            }
            detail_edit = true;
            Ext.getCmp('del_detail').disable();
        },
        'validateedit': function (rd, rec, reced, idx) {
            //有修改时才会调用
        },
        'afteredit': function (rd, rec, reced, idx) {
            //有修改时才会调用
            Ext.getCmp('btn_save').enable();
            Ext.getCmp('del_detail').enable();
            resetLeftMoney();
        },
        'canceledit': function (rd, rec, reced, idx) {
            var num = detail_record_store_main.data.length;
            if (num > 0) {
                for (var i = 0; i < num; i++) {
                    if(detail_record_store_main.getAt(i)){
                        if (!detail_record_store_main.getAt(i).get('bind_status')) {
                            // Ext.getCmp('detail_bind_status').allowBlank = true;
                            // Ext.getCmp('org_id').allowBlank = true;
                            // var s = detail_add_panel.getSelectionModel().getSelections();
                            // detail_record_store_main.remove(s[0]);
                        }
                    }
                }
                Ext.getCmp('btn_save').enable();
                Ext.getCmp('del_detail').enable();
            }
        },
    }
});

function detail_main_rand_num(n) {
    var rnd = "";
    for (var i = 0; i < n; i++)
        rnd += Math.floor(Math.random() * 10);
    return rnd;
}

//详情添加行事件
function detail_grid_cell_add() {

    Ext.getCmp('btn_save').disable();
    detail_edit = true;
    var e       = new detail_record({
        id: detail_main_rand_num(10)
    });

    if(settle_obj == 10 && detail_record_store_main.getCount() >= 1){
        Ext.MessageBox.alert("消息!","核算主体为渠道只能增加一个付款明细!");
        return false;
    }
    detail_add_editor.stopEditing();
    detail_record_store_main.add(e);
    detail_add_panel.getView().refresh();
    detail_add_panel.getSelectionModel().selectRow(detail_record_store_main.getCount() - 1);

    detail_add_editor.startEditing(detail_record_store_main.getCount() - 1);
}

var _sm = new Ext.grid.RowSelectionModel({
    listeners: {
        selectionchange: function (data) {
            if (data.getCount() && Ext.getCmp('_status').getValue() < 1) {
                Ext.getCmp('del_detail').enable();
            } else {
                Ext.getCmp('del_detail').disable();
            }
        }
    }
});

var detail_add_panel = new Ext.grid.GridPanel({
    store: detail_record_store_main,
    region: 'south',
    title: '付款明细',
    height: 260,
    width: 1010,
    style: 'padding-left:35px;margin-top:10px;',
    autoScroll: true,
    enableColumnResize: false,
    sm: _sm,
    plugins: [detail_add_editor],
    tbar: [
        {
            iconCls: 'silk-add',
            text: '增行',
            id: 'add_detail',
            disabled: true,
            handler: function () {
                var copName = Ext.getCmp("cooperation_type").getValue();
                if (copName == '主卡' && detail_record_store_main.getCount() > 0) {
                    Ext.MessageBox.alert("消息!","供应商为主卡模式，仅能给一张主卡付款！");
                    return false;
                }

                detail_grid_cell_add();
            }
        },
        {
            iconCls: 'silk-delete',
            text: '删行',
            id: 'del_detail',
            disabled: true,
            handler: function () {
                detail_edit = true;
                var s       = detail_add_panel.getSelectionModel().getSelections();
                for (var i = 0, r; r = s[i]; i++) {
                    detail_record_store_main.remove(r);
                }
                resetLeftMoney();
                Ext.getCmp('del_detail').disable();
            }
        }
    ],
    columns: [
        new Ext.grid.RowNumberer({header: '序号', width: 35}),
        {
            header: '<span style="color: red">*</span> 核算主体',
            dataIndex: 'service_area',
            width: 280,
            sortable: true,
            editor:   new Ext.form.ComboBox({
                width: 150,
                id: 'service_area',
                hiddenName:'detail_service_area',
                mode: 'local',
                value: '',
                triggerAction: 'all',
                forceSelection: true,
                displayField: 'value',//显示的值
                valueField: 'name',//后台接收的key
                store: stationStore,
                allowBlank:false,
                blankText:'核算主体',
                emptyText: '请选择..',
                enableKeyEvents: true,
                listeners: {
                    'beforequery': function (e) {
                        var combo = e.combo;
                        if (!e.forceAll) {
                            var input = e.query;
                            // 检索的正则
                            var regExp = new RegExp(".*" + input + ".*");
                            // 执行检索
                            combo.store.filterBy(function (record, id) {
                                // 得到每个record的项目名称值
                                var text = record.get(combo.displayField);
                                return regExp.test(text);
                            });
                            combo.expand();

                            return false;
                        }
                    },
                    keyup: function (field, e) {
                        if (e.keyCode == 40) {    //向下键
                            detail_grid_cell_add();
                        }
                    },
                    // select: function () {
                    //     getDetailOrgcode.load();
                    // }
                }
            }),
        },
        {
            id: 'pay_amount',
            header: '付款金额',
            dataIndex: 'pay_amount',
            width: 150,
            sortable: true,
            editor: new Ext.form.TextField({
                id: 'detail_pay_money',
                regex: /^(([1-9][0-9]*\.[0-9][0-9]*)|([0]\.[0-9][0-9]*)|([1-9][0-9]*)|([0]{1}))$/,
                regexText: '请输入正确金额',
                enableKeyEvents: true,
                listeners: {
                    keyup: function (field, e) {
                        if (e.keyCode == 40) {    //向下键
                            detail_grid_cell_add();
                        }
                    }
                }
            })
        },
        {
            id: 'remark',
            header: '备注',
            dataIndex: 'remark',
            width: 499,
            sortable: true,
            renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                if (value) {
                    return '<a title = "' + value + '">' + value + '</a>';
                } else {
                    return '';
                }
            },
            editor: new Ext.form.TextField({
                id: 'detail_remark',
                enableKeyEvents: true,
                listeners: {
                    keyup: function (field, e) {
                        if (e.keyCode == 40) {    //向下键
                            detail_grid_cell_add();
                        }
                    }
                }
            })
        }
    ]
})


function initLabelTxt(){
	Ext.getCmp("charge_type").setValue("");
	Ext.getCmp("status").setValue("");
	Ext.getCmp("cooperation_type").setValue("");
	Ext.getCmp("company_name").setValue("");
	Ext.getCmp("bank_account_no").setValue("");
	Ext.getCmp("bank_name").setValue("");
	//Ext.getCmp("receipt_title").setValue("");
	Ext.getCmp("operator_name").setValue("");
	Ext.getCmp("settlement_docker").setValue("");
	Ext.getCmp("network_docker").setValue("");

	stationStore.removeAll();
	// Ext.getCmp("station").setValue("");
	// Ext.getCmp("station").setRawValue("");

	// Ext.getCmp("channel_info").setValue("");
	Ext.getCmp("account_no").setValue("");
    Ext.getCmp('add_detail').disable();
}

var add_form_panel = new Ext.form.FormPanel({
    region: 'center',
    hideLabels: true,
    bodyStyle: 'padding: 20px',
    trackResetOnLoad: true,
    items: [
        {// 第一行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '供应商名称：',
                    style: 'padding-left: 35px'
                },
                new Ext.form.ComboBox({
                    width: 400,
                    editable: true,
                    id: 'channel_s',
                    hiddenName: 'channel',
                    store: channelWorkStore,
                    emptyText: '请选择',
                    mode: 'local',
                    displayField: 'name',
                    valueField: 'id',
                    triggerAction: 'all',
                    selectOnFocus: true,
                    listWidth: 400,
                    listeners:{
                        focus: function(){
                            channelWorkStore.load();
                        },
                        select: function(combo, record, index){
                            settle_obj = record.data.settle_obj;

                            var supplier_id = Ext.getCmp('channel_s').getValue();

                            add_form_panel.getEl().mask("loading...");
                            detail_record_store_main.removeAll();
                            Ext.getCmp('bank_s').setValue('');
                            bankListStore.removeAll();
                            initLabelTxt();
                            supperorStore.load({
                                params:{"supplier_id":supplier_id},
                                callback: function(records, options, success){
                                    var info = records[0].data;
                                    Ext.getCmp("charge_type").setValue(info.recharge_type_txt);
                                    Ext.getCmp("status").setValue(info.status_txt);
                                    Ext.getCmp("cooperation_type").setValue(info.cooperation_type_txt);
                                    //Ext.getCmp("company_name").setValue(info.company_name);
                                    //Ext.getCmp("bank_account_no").setValue(info.bank_account_no);
                                    //Ext.getCmp("receipt_title").setValue(info.receipt_title);
                                    Ext.getCmp("operator_name").setValue(info.operator);
                                    Ext.getCmp("settlement_docker").setValue(info.settlement_docker);
                                    Ext.getCmp("network_docker").setValue(info.network_docker);
                                    //Ext.getCmp("bank_name").setValue(info.bank_name);
                                    // if(settle_obj == 10){
                                    //     Ext.getCmp('add_detail').disable();
                                    //     var _r = new detail_record({
                                    //         service_area: '1111',
                                    //         pay_amount: '',
                                    //         remark: '备注备注'
                                    //     });
                                    //
                                    //     detail_record_store_main.add(_r);
                                    //
                                    //     //resetLeftMoney();
                                    //
                                    //     Ext.getCmp('del_detail').disable();
                                    //
                                    //     //Ext.getCmp('service_area').editable = false;
                                    //     //detail_add_editor.stopEditing(true);
                                    // }else{
                                    //     Ext.getCmp('add_detail').enable();
                                    // }
                                    Ext.getCmp('add_detail').enable();

                                    cooper_type = info.cooperation_type;
                                    var cp_type = 0;
                                    if(info.cooperation_type == 30){
                                        // Ext.getCmp('mainBox').show();
                                        // Ext.getCmp('selectBox').hide();
                                        cp_type = 1;
                                    }else{
                                        Ext.getCmp("account_no").setValue("--");
                                        // Ext.getCmp('selectBox').show();
                                        // Ext.getCmp('mainBox').hide();
                                        cp_type = 0;
                                    }
                                    stationStore.load({
                                        params:{"supplier_id":supplier_id,"type":cp_type},
                                        callback: function(records, options, success){
                                            add_form_panel.getEl().unmask();
                                            resetLeftMoney();
                                            if(info.cooperation_type != 30) {
                                                var allAreaInfo = "";
                                                for (var j = 0; j < records.length; j++) {
                                                    allAreaInfo += records[j].data.name + ',';
                                                }
                                                var newArea = allAreaInfo.substring(0,allAreaInfo.length - 1);
                                                // Ext.getCmp("station").setValue(newArea);
                                                // Ext.getCmp("station").setRawValue(newArea);
                                                // Ext.getCmp("channel_info").setValue(newArea);
                                            }
                                        }
                                    });
                                },
                            });
                            //获取指定收款公司
                            Ext.Ajax.request({
                                url: getUrl('oil_foss_payment_records', 'getSupplierCompany'),
                                method: 'post',
                                params: {supplier_id: supplier_id},
                                success: function sFn(response, options) {
                                    var result = Ext.decode(response.responseText);
                                    if(result.code == 0){
                                        company_info = result.data;
                                        Ext.getCmp("company_name").setValue(company_info.company_name);
                                        bankListStore.load({
                                            params:{"supplier_id":supplier_id}
                                        });
                                        Ext.getCmp('bank_s').enable();
                                    }else{
                                        var msg = result.msg ? result.msg : '操作失败';
                                        Ext.MessageBox.alert('提示', msg);
                                    }

                                }
                            });
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                    }
                }),
                {xtype: 'displayfield',value: ' <font color=red>*</font>'},

                {
                    xtype: 'displayfield',
                    value: '账号：',
                    style: 'padding-left: 35px'
                },
                new Ext.form.ComboBox({
                    width: 400,
                    editable: true,
                    id: 'bank_s',
                    hiddenName: 'collect_bank_id',
                    store: bankListStore,
                    emptyText: '请选择',
                    mode: 'local',
                    displayField: 'name',
                    valueField: 'id',
                    triggerAction: 'all',
                    selectOnFocus: true,
                    listWidth: 400,
                    listeners: {
                        select: function(combo, record, index){
                            var bank_info = record.data;
                            Ext.getCmp("bank_account_no").setValue(bank_info.bank_account_no);
                            Ext.getCmp("bank_name").setValue(bank_info.bank_name);
                        }
                    }
                }),
                {xtype: 'displayfield',value: ' <font color=red>*</font>'},
            ]
        },
        // {// 第二行
        // 	xtype: 'compositefield',
        // 	id: "mainBox",
        // 	items:[
        // 		{
        // 			xtype: 'displayfield',
        // 			value: '主&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;卡：',
        // 			style: 'padding-left: 35px',
        // 		},
        // 		new Ext.form.ComboBox({
        // 			width: 400,
        // 			id: 'main_no',
        // 			hiddenName: 'mainNoName',
        // 			mode: 'local',
        // 			value: '',
        // 			triggerAction: 'all',
        // 			forceSelection: true,
        // 			displayField: 'name',//显示的值
        // 			valueField: 'value',//后台接收的key
        // 			emptyText: '请选择',
        // 			enableKeyEvents: false,
        // 			store: stationStore,
        // 			listWidth: 240,
        // 			listeners:{
        // 				select: function(combo, record, index){
        //
        // 					var selectValue = Ext.getCmp('main_no').getValue();
        // 					// Ext.getCmp("channel_info").setValue(selectValue);
        //
        // 					var supplier_id = Ext.getCmp('channel_s').getValue();
        // 					var code = Ext.getCmp('main_no').getValue();
        // 					Ext.Ajax.request({
        // 						url:'../inside.php?t=json&m=oil_foss_payment_records&f=getStationInfo',
        // 						method:'post',
        // 						params:{supplier_id:supplier_id,code:code,cooper_type:cooper_type},
        // 						success: function(resp,opts){
        // 							var result = Ext.decode(resp.responseText);
        // 							Ext.getCmp("account_no").setValue(result.data.account);
        // 						},
        // 						failure: function(resp,opts){
        // 							Ext.MessageBox.alert('提示',Ext.decode(resp.responseText).msg);
        // 						}
        // 					});
        // 				},
        // 				'beforequery': function (e) {
        // 					var combo = e.combo;
        // 					if (!e.forceAll) {
        // 						var input = e.query;
        // 						// 检索的正则
        // 						var regExp = new RegExp(".*" + input + ".*");
        // 						// 执行检索
        // 						combo.store.filterBy(function (record, id) {
        // 							// 得到每个record的项目名称值
        // 							var text = record.get(combo.displayField);
        // 							return regExp.test(text);
        // 						});
        // 						combo.expand();
        // 						return false;
        // 					}
        // 				},
        // 			}
        // 		}),
        // 		{xtype: 'displayfield', value: ' <font color=red>*</font>'},
        // 	]
        // },
        // {// 第二行
        // 	xtype: 'compositefield',
        // 	id: "selectBox",
        // 	items:[
        // 		{
        // 			xtype: 'displayfield',
        // 			value: '服务区（油站）：',
        // 			style: 'padding-left: 35px',
        // 		},
        // 		new Ext.form.MultiSelect({
        // 			width: 350,
        // 			id: 'station',
        // 			hiddenName: 'stationName',
        // 			mode: 'local',
        // 			value: '',
        // 			triggerAction: 'all',
        // 			// forceSelection: true,
        // 			displayField: 'name',//显示的值
        // 			valueField: 'value',//后台接收的key
        // 			enableKeyEvents: false,
        // 			emptyText: '请选择',
        // 			store: stationStore,
        // 			listWidth: 350,
        // 			listeners:{
        // 				'select':function () {
        // 					var selectValue = Ext.getCmp('station').getValue();
        // 					// Ext.getCmp("channel_info").setValue(selectValue);
        // 				},
        // 				'beforequery': function (e) {
        // 					var combo = e.combo;
        // 					if (!e.forceAll) {
        // 						var input = e.query;
        // 						// 检索的正则
        // 						var regExp = new RegExp(".*" + input + ".*");
        // 						// 执行检索
        // 						combo.store.filterBy(function (record, id) {
        // 							// 得到每个record的项目名称值
        // 							var text = record.get(combo.displayField);
        // 							return regExp.test(text);
        // 						});
        // 						combo.expand();
        // 						return false;
        // 					}
        // 				},
        // 			}
        // 		}),
        // 		{xtype: 'displayfield', value: ' <font color=red>*</font>'},
        // 	]
        // },
        {
            xtype: 'compositefield',
            items:[
                {
                    xtype: 'displayfield',
                    value: '充&nbsp;值&nbsp;总&nbsp;额：',
                    style: 'padding-left: 36px',
                },
                {
                    xtype: 'textfield',
                    id: 'amount',
                    width: 200,
                    allowBlank: false,
                    regex: /^[1-9|0]*[0-9]?(\.[0-9]{1,2})?$/,
                    regexText: '格式不正确',
                    listeners: {
                        blur: function (field, e) {
                            resetLeftMoney();
                        }
                    }
                },
                {xtype: 'displayfield', value: '元 <font color=red>*</font>'},
                {
                    xtype: 'displayfield',
                    value: '充值方式：',
                    style: 'padding-left: 171px'
                },
                {
                    xtype: 'displayfield',
                    id: 'charge_type',
                    width: 100,
                    style: 'color:red;font-size:9px;font-weight:bold;'
                }
            ]
        },
        {// 第二行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '单号：',
                    style: 'padding-left: 71px'
                },
                {
                    xtype: 'displayfield',
                    id: 'no',
                    width: 100
                },
                {
                    xtype: 'displayfield',
                    value: '合作类型：',
                    style: 'padding-left: 80px'
                },
                {
                    xtype: 'displayfield',
                    id: 'cooperation_type',
                    width: 120,
                    style: 'color:red;font-size:9px;font-weight:bold;'
                },
                {
                    xtype: 'displayfield',
                    value: '账号：',
                    style: 'padding-left: 52px'
                },
                {
                    xtype: 'displayfield',
                    id: 'account_no',
                    width: 120
                },
                {
                    xtype: 'displayfield',
                    value: '创建时间：',
                    style: 'padding-left: 9px'
                },
                {
                    xtype: 'displayfield',
                    id: 'createtime',
                    width: 120
                },
                {
                    xtype: 'displayfield',
                    value: '状态：',
                    style: 'padding-left: 14px'
                },
                {
                    xtype: 'displayfield',
                    id: 'status',
                    width: 90
                },
                {xtype: 'hidden', id: '_status', value: ''},
            ]
        },
        // {
        // 	xtype: 'compositefield',
        // 	items: [
        // 		{
        // 			xtype: 'displayfield',
        // 			value: '渠道信息：',
        // 			style: 'padding-left: 36px'
        // 		},
        // 		{
        // 			xtype: 'displayfield',
        // 			id: 'channel_info',
        // 			width: 1000
        // 		},
        // 	]
        // },
        {// 第3行
            xtype: "compositefield",
            items: [
                {
                    xtype: 'displayfield',
                    value: '签约运营商：',
                    style: 'padding-left: 35px'
                },
                {
                    xtype: 'displayfield',
                    id: 'operator_name',
                    width: 100
                },
                {
                    xtype: 'displayfield',
                    value: '运营对接人：',
                    style: 'padding-left: 68px'
                },
                {
                    xtype: 'displayfield',
                    id: 'settlement_docker',
                    width: 100
                },
                {
                    xtype: 'displayfield',
                    value: '网络对接人：',
                    style: 'padding-left: 36px'
                },
                {
                    xtype: 'displayfield',
                    id: 'network_docker',
                    width: 100
                },
                {
                    xtype: 'displayfield',
                    value: '最后修改时间：',
                    style: 'padding-left: 5px'
                },
                {
                    xtype: 'displayfield',
                    id: 'last_update',
                    width: 120
                },
                {
                    xtype: 'displayfield',
                    value: '修改人：',
                    style: 'padding-left: 2px'
                },
                {
                    xtype: 'displayfield',
                    id: 'last_operator',
                    width: 100
                },
            ]
        },
        {
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '收款账号：',
                    style: 'padding-left: 47px'
                },
                {
                    xtype: 'displayfield',
                    id: 'bank_account_no',
                    width: 140
                },
                {
                    xtype: 'displayfield',
                    value: '收款公司：',
                    style: 'padding-left: 40px'
                },
                {
                    xtype: 'displayfield',
                    id: 'company_name',
                    width: 340
                },
                {
                    xtype: 'displayfield',
                    value: '开户行：',
                    style: 'padding-left: 19px'
                },
                {
                    xtype: 'displayfield',
                    id: 'bank_name',
                    width: 360
                },
                /*{
                 xtype: 'displayfield',
                 id: 'bank_name',
                 width: 140
                 },*/
                // {
                // 	xtype: 'displayfield',
                // 	value: '回票抬头：',
                // 	style: 'padding-left: 60px'
                // },
                // {
                // 	xtype: 'displayfield',
                // 	id: 'receipt_title',
                // 	width: 140
                // },
            ]
        },
        {// 第4行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: "摘&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;要：",
                    style: 'padding-left: 47px'
                },
                {
                    xtype: 'textfield',
                    id: 'bank_summary',
                    width: 899,
                    allowBlank: false,
                    maxLength:60
                },
                {xtype: 'displayfield', value: ' <font color=red>*</font>'},
            ]
        },
        {// 第5行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '内部反馈：',
                    style: 'padding-left: 46px'
                },
                {
                    xtype: 'textfield',
                    id: 'foss_remark',
                    width: 899,
                    maxLength:200,
                },
            ]
        },
        {// 第5行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：',
                    style: 'padding-left: 47px'
                },
                {
                    xtype: 'textfield',
                    id: 'remark',
                    width: 899,
                    maxLength:200,
                },
            ]
        },
        detail_add_panel,
        {// 第5行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '剩余金额',
                    style: 'padding-left: 36px'
                },
                {
                    xtype: 'displayfield',
                    id: 'leftMoney',
                    value: '--',
                    width:150,
                    style: 'color:red;font-size:9px;'
                },
                {
                    xtype: 'displayfield',
                    value: '余额全部分配完毕后，即可提交申请',
                },
            ]
        },
    ],
    buttons: [{
        text: "提交申请",
        id: 'btn_save',
        listeners: {
            click: function () {
                var amount = Ext.getCmp("amount").getValue();
                var channel_s = Ext.getCmp("channel_s").getValue();
                var bank_s = Ext.getCmp("bank_s").getValue();
                var remark = Ext.getCmp("remark").getValue();
                var bank = Ext.getCmp("bank_name").getValue();
                var bankSummary = Ext.getCmp("bank_summary").getValue();
                if(channel_s.length <= 0){
                    Ext.MessageBox.alert("消息!", "请选择供应商");
                    return false;
                }
                if(bank_s.length <= 0){
                    Ext.MessageBox.alert("消息!", "请选择账号");
                    return false;
                }
                console.log(amount)
                if(amount.length <= 0){
                    Ext.MessageBox.alert("消息!", "请输入充值金额");
                    return false;
                }
                if(bank.length <= 0){
                    Ext.MessageBox.alert("消息!", "请选择开户行");
                    return false;
                }
                var tmpAmount = 0;
                if(amount.indexOf(".")) {
                    var tmpLen = amount.split(".");
                    tmpAmount = tmpLen[0];
                }else{
                    tmpAmount = amount;
                }
                if(tmpAmount.length > 8){
                    Ext.MessageBox.alert("消息!", "充值金额不合法");
                    return false;
                }
                if(bankSummary.length <= 0){
                    Ext.MessageBox.alert("消息!", "请填写摘要");
                    return false;
                }
                if(remark.length > 200){
                    Ext.MessageBox.alert("消息!", "备注最多输入200个字符");
                    return false;
                }
                var resultCheckRepeatServiceArea = checkServiceArea();
                if (resultCheckRepeatServiceArea == 10) {
                    Ext.MessageBox.alert("消息!","存在重复服务区，无法提交付款申请");
                    return false;
                }
                if (resultCheckRepeatServiceArea == 20) {
                    Ext.MessageBox.alert("消息!","请给服务区/主卡分配充值金额");
                    return false;
                }
                var resultCheckDetailMoney = checkDetailMoney(amount);
                if (resultCheckDetailMoney == -10) {
                    Ext.MessageBox.alert("消息!","请完善明细内容后再提交申请");
                    return false;
                }
                if (resultCheckDetailMoney == 10) {
                    Ext.MessageBox.alert("消息!","分配金额大于充值总额，无法提交付款申请");
                    return false;
                }
                if (resultCheckDetailMoney == 20) {
                    Ext.MessageBox.alert("消息!","剩余金额未分配完，无法提交付款申请");
                    return false;
                }

                if(isCopy == 1){
                    add_update_id = 0;
                }
                var _form = this.ownerCt.ownerCt.getForm();
                if(_form.isValid()) {
                    _form.submit({
                        url: '../inside.php?t=json&m=' + controlName + '&f=create',
                        waitMsg: 'Saving Data...',
                        params: {
                            update_id: add_update_id,
                            details: getDetailStr()
                        },
                        success: function (resp, opts) {
                            Ext.MessageBox.alert("消息!", Ext.decode(opts.response.responseText).msg);
                            main_store.removeAll();
                            main_store.load();
                            add_win.hide();
                        },
                        failure: function (resp, opts) {
                            Ext.MessageBox.alert("消息!", Ext.decode(opts.response.responseText).msg);
                        }
                    });
                }
            }
        }
    }, {
        text: "取消",
        id: 'btn_cancel',
        handler: function () {
            /*Ext.MessageBox.confirm('导出', "确定取消吗？", function showResult(btn) {
             if (btn == 'yes') {
             add_win.hide();
             }
             });*/
            var edit = this.ownerCt.ownerCt.getForm().isDirty();
            if (edit || detail_edit) {
               Ext.Msg.show({
                   title: '提示',
                   msg: '有未保存的数据，是否保存？',
                   buttons: {
                       yes: '是',
                       no: '否',
                       cancel: '取消'
                   },
                   fn: function (e) {
                       if (e == 'yes') {
                           Ext.getCmp('btn_save').fireEvent('click');
                       }
                       if (e == 'no') {
                           detail_record_store_main.removeAll();
                           add_win.hide();
                       }
                   },
               });
            } else {
               add_win.hide();
            }
        }
    }]
});

//主窗体
if (!add_win) {
    add_win = new Ext.Window({
        layout: "border",
        width: 1100,
        height: 590,
        title: '添加/编辑',
        closeAction: 'hide',
        plain: true,
        modal: true, //遮罩层
        items: [add_form_panel]
    });
}

// var windows = _getWindow({
//     title: '添加备注',
//     id: 'add_win',
//     width: 550,
//     height:270,
//     button: [{
//         text: '确定',
//         id: 'button_ok',
//         handler: function () {
//             addPanel.submit();
//         }
//     }]
// });

/**
 * 初始化表单控件
 * @param status
 */
function initFormInput(status) {
    if(status < 1){
        Ext.getCmp('add_detail').disable();
        Ext.getCmp('del_detail').disable();
    }else{

        Ext.getCmp('add_detail').enable();
        Ext.getCmp('del_detail').disable();
    }
}

function strReplace(str)
{
    var exc = arguments[1]?arguments[1] : '……';
    var res = arguments[2]?arguments[2] : '';
    if(str.indexOf(exc) != -1)
    {
        msg = str.replace( exc , res );
        strReplace( msg );//递归调用
    }
    else
    {
        msg = str;
    }
    return msg;
}

function win_add() {
    isCopy = 0;
    add_update_id = '';
    detail_edit   = false;

    //初始化表单
    initFormInput(0);

    supperorStore.removeAll();
    stationStore.removeAll();

    add_form_panel.getForm().setValues([
        {id: 'channel_s', value: ''},
        {id: 'bank_s', value: ''},
        {id: 'oil_com', value: ''},
        {id: 'charge_type', value: ''},
        {id: 'status', value: ''},
        {id: 'cooperation_type', value: ''},
        {id: 'company_name', value: ''},
        {id: 'bank_account_no', value: ''},
        {id: 'bank_name', value: ''},
        {id: 'operator_name', value: ''},
        {id: 'settlement_docker', value: ''},
        {id: 'network_docker', value: ''},
        {id: 'createtime', value: ''},
        {id: 'last_update', value: ''},
        {id: 'account_no', value: ''},
        {id: 'bank_summary', value: ''},
        {id: 'foss_remark', value: ''},
        {id: 'remark', value: ''},
        {id: 'no', value: ''},
        {id: 'amount', value: ''},
    ]);

    detail_record_store_main.removeAll();
    Ext.getCmp('del_detail').disable();

    Ext.getCmp('bank_s').disable();

    Ext.getCmp('btn_save').enable();

    Ext.getCmp('channel_s').emptyText = '请选择..';
    Ext.getCmp('channel_s').applyEmptyText();

    Ext.getCmp('bank_s').emptyText = '请选择..';
    Ext.getCmp('bank_s').applyEmptyText();

    add_win.title = "添加";
    add_form_panel.buttons[0].text = "提交申请";
    add_win.show();
    add_form_panel.getForm().reset();
}

function win_update(isCope) {
    var preData = grid_list.getSelectionModel().getSelections();//当前选中行的数据
    preData = preData[0].data;
    if(preData.cooperation_type ==  40){
        //内部公司交易
        showUpdateInner();
    }else{
        let loadPaymentRecord = true
        $.ajax({
            url: '../inside.php?t=json&m=oil_foss_payment_records&f=getList',
            type: 'POST',
            dataType: 'json',
            async: false,
            data: {
                id: preData.id,
            },
            success: function(res){
                if (!res.hasOwnProperty("code") || res.code !== 0 || res.data.data.length <= 0) {

                    loadPaymentRecord = false
                    return false
                }
                preData.bank_account_no = res.data.data[0].bank_account_no
                preData.settlement_docker = res.data.data[0].settlement_docker
                preData.network_docker = res.data.data[0].network_docker
                preData.account = res.data.data[0].account
            },
            error: function(){
                loadPaymentRecord = false
            },
        });
        if (!loadPaymentRecord) {
            Ext.MessageBox.alert("系统提示", '付款详细信息获取失败,请稍后重试')
            return false
        }
        detail_edit   = true;

        //初始化表单
        initFormInput(1);
        isCopy = isCope
        var cooper_type = 0;
        add_win.title = "添加/编辑";

        console.log('ttttttt');
        console.log(preData);
        add_update_id = preData.id;

        add_form_panel.getForm().loadRecord({data : preData});
        add_form_panel.buttons[0].text = "重新提交申请";
        Ext.getCmp("charge_type").setValue(preData.recharge_type_txt);
        Ext.getCmp("status").setValue(preData.status_txt);
        Ext.getCmp("cooperation_type").setValue(preData.cooperation_type_txt);
        Ext.getCmp("amount").setValue(preData.recharge_amount);

        Ext.getCmp("channel_s").setValue(preData.supplier_id);
        Ext.getCmp("channel_s").setRawValue(preData.supplier_name);

        Ext.getCmp("bank_s").setValue(preData.collect_bank_id);
        Ext.getCmp("bank_s").setRawValue(strReplace(preData.bank_account_no));

        Ext.getCmp("amount").setValue(preData.recharge_amount);
        Ext.getCmp("last_update").setValue(preData.updatetime);
        var cp_type = 0;
        if(preData.cooperation_type == 30){
            Ext.getCmp("account_no").setValue(preData.account);
            // Ext.getCmp('mainBox').show();
            // Ext.getCmp('selectBox').hide();
            cp_type = 1;
        }else{
            //Ext.getCmp("station").setValue(preData.service_area_info);
            //Ext.getCmp("station").setRawValue(preData.service_area_info);
            Ext.getCmp("account_no").setValue("--");
            // Ext.getCmp('selectBox').show();
            // Ext.getCmp('mainBox').hide();
            cp_type = 0;
        }
        //复制添加
        if(isCope == 1){
            add_win.title = "复制添加";
            add_form_panel.buttons[0].text = "提交申请";
            Ext.getCmp("last_update").setValue("");
            Ext.getCmp("last_operator").setValue("");
            Ext.getCmp("no").setValue("");
            Ext.getCmp("createtime").setValue("");
            Ext.getCmp("status").setValue("Foss丨待审核");
        }

        stationStore.removeAll();
        stationStore.load({
            params:{"supplier_id":preData.supplier_id,"type":cp_type},
        });
        stationStore.on('load',function(){
            //服务区下拉选中
            if(preData.cooperation_type != 30) {
                // Ext.getCmp("station").setValue(preData.service_area_info);
                // Ext.getCmp("station").setRawValue(preData.service_area_info);
            }else{
                // Ext.getCmp("main_no").setValue(preData.service_area_info);
                // Ext.getCmp("main_no").setRawValue(preData.service_area_info);
            }
        });
        if (add_update_id) {
            detail_record_store_main.removeAll();
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=oil_foss_payment_detail&f=getList',
                params: {payment_id: add_update_id},
                method: 'POST',
                success: function (response, options) {
                    var obj = Ext.decode(response.responseText).data.data;
                    if (obj.length > 0) {
                        for (var i = 0; i < obj.length; i++) {
                            var _r = new detail_record({
                                service_area: obj[i].service_area,
                                pay_amount: obj[i].pay_amount,
                                remark: obj[i].remark
                            });

                            detail_record_store_main.add(_r);
                        }
                    }

                    resetLeftMoney();
                }
            });
        }

        add_win.show();
    }

}
var addInner_form_panel = new Ext.form.FormPanel({
    region: 'center',
    hideLabels: true,
    bodyStyle: 'padding: 20px',
    trackResetOnLoad: true,
    items: [
        {// 第一行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '收款方名称：',
                    style: 'padding-left: 35px'
                },
                new Ext.form.ComboBox({
                    width: 400,
                    editable: true,
                    id: 's_receive_company',
                    hiddenName: 'receive_company',
                    store: receiveCompanyStore,
                    emptyText: '请选择',
                    mode: 'local',
                    displayField: 'company_name',
                    valueField: 'id',
                    triggerAction: 'all',
                    selectOnFocus: true,
                    listWidth: 400,
                    listeners:{
                        focus: function(){
                            receiveCompanyStore.load();
                        },
                        select:function (combo, record, index) {
                            Ext.getCmp("s_receive_account_no").setValue(record.data.collect_bank_account_no);
                            Ext.getCmp("bank_name").setValue(record.data.collect_bank_name);
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                    }
                }),
                {xtype: 'displayfield',value: ' <font color=red>*</font>'},

                {
                    xtype: 'displayfield',
                    value: '收款账号：',
                    style: 'padding-left: 35px'
                },
                {
                    xtype: 'displayfield',
                    id: 's_receive_account_no',
                    width: 100,
                },
                // {xtype: 'displayfield',value: ' <font color=red>*</font>'},
            ]
        },
        {
            xtype: 'compositefield',
            items:[
                {
                    xtype: 'displayfield',
                    value: '付款&nbsp;金额:',
                    style: 'padding-left: 36px',
                },
                {
                    xtype: 'textfield',
                    id: 'inner_amount',
                    width: 200,
                    allowBlank: false,
                    regex: /^[1-9|0]*[0-9]?(\.[0-9]{1,2})?$/,
                    regexText: '格式不正确',
                    listeners: {
                        blur: function (field, e) {
                            // resetLeftMoney();
                        }
                    }
                },
                {xtype: 'displayfield', value: '元 <font color=red>*</font>'},
                {
                    xtype: 'displayfield',
                    value: '付款方式：',
                    style: 'padding-left: 50px'
                },
                {
                    xtype: 'displayfield',
                    id: 's_pay_method',
                    width: 100,
                    value:'油品线下',
                    style: 'color:red;font-size:9px;font-weight:bold;'
                }
            ]
        },
        {// 第三行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '付款方名称：',
                    style: 'padding-left: 35px'
                },
                new Ext.form.ComboBox({
                    width: 400,
                    editable: true,
                    id: 's_pay_company',
                    hiddenName: 'pay_company',
                    store: payCompanyStore,
                    emptyText: '请选择',
                    mode: 'local',
                    displayField: 'company_name',
                    valueField: 'id',
                    triggerAction: 'all',
                    selectOnFocus: true,
                    listWidth: 400,
                    listeners:{
                        focus: function(){
                            payCompanyStore.load();
                        },
                        select:function (combo, record, index) {
                            Ext.getCmp("s_pay_account_no").setValue(record.data.platform_bank_account_no);
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                    }
                }),
                {xtype: 'displayfield',value: ' <font color=red>*</font>'},
                {
                    xtype: 'displayfield',
                    value: '付款账号：',
                    style: 'padding-left: 35px'
                },
                {
                    xtype: 'displayfield',
                    id: 's_pay_account_no',
                    width: 100,
                },
                // {xtype: 'displayfield',v
                //
                // alue: ' <font color=red>*</font>'},
            ]
        },
        {// 第四行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '合作类型：',
                    style: 'padding-left: 35px'
                },
                {
                    xtype: 'displayfield',
                    id: 'inner_cooperation_type',
                    value: '内部',
                    width: 100,
                    style: 'color:red;font-size:9px;font-weight:bold;'
                },
                {
                    xtype: 'displayfield',
                    value: '状态：',
                    style: 'padding-left: 14px'
                },
                {
                    xtype: 'displayfield',
                    id: 'inner_status',
                    value: '待审核',
                    width: 90
                },
            ]
        },
        {
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '开户行：',
                    style: 'padding-left: 35px'
                },
                {
                    xtype: 'displayfield',
                    id: 'inner_bank_name',
                    width: 250
                },
                {
                    xtype: 'displayfield',
                    value: '单号：',
                    style: 'padding-left: 10px',
                },
                {
                    xtype: 'displayfield',
                    id: 'inner_no',
                    width: 150,
                }
            ]
        },
        {
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '创建时间：',
                    style: 'padding-left: 35px',
                },
                {
                    xtype: 'displayfield',
                    id: 'inner_createtime',
                    width: 150
                },
                {
                    xtype: 'displayfield',
                    value: '最后修改时间：',
                    style: 'padding-left: 10px',
                },
                {
                    xtype: 'displayfield',
                    id: 'inner_last_update',
                    width: 150
                },
                {
                    xtype: 'displayfield',
                    value: '修改人：',
                    style: 'padding-left: 10px',
                },
                {
                    xtype: 'displayfield',
                    id: 'inner_last_operator',
                    width: 100
                }
            ]
        },
        {// 第6行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: "摘&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;要：",
                    style: 'padding-left: 35px'
                },
                {
                    xtype: 'textfield',
                    id: 'inner_bank_summary',
                    width: 899,
                    allowBlank: false,
                    maxLength:60
                },
                {xtype: 'displayfield', value: ' <font color=red>*</font>'},
            ]
        },
        {// 第7行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '内部反馈：',
                    style: 'padding-left: 35px'
                },
                {
                    xtype: 'textfield',
                    id: 'inner_foss_remark',
                    width: 899,
                    maxLength:200,
                },
            ]
        },
        {// 第8行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：',
                    style: 'padding-left: 35px'
                },
                {
                    xtype: 'textfield',
                    id: 'inner_remark',
                    width: 899,
                    maxLength:200,
                },
            ]
        },
    ],
    buttons: [{
        text: "提交申请",
        id: 'inner_btn_save',
        listeners: {
            click: function () {
                var receive_company = Ext.getCmp("s_receive_company").getValue();
                var pay_company = Ext.getCmp("s_pay_company").getValue();
                var amount = Ext.getCmp("inner_amount").getValue();
                var bankSummary = Ext.getCmp("inner_bank_summary").getValue();
                var remark = Ext.getCmp("inner_remark").getValue();

                if(receive_company.length <= 0){
                    Ext.MessageBox.alert("消息!", "请选择收款方名称");
                    return false;
                }
                if(pay_company.length <= 0){
                    Ext.MessageBox.alert("消息!", "请选择付款方名称");
                    return false;
                }
                if(amount.length <= 0){
                    Ext.MessageBox.alert("消息!", "请输入付款金额");
                    return false;
                }
                var tmpAmount = 0;
                if(amount.indexOf(".")) {
                    var tmpLen = amount.split(".");
                    tmpAmount = tmpLen[0];
                }else{
                    tmpAmount = amount;
                }
                if(tmpAmount.length > 8){
                    Ext.MessageBox.alert("消息!", "付款金额不合法");
                    return false;
                }
                if(bankSummary.length <= 0){
                    Ext.MessageBox.alert("消息!", "请填写摘要");
                    return false;
                }
                if(remark.length > 200){
                    Ext.MessageBox.alert("消息!", "备注最多输入200个字符");
                    return false;
                }
                var _form = this.ownerCt.ownerCt.getForm();
                if(_form.isValid()) {
                    _form.submit({
                        url: '../inside.php?t=json&m=' + controlName + '&f=addInner',
                        waitMsg: 'Saving Data...',
                        params: {
                            update_id: addInner_update_id,
                        },
                        success: function (resp, opts) {
                            Ext.MessageBox.alert("消息!", Ext.decode(opts.response.responseText).msg);
                            main_store.removeAll();
                            main_store.load();
                            addInner_win.hide();
                        },
                        failure: function (resp, opts) {
                            Ext.MessageBox.alert("消息!", Ext.decode(opts.response.responseText).msg);
                        }
                    });
                }
            }
        }
    }, {
        text: "取消",
        id: 'inner_btn_cancel',
        handler: function () {
            /*Ext.MessageBox.confirm('导出', "确定取消吗？", function showResult(btn) {
             if (btn == 'yes') {
             add_win.hide();
             }
             });*/
            var edit = this.ownerCt.ownerCt.getForm().isDirty();
            if (edit) {
                Ext.Msg.show({
                    title: '提示',
                    msg: '有未保存的数据，是否保存？',
                    buttons: {
                        yes: '是',
                        no: '否',
                        cancel: '取消'
                    },
                    fn: function (e) {
                        if (e == 'yes') {
                            Ext.getCmp('inner_btn_save').fireEvent('click');
                        }
                        if (e == 'no') {
                            addInner_win.hide();
                        }
                    },
                });
            } else {
                addInner_win.hide();
            }
        }
    }]
});
if (!addInner_win) {
    addInner_win = new Ext.Window({
        layout: "border",
        width: 1100,
        height: 400,
        title: '添加内部公司付款',
        closeAction: 'hide',
        plain: true,
        modal: true, //遮罩层
        items: [addInner_form_panel]
    });
}
function showAddInner() {
    addInner_update_id=0;
    addInner_win.show()
    addInner_form_panel.getForm().reset();
}

function showUpdateInner() {
    let preData = grid_list.getSelectionModel().getSelections();//当前选中行的数据
    preData = preData[0].data;
    addInner_update_id = preData.id;
    addInner_form_panel.getForm().loadRecord({data : preData});
    addInner_form_panel.buttons[0].text = "重新提交申请";
    let loadPaymentRecord = true
    $.ajax({
        url: '../inside.php?t=json&m=oil_foss_payment_records&f=getList',
        type: 'POST',
        dataType: 'json',
        async: false,
        data: {
            id: preData.id,
        },
        success: function(res){
            if (!res.hasOwnProperty("code") || res.code !== 0 || res.data.data.length <= 0) {

                loadPaymentRecord = false
                return false
            }
            preData.bank_account_no = res.data.data[0].bank_account_no
            preData.platform_bank_account_no = res.data.data[0].platform_bank_account_no
        },
        error: function(){
            loadPaymentRecord = false
        },
    });
    if (!loadPaymentRecord) {
        Ext.MessageBox.alert("系统提示", '付款详细信息获取失败,请稍后重试')
        return false
    }
    receiveCompanyStore.load({
        //回调处理
        callback:function () {
            //收款公司
            Ext.getCmp("s_receive_company").setValue(preData.supplier_id);
            Ext.getCmp("s_receive_company").setRawValue(preData.supplier_name);
            Ext.getCmp("s_receive_account_no").setValue(preData.bank_account_no);
        }
    });
    payCompanyStore.load({
        //回调处理
        callback:function () {
            //付款公司
            Ext.getCmp("s_pay_company").setValue(preData.operator_id);
            Ext.getCmp("s_pay_company").setRawValue(preData.operator_name);
            Ext.getCmp("s_pay_account_no").setValue(preData.platform_bank_account_no);
        }
    });
    console.log('内部交易编辑')
    console.log(preData)
    Ext.getCmp("inner_no").setValue(preData.no);
    Ext.getCmp("inner_status").setValue(preData.status_txt);
    Ext.getCmp("inner_cooperation_type").setValue(preData.cooperation_type_txt);
    Ext.getCmp("inner_amount").setValue(preData.recharge_amount);
    Ext.getCmp("inner_last_update").setValue(preData.updatetime);
    Ext.getCmp("inner_bank_name").setValue(preData.bank_name);
    Ext.getCmp("inner_bank_summary").setValue(preData.bank_summary);
    Ext.getCmp("inner_remark").setValue(preData.remark);
    Ext.getCmp("inner_foss_remark").setValue(preData.foss_remark);
    Ext.getCmp("inner_createtime").setValue(preData.createtime);
    Ext.getCmp("inner_last_operator").setValue(preData.last_operator);

    addInner_win.show()
}