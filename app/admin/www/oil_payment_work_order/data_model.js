/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_payment_work_order';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","no","pay_type","pay_item","out_approval_no","amount","approval_status","creator","operators_audit_time","operators_auditor","finance_auditor","finance_audit_time","last_operator","is_del","createtime","updatetime",        ]
    ),
});