//导出
function exportData() {
    Ext.getCmp('btnExport').disable();
    var params = top_panel.getForm().getValues(true);

    // Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
    //         if (btn == 'yes') {
    //
    //             var url = '/inside.php?t=json&m=' + controlName + '&f=search&_export=1' + '&' + params;
    //             requestUrl(url);
    //         }
    //     }
    // );
    Ext.Ajax.request({
        url:'../inside.php?t=json&m=oil_account_money_charge&f=search&_export=1',
        method:'post',
        params:params,
        success: function sFn(response,options)
        {
            Ext.getCmp('btnExport').enable();

            var url = Ext.decode(response.responseText).data.redirect_url;
            console.log(url);
            window.open(url);

            var msg = Ext.decode(response.responseText).msg;
            Ext.Msg.alert('提示',msg);
        }
    });
}

/**
 * 根据状态调整按钮是否可用
 * @param status
 */
function btnStatus(status,charge_type) {
    (Ext.getCmp('btnBack')) ? Ext.getCmp('btnBack').enable() : '';//编辑
    (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').enable() : '';//编辑
    (Ext.getCmp('btnStatus')) ? Ext.getCmp('btnStatus').disable() : '';//查询状态
    if (status == 0) {
        (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').enable() : '';//删除
        (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').enable() : '';//审核
        (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').enable() : '';//驳回
        (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
        if(charge_type == 3){
            (Ext.getCmp('btnStatus')) ? Ext.getCmp('btnStatus').enable() : '';//查询状态
        }
        if(charge_type == 5){
            (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
            (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//编辑
        }
    }
    else if (status == 1) {
        (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
        (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
        (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
        (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').enable() : '';//销审
        if(charge_type == 5){
            (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
            (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//编辑
        }
    }
    else if (status == -1) {
        (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').enable() : '';//删除
        (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').enable() : '';//审核
        (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
        (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
        if(charge_type == 5){
            (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
        }
    }
}

//选择行
var sm_service = new Ext.grid.CheckboxSelectionModel({
    // singleSelect: false,
    listeners: {
        'selectionchange': function (data) {
            if (data.getCount()) {
                /*crow = data.selections.itemAt(0).data;
                var orgroot = crow._orgroot;
                if(crow.charge_type == 1){
                    getPayType.load({params:{orgcode:orgroot}});
                    getPayChannel.load({params:{orgcode:orgroot,pay_type:crow.pay_type}});
                }
                getPayName.load({params:{orgcode:orgroot}});
                btnStatus(crow._status,crow.charge_type);*/
                var new_rows = grid_service.getSelectionModel().getSelections();
                if(new_rows.length > 1){
                    (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
                    (Ext.getCmp('btnBack')) ? Ext.getCmp('btnBack').disable() : '';//编辑
                    (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
                    (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
                    (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
                    (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审

                    (Ext.getCmp('batchbtn')) ? Ext.getCmp('batchbtn').enable() : '';//批量审核
                }else{
                    crow = data.selections.itemAt(0).data;
                    var orgroot = crow._orgroot;
                    if(crow.charge_type == 1){
                        getPayType.load({params:{orgcode:orgroot}});
                        getPayChannel.load({params:{orgcode:orgroot,pay_type:crow.pay_type}});
                    }
                    getPayName.load({params:{orgcode:orgroot}});
                    btnStatus(crow._status,crow.charge_type);
                    if(new_rows.length == 1){
                        (Ext.getCmp('batchbtn')) ? Ext.getCmp('batchbtn').enable() : '';//批量审核
                    }else {
                        (Ext.getCmp('batchbtn')) ? Ext.getCmp('batchbtn').disable() : '';//批量审核
                    }
                }
            } else {
                (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
                (Ext.getCmp('btnBack')) ? Ext.getCmp('btnBack').disable() : '';//编辑
                (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
                (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
                (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
                (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
            }
        }
    }
});

//定义列表
var cm_service = new Ext.grid.ColumnModel([
    sm_service,
    // new Ext.grid.RowNumberer({'header': '序号', width: 35}),
    {header: '单号', dataIndex: 'no', sortable: true, width: 150},
    {header: '顶级机构', dataIndex: 'orgroot', sortable: true, width: 150},
    {header: '顶级机构编码', dataIndex: '_orgroot', sortable: true, width: 80},
    {header: '机构', dataIndex: 'org_name', sortable: true, width: 150},
    {header: '机构编码', dataIndex: 'orgcode', sortable: true, width: 100},
    {header: '机构运营商', dataIndex: 'name', sortable: true, width: 220},
    {header: '申请时间', dataIndex: 'app_time', sortable: true, width: 150},
    {header: '充值性质', dataIndex: '_charge_type', sortable: true, width: 80},
    {header: '状态', dataIndex: 'status', sortable: true, width: 60},
    {header: '到账金额', dataIndex: 'arrival_money', sortable: true, width: 100},
    {header: '充值金额', dataIndex: 'money', sortable: true, width: 100},
    {header: '返利性质', dataIndex: 'rebate_type', sortable: true, width: 100},
    {header: '收款方手续费', dataIndex: 'payee_fee_ratio', sortable: true, width: 100},
    {header: '付款方手续费', dataIndex: 'payer_fee_ratio', sortable: true, width: 100},
    {header: '支付方式', dataIndex: '_pay_type', sortable: true, width: 110},
    {header: '渠道', dataIndex: 'pay_channel', sortable: true, width: 110},
    {header: '付款单号', dataIndex: 'pay_no', sortable: true, width: 160},
    {header: '付款公司', dataIndex: 'pay_name', sortable: true, width: 150},
    {header: '在线支付状态', dataIndex: 'pay_status', sortable: true, width: 110},
    {header: '在线支付凭据号', dataIndex: 'prepaysn', sortable: true, width: 110},
    {header: '数据来源', dataIndex: 'data_from', sortable: true, width: 90},
    {header: '创建人', dataIndex: 'true_name', sortable: true, width: 110},
    {header:'完成时间',dataIndex:'audit_time',sortable:true,width:150},
    {header:'最后修改人',dataIndex:'last_operator',sortable:true,width:110},
    {header:'最后修改时间',dataIndex:'updatetime',sortable:true,width:150},
    {header:'测试机构',dataIndex:'is_test',sortable:true,width:80},
    {header: '备注/内', dataIndex: 'remark', sortable: true, width: 150},
    {header: '备注/外', dataIndex: 'remark_work', sortable: true, width: 150},
]);

var pageTool = new Ext.PagingToolbar({
    plugins: new Ext.ux.plugin.PagingToolbarResizer,
    pageSize: pagesize,
    displayInfo: true,
    displayMsg: '当前记录数: {0} - {1} 总记录数: {2}',
    emptyMsg: '没有符合条件的记录',
    store: store_main
});

//创建grid
var grid_service = new Ext.grid.GridPanel({
    loadMask: true,
    enableColumnMove: false,
    enableColumnResize: true,
    title: '充值申请',
    stripeRows: true,//斑马颜色
    autoScroll: true,
    store: store_main,
    cm: cm_service,
    sm: sm_service,
    tbar: [],
    //分页
    bbar: pageTool,
});

/**
 * 初始化表单控件
 * @param status
 */
function initFormInput(status){
    if(status == '1'){
        Ext.getCmp('org_id').disable();
        Ext.getCmp('app_time').disable();
        Ext.getCmp('money').disable();
        Ext.getCmp('payType').disable();
        Ext.getCmp('charge_type').disable();
        Ext.getCmp('pay_channel').disable();
        Ext.getCmp('pay_no').disable();
        Ext.getCmp('pay_name').disable();
        Ext.getCmp('arrival_money').disable();
        Ext.getCmp('payee_fee_ratio').disable();
        Ext.getCmp('payer_fee_ratio').disable();
    }else{
        Ext.getCmp('org_id').enable();
        Ext.getCmp('app_time').enable();
        Ext.getCmp('money').enable();
        Ext.getCmp('payType').enable();
        Ext.getCmp('charge_type').enable();
        Ext.getCmp('pay_channel').enable();
        Ext.getCmp('pay_no').enable();
        Ext.getCmp('pay_name').enable();
        Ext.getCmp('arrival_money').enable();
        Ext.getCmp('payee_fee_ratio').enable();
        Ext.getCmp('payer_fee_ratio').enable();
        //判断当前支付方式是否可用
        var payTypeFlag = false;
        getPayType.each(function (record) {
            if(record.get('key') == crow.pay_type){
                payTypeFlag = true;
            }
        });
        // if(!payTypeFlag){
        //     Ext.getCmp('payType').setValue('');
        // }
    }
}

/**
 * 编辑
 */
function showEdit() {
    showAdd(crow.id);
    //设置机构运营商
    // setOperators(crow.org_id);
    Ext.getCmp('operators').setValue('机构运营商：'+crow.name);
    Ext.getCmp('operators_name').setValue(crow.name);
    Ext.getCmp('operator_id').setValue(crow.operator_id);
    Ext.getCmp('no').setValue(crow.no);
}

/**
 * 删除
 */
function showDel() {
    var sm = grid_service.getSelectionModel();
    var data = sm.getSelections();
    var ids = '';
    for (var i = 0; i < data.length; i++) {
        (ids) ? ids = ids + ',' : '';
        ids += data[i].get("id");
    }
    if (ids.indexOf(',') == -1) {
        Ext.MessageBox.confirm('删除', '本操作不可恢复，确定要删除吗?', function showResult(btn) {
            if (btn == 'yes') {
                Ext.MessageBox.show({
                    title: "等待",
                    msg: "正在删除，请稍等...",
                    progress: true,
                    width: 300,
                    wait: true,
                    waitConfig: {interval: 300},
                    closable: true
                });
                Ext.Ajax.request({
                    url: '../inside.php?t=json&m=oil_card_main&f=delete',
                    method: 'post',
                    params: {id: ids},
                    success: function (resp, opts) {
                        var result = Ext.decode(resp.responseText);
                        Ext.MessageBox.alert('提示', result.msg);
                        if (result.success != false) {
                            (Ext.getCmp('btnDel')) ? Ext.getCmp('btnDel').disable() : '';//删除
                            (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
                            store_main.removeAll();
                            store_main.load();
                        }
                    },
                    failure: function (resp, opts) {
                        Ext.MessageBox.alert('提示', Ext.decode(resp.responseText).msg);
                    }
                });
            }
        });
    } else {
        Ext.MessageBox.alert('提示', '每次只能选择一行进行删除');
    }

}

function btnInit() {
    (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
    (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
    (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
    (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
    (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
}

/**
 * 下载返利充值单模板
 */
function downTemp() {
    Ext.MessageBox.confirm('返利充值单模板下载', '确认下载?', function showResult(btn) {
        if (btn == 'yes') {
            window.location.href = '/download/templates/batchFanliCharge.xlsx';
        }
    });
}

/**
 * 导入返利充值单模板
 */
function importData() {
    var form = new Ext.form.FormPanel({
        baseCls: 'x-plain', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
        items: [
            {
                xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
                id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
                anchor: '100%'
            }
        ]
    });
    var importData  = new Ext.Window({
        title: '导入返利充值单', width: 400, height: 105, minWidth: 300, minHeight: 100, modal: true,
        layout: 'fit', plain: true, bodyStyle: 'padding:5px;', buttonAlign: 'center', items: form,
        buttons: [{
            text: '导入',
            handler: function () {
                if (form.form.isValid()) {
                    if (Ext.getCmp('userfile').getValue() == '') {
                        Ext.Msg.alert('系统提示', '请选择你要上传的文件');
                        return;
                    }
                    form.getForm().submit({
                        url: '/inside.php?t=json&m=oil_account_money_charge&f=exportExcel',
                        success: function (form, action) {
                            store_main.removeAll();
                            store_main.load();
                            Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).msg);
                            importData.close();
                        },
                        failure: function (form, action) {
                            Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).msg);
                        }
                    })
                }
            }
        }, {
            text: '关闭',
            handler: function () {
                importData.close();
            }
        }]
    });
    importData.show();
}

/**
 * 批量审核返利充值单模板
 */
function batchAudit() {
    Ext.MessageBox.confirm('导出', "确定要批量通过吗？", function showResult(btn) {
            if (btn == 'yes') {
                var ids = "";
                var new_rows = grid_service.getSelectionModel().getSelections();
                for (var i = 0; i < new_rows.length; i++) {
                    if (ids === '') {
                        ids = new_rows[i].get('id');
                    } else {
                        ids += ',' + new_rows[i].get('id');
                    }
                }
                Ext.Ajax.request({
                    url: '../inside.php?t=json&m=' + controlName + '&f=batchAudit',
                    method: 'post',
                    params: {ids: ids},
                    success: function sFn(response, options) {
                        store_main.reload();//刷新页面
                        var data = Ext.decode(response.responseText);
                        Ext.Msg.alert('系统提示', data.msg);
                    }
                });
            }
        }
    );
}

/**
 * 删除 操作方法
 * url  AJAX请求PHP的方法 noticeDelete=>删除
 */
function customerExecute(url) {
    var sm = grid_service.getSelectionModel();
    var data = sm.getSelections();
    var card_no = data[0].get('no');
    var type = '';
    var str = '';
    if (url == 'delete') {
        type = '删除';
        str = '删除之后单号[' + card_no + ']将不再显示，确定要删除？';
    }
    else if (url == 'cardAudit') {
        type = '审核';
        ids = data[0].get("id");
        charge_money = data[0].get("money");
        charge_type = data[0].get("charge_type");
        console.log(charge_money,charge_type);
        if(charge_money < 0 && charge_type == 1){
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=' + controlName + '&f=checkBalance',
                method: 'post',
                params: {ids: ids},
                success: function sFn(response, options) {
                    var data = Ext.decode(response.responseText);
                    if(data.code != 0)
                    {
                        str = data.msg;
                    }else{
                        str = '确定要审核通过单号[' + card_no + ']？';
                    }
                    Ext.MessageBox.confirm('' + type + '', str, function showResult(btn) {
                        if (btn == 'yes') {
                            Ext.Ajax.request({
                                url: '../inside.php?t=json&m=' + controlName + '&f=' + url,
                                method: 'post',
                                params: {ids: ids},
                                success: function sFn(response, options) {
                                    store_main.reload();//刷新页面
                                    var data = Ext.decode(response.responseText);
                                    if (typeof data.status !== undefined) {
                                        btnStatus(data.status);
                                    }
                                    Ext.Msg.alert('系统提示', data.msg);
                                }
                            });
                        }
                    })
                }
            });
            return;
        }else{
            console.log('正充值');
            str = '确定要审核通过单号[' + card_no + ']？';
        }

    }
    else if (url == 'cardReject') {
        type = '驳回';
        str = '确定要驳回单号[' + card_no + ']？';
    }
    else if (url == 'cardUnAudit') {
        type = '销审';
        str = '确定要销审单号[' + card_no + ']？';
    }
    else if (url == 'checkStatus') {
        type = '查询授信状态';
        str = '确定要查询单号[' + card_no + ']授信状态？';
        url = "checkCreditChargeStatus";
    }
    var ids = '';
    ids = data[0].get("id");
    Ext.MessageBox.confirm('' + type + '', str, function showResult(btn) {
        if (btn == 'yes') {
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=' + controlName + '&f=' + url,
                method: 'post',
                params: {ids: ids},
                success: function sFn(response, options) {
                    store_main.reload();//刷新页面
                    var data = Ext.decode(response.responseText);
                    if (typeof data.status !== undefined) {
                        btnStatus(data.status);
                    }
                    Ext.Msg.alert('系统提示', data.msg);
                }
            });
        }
    });
}