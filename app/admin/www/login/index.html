<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<title>中启行系统登录</title>

<link rel="stylesheet" type="text/css" href="../widgets/extjs/resources/css/ext-all.css" />
<link href="../public/fonts/font.css" rel="stylesheet" type="text/css" />
<link href="../public/css/login.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../widgets/extjs/adapter/ext/ext-base.js"></script>
<script type="text/javascript" src="../widgets/extjs/ext-all.js"></script>
<script type="text/javascript" src="../public/js/jquery.min.js"></script>
<script type="text/javascript"  src="../public/js/three.js"></script>
<script type="text/javascript"  src="../public/js/Projector.js"></script>
<script type="text/javascript"  src="../public/js/CanvasRenderer.js"></script>

<script type="text/javascript"  src="../public/js/stats.min.js"></script>
<script type="text/javascript"  src="../public/js/Array.forEach.polyfill.min.js"></script>
<script type="text/javascript"  src="../public/js/jquery.msgBox.min.js"></script>
<script type="text/javascript">
    var SEPARATION = 100, AMOUNTX = 50, AMOUNTY = 50;
    var container, stats;
    var camera, scene, renderer;

    var particles, particle, count = 0;

    var mouseX = 0, mouseY = 0;

    var windowHalfX = window.innerWidth / 2;
    var windowHalfY = window.innerHeight / 2;
    function init() {
        var lizi = document.getElementById('indexLizi');
        container = document.createElement( 'div' );
        container.style.position= "relative";
        container.style.top= "200px";
        lizi.appendChild( container );

        camera = new THREE.PerspectiveCamera( 75, window.innerWidth / window.innerHeight, 1, 10000 );
        camera.position.z = 1000;

        scene = new THREE.Scene();

        particles = new Array();

        var PI2 = Math.PI * 2;
        var material = new THREE.SpriteCanvasMaterial( {
            color: 0x0099fb,
            program: function ( context ) {

                context.beginPath();
                context.arc( 0, 0, 0.5, 0, PI2, true );
                context.fill();

            }
        } );

        var i = 0;

        for ( var ix = 0; ix < AMOUNTX; ix ++ ) {

            for ( var iy = 0; iy < AMOUNTY; iy ++ ) {

                particle = particles[ i ++ ] = new THREE.Sprite( material );
                particle.position.x = ix * SEPARATION - ( ( AMOUNTX * SEPARATION ) / 2 );
                particle.position.z = iy * SEPARATION - ( ( AMOUNTY * SEPARATION ) / 2 );
                scene.add( particle );

            }

        }

        renderer = new THREE.CanvasRenderer({
         // 在 css 中设置背景色透明显示渐变色
          alpha: true,
          antialias: true
        });
        renderer.setPixelRatio( window.devicePixelRatio );
        renderer.setSize( window.innerWidth, window.innerHeight );
        container.appendChild( renderer.domElement );
        document.addEventListener( 'mousemove', onDocumentMouseMove, false );
        window.addEventListener( 'resize', onWindowResize, false );
    }

    function onWindowResize() {

        windowHalfX = window.innerWidth / 2;
        windowHalfY = window.innerHeight / 2;

        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();

        renderer.setSize( window.innerWidth, window.innerHeight );

    }


    function onDocumentMouseMove( event ) {
        mouseX = event.clientX - windowHalfX;
        mouseY = -500;
    }


    function animate() {
        requestAnimationFrame( animate );
        render();
    }

    function render() {

        camera.position.x += ( mouseX - camera.position.x ) * .05;
        camera.position.y += ( 500 - camera.position.y ) * .05;
        camera.lookAt( scene.position );

        var i = 0;

        for ( var ix = 0; ix < AMOUNTX; ix ++ ) {

            for ( var iy = 0; iy < AMOUNTY; iy ++ ) {

                particle = particles[ i++ ];
                particle.position.y = ( Math.sin( ( ix + count ) * 0.3 ) * 50 ) +
                    ( Math.sin( ( iy + count ) * 0.5 ) * 50 );
                particle.scale.x = particle.scale.y = ( Math.sin( ( ix + count ) * 0.3 ) + 1 ) * 4 +
                    ( Math.sin( ( iy + count ) * 0.5 ) + 1 ) * 4;

            }

        }

        renderer.render( scene, camera );

        count += 0.1;

    }
    window.onload = function(){
        $('.pubLog').addClass('hide');
        $('.bgStyle').removeClass('hide')
        if(location.href.indexOf('hbgszyht')>-1){//中油汇通
            $('body').attr('id','zyht')
            $('#zyht_logo').removeClass('hide')
            $('.title img').css('width','60px')
            // 设置背景图片
            document.title = '中油高速通运营系统'
            $('.logoTitle').text('中油高速通运营系统')
        }else{
            $('#g7_logo').removeClass('hide');
            $('.logoTitle').text('中启行运营系统')
            init();
            animate();
        }
    }

</script>
</head>
<style type="text/css">
    .hide{
        display: none !important;
    }
    .loading{
        position: absolute;
        top:0;
        left:0;
        z-index: 1002;
        right:0;
        bottom:0;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(0,0,0,.7);
        color:#fff;
        font-size: 16px;
        visibility: hidden;
    }
    .msgBox-testArea{
        position: absolute;
        z-index: 1003;
    }
    label input[type=checkbox].checkbox {
        visibility: hidden;
         position: absolute;
    }

    label input[type=checkbox].checkbox:checked+span:before {
        color: #fff;
    }
    label input[type=checkbox].checkbox:checked+span:before {
        content: "\f00c";
    }
    label input[type=checkbox].checkbox+span:before {
        content: "\a0";
    }
    label input[type=checkbox].checkbox+span:before, label input[type=radio].radiobox+span:before {
        font-family: "FontAwesome";
        font-size: 12px;
        border-radius: 0;
        content: "\a0";
        display: inline-block;
        text-align: center;
        vertical-align: middle;
        padding: 1px;
        height: 12px;
        line-height: 12px;
        min-width: 12px;
        margin-right: 5px;
        border: 1px solid rgba(193,193,193,0.4);
        background-color: transparent;
        font-weight: 400;
        margin-top: -1px;
    }
    input[type=checkbox].checkbox+span{
        box-sizing: content-box!important;
    -webkit-box-sizing: content-box!important;
    -moz-box-sizing: content-box!important;
    }
</style>
<body>
<div class="msgBox-testArea"></div>
<div class="loading" id="loading"><img src="../public/image/login/oval.svg" />&nbsp;&nbsp;正在验证登录信息...</div>
<div class="index bgStyle hide">
    <div class="wrap">
        <div class="login_box">
            <div class="title">
                <img class="pubLog hide" id="g7_logo" src="../public/image/login/login_logo.png" />
                <img class="pubLog hide" id="zyht_logo" src="../public/image/login/login_zyht_logo.png" />
                <span class="font logoTitle">中启行运营系统</span>
            </div>
            <div class="form">
                <form>
                    <div class="control">
                        <input id="userName" placeholder="请输入用户名" type="text" class="input_text" size="30" />
                    </div>
                    <div class="control">
                    <input id="passWord" placeholder="请输入密码" type="password" class="input_text" size="30"  />
                    </div>
                    <div class="control control_vcode">
                        <input id="valCode" placeholder="请输入验证码" type="text" class="input_text" size="10"  />
                        <img id="valImg" height="42" src="/inside.php?t=json&m=login&f=generateCaptcha" onclick="this.src='/inside.php?t=json&m=login&f=generateCaptcha&'+Math.random();" />
                    </div>
                    <div class="control checkbox">
                        <label>
                            <input id="checkBox" type="checkbox" class="checkbox">
                            <span langlanguage="rememberPassword">保持登录</span>
                        </label>
                    </div>

                    <input id="login" type="button" class="tj_but" value="登录"/>
                </form>
            </div>
        </div>
    </div>
</div>
<div id="indexLizi"></div>
</body>
</html>
<script type="text/javascript" src="login.js"></script><!-- 登录的JS -->




