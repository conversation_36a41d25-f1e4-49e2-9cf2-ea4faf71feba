var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 50,
	items  : 
	[
	     {
			xtype : 'compositefield',
			items : 
			[
				{
					xtype: 'displayfield',
					value: '公司名称：'
				},
				{
					xtype : 'textfield',
					id    : 'company_nameLk',
					name  : 'company_nameLk',
					width : 200
				},
                {
                    xtype: 'displayfield',
                    value: '姓名：'
                },
                {
                    xtype : 'textfield',
                    id    : 'nameLk',
                    name  : 'nameLk',
                    width : 200
                },
                {
                    xtype: 'displayfield',
                    value: '用油模式：'
                },
                {
                    xtype : 'textfield',
                    id    : 'oil_modeLk',
                    name  : 'oil_modeLk',
                    width : 200
                },
                {
                    xtype: 'displayfield',
                    value: '地区：'
                },
                {
                    xtype : 'textfield',
                    id    : 'areaLk',
                    name  : 'areaLk',
                    width : 200
                },
				{
					xtype: 'displayfield',
					value: '手机号：'
				},
				{
					xtype : 'textfield',
					id    : 'mobile',
					name  : 'mobile',
					width : 100
				},
				{
					xtype:'button',
					text:'查询',
					style : 'padding-left : 10px;',
					handler: function()
					{
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
					}
				},
				{
					xtype: 'button',
					text: '重置',
					style: 'padding-left : 10px;',
					handler: function () {
						top_panel.getForm().reset();
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
						// btnInit();
					}
				},
			]
		 }	
	]
});