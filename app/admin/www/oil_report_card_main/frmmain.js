Ext.onReady(function(){	
	Ext.Ajax.request({
		url:'../inside.php?t=json&m=login&f=getConfig',
		method:'post',
		success: function(resp,opts) { 
			var respText = Ext.util.JSON.decode(resp.responseText); 
			if(!respText.id) {
				window.location.href = '../login/';
			}
			else {				
				var main_panel = new Ext.TabPanel(
						{xtype:"tabpanel",
						region:"center",
						items:[grid_service]
				});
				main_panel.setActiveTab(grid_service);
				
				store_main.load();
				
				new Ext.Viewport(
					{enableTabScroll:true,
						layout:"border",
						items:[
						       top_panel,
						       main_panel,
						       ]}
				);
				editSource('oil_org_bill',grid_service);
			}
		}
	});
});