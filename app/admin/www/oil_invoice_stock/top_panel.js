var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 50,
	items  : 
	[
	     {
			xtype : 'compositefield',
			items : 
			[
				{
                    xtype: 'displayfield',
                    value: '购方名称：'
                },
                {
                    xtype: 'combo',
                    width: 220,
                    id   : 'card_operators_id',
                    hiddenName : "card_operators_id",
                    editable: false,
                    emptyText : '全部',
                    triggerAction:'all',
                    displayField: 'company_name',
                    valueField: 'id',
                    store  : new Ext.data.Store({url: '../inside.php?t=json&m=oil_operators&f=getList&limit=1000',
                        reader: new Ext.data.JsonReader({
                            totalProperty:'data.total',
                            root:'data.data'
                        },['id', 'company_name'])})
                },


                {
                    xtype: 'displayfield',
                    value: '类目：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype: 'combo',
                    width: 100,
                    id   : 'commodityType',
                    hiddenName : "commodityType",
                    editable: false,
                    emptyText : '全部',
                    triggerAction:'all',
                    displayField: 'name',
                    valueField: 'id',
                    store  : new Ext.data.Store({url: '../inside.php?t=json&m=oil_dict&f=getCommodityType',
                        reader: new Ext.data.JsonReader({
                            root:'data'
                        },['id', 'name'])})
                },

                {
                    xtype: 'displayfield',
                    value: '服务名称：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype : 'textfield',
                    id    : 'commodityNameIn',
                    name  : 'commodityNameIn',
                    width : 100
                },
                {
                    xtype:'button',
                    text:'多选',
                    listeners:{
                        'click':function () {
                            show_search_win('请手动输入服务名称，以回车换行：','commodityNameIn', function () {
                                main_store.removeAll();
                                main_store.load();
                            });//search_win.js 里
                        }
                    }
                },

                {
                    xtype: 'displayfield',
                    value: '规格型号：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype : 'textfield',
                    id    : 'specificationModelIn',
                    name  : 'specificationModelIn',
                    width : 100
                },
                {
                    xtype:'button',
                    text:'多选',
                    listeners:{
                        'click':function () {
                            show_search_win('请手动输入规格型号，以回车换行：','specificationModelIn', function () {
                                main_store.removeAll();
                                main_store.load();
                            });//search_win.js 里
                        }
                    }
                },

				{
					xtype:'button',
					text:'查询',
					style : 'padding-left : 10px;',
					handler: function()
					{
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
					}
				},
				{
					xtype: 'button',
					text: '重置',
					style: 'padding-left : 10px;',
					handler: function () {
						top_panel.getForm().reset();
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
						btnInit();
					}
				},
			]
		 }	
	]
});