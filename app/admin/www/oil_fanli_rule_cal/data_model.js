/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_fanli_rule_cal';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","no","audit_status","audit_status_txt","fanli_total_money","fanli_total_jifen","cal_date","log_num","source","source_txt"]
    ),
});

//匹配规则
var matchRuleList = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getMatchList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        ["object_txt","rule_no","rule_name","expire_time"]
    ),
});

//返利明细
var fanliDetailList = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getDetailList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "use_org","use_oil_com","use_oil_type","use_main","fanli_money","fanli_jifen","use_region"
        ]
    ),
});

//操作日志
var detailLogList = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl('oil_sys_operator_logs','getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data',
            root: 'data'
        },
        [
            "operator_id","operator_name","operator_content","complete_status","remark","createtime","id","operator_time"
        ]
    ),
});
