var date = new Date();
var monthStart = new Date(date.getFullYear(), date.getMonth(), 1);

var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 50,
	items  : 
	[
	     {
			xtype : 'compositefield',
			items : 
			[
                {
                    xtype: 'displayfield',
                    value: '计算周期：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype: "datefield",
                    width: 100,
                    value: monthStart,
                    format: 'Y-m-d',
                    name: 'expire_cal_start',
                },
                {
                    xtype: 'displayfield',
                    value: '~'
                },
                {
                    xtype: "datefield",
                    width: 100,
                    value: date,
                    format: 'Y-m-d',
                    name: 'expire_cal_end',
                },
                {
                    xtype: 'displayfield',
                    value: '计算状态：'
                },
                {
                    xtype: 'combo',
                    width: 80,
                    hiddenName: 'status',
                    mode: 'local',
                    triggerAction: 'all',
                    displayField: 'name',
                    valueField: 'value',
                    emptyText: '请选择..',
                    store: new Ext.data.SimpleStore({
                        fields: ['name', 'value'],
                        data: [['全部', ''],['计算中', 3], ['完成', 1],['失败', 2]]
                    })
                },
                {
                    xtype: 'displayfield',
                    value: '审核状态：'
                },
                {
                    xtype: 'combo',
                    width: 80,
                    hiddenName: 'audit_status',
                    mode: 'local',
                    triggerAction: 'all',
                    displayField: 'name',
                    valueField: 'value',
                    emptyText: '请选择..',
                    store: new Ext.data.SimpleStore({
                        fields: ['name', 'value'],
                        data: [['全部', ''],['已驳回', 2], ['待审核', 3],['已审核', 1]]
                    })
                },
                {
                    xtype: 'displayfield',
                    value: '返利单号：'
                },
                {
                    xtype : 'textfield',
                    id    : 'fanli_no',
                    name  : 'fanli_noLk',
                    width : 200
                },
				{
					xtype:'button',
					text:'查询',
					style : 'padding-left : 10px;',
					handler: function()
					{
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
					}
				},
				{
					xtype: 'button',
					text: '重置',
					style: 'padding-left : 10px;',
					handler: function () {
						top_panel.getForm().reset();
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
						btnInit();
					}
				},
			]
		 }	
	]
});