/**
 * 根据状态判断按钮是否可用
 * @param status
 */
function btnStatus(status){
    (Ext.getCmp('btnCardShow')) ? Ext.getCmp('btnCardShow').enable() : '';//查看
	(Ext.getCmp('btnUpdate')) ? Ext.getCmp('btnUpdate').enable() : '';//编辑
	if(status == 0){
		(Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').enable() : '';//删除
		(Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').enable() : '';//审核
		(Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').enable() : '';//驳回
		(Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
        (Ext.getCmp('btnCardAdd')) ? Ext.getCmp('btnCardAdd').disable() : '';//卡号维护
		(Ext.getCmp('btnExpress')) ? Ext.getCmp('btnExpress').disable() : '';//快递信息
		(Ext.getCmp('btnFileUrl')) ? Ext.getCmp('btnFileUrl').enable() : '';//行驶证
	}else if(status == 1){
		(Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
		(Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
		(Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
		(Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').enable() : '';//销审
        (Ext.getCmp('btnCardAdd')) ? Ext.getCmp('btnCardAdd').enable() : '';//卡号维护
		(Ext.getCmp('btnExpress')) ? Ext.getCmp('btnExpress').disable() : '';//快递信息
		(Ext.getCmp('btnFileUrl')) ? Ext.getCmp('btnFileUrl').enable() : '';//行驶证
	}else if(status == -1){
		(Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').enable() : '';//删除
		(Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').enable() : '';//审核
		(Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
		(Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
        (Ext.getCmp('btnCardAdd')) ? Ext.getCmp('btnCardAdd').disable() : '';//卡号维护
		(Ext.getCmp('btnExpress')) ? Ext.getCmp('btnExpress').disable() : '';//快递信息
		(Ext.getCmp('btnFileUrl')) ? Ext.getCmp('btnFileUrl').enable() : '';//行驶证
	}else if(status == 2){//已绑主卡
        (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
        (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
        (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
        (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
        (Ext.getCmp('btnCardAdd')) ? Ext.getCmp('btnCardAdd').disable() : '';//卡号维护
		(Ext.getCmp('btnExpress')) ? Ext.getCmp('btnExpress').enable() : '';//快递信息
		(Ext.getCmp('btnFileUrl')) ? Ext.getCmp('btnFileUrl').enable() : '';//行驶证
    }else if(status == 3){//完成
		(Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
		(Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
		(Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
		(Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
		(Ext.getCmp('btnCardAdd')) ? Ext.getCmp('btnCardAdd').disable() : '';//卡号维护
		(Ext.getCmp('btnExpress')) ? Ext.getCmp('btnExpress').enable() : '';//快递信息
		(Ext.getCmp('btnFileUrl')) ? Ext.getCmp('btnFileUrl').enable() : '';//行驶证
	}
}

var _sm =new Ext.grid.RowSelectionModel({
	singleSelect: true,
	listeners: {
		selectionchange: function(data) {
			if (data.getCount()){
				var sm   = grid_list.getSelectionModel();
				var info = sm.getSelections();
				var status = info[0].get('status');
				btnStatus(status);
                getOrgContact.baseParams = {org_id: info[0].get('org_id_big'),_export:1};
                getOrgContact.load();

                getOrgAddr.baseParams = {org_id: info[0].get('org_id_big'),_export:1};
                getOrgAddr.load();
			}else{
                (Ext.getCmp('btnUpdate')) ? Ext.getCmp('btnUpdate').disable() : '';//编辑
                (Ext.getCmp('btnCardAdd')) ? Ext.getCmp('btnCardAdd').disable() : '';//卡号维护
				(Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
				(Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
				(Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
                (Ext.getCmp('btnCardShow')) ? Ext.getCmp('btnCardShow').disable() : '';//查看
                (Ext.getCmp('btnExpress')) ? Ext.getCmp('btnExpress').disable() : '';//快递信息
            }
	    }
    }
});

var pageTool = new Ext.PagingToolbar({
	plugins: new Ext.ux.plugin.PagingToolbarResizer,
	pageSize: pagesize,
	displayInfo : true,
	displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
	emptyMsg : '没有符合条件的记录',
	store : cardStore
});

//公告列表 开始
var grid_list = new Ext.grid.GridPanel({
	title	 :'开卡申请',
	region	 : 'center',
	loadMask : true,
	cm : new Ext.grid.ColumnModel([
		{header : '单号',dataIndex : 'no',width : 120},
        {header : '卡供应商',dataIndex:'supplyer_name',sortable:true,width:80},
        {header : '创建机构',dataIndex : 'create_org_name',width : 150},
		{header : '机构运营商',dataIndex : 'org_operators_name',width : 150},
        {header : '返利机构',dataIndex : 'fanli_org_name',width : 150},
		{header : '油品负责人',dataIndex : 'org_contact_name',width : 80},
        {header : '负责人电话',dataIndex : 'org_contact_mobile',width : 100},
        {header : '收卡人',dataIndex : 'org_addr_name',width : 80},
        {header : '收卡人手机',dataIndex : 'org_addr_mobile',width : 100},
		{header : '收卡地址',dataIndex : 'org_addr_address',width : 180},
        {header : '快递公司',dataIndex : 'express_com',width : 120},
        {header : '快递单号',dataIndex : 'express_no',width : 150},
		{header : '申请时间',dataIndex : 'apply_time',width : 130},
        {header : '审核时间',dataIndex : 'audit_time',width : 130},
		{header : '油卡类型',dataIndex : 'oil_com_name',width : 80},
		{header : '卡来源',dataIndex : '_card_from',width : 80},
		{header : '积分可用地区',dataIndex : 'province',width : 100},
		{header : '协议返利',dataIndex : 'fanli_rate',width : 80,align:'right'},
		{header : '开卡数量',dataIndex : 'num',width : 80, align:'center'},
		{header : '车辆类型',dataIndex : 'truck_type',width : 80},
		{header : '组织',dataIndex : 'gsporg_name',width : 120},
		{header : '业务负责人',dataIndex : 'user_name',width : 80},
        {header : '专属客服',dataIndex : 'exclusive_custom',width : 80},
		{header : '数据来源',dataIndex : 'data_from',width : 80,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
			var ss = record.data.data_from;
			if(ss == 1)
			{
				return 'GSP';
			}
			else if(ss == 2)
			{
				return 'WEB';
			}
			else if(ss == 3)
			{
				return 'APP';
			}
			else
			{
				return '';
			}
	    }},
		{header : '状态',dataIndex : 'status',width : 80,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
			var ss = record.data.status;
			if(ss == 0)
			{
				return '未审核';
			}
			else if(ss == 1)
			{
				return '已审核';
			}
			else if(ss == -1)
			{
				return '驳回';
			}
			else if(ss == 2)
			{
				return '已绑主卡';
			}
			else if(ss == 3)
			{
				return '完成';
			}
	    }},
		{header : '完成人',dataIndex : 'complete_person',width : 80},
		{header : '完成时间',dataIndex : 'complete_time',width : 120},
        {header : '是否办卡',dataIndex : 'is_done',width : 80,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
            var ss = record.data.status;
            if(ss == 0)
            {
                return '否';
            }
            else if(ss == 1)
            {
                return '是';
            }
        }},
		{header : '主卡号',dataIndex : 'main_no',width : 150},
		{header : '创建人',dataIndex : 'true_name',width : 80},
		{header : '最后修改人',dataIndex : 'last_operator',width : 80},
		{header : '最后更新时间',dataIndex : 'updatetime',width : 120},
		{header : '备注/内',dataIndex : 'remark',width : 100},
		{header : '备注/外',dataIndex : 'remark_work',width : 100},
		{header:'附件',dataIndex:'file_url',sortable:true,width:60,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
			var ss = record.data.file_url;
			if(ss)
			{
				return '<a href="'+ss+'"><img src="../public/skin/download.png" /></a>';
			} else {
				return '';
			}
		}},
	]),
	listeners: {
		"rowclick" : rowClick
	},
	store : cardStore,
	sm :_sm,
	bbar: pageTool,
	tbar : []	
});

//修改油卡类型的列宽
Ext.Ajax.request({
	url: '../inside.php?t=json&m=oil_card_main&f=getOilComWidth',
	method: 'post',
	params: {},
	success: function(response, options){
		var oil_com_index = false;
		grid_list.getColumnModel().getColumnsBy(function(columnConfig, index){
			if (columnConfig.dataIndex == 'oil_com_name') {
				oil_com_index = index;
				return true;
			}
		});
		if (oil_com_index !== false) {
			grid_list.getColumnModel().setColumnWidth(oil_com_index, Ext.decode(response.responseText).width);
		}
	}
});

//列表表格
var main_tab_panel = new Ext.TabPanel({
	xtype   : "tabpanel",
	region  : "center",
	id		: 'sale_firm_tab',
	items:
	[
		grid_list
	]
});
main_tab_panel.setActiveTab(grid_list);//设置特定的tab为活动面板


/**
* 删除 操作方法
* url  AJAX请求PHP的方法 noticeDelete=>删除 
*/
function customerExecute(url){
	var sm   = grid_list.getSelectionModel();
	var data = sm.getSelections();
	var card_no = data[0].get('no');
	var fanli_region = data[0].get('fanli_region');
	var type = '';
	var str  = '';

	if(url == 'delete')
	{
		type = '删除';
		str  = '删除之后单号['+card_no+']将不再显示，确定要删除？';
	}
	else if(url == 'cardAudit')
	{
		type = '审核';
		str  = '确定要审核通过单号['+card_no+']？';
	}
	else if(url == 'cardReject')
	{
		type = '驳回';
		str  = '确定要驳回单号['+card_no+']？';
	}
	else if(url == 'cardUnAudit')
	{
		type = '销审';
		str  = '确定要销审单号['+card_no+']？';
	}
	var ids  = '';
	ids = data[0].get("id");
	Ext.MessageBox.confirm(''+type+'', str, function showResult(btn){
		if (btn == 'yes'){
			Ext.Ajax.request({
				url:'../inside.php?t=json&m='+ controlName +'&f='+url,
				method:'post',
				params:{ids:ids, fanli_region: fanli_region},
				success: function (response,options){
                    var data = Ext.decode(response.responseText);
                    if(parseInt(data.code) == 0){
                        cardStore.reload();//刷新页面
                        if(typeof data.data.status !== undefined){
                            btnStatus(data.data.status);
                        }
                    }
                    var msg = parseInt(data.code) != 0 ? data.msg : data.data.msg;
                    Ext.Msg.alert('系统提示', msg);
				},
				failure:function (response,options){
                    Ext.Msg.alert('系统提示', Ext.decode(response.responseText).msg);
                }
			});
		}
	});	
}

//导出
function exportData() {
    var ids  = cardStore.data.keys;
    if(ids.length > 0){
    	var params = top_panel.getForm().getValues();
        params.apply_timeLe = params.apply_timeLe ? params.apply_timeLe + ' 23:59:59': '';
        params.complete_timeLe = params.complete_timeLe ? params.complete_timeLe + ' 23:59:59': '';

        Ext.Ajax.request({
            url: '../inside.php?t=json&m=oil_card_vice_app&f=export&_export=1',
            params: params,
            method: 'POST',
            success: function sFn(response,options){

				var url = Ext.decode(response.responseText).data.redirect_url;
				console.log(url);
				window.open(url);

                var msg = Ext.decode(response.responseText).msg;
                Ext.Msg.alert('提示',msg);
            }
        });
    }else{
    	Ext.Msg.alert('提示','没有数据需要导出');
	}
}


function exportDataApp() {
	var ids  = cardStore.data.keys;
	if(ids.length > 0){
		var params = top_panel.getForm().getValues();
		params.apply_timeLe = params.apply_timeLe ? params.apply_timeLe + ' 23:59:59': '';
		params.complete_timeLe = params.complete_timeLe ? params.complete_timeLe + ' 23:59:59': '';

		Ext.Ajax.request({
			url: '../inside.php?t=json&m=oil_card_vice_app&f=exportApp&_export=1',
			params: params,
			method: 'POST',
			success: function sFn(response,options){

				var url = Ext.decode(response.responseText).data.redirect_url;
				console.log(url);
				window.open(url);

				var msg = Ext.decode(response.responseText).msg;
				Ext.Msg.alert('提示',msg);
			}
		});
	}else{
		Ext.Msg.alert('提示','没有数据需要导出');
	}
}



function getParams(params) {
	var args = '&';
	for(var i in params){
		args += i+'='+params[i]+'&';
	}
	return args;
}

//Story #53376
//---创建表单及表单POST提交---开始
var extend = function (a, b) {
    var n;
    if (!a) {
        a = {};
    }
    for (n in b) {
        a[n] = b[n];
    }
    return a;
};

var css = function (el, styles) {
    extend(el.style, styles);
};

var createElement = function (tag, attribs, styles, parent) {
    var el = document.createElement(tag);
    if (attribs) {
        extend(el, attribs);
    }
    if (styles) {
        css(el, styles);
    }
    if (parent) {
        parent.appendChild(el);
    }
    return el;
}

var post = function (url, data) {
    var name,
        form;

    // create the form
    form = createElement('form', {
        method: 'post',
        action: url,
        enctype: 'multipart/form-data',
        target: '_blank'
    }, {
        display: 'none'
    }, document.body);

    // add the data
    for (name in data) {
        createElement('input', {
            type: 'hidden',
            name: name,
            value: data[name]
        }, null, form);
    }
console.log('222222222222')
    // submit
    form.submit();

    // clean up
    //discardElement(form);
}
//---创建表单及表单POST提交---结束


//导入并替换行驶证
function importAndUpdateAttachment() {

	var form = new Ext.form.FormPanel({
		baseCls: 'x-plain', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
		items: [
			{
				xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
				id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
				anchor: '100%'
			}
		]
	});
	var importAttachmentBoot = new Ext.Window({
		title: '行驶证 （支持zip、rar文件）',
		width: 400,
		height: 105,
		minWidth: 300,
		minHeight: 100,
		closeAction: 'destroy',
		modal: true,
		layout: 'fit',
		plain: true,
		bodyStyle: 'padding:5px;',
		buttonAlign: 'center',
		items: form,
		buttons: [{
			text: '导入',
			handler: function () {
				if (form.form.isValid()) {
					if (Ext.getCmp('userfile').getValue() === '') {
						Ext.Msg.alert('系统提示', '请选择你要上传的文件');
						return;
					}

					var sm   = grid_list.getSelectionModel();
					var data = sm.getSelections();

					form.getForm().submit({
						url: '../inside.php?t=json&m=oil_card_vice_app&f=importAndUpdateAttachment',
						success: function (form, action) {
							var r = Ext.decode(action.response.responseText).msg;
							Ext.MessageBox.alert('提示', r);
							importAttachmentBoot.destroy();
							main_store.removeAll();
							main_store.load();
						},
						failure: function (form, action) {
							var r = Ext.decode(action.response.responseText).msg;
							Ext.MessageBox.alert('提示', r);
						},
						params: {id: data[0].get('id')}
					})
				}
			}
		}, {
			text: '关闭',
			handler: function () {
				importAttachmentBoot.destroy();
			}
		}]
	});
	importAttachmentBoot.show();
}

function importAlter() {
	var sm   = grid_list.getSelectionModel();
	var data = sm.getSelections();
	var down_file_url = data[0].get('file_url');
	var alter_msg = '添加行驶证附件';
	if (typeof down_file_url == 'string' && down_file_url.length > 0) {
		alter_msg ='该申请已有行驶证附件,确定要替换吗?';
	}

	Ext.Msg.confirm('提示', alter_msg, function(btn){
		if(btn === 'yes'){
			importAndUpdateAttachment();
		}
	});

}
