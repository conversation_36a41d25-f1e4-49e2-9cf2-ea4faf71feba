/**
 * 切换地图类型
 * @param flag | 10高德地图，20百度地图
 */
function changeMapType(flag) {
    if(flag == 10){
        document.getElementById('frame1').src = './gaode_map.html';
    } else if(flag == 20){
        document.getElementById('frame1').src = './baidu_map.html';
    } else{
        Ext.Msg.alert('提示','未知的地图类型');
    }
}

/**
 * 油站地图标记
 */
function markStationMap() {
    var data   = oilStationGrid.getSelectionModel().getSelections()[0].data;

    var mapUrl = data.map_type != 20 ? "./gaode_map.html" : "./baidu_map.html";
    var panel2 = new Ext.Panel( {
        id:  "panel2",
        fitToFrame: true,
        height:document.body.clientHeight*0.9-47,
        layout: 'fit',
        html: '<iframe id="frame1" src='+mapUrl+' frameborder="0" width="100%" height="100%"></iframe>'
    });

    var stationMapForm = new Ext.form.FormPanel({
        buttonAlign: 'center',
        labelAlign: 'right',
        labelWidth: 60,
        bodyStyle: 'background-color: white;',
        frame: true,
        region: 'center',
        items: [
            {
                layout: "column",
                items: [
                    {
                        columnWidth: 0.26,
                        layout: "form",
                        items: {
                            xtype: 'fieldset',
                            title: '油站信息地图标记',
                            layout:'form',
                            autoHeight: true,
                            style:'margin:5px',
                            items: [
                                {
                                    xtype: 'combo',
                                    fieldLabel: "地图类型",
                                    width:180,
                                    id: 'map_type',
                                    name: 'map_type',
                                    hiddenName: 'map_type',
                                    editable:false,
                                    mode: 'local',
                                    triggerAction:'all',
                                    displayField: 'value',
                                    valueField: 'key',
                                    store: new Ext.data.SimpleStore({
                                        fields: ['key', 'value'],
                                        data: [['10', '高德地图'],['20', '百度地图']]
                                    }),
                                    listeners:{
                                        select:function(combo, record, index){
                                            var map_type = Ext.getCmp('map_type').getValue();
                                            //切换地图类型
                                            changeMapType(map_type);
                                        }
                                    }
                                },
                                {
                                    layout: "column",
                                    items: [
                                        {
                                            columnWidth: 0.85,
                                            layout: "form",
                                            items: {
                                                xtype: "textfield",
                                                fieldLabel: "关键字",
                                                id: 'keywords',
                                                name: 'keywords'
                                            }
                                        },
                                        {
                                            columnWidth: 0.15,
                                            layout: "form",
                                            items: {
                                                xtype: "button",
                                                text: "查询",
                                                id: 'searchData'
                                            }
                                        },
                                    ]
                                },
                                {
                                    xtype: "textfield",
                                    fieldLabel: "经度",
                                    id: 'lng',
                                    name: 'lng'
                                },
                                {
                                    xtype: "textfield",
                                    fieldLabel: "纬度",
                                    id: 'lat',
                                    name: 'lat'
                                },
                                {
                                    xtype: "textfield",
                                    fieldLabel: "油站别名",
                                    id: 'alias',
                                    name: 'alias'
                                },
                                {
                                    xtype: "textarea",
                                    width:185,
                                    height:60,
                                    fieldLabel: "详细地址",
                                    id: 'detail_address',
                                    name: 'detail_address'
                                },
                                {
                                    xtype: "textfield",
                                    fieldLabel: "联系电话",
                                    id: 'phone',
                                    name: 'phone'
                                },
                                {
                                    xtype: "button",
                                    text: "保存",
                                    cls:'button',
                                    width:250,
                                    handler: function () {
                                        if(!Ext.getCmp('lng').getValue() || !Ext.getCmp('lng').getValue()){
                                            Ext.Msg.alert('提示', '经度或纬度必填');
                                            return;
                                        }
                                        if(!Ext.getCmp('alias').getValue()){
                                            Ext.Msg.alert('提示', '油站别名必填');
                                            return;
                                        }
                                        var _form = stationMapForm.getForm();
                                        if (_form.isValid()) {
                                            _form.submit({
                                                url: '../inside.php?t=json&m=oil_station&f=update',
                                                waitMsg: 'Saving Data...',
                                                success: function (form, action) {
                                                    Ext.MessageBox.alert("系统提示", action.result.msg);
                                                    stationMapWin.destroy();
                                                    oilStationStore.removeAll();
                                                    oilStationStore.load();
                                                },
                                                failure: function (form, action) {
                                                    Ext.MessageBox.alert("系统提示", action.result.msg);
                                                },
                                                params: {id:data.id,level:1}
                                            });
                                        } else {
                                            Ext.MessageBox.alert("提示", '输入有误，请检查');
                                        }
                                    }
                                },
                            ]
                        }
                    },
                    {
                        columnWidth: 0.74,
                        layout: "form",
                        items: panel2
                    },
                ]
            }
        ]
    });

    var stationMapWin = new Ext.Window({
        layout: "border",
        width:"85%",
        height: document.body.clientHeight*0.9,
        title: '地图标记',
        closeAction: 'destroy',
        plain: true,
        modal: true,
        items: [stationMapForm],
        listeners: { 
            resize: function( viewport, adjWidth, adjHeight, rawWidth, rawHeight ) { 
                Ext.getCmp("panel2").setHeight(adjHeight-47);
            },
            afterrender:function () {
                if(data.lng && data.lat){
                    setTimeout(function () {
                        var iframe1 = Ext.getDom('frame1').contentWindow;
                        iframe1.addMarkerByLnglat(data.lng,data.lat);

                        var info = {};
                        info.lng = Ext.getCmp('lng').getValue();
                        info.M = Ext.getCmp('lng').getValue();
                        info.lat = Ext.getCmp('lat').getValue();
                        info.O = Ext.getCmp('lat').getValue();
                        info.alias = Ext.getCmp('alias').getValue();
                        info.detail_address = Ext.getCmp('detail_address').getValue();
                        info.phone = Ext.getCmp('phone').getValue();

                        iframe1.openInfoWin(info);
                    },500);

                }
            }
        }
    });

    stationMapWin.show();

    function searchData() {
        Ext.getCmp('lng').setValue('');
        Ext.getCmp('lat').setValue('');
        Ext.getCmp('alias').setValue('');
        Ext.getCmp('detail_address').setValue('');
        Ext.getCmp('phone').setValue('');

        setTimeout(function () {
            var document = Ext.getDom('frame1').contentWindow;
            document.toggleStatus = true;

            document.searchByKeywords();
        },500)
    }
    Ext.getCmp('searchData').on('click',searchData);

    console.log('data--',data);
    stationMapForm.getForm().loadRecord({data: data});
    if(!data.map){
        Ext.getCmp('map_type').setValue('10');
    }

    if(data.alias){
        var stationName = data.alias;
        Ext.getCmp('keywords').setValue(stationName);
    }else{
        var stationName = data.station_name;
        Ext.getCmp('keywords').setValue(stationName);
        Ext.getCmp('searchData').fireEvent('click');
    }
}

/**
 * 油站导入
 */
function importStation(){
    var form = new Ext.form.FormPanel({
        baseCls: 'x-plain', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
        items: [
            {
                xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
                id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
                anchor: '100%'
            }
        ]
    });
    var fanliBoot = new Ext.Window({
        title: '批量导入',
        width: 400,
        height: 105,
        minWidth: 300,
        minHeight: 100,
        closeAction: 'destroy',
        modal: true,
        layout: 'fit',
        plain: true,
        bodyStyle: 'padding:5px;',
        buttonAlign: 'center',
        items: form,
        buttons: [{
            text: '导入',
            handler: function () {
                if (form.form.isValid()) {
                    if (Ext.getCmp('userfile').getValue() === '') {
                        Ext.Msg.alert('系统提示', '请选择你要上传的文件');
                        return;
                    }
                    form.getForm().submit({
                        url: getUrl(controlName,'importStation'),
                        waitMsg: 'Saving Data...',
                        success: function (response, action) {
                            var result = action.result;
                            Ext.MessageBox.alert("系统提示", result.msg);
                            fanliBoot.destroy();
                            oilStationStore.removeAll();
                            oilStationStore.load();

                        },
                        failure: function (form, action) {
                            var r = action.result;
                            Ext.MessageBox.alert('提示', r.msg);
                        }
                    })
                }
            }
        }, {
            text: '关闭',
            handler: function () {
                fanliBoot.destroy();
            }
        }]
    });
    fanliBoot.show();
}


/**
 * 油站导出
 */
function exportStation() {
    Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
            if (btn === 'yes') {
                var params = top_panel.getForm().getValues(true);
                window.location.href = getUrl(controlName,'search') + '&_export=1&' + params;

                /*Ext.Ajax.request({
                    url:getUrl(controlName,'search'),
                    method:'post',
                    params:params+"&_export=1",
                    success: function sFn(response,options){
                        var result = Ext.decode(response.responseText);
                        if(result.code === 0){
                            main_store.removeAll();
                            main_store.load();
                            Ext.MessageBox.alert("系统提示", result.msg);
                        }else{
                            Ext.MessageBox.alert("系统提示", '操作失败');
                        }
                    },
                    failure: function (response, options) {
                    }
                });*/

            }
        }
    );
}

/**
 * 下载模板
 */

function downStationExcel(){
    Ext.MessageBox.confirm('油站模板下载', '确认下载?', function showResult(btn) {
        if (btn == 'yes') {
            window.location.href = '/download/templates/batchStation.xlsx';
        }
    });
}