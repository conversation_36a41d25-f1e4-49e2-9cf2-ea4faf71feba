/**
 * Created by wwy on 2015/11/13.
 */
//获取月初日期
/**
 * 获取日期函数
 * @param $flag 1 为月初，2为当前日期
 * @param $mode 1 为全格式，2为年月日
 * @returns {String}
 */
function GetDateStr($flag, $mode)
{
    var dd = new Date();
    var y = dd.getFullYear();
    var m = dd.getMonth() + 1;//获取当前月份的日期
    var h = dd.getHours();
    var i = dd.getMinutes();
    var s = dd.getSeconds();
    if (m.toString().length == 1) {
        m = '0' + m;
    }
    var d = '';
    if ($flag == 1)
        d = "01";
    else {
        //var new_date = new Date(y,m,1);//取当年当月中的第一天
        //d = (new Date(new_date.getTime()-1000*60*60*24)).getDate();
        //if(d.toString().length==1){
        //    d = '0'+d;
        d = dd.getDate();
        if (d.toString().length == 1) {
            d = '0' + d;
        }
    }

    var returnStr = '';
    if($mode == 2){
        returnStr = y + "-" + m + "-"+d;
    }else{
        returnStr = y+"-"+m+"-"+d+" "+h+":"+ i +":"+ s;
    }

    return returnStr;
}
var top_panel = new Ext.form.FormPanel({
    region : 'north',
    hideLabels : true,
    bodyStyle : 'padding: 10px',
    height : 70,
    items  : [
            {
                xtype : 'compositefield',
                items : [
                    {
                        xtype: 'displayfield',
                        value: '油卡类型：',
                        style: 'padding-left: 10px'
                    },
                    {
                        xtype: 'combo',
                        width: 80,
                        id   : 'search_oil_come',
                        hiddenName:'search_oil_come',
                        editable:true,
                        forceSelection: true,
                        mode: 'local',
                        triggerAction:'all',
                        displayField: 'oil_com',
                        valueField: 'id',
                        emptyText : '',
                        store : oilComStore,
                        listWidth: 200
                    },
                        {
                            xtype: 'displayfield',
                            value: '油站名称：'
                        },
                        {
                            xtype: 'textfield',
                            width: 100,
                            id   : 'search_station_name',
                            emptyText: ''
                        },
                        {
                            xtype: 'displayfield',
                            value: '省/直辖市：',
                            style: 'padding-left: 10px'
                        },
                        {
                            xtype: 'combo',
                            width: 70,
                            id   : 'search_regions_id',
                            editable: true,
                            emptyText : '',
                            mode: 'local',
                            triggerAction:'all',
                            displayField: 'province',
                            valueField: 'id',
                            store: oilProvincesStore,
                            listeners:{
                                'expand':function(){
                                    if(!insertFlag){
                                        oilProvincesStore.insert(0, insertRecord);
                                        insertFlag = true;
                                    }
                                }
                            }
                        },
                        {
                            xtype: 'displayfield',
                            value: '创建时间：'
                        },
                        {
                            xtype: "datetimefield",
                            id: "s_start_time",
                            name:'createtimeGe',
                            format:'Y-m-d H:i:s',
                           value: GetDateStr(1,2) + ' 00:00:00',
                            width: 145
                        },
                        {
                            xtype: 'displayfield',
                            value: '—'
                        },
                        {
                            xtype: "datetimefield",
                            id: "s_end_time",
                            name:'createtimeLe',
                            format:'Y-m-d H:i:s',
                            value: GetDateStr(2,2) + ' 23:59:59',
                            width: 145
                        },



                    ]
            },
            {
                xtype: 'compositefield',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '最后修改时间：'
                    },
                    {
                        xtype: "datetimefield",
                        id: "s_start_utime",
                        name:'updatetimeGe',
                        format:'Y-m-d H:i:s',
                        width: 145
                    },
                    {
                        xtype: 'displayfield',
                        value: '—'
                    },
                    {
                        xtype: "datetimefield",
                        id: "s_end_utime",
                        name:'updatetimeLe',
                        format:'Y-m-d H:i:s',
                        width: 145
                    },
                    {
                        xtype: 'displayfield',
                        value: '油站运营商：'
                    },
                    {
                        xtype: 'textfield',
                        width: 200,
                        id   : 'station_operators',
                        emptyText: ''
                    },
                    {
                        xtype:'button',
                        text:'查询',
                        style: 'padding-left : 10px;',
                        handler: function()
                        {
                            oilStationStore.removeAll();
                            oilStationStore.load();
                        }
                    },
                    {
                        xtype:'button',
                        text:'重置',
                        style: 'padding-left : 10px;',
                        handler: function()
                        {
                            Ext.getCmp('search_station_name').setValue('');
                            Ext.getCmp('station_operators').setValue('');
                            Ext.getCmp('search_regions_id').setValue('');
                            Ext.getCmp('search_oil_come').setValue('');
                            Ext.getCmp('s_start_time').setValue(GetDateStr(1,2) + ' 00:00:00');
                            Ext.getCmp('s_end_time').setValue(GetDateStr(2,2) + ' 23:59:59');
                            Ext.getCmp('s_start_utime').setValue('');
                            Ext.getCmp('s_end_utime').setValue('');
                            oilStationStore.removeAll();
                            oilStationStore.load();
                        }
                    },


                ]
            },
        ]
});
