var top_panel = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 70,
    items:
        [
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '收款时间：',
                            width: 60,
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            name: 'paytimeGe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '~'
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            name: 'paytimeLe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '状态：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 120,
                            mode: 'remote',
                            triggerAction: 'all',
                            forceSelection: true,
                            emptyText: '请选择..',
                            hiddenName: 'status',
                            displayField: 'value',
                            valueField: 'key',
                            store: getStatus
                        }),

                        {
                            xtype: 'displayfield',
                            value: '付款方：',
                            width: 60,
                        },
                        {
                            xtype: 'textfield',
                            name: 'payment_company',
                            width: 100
                        },

                        {
                            xtype: 'displayfield',
                            value: '收款流水号：',
                            width: 80,
                        },
                        {
                            xtype: 'textfield',
                            name: 'pay_no',
                            width: 150
                        },

                        {
                            xtype: 'displayfield',
                            value: '认领客户：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 200,
                            listWidth: 350,
                            hiddenName: 'orgcode',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'remote',
                            queryParam: 'keyword',
                            minChars: 2,
                            displayField: 'org_name',//显示的值
                            valueField: 'orgcode',//后台接收的key
                            store: getOilOrg,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                            listeners: {
                                'select': function (combo, record, index) {
                                    Ext.getCmp('receipt_title_id').setValue('');
                                    getReceiptTitle.removeAll();
                                    console.log(record.data);
                                    getReceiptTitle.baseParams = {orgcode: record.data.orgcode, _export: 1};
                                    getReceiptTitle.load();
                                },
                            },
                        }),
                        {
                            xtype: 'displayfield',
                            value: '认领方：'
                        },
                        {
                            xtype: 'combo',
                            hiddenName: 'claim_object',
                            mode: 'local',
                            width: 90,
                            emptyText: '请选择..',
                            triggerAction: 'all',
                            forceSelection: true,
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [['10', '下游'], ['20', '上游'], ['30', '未知']]
                            }),
                        },
                    ]
            },
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '认领时间：',
                            width: 60,
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            name: 'createtimeGe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '~'
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            name: 'createtimeLe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '认领用途：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 120,
                            mode: 'remote',
                            triggerAction: 'all',
                            forceSelection: true,
                            emptyText: '请选择..',
                            hiddenName: 'purpose_id',
                            displayField: 'name',
                            valueField: 'id',
                            store: purposeList,
                            queryParam: 'keyword',
                            minChars: 2,
                        }),

                        {
                            xtype: 'displayfield',
                            value: '收款方：',
                            width: 60,
                        },
                        {
                            xtype: 'textfield',
                            name: 'receipt_company',
                            width: 100
                        },

                        {
                            xtype: 'displayfield',
                            value: '业务单号：',
                            width: 80,
                            style: 'padding-left: 15px'
                        },
                        {
                            xtype: 'textfield',
                            name: 'business_no',
                            width: 150
                        },

                        {
                            xtype: 'displayfield',
                            value: '&nbsp;',
                            width: 60,
                        },
                        {
                            xtype: 'button',
                            text: '查询',
                            handler: function () {
                                main_store.removeAll();//移除原来的数据
                                main_store.load();//加载新搜索的数据
                            }
                        },
                        {
                            xtype: 'button',
                            text: '重置',
                            style: 'padding-left : 10px;',
                            handler: function () {
                                top_panel.getForm().reset();
                                main_store.removeAll();//移除原来的数据
                                main_store.load();//加载新搜索的数据
                                btnInit();
                            }
                        },
                        // {
                        //     xtype: 'displayfield',
                        //     value: '认领单号：'
                        // },
                        // {
                        //     xtype : 'textfield',
                        //     name  : 'user_name',
                        //     width : 100
                        // },
                    ]
            }
        ]
});