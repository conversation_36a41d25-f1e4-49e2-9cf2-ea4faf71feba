var controlName = 'oil_inspect_tax';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getSupplierList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","operator_id","cooperation_status","cooperation_status_value","cooperation_num","cooperation_type","cooperation_type_value","bill_type","bill_type_value",
            "network_docker","network_docker_phone","settlement_docker","settlement_docker_phone","collect_company_id","company_name","settle_obj","settle_obj_value",
            "supplier_name","creator","auditor","last_operator","status","remark","audit_time","operator_name","createtime","merchant_id","eas_code",
            "updatetime","deletetime","data_source","data_source_value","recharge_type","recharge_type_value","brand","brand_value","receipt_statement","receipt_statement_value",
            "receipt_claim","receipt_claim_value","receipt_type_value","receipt_type"
        ]
    ),
});

// 获取油站供应商列表
var getOilSupplier = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_station_supplier&f=getOilSupplier', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','supplier_name'])
});

// 获取签约运营商列表
var getOilOperators = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_operators&f=getReceiveCompany', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','company_name'])
});

//供应商油站明细
var detailStore=new Ext.data.Store({
    proxy:new Ext.data.HttpProxy({url:'../inside.php?t=json&m=oil_supplier_relation&f=getList',method:"POST"}),
    //params:{start:0,limit:5},
    reader:new Ext.data.JsonReader({
            totalProperty:'data.total',
            root:'data.data'},
        ['id', 'name', 'cooperation_type', 'cooperation_type_value', 'service_area_info','code', 'description', 'contactor_name','contactor_phone','account','password']
    )
});

// 关联服务区
var detailAreaStore=new Ext.data.Store({
    proxy:new Ext.data.HttpProxy({url:'../inside.php?t=json&m=oil_supplier_relation&f=getBindAreaList',method:"POST"}),
    //params:{start:0,limit:5},
    reader:new Ext.data.JsonReader({
            totalProperty:'',
            root:'data'},
        ['id', 'area_id', 'name', 'cooperation_type', 'cooperation_type_value', 'statistics_type','area_status_value', 'statistics_type_value', 'station_num','code', 'description', 'contactor_name','contactor_phone']
    )
});

// 获取运营商油站列表
var getOilSupplierStations = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_supplier_relation&f=getOilSupplierStations', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','name','code_name'])
});

var getSettleObj = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_station_supplier&f=getSettleObj', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','name'])
});

// 获取油站服务区列表
var getStationServiceAreas = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_supplier_relation&f=getStationServiceAreas', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','service_area_info','code_area'])
});

// 关联收款公司
var detailSupplierCompanyStore=new Ext.data.Store({
    proxy:new Ext.data.HttpProxy({url:'../inside.php?t=json&m=oil_supplier_company&f=getList',method:"POST"}),
    //params:{start:0,limit:5},
    reader:new Ext.data.JsonReader({
            totalProperty:'data.total',
            root:'data.data'},
        ['id', 'company_name', 'collect_company_id', 'collect_banks_nums', 'contract_type', 'eas_code', 'merchant_id', 'status','status_name',
            'supplier_id', 'supplier_name','createtime','creator','last_operator','updatetime','change_reason','change_reason_name']
    )
});

//获取状态信息
var getIsOnStatus = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_supplier_operator&f=getIsOnStatus', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data',
            root: 'data'
        },
        ['key', 'value']
    )
});

// 获取服务区列表
var getAreaList = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_station_area&f=getOilStationAreas', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','name','code', 'code_name'])
});

// 获取服务区列表 searchOperatorsData
var getcardMainList = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_card_main&f=searchCardMainData', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','oil_com_val','main_no', 'oil_com'])
});

// 获取运营商列表
var getStationOperatorsList = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_station_operators&f=searchOperatorsData', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','pcode','pname', 'operators_name'])
});

// 获取在用收款公司列表
var getCollectCompany = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_collect_company&f=searchList', method: "POST"}),
    autoLoad: false,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','company_name','merchant_id'])
});

// 获取已绑定的收款账号
var getBindCard = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_collect_bank&f=searchList', method: "POST"}),
    autoLoad: false,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','collect_company_id','bank_account_no','bank_name','unin_code'])
});

// 供应商回票单位
var detailSupplierReceiptReturnUnitStore=new Ext.data.Store({
    proxy:new Ext.data.HttpProxy({url:'../inside.php?t=json&m=oil_station_supplier&f=getSupplierReceiptReturnUnitList',method:"POST"}),
    reader:new Ext.data.JsonReader({
            totalProperty:'',
            root:'data'},
        ['id', 'oil_name', 'unit','createtime','updatetime']
    )
});

var detailSupplierReceiptReturnUnitOilStore = new Ext.data.SimpleStore({
    fields: ['value', 'text'],
    data: [['', '请选择'],['升', '升'], ['吨', '吨']],
});

var detailSupplierReceiptReturnUnitNewOilStore = new Ext.data.SimpleStore({
    fields: ['value', 'text'],
    data: [['', '请选择'],['千克', '千克'], ['立方米', '立方米'], ['吨', '吨']],
});