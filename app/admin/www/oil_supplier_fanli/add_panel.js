/**
 * 定义添加组件容器类
 */
var supplierInfo = "";
var addPanel = {
	id:'',
	formId:'',
	winId:'',
	//初始化容器
	init: function (params) {
		if(this.id){//编辑
			Ext.getCmp(this.formId).getForm().loadRecord({data: params.data});
		}
	},
	//获取组件容器
	getPanel: function (formId, winId, id) {
		this.id = id;
		this.formId = formId;
		this.winId = winId;
		var panel = [
			{// 第一行
				xtype : 'compositefield',
				items :
					[
						{
							xtype: 'displayfield',
							value: '油站供应商名称：',
							style: 'padding-left: 12px'
						},
						new Ext.form.ComboBox({
							width: 300,
							hiddenName: 'supplier_id',
							id:"i_supplier_id",
							triggerAction: 'all',
							forceSelection: true,
							mode: 'local',
							queryParam:'supplier_nameLike',
							minChars:2,
							displayField: 'supplier_name',//显示的值
							valueField: 'id',//后台接收的key
							store: supplierStore,
							allowBlank: false,
							emptyText: '请选择..',
							enableKeyEvents: true,
							listeners: {
								'focus': function () {
									supplierStore.load();
								},
								'beforequery': function (e) {
									var combo = e.combo;
									if (!e.forceAll) {
										var input = e.query;
										// 检索的正则
										var regExp = new RegExp(".*" + input + ".*");
										// 执行检索
										combo.store.filterBy(function (record, id) {
											// 得到每个record的项目名称值
											var text = record.get(combo.displayField);
											return regExp.test(text);
										});
										combo.expand();
										return false;
									}
								},
								'select': function(combo, record, index){
									console.log(record.data);
									var obj_txt = "1级-油站供应商"

									if(record.data.settle_obj == 20){
										obj_txt = record.data.cooperation_type == 30 ? '2级-主卡' : "2级-服务区";
										Ext.getCmp("area_form").show();
									}else{
										Ext.getCmp("area_form").hide();
									}
									supplierInfo = record.data;
									Ext.getCmp("i_area_id").setValue("");
									Ext.getCmp("settle_txt").setValue(obj_txt);
									Ext.getCmp("i_operator_id").setValue(record.data.operator_id);
									Ext.getCmp("i_collect_company_id").setValue("");
									getSupplierCompany.reload()
									areaStore.removeAll();
								},
							}
						}),
						{xtype: 'displayfield',value: ' <font color=red>*</font>'},
					]
			},
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: '核算主体：',
							style: 'padding-left: 12px'
						},
						{
							xtype:"displayfield",
							id:"settle_txt",
							width:150,
						},
					]
			},
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
				id:"area_form",
				items :
					[
						{
							xtype: 'displayfield',
							value: '服务区/主卡：',
							style: 'padding-left: 12px'
						},
						new Ext.form.ComboBox({
							width: 350,
							hiddenName: 'area_id',
							triggerAction: 'all',
							id:"i_area_id",
							forceSelection: true,
							mode: 'local',
							queryParam:'nameLike',
							minChars:2,
							displayField: 'full_name',//显示的值
							valueField: 'id',//后台接收的key
							store: areaStore,
							// allowBlank: false,
							// emptyText: '请选择..',
							enableKeyEvents: true,
							listeners: {
								'focus': function () {
									areaStore.removeAll();
									s_id = Ext.getCmp("i_supplier_id").getValue();
									areaStore.baseParams.supplier_id = s_id;
									areaStore.baseParams.cooperation_type = supplierInfo.cooperation_type;
									areaStore.load();
								},
								'beforequery': function (e) {
									var combo = e.combo;
									if (!e.forceAll) {
										var input = e.query;
										// 检索的正则
										var regExp = new RegExp(".*" + input + ".*");
										// 执行检索
										combo.store.filterBy(function (record) {
											// 得到每个record的项目名称值
											var text = record.get(combo.displayField);
											return regExp.test(text);
										});
										combo.expand();
										return false;
									}
								}
							}
						}),
						{xtype: 'displayfield',value: ' <font color=red>*</font>'},
					]
			},
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: '签约主体：',
							style: 'padding-left: 12px'
						},
						new Ext.form.ComboBox({
							xtype: 'combo',
							width: 200,
							id: 'i_operator_id',
							name: 'operator_id',
							hiddenName: 'operator_id',
							triggerAction: 'all',
							forceSelection: true,
							mode: 'local',
							minChars:2,
							allowBlank: false,
							blankText : '不能为空',
							displayField: 'name',//显示的值
							valueField: 'id',//后台接收的key
							store: getOilOperators,
							emptyText: '请选择..',
							enableKeyEvents: true,
							typeAhead: false, // 关闭自动完成
							editable: false, // 禁用编辑，防止用户输入
						}),
						{xtype: 'displayfield',value: ' <font color=red>*</font>'},
					]
			},
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: '收款公司：',
							style: 'padding-left: 12px'
						},
						new Ext.form.ComboBox({
							xtype: 'combo',
							width: 300,
							id: 'i_collect_company_id',
							name: 'collect_company_id',
							hiddenName: 'collect_company_id',
							triggerAction: 'all',
							forceSelection: true,
							mode: 'local',
							minChars:2,
							allowBlank: false,
							blankText : '不能为空',
							displayField: 'company_name',//显示的值
							valueField: 'collect_company_id',//后台接收的key
							store: getSupplierCompany,
							emptyText: '请选择..',
							enableKeyEvents: true,
							typeAhead: false, // 关闭自动完成
							editable: false, // 禁用编辑，防止用户输入
							listeners: {
								'beforequery': function (e) {
									getSupplierCompany.baseParams = {
										supplier_id:Ext.getCmp("i_supplier_id").getValue(),
										limit: 1000,
										page: 0,
									};
									getSupplierCompany.load();
								},
								afterrender: function(combo) {
                                    combo.store.on('load', function(store) {
                                        for (var i = 0; i < store.getCount(); i++) {
                                            var record = store.getAt(i);
                                            if (record.get('status') == 1) {
                                                combo.setValue(record.get('id'));
                                            }
                                        }
                                    }
                                },
							},
						}),
						{xtype: 'displayfield',value: ' <font color=red>*</font>'},
					]
			},
			{// form 表单
				xtype : 'radiogroup',
				id:'fanliId',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: "返利形式：",
							style: 'padding-left: 12px'
						},
						{
							xtype : "radio",
							name:'fanli_type',
							inputValue: '10',
							id:"fanli_10",
							checked   : true,
							boxLabel : "现金",
						},
						{
							xtype : "radio",
							name:'fanli_type',
							inputValue: '20',
							id:"fanli_20",
							boxLabel : "积分",
						},
						{xtype: 'displayfield',value: ' <font color=red>*</font>'},
					],
					listeners:{
						'change':function(group,checked){
							switch (checked.inputValue){
								case '20':
									Ext.getCmp("arrive_10").setValue(true);
									Ext.getCmp("arrive_20").setValue(false);
									Ext.getCmp("arrive_20").disable();
									Ext.getCmp("arrive_10").enable();
									Ext.getCmp("i_service").disable();
									break;
								case '10':
									Ext.getCmp("arrive_10").enable();
									Ext.getCmp("arrive_20").enable();
									Ext.getCmp("i_service").enable();
									Ext.getCmp("arrive_10").setValue(true);
									Ext.getCmp("arrive_20").setValue(false);
									break;
							}
						}
					}
			},
			// {// 第二行
			// 	xtype : 'compositefield',
			// 	style:'padding-top:5px;',
			// 	items :
			// 		[
			// 			{
			// 				xtype: 'displayfield',
			// 				value: '返利形式：',
			// 				style: 'padding-left: 12px'
			// 			},
			// 			{
			// 				xtype: 'combo',
			// 				width: 340,
			// 				id   : 'i_fanli_type',
			// 				hiddenName : "fanli_type",
			// 				editable: false,
			// 				allowBlank: false,
			// 				emptyText : '请选择',
			// 				mode: 'local',
			// 				triggerAction:'all',
			// 				displayField: 'name',
			// 				valueField: 'value',
			// 				store : new Ext.data.SimpleStore({
			// 					fields: ['name', 'value'],
			// 					data: [['现金', '10'], ['积分', '20']]
			// 				})
			// 			},
			// 			{xtype: 'displayfield',value: ' <font color=red>*</font>'},
			// 		]
			// },
			{// form 表单
				xtype : 'radiogroup',
				id:'arriveId',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: "返利到账类型：",
							style: 'padding-left: 12px'
						},
						{
							xtype : "radio",
							name:'arrive_type',
							id:"arrive_10",
							inputValue: '10',
							checked   : true,
							boxLabel : "充到账户余额",
						},
						{
							xtype : "radio",
							name:'arrive_type',
							id:"arrive_20",
							inputValue: '20',
							boxLabel : "不充到账户余额",
						},
						{xtype: 'displayfield',value: ' <font color=red>*</font>'},
					]
			},
			{// 第二行
				xtype : 'radiogroup',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: '返利类型：',
							style: 'padding-left: 12px'
						},
                        {
                            xtype : "radio",
                            name:'rebate_form',
                            id:"rebate_form_20",
                            inputValue: '20',
                            boxLabel : "充值返利",
                        },
                        {
                            xtype : "radio",
                            name:'rebate_form',
                            id:"rebate_form_10",
                            inputValue: '10',
                            checked   : true,
                            boxLabel : "消费返利",
                        },
                        {xtype: 'displayfield',value: ' <font color=red>*</font>'},
					],
                listeners: {
				    'change': function(group,checked){
                        switch (checked.inputValue){
                            case '20':
                                Ext.getCmp('i_consume_time').hide();
                                Ext.getCmp("i_r_recharge_no").show();
                                Ext.getCmp('i_start_time').disable();
                                Ext.getCmp('i_end_time').disable();
                                break;
                            case '10':
                                Ext.getCmp("i_r_recharge_no").hide();
                                Ext.getCmp('i_consume_time').show();
                                Ext.getCmp('i_start_time').enable();
                                Ext.getCmp('i_end_time').enable();
                                break;
                        }
                    }
                }
			},
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: '返利金额：',
							style: 'padding-left: 12px'
						},
						{
							xtype:"numberfield",
							id:"i_fanli",
							name:"fanli_fee",
							allowBlank: false,
							emptyText : '不能为空',
							decimalPrecision:2,//精确到小数点后两位
							width:150,
						},
						{xtype: 'displayfield',value: ' <font color=red>如果是积分，请自行换算成金额*</font>'},
					]
			},
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: '服务费金额：',
							style: 'padding-left: 12px'
						},
						{
							xtype:"numberfield",
							id:"i_service",
							name:"service_fee",
							allowBlank: false,
							emptyText : '不能为空',
							decimalPrecision:2,//精确到小数点后两位
							width:160,
						},
						{xtype: 'displayfield',value: ' <font color=red>如果是积分，请自行换算成金额*</font>'},
					]
			},
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
                id:'i_consume_time',
				items :
					[
						{
							xtype: 'displayfield',
							value: '消费时间：',
							style: 'padding-left: 12px'
						},
						{
							xtype: "datetimefield",
							id: "i_start_time",
							name: 'trade_starttime',
							format: 'Y-m-d H:i:s',
							allowBlank: false,
							emptyText : '请选择',
							width: 150,
							value: new Date(new Date().setHours(0, 0, 0, 0))
						},
						{
							xtype: 'displayfield',
							value: '—'
						},
						{
							xtype: "datetimefield",
							id: "i_end_time",
							name: 'trade_endtime',
							format: 'Y-m-d H:i:s',
							allowBlank: false,
							emptyText : '请选择',
							width: 150,
							value: new Date(new Date().setHours(23, 59, 59, 999))
						},
						{xtype: 'displayfield',value: ' <font color=red>*</font>'},
					]
			},
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: '上游实际到账时间：',
							style: 'padding-left: 12px'
						},
						{
							xtype: "datefield",
							id: "s_real_arrival_time",
							name: 'real_arrival_time',
							format: 'Y-m-d',
							width: 145
						},
						{xtype: 'displayfield',value: ' <font color=red>*</font>'},
					]
			},
            {// 第二行
                xtype : 'compositefield',
                style:'padding-top:5px;',
                id:'i_r_recharge_no',
                hidden:true,
                items :
                    [
                        {
                            xtype: 'displayfield',
                            value: '充值单号：',
                            style: 'padding-left: 12px'
                        },
                        {
                            xtype:"textarea",
                            id:"i_recharge_no",
                            name:"recharge_no",
                            width:320,
                            height:60,
                        },
                        {xtype: 'displayfield',value: ' <font color=red>多个充值单号之间以英文逗号,分开，最多输入50个</font>'},
                    ]
            },
			{// 第二行
				xtype : 'compositefield',
				style:'padding-top:5px;',
				items :
					[
						{
							xtype: 'displayfield',
							value: '录入备注：',
							style: 'padding-left: 12px'
						},
						{
							xtype:"textarea",
							id:"i_remark",
							name:"remark",
							width:320,
							height:60,
						},
						// {xtype: 'displayfield',value: ' <font color=red>*</font>'},
					]
			},
		];

		return panel;
	},
	//提交表单
	submit: function () {
		var _form = Ext.getCmp(this.formId).getForm();
		var that = this;
		if (_form.isValid()) {
			var url = '';
			if (this.id) {
				url = getUrl(controlName, 'createOrUpdate');
			} else {
				url = getUrl(controlName, 'createOrUpdate');
			}
			_form.submit({
				url: url,
				waitMsg: 'Saving Data...',
				params:{id:that.id},
				success: function (form, action) {
					Ext.MessageBox.alert("消息!", action.result.msg);
					main_store.removeAll();
					main_store.load();
					oilTest.closeWin(that.winId)
				},
				failure: function (form, action) {
					Ext.MessageBox.alert("消息!", action.result.msg);
				}
			});
		} else {
			Ext.MessageBox.alert("提示", '输入有误，请检查');
		}
	}
}