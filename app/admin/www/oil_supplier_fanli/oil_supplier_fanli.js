/**
 * 全局类
 */
function oiltest() {
    //列表表格
    this.getGridPanel = function () {
        var _sm =new Ext.grid.CheckboxSelectionModel({
            singleSelect: false,
            listeners: {
                selectionchange: function(data) {
                    if (data.getCount()){
                        var sl   = grid_list.getSelectionModel();
                        var data = sl.getSelections();
                        var statusArr = [];
                        data.forEach(function (val) {
                            statusArr.push(val.get('status'));
                        });
                        if(data.length > 1){
                            (Ext.getCmp('btnUpdate')) ? Ext.getCmp('btnUpdate').disable() : '';//修改按钮
                        }else{
                            (Ext.getCmp('btnUpdate')) ? Ext.getCmp('btnUpdate').enable() : '';//修改按钮
                        }
                        console.log(statusArr);
                        if(statusArr.indexOf(20) >= 0) {
                            (Ext.getCmp('btnDelete')) ? Ext.getCmp('btnDelete').disable() : '';//删除按钮
                            (Ext.getCmp('btnUpdate')) ? Ext.getCmp('btnUpdate').disable() : '';//修改按钮
                        }else{
                            (Ext.getCmp('btnDelete')) ? Ext.getCmp('btnDelete').enable() : '';//删除按钮
                            (Ext.getCmp('btnUpdate')) ? Ext.getCmp('btnUpdate').enable() : '';//修改按钮
                        }
                        if(statusArr.indexOf(20) >= 0 || statusArr.indexOf(30) >= 0) {
                            (Ext.getCmp('btnbatchaudit')) ? Ext.getCmp('btnbatchaudit').disable() : '';//通过按钮
                            (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回按钮
                        }else{
                            (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').enable() : '';//驳回按钮
                            (Ext.getCmp('btnbatchaudit')) ? Ext.getCmp('btnbatchaudit').enable() : '';//通过按钮
                        }
                    }else{
                        (Ext.getCmp('btnDelete')) ? Ext.getCmp('btnDelete').disable() : '';//删除按钮
                        (Ext.getCmp('btnUpdate')) ? Ext.getCmp('btnUpdate').disable() : '';//修改按钮
                        (Ext.getCmp('btnbatchaudit')) ? Ext.getCmp('btnbatchaudit').disable() : '';//通过按钮
                        (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回按钮
                    }
                }
            }
        });

        var grid_list = new Ext.grid.GridPanel({
            title	 :'上游返利记录',
            region	 : 'center',
            loadMask : true,
            cm : new Ext.grid.ColumnModel([
                _sm,
                {header : '序号',dataIndex : 'id',width : 60},
                {header : '上游返利单号',dataIndex : 'no',width : 150},
                {header : '审核状态',dataIndex : 'status_txt',width : 90,align: 'center',renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                    var ss = record.data.status;
                        if(ss == 20) {
                            return '<span style="color:green;">'+record.data.status_txt+'</span>';
                        } else if(ss == 30) {
                            return '<span style="color:red;">' + record.data.status_txt + '</span>';
                        } else {
                             return  '<span style="color:black;">'+record.data.status_txt+'</span>';
                        }
                }},
                {header : '油站供应商ID',dataIndex : 'supplier_id',width : 100},
                {header : '油站供应商名称',dataIndex : 'supplier_name',width : 200},
                {header : '油站供应商合作类型',dataIndex : 'cooperation_type_txt',width : 200},
                {header : '核算主体',dataIndex : 'settle_obj_txt',width : 100},
                {header : '核算主体ID',dataIndex : 'settle_object_id',width : 100},
                {header : '核算主体名称',dataIndex : 'settle_object_name',width : 180},
                {header : '服务区/主卡编码',dataIndex : 'area_code',width : 180},
                {header : '服务区/主卡名称',dataIndex : 'area_name',width : 180},
                {header : '返利类型',dataIndex : 'rebate_form_txt',width : 100},
                {header : '返利金额',dataIndex : 'fanli_fee',width : 100},
                {header : '服务费金额',dataIndex : 'service_fee',width : 100},
                {header : '消费开始时间',dataIndex : 'trade_starttime_txt',width : 180},
                {header : '消费结束时间',dataIndex : 'trade_endtime_txt',width : 180},
                {header : '上游实际到账时间',dataIndex : 'real_arrival_time',width : 150},
                {header : '充值单号',dataIndex : 'recharge_no_txt',width : 150},
                {header : '返利形式',dataIndex : 'fanli_type_txt',width : 100},
                {header : '返利到账类型',dataIndex : 'arrive_type_txt',width : 100},
                {header : '签约主体',dataIndex : 'operator_name',width : 180},
                {header : '收款公司',dataIndex : 'collect_company_name',width : 180},
                {header : '创建时间',dataIndex : 'createtime',width : 180},
                {header : '创建人',dataIndex : 'creator',width : 180},
                {header : '最后修改时间',dataIndex : 'updatetime',width : 180},
                {header : '最后修改人',dataIndex : 'last_operator',width : 180},
                {header : '审核时间',dataIndex : 'audit_time',width : 180},
                {header : '审核人',dataIndex : 'audit_operator',width : 180},
                {header : '录入备注',dataIndex : 'remark',width : 180,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                    return '<a title="'+record.data.remark+'">'+record.data.remark+'</a>';
                }},
                {header : '审核备注',dataIndex : 'audit_remark',width : 180,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                    return '<a title="'+record.data.audit_remark+'">'+record.data.audit_remark+'</a>';
                }},
            ]),
            store : main_store,
            sm :_sm,
            //分页
            bbar: new Ext.PagingToolbar({
                plugins: new Ext.ux.plugin.PagingToolbarResizer,
                pageSize: pagesize,
                displayInfo : true,
                displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
                emptyMsg : '没有符合条件的记录',
                store : main_store
            }),
            tbar : []
        });

        return grid_list;
    };

    //关闭窗口
    this.closeWin = function (id) {
        if (!id) return;
        var win = Ext.getCmp(id);
        win.destroy();
    };

    var _getWindow = function (config) {
        var width = config.width,
            height = config.height,
            title = config.title,
            id = config.id,
            button = config.button;

        button.push({
            text: '取消',
            id: 'button_cancer',
            handler: function () {
                var oilTest = new oiltest();
                oilTest.closeWin(id);
            }
        });
        var windows = new Ext.Window({
            layout: 'border',
            region: 'center',
            width: width,
            modal: true,//遮挡窗口后的内容
            id: id,
            height: height,
            title: title,
            closeAction: 'destroy',
            buttons: button
        });

        return windows;
    };

    //添加
    this.add = function () {
        var windows = _getWindow({
            title: '添加/编辑上游返利',
            id: 'add_win',
            width: 700,
            height:500,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'add-form',
            items: addPanel.getPanel('add-form', 'add_win')
        });
        Ext.getCmp("area_form").hide();
        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };

    //编辑
    this.update = function () {
        var sm   = grid_list.getSelectionModel();
        var data = sm.getSelections();
        data[0] ? id = data[0].get("id") : '';
        getSupplierCompany.baseParams = {
            supplier_id: data[0].data.supplier_id
        };
        getSupplierCompany.reload()
        var windows = _getWindow({
            title: '添加/编辑上游返利',
            id: 'update_win',
            width: 700,
            height:460,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'update-form',
            items: addPanel.getPanel('update-form', 'update_win',id)
        });

        var selectRow = data[0].data;
        console.log(selectRow);

        function fn() {
            addPanel.init(data[0]);

            var supTxt = selectRow.supplier_name;
            var _supObj = Ext.getCmp('i_supplier_id');
            _supObj.setValue(selectRow.supplier_id);
            _supObj.setRawValue(supTxt);

            var obj_txt = "1级-油站供应商"
            if (selectRow.settle_obj == 20) {
                obj_txt = "2级-服务区";
            }
            if (selectRow.cooperation_type == 30) {
                obj_txt = "2级-主卡";
            }
            Ext.getCmp("settle_txt").setValue(obj_txt);
            if (selectRow.settle_obj == 10) {
                Ext.getCmp("area_form").hide();
            } else {
                var areaObj = Ext.getCmp("i_area_id");
                areaObj.setValue(selectRow.area_id);
                areaObj.setRawValue(selectRow.area_name);
            }
        }

        var task = new Ext.util.DelayedTask(fn);
        task.delay(500);
        windows.add(panel);
        windows.doLayout();
        windows.show();

        if(selectRow.arrive_type == 10){
            Ext.getCmp("arrive_10").setValue(true);
            Ext.getCmp("arrive_20").setValue(false);
        }else{
            Ext.getCmp("arrive_10").setValue(false);
            Ext.getCmp("arrive_20").setValue(true);
        }
        if(selectRow.fanli_type == 10){
            Ext.getCmp("fanli_10").setValue(true);
            Ext.getCmp("fanli_20").setValue(false);
        }else{
            Ext.getCmp("fanli_10").setValue(false);
            Ext.getCmp("fanli_20").setValue(true);
            Ext.getCmp("arrive_10").setValue(false);
            Ext.getCmp("arrive_20").setValue(true);
        }
        if (selectRow.rebate_form == 10) {
            Ext.getCmp("rebate_form_10").setValue(true);
            Ext.getCmp("rebate_form_20").setValue(false);
            Ext.getCmp('i_start_time').enable();
            Ext.getCmp('i_end_time').enable();
        } else {
            Ext.getCmp("rebate_form_10").setValue(false);
            Ext.getCmp("rebate_form_20").setValue(true);
            Ext.getCmp('i_start_time').disable();
            Ext.getCmp('i_end_time').disable();
        }
    };

    this.updateData = function (flag) {

        var sl   = grid_list.getSelectionModel();
        var data = sl.getSelections();
        var selectIds = [];
        data.forEach(function (val) {
            selectIds.push(val.get('id'));
        });
        console.log(selectIds);
        if(selectIds.length == 0){
            Ext.MessageBox.alert("消息!", "请选择数据！");
            return
        }

        var msg = '删除之后该条纪录将不再显示，确定要删除？';
        if(flag == 1) {
            Ext.MessageBox.confirm('删除', msg, function showResult(btn) {
                if (btn == 'yes') {
                    Ext.Ajax.request({
                        url: getUrl(controlName, 'batchUpdateData'),
                        method: 'post',
                        params: {ids: selectIds.toString(), flag: flag},
                        success: function sFn(response, options) {
                            var r = Ext.decode(response.responseText);
                            if(r.code == 0) {
                                main_store.removeAll();
                                main_store.load();
                                Ext.MessageBox.alert("消息!", "成功");
                            }else {
                                Ext.MessageBox.alert("消息!", r.msg);
                            }
                        },
                        failure: function (form, action) {
                            var r = Ext.decode(form.responseText);
                            Ext.MessageBox.alert("消息!", r.msg);
                        }
                    });
                }
            });
        }else {
            if(flag == 2){
                msg = "确认通过选中的记录吗？";
            }else if(flag == 3){
                msg = "确认驳回选中的记录吗？";
            }
            Ext.MessageBox.prompt("提示", msg+"", function (id, msg) {
                // alert("单击的按钮ID是：" + id + "\n" +"输入的内容是：" + msg);
                if(id == "ok") {
                    Ext.Ajax.request({
                        url: getUrl(controlName, 'batchUpdateData'),
                        method: 'post',
                        params: {ids: selectIds.toString(), flag: flag,auditRemark:msg},
                        success: function sFn(response, options) {
                            console.log(response);
                            var r = Ext.decode(response.responseText);
                            if(r.code == 0) {
                                main_store.removeAll();
                                main_store.load();
                                Ext.MessageBox.alert("消息!", "成功");
                            }else {
                                Ext.MessageBox.alert("消息!", r.msg);
                            }
                        },
                        failure: function (form, action) {
                            var r = Ext.decode(form.responseText);
                            Ext.MessageBox.alert("消息!", r.msg);
                        }
                    });
                }
            }, this, true, "");
        }
    };

    //导出
    this.export = function () {
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn == 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = '/inside.php?t=json&m='+ controlName +'&f=getList&_export=1'+'&'+params;
                }
            }
        );
    };

    this.downTpl = function () {
        Ext.MessageBox.confirm('下载模板', "确定要下载吗？", function showResult(btn) {
                if (btn === 'yes') {
                    window.location.href = '/download/templates/上游返利录入模板.xlsx';
                }
            }
        );
    }

    /**
     * 批量导入
     */
    this.batchImport = function() {
        var form = new Ext.form.FormPanel({
            baseCls: 'x-plain', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
            items: [
                {xtype: 'displayfield',fieldLabel: '批量导入上游返利数据数据填写规范',style: 'padding-left: 5px',with:100,
                    value: ' <font color=red>1、请根据油站供应商编码、名称，填写正确的核算主体，核算主体有1级-油站供应商、2级-服务区，请务必选择正确，否则将不能通过！'+'<br/>' +
                        '2、当选择核算主体是供应商或平台时，服务区编码和服务区名称请为空。'+'<br/>'+
                        '3、返利类型分为：充值返利、消费返利，只能选择一个！'+'<br/>'+
                        '4、消费开始时间和消费结束时间格式举例：2021-01-12 08:00:00。若返利类型是充值返利，这两列数据请为空！<br/>'+
                        '5、返利形式分为：现金、积分，只能选一个'+'<br/>'+
                        '6、充值单号：多个单号之间用英文,分开。若没有请为空。<br/>'+
                        '7、返利到账类型分为：充到账户余额、不充到账户余额，只能选一个。当返利形式为积分时，返利到账类型只能选择不充到账户余额！'+'<br/>'+
                        '8、录入备注选填。</font>'
                },
                {
                    //添加横线
                    xtype: 'compositefield',
                    style: "margin-top:10px; height:1px;border:none;border-top:1px solid #8db2e3",
                    items: [
                        {
                            xtype: 'displayfield',
                            value: '',
                        },
                    ],
                },
                {
                    xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
                    id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
                    anchor: '100%'
                }
            ]
        });
        var batchImport = new Ext.Window({
            title: '批量导入',
            width: 650,
            height: 350,
            minWidth: 300,
            minHeight: 600,
            closeAction: 'destroy',
            modal: true,
            layout: 'fit',
            plain: true,
            bodyStyle: 'padding:5px;',
            buttonAlign: 'center',
            items: form,
            buttons: [{
                text: '导入',
                handler: function () {
                    if (form.form.isValid()) {
                        if (Ext.getCmp('userfile').getValue() === '') {
                            Ext.Msg.alert('系统提示', '请选择你要上传的文件');
                            return;
                        }

                        console.log('========导入打印=========');
                        console.log(Ext);
                        batchImport.getEl().mask("数据导入中...");
                        form.getForm().submit({
                            url: getUrl(controlName,'batchImport'),
                            success: function (form, action) {
                                var r = Ext.decode(action.response.responseText).msg;
                                batchImport.getEl().unmask();
                                Ext.MessageBox.alert('提示', r);
                                batchImport.destroy();
                                main_store.removeAll();
                                main_store.load();
                            },
                            failure: function (form, action) {
                                var r = Ext.decode(action.response.responseText).msg;
                                batchImport.getEl().unmask();
                                Ext.MessageBox.alert('提示', r);
                            }
                        })
                    }
                }
            }, {
                text: '关闭',
                handler: function () {
                    batchImport.destroy();
                }
            }]
        });
        batchImport.show();
    }
}
var oilTest = new oiltest();