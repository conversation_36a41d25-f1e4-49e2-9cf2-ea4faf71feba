var crow;
var pageSize = 50;
var winClose;
var controlName = 'oil_pool_account';
//资源搜索
var store_main = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=' + controlName + '&f=accountSearch', method: "POST"}),
    reader: new Ext.data.JsonReader({
        totalProperty: 'data.total',
        root: 'data.result'
    }, [
        {name: 'id'},
        {name: 'orgroot'},
        {name: 'account_no'},
        {name: 'orgname'},
        {name: 'money'},
        {name: 'charge_total'},
        {name: 'assign_total'},
        {name: 'fanli_total'},
        {name: 'vice_card_num'},
        {name: 'updatetime'},
        {name: 'orgcode'}
    ]),
});

//选择行
var sm_service = new Ext.grid.RowSelectionModel({
    singleSelect: false,
    listeners: {
        'selectionchange': function (data) {
            if (data.hasSelection()) {
                crow = data.selections.itemAt(0).data;
                var sm = grid_service.getSelectionModel();
                var info = sm.getSelections();
                var status = info[0].get('status');
                (Ext.getCmp('btnviewdetail')) ? Ext.getCmp('btnviewdetail').enable() : '';//账户明细
            }else {
                (Ext.getCmp('btnviewdetail')) ? Ext.getCmp('btnviewdetail').disable() : '';//账户明细
            }
        }
    }
});

//定义列表
var cm_service = new Ext.grid.ColumnModel({
    defaults: {
        width: 120,
        align: 'right',
        sortable: false
    }, columns: [
        new Ext.grid.RowNumberer(),
        {header: '<div align=center>账号</div>', dataIndex: 'account_no', sortable: true, width: 150, align: 'left'},
        {header: '<div align=center>顶级机构名称</div>', dataIndex: 'orgroot', sortable: true, width: 220, align: 'left'},
        {header: '<div align=center>机构名称</div>', dataIndex: 'orgname', sortable: true, width: 220, align: 'left'},
        {
            header: '<div align=center>现金余额</div>',
            dataIndex: 'money',
            sortable: true,
            width: 160,
            renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                var ss = record.data.money;
                ss = parseFloat(ss);
                return ss.toFixed(2);
            }
        },
        {
            header: '<div align=center>累计分配</div>',
            dataIndex: 'assign_total',
            sortable: true,
            width: 110,
            renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                var ss = record.data.assign_total;
                ss = parseFloat(ss);
                return ss.toFixed(2);
            }
        },
        {
            header: '<div align=center>累计现金返利</div>',
            dataIndex: 'fanli_total',
            sortable: true,
            width: 110,
            renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                var ss = record.data.fanli_total;
                if (ss == null) {
                    return '0.00';
                }
                ss = parseFloat(ss);
                return ss.toFixed(2);
            }
        },
        {header: '<div align=center>副卡数量</div>', dataIndex: 'vice_card_num', sortable: true, width: 110},
        {header: '<div align=center>最后分配时间</div>', dataIndex: 'updatetime', sortable: true, width: 160},
    ]
});

var pageTool = new Ext.PagingToolbar({
    plugins: new Ext.ux.plugin.PagingToolbarResizer,
    pageSize: pageSize,
    displayInfo: true,
    displayMsg: '当前记录数: {0} - {1} 总记录数: {2}',
    emptyMsg: '没有符合条件的记录',
    store: store_main
});
//创建grid
var grid_service = new Ext.grid.GridPanel({
    loadMask: true,
    enableColumnMove: false,
    enableColumnResize: true,
    title: '撬装账户查询',
    stripeRows: true,//斑马颜色
    autoScroll: true,
    store: store_main,
    cm: new Ext.grid.ColumnModel({
        defaults: {
            width: 120,
            align: 'right',
            sortable: false
        }, columns: [
            //sm_service,
            new Ext.grid.RowNumberer(),
            {header: '<div align=center>账号</div>', dataIndex: 'account_no', sortable: true, width: 150, align: 'left'},
            {header: '<div align=center>顶级机构名称</div>', dataIndex: 'orgroot', sortable: true, width: 220, align: 'left'},
            {header: '<div align=center>机构名称</div>', dataIndex: 'orgname', sortable: true, width: 220, align: 'left'},
            {
                header: '<div align=center>现金余额</div>',
                dataIndex: 'money',
                sortable: true,
                width: 160,
                renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                    var ss = record.data.money;
                    ss = parseFloat(ss);
                    return ss.toFixed(2);
                }
            },
            {
                header: '<div align=center>累计分配</div>',
                dataIndex: 'assign_total',
                sortable: true,
                width: 110,
                renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                    var ss = record.data.assign_total;
                    ss = parseFloat(ss);
                    return ss.toFixed(2);
                }
            },
            {
                header: '<div align=center>累计现金返利</div>',
                dataIndex: 'fanli_total',
                sortable: true,
                width: 110,
                renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                    var ss = record.data.fanli_total;
                    if (ss == null) {
                        return '0.00';
                    }
                    ss = parseFloat(ss);
                    return ss.toFixed(2);
                }
            },
            {header: '<div align=center>副卡数量</div>', dataIndex: 'vice_card_num', sortable: true, width: 110},
            {header: '<div align=center>最后分配时间</div>', dataIndex: 'updatetime', sortable: true, width: 160},
        ]
    }),
    sm: sm_service,
    tbar: [],
    //分页
    bbar: pageTool
});
function btnInit() {
    (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
    (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
    (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
    (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
    (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
}