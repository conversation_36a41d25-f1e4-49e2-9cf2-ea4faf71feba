var requireTip = {xtype: 'displayfield', html: '<font color="red" class="requireTip">*</font>'};
//余额刷新
function balanceRefresh(vice_no) {
    var sh;
    function refresh() {
        Ext.Ajax.request({
            url: getUrl('oil_card_change', 'getViceCardRemain'),
            params: {vice_no: vice_no},
            method: 'POST',
            success: function (response, options) {
                var data = Ext.decode(response.responseText).data;
                console.log('data--',data)
                var start_time = localStorage.getItem("oilcardlossStartTime");
                console.log('start_time--'+start_time)
                if(data.remain_get_time > start_time){
                    clearInterval(sh);
                    Ext.getCmp('_card_remain').setValue(data.card_remain);
                    Ext.getCmp('card_remain').setValue(data.card_remain);
                    Ext.getCmp('_reserve_remain').setValue(data.reserve_remain);
                    Ext.getCmp('reserve_remain').setValue(data.reserve_remain);
                    Ext.getCmp('remain_get_time').setValue('<span style="color:#20c120;">'+data.remain_get_time+'</span>');
                }
            }
        });
    }
    sh=setInterval(refresh,1000);
}
/**
 * 定义添加组件容器类
 */
var addPanel = {
    id: '',
    formId: '',
    winId: '',
    //初始化容器
    init: function (params) {
        if (this.id) {//编辑
            Ext.getCmp(this.formId).getForm().loadRecord({data: params.data});
            if(params.get('status') < 10){
                Ext.getCmp('old_vice_no').fireEvent('blur');
            }
        }
    },
    //获取组件容器
    getPanel: function (formId, winId, id) {
        this.id = id;
        this.formId = formId;
        this.winId = winId;
        var panel = [
            {
                layout:"column",
                style:'padding-bottom:10px;',
                items:[
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items:  {
                            xtype:'button',
                            text:'实时余额刷新',
                            id:'balanceRefreshBtn',
                            style:'padding-left:50px;',
                            disabled:true,
                            handler:function () {
                                var vice_no = Ext.getCmp('old_vice_no').getValue();
                                if(!vice_no){
                                    Ext.Msg.alert('提示','必须先填原卡号');
                                    return;
                                }
                                Ext.Ajax.request({
                                    url: getUrl('oil_card_vice', 'balanceRefresh'),
                                    params: {viceNos: vice_no},
                                    method: 'POST',
                                    success: function (response, options) {
                                        var result = Ext.decode(response.responseText);
                                        Ext.MessageBox.alert('提示', result.msg);
                                        if(result.code == 0){
                                            var v7 = new Date();//获取当前日期
                                            var date = Ext.util.Format.date(v7, "Y-m-d H:i:s");
                                            localStorage.setItem("oilcardlossStartTime", date);//设置前端缓存
                                            balanceRefresh(vice_no);
                                        }
                                    }
                                });
                            }
                        }
                    },
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        labelAlign: 'right',
                        labelWidth: 150,
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: "副卡余额更新时间",
                            id: 'remain_get_time',
                            name: 'remain_get_time'
                        }
                    },
                ]
            },
            {//第1行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype:'compositefield',
                            items:[{
                                xtype: 'combo',
                                width: 150,
                                fieldLabel: '变更类型',
                                id: 'change_type',
                                hiddenName: 'change_type',
                                mode: 'local',
                                triggerAction: 'all',
                                displayField: 'value',
                                valueField: 'key',
                                allowBlank: false,
                                blankText: '不能为空',
                                store: getChangeType
                            },requireTip]
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype:'compositefield',
                            items:[{
                                xtype: "textfield",
                                fieldLabel: "原卡号",
                                id:'old_vice_no',
                                name:'old_vice_no',
                                allowBlank: false,
                                blankText: '不能为空',
                                width: 150,
                                listeners: {
                                    blur: function (field) {
                                        var vice_no = Ext.getCmp('old_vice_no').getValue();
                                        if(!vice_no){
                                            return;
                                        }
                                        Ext.Ajax.request({
                                            url: getUrl('oil_card_change','getViceNoInfo'),
                                            params: { vice_no: vice_no},
                                            success: function(response, opts) {
                                                var result = Ext.decode(response.responseText);
                                                if(result.code == 0){
                                                    Ext.getCmp(formId).getForm().loadRecord({data: result.data});
                                                    if(result.data.oil_com == 1 || result.data.oil_com == 2){
                                                        Ext.getCmp('balanceRefreshBtn').enable();//激活余额实时刷新按钮
                                                    }else{
                                                        Ext.getCmp('balanceRefreshBtn').disable();//禁用余额实时刷新按钮
                                                    }
                                                    Ext.getCmp('button_ok').enable();//激活确定按钮
                                                }else{
                                                    Ext.getCmp('balanceRefreshBtn').disable();//禁用余额实时刷新按钮
                                                    Ext.getCmp('button_ok').disable();//禁用确定按钮
                                                    Ext.getCmp(formId).getForm().loadRecord({data: {
                                                        old_vice_no:'',
                                                        _oil_com:'',
                                                        oil_com:'',
                                                        _card_main_no:'',
                                                        card_main_no:'',
                                                        _active_region:'',
                                                        active_region:'',
                                                        _operators:'',
                                                        operators:'',
                                                        _org_name:'',
                                                        org_name:'',
                                                        _root_org_name:'',
                                                        root_org_name:'',
                                                        _old_vice_status:'',
                                                        old_vice_status:'',
                                                        _card_remain:'',
                                                        card_remain:'',
                                                        _reserve_remain:'',
                                                        reserve_remain:'',
                                                        _old_vice_password:'',
                                                        old_vice_password:'',
                                                        remain_get_time:'',
                                                        exclusive_custom:'',
                                                        _exclusive_custom:''
                                                    }});
                                                    Ext.Msg.alert('提示',result.msg);
                                                }
                                            },
                                            failure: function(response, opts) {
                                                // console.log('server-side failure with status code ' + response.status);
                                            }
                                        });
                                    },
                                    focus:function (field) {
                                        Ext.getCmp('button_ok').disable();//禁用确定按钮
                                    }
                                }
                            },requireTip]
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "原卡状态",
                                id:'_old_vice_status',
                                width: 150
                            },
                            {xtype:'hidden',name:'old_vice_status'}
                        ]
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "绑定车牌号",
                                id:'_old_truck_no',
                                width: 150
                            },
                            {xtype:'hidden',name:'old_truck_no'}
                        ]
                    }]
            },
            {//第2行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "油卡类型",
                                id:'_oil_com',
                                width: 150
                            },
                            {xtype:'hidden',name:'oil_com'},
                            {xtype: 'hidden', name: 'exclusive_custom'},
                        ]
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "主卡号",
                                id:'_card_main_no',
                                width: 150
                            },
                            {xtype:'hidden',name:'card_main_no'}
                        ]
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "开卡地区",
                                id:'_active_region',
                                width: 150
                            },
                            {xtype:'hidden',name:'active_region'}
                        ]
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "主卡运营商",
                                id:'_operators',
                                width: 150
                            },
                            {xtype:'hidden',name:'operators'}
                        ]
                    }]
            },
            {//第3行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "所属机构",
                                id:'_org_name',
                                width: 150
                            },
                            {xtype:'hidden',name:'org_name'},
                            {xtype:'hidden',name:'orgcode'}
                        ]
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "顶级机构",
                                id:'_root_org_name',
                                width: 150
                            },
                            {xtype:'hidden',name:'root_org_name'}
                        ]
                    }, {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype:'compositefield',
                            items:[{
                                xtype: "textfield",
                                fieldLabel: "收件人信息",
                                name:'receiver_info',
                                allowBlank: false,
                                blankText: '不能为空',
                                width: 428
                            },requireTip]
                        }
                    }]
            },
            {//第4行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "原卡余额",
                                id:'_card_remain',
                                width: 150
                            },
                            {xtype:'hidden',name:'card_remain',id:'card_remain'}
                        ]
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "原卡备付金",
                                id:'_reserve_remain',
                                width: 150
                            },
                            {xtype:'hidden',name:'reserve_remain',id:'reserve_remain'}
                        ]
                    }, {
                        columnWidth: 0.16,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "专属客服",
                                id:'_exclusive_custom',
                                // width: 100,
                            },{xtype:'hidden',name:'exclusive_custom',id:'exclusive_custom'}
                        ]
                    },{
                        columnWidth: 0.15,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "原密码",
                                id:'_old_vice_password',
                                // width: 80
                            },{xtype:'hidden',name:'old_vice_password',id:'old_vice_password'}
                        ]
                    }, {
                        columnWidth: 0.19,
                        layout: "form",
                        items: {
                            xtype:'compositefield',
                            items:[{
                                xtype: "textfield",
                                fieldLabel: "新密码",
                                name:'new_vice_password',
                                allowBlank: false,
                                blankText: '不能为空',
                                width: 82
                            },requireTip]
                        }
                    }]
            },
            {//第5行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype:'compositefield',
                            items:[{
                                xtype: "textfield",
                                fieldLabel: "原卡信息",
                                name:'old_card_info',
                                allowBlank: false,
                                blankText: '不能为空',
                                width: 428
                            },requireTip]
                        }
                    },
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype:'compositefield',
                            items:[{
                                xtype: "textfield",
                                fieldLabel: "新卡信息",
                                name:'new_card_info',
                                allowBlank: false,
                                blankText: '不能为空',
                                width: 428
                            },requireTip]
                        }
                    }]
            },
            {//第6行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "寄回客户快递信息",
                            name:'express_info',
                            width: 428
                        }
                    },
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "客户寄出快递信息",
                            name:'customer_express_info',
                            width: 428
                        }
                    }]
            },
            {//第6行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "卡务快递信息",
                            name:'card_express_info',
                            width: 428
                        }
                    },
                    ]
            },
            {//第7行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "备注/内",
                            name:'remark',
                            width: 983
                        }
                    }]
            },
            {//第9行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "备注/外",
                            name:'remark_work',
                            width: 983
                        }
                    }]
            }
        ];

        return panel;
    },
    //获取组件容器(审核后)
    getPanelAfterAudit: function (formId, winId, id) {
        this.id = id;
        this.formId = formId;
        this.winId = winId;
        var panel = [
            {//第1行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype:'displayfield',
                            fieldLabel: "变更类型",
                            name:'_change_type'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype:'displayfield',
                            fieldLabel: "原卡号",
                            name:'old_vice_no'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "原卡状态",
                            name:'old_vice_status'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "绑定车牌号",
                            name:'old_truck_no',
                            width: 150
                        }
                    }]
            },
            {//第2行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "油卡类型",
                                name:'_oil_com',
                                width: 150
                            }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "主卡号",
                                name:'card_main_no',
                                width: 150
                            }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "开卡地区",
                                name:'active_region',
                                width: 150
                            }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "主卡运营商",
                                name:'operators',
                                width: 150
                            }
                    }]
            },
            {//第3行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "所属机构",
                                name:'org_name',
                                width: 150
                            }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "顶级机构",
                                name:'root_org_name',
                                width: 150
                            }
                    }, {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype:'compositefield',
                            items:[{
                                xtype: "textfield",
                                fieldLabel: "收件人信息",
                                name:'receiver_info',
                                allowBlank: false,
                                blankText: '不能为空',
                                width: 428
                            },requireTip]
                        }
                    }]
            },
            {//第4行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "原卡余额",
                                name:'card_remain',
                                width: 150
                            }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "原卡备付金",
                                name:'reserve_remain',
                                width: 150
                            }
                    }, {
                        columnWidth: 0.17,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "专属客服",
                                id:'_exclusive_custom',
                                // width: 60,
                            }
                        ]
                    },{
                        columnWidth: 0.15,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "原密码",
                                name:'old_vice_password',
                                // width: 60
                            }
                    }, {
                        columnWidth: 0.18,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "新密码",
                                name:'new_vice_password',
                                width: 82
                            }
                    }]
            },
            {//第5行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                                    xtype: 'displayfield',
                                    width: 428,
                                    fieldLabel: '原卡信息',
                                    name: 'old_card_info'
                                }
                    },
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                                xtype: "displayfield",
                                fieldLabel: "新卡信息",
                                width: 428,
                                name:'new_card_info'
                            }
                    }]
            },
            {//第6行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: 'textfield',
                            width: 428,
                            fieldLabel: '寄回客户快递信息',
                            name: 'express_info'
                        }
                    },
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "客户寄出快递信息",
                            width: 428,
                            name:'customer_express_info'
                        }
                    }]
            },
            {//第6行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: 'textfield',
                            width: 428,
                            fieldLabel: '卡务快递信息',
                            name: 'card_express_info'
                        }
                    },
                ]
            },
            {//第7行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "备注/内",
                            name:'remark',
                            width: 983
                        }
                    }]
            },
            {//第9行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "备注/外",
                            name:'remark_work',
                            width: 983
                        }
                    }]
            }
        ];

        return panel;
    },
    //获取组件容器(查看)
    getShowPanel: function (formId, winId, id) {
        this.id = id;
        this.formId = formId;
        this.winId = winId;
        var panel = [
            {//第1行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype:'displayfield',
                            fieldLabel: "变更类型",
                            name:'_change_type'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype:'displayfield',
                            fieldLabel: "原卡号",
                            name:'old_vice_no'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "原卡状态",
                            name:'old_vice_status'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "绑定车牌号",
                            name:'old_truck_no',
                            width: 150
                        }
                    }]
            },
            {//第2行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "油卡类型",
                            name:'_oil_com',
                            width: 150
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "主卡号",
                            name:'card_main_no',
                            width: 150
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "开卡地区",
                            name:'active_region',
                            width: 150
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "主卡运营商",
                            name:'operators',
                            width: 150
                        }
                    }]
            },
            {//第3行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "所属机构",
                            name:'org_name',
                            width: 150
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "顶级机构",
                            name:'root_org_name',
                            width: 150
                        }
                    }, {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "收件人信息",
                            name:'receiver_info',
                            width: 428
                        }
                    }]
            },
            {//第4行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "原卡余额",
                            name:'card_remain',
                            width: 150
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "原卡备付金",
                            name:'reserve_remain',
                            width: 150
                        }
                    }, {
                        columnWidth: 0.16,
                        layout: "form",
                        items: [
                            {
                                xtype: "displayfield",
                                fieldLabel: "专属客服",
                                id:'_exclusive_custom',
                                // width: 100,
                            }
                        ]
                    },{
                        columnWidth: 0.15,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "原密码",
                            name:'old_vice_password',
                            // width: 80
                        }
                    }, {
                        columnWidth: 0.19,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "新密码",
                            name:'new_vice_password',
                            width: 82
                        }
                    }]
            },
            {//第5行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            width: 428,
                            fieldLabel: '原卡信息',
                            name: 'old_card_info'
                        }
                    },
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "新卡信息",
                            width: 428,
                            name:'new_card_info'
                        }
                    }]
            },
            {//第6行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            width: 428,
                            fieldLabel: '寄回客户快递信息',
                            name: 'express_info'
                        }
                    },
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "客户寄出快递信息",
                            width: 428,
                            name:'customer_express_info'
                        }
                    }]
            },
            {//第6行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            width: 428,
                            fieldLabel: '卡务快递信息',
                            name: 'card_express_info'
                        }
                    },
                ]
            },
            {//第7行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "备注/内",
                            name:'remark',
                            width: 983
                        }
                    }]
            },
            {//第9行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "备注/外",
                            name:'remark_work',
                            width: 983
                        }
                    }]
            }
        ];

        return panel;
    },
    //提交表单
    submit: function () {
        var _form = Ext.getCmp(this.formId).getForm();
        var that = this;
        if (_form.isValid()) {
            var url = '';
            if (this.id) {
                url = getUrl(controlName, 'edit');
            } else {
                url = getUrl(controlName, 'create');
            }
            _form.submit({
                url: url,
                waitMsg: 'Saving Data...',
                params: {id:that.id},
                success: function (form, action) {
                    Ext.MessageBox.alert("消息!", action.result.msg);
                    main_store.removeAll();
                    main_store.load();
                    oilTest.closeWin(that.winId)
                },
                failure: function (form, action) {
                    Ext.MessageBox.alert("消息!", action.result.msg);
                }
            });
        } else {
            Ext.MessageBox.alert("提示", '输入有误，请检查');
        }
    }
}