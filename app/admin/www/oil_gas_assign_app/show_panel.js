var show_win;//添加窗口

var show_form_panel = new Ext.form.FormPanel({
    region: 'center',
    hideLabels: true,
    trackResetOnLoad: true,
    bodyStyle: 'padding: 10px',
    height: 60,
    items: [
        {// 第一行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '单号：',
                    style: 'padding-left: 24px'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_no',
                    width: 150,
                    value: ''
                },
                {
                    xtype: 'displayfield',
                    value: '机构：',
                    style: 'padding-left: 24px'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_org_id',
                    width: 150,
                    height: 18,
                    autoScroll: true
                },
                {
                    xtype: 'displayfield',
                    value: '分配总金额：',
                    style: 'padding-left: 36px'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_money_total',
                    width: 150,
                    value: ''
                },
                {
                    xtype: 'displayfield',
                    value: '分配总积分：',
                    style: 'padding-left: 24px'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_jifen_total',
                    width: 145,
                    value: ''
                },
            ]
        },
        {// 第二行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '分配卡数：'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_assign_num',
                    width: 150,
                },
                {
                    xtype: 'displayfield',
                    value: '状态：',
                    style: 'padding-left: 24px'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_status',
                    value: '',
                    width: 150
                },
                {
                    xtype: 'displayfield',
                    value: '账户余额：',
                    style: 'padding-left: 48px'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_money',
                    value: '',
                    width: 150
                },
                {
                    xtype: 'displayfield',
                    value: '数据来源：',
                    style: 'padding-left: 36px'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_data_from',
                    width: 150,
                    value: ''
                }
            ]
        },
        {// 第三行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '申请时间：',
                    style: 'padding-left: 24px'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_apply_time_field',
                    width: 150,
                    height: 18
                }
            ]
        },
        {// 第四行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '备注：',
                    style: 'padding-left: 24px'
                },
                {
                    xtype: 'displayfield',
                    id: 'k_remark',
                    width: 890,
                    height: 18,
                    autoScroll: true
                }
            ]
        },
    ],
    buttons: [{
        text: "关闭",
        handler: function () {
            show_win.hide();
        }
    }]
});

//主窗体
if (!show_win) {
    show_win = new Ext.Window({
        layout: "border",
        width: 1000,
        height: 550,
        title: '分配申请维护',
        closeAction: 'hide',
        plain: true,
        modal: true,
        items: [show_form_panel]
    });
}

