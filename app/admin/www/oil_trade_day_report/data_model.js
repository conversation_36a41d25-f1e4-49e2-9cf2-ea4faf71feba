/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_trade_day_report';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","org_id","orgcode","orgname","cdcid","total_amount","total_count","total_num","ecard_total_amount",
            "ecard_total_count","ecard_total_num","pcard_total_amount","pcard_total_count","pcard_total_num","day",
            "createtime","updatetime",        ]
    ),
});

var getOilOrg = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_org&f=getOilOrg', method: "POST"}),
    // autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id', 'orgcode', 'org_name'])
});