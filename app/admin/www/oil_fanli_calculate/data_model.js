 var pagesize = 50;
 var controlName = 'oil_fanli_calculate';

//获取列表信息
 var calculateStore = new Ext.data.Store({
     proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=' + controlName + '&f=search', method: "POST"}),
     reader: new Ext.data.JsonReader(
         {
             totalProperty: 'data.total',
             root: 'data.data'
         },
         [
             'fanli_no', 'starttime', 'endtime', 'createtime', 'status', 'last_operator', 'creator_name','totalMoney','totalJifen'
         ]
     ),
 });

//分配申请详情
 var detailStore=new Ext.data.Store({
     proxy:new Ext.data.HttpProxy({url:'../inside.php?t=json&m='+ controlName +'&f=getDetails',method:"POST"}),
     //params:{start:0,limit:5},
     reader:new Ext.data.JsonReader({
             totalProperty:'total',
             root:'data'},
         ['main_no', 'createtime', 'fanli_money', 'fanli_jifen', 'oil_com', 'oil_com_name', 'oil_type', 'fanli_way',
             'fanli_coe', 'org_name', 'province','orgcode','orgroot','_orgroot']
     )
 });

 //油品机构列表
var getOilOrg = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_card_vice&f=getOilOrg', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        tatalProperty: '',
        root: ''
    }, ['id', 'org_name', 'orgcode', 'orgname'])
});


