var bottomTitle = '';

var detail_sales_grid = new Ext.grid.GridPanel({
    region: 'center',
    loadMask: true,
    cm: new Ext.grid.ColumnModel([
        {header: '单据编号', dataIndex: 'sale_no', width: 160},
        {header: '行号', dataIndex: 'line_code', width: 70},
        {header: '货物名称', dataIndex: 'oil_type_value', width: 100},
        {header: '二级分类', dataIndex: 'oil_sec_type_value', width: 100},
        {header: '票面油品', dataIndex: 'sku', width: 160},//规格型号
        {header: '计量单位', dataIndex: 'unit', width: 80},
        {header: '单价', dataIndex: 'unit_price', width: 120},
        {header: '数量', dataIndex: 'num', width: 110},
        {header: '浪费库存', dataIndex: 'waste_num', width: 110},
        {header: '金额', dataIndex: 'amount', width: 120},
        {header: '折扣金额', dataIndex: 'disamount', width: 110},
        {header: '单价（折扣后）', dataIndex: 'discount_price', width: 110},
        {header: '税率', dataIndex: 'tax_rate', width: 80, renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
            return value * 100 + '%';
        }},
        {header: '税收编码', dataIndex: 'tax_no', width: 160},
        {header: '最后修改人', dataIndex: 'last_operator', width: 120},
        {header: '创建时间', dataIndex: 'createtime', width: 120},
    ]),
    store: detail_sales_store,
    //分页
    bbar: new Ext.PagingToolbar({
        plugins: new Ext.ux.plugin.PagingToolbarResizer,
        pageSize: pagesize,
        displayInfo: true,
        displayMsg: '当前记录数: {0} - {1} 总记录数: {2}',
        emptyMsg: '没有符合条件的记录',
        store: detail_sales_store
    }),
    tbar: []
});

var detail_grid = new Ext.grid.GridPanel({
    region: 'center',
    loadMask: true,
    cm: new Ext.grid.ColumnModel([
        {header: '销售单据编号', dataIndex: 'sale_no', width: 100},
        {header: '发票申请单号', dataIndex: 'receipt_apply_no', width: 100},
        {header: '发票号码', dataIndex: 'receipt_no', width: 80},
        {header: '关联发票号码', dataIndex: 'receipt_no_relation', width: 100},
        {header: '发票代码', dataIndex: 'receipt_code', width: 80},
        {
            header: '发票类型',
            dataIndex: 'receipt_type_text',
            width: 60,
            align: "center",
        },
        {
            header: '开票状态',
            dataIndex: 'status',
            width: 60,
            align: "center",
            renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                var statusText = '';
                switch (parseInt(value)) {
                    case 10:
                        statusText = '<span style="color: blue">' + record.data.status_text + '</span>';
                        break;
                    case 20:
                        statusText = '<span style="color:red;">' + record.data.status_text + '</span>';
                        break;
                    default:
                        statusText = '<span style="color:purple;">' + record.data.status_text + '</span>';
                }

                return statusText;
            }
        },
        {
            header: '红蓝票',
            dataIndex: 'color_type',
            width: 60,
            align: "center",
            renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                var statusText = '';
                switch (parseInt(value)) {
                    case 10:
                        statusText = '<span style="color: red">' + record.data.color_type_text + '</span>';
                        break;
                    case 20:
                        statusText = '<span style="color:blue;">' + record.data.color_type_text + '</span>';
                        break;
                    default:
                        statusText = '<span style="color:orangered;">' + record.data.color_type_text + '</span>';
                }

                return statusText;
            }
        },
        {
            header: '勾稽状态',
            dataIndex: 'choice_status',
            width: 80,
            align: "center",
            renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                return record.data.choice_status_text;
            }
        },
        {header: '开票时间', dataIndex: 'receipt_time', width: 120},
        {header: '油品种类', dataIndex: 'oil_type', width: 80},
        {header: '税率', dataIndex: 'tax_rate', width: 60, align: "right",renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
                return value.toFixed(0) + '%';
            }},
        {
            header: '税额',
            dataIndex: 'tax_amount',
            width: 80,
            align: "right",
        },
        {
            header: '不含税金额',
            dataIndex: 'no_tax_amount',
            width: 100,
            align: "right",
        },
        {
            header: '合计金额',
            dataIndex: 'receipt_amount',
            width: 100,
            align: "right",
        },
        {header: '销售方名称', dataIndex: 'seller_name', width: 180},
        {header: '发票抬头(购买方名称)', dataIndex: 'receipt_title', width: 180},
        {header: '纳税人识别号', dataIndex: 'taxpayer_no', width: 140},
        {header: '购方地址电话', dataIndex: 'addr_tel', width: 160},
        {header: '购方银行账号', dataIndex: 'bank_account', width: 300},
        {header: '开票人', dataIndex: 'drawer', width: 60},
        {header: '收款人', dataIndex: 'payee', width: 60},
        {header: '复核人', dataIndex: 'checker', width: 60},
        {header: '创建人', dataIndex: 'creator_name', width: 60},
        {header: '最后修改人', dataIndex: 'last_operator', width: 80},
        {header: '创建时间', dataIndex: 'createtime', width: 120},
        {header: '最后修改时间', dataIndex: 'updatetime', width: 140},
        {header: '金税备注', dataIndex: 'golden_remark', width: 180},
        {header: '后台备注', dataIndex: 'admin_remark', width: 180},
    ]),
    store: detail_store,
    //分页
    bbar: new Ext.PagingToolbar({
        plugins: new Ext.ux.plugin.PagingToolbarResizer,
        pageSize: pagesize,
        displayInfo: true,
        displayMsg: '当前记录数: {0} - {1} 总记录数: {2}',
        emptyMsg: '没有符合条件的记录',
        store: detail_store
    }),
    tbar: []
});

var additional_grid = new Ext.grid.GridPanel({
    region: 'center',
    loadMask: true,
    cm: new Ext.grid.ColumnModel([
        {header : '交易id',dataIndex : 'trades_id',width : 120},
        {header : 'GMS流水类型',dataIndex : 'from_txt',width : 90},
        {header : '货物名称',dataIndex : 'oil_type_txt',width : 100},
        {header : '二级分类',dataIndex : 'oil_sec_type_txt',width : 100},
        {header : '规格型号',dataIndex : 'oil_name',width : 180},
        {header : '单价',dataIndex : 'trade_price',width : 100},
        {header: '已开数量', dataIndex: 'receipt_num', width: 110, renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
            return '<span style="color:orangered">'+value+'</span>';
        }},
        {header: '已开金额', dataIndex: 'receipt_money', width: 110, renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
            return '<span style="color:orangered">'+value+'</span>';
        }},
        {header: '已开返利', dataIndex: 'receipt_fanli', width: 110, renderer: function (value, cellMeta, record, rowIndex, columnIndex, store) {
            return '<span style="color:orangered">'+value+'</span>';
        }},
        {header : '原始数量',dataIndex : 'trade_num',width : 110},
        {header : '原始金额',dataIndex : 'trade_money',width : 110},
        {header : '原始返利',dataIndex : 'use_fanli_money',width : 110},
    ]),
    store: invoice_trades_store,
    sm : new Ext.grid.RowSelectionModel({
        singleSelect: true,
        listeners: {
            selectionchange: function(data) {
                if (data.getCount()){
                    (Ext.getCmp('del_trades')) ? Ext.getCmp('del_trades').enable() : '';//删除按钮
                }else{
                    (Ext.getCmp('del_trades')) ? Ext.getCmp('del_trades').disable() : '';//删除按钮
                }
            }
        }
    }),
    //分页
    bbar: [],
    tbar: new Ext.Toolbar({
        items: [
            {text: '添加消费', iconCls: 'silk-add', id: 'add_trades', disabled: false, handler: function(){
                addTrades();
            }},
            {text: '删除消费', iconCls: 'silk-delete', id: 'del_trades', disabled: true, handler: function(){
                delTrades();
            }},
        ]
    })
});

//底部表格
var bottom_panel = new Ext.TabPanel({
    region:"south",
    activeTab:0,
    frame:true,
    height:210,
    items:
        [
            {
                xtype:'panel',
                title: '发票销售明细',
                layout:'border',
                items:[
                    detail_sales_grid,
                ]
            },
            {
                xtype:'panel',
                title: '发票信息',
                layout:'border',
                items:[
                    detail_grid,
                ]
            },
            {
                xtype:'panel',
                title: '附加消费',
                layout:'border',
                items:[
                    additional_grid
                ]
            },
        ],
    listeners:{
        tabchange:function(tp,p){
            bottomTitle = p.title;
            if(crow){
                loadBottomData(p.title);
            }

        }
    }
});

//加载底部数据
function loadBottomData(flag) {
    if (selectNum == 1) {
        if(flag === '发票销售明细'){
            detail_sales_store.removeAll();
            detail_sales_store.baseParams = {'sales_id':crow.id,'start':0,'limit':500};
            detail_sales_store.load();
        }else if(flag === '发票信息'){
            console.log('切换======发票信息');
            console.log(crow);
            detail_store.removeAll();
            if (crow.receipt_id > 0) {
                detail_store.baseParams = {'id':crow.receipt_id,'start':0,'limit':500};
                detail_store.load();
            }
        }else if(flag === '附加消费'){
            console.log('切换======附加消费');
            console.log(crow);
            invoice_trades_store.removeAll();
            if (crow.sale_no.length > 0) {
                invoice_trades_store.baseParams = {'relation_sale_no':crow.sale_no,'page':1,'limit':10000};
                invoice_trades_store.load();
            }
        }
    }
}

function addTrades() {

    if( crow.can_add_trade != 1 ){
        Ext.MessageBox.alert("系统提示", "请选择外部【未提交】【待开】的【蓝票】");
        return false;
    }

    if(crow.sale_no == '' || crow.sale_no == undefined || crow.length == 0 ){
        Ext.MessageBox.alert("系统提示", "请选择要添加消费的【销售单据】");
        return false;
    }

    var tradeForm = new Ext.form.FormPanel({
        region: 'center',
        buttonAlign: 'center',
        hideLabels: true,
        trackResetOnLoad: true,
        bodyStyle: 'padding: 10px',
        height: 80,
        items: [
            {// 第二行
                xtype: 'compositefield',
                style: 'margin-bottom: 5px;margin-top:20px;',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '交易ID：',
                        style: 'padding-left: 80px'
                    },
                    new Ext.form.ComboBox({
                        xtype: 'combo',
                        width: 300,
                        id: 'trades_id',
                        hiddenName: 'trades_id',
                        allowBlank: false,
                        blankText : '交易ID不能为空',
                        //forceSelection: true,
                        mode: 'remote',
                        minChars: 2,
                        queryParam:'keyword',
                        emptyText : '请选择',
                        triggerAction:'all',
                        displayField: 'show_value',
                        valueField: 'trades_id',
                        store: getReceiptTrades,
                        //enableKeyEvents: true,
                        listeners: {
                            'focus': function () {
                                console.log("addTrades-=-=focus");
                                console.log(crow)
                                getReceiptTrades.baseParams.receipt_apply_id = crow.receipt_apply_id;
                                getReceiptTrades.baseParams.except_sale_no = crow.sale_no;
                                // getReceiptTrades.load();
                            },
                            select: function(combo, record, index){
                                var tradeInfo = record.data;
                                Ext.getCmp("oil_sec_type").setValue(tradeInfo.oil_sec_type_txt);
                                Ext.getCmp("oil_name").setValue(tradeInfo.oil_name);
                                Ext.getCmp("from_txt").setValue(tradeInfo.from_txt);
                                Ext.getCmp("receipt_num").setValue(tradeInfo.receipt_num);
                                Ext.getCmp("receipt_money").setValue(tradeInfo.receipt_money);
                                Ext.getCmp("receipt_fanli").setValue(tradeInfo.receipt_fanli);
                                Ext.getCmp("trade_num").setValue(tradeInfo.trade_num);
                                Ext.getCmp("trade_money").setValue(tradeInfo.trade_money);
                                Ext.getCmp("fanli").setValue(tradeInfo.use_fanli_money);
                            }
                        }
                    }),
                ]
            },
            {// 第三行
                xtype: 'compositefield',
                style: 'margin-bottom: 10px;',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '二级分类：',
                        style: 'padding-left: 5px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'oil_sec_type',
                        width: 90,
                    },
                    {
                        xtype: 'displayfield',
                        value: '规格型号：',
                        style: 'padding-left: 5px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'oil_name',
                        width: 180,
                    },
                    {
                        xtype: 'displayfield',
                        value: '流水类型：',
                        style: 'padding-left: 5px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'from_txt',
                        width: 90,
                    },
                ]
            },
            {// 第三行
                xtype: 'compositefield',
                style: 'margin-bottom: 10px;',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '已开数量：',
                        style: 'padding-left: 5px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'receipt_num',
                        width: 90,
                    },
                    {
                        xtype: 'displayfield',
                        value: '已开金额：',
                        style: 'padding-left: 5px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'receipt_money',
                        width: 180,
                    },
                    {
                        xtype: 'displayfield',
                        value: '已开返利：',
                        style: 'padding-left: 5px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'receipt_fanli',
                        width: 90,
                    },
                ]
            },
            {
                xtype: 'compositefield',
                style: 'margin-bottom: 10px;',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '原始数量：',
                        style: 'padding-left: 5px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'trade_num',
                        width: 90,
                    },{
                        xtype: 'displayfield',
                        value: '原始金额：',
                        style: 'padding-left: 5px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'trade_money',
                        width: 180,
                    },{
                        xtype: 'displayfield',
                        value: '原始返利：',
                        style: 'padding-left: 5px'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'fanli',
                        width: 90,
                    },
                ]
            },
        ],
        buttons:[
            {
                text: '添加消费',
                handler: function(){
                    var _form = tradeForm.getForm();
                    var submitParams  = {sale_no: crow.sale_no,receipt_apply_id:crow.receipt_apply_id,flag:1};
                    if(_form.isValid()){
                        _form.submit({
                            url     : "../inside.php?t=json&m=oil_receipt_invoice_trades&f=setTradeForReceiptSale",
                            waitMsg : 'Saving Data...',
                            success : function(form, action){
                                Ext.MessageBox.alert("系统提示", action.result.msg);
                                tradeWindow.destroy();
                                getReceiptTrades.load({params:{'relation_receipt_no': crow.sale_no}});
                                main_store.removeAll();
                                main_store.load();
                                loadBottomData('关联附加消费')
                            },
                            failure : function (form, action){
                                Ext.MessageBox.alert("系统提示", action.result.msg);
                            },
                            params: submitParams
                        });
                    }
                }
            },
            {
                text: '取消',
                handler: function() {
                    tradeWindow.destroy();
                }
            }
        ]
    });

    var tradeWindow = new Ext.Window({
        layout:"border",
        width: 600,
        height: 250,
        title:'关联附加消费',
        closeAction:'destroy',
        plain: true,
        modal: true,
        items:[tradeForm]
    });
    tradeWindow.show();
}

function delTrades() {

    if( crow.sale_no == '' || crow.sale_no == undefined || crow.length == 0){
        Ext.MessageBox.alert("系统提示", "请选择要删除消费的【销售单据】");
        return false;
    }

    Ext.Msg.confirm('系统提示', '是否删除该条关联的消费记录？', function(btn){
        if(btn === 'yes'){
            var item = additional_grid.getSelectionModel().getSelections()[0].data;
            console.log(item)
            Ext.Ajax.request({
                url: "../inside.php?t=json&m=oil_receipt_invoice_trades&f=setTradeForReceiptSale",
                success: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                    invoice_trades_store.removeAll();
                    invoice_trades_store.baseParams = {'relation_sale_no':crow.sale_no,'page':1,'limit':10000};
                    invoice_trades_store.load();
                    main_store.removeAll();
                    main_store.load();
                    loadBottomData('关联附加消费')
                },
                failure: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                },
                params: {sale_no: crow.sale_no,receipt_apply_id:crow.receipt_apply_id,flag:2,trades_id:item.trades_id},
            });
        }
    });
}
