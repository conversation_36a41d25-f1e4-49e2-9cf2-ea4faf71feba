var lastMonthToday = function(){
    var now=new Date();
    var year = now.getFullYear();//getYear()+1900=getFullYear()
    var month = now.getMonth() +1;//0-11表示1-12月
    var day = now.getDate();
    if(parseInt(month)<10){
        month="0"+month;
    }
    if(parseInt(day)<10){
        day="0"+day;
    }

    now =year + '-'+ month + '-' + day;

    if (parseInt(month) ==1) {//如果是1月份，则取上一年的12月份
        return (parseInt(year) - 1) + '-12-' + day;
    }

    var  preSize= new Date(year, parseInt(month)-1, 0).getDate();//上月总天数
    if (preSize < parseInt(day)) {//上月总天数<本月日期，比如3月的30日，在2月中没有30
        return year + '-' + month + '-01';
    }

    if(parseInt(month) <=10){
        return year + '-0' + (parseInt(month)-1) + '-' + day;
    }else{
        return year + '-' + (parseInt(month)-1) + '-' + day;
    }

}


var date = new Date();
var currentMonth = new Date(date.getFullYear(), date.getMonth(), date.getDate());
var preMonth = lastMonthToday();


var top_panel = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 125,
    items:
        [
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '销售单据：',
                            width: 60,
                        },
                        {
                            xtype: 'textfield',
                            id: 's_sale_no',
                            name: 'sale_no',
                            width: 80
                        },
                        {
                            xtype: 'button',
                            text: '多选',
                            listeners: {
                                'click': function () {
                                    show_search_win('请输入编号，以回车换行：', 's_sale_no', function () {
                                        main_store.removeAll();
                                        main_store.load();
                                    }, ',');
                                }
                            }
                        },
                        {
                            xtype: 'displayfield',
                            value: '机构：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 209,
                            id: 'orgCodeLike',
                            value: '',
                            triggerAction: 'all',
                            hiddenName: 'orgCodeLike',
                            forceSelection: true,
                            mode: 'local',
                            displayField: 'org_name',//显示的值
                            valueField: 'orgcode',//后台接收的key
                            store: getOilOrg,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                            listWidth: 350,
                            listeners: {
                                'focus': function () {
                                    getOilOrg.load();
                                },
                                // 'select': function (combo, record, index) {
                                //     Ext.getCmp('receipt_title_id').setValue('');
                                //     var orgInfo=Ext.getCmp('orgCodeLike').getRawValue().split(" ");
                                //     //加载发票抬头
                                //     getReceiptTitle.removeAll();
                                //     getReceiptTitle.baseParams = {orgcode:orgInfo[0],status:1,searchFlag:'all'};
                                //     getReceiptTitle.load();
                                // },
                                'beforequery': function (e) {
                                    var combo = e.combo;
                                    if (!e.forceAll) {
                                        var input = e.query;
                                        // 检索的正则
                                        var regExp = new RegExp(".*" + input + ".*");
                                        // 执行检索
                                        combo.store.filterBy(function (record, id) {
                                            // 得到每个record的项目名称值
                                            var text = record.get(combo.displayField);
                                            return regExp.test(text);
                                        });
                                        combo.expand();
                                        return false;
                                    }
                                }
                            }
                        }),
                        {//包含顶级
                            xtype: "hidden",
                            name: 'org_flag',
                            value: true
                        },
                        // {
                        //     xtype: 'displayfield',
                        //     value: '发票抬头：'
                        // },
                        // new Ext.form.ComboBox({
                        //     width: 250,
                        //     hiddenName: 'receipt_title_id',
                        //     id: 'receipt_title_id',
                        //     triggerAction: 'all',
                        //     forceSelection: true,
                        //     mode: 'local',
                        //     displayField: 'invoice_name',//显示的值
                        //     valueField: 'id',//后台接收的key
                        //     store: getReceiptTitle,
                        //     enableKeyEvents: true,
                        //
                        // }),

                        {
                            xtype: 'displayfield',
                            value: '申请日期：',
                            width: 60,
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            value: preMonth,
                            name: 'createtimeGe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '~'
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            value: currentMonth,
                            name: 'createtimeLe'
                        },

                        {
                            xtype: 'displayfield',
                            value: '发票类型：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 70,
                            hiddenName: 'receipt_type',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [['10', '专票'], ['20', '普票'],['30', '专票（电子）'], ['40', '普票（电子）'],['50', '数电专票'], ['60', '数电普票']]
                            })
                        },
                        {
                            xtype: 'displayfield',
                            value: '油品种类：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 65,
                            hiddenName: 'oil_type',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [['', '全部'], ['12', '燃油'], ['5', '天然气'], ["6", "尿素"]]
                            })
                        },
                        {
                            xtype: 'displayfield',
                            value: '创建人：',
                            width: 60,
                        },
                        {
                            xtype: 'textfield',
                            width: 65,
                            name: 'creator_name'
                        },
                    ]
            },
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '申请单号：',
                            width: 60,
                        },
                        {
                            xtype: 'textfield',
                            id: 'receipt_apply_no',
                            name: 'receipt_apply_no',
                            width: 80
                        },
                        {
                            xtype: 'button',
                            text: '多选',
                            listeners: {
                                'click': function () {
                                    show_search_win('请输入编号，以回车换行：', 'receipt_apply_no', function () {
                                        main_store.removeAll();
                                        main_store.load();
                                    }, ',');
                                }
                            }
                        },
                        {
                            xtype: 'displayfield',
                            value: '发票抬头：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 209,
                            id: 'receipt_title_id',
                            value: '',
                            triggerAction: 'all',
                            hiddenName: 'receipt_title_id',
                            forceSelection: true,
                            mode: 'remote',
                            queryParam: 'corp_nameLike',
                            minChars: 2,
                            displayField: 'invoice_name',//显示的值
                            valueField: 'id',//后台接收的key
                            store: getReceiptTitle,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                        }),

                        {
                            xtype: 'displayfield',
                            value: '开票日期：'
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            name: 'receipt_timeGe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '~'
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            name: 'receipt_timeLe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '开票状态：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 70,
                            hiddenName: 'receipt_status',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [['10', '待开'], ['20', '已开'], ['30', '待废'], ['40', '已废']]
                            })
                        },
                        {
                            xtype: 'displayfield',
                            value: '开票渠道：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 65,
                            hiddenName: 'open_channel',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [['', '全部'], ['10', '航信开票'], ['30', '开灵开票']]
                            })
                        },
                        {
                            xtype: 'displayfield',
                            value: '修改人：',
                            width: 60,
                        },
                        {
                            xtype: 'textfield',
                            name: 'last_operator',
                            width: 65
                        },
                    ]
            },
            {
                xtype: 'compositefield',
                items:
                    [
                        // 发票号 add at 20190701 by liupj
                        {
                            xtype: 'displayfield',
                            value: '发票号码：',
                            width: 60,
                        },
                        {
                            xtype: 'textfield',
                            id: 'receipt_no',
                            name: 'receipt_no',
                            width: 80
                        },
                        {
                            xtype: 'button',
                            text: '多选',
                            listeners: {
                                'click': function () {
                                    show_search_win('请输入发票号，以回车换行：', 'receipt_no', function () {
                                        main_store.removeAll();
                                        main_store.load();
                                    }, ',');
                                }
                            }
                        },
                        {
                            xtype: 'displayfield',
                            value: '实开金额：',
                            width: 60,
                        },
                        {
                            xtype: "textfield",
                            width: 95,
                            format: 'Y-m-d',
                            name: 'real_amountGe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '~'
                        },
                        {
                            xtype: "textfield",
                            width: 95,
                            format: 'Y-m-d',
                            name: 'real_amountLe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '修改时间：',
                            width: 60,
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            name: 'updatetimeGe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '~'
                        },
                        {
                            xtype: "datefield",
                            width: 90,
                            format: 'Y-m-d',
                            name: 'updatetimeLe'
                        },

                        {
                            xtype: 'displayfield',
                            value: '单据状态：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 70,
                            hiddenName: 'check_status',
                            mode: 'remote',
                            triggerAction: 'all',
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.Store({
                                proxy: new Ext.data.HttpProxy({
                                    url: getUrl(controlName, 'getCheckStatus'),
                                    method: "POST"
                                }),
                                reader: new Ext.data.JsonReader({
                                    totalProperty: '',
                                    root: 'data'
                                }, ['key', 'value']),
                            })
                        },
                        {
                            xtype: 'displayfield',
                            value: '是否开票：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 65,
                            hiddenName: 'is_open',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [['', '全部'], ['2', '否'], ['1', '是']]
                            })
                        },


                        {
                            xtype: 'displayfield',
                            value: '红蓝票：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 65,
                            hiddenName: 'color_type',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [['10', '红票'], ['20', '蓝票']]
                            })
                        }
                    ]
            },
            {
                xtype: 'compositefield',
                items:
                    [ {
                        xtype: 'displayfield',
                        value: '销售方名称：'
                    },
                    new Ext.form.ComboBox({
                        width: 209,
                        id: 'seller_name',
                        value: '',
                        triggerAction: 'all',
                        hiddenName: 'seller_name',
                        forceSelection: true,
                        mode: 'local',
                        displayField: 'company_name',//显示的值
                        valueField: 'company_name',//后台接收的key
                        store: saleCompany,
                        emptyText: '请选择..',
                        enableKeyEvents: true,
                        listWidth: 350,
                        listeners: {
                            'focus': function () {
                                saleCompany.load();
                            },
                            'beforequery': function (e) {
                                var combo = e.combo;
                                if (!e.forceAll) {
                                    var input = e.query;
                                    // 检索的正则
                                    var regExp = new RegExp(".*" + input + ".*");
                                    // 执行检索
                                    combo.store.filterBy(function (record, id) {
                                        // 得到每个record的项目名称值
                                        var text = record.get(combo.displayField);
                                        return regExp.test(text);
                                    });
                                    combo.expand();
                                    return false;
                                }
                            }

                        }
                    }),
                    {
                        xtype: 'displayfield',
                        value: '状态：',
                        width: 60,
                    },
                    {
                        xtype: 'combo',
                        width: 100,
                        hiddenName: 'exception_flag',
                        mode: 'local',
                        triggerAction: 'all',
                        displayField: 'value',
                        valueField: 'key',
                        store: new Ext.data.SimpleStore({
                            fields: ['key', 'value'],
                            data: [
                                ['10', '正常'], 
                                ['20', '单价过低'],
                                ['30', '异常'], 
                                ['31', '关联异常'],
                                ['-1', '空']
                            ]
                        })
                    },
                
                     {
                        xtype: 'button',
                        text: '查询',
                        style: 'padding-left : 10px;',
                        handler: function () {
                            main_store.removeAll();//移除原来的数据
                            main_store.load();//加载新搜索的数据
                        }
                    },
                        {
                            xtype: 'button',
                            text: '重置',
                            style: 'padding-left : 10px;',
                            handler: function () {
                                top_panel.getForm().reset();
                                main_store.removeAll();//移除原来的数据
                                main_store.load();//加载新搜索的数据
                            }
                        },
                    ]}
        ]
});