/**
 * 获取日期函数
 * @param $flag 1 为月初，2为当前日期
 * @param $mode 1 为全格式，2为年月日
 * @returns {String}
 */
function GetDateStr($flag, $mode)
{
    var dd = new Date();
    var y = dd.getFullYear();
    var m = dd.getMonth() + 1;//获取当前月份的日期
    var h = dd.getHours();
    var i = dd.getMinutes();
    var s = dd.getSeconds();
    if (m.toString().length == 1) {
        m = '0' + m;
    }
    var d = '';
    if ($flag == 1)
        d = "01";
    else {
        d = dd.getDate();
        if (d.toString().length == 1) {
            d = '0' + d;
        }
    }

    var returnStr = '';
    if($mode == 2){
        returnStr = y + "-" + m + "-"+d;
    }else{
        returnStr = y+"-"+m+"-"+d+" "+h+":"+ i +":"+ s;
    }

    return returnStr;
}

var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 15px',
	height : 100,
	items  :
	[
	     {
			xtype : 'compositefield',
			items :
			[
                {
                    xtype: 'displayfield',
                    value: '核算主体：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype: 'combo',
                    width: 150,
                    id   : 'id_settle_obj',
                    hiddenName : "search_settle_obj",
                    editable: true,
                    emptyText : '全部',
                    mode: 'local',
                    triggerAction:'all',
                    displayField: 'name',
                    valueField: 'value',
                    store : new Ext.data.SimpleStore({
                        fields: ['name', 'value'],
                        data: [['1级-供应商', '100'], ['2级-服务区', '200'], ['2级-主卡', '300']]
                    })
                },
                {
                    xtype: 'displayfield',
                    value: '油站供应商：',
                    style: 'padding-left: 0px'
                },
                new Ext.form.ComboBox({
                    width: 290,
                    hiddenName: 'id',
                    triggerAction: 'all',
                    forceSelection: true,
                    mode: 'local',
                    queryParam:'supplier_nameLike',
                    minChars:2,
                    displayField: 'supplier_name',//显示的值
                    valueField: 'id',//后台接收的key
                    store: getOilSupplier,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listWidth:400,
                    listeners: {
                        'focus': function () {
                            getOilSupplier.load();
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        }
                    }
                }),
                {
                    xtype: 'displayfield',
                    value: '签约运营商：',
                    style: 'padding-left: 10px'
                },
                new Ext.form.ComboBox({
                    width: 120,
                    hiddenName: 'operator_id',
                    triggerAction: 'all',
                    forceSelection: true,
                    mode: 'remote',
                    queryParam:'nameLike',
                    minChars:2,
                    displayField: 'name',//显示的值
                    valueField: 'id',//后台接收的key
                    store: getOilOperators,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                }),
                {
                    xtype: 'displayfield',
                    value: '充值方式：',
                    style: 'padding-left: 22px'
                },
                {
                    xtype: 'combo',
                    width: 120,
                    id   : 'recharge_type',
                    hiddenName : "recharge_type",
                    editable: true,
                    emptyText : '全部',
                    mode: 'local',
                    triggerAction:'all',
                    displayField: 'name',
                    valueField: 'value',
                    store : new Ext.data.SimpleStore({
                        fields: ['name', 'value'],
                        data: [['线下', '1'], ['线上', '2']]
                    })
                },
			]
		 },
        {
            xtype : 'compositefield',
            items :
                [
                    {
                        xtype: 'displayfield',
                        value: '合作类型：',
                        style: 'padding-left: 10px'
                    },
                    {
                        xtype: 'combo',
                        width: 150,
                        id   : 'cooperation_type',
                        hiddenName : "cooperation_type",
                        editable: true,
                        emptyText : '全部',
                        mode: 'local',
                        triggerAction:'all',
                        displayField: 'name',
                        valueField: 'value',
                        store : new Ext.data.SimpleStore({
                            fields: ['name', 'value'],
                            data: [['平台', '10'], ['站点', '20'], ['主卡', '30']]
                        })
                    },
                    {
                        xtype: 'displayfield',
                        value: '收款公司：',
                        style: 'padding-left: 12px'
                    },
                    {
                        xtype : 'textfield',
                        id    : 's_collect_company_id',
                        name  : 'collect_company_id',
                        width : 290
                    },
                    new Ext.form.ComboBox({
                        width: 290,
                        id: 's_collect_company_id',
                        hiddenName: 'collect_company_id',
                        triggerAction: 'all',
                        forceSelection: true,
                        mode: 'remote',
                        queryParam: 's_company_name',
                        minChars: 2,
                        displayField: 'company_name',//显示的值
                        valueField: 'id',//后台接收的key
                        store: getCollectCompany,
                        emptyText: '请选择..',
                        enableKeyEvents: true,
                        listWidth:400,
                        listeners: {
                            'focus': function () {
                                getCollectCompany.load();
                            },
                            'beforequery': function (e) {
                                var combo = e.combo;
                                if (!e.forceAll) {
                                    var input = e.query;
                                    // 检索的正则
                                    var regExp = new RegExp(".*" + input + ".*");
                                    // 执行检索
                                    combo.store.filterBy(function (record, id) {
                                        // 得到每个record的项目名称值
                                        var text = record.get(combo.displayField);
                                        return regExp.test(text);
                                    });
                                    combo.expand();
                                    return false;
                                }
                            }
                        }
                    }),
                    {
                        xtype: 'displayfield',
                        value: '结算对接人：',
                        style: 'padding-left: 10px'
                    },
                    {
                        xtype : 'textfield',
                        id    : 'settlement_docker',
                        name  : 'settlement_docker',
                        width : 120
                    },
                    {
                        xtype: 'displayfield',
                        value: '网络负责人：',
                        style: 'padding-left: 10px'
                    },
                    {
                        xtype : 'textfield',
                        id    : 'network_docker',
                        name  : 'network_docker',
                        width : 120
                    },
                ]
        },
        {
            xtype : 'compositefield',
            items :
                [
                    {
                        xtype: 'displayfield',
                        value: '合作状态：',
                        style: 'padding-left: 10px'
                    },
                    {
                        xtype: 'combo',
                        width: 150,
                        id   : 'cooperation_status',
                        hiddenName : "cooperation_status",
                        editable: true,
                        emptyText : '全部',
                        mode: 'local',
                        triggerAction:'all',
                        displayField: 'name',
                        valueField: 'value',
                        store : new Ext.data.SimpleStore({
                            fields: ['name', 'value'],
                            data: [['待审核', '10'], ['合作中', '20'], ['停止合作', '2']]
                        })
                    },
                    {
                        xtype: 'displayfield',
                        value: '服务区/运营商/主卡：',
                        style: 'padding-left: 12px'
                    },
                    {
                        xtype : 'textfield',
                        id    : 'area_operator_card',
                        name  : 'area_operator_cardLike',
                        width : 290
                    },
                    {
                        xtype: 'displayfield',
                        value: '回票方式：',
                        style: 'padding-left: 10px'
                    },
                    {
                        xtype: 'combo',
                        width: 150,
                        id   : 'receipt_type',
                        hiddenName : "receipt_type",
                        editable: true,
                        emptyText : '全部',
                        mode: 'local',
                        triggerAction:'all',
                        displayField: 'name',
                        valueField: 'value',
                        store : new Ext.data.SimpleStore({
                            fields: ['name', 'value'],
                            data: [['消费回票', '10'], ['充值回票', '20']]
                        })
                    },
                    {
                        xtype:'button',
                        text:'查询',
                        style : 'padding-left : 10px;',
                        handler: function()
                        {
                            main_store.removeAll();//移除原来的数据
                            main_store.load();//加载新搜索的数据
                        }
                    },
                    {
                        xtype: 'button',
                        text: '重置',
                        style: 'padding-left : 10px;',
                        handler: function () {
                            top_panel.getForm().reset();
                            main_store.removeAll();//移除原来的数据
                            main_store.load();//加载新搜索的数据
                            detailStore.removeAll();
                            detailSupplierCompanyStore.removeAll();
                            crow = {};
                            btnInit(0);
                        }
                    },
                ]
        }
	]
});