/**
 * 全局类
 */
function oiltest() {
    var _sm;

    //列表表格
    this.getGridPanel = function () {
        _sm = new Ext.grid.CheckboxSelectionModel({
            listeners: {
                selectionchange: function (data) {
                    if (data.getCount()) {
                        //加载底部数据
                        var rows = grid_list.getSelectionModel().getSelections();
                        var record = rows[0];

                        if (record.get('choice_status') === 10 && record.get('status') === 10 && record.get('color_type') === 20) {//未勾稽,已开,蓝票 && record.get('receipt_channel') === 2
                            (Ext.getCmp('btnCancel')) ? Ext.getCmp('btnCancel').enable() : '';//冲红按钮
                        } else {
                            (Ext.getCmp('btnCancel')) ? Ext.getCmp('btnCancel').disable() : '';//冲红按钮
                        }


                        if (data.getCount() == 1) {
                            if (record.get('choice_status') === 10 && record.get('status') === 20 && record.get('color_type') === 20) {//未勾稽,待废
                                (Ext.getCmp('btnBackCancel')) ? Ext.getCmp('btnBackCancel').enable() : '';//撤销冲红按钮
                            } else {
                                (Ext.getCmp('btnBackCancel')) ? Ext.getCmp('btnBackCancel').disable() : '';//撤销冲红按钮
                            }
                        } else {
                            (Ext.getCmp('btnCancel')) ? Ext.getCmp('btnCancel').disable() : '';//冲红按钮
                        }
                        //删除
                        if (data.getCount() == 1) {
                            //渠道=线下
                            if (record.get('receipt_channel') === 1 && record.get('translate_status') === 4 && record.get('out_status') === 1) {
                                (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').enable() : '';
                            } else {
                                (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').disable() : '';
                            }
                        } else {
                            (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').disable() : '';
                        }
                        var statusArr = [];
                        rows.forEach(function (val) {
                            if (val.get('receipt_channel') === 1 && val.get('translate_status') === 3 && val.get('out_status') === 1) {
                                statusArr.push(val.get('translate_status'));
                            }
                        });
                        //确认出库
                        if (rows.length == statusArr.length ) {
                            (Ext.getCmp('btnoutStock')) ? Ext.getCmp('btnoutStock').enable() : '';
                        } else {
                            (Ext.getCmp('btnoutStock')) ? Ext.getCmp('btnoutStock').disable() : '';
                        }
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').enable() : '';//修改按钮
                        (Ext.getCmp('addRemarkBtn')) ? Ext.getCmp('addRemarkBtn').enable() : '';//添加备注
                    } else {
                        (Ext.getCmp('btnCancel')) ? Ext.getCmp('btnCancel').disable() : '';//冲红按钮
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').disable() : '';//修改按钮
                        (Ext.getCmp('addRemarkBtn')) ? Ext.getCmp('addRemarkBtn').disable() : '';//添加备注
                    }
                }
            }
        });

        var grid_list = new Ext.grid.GridPanel({
            title: '天然气库存管理',
            region: 'center',
            loadMask: true,
            cm: new Ext.grid.ColumnModel([
                 _sm,
                {header: '月份', dataIndex: 'business_time', width: 120},
                {header: '公司主体', dataIndex: 'operator_name', width: 120},
                {header: '<font color=blue>本月购进量(吨)</font>', dataIndex: 'm_quantity_in', width: 120},
                {header: '<font color=blue>本月购自省外(吨)</font>', dataIndex: 'm_quantity_province_out_in', width: 120},
                {header: '<font color=blue>本年累计购进量(吨)</font>', dataIndex: 'y_quantity_in', width: 120},
                {header: '<font color=blue>本年累计购自省外(吨)</font>', dataIndex: 'y_quantity_province_out_in', width: 120},
                {header: '<font color=blue>本月购进量(元)</font>', dataIndex: 'm_amount_in', width: 160},
                {header: '<font color=blue>本月购自省外(元)</font>', dataIndex: 'm_amount_province_out_in', width: 100},
                {header: '<font color=blue>本年累计购进(元)</font>', dataIndex: 'y_amount_in', width: 100},
                {header: '<font color=blue>本年累计购自省外(元)</font>', dataIndex: 'y_amount_province_out_in', width: 100},
                
                {header: '本月销量(吨)', dataIndex: 'm_quantity', width: 120},
                {header: '本月销省外(吨)', dataIndex: 'm_quantity_province_out', width: 120},
                {header: '本年累计销量(吨)', dataIndex: 'y_quantity', width: 120},
                {header: '本年累计销省外(吨)', dataIndex: 'y_quantity_province_out', width: 120},
                {header: '本月销量(元)', dataIndex: 'm_amount', width: 160},
                {header: '本月销省外(元)', dataIndex: 'm_amount_province_out', width: 100},
                {header: '本年累计销(元)', dataIndex: 'y_amount', width: 100},
                {header: '本年累计销省外(元)', dataIndex: 'y_amount_province_out', width: 100},
                {header: '<font color=blue>本月底库存(吨)</font>', dataIndex: 'stock', width: 100},
                {header: '定版状态', dataIndex: 'push_status', width: 100},
                {header: '定版人', dataIndex: 'psuh_name', width: 100},
                {header: '定版时间', dataIndex: 'pushtime', width: 100},
                {header: '最近更新人', dataIndex: 'last_operator', width: 100},
                {header: '最近更新时间', dataIndex: 'updatetime', width: 100},
                {header: '创建时间', dataIndex: 'createtime', width: 100},
            ]),
            listeners: {
                "rowclick" : rowClick
            },
            store: main_store,
            sm: _sm,
            //分页
            bbar: new Ext.PagingToolbar({
                plugins: new Ext.ux.plugin.PagingToolbarResizer,
                pageSize: pagesize,
                displayInfo: true,
                displayMsg: '当前记录数: {0} - {1} 总记录数: {2}',
                emptyMsg: '没有符合条件的记录',
                store: main_store
            }),
            tbar: []
        });

        return grid_list;
    };

    //关闭窗口
    this.closeWin = function (id) {
        if (!id) return;
        var win = Ext.getCmp(id);
        win.destroy();
    };

    var _getWindow = function (config) {
        var width = config.width,
            height = config.height,
            title = config.title,
            id = config.id,
            button = config.button;

        button.push({
            text: '取消',
            id: 'button_cancer',
            handler: function () {
                var oilTest = new oiltest();
                oilTest.closeWin(id);
            }
        });
        var windows = new Ext.Window({
            layout: 'border',
            region: 'center',
            width: width,
            modal: true,//遮挡窗口后的内容
            id: id,
            height: height,
            title: title,
            closeAction: 'destroy',
            buttons: button
        });

        return windows;
    };

    //添加
    this.add = function () {
        var windows = _getWindow({
            title: '添加-发票管理',
            id: 'add_win',
            width: 550,
            height: 270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'add-form',
            items: addPanel.getPanel('add-form', 'add_win')
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };

    //编辑
    this.update = function () {
        var sm = grid_list.getSelectionModel();
        var data = sm.getSelections();
        data[0] ? id = data[0].get("id") : '';

        var windows = _getWindow({
            title: '编辑-发票管理',
            id: 'update_win',
            width: 550,
            height: 270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'update-form',
            items: addPanel.getPanel('update-form', 'update_win', id)
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init(data[0]);
    };
    
    
    this.compute = function () {
    	var preData = grid_list.getSelectionModel().getSelections();//当前选中行的数据
    	var l = preData.length;
    	if(l != 1){
    		alert("请选择一条数据操作");
    		return;
    	}
        preData = preData[0].data;
        msg = preData.business_time+preData.operator_name+"";
        Ext.MessageBox.confirm('重算', "确定更新"+msg+"统计数据吗？", function showResult(btn) {
                if (btn === 'yes') {
                    var id = preData.id;
                    Ext.Ajax.request({
                        url: '/inside.php?t=json&m=oil_invoice_import_statistics&f=gasCompute&id='+id,
                        method: 'get',
                        params: {
               
                        },
                        success: function(responseA, action) {
                        	Ext.MessageBox.alert('提示', Ext.decode(responseA.responseText).message);
                        	main_store.reload();
                           
                        },
                        failure: function(form, action) {
                            // 处理接口请求失败的情况
                        	Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).message);
                        }
                    });
                }
            }
        );
    };

    this.push = function () {
    	var data = grid_list.getSelectionModel().getSelections();//当前选中行的数据
    	var l = preData.length;
        var ids = '';
        if(data.length > 0){
            for(var i = 0; i < data.length; i++){
                if(ids === ''){
                    ids = data[i].get('id');
                }else{
                    ids += ',' + data[i].get('id');
                }
            }
        }
  
        Ext.MessageBox.confirm('定版', "定版后不能重算，确定对"+l+"条定版数据吗？", function showResult(btn) {
                if (btn === 'yes') {
                    var id = preData.id;
                    Ext.Ajax.request({
                        url: '/inside.php?t=json&m=oil_invoice_import_statistics&f=gasPush&id='+id,
                        method: 'get',
                        params: {
               
                        },
                        success: function(responseA, action) {
                        	Ext.MessageBox.alert('提示', Ext.decode(responseA.responseText).msg);
                        	main_store.reload();
                           
                        },
                        failure: function(form, action) {
                            // 处理接口请求失败的情况
                        	Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).message);
                        }
                    });
                }
            }
        );
    };
    
    
    this.exportDetails = function () {
        var form = new Ext.form.FormPanel({
            baseCls: 'x-plain',
            height: 255,
            labelWidth: 70,
            defaultType: 'textfield',
            items: [
                {// 第一行
                    xtype: 'compositefield',
                    style: 'margin-bottom: 5px;',
                    items: [
                        {
                            xtype: 'displayfield',
                            value: '公司主体：'
                        },{
                            xtype: 'combo',
                            width: 140,
                            id: 'export_operator_id',
                            hiddenName: "operator_id",
                            editable: true,
                            emptyText: '请选择..',
                            forceSelection: true,
                            triggerAction: 'all',
                            displayField: 'company_name',
                            valueField: 'id',
                            store: new Ext.data.Store({
                                url: '../inside.php?t=json&m=oil_operators&f=getlist&c_type=1',
                                reader: new Ext.data.JsonReader({
                                    totalProperty: 'data.total',
                                    root: 'data.data'
                                }, ['id', 'company_name'])
                            })
                        },
                    ]
                },
                {// 第二行
                    xtype: 'compositefield',
                    style: 'margin-bottom: 5px;',
                    items: [
                        {
                            xtype: 'displayfield',
                            value: '所属月份：',
                        },
                        {
                            xtype: "datefield",
                            width: 100,
                            value: preMonth,
                            format: 'Y-m',
                            name: 'export_business_timeGe',
                            id: 'export_business_timeGe',
                        },
                        {
                            xtype: 'displayfield',
                            value: '-',
                        },
                        {
                            xtype: "datefield",
                            width: 100,
                            value: currentMonth,
                            format: 'Y-m',
                            name: 'export_business_timeLe',
                            id: 'export_business_timeLe',
                        },

                    ]
                },
            ]
        });


        var exportDetailData  = new Ext.Window({
            title: '导出明细', width: 400, height: 130, minWidth: 300, minHeight: 500, modal: true,
            layout: 'fit', plain: true, bodyStyle: 'padding:5px;', buttonAlign: 'center',
            items: form,
            buttons: [{
                text: '确定导出',
                handler: function () {
                    if (form.form.isValid()) {
                        if (Ext.getCmp('export_operator_id').getValue() === '') {
                            Ext.Msg.alert('系统提示', '请选择公司主体');
                            return;
                        }
                        var btGe = Ext.getCmp('export_business_timeGe').getValue();
                        var btLe = Ext.getCmp('export_business_timeLe').getValue();
                        var curr = currentMonth;
                        if (btGe > btLe  || btLe > curr || btLe >curr ) {
                            Ext.Msg.alert('系统提示', '开始时间不得晚于结束时间,开始时间、结束时间不能晚于当前月份');
                            return;
                        }
                        
                        var start = new Date(btGe); // 将起始日期字符串转换为Date对象
                        var end = new Date(btLe);     // 将结束日期字符串转换为Date对象
                      
                        var timeDiff = Math.abs(end.getTime() - start.getTime()); // 获取时间差（单位：毫秒）
                        var monthDiff = Math.floor(timeDiff / (30 * 24 * 60 * 60 * 1000)); // 根据每个月平均天数进行近似计算
                        if (monthDiff>12 ) {
                            Ext.Msg.alert('系统提示', '导出时间不能超过12个月'); 
                            return;
                        }	

                        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                                if (btn === 'yes') {
                                    var params = form.getForm().getValues(true);
                                    window.location.href = '/inside.php?t=json&m='+ controlName +'&f=getGasDetails'+'&'+params;
                                }
                            }
                        );
                    }
                }
            }, {
                text: '取消',
                handler: function () {
                	exportDetailData.close();
                }
            }]
        });
        exportDetailData.show();
    };
    
    
    //删除
    this.delete = function () {
        var data = _sm.getSelections();
        if (data.length > 1) {
            Ext.MessageBox.alert('提示', '只能单条数据');
            return false;
        }
        Ext.MessageBox.confirm('删除', '确认是否删除此对账单？', function showResult(btn) {
            if (btn === 'yes') {
                var sm = grid_list.getSelectionModel();
                var data = sm.getSelections();
                var ids = data[0].get("id");
                var status = data[0].get("status");
                if(status != 1){
                	Ext.MessageBox.alert('提示', '已发布账单不允许删除');
                    return false;
                }
                Ext.Ajax.request({
                    url: getUrl(controlName, 'remove'),
                    method: 'post',
                    params: {ids: ids},
                    success: function sFn(response, options) {
                        var result = Ext.decode(response.responseText);
                        if (parseInt(result.code) != 0) {
                            Ext.Msg.alert('提示', result.msg);
                        } else {
                            main_store.removeAll();
                            main_store.load();
                            Ext.Msg.alert('系统提示', '删除成功');
                        }
                    },
                    failure: function (resp) {
                        var result = Ext.decode(resp.responseText);
                        Ext.Msg.alert('系统提示', result.msg);
                    }
                });
            }
        });
    };
    //导出
    this.export = function () {
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn == 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = '/inside.php?t=json&m=' + controlName + '&f=getOilList&_export=1' + '&' + params;
                }
            }
        );
    };

    /**
     * 导入
     */
    this.import = function () {
        var form = new Ext.form.FormPanel({
            baseCls: 'x-plain',
            labelWidth: 70,
            fileUpload: true,
            items: [
                {
                    xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
                    id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
                    anchor: '100%'
                }
            ]
        });
        var import_win = new Ext.Window({
            title: '批量导入',
            width: 400,
            height: 105,
            modal: true,
            layout: 'fit',
            items: form,
            bodyStyle: 'padding:5px;',
            plain: true,
            buttonAlign: 'center',
            buttons: [{
                text: '导入',
                handler: function () {
                    if (form.form.isValid()) {
                        if (Ext.getCmp('userfile').getValue() === '') {
                            Ext.Msg.alert('系统提示', '请选择你要上传的文件');
                            return;
                        }

                        isDirty = true;
                        form.getForm().submit({
                            url: getUrl(controlName, 'import'),
                            waitMsg: 'Saving Data...',
                            success: function (form, action) {
                                var r = Ext.decode(action.response.responseText);
                                if (parseInt(r.code) != 0) {
                                    Ext.Msg.alert('提示', '导入失败');
                                } else {
                                    if (r.data.successCount >= 0) {
                                        Ext.MessageBox.alert('提示', "导入成功:" + r.data.successCount + "条,失败：" + r.data.errorCount + "条,总共：" + r.data.total + "条");
                                    } else {
                                        Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).msg);
                                    }
                                }
                                import_win.close();
                                main_store.removeAll();
                                main_store.load();
                            },
                            failure: function (form, action) {
                                Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).msg);
                            }
                        })
                    }
                }
            }, {
                text: '关闭',
                handler: function () {
                    import_win.close();
                }
            }]
        });
        import_win.show();
    };

    /**
     * 冲红
     */
    this.cancel = function () {
        var data = _sm.getSelections();
        if (data.length > 1) {
            Ext.MessageBox.alert('提示', '只能单张发票冲红');
            return false;
        }
        var record = data[0].data;
        Ext.Ajax.request({
            url: '/inside.php?t=json&m=oil_receipt_manage&f=getRedInfoByBlue',
            params: {id: record.id},
            success: function (resp, opts) {
                data = Ext.decode(resp.responseText).data;
                Ext.getCmp('red_number').setValue(data.hasOwnProperty('red_no') ? data.red_no : '')
                Ext.getCmp('red_number_uuid').setValue(data.hasOwnProperty('red_uuid') ? data.red_uuid : '')
            },
            failure: function() {
            }
        });
        var add_form_panel = new Ext.form.FormPanel({
            region: 'center',
            hideLabels: true,
            bodyStyle: 'padding: 10px',
            height: 60,
            items:
                [
                    {
                        xtype: 'compositefield',
                        width: 465,
                        style: 'margin-bottom:-2px;',
                        items:
                            [
                                {
                                    xtype: 'displayfield',
                                    style: 'margin-left:0px;',
                                    value: ' '
                                },
                                {
                                    xtype: 'displayfield',
                                    id: 't_receipt_type',
                                    style: 'margin-left:0px;color:gray;',
                                    value: '',
                                },
                            ]

                    },
                    {
                        xtype: 'compositefield',
                        id: 'red_number_box',
                        style: 'margin-top:10px;',
                        items:
                            [
                                {
                                    xtype: 'displayfield',
                                    value: '红字信息单编码：'
                                },
                                {
                                    xtype: "textfield",
                                    id: "red_number",
                                    name: "red_number",
                                    allowBlank: false,
                                    blankText: '不能为空',
                                    width: 350,
                                },
                                {xtype: 'displayfield', value: ' <font color=red>*</font>'},
                            ]

                    },
                    {
                        xtype: 'compositefield',
                        id: 'red_number_uuid_box',
                        items:
                            [
                                {
                                    xtype: 'displayfield',
                                    value: '红字信息单UUID：'
                                },
                                {
                                    xtype: "textfield",
                                    id: "red_number_uuid",
                                    name: "red_number_uuid",
                                    allowBlank: false,
                                    blankText: '不能为空',
                                    width: 346,
                                },
                                {xtype: 'displayfield', value: ' <font color=red>*</font>'},
                            ]

                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-top:20px',
                        items: [
                            {
                                xtype: 'displayfield',
                                value: '是否重新生成发票销售单据：'
                            },
                            {
                                xtype: 'radiogroup',
                                width: 250,
                                id: 'client_env',
                                items: [
                                    {boxLabel: '是', width: 125, name: 'is_repeat', inputValue: 1, checked: true},
                                    {boxLabel: '否', width: 125, name: 'is_repeat', inputValue: 2},
                                ]
                            }
                        ],
                    },
                ],
            buttons: [{
                text: "确定",
                handler: function () {
                    var _form = this.ownerCt.ownerCt.getForm();
                    if (_form.isValid()) {
                        Ext.MessageBox.confirm('提示', '确定要冲红选中项吗？', function showResult(btn) {
                            if (btn === 'yes') {
                                _form.submit({
                                    url: getUrl(controlName, 'cancel'),
                                    params: {
                                        ids: record.id
                                    },
                                    waitMsg: 'Saving Data...',
                                    success: function (form, action) {
                                        //刷新页面
                                        main_store.removeAll();
                                        main_store.load();
                                        add_win.destroy();
                                        Ext.MessageBox.alert("消息!", action.result.msg);
                                    },
                                    failure: function (resp, opts) {
                                        Ext.MessageBox.alert("消息!", "操作失败!");
                                    }
                                });
                            }
                        });
                    } else {
                        Ext.Msg.alert('提示', '非法输入');
                    }
                }
            }, {
                text: "取消",
                handler: function () {
                    add_win.destroy();
                    add_win.hide();
                }
            }]
        });
        // 10专票，20普票，30专票电子，40普票电子 50数电专票，60数电普票
        Ext.getCmp('t_receipt_type').setValue('对应正数发票代码:' + record.receipt_code + ' 号码:' + record.receipt_no);
        if (record.receipt_type == 20 || record.receipt_type == 40) {
            Ext.getCmp('red_number_box').hide();
            Ext.getCmp('red_number_uuid_box').hide();
            Ext.getCmp('red_number_uuid').allowBlank = true;
            Ext.getCmp('red_number').allowBlank = true;
        } else if (record.receipt_type == 10 || record.receipt_type == 30) {
            Ext.getCmp('red_number_uuid_box').hide();
            Ext.getCmp('red_number_uuid').allowBlank = true;
        }
        var add_win = new Ext.Window({
            layout: "border",
            width: 500,
            height: 320,
            title: '冲红',
            closeAction: 'destroy',
            plain: true,
            items: [add_form_panel]
        });
        add_win.show();
    };

    /**
     * 添加备注
     */
    this.addRemark = function () {
        var data = _sm.getSelections();
        if (data.length > 100) {
            Ext.MessageBox.alert('提示', '最多选择100条数据');
            return false;
        }
        var ids = '';
        if (data.length > 0) {
            for (var i = 0; i < data.length; i++) {
                ids += ids ? ',' + data[i].get('id') : data[i].get('id');
            }
        }
        console.log('ids--', ids);
        var add_form_panel = new Ext.form.FormPanel({
            region: 'center',
            hideLabels: true,
            bodyStyle: 'padding: 10px',
            height: 60,
            items:
                [
                    {
                        xtype: 'compositefield',
                        items:
                            [
                                {
                                    xtype: 'displayfield',
                                    value: '备注：'
                                },
                                {
                                    xtype: "textarea",
                                    name: "admin_remark",
                                    allowBlank: false,
                                    blankText: '不能为空',
                                    width: 350,
                                    height: 120
                                },
                                {xtype: 'displayfield', value: ' <font color=red>*</font>'},
                            ]
                    },
                ],
            buttons: [{
                text: "确定",
                handler: function () {
                    var _form = this.ownerCt.ownerCt.getForm();
                    if (_form.isValid()) {
                        _form.submit({
                            url: getUrl(controlName, 'addRemark'),
                            params: {
                                ids: ids
                            },
                            waitMsg: 'Saving Data...',
                            success: function (form, action) {
                                //刷新页面
                                main_store.removeAll();
                                main_store.load();
                                add_win.destroy();
                                Ext.MessageBox.alert("消息!", action.result.msg);
                            },
                            failure: function (resp, opts) {
                                Ext.MessageBox.alert("消息!", "操作失败!");
                            }
                        });
                    } else {
                        Ext.Msg.alert('提示', '非法输入');
                    }
                }
            }, {
                text: "取消",
                handler: function () {
                    add_win.hide();
                }
            }]
        });

        var add_win = new Ext.Window({
            layout: "border",
            width: 500,
            height: 220,
            title: '添加备注',
            closeAction: 'destroy',
            plain: true,
            items: [add_form_panel]
        });
        add_win.show();
    };

    /**
     * 撤销冲红
     */
    this.backCancel = function () {
        var data = _sm.getSelections();
        if (data.length > 1) {
            Ext.MessageBox.alert('提示', '请选择1条数据');
            return false;
        }
        var ids = data[0].get('id');

        Ext.MessageBox.confirm('撤销冲红', "确定要撤销冲红吗？", function showResult(btn) {
                if (btn == 'yes') {
                    Ext.Ajax.request({
                        url: getUrl(controlName, 'backCancel'),
                        method: 'post',
                        params: {id: ids},
                        success: function sFn(response, options) {
                            var result = Ext.decode(response.responseText);

                            if (result.code != 0) {
                                Ext.Msg.alert('系统提示', result.msg);
                            } else {
                                main_store.removeAll();
                                main_store.load();
                                Ext.Msg.alert('系统提示', '撤销成功');
                            }
                        }
                    })
                }
            }
        );
    };

    this.pullNewReceipt = function () {
    	 var form = new Ext.form.FormPanel({
    	        baseCls: 'x-plain',
    	        height: 255,
    	        labelWidth: 70,
    	        fileUpload: true,
    	        defaultType: 'textfield',
    	        items: [
    	        
    	            {// 第1行
    	                xtype: 'compositefield',
    	                style: 'margin-bottom: 5px;',
    	                items: [
    	                    {
    	                        xtype: 'displayfield',
    	                        value: '刷新范围：',
    	                    },
    	                    {
    	                        xtype: "datefield",
    	                        width: 100,
    	                        value: currentMonth,
    	                        format: 'Y-m-d',
    	                        name: 's_time',
    	                        id: 's_time',
    	                    },
    	                    {
    	                        xtype: 'displayfield',
    	                        value: '-',
    	                    },
    	                    {
    	                        xtype: "datefield",
    	                        width: 100,
    	                        value: currentMonth,
    	                        format: 'Y-m-d',
    	                        name: 'e_time',
    	                        id: 'e_time',
    	                    },

    	                ]
    	            },
    	            {// 第2行
    	                xtype: 'compositefield',
    	                style: 'margin-bottom: 5px;',
    	                items: [
    	                    {
    	                        xtype: 'displayfield',
    	                        value: '销方名称：'
    	                    },{
    	                        xtype: 'combo',
    	                        width: 140,
    	                        id: 'import_operator_id',
    	                        hiddenName: "operator_id",
    	                        editable: true,
    	                        emptyText: '请选择..',
    	                        forceSelection: true,
    	                        triggerAction: 'all',
    	                        displayField: 'company_name',
    	                        valueField: 'id',
    	                        store: new Ext.data.Store({
    	                            url: '../inside.php?t=json&m=oil_operators&f=getlist&c_type=1',
    	                            reader: new Ext.data.JsonReader({
    	                                totalProperty: 'data.total',
    	                                root: 'data.data'
    	                            }, ['id', 'company_name'])
    	                        })
    	                    },
    	                ]
    	            },
    	        ]
    	    });
    	    var importData  = new Ext.Window({
    	        title: '手动刷新', width: 400, height: 200, minWidth: 300, minHeight: 500, modal: true,
    	        layout: 'fit', plain: true, bodyStyle: 'padding:5px;', buttonAlign: 'center', items: form,
    	        buttons: [{
    	            text: '手动刷新',
    	            handler: function () {
    	                if (form.form.isValid()) {
    	                    if (Ext.getCmp('import_operator_id').getValue() === '') {
    	                        Ext.Msg.alert('系统提示', '请选择销方名称');
    	                        return;
    	                    }
    	                    if (Ext.getCmp('s_time').getValue() === '') {
    	                        Ext.Msg.alert('系统提示', '请选择开始年月');
    	                        return;
    	                    }

    	                    if (Ext.getCmp('e_time').getValue() === '') {
    	                        Ext.Msg.alert('系统提示', '请选择结束年月');
    	                        return;
    	                    }


    	                    Ext.Ajax.request({
    	                        url: '/inside.php?t=json&m=oil_receipt_manage&f=pullNewReceiptAll',
    	                        method: 'POST',
    	                        params: {
    	                            operator_id : Ext.getCmp('import_operator_id').getValue(),
    	                            s_time : Ext.getCmp('s_time').getValue(),
    	                            e_time : Ext.getCmp('e_time').getValue()
    	                        },
    	                        success: function(responseA, opts) {
    	                        	importData.close();
    	                        	if(Ext.decode(responseA.responseText).code == 2){
    	                        		Ext.MessageBox.alert('提示', Ext.decode(responseA.responseText).message);
    	                        	}else{
    	                        		Ext.MessageBox.alert('提示', '提交成功！请稍后刷新页面');
    	                        	}
                                    main_store.removeAll();
                                    main_store.load();
    	                           
    	                        },
    	                        failure: function(form, action) {
    	                            // 处理接口请求失败的情况
    	                        	Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).message);
    	                        }
    	                    });

    	                }
    	            }
    	        }, {
    	            text: '关闭',
    	            handler: function () {
    	                importData.close();
    	            }
    	        }]
    	    });
    	    importData.show();
    };


    /**
     * 出库
     * @returns {boolean}
     */
    this.outStock = function () {
        var data = _sm.getSelections();
        if (data.length > 100) {
            Ext.MessageBox.alert('提示', '最多选择100条数据');
            return false;
        }
        var ids = '';
        if (data.length > 0) {
            for (var i = 0; i < data.length; i++) {
                ids += ids ? ',' + data[i].get('id') : data[i].get('id');
            }
        }
        console.log('ids--', ids);
        Ext.MessageBox.confirm('确认出库', "确认是否进行出库操作，确认后将进行油品库存相应扣减，确认出库后的销项票不可删除，请谨慎操作？", function showResult(btn) {
            if (btn == 'yes') {
                Ext.Ajax.request({
                    url: getUrl(controlName, 'outStock'),
                    method: 'post',
                    params: {ids: ids},
                    success: function sFn(response, options) {
                        var result = Ext.decode(response.responseText);

                        if (result.code != 0) {
                            Ext.Msg.alert('系统提示', result.msg);
                        } else {
                            main_store.removeAll();
                            main_store.load();
                            Ext.Msg.alert('系统提示', '执行成功！');
                        }
                    }
                })
            }
        });
    };
}

function previewReceipt(download_url) {
    if (!download_url) {
        return false
    }
    let previewReceiptPanel = new Ext.Panel({
        region: 'center',
        autoScroll: true,
        items: [
            {
                xtype: 'box',
                autoEl: {
                    id: 'preview_receipt',
                    tag: 'embed',
                    style: "width: 100%;height:100%;",
                    type: 'application/pdf',
                    src: download_url
                }
            }
        ]
    })
    let previewReceiptWindow = new Ext.Window({
        title: '发票查看',
        layout: 'fit',
        width: 900,
        height: 700,
        closeAction: 'close',
        closable: true,
        resizable: false,
        plain: true,
        modal: true,
        items: [previewReceiptPanel],
        constrainHeader: true
    })
    previewReceiptWindow.show()
}

var oilTest = new oiltest();