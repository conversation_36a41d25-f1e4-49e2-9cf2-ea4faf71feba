var top_panel = new Ext.form.FormPanel({
    region : 'north',
    hideLabels : true,
    bodyStyle : 'padding: 10px',
    height : 90,
    items  :
        [
            {//第一行
                xtype : 'compositefield',
                items :
                    [
                        {
                            xtype: 'displayfield',
                            value: '单号：'
                        },
                        {
                            xtype : 'textfield',
                            name  : 'no',
                            width : 120
                        },
                        {
                            xtype: 'displayfield',
                            value: '顶级机构：',
                        },
                        new Ext.form.ComboBox({
                            width: 174,
                            listWidth: 350,
                            id: 'root_org_id',
                            hiddenName: 'root_org_id',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'remote',
                            queryParam: 'keyword',
                            minChars: 2,
                            displayField: 'org_name',//显示的值
                            valueField: 'id',//后台接收的key
                            store: getTopOilOrgNew,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                            listeners: {
                                'focus': function () {
                                    getTopOilOrgNew.params = {"is_root": 1};
                                    getTopOilOrgNew.load();
                                },
                            },
                            /*listeners: {
                                'beforequery': function (e) {
                                    getTopOilOrgNew.params = {"is_root": 1};
                                },
                            },*/
                        }),
                        /*new Ext.form.ComboBox({
                            width: 180,
                            id: 'root_org_id',
                            hiddenName:'root_org_id',
                            value: '',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'local',
                            displayField: 'org_name',//显示的值
                            valueField: 'id',//后台接收的key
                            store: getTopOilOrg,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                            listeners: {
                                'focus': function () {
                                    getTopOilOrg.load();
                                },
                                'beforequery': function (e) {
                                    var combo = e.combo;
                                    if (!e.forceAll) {
                                        var input = e.query;
                                        // 检索的正则
                                        var regExp = new RegExp(".*" + input + ".*");
                                        // 执行检索
                                        combo.store.filterBy(function (record, id) {
                                            // 得到每个record的项目名称值
                                            var text = record.get(combo.displayField);
                                            return regExp.test(text);
                                        });
                                        combo.expand();
                                        return false;
                                    }
                                },
                            },
                        }),*/
                        {
                            xtype: 'displayfield',
                            value: '机构：',
                        },
                        new Ext.form.ComboBox({
                            width: 174,
                            listWidth: 350,
                            hiddenName: 'org_id',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'remote',
                            queryParam: 'keyword',
                            minChars: 2,
                            displayField: 'org_name',//显示的值
                            valueField: 'id',//后台接收的key
                            store: getOilOrg,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                        }),
                        /*new Ext.form.ComboBox({
                            width: 180,
                            hiddenName:'org_id',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'local',
                            displayField: 'org_name',//显示的值
                            valueField: 'id',//后台接收的key
                            store: getOilOrg,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                            listeners:{
                                'focus': function(){
                                    getOilOrg.load();
                                },
                                'beforequery': function (e) {
                                    var combo = e.combo;
                                    if (!e.forceAll) {
                                        var input = e.query;
                                        // 检索的正则
                                        var regExp = new RegExp(".*" + input + ".*");
                                        // 执行检索
                                        combo.store.filterBy(function (record, id) {
                                            // 得到每个record的项目名称值
                                            var text = record.get(combo.displayField);
                                            return regExp.test(text);
                                        });
                                        combo.expand();
                                        return false;
                                    }
                                },
                            }
                        }),*/
                        {
                            xtype: 'displayfield',
                            value: '机构运营商：',
                            style: 'padding-left: 10px'
                        },
                        {
                            xtype: 'combo',
                            width: 220,
                            id   : 'operators_id',
                            hiddenName : "operators_id",
                            editable: true,
                            emptyText : '全部',
                            triggerAction:'all',
                            displayField: 'company_name',
                            valueField: 'id',
                            store  : getOperatorsInfo
                        },
                        {
                            xtype: 'displayfield',
                            value: '授信账户：'
                        },
                        {
                            xtype: 'combo',
                            width: 180,
                            hiddenName : "credit_account_id",
                            editable: true,
                            emptyText : '全部',
                            mode: 'local',
                            typeAhead: true,
                            triggerAction:'all',
                            forceSelection: true,
                            selectOnFocus: true,
                            displayField: 'credit_account',
                            valueField: 'id',
                            store  : getCreditProviders,
                            enableKeyEvents: true,
                            listWidth:320,
                            listeners: {
                                expand: function (combo) {
                                    getCreditProviders.load();
                                },
                                'focus': function () {
                                    getCreditProviders.load();
                                },
                                'beforequery': function (e) {
                                    active_flag = false;
                                    var combo = e.combo;
                                    if (!e.forceAll) {
                                        var input = e.query;
                                        // 检索的正则
                                        var regExp = new RegExp(".*" + input + ".*");
                                        // 执行检索
                                        combo.store.filterBy(function (record, id) {
                                            // 得到每个record的项目名称值
                                            var text = record.get(combo.displayField);
                                            return regExp.test(text);
                                        });
                                        combo.expand();
                                        return false;
                                    }
                                }
                            }
                        },
                        {
                            xtype: 'displayfield',
                            value: '状态：',
                            style: 'padding-left: 10px'
                        },
                        {
                            xtype: 'combo',
                            mode  : 'local',
                            hiddenName : "status",
                            width: 80,
                            emptyText: '请选择..',
                            triggerAction : 'all',
                            forceSelection: true,
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key','value'],
                                data: [['-1','已驳回'], ['0','待审核'], ['1','已审核']]
                            })
                        },
                    ]
            },
            {//第二行
                xtype : 'compositefield',
                items :
                    [

                        {
                            xtype: 'displayfield',
                            value: '申请时间：'
                        },
                        {
                            xtype: "datefield",
                            name:'apply_timeGe',
                            format:'Y-m-d',
                            width: 100
                        },
                        {
                            xtype: 'displayfield',
                            value: '—'
                        },
                        {
                            xtype: "datefield",
                            name:'apply_timeLe',
                            format:'Y-m-d',
                            width: 100
                        },
                        {
                            xtype: 'displayfield',
                            value: '创建人：'
                        },
                        {
                            xtype : 'textfield',
                            name  : 'creator_name',
                            width : 100
                        },
                        {
                            xtype: 'displayfield',
                            value: '创建时间：'
                        },
                        {
                            xtype: "datefield",
                            name:'createtimeGe',
                            format:'Y-m-d',
                            width: 100
                        },
                        {
                            xtype: 'displayfield',
                            value: '—'
                        },
                        {
                            xtype: "datefield",
                            name:'createtimeLe',
                            format:'Y-m-d',
                            width: 100
                        },
                        {
                            xtype: 'displayfield',
                            value: '最后修改人：'
                        },
                        {
                            xtype : 'textfield',
                            name  : 'last_operator',
                            width : 120
                        },
                        {
                            xtype: 'displayfield',
                            value: '最后修改时间：'
                        },
                        {
                            xtype: "datefield",
                            name:'updatetimeGe',
                            format:'Y-m-d',
                            width: 100
                        },
                        {
                            xtype: 'displayfield',
                            value: '—'
                        },
                        {
                            xtype: "datefield",
                            name:'updatetimeLe',
                            format:'Y-m-d',
                            width: 100
                        },
                    ]
            },
            {//第二行
                xtype : 'compositefield',
                items :
                    [
                        {
                            xtype: 'displayfield',
                            value: '单据类型：',
                            style: 'padding-left: 10px'
                        },
                        {
                            xtype: 'combo',
                            mode  : 'local',
                            hiddenName : "no_way",
                            width: 100,
                            emptyText: '请选择..',
                            triggerAction : 'all',
                            forceSelection: true,
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key','value'],
                                data: [['10','正常还款'], ['20','消费撤销'],['','全部']]
                            })
                        },
                        {
                            xtype: 'displayfield',
                            value: '备注内：',
                            style: 'padding-left: 10px'
                        },
                        {
                            xtype : 'textfield',
                            name  : 'remark',
                            width : 120
                        },
                        {
                            xtype: 'displayfield',
                            value: '备注外：'
                        },
                        {
                            xtype : 'textfield',
                            name  : 'remark_work',
                            width : 120
                        },
                        {
                            xtype:'button',
                            text:'查询',
                            style : 'padding-left : 10px;',
                            handler: function()
                            {
                                main_store.removeAll();//移除原来的数据
                                main_store.load();//加载新搜索的数据
                            }
                        },
                        {
                            xtype: 'button',
                            text: '重置',
                            style: 'padding-left : 10px;',
                            handler: function () {
                                top_panel.getForm().reset();
                                main_store.removeAll();//移除原来的数据
                                main_store.load();//加载新搜索的数据
                                // btnInit();
                            }
                        },
                    ]
            }
        ]
});