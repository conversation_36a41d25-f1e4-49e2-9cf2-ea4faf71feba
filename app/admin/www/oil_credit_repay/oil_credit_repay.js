/**
 * 全局类
 */

function oiltest() {
    var btnInit = function(status){
        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').enable() : '';//修改按钮
        if(status == '0'){//待审核
            (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').enable() : '';//驳回
            (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').enable() : '';//审核
            (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').enable() : '';//删除按钮
        }else if(status == '-1'){//已驳回
            (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
            (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
            (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').enable() : '';//删除按钮
        }else if(status == '1'){//已审核
            (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
            (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
            (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').disable() : '';//删除按钮
        }
    }
    var getSelectRecord = function(){
        return grid_list.getSelectionModel().getSelections()[0];
    }
    //列表表格
    this.getGridPanel = function () {
        var _sm =new Ext.grid.RowSelectionModel({
            singleSelect: true,
            listeners: {
                selectionchange: function(data) {
                    if (data.getCount()){
                        var row = data.getSelections()[0].data;
                        btnInit(row.status);
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').enable() : '';//修改按钮
                    }else{
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').disable() : '';//修改按钮
                        (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
                        (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
                        (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').enable() : '';//删除按钮
                    }
                }
            }
        });

        var grid_list = new Ext.grid.GridPanel({
            title	 :'授信账户还款',
            region	 : 'center',
            loadMask : true,
            cm : new Ext.grid.ColumnModel([
                {header : '单号',dataIndex : 'no',width : 120},
                {header : '顶级机构',dataIndex : '_root_org_id',width : 180},
                {header : '机构',dataIndex : '_org_id',width : 180},
                {header : '授信关联还款公司',dataIndex : 'credit_pay_company_name',width : 180},
                {header : '还款资金来源',dataIndex : 'pay_company_name',width : 180},
                {header : '机构运营商',dataIndex : '_operators_id',width : 220},
                {header : '授信账户',dataIndex : '_credit_provider_id',width : 120},
                {header : '还款金额',dataIndex : 'repay_money',width : 80},
                {header : '状态',dataIndex : '_status',width : 80},
                {header : '单据类型',dataIndex : 'way_txt',width : 80},
                {header : '申请时间',dataIndex : 'apply_time',width : 120},
                {header : '创建人',dataIndex : 'creator_name',width : 120},
                {header : '创建时间',dataIndex : 'createtime',width : 120},
                {header : '最后修改人',dataIndex : 'last_operator',width : 120},
                {header : '最后修改时间',dataIndex : 'updatetime',width : 120},
                {header : '备注内',dataIndex : 'remark',width : 180},
                {header : '备注外',dataIndex : 'remark_work',width : 180},
            ]),
            store : main_store,
            sm :_sm,
            //分页
            bbar: new Ext.PagingToolbar({
                plugins: new Ext.ux.plugin.PagingToolbarResizer,
                pageSize: pagesize,
                displayInfo : true,
                displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
                emptyMsg : '没有符合条件的记录',
                store : main_store
            }),
            tbar : []
        });

        return grid_list;
    };

    //关闭窗口
    this.closeWin = function (id) {
        if (!id) return;
        var win = Ext.getCmp(id);
        win.destroy();
    };

    var _getWindow = function (config) {
        var width = config.width,
            height = config.height,
            title = config.title,
            id = config.id,
            button = config.button;

        button.push({
            text: '取消',
            id: 'button_cancer',
            handler: function () {
                var oilTest = new oiltest();
                oilTest.closeWin(id);
            }
        });
        var windows = new Ext.Window({
            layout: 'border',
            region: 'center',
            width: width,
            modal: true,//遮挡窗口后的内容
            id: id,
            height: height,
            title: title,
            closeAction: 'destroy',
            buttons: button
        });

        return windows;
    };

    //添加
    this.add = function () {
        var windows = _getWindow({
            title: '添加-授信账户还款',
            id: 'add_win',
            width: 1000,
            height: 200,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'add-form',
            items: addPanel.getPanel('add-form', 'add_win')
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };

    //编辑
    this.update = function () {
        var sm   = grid_list.getSelectionModel();
        var data = sm.getSelections();
        data[0] ? id = data[0].get("id") : '';
        data[0] ? status = data[0].get("status") : '';
        var windows = _getWindow({
            title: '编辑-授信账户还款',
            id: 'update_win',
            width: 1000,
            height: 200,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });
        if(status == 0){
            var items = addPanel.getPanel('update-form', 'update_win', id);
        }else{
            var items = addPanel.getPanelAfterAudit('update-form', 'update_win', id);
        }

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'update-form',
            items: items
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();

        addPanel.init(data[0]);
    };

    //删除
    this.delete = function () {
        Ext.MessageBox.confirm('删除', '删除之后该条纪录将不再显示，确定要删除？', function showResult(btn){
            if (btn == 'yes')
            {
                var sm   = grid_list.getSelectionModel();
                var data = sm.getSelections();
                var ids = data[0].get("id");
                Ext.Ajax.request({
                    url:getUrl(controlName,'remove'),
                    method:'post',
                    params:{ids:ids},
                    success: function sFn(response,options)
                    {
                        var result = Ext.decode(response.responseText);
                        Ext.MessageBox.alert('提示', result.msg);
                        if (result.success != false) {
                            main_store.reload({
                                callback:function(){
                                    btnInit('-10');
                                }
                            });
                        }
                        //main_store.removeAll();
                        main_store.load();
                        //Ext.Msg.alert('系统提示', '删除成功');
                    }
                });
            }
        });
    };
    //驳回
    this.reject = function () {
        var record = getSelectRecord();//获取选中行
        Ext.MessageBox.confirm('提示', '授信账户还款工单驳回后无法进行编辑，也无法进行其他操作。<br>确定要驳回挂失工单吗？', function showResult(btn) {
            if (btn == 'yes') {
                Ext.Ajax.request({
                    url: getUrl(controlName, 'reject'),
                    method: 'post',
                    params: {id: record.get('id')},
                    success: function sFn(response, options) {
                        var result = Ext.decode(response.responseText);
                        Ext.MessageBox.alert('提示', result.msg);
                        if (result.success != false) {
                            main_store.reload({
                                callback:function(){
                                    btnInit('-10');
                                }
                            });
                        }
                    }
                });
            }
        });
    }
    //审核
    this.audit = function () {
        var record = getSelectRecord();//获取选中行
        Ext.MessageBox.confirm('提示', '确定要审核通过授信账户还款工单吗？', function showResult(btn) {
            if (btn == 'yes') {
                Ext.Ajax.request({
                    url: getUrl(controlName, 'audit'),
                    method: 'post',
                    params: {id: record.get('id')},
                    success: function sFn(response, options) {
                        var result = Ext.decode(response.responseText);
                        Ext.MessageBox.alert('提示', result.msg);
                        if (result.success != false) {
                            main_store.reload({
                                callback:function(record){
                                    btnInit('10');
                                }
                            });
                        }
                    }
                });
            }
        });
    }
    //导出
    this.export = function () {
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn == 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = '/inside.php?t=json&m='+ controlName +'&f=getList&_export=1'+'&'+params;
                }
            }
        );
    };
}
var oilTest = new oiltest();