/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_credit_repay';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","no","no_type","org_id","orgname", "pay_company_name", "root_orgname","operators_id",
            "operators_name","credit_provider_id","credit_provider_name","credit_account_id","repay_money",
            "status","remark","remark_work","creator_id","creator_name","last_operator_id","last_operator",
            "createtime","updatetime","deleted_at","_credit_provider_id","_operators_id","_org_id","_root_org_id",
            "_status","apply_time","way_txt","credit_pay_company_name"]
    ),
});

//获取授信方列表
var getCreditProvider = new Ext.data.Store({
    url: '../inside.php?t=json&m=oil_credit_repay&f=getCreditAccount',
    autoLoad:true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data.data'
    }, ['id', 'credit_account','account_no','credit_provider_name'])
});

//获取授信方列表top
var getCreditProviders = new Ext.data.Store({
    url: '../inside.php?t=json&m=oil_credit_repay&f=getCreditAccount',
    autoLoad:true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data.data'
    }, ['id', 'credit_account','account_no','credit_provider_name'])
});

//创建机构列表
/*var getOilOrg = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_card_vice&f=getOilOrg', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: ''
    }, ['id', 'orgcode', 'org_name','orgname','operators_id'])
});*/

var getOilOrg = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_org&f=getOilOrg', method: "POST"}),
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id', 'org_name', 'orgcode', 'orgname'])
});


//获取运营商信息
var getOperatorsInfo = new Ext.data.Store({
    url: '../inside.php?t=json&m=oil_operators&f=getlist',
    autoLoad:true,
    reader: new Ext.data.JsonReader({
        totalProperty: 'data.total',
        root: 'data.data'
    }, ['id', 'name','company_name'])
});

//顶级机构列表
var getTopOilOrg = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_org&f=getTopOilOrg', method: "POST"}),
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data.data'
    }, ['id', 'orgcode', 'org_name'])
});


var getTopOilOrgNew = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_org&f=getOilOrg', method: "POST"}),
    // autoLoad: true,
    baseParams:{is_root:1},
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['id','orgcode', 'org_name'])
});

