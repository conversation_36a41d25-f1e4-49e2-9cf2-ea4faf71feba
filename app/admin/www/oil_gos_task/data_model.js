/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_gos_task';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","task_id","task_name","params","method","method_type","data","send_use_time","callback_use_time", "code","message","createtime","updatetime", 'status','createuser','updateuser'       ]
    ),
});