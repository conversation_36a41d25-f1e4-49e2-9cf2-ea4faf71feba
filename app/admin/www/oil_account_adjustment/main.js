Ext.onReady(function(){
	Ext.QuickTips.init();
	Ext.form.Field.prototype.msgTarget = "title";
	Ext.Ajax.request({
		url 	: '../inside.php?t=json&m=login&f=getConfig',
		method  : 'POST',
		success : function( resp,opts )
		{
			var data = Ext.util.JSON.decode(resp.responseText);
			
			//如果没有登录，则跳转到登录页面
			if(!data.id){
				window.location.href = "../login/";
			}else{
				var main_panel = new Ext.Panel({
					header:false,
					region:"center",
					layout:"border",
					items:
					[
						top_panel,
						main_tab_panel
					]
				});
				
				//渲染到模板
				new Ext.Viewport(
					{
						enableTabScroll:true,
						layout:"border",
						items:
						[
							main_panel
						]
					}
				);
				 main_store.addListener('beforeload',function(){
					this.baseParams = top_panel.getForm().getValues();
					this.baseParams.start = 0;
					this.baseParams.limit = pagesize;
				});
                //加载数据
				main_store.load();
				editSource('oil_account_adjustment',grid_list);
			}
		}
	});

});