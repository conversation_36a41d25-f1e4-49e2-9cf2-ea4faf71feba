/**
 * 定义添加组件容器类
 */
var addPanel = {
	id:'',
	formId:'',
	winId:'',
	//初始化容器
	init: function (params) {
		if(this.id){//编辑
			Ext.getCmp(this.formId).getForm().setValues([
				{id:'title', value:params.get("title")},
				{id:'content', value:params.get("content")},
			]);
		}else{//添加
			Ext.getCmp(this.formId).getForm().setValues([
				{id:'title', value:''},
				{id:'content', value:''},
			]);
		}
	},
	//获取组件容器
	getPanel: function (formId, winId, id) {
		this.id = id;
		this.formId = formId;
		this.winId = winId;
		var panel = new Ext.Panel({
			region: "center",
			hideLabels: true,
			autoScroll: true,
			bodyStyle: 'padding: 10px',
			buttonAlign: 'center',
			fileUpload: true,
			border: false,
			items: [
				{// 第一行
					xtype : 'compositefield',
					items :
						[
							{
								xtype: 'displayfield',
								value: '标 题：'
							},
							{
								xtype : 'textfield',
								id    : 'title',
								width : 200
							},
							{xtype: 'displayfield',value: ' <font color=red>*</font>'},
						]
				},
				{// 第二行
					xtype : 'compositefield',
					style:'padding-top:5px;',
					items :
						[
							{
								xtype: 'displayfield',
								value: '内 容：'
							},
							{
								xtype:"textarea",
								id:"content",
								width:350,
								height : 120
							},
							{xtype: 'displayfield',value: ' <font color=red>*</font>'},
						]
				}
			]
		});

		return panel;
	},
	//提交表单
	submit: function () {
		var _form = Ext.getCmp(this.formId).getForm();
		var that = this;
		if (_form.isValid()) {
			var url = '';
			if (this.id) {
				url = getUrl(controlName, 'edit');
			} else {
				url = getUrl(controlName, 'create');
			}
			_form.submit({
				url: url,
				waitMsg: 'Saving Data...',
				params:{id:that.id},
				success: function (form, action) {
					Ext.MessageBox.alert("消息!", '操作成功');
					main_store.removeAll();
					main_store.load();
					oilTest.closeWin(that.winId)
				},
				failure: function (form, action) {
					Ext.MessageBox.alert("消息!", '操作失败');
				}
			});
		} else {
			Ext.MessageBox.alert("提示", '输入有误，请检查');
		}
	}
}