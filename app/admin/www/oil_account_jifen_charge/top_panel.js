/**
 * 获取日期函数
 * @param $flag 1 为月初，2为当前日期
 * @returns {String}
 */
function GetDateStr($flag) {
    var dd = new Date();
    var y = dd.getFullYear();
    var m = dd.getMonth() + 1;//获取当前月份的日期
    if (m.toString().length == 1) {
        m = '0' + m;
    }
    var d = '';
    if ($flag == 1)
        d = "01";
    else {
        d = dd.getDate();
        if (d.toString().length == 1) {
            d = '0' + d;
        }
    }

    return y + "-" + m + "-" + d;
}
//运营商列表
var getOperators = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_account_money_charge&f=getOperators', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        tatalProperty: '',
        root: ''
    }, ['id', 'code', 'name'])
});
getOperators.load();
var top_panel = new Ext.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 70,
    items: [
        {//第一行
            xtype: 'compositefield',
            items: [
                {xtype: 'displayfield', value: '单号：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_no',
                    name: 'no',
                    width: 100,
                    listeners: {
                        'specialkey': function (field, e) {
                            if (e.getKey() == Ext.EventObject.ENTER) {//响应回车
                                store_service.removeAll();
                                store_service.load();
                                sm_service.clearSelections();
                            }
                        }
                    },
                },
                {
                    xtype: 'displayfield',
                    value: '顶级机构：',
                },
                new Ext.form.ComboBox({
                    width: 240,
                    id: 'orgroot',
                    hiddenName: 'orgroot',
                    mode: 'local',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'org_name',//显示的值
                    valueField: 'orgcode',//后台接收的key
                    store: getTopOilOrg,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        'focus': function () {
                            getTopOilOrg.load();
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                    }
                }),
                {
                    xtype: 'displayfield',
                    value: '机构：',
                },
                new Ext.form.ComboBox({
                    width: 240,
                    id: 's_oil_org',
                    hiddenName: 'orgcode',
                    mode: 'local',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'org_name',//显示的值
                    valueField: 'orgcode',//后台接收的key
                    store: getOilOrg,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        'focus': function () {
                            getOilOrg.load();
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                    }
                }),
                {
                    xtype: "checkbox",
                    id: "org_flag",
                    name: 'org_flag',
                    checked: true,
                },
                {
                    xtype: 'displayfield',
                    value: '(包含下级)',
                },
                {
                    xtype: 'displayfield',
                    value: '申请时间：',
                    style: 'padding-left:10px;'
                },
                {
                    xtype: "datefield",
                    id: "s_start_time",
                    name: 'app_timeGe',
                    format:'Y-m-d',
                    width: 100,
                },
                {
                    xtype: 'displayfield',
                    value: '—'
                },
                {
                    xtype: "datefield",
                    id: "s_end_time",
                    name: 'app_timeLe',
                    format:'Y-m-d',
                    width: 100,
                },
            ]
        },
        {//第二行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '机构运营商：'
                },
                new Ext.form.ComboBox({
                    width: 160,
                    id: 'name',
                    hiddenName: 'operator_id',
                    mode: 'local',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'name',//显示的值
                    valueField: 'id',//后台接收的key
                    store: getOperators,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        'focus': function () {
                            getOperators.load();
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                    }
                }),
                {
                    xtype: 'displayfield',
                    value: '状态：',
                },
                {
                    xtype: 'combo',
                    hiddenName: 'status',
                    id: 's_status',
                    mode: 'local',
                    width: 100,
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'status_name',
                    valueField: 'status_id',
                    value: '',
                    store: new Ext.data.SimpleStore({
                        fields: ['status_id', 'status_name'],
                        data: [['0', '未审核'], ['1', '已审核'], ['-1', '驳回']]
                    }),
                },
                {
                    xtype: 'displayfield',
                    value: '最后修改时间：'
                },
                {
                    xtype: "datetimefield",
                    id: "s_start_utime",
                    name:'updatetimeGe',
                    format:'Y-m-d H:i:s',
                    width: 145
                },
                {
                    xtype: 'displayfield',
                    value: '—'
                },
                {
                    xtype: "datetimefield",
                    id: "s_end_utime",
                    name:'updatetimeLe',
                    format:'Y-m-d H:i:s',
                    width: 145
                },
                {
                    xtype: 'displayfield',
                    value: '是否为测试机构：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype: 'combo',
                    width: 100,
                    hiddenName : "is_test",
                    editable: true,
                    emptyText : '全部',
                    mode: 'local',
                    triggerAction:'all',
                    displayField: 'name',
                    valueField: 'value',
                    store : new Ext.data.SimpleStore({
                        fields: ['name', 'value'],
                        data: [['全部', ''], ['否', '1'], ['是', '2']]
                    })
                },
                {
                    xtype: 'button',
                    text: '查询',
                    style: 'padding-left : 30px;',
                    handler: function () {
                        store_main.removeAll();
                        store_main.load();
                        (Ext.getCmp('btnDel')) ? Ext.getCmp('btnDel').disable() : '';//删除
                        (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
                    }
                },
                {
                    xtype: 'button',
                    text: '重置',
                    style: 'padding-left : 10px;',
                    handler: function () {
                        Ext.getCmp('s_no').setValue('');
                        Ext.getCmp('s_oil_org').setValue('');
                        Ext.getCmp('name').setValue('');
                        Ext.getCmp('s_start_time').setValue(GetDateStr(1));
                        Ext.getCmp('s_end_time').setValue(GetDateStr(2));
                        Ext.getCmp('s_start_utime').setValue('');
                        Ext.getCmp('s_end_utime').setValue('');
                        Ext.getCmp('s_status').setValue('');
                        Ext.getCmp('org_flag').setValue(true);
                        store_main.removeAll();//移除原来的数据
                        store_main.load();//加载新搜索的数据
                        btnInit();
                    }
                },
            ]
        }

    ]
});

