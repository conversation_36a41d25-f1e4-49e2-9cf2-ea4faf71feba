var crow;
var pagesize = 50;
var winClose;
var controlName = 'oil_account_jifen_charge';
//资源搜索
var store_main = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=' + controlName + '&f=search', method: "POST"}),
    reader: new Ext.data.JsonReader({
        totalProperty: 'data.total',
        root: 'data.data'
    }, [
        {name: 'id'},
        {name: 'no'},
        {name: 'no_type'},
        {name: 'org_id'},
        {name: 'org_name'},
        {name: 'orgroot'},
        {name: 'app_time'},
        {name: 'jifen'},
        {name: 'status'},
        {name: '_status'},
        {name: 'main_no'},
        {name: 'show_g7s'},
        {name: 'data_from'},
        {name: 'true_name'},
        {name: 'remark'},
        {name: 'remark_work'},
        {name:'last_operator'},
        {name:'updatetime'},
        {name:'org_operator'},
        {name: 'operator_id'},
        {name: 'is_test'},
        {name: 'show_g7s_name'},
    ]),
});

//导出
function exportData() {
    Ext.getCmp('btnExport').disable();
    var params = top_panel.getForm().getValues(true);
    // Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
    //         if (btn == 'yes') {
    //             var params = top_panel.getForm().getValues(true);
    //             window.open('/inside.php?t=json&m=' + controlName + '&f=search&_export=1' + '&' + params);
    //         }
    //     }
    // );
    Ext.Ajax.request({
        url:'../inside.php?t=json&m=oil_account_jifen_charge&f=search&_export=1',
        method:'post',
        params:params,
        success: function sFn(response,options)
        {
            Ext.getCmp('btnExport').enable();

            var url = Ext.decode(response.responseText).data.redirect_url;
            console.log(url);
            window.open(url);

            var msg = Ext.decode(response.responseText).msg;
            Ext.Msg.alert('提示',msg);
        }
    });
}

/**
 * 根据状态调整按钮是否可用
 * @param status
 */
function btnStatus(status) {
    (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').enable() : '';//编辑
    if (status == 0) {
        (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').enable() : '';//删除
        (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').enable() : '';//审核
        (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').enable() : '';//驳回
        (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
    }
    else if (status == 1) {
        (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
        (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
        (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
        (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').enable() : '';//销审
    }
    else if (status == -1) {
        (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').enable() : '';//删除
        (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').enable() : '';//审核
        (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
        (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
    }
}
//选择行
var sm_service = new Ext.grid.RowSelectionModel({
    singleSelect: false,
    listeners: {
        selectionchange: function (data) {
            if (data.hasSelection()) {
                crow = data.selections.itemAt(0).data;

                var sm = grid_service.getSelectionModel();
                var info = sm.getSelections();
                var status = info[0].get('_status');
                btnStatus(status);
            }
            else {
                (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
                (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
                (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
                (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
                (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
            }
        }
    }
});

//定义列表
var cm_service = new Ext.grid.ColumnModel([
    //sm_service,
    new Ext.grid.RowNumberer({'header': '序号', width: 35}),
    {header: '单号', dataIndex: 'no', sortable: true, width: 150},
    {header: '顶级机构', dataIndex: 'orgroot', sortable: true, width: 150},
    {header: '机构', dataIndex: 'org_name', sortable: true, width: 150},
    {header: '机构运营商', dataIndex: 'org_operator', sortable: true, width: 150},
    {header: '主卡号', dataIndex: 'main_no', sortable: true, width: 160},
    {header: '申请时间', dataIndex: 'app_time', sortable: true, width: 130},
    {header: '充值积分', dataIndex: 'jifen', sortable: true, width: 110},
    {header: '状态', dataIndex: 'status', sortable: true, width: 90},
    {header: '是否在G7S端显示', dataIndex: 'show_g7s_name', sortable: true, width: 110},
    {header: '数据来源', dataIndex: 'data_from', sortable: true, width: 80},
    {header: '创建人', dataIndex: 'true_name', sortable: true, width: 110},
    {header:'最后修改人',dataIndex:'last_operator',sortable:true,width:110},
    {header:'最后修改时间',dataIndex:'updatetime',sortable:true,width:130},
    {header:'测试机构',dataIndex:'is_test',sortable:true,width:70},
    {header: '备注/内', dataIndex: 'remark', sortable: true, width: 150},
    {header: '备注/外', dataIndex: 'remark_work', sortable: true, width: 150},
]);

var pageTool = new Ext.PagingToolbar({
    plugins: new Ext.ux.plugin.PagingToolbarResizer,
    pageSize: pagesize,
    displayInfo: true,
    displayMsg: '当前记录数: {0} - {1} 总记录数: {2}',
    emptyMsg: '没有符合条件的记录',
    store: store_main
});
//创建grid
var grid_service = new Ext.grid.GridPanel({
    loadMask: true,
    enableColumnMove: false,
    enableColumnResize: true,
    title: '充值积分',
    stripeRows: true,//斑马颜色
    autoScroll: true,
    store: store_main,
    cm: cm_service,
    sm: sm_service,
    tbar: [],
    //分页
    bbar: pageTool,
});
function $(id) {
    return document.getElementById(id);
}
/**
 * 初始化表单控件
 * @param status
 */
function initFormInput(status)
{
    if(status < 1){
        Ext.getCmp('org_id').enable();
        Ext.getCmp('main_no').setReadOnly(false);
        Ext.getCmp('app_time').enable();
        Ext.getCmp('jifen').enable();
        Ext.getCmp('show_g7s').enable();
    }else{
        Ext.getCmp('org_id').disable();
        Ext.getCmp('main_no').setReadOnly(true);
        Ext.getCmp('app_time').disable();
        Ext.getCmp('jifen').disable();
        Ext.getCmp('show_g7s').disable();
    }
}
function showEdit() {
    var sm = grid_service.getSelectionModel();
    var data = sm.getSelections();
    var ids = '';
    for (var i = 0; i < data.length; i++) {
        (ids) ? ids = ids + ',' : '';
        ids += data[i].get("id");
    }
    if (ids.indexOf(',') == -1) {
        var row = crow;
        showAdd(row.id);
        initFormInput(data[0].get('_status'));//初始化表单控件
        panel_add.getForm().setValues([
            {id: 'org_id', value: row.org_id},
            {id: 'app_time', value: row.app_time},
            {id: 'jifen', value: row.jifen},
            {id: 'main_no', value: row.main_no},
            {id: 'remark', value: row.remark},
            {id: 'remark_work', value: row.remark_work},
            {id: 'operators_name', value: row.org_operator},
            {id: 'operator_id', value: row.operator_id},
            {id: 'show_g7s', value: row.show_g7s},
        ]);
    } else {
        Ext.MessageBox.alert('提示', '每次只能选择一行进行编辑');
    }


}

function btnInit() {
    (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
    (Ext.getCmp('deletebtn')) ? Ext.getCmp('deletebtn').disable() : '';//删除
    (Ext.getCmp('auditbtn')) ? Ext.getCmp('auditbtn').disable() : '';//审核
    (Ext.getCmp('rejectbtn')) ? Ext.getCmp('rejectbtn').disable() : '';//驳回
    (Ext.getCmp('unauditbtn')) ? Ext.getCmp('unauditbtn').disable() : '';//销审
}

/**
 * 删除 操作方法
 * url  AJAX请求PHP的方法 noticeDelete=>删除
 */
function customerExecute(url) {
    var sm = grid_service.getSelectionModel();
    var data = sm.getSelections();
    var card_no = data[0].get('no');
    var type = '';
    var str = '';
    if (url == 'delete') {
        type = '删除';
        str = '删除之后单号[' + card_no + ']将不再显示，确定要删除？';
    }
    else if (url == 'cardAudit') {
        type = '审核';
        str = '确定要审核通过单号[' + card_no + ']？';
    }
    else if (url == 'cardReject') {
        type = '驳回';
        str = '确定要驳回单号[' + card_no + ']？';
    }
    else if (url == 'cardUnAudit') {
        type = '销审';
        str = '确定要销审单号[' + card_no + ']？';
    }
    var ids = '';
    ids = data[0].get("id");
    Ext.MessageBox.confirm('' + type + '', str, function showResult(btn) {
        if (btn == 'yes') {
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=' + controlName + '&f=' + url,
                method: 'post',
                params: {ids: ids},
                success: function sFn(response, options) {
                    store_main.reload();//刷新页面
                    var data = Ext.decode(response.responseText);
                    if (typeof data.status !== undefined) {
                        btnStatus(data.status);
                    }
                    Ext.Msg.alert('系统提示', data.msg);
                }
            });
        }
    });
}

	