var requireTip = {xtype: 'displayfield', html: '<font color="red" class="requireTip">*</font>'};

/**
 * 定义添加组件容器类
 */
var addPanel = {
	id:'',
	formId:'',
	winId:'',
	url:'',
	//初始化容器
	init: function (params) {
		if(this.id){//编辑
			Ext.getCmp(this.formId).getForm().loadRecord({data: params.data});
		}
	},
	//获取组件容器
	getPanel: function (formId, winId, id) {
        this.url = '';
		this.id = id;
		this.formId = formId;
		this.winId = winId;
		var panel = [
            {//第1行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '单号',
                            id:'no'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '状态',
                            id:'status_name'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items:
                            {
                                xtype: 'compositefield',
                                fieldLabel: '所属机构',
                                items: [
                                    new Ext.form.ComboBox({
                                        value: '',
                                        width: 120,
                                        hiddenName: 'orgcode',
                                        triggerAction: 'all',
                                        forceSelection: true,
                                        mode: 'local',
                                        displayField: 'org_name', //显示的值
                                        valueField: 'orgcode', //后台接收的key
                                        store: getOilOrg,
                                        emptyText: '请选择..',
                                        enableKeyEvents: true,
                                        listeners: {
                                            'focus': function() {
                                                getOilOrg.load();
                                            },
                                            'beforequery': function(e) {
                                                var combo = e.combo;
                                                if (!e.forceAll) {
                                                    var input = e.query;
                                                    // 检索的正则
                                                    var regExp = new RegExp(".*" + input + ".*");
                                                    // 执行检索
                                                    combo.store.filterBy(function(record, id) {
                                                        // 得到每个record的项目名称值
                                                        var text = record.get(combo.displayField);
                                                        return regExp.test(text);
                                                    });
                                                    combo.expand();
                                                    return false;
                                                }
                                            },
                                        }
                                    }),
                                    requireTip
                                ]
                            },
                    },{
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '顶级机构',
                            id:'orgroot_name'
                        }
                    }]
            },
            {//第2行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                                xtype: 'compositefield',
                                items: [
                                    {
                                        xtype: 'combo',
                                        hiddenName: 'oil_com',
                                        fieldLabel: "油卡类型",
                                        mode  : 'local',
                                        width: 100,
                                        submitValue: true,
                                        triggerAction: 'all',
                                        forceSelection: true,
                                        allowBlank: false,
                                        blankText: '不能为空',
                                        displayField: 'oil_com',
                                        valueField: 'id',
                                        store: oilComStore,
                                        listeners: {
                                            'focus': function() {
                                                oilComStore.load();
                                            },
                                            'beforequery': function(e) {
                                                var combo = e.combo;
                                                if (!e.forceAll) {
                                                    var input = e.query;
                                                    // 检索的正则
                                                    var regExp = new RegExp(".*" + input + ".*");
                                                    // 执行检索
                                                    combo.store.filterBy(function(record, id) {
                                                        // 得到每个record的项目名称值
                                                        var text = record.get(combo.displayField);
                                                        return regExp.test(text);
                                                    });
                                                    combo.expand();
                                                    return false;
                                                }
                                            },
                                            'select': function (combo, record, index) {
                                                getProvince.removeAll();
                                                getProvince.baseParams = {oil_com: record.data.id};
                                                getProvince.load();
                                            },
                                        }
                                    },
                                    requireTip
                                ],
                            },
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'combo',
                            width: 100,
                            hiddenName: 'fanli_region',
                            fieldLabel: "开卡地区",
                            mode: 'local',
                            triggerAction: 'all',
                            forceSelection: true,
                            displayField: 'province',//显示的值
                            valueField: 'id',//后台接收的key
                            store: getProvince,
                            emptyText: '请选择..',
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype:'compositefield',
                            items:[
                                {
                                    xtype: "textfield",
                                    fieldLabel: "数量",
                                    allowBlank: false,
                                    regex: /^\d+$/,
                                    regexText: '请输入整数',
                                    blankText: '不能为空',
                                    name: 'num',
                                    width: 100
                                },
                                requireTip
                            ]
                        }
                    },{
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype:'compositefield',
                            items:[
                                {
                                    xtype: "textfield",
                                    fieldLabel: "统一密码",
                                    allowBlank: true,
                                    name: 'password',
                                    regex: /^\d{6}$/,
                                    regexText: '请输入6位数字',
                                    width: 100
                                },
                            ]
                        }
                    }]
            },
            {//第3行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: 'compositefield',
                            fieldLabel: '申请人',
                            items: [
                                new Ext.form.ComboBox({
                                    width: 150,
                                    value: '',
                                    hiddenName: 'fullName',
                                    triggerAction: 'all',
                                    forceSelection: true,
                                    mode: 'local',
                                    displayField: 'fullName', //显示的值
                                    valueField: 'id', //后台接收的key
                                    store: getOrgContact,
                                    emptyText: '请选择..',
                                    enableKeyEvents: true,
                                    listeners: {
                                        'focus': function() {
                                            getOrgContact.load();
                                        },
                                        'beforequery': function(e) {
                                            var combo = e.combo;
                                            if (!e.forceAll) {
                                                var input = e.query;
                                                // 检索的正则
                                                var regExp = new RegExp(".*" + input + ".*");
                                                // 执行检索
                                                combo.store.filterBy(function(record, id) {
                                                    // 得到每个record的项目名称值
                                                    var text = record.get(combo.displayField);
                                                    return regExp.test(text);
                                                });
                                                combo.expand();
                                                return false;
                                            }
                                        },
                                        'select': function (combo, record, index) {
                                            Ext.getCmp('org_contact_id').setValue(record.data.id);
                                            Ext.getCmp('contact_name').setValue(record.data.contact_name);
                                            Ext.getCmp('mobile').setValue(record.data.contact_mobile);
                                        },
                                    }
                                }),
                            ],
                        }
                    },
                    {
                        xtype: 'hidden',
                        id: 'org_contact_id',
                        value: ''
                    },
                    {
                        xtype: 'hidden',
                        id: 'contact_name',
                        value: ''
                    },
                    {
                        xtype: 'hidden',
                        id: 'mobile',
                        value: ''
                    },
                    {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: 'compositefield',
                            fieldLabel: '收卡地址',
                            items: [
                                new Ext.form.ComboBox({
                                    width: 300,
                                    value: '',
                                    hiddenName: 'fullAddress',
                                    triggerAction: 'all',
                                    forceSelection: true,
                                    mode: 'local',
                                    displayField: 'fullAddress', //显示的值
                                    valueField: 'id', //后台接收的key
                                    store: getOrgAddr,
                                    emptyText: '请选择..',
                                    enableKeyEvents: true,
                                    listeners: {
                                        'focus': function() {
                                            getOrgAddr.load();
                                        },
                                        'beforequery': function(e) {
                                            var combo = e.combo;
                                            if (!e.forceAll) {
                                                var input = e.query;
                                                // 检索的正则
                                                var regExp = new RegExp(".*" + input + ".*");
                                                // 执行检索
                                                combo.store.filterBy(function(record, id) {
                                                    // 得到每个record的项目名称值
                                                    var text = record.get(combo.displayField);
                                                    return regExp.test(text);
                                                });
                                                combo.expand();
                                                return false;
                                            }
                                        },
                                        'select': function (combo, record, index) {
                                            Ext.getCmp('org_addr_id').setValue(record.data.id);
                                            Ext.getCmp('org_addr_name').setValue(record.data.addr_name);
                                            Ext.getCmp('org_addr_mobile').setValue(record.data.addr_mobile);
                                            Ext.getCmp('province').setValue(record.data.province_name);
                                            Ext.getCmp('city').setValue(record.data.city_name);
                                            Ext.getCmp('area').setValue(record.data.district_name);
                                            Ext.getCmp('address').setValue(record.data.address);
                                        },
                                    }
                                }),
                            ],
                        }
                    },
                    {
                        xtype: 'hidden',
                        id: 'org_addr_id',
                        value: ''
                    },
                    {
                        xtype: 'hidden',
                        id: 'org_addr_name',
                        value: ''
                    },
                    {
                        xtype: 'hidden',
                        id: 'org_addr_mobile',
                        value: ''
                    },
                    {
                        xtype: 'hidden',
                        id: 'province',
                        value: ''
                    },
                    {
                        xtype: 'hidden',
                        id: 'city',
                        value: ''
                    },
                    {
                        xtype: 'hidden',
                        id: 'area',
                        value: ''
                    },
                    {
                        xtype: 'hidden',
                        id: 'address',
                        value: ''
                    },
                    ]
            },
            {//第4行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '创建人',
                            id:'creator_name'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '专属客服',
                            id:'exclusive_custom'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '来源',
                            id:'data_from_name'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '关联开卡单',
                            id:'card_vice_app_no'
                        }
                    }]
            },
            {//第5行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '申请时间',
                            id:'apply_time'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '受理时间',
                            id:'accept_time'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '完成时间',
                            id:'complete_time'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '处理时长',
                            id:'_handle_time'
                        }
                    }
                ]
            },
            {//第6行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "退回原因",
                            name: 'reject_reason',
                            width: 850
                        }
                    }]
            },
            {//第6行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "备注/内",
                            name: 'remark',
                            width: 850
                        }
                    }]
            },
            {//第7行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "textfield",
                            fieldLabel: "备注/外",
                            name: 'remark_work',
                            width: 850
                        }
                    }]
                        }
		];

		return panel;
	},
    getRejectReasonPanel: function (formId, winId,id, url) {
        this.id = id;
        this.url = url;
        this.formId = formId;
        this.winId = winId;
        var panel = [
            {//第3行
                layout: "column",
                items: [{
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: 'compositefield',
                            items: [
                                {
                                    xtype: "textarea",
                                    fieldLabel: "退回原因",
                                    name: 'reject_reason',
                                    allowBlank: false,
                                    blankText: '不能为空',
                                    width: 480
                                },
                                requireTip
                            ]
                        }
                    }]
            }
        ];

        return panel;
    },
    //获取组件容器(查看)
    getShowPanel: function (formId, winId, id) {
        this.id = id;
        this.formId = formId;
        this.winId = winId;
        var panel = [
            {//第1行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '单号',
                            name:'no'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '状态',
                            name:'status_name'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '所属机构',
                            name:'org_name'
                        }
                    },{
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '顶级机构',
                            name:'orgroot_name'
                        }
                    }]
            },
            {//第2行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: "油卡类型",
                            name  : '_oil_com'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            width: 100,
                            fieldLabel: "开卡地区",
                            name: 'fanli_region_name'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "数量",
                            name: 'num'
                        }
                    },{
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '统一密码',
                            name:'password'
                        }
                    }]
            },
            {//第3行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'compositefield',
                            items: [
                                {
                                    xtype: "displayfield",
                                    fieldLabel: "申请人",
                                    name: 'contact_name'
                                },
                                requireTip
                            ]
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "手机号",
                            name: 'mobile'
                        }
                    }, {
                        columnWidth: 0.5,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "收卡地址",
                            name: 'address'
                        }
                    }]
            },
            {//第4行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '创建人',
                            id:'creator_name'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '专属客服',
                            id:'exclusive_custom'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '来源',
                            id:'data_from_name'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '关联开卡工单',
                            id:'card_vice_app_no'
                        }
                    }]
            },
            {//第5行
                layout: "column",
                items: [
                    {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '申请时间',
                            id:'apply_time'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '受理时间',
                            id:'accept_time'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '完成时间',
                            name:'complete_time'
                        }
                    }, {
                        columnWidth: 0.25,
                        layout: "form",
                        items: {
                            xtype: 'displayfield',
                            fieldLabel: '处理时长',
                            name:'_handle_time'
                        }
                    }]
            },
            {//第6行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "退回原因",
                            name: 'reject_reason'
                        }
                    }]
            },
            {//第7行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "备注/内",
                            name: 'remark'
                        }
                    }]
            },
            {//第8行
                layout: "column",
                items: [
                    {
                        columnWidth: 1,
                        layout: "form",
                        items: {
                            xtype: "displayfield",
                            fieldLabel: "备注/外",
                            name: 'remark_work'
                        }
                    }]
            }
        ];

        return panel;
    },
    shopImgPanel: function (){

        var sm = grid_list.getSelectionModel();
        var data = sm.getSelections();

        var one_grid_store = new Ext.data.Store({
            proxy: new Ext.data.MemoryProxy(data[0].json.applay_details),
            autoLoad: true,
            reader: new Ext.data.JsonReader({}, ["truck_no","name","truck_img_url","id_card_front_url","id_card_back_url"])
        });

        console.log(222);
        _sm = new Ext.grid.RowSelectionModel({
            singleSelect: true,
            listeners: {
                selectionchange: function (data) {
                    if (data.getCount()) {
                        var row = data.getSelections()[0].data;
                        var imgs = [];
                        imgs[0] = {'name':'行驶证', 'url':row.truck_img_url};
                        var num = 1;
                        if (row.id_card_front_url) {
                            imgs[1] = {'name':'身份证正面', 'url':row.id_card_front_url};
                            num++;
                        }
                        if (row.id_card_back_url) {
                            imgs[num] = {'name':'身份证反面', 'url':row.id_card_back_url};
                        }
                        addImgList(imgs);
                        //显示大图
                        showBigImgurl(row.truck_img_url);
                    } else {

                    }
                }
            }
        });

        console.log(222);
        var one_grid_list = new Ext.grid.GridPanel({
            title:"资料明细",
            region: 'center',
            loadMask: true,
            width:1000,
            height:200,
            autoScroll: true,
            hideLabels: true,
            cm: new Ext.grid.ColumnModel([
                {header: '车牌号', dataIndex: 'truck_no', width: 120},
                {header: '所有人', dataIndex: 'name', width: 320},
                {header: '行驶证', dataIndex: 'truck_img_url', width: 80, renderer:function(value){
                    return "--";
                }},
                {header: '身份证正面', dataIndex: 'id_card_url', width: 80, renderer:function(value){
                    return "--";
                }},
                {header: '身份证反面', dataIndex: 'id_card_img_url', width: 80, renderer:function(value){
                    return "--";
                }},
            ]),
            store: one_grid_store,
            sm: _sm,
        });
        one_grid_store.on("load",function(store) {
            if (store.data.items.length > 0) {
                var row = store.data.items[0].data;
                var imgs = [];
                imgs[0] = {'name':'行驶证', 'url':row.truck_img_url};
                var num = 1;
                if (row.id_card_front_url) {
                    imgs[1] = {'name':'身份证正面', 'url':row.id_card_front_url};
                    num++;
                }
                if (row.id_card_back_url) {
                    imgs[num] = {'name':'身份证反面', 'url':row.id_card_back_url};
                }
                addImgList(imgs);
                //显示大图
                showBigImgurl(row.truck_img_url);
            }
        },this);

        console.log(333);
        var img_list = new Ext.Panel({
            region: 'east',
            height: 450,
            width:250,
            id:'img_list',
            autoScroll: true,
            hideLabels: true,
            defaults: {
                anchor: '-20'
            },
            style:'border: 1px solid #b5b8c8;position: absolute;top:39px;right:0px;z-index: 99;padding-left: 12px;padding-top:10px;margin-top:10px;',
            items: [
            ]
        });

        console.log(444);
        var absolutePanel = new Ext.Panel({
            region: 'east',
            height: 294,
            width:680,
            style:'border-top: 1px solid #B5B8C8;',
            items: [{
                xtype:'fieldset',
                hideLabels: true,
                // layout:'form',
                autoHeight:true,
                style: '',
                id:'bigImgurl',
                border:false,
                defaults: {
                    anchor: '-20'
                },
                items :[
                    {
                        xtype:'box',
                        height: 280,
                        width:625,
                        style:'padding-bottom:5px;float:left;',
                    }
                ]
            }]
        });
        console.log(555);
        var panel = new Ext.Panel({
            items: [one_grid_list, absolutePanel, img_list]
        });

	    return panel;
    },
	//提交表单
	submit: function () {
		var _form = Ext.getCmp(this.formId).getForm();
		var that = this;
		if (_form.isValid()) {
		    var url = this.url;
			if(!url){
                if (this.id) {
                    url = getUrl(controlName, 'edit');
                } else {
                    url = getUrl(controlName, 'create');
                }
            }

			_form.submit({
				url: url,
				waitMsg: 'Saving Data...',
				params:{id:that.id},
				success: function (form, action) {
					Ext.MessageBox.alert("消息!", action.result.msg);
					main_store.removeAll();
					main_store.load();
					oilTest.closeWin(that.winId)
				},
				failure: function (form, action) {
					Ext.MessageBox.alert("消息!", action.result.msg);
				}
			});
		} else {
			Ext.MessageBox.alert("提示", '输入有误，请检查');
		}
	}
}

function addImgList (imgs){
    var html = '';
    for (i=0; i<imgs.length; i++) {
        html += '<fieldset id="ext-comp-1084" class=" x-fieldset x-form-label-right">';
        html += '<legend class="x-fieldset-header x-unselectable">';
        html += '<span class="x-fieldset-header-text">'+imgs[i].name+'</span></legend>';
        html += '<div class="x-fieldset-bwrap">';
        html += '<div class="x-fieldset-body" style="height: auto;">';
        html += '<img src="'+imgs[i].url+'" onmouseover="showBigImgurl(this.src)" style="padding-bottom: 5px; width: 200px; height: 89px;">';
        html += '</div></div></fieldset>';
    }
    Ext.getCmp('img_list').body.update(html);
}
function showBigImgurl(imgurl) {
    var html = '<img src="'+imgurl+'" id="bigImgurl" style="padding-bottom: 5px; float: left; width: 670px; height: 275px;">';
    Ext.getCmp('bigImgurl').body.update(html);
}