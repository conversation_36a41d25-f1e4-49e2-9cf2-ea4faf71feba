/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_card_main_charge';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","api_id","main_no","oil_com","oil_com_text","trade_type","trade_money","fetch_time","trade_time","trade_place","updatetime","createtime","activeProvince"
        ]
    )
});

//返利地区（全国+省/直辖市)
var getActiveProvince = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_card_vice&f=getProvince', method: "POST"}),
    reader: new Ext.data.JsonReader({
        tatalProperty: '',
        root: ''
    }, ['id', 'province']),
    listeners: {
        'beforeload': function () {
            this.baseParams.all = true;
        }
    }
});