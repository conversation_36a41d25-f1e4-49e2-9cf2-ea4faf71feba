/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_credit_payments';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","bill_no","credit_product",'org_name','orgcode',"bill_begin_time","bill_end_time","bills_num","bills_total_fee","payback_time","arrive_fee","receive_num","receive_total_fee","bill_status","remark","createtime","updatetime","last_operator_id","last_operator",'status_txt'
        ]
    ),
});

//获取授信产品
var creditProduct = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_credit_payments&f=getProductList', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        tatalProperty: '',
        root: ''
    }, ['key', 'value'])
});

creditProduct.load();

//获取授信机构
var creditOrgList = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_credit_payments&f=getCreditOrgList', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        tatalProperty: '',
        root: ''
    }, ['key', 'value'])
});

creditOrgList.load();

var detailList = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl('oil_credit_payment_details','getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","payNo","chargeNo","trade_id","trade_extID","id_extID","vice_no","trade_money","trade_num","service_fee","orgcode","org_name","trade_time","status","status_txt","remark"
        ]
    ),
});