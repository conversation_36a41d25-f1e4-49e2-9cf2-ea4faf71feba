var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 50,
	items  : 
	[
	     {
			xtype : 'compositefield',
			items : 
			[
				{
					xtype: 'displayfield',
					value: '账单编号：'
				},
				{
					xtype : 'textfield',
					id    : 'no_title',
					name  : 'bill_no',
					width : 200
				},
                {
                    xtype: 'displayfield',
                    value: '机构：'
                },
                new Ext.form.ComboBox({
                    width: 180,
                    id: 'orgcode',
                    hiddenName: 'orgcode',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    store: creditOrgList,
                    emptyText: '请选择..',
                    valueField: 'key',
                    displayField: 'value',
                    enableKeyEvents: true,
                }),
				{
					xtype: 'displayfield',
					value: '授信产品：'
				},
                new Ext.form.ComboBox({
                    width: 120,
                    id: 'product_id',
                    hiddenName: 'product_code',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    store: creditProduct,
                    emptyText: '请选择..',
                    valueField: 'key',
                    displayField: 'value',
                    enableKeyEvents: true,
                }),
                {
                    xtype: 'displayfield',
                    value: '账单日：'
                },
                {
                    xtype: "datefield",
                    id: "day_start",
                    name: 'day_start_name',
                    format: 'Y-m-d',
                    width: 130
                },
                {
                    xtype: 'displayfield',
                    value: '—'
                },
                {
                    xtype: "datefield",
                    id: "day_end",
                    name: 'day_end_name',
                    format: 'Y-m-d',
                    width: 130
                },
                {
                    xtype: 'displayfield',
                    value: '状态：'
                },
                {
                    xtype : 'combo',
                    hiddenName  : 'bill_status',
                    id: 'bill_status',
                    mode  : 'local',
                    width: 80,
                    emptyText: '请选择..',
                    triggerAction : 'all',
                    forceSelection: true,
                    displayField  : 'value',
                    valueField    : 'key',
                    value  : '',
                    store  : new Ext.data.SimpleStore({
                        fields: ['key','value'],
                        data: [['','全部'],['1','已确认'], ['2','未确认']]
                    }),
                },
				{
					xtype:'button',
					text:'查询',
					style : 'padding-left : 10px;',
					handler: function()
					{
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
					}
				},
				{
					xtype: 'button',
					text: '重置',
					style: 'padding-left : 10px;',
					handler: function () {
						top_panel.getForm().reset();
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
						btnInit();
					}
				},
			]
		 }	
	]
});