/**
 * 全局类
 */
var crow = "";
function oiltest() {
    //列表表格
    this.getGridPanel = function () {
        var _sm =new Ext.grid.RowSelectionModel({
            singleSelect: false,
            listeners: {
                selectionchange: function (data) {
                    if (data.hasSelection()) {
                        crow = data.selections.itemAt(0).data;
                        console.log(crow.bill_status);
                        (Ext.getCmp('btnRemark')) ? Ext.getCmp('btnRemark').enable() : '';//备注
                        if (crow.bill_status == 2) {
                            (Ext.getCmp('btnAudit')) ? Ext.getCmp('btnAudit').enable() : '';//确认回款
                        } else {
                            (Ext.getCmp('btnAudit')) ? Ext.getCmp('btnAudit').disable() : '';//确认回款
                        }
                    } else {
                        (Ext.getCmp('btnAudit')) ? Ext.getCmp('btnAudit').disable() : '';//确认回款
                        (Ext.getCmp('btnRemark')) ? Ext.getCmp('btnRemark').disable() : '';//备注
                    }
                }
            }
        });

        var grid_list = new Ext.grid.GridPanel({
            title	 :'授信回款对账',
            region	 : 'center',
            loadMask : true,
            cm : new Ext.grid.ColumnModel([
                new Ext.grid.RowNumberer({'header': '序号', width: 35}),
                {header : '账单编号',dataIndex : 'bill_no',width : 180},
                {header : '授信产品',dataIndex : 'credit_product',width : 150},
                {header : '机构编码',dataIndex : 'orgcode',width : 90},
                {header : '机构名称',dataIndex : 'org_name',width : 180,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                        return '<a title="'+record.data.org_name+'">'+record.data.org_name+'</a>';
                }},
                {header : '账单开始时间',dataIndex : 'bill_begin_time',width : 110},
                {header : '账单截至时间',dataIndex : 'bill_end_time',width : 110},
                {header : '应收笔数',dataIndex : 'receive_num',width : 100,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                    var ying = record.data.receive_num;
                    var hui = record.data.bills_num;
                    if(ying != hui){
                        return '<span style="color:red;">'+ying+'</span>';
                    }else{
                        return ying;
                    }
                }},
                {header : '应收金额',dataIndex : 'receive_total_fee',width : 160},
                {header : '回款笔数',dataIndex : 'bills_num',width : 100,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                        var ying = record.data.receive_num;
                        var hui = record.data.bills_num;
                        if(ying != hui){
                            return '<span style="color:red;">'+hui+'</span>';
                        }else{
                            return hui;
                        }
                    }},
                {header : '回款金额',dataIndex : 'bills_total_fee',width : 160},
                {header : '状态',dataIndex : 'status_txt',width : 80},
                {header : '操作人id',dataIndex : 'last_operator_id',width :90},
                {header : '操作人名称',dataIndex : 'last_operator',width : 80},
                {header : '备注',dataIndex : 'remark',width : 180,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                        if(record.data.remark) {
                            return '<a title="' + record.data.remark + '">' + record.data.remark + '</a>';
                        }else{
                            return "";
                        }
                }},
            ]),
            listeners: {
                "rowclick" : rowClick
            },
            store : main_store,
            sm :_sm,
            //分页
            bbar: new Ext.PagingToolbar({
                plugins: new Ext.ux.plugin.PagingToolbarResizer,
                pageSize: pagesize,
                displayInfo : true,
                displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
                emptyMsg : '没有符合条件的记录',
                store : main_store
            }),
            tbar : []
        });

        return grid_list;
    };

    //关闭窗口
    this.closeWin = function (id) {
        if (!id) return;
        var win = Ext.getCmp(id);
        win.destroy();
    };

    var _getWindow = function (config) {
        var width = config.width,
            height = config.height,
            title = config.title,
            id = config.id,
            button = config.button;

        button.push({
            text: '取消',
            id: 'button_cancer',
            handler: function () {
                var oilTest = new oiltest();
                oilTest.closeWin(id);
            }
        });
        var windows = new Ext.Window({
            layout: 'border',
            region: 'center',
            width: width,
            modal: true,//遮挡窗口后的内容
            id: id,
            height: height,
            title: title,
            closeAction: 'destroy',
            buttons: button
        });

        return windows;
    };

    //添加
    this.add = function () {
        var windows = _getWindow({
            title: '添加-oil_credit_payments',
            id: 'add_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'add-form',
            items: addPanel.getPanel('add-form', 'add_win')
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };

    //编辑
    this.update = function () {
        var sm   = grid_list.getSelectionModel();
        var data = sm.getSelections();
        data[0] ? id = data[0].get("id") : '';

        var windows = _getWindow({
            title: '编辑-oil_credit_payments',
            id: 'update_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'update-form',
            items: addPanel.getPanel('update-form', 'update_win',id)
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init(data[0]);
    };

    //删除
    this.delete = function () {
        Ext.MessageBox.confirm('删除', '删除之后该条纪录将不再显示，确定要删除？', function showResult(btn){
            if (btn == 'yes')
            {
                var sm   = grid_list.getSelectionModel();
                var data = sm.getSelections();
                var ids = data[0].get("id");
                Ext.Ajax.request({
                    url:getUrl(controlName,'remove'),
                    method:'post',
                    params:{ids:ids},
                    success: function sFn(response,options)
                    {
                        main_store.removeAll();
                        main_store.load();
                        Ext.Msg.alert('系统提示', '删除成功');
                    }
                });
            }
        });
    };
    //导出
    this.export = function () {
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn == 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = '/inside.php?t=json&m='+ controlName +'&f=getList&_export=1'+'&'+params;
                }
            }
        );
    };

    //确认回款
    this.agreeAudit = function () {
        var sm   = grid_list.getSelectionModel();
        var data = sm.getSelections();
        var obj = data[0].data;

        var windows = _getWindow({
            title: '确认回款',
            id: 'pay_result',
            width: 550,
            height:250,
            button: [{
                text: '提交',
                id: 'button_ok',
                handler: function () {
                    var sm   = grid_list.getSelectionModel();
                    var data = sm.getSelections();
                    var ids = data[0].get("id");

                    var remark = Ext.getCmp('remark_id').getValue();

                    var params = {'id':ids,'remark':remark};
                    if(ids) {
                        submitForm(params);
                    }
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'left',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;padding-left:10px;',
            region: 'center',
            id: 'pay-form',
            items: addPanel.getPanel('pay-form', 'pay_result')
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };
}
var oilTest = new oiltest();

function submitForm (params){
    Ext.Ajax.request({
        url: getUrl('oil_credit_payments', 'edit'),
        method: 'post',
        params: params,
        success: function sFn(response, options) {
            var msg = Ext.decode(response.responseText).msg;
            console.log(msg);
            main_store.removeAll();
            main_store.load();
            oilTest.closeWin('pay_result');
            Ext.Msg.alert('提示', msg);
        }
    });
};