/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_masking_source';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","source_name","source_control","is_masking","creator_id","creator_name","createtime","last_operator_id","last_operator","updatetime",        ]
    ),
});