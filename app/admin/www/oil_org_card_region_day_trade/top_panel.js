var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 50,
	items  : 
	[
	     {
			xtype : 'compositefield',
			items : 
			[
				{
					xtype: 'displayfield',
					value: '标题：'
				},
				{
					xtype : 'textfield',
					id    : 'no_title',
					name  : 'title',
					width : 200
				},
				{
					xtype: 'displayfield',
					value: '添加人：'
				},
				{
					xtype : 'textfield',
					id    : 'no_user_name',
					name  : 'user_name',
					width : 100
				},
			
				{
					xtype:'button',
					text:'查询',
					style : 'padding-left : 10px;',
					handler: function()
					{
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
					}
				},
				{
					xtype: 'button',
					text: '重置',
					style: 'padding-left : 10px;',
					handler: function () {
						top_panel.getForm().reset();
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
						btnInit();
					}
				},
			]
		 }	
	]
});