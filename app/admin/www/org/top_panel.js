/*
 * 组织管理搜索栏
 * liuzhi
 * 
 */

//界面
var top_panel = new Ext.Panel ({
	region:'north',
	hideLabels:true,
	bodyStyle:'padding: 10px',
	height:50,
	items:[
		     {
				xtype : 'compositefield',
				items : 
				[
					{
						xtype: 'displayfield',
						value: '组织名称：'
					},
					{
						xtype:'textfield',
						fieldLabel:'组织名称',
						name:'s_org_name',
					    id:"s_org_name",
						width:200,
						enableKeyEvents:true
					},
					{
						xtype: 'displayfield',
						value: '组织ID：'
					},
					{
						xtype:'textfield',
						fieldLabel:'组织ID',
						name:'s_org_code',
					    id:"s_org_code",
						width:200,
						enableKeyEvents:true
					},
					{
						xtype:'button',
						text:'搜索',
						handler: function(){
							var s_org_name = Ext.getCmp("s_org_name").getValue();
							grid_org.getLoader().baseParams.name = s_org_name;
							grid_org.getLoader().load(grid_org.getRootNode(),function(){grid_org.expandAll()});
							(Ext.getCmp('btnDel'))?Ext.getCmp('btnDel').disable():'';//删除
							(Ext.getCmp('btnEdit'))?Ext.getCmp('btnEdit').disable():'';//编辑
						}
					},
				]
		     }
		 ]
});