var smartwin;//扫描窗体
var sid = '';//编辑的组织id号
var org_code = '';//编辑的组织编号
var store_org_edt =new Ext.data.Store({
		proxy:new Ext.data.HttpProxy({url:'../inside.php?t=json&m=org&f=searchAll',method:"POST"}),
		reader:new Ext.data.JsonReader({
			tatalProperty:'',
			root:'data'
		},[
			{name:'org_code'},
			{name:'org_name'},
			{name:'edited_name'}
		]),
		baseParams: {},
		listeners: {
			'load' : function(){
				
			}
		}
	});
  store_org_edt.load(); 
//主容器
var toppanel_add_itemlis = new Ext.FormPanel({
	region:"center",
	bodyStyle:'padding: 10px',
	border:false,
	items:[
	    {xtype: 'compositefield',
	     items: [
	             {xtype:'textfield',id:'name',name:'name',width:180,fieldLabel:'组织名称'},
	             {xtype: 'displayfield',value: ' <font color=red>*</font>'}]
	    },
		{xtype:'textfield',id:'contact',name:'contact',width:180,fieldLabel:'联系人'},
		{xtype:'textfield',id:'address',name:'address',width:180,fieldLabel:'地址'},
		{xtype:'textfield',id:'tel',name:'tel',width:180,fieldLabel:'联系电话'},
		{xtype: 'compositefield',
	     items: [
				{xtype:'combo',id:'_parent',name:'_parent',width:180,fieldLabel:'上级组织',mode:'local',editable:false,triggerAction:'all',displayField:'edited_name',valueField:'org_code',store:store_org_edt,
					listeners: {
						expand: function(combo){
							store_org_edt.load();
						},
						beforeselect: function(combo,record,index){
							record.data.edited_name　=　record.data.org_name;
							Ext.getCmp("parent").setValue(record.data.org_code);
						}
					}
				},//摆设
				{xtype: 'displayfield',value: ' <font color=red>*</font>'}]
		},
		{xtype:'textarea',id:'remark',name:'remark',width:180,fieldLabel:'备注'},
		{xtype:'hidden',id:'parent',name:'parent'}//上级组织
		],
        buttons:[{text: '确定',
            handler : function(){
            	if(checkinput()){
            		var _form = this.ownerCt.ownerCt.getForm();
                	if (sid!=''){
                		_form.submit({url:'../inside.php?t=json&m=org&f=update', 
                                success: sFn,
                                failure: fFn,
                                params: { id:sid,org_code:org_code, updatetime: updatetime//Story #49818
                                },
                        waitMsg:'Saving Data...'});
                	}else{ 			
        				_form.submit({url:'../inside.php?t=json&m=org&f=add', 
                                success: sFn,
                                failure: fFn,
                                params: { id:sid,parent:Ext.getCmp("parent").getValue()},
                        waitMsg:'Saving Data...'});
                	}
                	smartwin.hide();
            	}
            }},{text: '关闭',
                handler : function(){
                	smartwin.hide();
                }
        }] 
});
///////////////////////////////
//弹出窗体
///////////////////////////////
if(!smartwin){//主窗体
	smartwin = new Ext.Window({
		layout:"border",
        width:380,
        height:285,
        title:'组织维护',
        closeAction:'hide',
        plain: true,
        items:[toppanel_add_itemlis]
    });
}

//初始化界面
function showAdd(id,oCode)
{
	if (id){
		sid = id;
		org_code = oCode;
	}else{
		sid = '';
	}	
	if (sid==''){
		Ext.getCmp("name").setValue('');
        Ext.getCmp("contact").setValue('');
        Ext.getCmp("address").setValue('');
        Ext.getCmp("tel").setValue('');
        Ext.getCmp("_parent").setValue('顶级组织');
		Ext.getCmp("parent").setValue(0);
        Ext.getCmp("remark").setValue('');
	}
	smartwin.show();
}

function sFn(d,form)
{
	sid ='';
	grid_org.getLoader().load(grid_org.getRootNode(),function(){grid_org.expandAll()});
	store_org_edt.load();
	(Ext.getCmp('btnDel'))?Ext.getCmp('btnDel').disable():'';//删除
	(Ext.getCmp('btnEdit'))?Ext.getCmp('btnEdit').disable():'';//编辑
	//Story #49818
	var r = Ext.decode(form.response.responseText);
	if (r.success && r.k3_err_msg) {
	    Ext.MessageBox.alert('提示',r.k3_err_msg);
	} else {
	    Ext.MessageBox.alert('提示',r.msg);
	}
}

function fFn(d,form)
{
	sid ='';
	grid_org.getLoader().load(grid_org.getRootNode(),function(){grid_org.expandAll()});
	store_org_edt.load();
	(Ext.getCmp('btnDel'))?Ext.getCmp('btnDel').disable():'';//删除
	(Ext.getCmp('btnEdit'))?Ext.getCmp('btnEdit').disable():'';//编辑
	Ext.MessageBox.alert('提示',Ext.decode(form.response.responseText).msg);
}
/**
 * 检查输入格式
 * @returns
 */
function checkinput(){
	var ss ='';
	(Ext.getCmp("name").getValue()=='')?ss='组织名不能为空':'';
	if(Ext.getCmp("_parent").getValue()==''){
		ss += (ss)?','+'上级组织不能为空':'上级组织不能为空';
	}
	if (ss==''){
		return true;
	}else{
		Ext.MessageBox.alert('提示！',ss);
		return false;
	}
}