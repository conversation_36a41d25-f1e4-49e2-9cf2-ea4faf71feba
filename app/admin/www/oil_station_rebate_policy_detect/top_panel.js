var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 50,
	items  : 
	[
	     {
			xtype : 'compositefield',
			items : 
			[
				{
					xtype: 'displayfield',
					value: 'ID：'
				},
				{
					xtype : 'textfield',
					id    : 'id',
					name  : 'id',
					width : 200
				},
                {
                    xtype: 'displayfield',
                    value: '探测时间：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype: "datefield",
                    width: 100,
                    //value: monthStart,
                    value: '',
                    format: 'Y-m-d',
                    name: 'stat_timeGte',
                },
                {
                    xtype: 'displayfield',
                    value: '-'
                },
                {
                    xtype: "datefield",
                    width: 100,
                    value: '',
                    format: 'Y-m-d',
                    name: 'stat_timeLte',
                },
                {
                    xtype: 'displayfield',
                    value: '探测类型：',
                    style: 'padding-left: 10px'
                },
                {
                    xtype: 'combo',
                    width: 150,
                    id   : 'detect_type',
                    hiddenName : "detect_type",
                    editable: true,
                    emptyText : '请选择',
                    mode: 'local',
                    triggerAction:'all',
                    displayField: 'name',
                    valueField: 'value',
                    store : new Ext.data.SimpleStore({
                        fields: ['name', 'value'],
                        data: [['全量探测（精准+缺失）', '1'], ['缺失探测', '2']]
                    })
                },
				{
					xtype:'button',
					text:'查询',
					style : 'padding-left : 10px;',
					handler: function()
					{
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
					}
				},
				{
					xtype: 'button',
					text: '重置',
					style: 'padding-left : 10px;',
					handler: function () {
						top_panel.getForm().reset();
						main_store.removeAll();//移除原来的数据
						main_store.load();//加载新搜索的数据
						btnInit();
					}
				},
			]
		 }	
	]
});