/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_owing_initial_log';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","supplier_id","collect_company_id","oil_base_id","owing_money","owing_num","supplier_surplus","supplier_rebate","supplier_consume","supplier_recharge","collect_company_recharge","consume_time","recharge_time","ticket_time","createtime","updatetime",        ]
    ),
});