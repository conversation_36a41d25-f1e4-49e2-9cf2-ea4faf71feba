/**
 * Created by wwy on 2015/9/22.
 */

//获取月初日期
/**
 * 获取日期函数
 * @param $flag 1 为月初，2为当前日期
 * @param $mode 1 为全格式，2为年月日
 * @returns {String}
 */
function GetDateStr($flag, $mode) {
    var dd = new Date();
    var y = dd.getFullYear();
    var m = dd.getMonth() + 1;//获取当前月份的日期
    var h = dd.getHours();
    var i = dd.getMinutes();
    var s = dd.getSeconds();
    if (m.toString().length == 1) {
        m = '0' + m;
    }
    var d = '';
    if ($flag == 1)
        d = "01";
    else {
        d = dd.getDate();
        if (d.toString().length == 1) {
            d = '0' + d;
        }
    }

    var returnStr = '';
    if ($mode == 2) {
        returnStr = y + "-" + m + "-" + d;
    } else {
        returnStr = y + "-" + m + "-" + d + " " + h + ":" + i + ":" + s;
    }

    return returnStr;
}

var top_panel = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 180,
    items:
        [
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '机构：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 240,
                            listWidth: 350,
                            id: 'orgCodeLike',
                            hiddenName: 'orgCodeLike',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'remote',
                            queryParam: 'keyword',
                            minChars: 2,
                            displayField: 'org_name',//显示的值
                            valueField: 'orgcode',//后台接收的key
                            store: getOilOrgNew,
                            emptyText: '请选择..',
                            enableKeyEvents: true
                        }),
                        {//包含顶级
                            xtype: "hidden",
                            name: 'org_flag',
                            value: true
                        },
                        {
                            xtype: 'displayfield',
                            value: '测试机构：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 80,
                            hiddenName: "is_test",
                            editable: true,
                            emptyText: '全部',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'name',
                            valueField: 'value',
                            store: new Ext.data.SimpleStore({
                                fields: ['name', 'value'],
                                data: [['全部', ''], ['否', '1'], ['是', '2']]
                            })
                        },
                        {
                            xtype: 'displayfield',
                            value: '每日开票：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 80,
                            id: 'is_recepit_nowtime',
                            hiddenName: "is_recepit_nowtime",
                            editable: true,
                            emptyText: '全部',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'name',
                            valueField: 'value',
                            store: new Ext.data.SimpleStore({
                                fields: ['name', 'value'],
                                data: [['否', '1'], ['是', '2']]
                            })
                        },
                        // {
                        //     xtype: 'displayfield',
                        //     value: '手续费单日上限：',
                        //     width: 100,
                        // },
                        // new Ext.form.ComboBox({
                        //     width: 100,
                        //     listWidth: 200,
                        //     hiddenName: "single_day_ceil",
                        //     value: '',
                        //     triggerAction: 'all',
                        //     forceSelection: true,
                        //     emptyText: '请选择..',
                        //     displayField: 'value',
                        //     valueField: 'key',
                        //     store: getSingleDayCeil
                        // }),
                    ]
            },
            {
                xtype: 'compositefield',
                items:
                    [
                      {
                        xtype: 'displayfield',
                        value: '客户名称：',
                        width: 60,
                      },
                      {
                        xtype: 'textfield',
                        id: 'sub_org_name',
                        name: 'sub_org_name',
                        width: 89
                      },
                      {
                        xtype: 'displayfield',
                        value: '平台客户：',
                        width: 60,
                      },
                      {
                        xtype: 'combo',
                        width: 80,
                        hiddenName: "is_system_orgcode",
                        editable: true,
                        emptyText: '全部',
                        mode: 'local',
                        triggerAction: 'all',
                        displayField: 'name',
                        valueField: 'value',
                        store: new Ext.data.SimpleStore({
                          fields: ['name', 'value'],
                          data: [['全部', ''], ['否', '1'], ['是', '2']]
                        })
                      },
                        // 20190628 lpj add
                        {
                            xtype: 'displayfield',
                            value: '付款公司：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 240,
                            listWidth: 350,
                            hiddenName: 'pay_company_id',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'remote',
                            queryParam: 'company_nameLike',
                            minChars: 2,
                            displayField: 'pay_name',//显示的值
                            valueField: 'id',//后台接收的key
                            store: getPayName,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                        }),
                    ]
            },
            {
              xtype: 'compositefield',
              items:[
                {
                  xtype: 'displayfield',
                  value: '首次开票：',
                  width: 60,
                },
                {
                  xtype: 'combo',
                  width: 80,
                  hiddenName: "is_first_receipt_apply",
                  editable: true,
                  emptyText: '全部',
                  mode: 'local',
                  triggerAction: 'all',
                  displayField: 'name',
                  valueField: 'value',
                  store: new Ext.data.SimpleStore({
                    fields: ['name', 'value'],
                    data: [['全部', ''], ['否', '1'], ['是', '2']]
                  })
                },
                {
                  xtype: 'displayfield',
                  value: '运营商：',
                  width: 60,
                },
                {
                  xtype: 'combo',
                  width: 80,
                  listWidth: 200,
                  id: 'operators_id',
                  hiddenName: "operators_id",
                  editable: true,
                  emptyText: '全部',
                  triggerAction: 'all',
                  displayField: 'name',
                  valueField: 'id',
                  store: new Ext.data.Store({
                    url: '../inside.php?t=json&m=oil_operators&f=getlist&c_type=1',
                    reader: new Ext.data.JsonReader({
                      totalProperty: 'data.total',
                      root: 'data.data'
                    }, ['id', 'name'])
                  })
                },
                {
                  xtype: 'displayfield',
                  value: '创建时间：',
                  width: 60,
                },
                {
                  xtype: "datetimefield",
                  id: "s_start_time",
                  name: 'createtimeGe',
                  format: 'Y-m-d H:i:s',
                  width: 140
                },
                {
                  xtype: 'displayfield',
                  value: '~'
                },
                {
                  xtype: "datetimefield",
                  id: "s_end_time",
                  name: 'createtimeLe',
                  format: 'Y-m-d H:i:s',
                  width: 140
                },
              ]
            },
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '代理商：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 240,
                            id: 'agent_org',
                            hiddenName: 'agent_org',
                            value: '',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'local',
                            displayField: 'org_name',//显示的值
                            valueField: 'orgcode',//后台接收的key
                            store: getTopOilOrg,
                            emptyText: '请选择..',
                            enableKeyEvents: true,
                            listeners: {
                                'focus': function () {
                                    getTopOilOrg.load();
                                },
                                'beforequery': function (e) {
                                    var combo = e.combo;
                                    if (!e.forceAll) {
                                        var input = e.query;
                                        // 检索的正则
                                        var regExp = new RegExp(".*" + input + ".*");
                                        // 执行检索
                                        combo.store.filterBy(function (record, id) {
                                            // 得到每个record的项目名称值
                                            var text = record.get(combo.displayField);
                                            return regExp.test(text);
                                        });
                                        combo.expand();
                                        return false;
                                    }
                                }
                            },
                        }),

                        {
                            xtype: 'displayfield',
                            value: '专属客服：',
                            width: 60,
                        },
                        new Ext.form.ComboBox({
                            width: 80,
                            hiddenName: "exclusive_custom",
                            value: '',
                            triggerAction: 'all',
                            forceSelection: true,
                            emptyText: '请选择..',
                            displayField: 'value',
                            valueField: 'key',
                            store: getExclusiveCustom
                        }),

                        {
                            xtype: 'displayfield',
                            value: '核算方式：',
                            width: 60,
                        },
                        {
                            xtype: 'combo',
                            width: 80,
                            hiddenName: "receipt_mode",
                            editable: true,
                            emptyText: '全部',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'name',
                            valueField: 'value',
                            store: new Ext.data.SimpleStore({
                                fields: ['name', 'value'],
                                data: [['全部', ''], ['集中核算', '1'], ['独立核算', '2']]
                            })
                        },
                    ]
            },
            {
                xtype: 'compositefield',
                items:
                    [
                      {
                        xtype: 'displayfield',
                        value: '修改时间：',
                        width: 60,
                      },
                      {
                        xtype: "datetimefield",
                        id: "s_start_utime",
                        name: 'updatetimeGe',
                        format: 'Y-m-d H:i:s',
                        width: 140
                      },
                      {
                        xtype: 'displayfield',
                        value: '~'
                      },
                      {
                        xtype: "datetimefield",
                        id: "s_end_utime",
                        name: 'updatetimeLe',
                        format: 'Y-m-d H:i:s',
                        width: 140
                      },
                        {
                            xtype: 'displayfield',
                            value: '数据完整度：',
                            width: 80,
                        },
                        {
                            xtype: 'combo',
                            width: 100,
                            hiddenName: "sign_data_integrity",
                            editable: true,
                            emptyText: '全部',
                            mode: 'local',
                            triggerAction: 'all',
                            displayField: 'name',
                            valueField: 'value',
                            store: new Ext.data.SimpleStore({
                                fields: ['name', 'value'],
                                data: [['全部', ''], ['完整唯一', 10], ['完整不唯一', 20], ['不完整唯一', 30], ['不完整不唯一', 40],['全部缺失', 60]]
                            })
                        },
                    ]
            },
          {
            xtype: 'compositefield',
            items:[
              {
                xtype: 'displayfield',
                value: '精细核算方式：',
                width: 100,
              },
              {
                xtype: 'combo',
                width: 90,
                hiddenName: "sign_receipt_mode",
                editable: true,
                emptyText: '全部',
                mode: 'local',
                triggerAction: 'all',
                displayField: 'name',
                valueField: 'value',
                store: new Ext.data.SimpleStore({
                  fields: ['name', 'value'],
                  data: [['全部', ''], ['集中核算', 10], ['标准独立核算', 20], ['非标独立核算', 30], ['异常', 40]]
                })
              },
              {
                xtype: 'displayfield',
                value: '是否可换签：',
                width: 80,
              },
              {
                xtype: 'combo',
                width: 80,
                hiddenName: "can_change_sign",
                editable: true,
                emptyText: '全部',
                mode: 'local',
                triggerAction: 'all',
                displayField: 'name',
                valueField: 'value',
                store: new Ext.data.SimpleStore({
                  fields: ['name', 'value'],
                  data: [['全部', ''], ['可以', 10], ['不可以', 20]]
                })
              },
              {
                xtype: 'button',
                text: '查询',
                handler: function () {
                  crow = null;
                  enterSearch();
                  receiptTitleStore.removeAll();
                  orgPayInfoStore.removeAll();
                }
              },
              {
                xtype: 'button',
                text: '重置',
                style: 'padding-left : 10px;',
                handler: function () {
                  crow = null;
                  top_panel.getForm().reset();
                  enterSearch();
                  receiptTitleStore.removeAll();
                  orgPayInfoStore.removeAll();
                }
              }
            ]
          }
        ]
});

function enterSearch() {
    //刷新页面，树状数据全部展开
    treeLoader.load(grid_list.getRootNode(), function () {
        //grid_list.expandAll();
    });
}
