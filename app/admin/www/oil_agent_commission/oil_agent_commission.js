/**
 * 全局类
 */
var crow;
function oiltest() {
    //列表表格
    this.getGridPanel = function () {
        var _sm =new Ext.grid.RowSelectionModel({
            singleSelect: true,
            listeners: {
                selectionchange: function(data) {
                    if (data.getCount()){
                        (Ext.getCmp('btnagree')) ? Ext.getCmp('btnagree').enable() : '';//通过按钮
                        (Ext.getCmp('btnunaudit')) ? Ext.getCmp('btnunaudit').enable() : '';//销审按钮
                        var one = grid_list.getSelectionModel().getSelections();
                        crow = one;
                        var status = one[0].data.audit_status;
                        if( status == 3){
                            (Ext.getCmp('btnagree')) ? Ext.getCmp('btnagree').disable() : '';//通过按钮
                        }else if(status == 2){
                            (Ext.getCmp('btnunaudit')) ? Ext.getCmp('btnunaudit').disable() : '';//销审按钮
                        }else{
                            (Ext.getCmp('btnagree')) ? Ext.getCmp('btnagree').disable() : '';//通过按钮
                            (Ext.getCmp('btnunaudit')) ? Ext.getCmp('btnunaudit').disable() : '';//销审按钮
                        }
                    }else{
                        (Ext.getCmp('btnagree')) ? Ext.getCmp('btnagree').disable() : '';//通过按钮
                        (Ext.getCmp('btnunaudit')) ? Ext.getCmp('btnunaudit').disable() : '';//销审按钮
                    }
                }
            }
        });

        var grid_list = new Ext.grid.GridPanel({
            title	 :'返佣记录',
            region	 : 'center',
            loadMask : true,
            cm : new Ext.grid.ColumnModel([
                {header : '代理商',dataIndex : 'agent_org_txt',width : 250},
                {header : '客户机构',dataIndex : 'customer_orgcode_txt',width : 250},
                {header : '月份',dataIndex : 'month',width : 100},
                {header : '当月充值(元)',dataIndex : 'charge_fee',width : 180},
                {header : '当月消费(元)',dataIndex : 'consume_fee',width : 180},
                // {header : '代理返利(元)',dataIndex : 'fanli_fee',width : 100},
                {header : '代理佣金(元)',dataIndex : 'commission_fee',width : 100},
                {header : '审核状态',dataIndex : 'audit_status_txt',width : 100,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                    var status = record.data.audit_status;
                    if(status == 3){
                        return '<a href="javascript:void(0)" style="color:green">'+record.data.audit_status_txt+'</a>';
                    }else if(status == 2){
                        return '<a href="javascript:void(0)" style="color:red">'+record.data.audit_status_txt+'</a>';
                    }else{
                        return '<a href="javascript:void(0)" style="color:orangered">'+record.data.audit_status_txt+'</a>';
                    }
                }},
                {header : '日志',dataIndex : 'log_num',width : 100},
            ]),
            store : main_store,
            sm :_sm,
            //分页
            bbar: new Ext.PagingToolbar({
                plugins: new Ext.ux.plugin.PagingToolbarResizer,
                pageSize: pagesize,
                displayInfo : true,
                displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
                emptyMsg : '没有符合条件的记录',
                store : main_store
            }),
            tbar : [],
            listeners: {
                'rowclick': rowClick
            }
        });

        return grid_list;
    };

    //关闭窗口
    this.closeWin = function (id) {
        if (!id) return;
        var win = Ext.getCmp(id);
        win.destroy();
    };

    var _getWindow = function (config) {
        var width = config.width,
            height = config.height,
            title = config.title,
            id = config.id,
            button = config.button;

        button.push({
            text: '取消',
            id: 'button_cancer',
            handler: function () {
                var oilTest = new oiltest();
                oilTest.closeWin(id);
            }
        });
        var windows = new Ext.Window({
            layout: 'border',
            region: 'center',
            width: width,
            modal: true,//遮挡窗口后的内容
            id: id,
            height: height,
            title: title,
            closeAction: 'destroy',
            buttons: button
        });

        return windows;
    };

    //添加
    this.add = function () {
        var windows = _getWindow({
            title: '添加-代理商佣金记录',
            id: 'add_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'add-form',
            items: addPanel.getPanel('add-form', 'add_win')
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };

    //编辑
    this.update = function () {
        var sm   = grid_list.getSelectionModel();
        var data = sm.getSelections();
        data[0] ? id = data[0].get("id") : '';

        var windows = _getWindow({
            title: '编辑-代理商佣金记录',
            id: 'update_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'update-form',
            items: addPanel.getPanel('update-form', 'update_win',id)
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init(data[0]);
    };

    //删除
    this.delete = function () {
        Ext.MessageBox.confirm('删除', '删除之后该条纪录将不再显示，确定要删除？', function showResult(btn){
            if (btn == 'yes')
            {
                var sm   = grid_list.getSelectionModel();
                var data = sm.getSelections();
                var ids = data[0].get("id");
                Ext.Ajax.request({
                    url:getUrl(controlName,'remove'),
                    method:'post',
                    params:{ids:ids},
                    success: function sFn(response,options)
                    {
                        main_store.removeAll();
                        main_store.load();
                        Ext.Msg.alert('系统提示', '删除成功');
                    }
                });
            }
        });
    };
    //导出
    this.export = function () {
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn == 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = '/inside.php?t=json&m='+ controlName +'&f=getList&_export=1'+'&'+params;
                }
            }
        );
    };
    //销审
    this.unaudit = function () {
        Ext.MessageBox.confirm('销审', "确定要销审吗？", function showResult(btn) {
                if (btn == 'yes') {
                    operator("unaudit","unAuditData");
                }
            }
        );
    }
    //通过
    this.agree = function () {
        Ext.MessageBox.confirm('通过', "确定要通过吗？", function showResult(btn) {
                if (btn == 'yes') {
                    operator("agree","auditData");
                }
            }
        );
    }
    this.downTpl = function () {
        Ext.MessageBox.confirm('下载', "确定要下载佣金模板吗？", function showResult(btn) {
                if (btn == 'yes') {
                    window.location.href = '/download/templates/agentCommissionTpl.xlsx';
                }
            }
        );
    }
    //导入佣金
    this.importData = function () {
        var form = new Ext.form.FormPanel({
            baseCls: 'x-plain', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
            items: [
                {
                    xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
                    id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
                    anchor: '100%'
                }
            ]
        });
        var fanliBoot = new Ext.Window({
            title: '批量导入',
            width: 400,
            height: 105,
            minWidth: 300,
            minHeight: 100,
            closeAction: 'destroy',
            modal: true,
            layout: 'fit',
            plain: true,
            bodyStyle: 'padding:5px;',
            buttonAlign: 'center',
            items: form,
            buttons: [{
                text: '导入',
                handler: function () {
                    if (form.form.isValid()) {
                        if (Ext.getCmp('userfile').getValue() === '') {
                            Ext.Msg.alert('系统提示', '请选择你要上传的文件');
                            return;
                        }
                        form.getForm().submit({
                            url: getUrl(controlName,'importCommission'),
                            success: function (form, action) {
                                var r = Ext.decode(action.response.responseText).msg;
                                Ext.MessageBox.alert('提示', r);
                                fanliBoot.destroy();
                                main_store.removeAll();
                                main_store.load();
                            },
                            failure: function (form, action) {
                                var r = Ext.decode(action.response.responseText).msg;
                                Ext.MessageBox.alert('提示', r);
                            }
                        })
                    }
                }
            }, {
                text: '关闭',
                handler: function () {
                    fanliBoot.destroy();
                }
            }]
        });
        fanliBoot.show();
    }
}
var oilTest = new oiltest();


function operator(type,callFunction)
{
    var sm   = grid_list.getSelectionModel();
    var data = sm.getSelections();
    var ids = data[0].get("id");
    Ext.Ajax.request({
        url:getUrl(controlName,callFunction),
        method:'post',
        params:{id:ids},
        success: function sFn(response,options)
        {
            main_store.removeAll();
            main_store.load();
            var obj = Ext.decode(response.responseText);
            Ext.Msg.alert('系统提示', obj.msg);
        }
    });
}