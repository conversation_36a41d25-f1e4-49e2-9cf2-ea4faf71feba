/**
 * 全局类
 */
function oiltest() {
    //列表表格
    this.getGridPanel = function () {
        var _sm =new Ext.grid.RowSelectionModel({
            singleSelect: true,
            listeners: {
                selectionchange: function(data) {
                    if (data.getCount()){
                        (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').enable() : '';//删除按钮
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').enable() : '';//修改按钮
                    }else{
                        (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').disable() : '';//删除按钮
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').disable() : '';//修改按钮
                    }
                }
            }
        });

        var grid_list = new Ext.grid.GridPanel({
            title	 :'机构付款公司变更记录表',
            region	 : 'center',
            loadMask : true,
            cm : new Ext.grid.ColumnModel([
            	                {header : '',dataIndex : 'id',width : 180},
            	                {header : '付款公司ID',dataIndex : 'pay_company_id',width : 180},
            	                {header : '',dataIndex : 'orgroot',width : 180},
            	                {header : '合同档案id',dataIndex : 'contract_id',width : 180},
            	                {header : '开票是否校验合同，10 校验，20不校验',dataIndex : 'is_check_contract',width : 180},
            	                {header : '付款公司名称',dataIndex : 'company_name',width : 180},
            	                {header : '纳税人识别号',dataIndex : 'taxpayer_no',width : 180},
            	                {header : '状态 1正常，2停用',dataIndex : 'status',width : 180},
            	                {header : '累计充值金额',dataIndex : 'charge_total',width : 180},
            	                {header : '累计充值统计截止日期',dataIndex : 'charge_total_date',width : 180},
            	                {header : '发票手动调整额度',dataIndex : 'dynamic_money',width : 180},
            	                {header : '备注',dataIndex : 'remark',width : 180},
            	                {header : '创建人id',dataIndex : 'creator_id',width : 180},
            	                {header : '创建人姓名',dataIndex : 'creator_name',width : 180},
            	                {header : '接口添加人id',dataIndex : 'other_creator_id',width : 180},
            	                {header : '接口添加人名称',dataIndex : 'other_creator',width : 180},
            	                {header : '最后修改人id',dataIndex : 'last_operator_id',width : 180},
            	                {header : '最后修改人姓名',dataIndex : 'last_operator',width : 180},
            	                {header : '合同绑定时间',dataIndex : 'contract_bind_time',width : 180},
            	                {header : '创建时间',dataIndex : 'createtime',width : 180},
            	                {header : '更新时间',dataIndex : 'updatetime',width : 180},
            	                {header : '1:未传 2：已传 是否给crm穿过数据创建',dataIndex : 'is_to_crm',width : 180},
            	                {header : '对接crm，关联id',dataIndex : 'new_accountid',width : 180},
            	            ]),
            store : main_store,
            sm :_sm,
            //分页
            bbar: new Ext.PagingToolbar({
                plugins: new Ext.ux.plugin.PagingToolbarResizer,
                pageSize: pagesize,
                displayInfo : true,
                displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
                emptyMsg : '没有符合条件的记录',
                store : main_store
            }),
            tbar : []
        });

        return grid_list;
    };

    //关闭窗口
    this.closeWin = function (id) {
        if (!id) return;
        var win = Ext.getCmp(id);
        win.destroy();
    };

    var _getWindow = function (config) {
        var width = config.width,
            height = config.height,
            title = config.title,
            id = config.id,
            button = config.button;

        button.push({
            text: '取消',
            id: 'button_cancer',
            handler: function () {
                var oilTest = new oiltest();
                oilTest.closeWin(id);
            }
        });
        var windows = new Ext.Window({
            layout: 'border',
            region: 'center',
            width: width,
            modal: true,//遮挡窗口后的内容
            id: id,
            height: height,
            title: title,
            closeAction: 'destroy',
            buttons: button
        });

        return windows;
    };

    //添加
    this.add = function () {
        var windows = _getWindow({
            title: '添加-机构付款公司变更记录表',
            id: 'add_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'add-form',
            items: addPanel.getPanel('add-form', 'add_win')
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };

    //编辑
    this.update = function () {
        var sm   = grid_list.getSelectionModel();
        var data = sm.getSelections();
        data[0] ? id = data[0].get("id") : '';

        var windows = _getWindow({
            title: '编辑-机构付款公司变更记录表',
            id: 'update_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'update-form',
            items: addPanel.getPanel('update-form', 'update_win',id)
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init(data[0]);
    };

    //删除
    this.delete = function () {
        Ext.MessageBox.confirm('删除', '删除之后该条纪录将不再显示，确定要删除？', function showResult(btn){
            if (btn == 'yes')
            {
                var sm   = grid_list.getSelectionModel();
                var data = sm.getSelections();
                var ids = data[0].get("id");
                Ext.Ajax.request({
                    url:getUrl(controlName,'remove'),
                    method:'post',
                    params:{ids:ids},
                    success: function sFn(response,options)
                    {
                        main_store.removeAll();
                        main_store.load();
                        Ext.Msg.alert('系统提示', '删除成功');
                    }
                });
            }
        });
    };
    //导出
    this.export = function () {
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn == 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = '/inside.php?t=json&m='+ controlName +'&f=getList&_export=1'+'&'+params;
                }
            }
        );
    };
}
var oilTest = new oiltest();