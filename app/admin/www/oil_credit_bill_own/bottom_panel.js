let bottomTitle = '';

const repayment_detail_grid = new Ext.grid.GridPanel({
    region: "center",
    loadMask: true,
    store: repayment_detail_store,
    cm: new Ext.grid.ColumnModel([
        {header: '还款单号', dataIndex: 'no', sortable: true, width: 240},
        {header: '还款金额', dataIndex: 'repay_money', sortable: true, width: 110},
        {header: '还款状态', dataIndex: '_status', sortable: true, width: 100},
        {header: '还款时间', dataIndex: 'createtime', width: 120},
    ]),
    bbar: new Ext.PagingToolbar({
        pageSize: 100,
        store: repayment_detail_store,
        displayInfo: true,
        displayMsg: '显示第{0}条到{1}条记录,一共{2}条',
        emptyMsg: '没有记录'
    })
});

const repayment_money_detail_grid = new Ext.grid.GridPanel({
    region: "center",
    loadMask: true,
    store: repayment_money_detail_store,
    cm: new Ext.grid.ColumnModel([
        {header: '资金来源单号', dataIndex: 'app_no', sortable: true, width: 240},
        {header: '资金来源', dataIndex: '_app_type', sortable: true, width: 240},
        {header: '金额', dataIndex: 'money', width: 110},
        {header: '资金使用状态', dataIndex: '_status', width: 80},
        {header: '创建时间', dataIndex: 'createtime', width: 120},
        {header: '使用时间', dataIndex: 'updatetime', width: 120},
    ]),
    bbar: new Ext.PagingToolbar({
        pageSize: 100,
        store: repayment_money_detail_store,
        displayInfo: true,
        displayMsg: '显示第{0}条到{1}条记录,一共{2}条',
        emptyMsg: '没有记录'
    }),
    tbar: new Ext.Toolbar({
        items: [
        ]
    })
});

//加载底部数据
function loadBottomData(flag) {
    if (crow.id === undefined) {
        if(flag === '还款明细'){
            repayment_detail_store.removeAll();
        }else if(flag === '还款资金来源'){
            repayment_money_detail_store.removeAll();
        }
        return;
    }
    if(flag === '还款明细'){
        repayment_detail_store.removeAll();
        repayment_detail_store.baseParams = {'billID':crow.id,'start':0,'limit':100};
        repayment_detail_store.load();
    }else if(flag === '还款资金来源'){
        repayment_money_detail_store.removeAll();
        repayment_money_detail_store.baseParams = {'bill_id':crow.id,'start':0,'limit':100};
        repayment_money_detail_store.load();
    }
}

//底部表格
const bottom_panel = new Ext.TabPanel({
    region: "south",
    activeTab: 0,
    frame: true,
    height: 210,
    items:
        [
            {
                xtype: 'panel',
                id: 'a_panel',
                title: '还款明细',
                layout: 'border',
                hidden: true,
                items: [
                    repayment_detail_grid,
                ]
            },
            {
                xtype: 'panel',
                title: '还款资金来源',
                layout: 'border',
                items: [
                    repayment_money_detail_grid,
                ]
            },
        ],
    listeners: {
        tabchange: function (tp, p) {
            bottomTitle = p.title;
            if (crow) {
                loadBottomData(p.title);
            }

        }
    }
});

//点击列表，获取底部数据
function rowClick() {
    if(bottomTitle){
        loadBottomData(bottomTitle);
    }
}
