/**
* 报警配置列表
* lixiaoli
* 2014/08/08
*/
monitorStore.load();
var _sm =new Ext.grid.RowSelectionModel({
	singleSelect: true,
	listeners: {
		selectionchange: function(data) {
			if (data.getCount())  {
				(Ext.getCmp('monitorUpdate')) ? Ext.getCmp('monitorUpdate').enable() : '';//修改按钮
                (Ext.getCmp('monitorDelete')) ? Ext.getCmp('monitorDelete').enable() : '';//删除按钮
			} else  {
				(Ext.getCmp('monitorUpdate')) ? Ext.getCmp('monitorUpdate').disable() : '';//修改按钮
                (Ext.getCmp('monitorDelete')) ? Ext.getCmp('monitorDelete').disable() : '';//删除按钮
			}
	    }
    }
});

//配置列表 开始
var grid_list = new Ext.grid.GridPanel({
	title	 :'报警配置',
	region	 : 'center',
	loadMask : true,
	cm : new Ext.grid.ColumnModel([
		{header : '组织类型',dataIndex : 'group_type',width : 80,renderer : function(value, cellMeta, record, rowIndex, columnIndex, store){
            if(record.data['group_type'] == 1){
                return '客户';
            }else if(record.data['group_type'] == 2){
                return '内部组织';
            }else if(record.data['group_type'] == 3){
                    return '个人';
            }
        }},
        {header : '监控类型',dataIndex : 'monitor_type',width : 80,renderer : function(value, cellMeta, record, rowIndex, columnIndex, store){
        	if (record.data['monitor_type'] == '1') {//Story #25392
        		return '温控';
        	} else if (record.data['monitor_type'] == '2') {
        		return '离线';
        	} else if (record.data['monitor_type'] == '3') {
        		return '年审提醒';
        	}
        }},
		{header : '名称',dataIndex : 'customer_name',width : 220},
		{header : '标题',dataIndex : 'title',width : 180},
        {header : '内容',dataIndex : 'contents',width : 300},
        {header : '手机号',dataIndex : 'mobile',width : 160},
        {header : '邮箱',dataIndex : 'email',width : 160},
        {header : '创建人',dataIndex : 'creator_name',width : 120},
        {header : '创建时间',dataIndex : 'createtime',width : 160},
        {header : '修改时间',dataIndex : 'updatetime',width : 160}
	]),
	store : monitorStore,
	sm :_sm, //固定表格的宽度 注释掉，显示宽度滚动条
	viewConfig:{
		forceFit:true
	},
	
	//分页
	bbar: new Ext.PagingToolbar({
		plugins: new Ext.ux.plugin.PagingToolbarResizer,
		pageSize: pagesize,
		displayInfo : true,
		displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',   
		emptyMsg : '没有符合条件的记录',   
		store : monitorStore
	}),
    listeners:{
        "rowclick" : rowClick
    },
	tbar : []	
});

//列表表格
var main_tab_panel = new Ext.TabPanel({
	xtype   : "tabpanel",
	region  : "center",
	id		: 'sale_firm_tab',
	items:
	[
		grid_list
	]
});
main_tab_panel.setActiveTab(grid_list);//设置特定的tab为活动面板

//删除
function monitorDelete()
{
    var sm   = grid_list.getSelectionModel();
    var data = sm.getSelections();
    var id  = '';
    id 	 = data[0].get("id");

    Ext.MessageBox.confirm('删除', '确定删除吗？删除后不可恢复', function showResult(btn){
        if (btn == 'yes')
        {
            Ext.Ajax.request({
                url:'../inside.php?t=json&m='+ controlName +'&f=monitorDelete',
                method:'post',
                params:{id:id},
                success: function sFn(response,options)
                {
                    monitorStore.load();//刷新页面
                    Ext.Msg.alert('系统提示', Ext.decode(response.responseText).msg);
                }
            });
        }
    });
}