/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_supplier_contract_extra';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","supplier_contract_id","supply_no","supply_title","supply_type","open_type","line","status","creator_id","creator_name","last_operator_id","last_operator","createtime","updatetime",        ]
    ),
});