//顶级机构列表
var getTopOilOrg = new Ext.data.Store({
	proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_org&f=getTopOilOrg', method: "POST"}),
	reader: new Ext.data.JsonReader({
		totalProperty: '',
		root: 'data.data'
	}, ['id', 'orgcode', 'org_name'])
});

var form_operator = {};
var form_params = {};
var form_debug_msg = '';
/**
 * 注意：此功能只适用于：PHP程序中【查询】和【导出】时【数据库字段名】相互一致
 * 1.提交表单时【表单控件的名称】取得优先顺序form_name > name > id属性
 * 2.表单控件的form_column属性用来指定数据库字段名
 * 3.表单控件的form_column_operator属性用来指定数据库字段关系(=、>、<、like、in...)
 * 4.表单控件的form_ignore属性用来禁用表单控件的自动完成
 * 5.表单控件的form_call_fn属性，使用一个方法修改其他控件或自身控件的属性，此方法会传入form_params
 * 温馨提示：如果WHERE条件不正确，多半是【form_column】或【form_column_operator】没搞对
 * @param {Object} op
 * op.export 开启导出 非必填
 * op.get_num 计算数量 非必填
 * op.form 设置formPanel对象 必填
 * op.grid_list 设置表格对象(取得分页大小时使用) 非必填
 */
function getFormData(op){
	form_operator = {};
	form_params = {};
	form_debug_msg = '';
	//是否导出
	if (!op.export) {
		op.export = false;
		form_debug_msg += (form_debug_msg ? '\n' : '') + '没有开启导出功能';
	}
	//是否计算数量
	if (!op.get_num) {
		op.get_num = false;
		form_debug_msg += (form_debug_msg ? '\n' : '') + '没有开启计算个数功能';
	}
	
	if (op.form) {
		Ext.each(op.form.items.items, function(v, i){
			if (v.xtype == 'compositefield') {
				Ext.each(v.items.items, function(vv, ii){
					getItemOperator(vv);
				});
			} else {
				getItemOperator(v);
			}
		});
		
		form_params.form_operator = JSON.stringify(form_operator);
		if (form_params.form_operator == '{}') {
			form_debug_msg += (form_debug_msg ? '\n' : '') + '没有设置控件的form_column属性';
		}
		var form_params_str = '';
		if (op.export) {
			for (var key in form_params) {
				form_params_str += (form_params_str ? '&' : '') + key + '=' + form_params[key];
			}
			if (op.get_num) {
				form_params_str += (form_params_str ? '&' : '') + 'get_num=' + true;
			}
			//打印运行日志
			op.debug ? console.log(form_debug_msg) : '';
			return (form_params_str ? '&' + form_params_str : '');
		} else {
			if (op.get_num) {
				form_params.get_num = op.get_num;
			} else {
				form_params.start = 0;//这里不会影响分页
				if (op.grid_list && op.grid_list.getBottomToolbar()) {
					form_params.limit = op.grid_list.getBottomToolbar().pageSize;
				} else {
					form_params.limit = 20;
				}
			}
			
			//打印运行日志
			op.debug ? console.log(form_debug_msg) : '';
			return form_params;
		}
		
	} else {
		form_debug_msg += (form_debug_msg ? '\n' : '') + '没有设置控件所在的Panel(form参数)';
		//打印运行日志
		op.debug ? console.log(form_debug_msg) : '';
		return false;
	}
}

function getItemOperator(item){
	!item.form_call_fn ? item.form_call_fn = function(){} : '';
	item.form_call_fn(item, form_params);
	var operator = {};
	var item_name = item.form_name ? item.form_name : (item.name ? item.name : item.id);
	if (item.xtype == 'displayfield') {
		return false;
	} else {
		if (item.submitValue) {
			form_params[item_name] = item.getValue();
		}
		if (item.form_ignore) {
			return false;
		} else {
			if (!item.submitValue) {
				form_params[item_name] = item.getValue();
			}
			if (item.form_column) {
				operator = {
					column: item.form_column,
					column_operator: item.form_column_operator ? item.form_column_operator : ''
				};
				if (item.xtype == 'datefield' || item.xtype == 'datetimefield') {
					operator.column_date_type = item.xtype;
					if (form_params[item_name] !== '' && Ext.isDate(form_params[item_name])) {
						form_params[item_name] = form_params[item_name].dateFormat('Y-m-d H:i:s');
					}
				}
			} else {
				return false;
			}
			
			form_operator[item_name] = operator;
		}
	}
}
