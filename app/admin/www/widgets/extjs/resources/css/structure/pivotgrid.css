/*!
 * Ext JS Library 3.3.1
 * Copyright(c) 2006-2010 Sencha Inc.
 * <EMAIL>
 * http://www.sencha.com/license
 */
.x-pivotgrid .x-grid3-header-offset table {
    width: 100%;
    border-collapse: collapse;
}

.x-pivotgrid .x-grid3-header-offset table td {
    padding: 4px 3px 4px 5px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 11px;
    line-height: 13px;
    font-family: tahoma;
}

.x-pivotgrid .x-grid3-row-headers {
    display: block;
    float: left;
}

.x-pivotgrid .x-grid3-row-headers table {
    height: 100%;
    width: 100%;
    border-collapse: collapse;
}

.x-pivotgrid .x-grid3-row-headers table td {
    height: 18px;
    padding: 2px 7px 0 0;
    text-align: right;
    text-overflow: ellipsis;
    font-size: 11px;
    font-family: tahoma;
}

.ext-gecko .x-pivotgrid .x-grid3-row-headers table td {
    height: 21px;
}

.x-grid3-header-title {
    top: 0%;
    left: 0%;
    position: absolute;
    text-align: center;
    vertical-align: middle;
    font-family: tahoma;
    font-size: 11px;
    padding: auto 1px;
    display: table-cell;
}

.x-grid3-header-title span {
    position: absolute;
    top: 50%;
    left: 0%;
    width: 100%;
    margin-top: -6px;
}