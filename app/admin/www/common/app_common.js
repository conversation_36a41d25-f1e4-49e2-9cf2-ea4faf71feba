/*
保留默认的一些变量函数和库内容
*/

///////////////////////////
//
//		公共变量 所有都带_c_var
/////////////////////////////
//var _c_var_ = '';
var _c_var_user;//当前登录用户信息
var _c_var_menuNodes, //Story #48624 by wwy 存储左侧menu的数据
	_c_var_trucksBrandDetailId; //Story #48624 by wwy 记录 _c_var_trucks_brand_detailId frame的id
///////////////////////////
//
//  end 公共变量 end
///////////////////////////

///////////////////////////
//
//  公共函数 函数名都带 _c_fun_
///////////////////////////

function _c_fun_setCookie(name, value)
{
	var argv = _c_fun_setCookie.arguments;
	var argc = _c_fun_setCookie.arguments.length;
	var expires = (argc > 2) ? argv[2] : null;
	if(expires!=null)
	{
		var LargeExpDate = new Date ();
		LargeExpDate.setTime(LargeExpDate.getTime() + (expires*1000*3600*24));
	}
	document.cookie = name + "=" + escape (value)+((expires == null) ? "" : ("; expires=" +LargeExpDate.toGMTString()));
}

function _c_fun_getCookie(Name)
{
	var search = Name + "=";
	if(document.cookie.length > 0)
	{
		offset = document.cookie.indexOf(search);
		if(offset != -1)
		{
			offset += search.length;
			end = document.cookie.indexOf(";", offset);
			if(end == -1) end = document.cookie.length;
			return unescape(document.cookie.substring(offset, end));
		}
		else return "";
	}
}

function _c_fun_deleteCookie(name)
{
	var expdate = new Date();
	expdate.setTime(expdate.getTime() - (86400 * 1000 * 1));
	setCookie(name, "", expdate);
}

///////////////////////////
//
//  end 公共函数 end
///////////////////////////


//////////////////////////\
//
//  公共库 _c_store
/////////////////////////

//////////////////////////\
//
//  end 公共库
/////////////////////////
/*
保留默认的一些变量函数和库内容
*/

///////////////////////////
//
//		公共变量 所有都带_c_var
/////////////////////////////
//var _c_var_ = '';
var _c_var_user;//当前登录用户信息
var _c_var_menuNodes, //Story #48624 by wwy 存储左侧menu的数据
	_c_var_trucksBrandDetailId; //Story #48624 by wwy 记录 _c_var_trucks_brand_detailId frame的id
///////////////////////////
//
//  end 公共变量 end
///////////////////////////

///////////////////////////
//
//  公共函数 函数名都带 _c_fun_
///////////////////////////

function _c_fun_setCookie(name, value)
{
	var argv = _c_fun_setCookie.arguments;
	var argc = _c_fun_setCookie.arguments.length;
	var expires = (argc > 2) ? argv[2] : null;
	if(expires!=null)
	{
		var LargeExpDate = new Date ();
		LargeExpDate.setTime(LargeExpDate.getTime() + (expires*1000*3600*24));
	}
	document.cookie = name + "=" + escape (value)+((expires == null) ? "" : ("; expires=" +LargeExpDate.toGMTString()));
}

function _c_fun_getCookie(Name)
{
	var search = Name + "=";
	if(document.cookie.length > 0)
	{
		offset = document.cookie.indexOf(search);
		if(offset != -1)
		{
			offset += search.length;
			end = document.cookie.indexOf(";", offset);
			if(end == -1) end = document.cookie.length;
			return unescape(document.cookie.substring(offset, end));
		}
		else return "";
	}
}

function _c_fun_deleteCookie(name)
{
	var expdate = new Date();
	expdate.setTime(expdate.getTime() - (86400 * 1000 * 1));
	setCookie(name, "", expdate);
}

///////////////////////////
//
//  end 公共函数 end
///////////////////////////


//////////////////////////\
//
//  公共库 _c_store
/////////////////////////

//////////////////////////\
//
//  end 公共库
/////////////////////////
window.onload=function (){
	var proZyht = window.parent.location.host == 'oms.hbgszyht.com'
	var zyhtBol  = (window.parent.location.host == 'oms.test.hbgszyht.com'||proZyht)
	var urlConfig = {
		test:'http://oms.test.hbgszyht.com/v2/',
		pro:'http://oms.hbgszyht.com/v2/'
	}
	console.log('zyhtBol===',zyhtBol);
	console.log('=window.parent.location===',window.parent.location);
	console.log('=window.location',window.location);
	console.log('zyhtBol===',zyhtBol);

	if (zyhtBol) {
		$('iframe').remove()
		window.parent.location.href =proZyht? urlConfig.pro : urlConfig.test
	}
}
