/**
 * 跳转页面
 */
function jumpPage(receipt_no){
    if (receipt_no) {
        Ext.Ajax.request({
            url: '../inside.php?t=json&m=common&f=getSourceInfo',
            method: 'post',
            params: {url: '/oil_invoiced_consumption'},
            success: function(resp,opts){
                var re = Ext.decode(resp.responseText);
                if (re.data && parent && parent.main_panel) {

                    var str = '<iframe scrolling="auto" frameborder="0" width="100%" height="100%" src="' + re.data.url + '" id="iframe' + re.data.id + '"></iframe>';
                    var n = parent.main_panel.getComponent(re.data.id);
                    if (!n) { //判断是否已经打开该面板
                        n = parent.main_panel.add({
                            id: 'treenode' + re.data.source_code,
                            title: re.data.source_name,
                            closable: true, //关闭按钮
                            html: str       //通过html载入目标页
                        });
                    }
                    parent.main_panel.setActiveTab(n);
                    setWinDetail(re.data.id, receipt_no,1);
                } else {
                    Ext.MessageBox.alert('提示！', '资源不存在');
                }
            }
        });
    } else {
        Ext.MessageBox.alert('提示！', '政策不存在');
    }
}

function setWinDetail(id, value, flag){
    var win = parent.document.getElementById('iframe'+id).contentWindow;
    win.onload = function(){
        afterLoaded(win.document, function(){
            winCallback(win, value);
        });
    };
    //如果已经打开也需要重新刷新
    winCallback(win, value);
}
function afterLoaded(dom, fn){
    if (dom.readyState == 'complete') {
        fn();
    } else {
        setTimeout(function(){afterLoaded(dom,fn)},500);
        return;
    }
}
function winCallback(win, value){
    win._win = win;

    if (!win.Ext) {
        return;
    }

    win.Ext.getCmp('receipt_no').setValue(value);
    win.Ext.getCmp('trade_time_ge').setValue('');
    win.Ext.getCmp('trade_time_le').setValue('');
    win.Ext.getCmp('create_time_ge').setValue('');
    win.Ext.getCmp('create_time_le').setValue('');
    win.Ext.getCmp('trades_id').setValue('');
    win.Ext.getCmp('trade_place').setValue('');
    win.Ext.getCmp('oil_type').setValue('');
    win.Ext.getCmp('payer_name').setValue('');
    win.Ext.getCmp('receipt_oil_name').setValue('');
    win.Ext.getCmp('receipt_code').setValue('');
    win.Ext.getCmp('receipt_apply_no').setValue('');
    win.Ext.getCmp('receipt_type').setValue('');
    win.main_store.removeAll();
    win.main_store.load();

}