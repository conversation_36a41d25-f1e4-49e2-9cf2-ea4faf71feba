var lastMonthToday = function(){
    var now=new Date();
    var year = now.getFullYear();//getYear()+1900=getFullYear()
    var month = now.getMonth() +1;//0-11表示1-12月
    var day = now.getDate();
    if(parseInt(month)<10){
        month="0"+month;
    }
    if(parseInt(day)<10){
        day="0"+day;
    }

    now =year + '-'+ month + '-' + day;

    if (parseInt(month) ==1) {//如果是1月份，则取上一年的12月份
        return (parseInt(year) - 1) + '-12-' + day;
    }

    var  preSize= new Date(year, parseInt(month)-1, 0).getDate();//上月总天数
    if (preSize < parseInt(day)) {//上月总天数<本月日期，比如3月的30日，在2月中没有30
        return year + '-' + month + '-01';
    }

    if(parseInt(month) <=10){
        return year + '-0' + (parseInt(month)-1) + '-' + day;
    }else{
        return year + '-' + (parseInt(month)-1) + '-' + day;
    }

}


var date = new Date();
var currentMonth = new Date(date.getFullYear(), date.getMonth(), date.getDate());
var preMonth = lastMonthToday();

var top_panel = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 90,
    items:
        [
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '销售单据编号：'
                        },
                        {
                            xtype: 'textfield',
                            id: 'sale_noIn',
                            name: 'sale_noIn',
                            width: 140
                        },
                        {
                            xtype: 'button',
                            text: '多选',
                            listeners: {
                                'click': function () {
                                    show_search_win('请输入编号，以回车换行：', 'sale_noIn', function () {
                                        main_store.removeAll();
                                        main_store.load();
                                    },',');
                                }
                            }
                        },
                        {
                            xtype: 'displayfield',
                            value: '开票日期：',
                            style: 'padding-left: 10px'
                        },
                        {
                            xtype: "datefield",
                            width: 100,
                            value: preMonth,
                            format: 'Y-m-d',
                            name: 'receipt_timeGe',
                            id: 'receipt_timeGe',
                        },
                        {
                            xtype: 'displayfield',
                            value: '~'
                        },
                        {
                            xtype: "datefield",
                            width: 100,
                            value: currentMonth,
                            format: 'Y-m-d',
                            name: 'receipt_timeLe',
                            id: 'receipt_timeLe',
                        },

                        {
                            xtype: 'displayfield',
                            value: '发票抬头：'
                        },
                        {
                            xtype: 'textfield',
                            id: 'receipt_title',
                            name: 'receipt_title',
                            width: 160
                        },

                        {
                            xtype: 'displayfield',
                            value: '发票类型：'
                        },
                        {
                            xtype: 'combo',
                            id: 'receipt_type',
                            hiddenName: 'receipt_type',
                            mode: 'local',
                            width: 80,
                            emptyText: '请选择..',
                            triggerAction: 'all',
                            forceSelection: true,
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [
                                    [30, '专票（电子）'],
                                    [40, '普票（电子）'],
                                    [50, '数电专票'],
                                    [60, '数电普票'],
                                ]
                            }),
                        },
                        {
                            xtype: 'displayfield',
                            value: '勾稽状态：'
                        },
                        {
                            xtype: 'combo',
                            hiddenName: 'choice_status',
                            mode: 'local',
                            width: 80,
                            emptyText: '请选择..',
                            triggerAction: 'all',
                            forceSelection: true,
                            displayField: 'choice_status_name',
                            valueField: 'choice_status_id',
                            id: 'choice_status',
                            value: '',
                            store: new Ext.data.SimpleStore({
                                fields: ['choice_status_id', 'choice_status_name'],
                                data: [['10', '未勾'], ['20', '已勾']]
                            }),
                        },

                    ]
            },
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '发&nbsp;&nbsp票&nbsp;&nbsp号&nbsp;&nbsp码：'
                        },
                        {
                            xtype: 'textfield',
                            id: 'receipt_noIn',
                            name: 'receipt_noIn',
                            width: 140
                        },
                        {
                            xtype: 'button',
                            text: '多选',
                            listeners: {
                                'click': function () {
                                    show_search_win('请输入编号，以回车换行：', 'receipt_noIn', function () {
                                        main_store.removeAll();
                                        main_store.load();
                                    },',');
                                }
                            }
                        },

                        {
                            xtype: 'displayfield',
                            value: '创建日期：',
                            style: 'padding-left: 10px'
                        },
                        {
                            xtype: "datefield",
                            width: 100,
                            value: '',
                            format: 'Y-m-d',
                            id: 'createtimeGe',
                            name: 'createtimeGe',
                        },
                        {
                            xtype: 'displayfield',
                            value: '~'
                        },
                        {
                            xtype: "datefield",
                            width: 100,
                            value: '',
                            format: 'Y-m-d',
                            id: 'createtimeLe',
                            name: 'createtimeLe',
                        },

                        {
                            xtype: 'displayfield',
                            value: '开票状态：'
                        },
                        {
                            xtype: 'combo',
                            hiddenName: 'status',
                            id: 'status',
                            mode: 'local',
                            width: 80,
                            emptyText: '请选择..',
                            triggerAction: 'all',
                            forceSelection: true,
                            displayField: 'status_name',
                            valueField: 'status_id',
                            value: '',
                            store: new Ext.data.SimpleStore({
                                fields: ['status_id', 'status_name'],
                                data: [['10', '已开'], ['20', '待废'], ['30', '已废']]
                            }),
                        }
                    ]
            },
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '发票申请单号：'
                        },
                        {
                            xtype: 'textfield',
                            id: 'receipt_apply_noIn',
                            name: 'receipt_apply_noIn',
                            width: 140
                        },
                        {
                            xtype: 'button',
                            text: '多选',
                            listeners: {
                                'click': function () {
                                    show_search_win('请输入编号，以回车换行：', 'receipt_apply_noIn', function () {
                                        main_store.removeAll();
                                        main_store.load();
                                    },',');
                                }
                            }
                        },
                        // {
                        //     xtype: 'displayfield',
                        //     value: '开票金额：',
                        // },
                        // {
                        //     xtype: "textfield",
                        //     width: 100,
                        //     value: '',
                        //     id: 'receipt_amountGe',
                        //     name: 'receipt_amountGe',
                        // },
                        // {
                        //     xtype: 'displayfield',
                        //     value: '~'
                        // },
                        // {
                        //     xtype: "textfield",
                        //     width: 100,
                        //     value: '',
                        //     id: 'receipt_amountLe',
                        //     name: 'receipt_amountLe',
                        // },
                        {
                            xtype: 'displayfield',
                            value: '红蓝票：',
                            style:'padding-left:12px'
                        },
                        {
                            xtype: 'combo',
                            width: 100,
                            hiddenName: 'color_type',
                            id: 'color_type',
                            mode: 'local',
                            triggerAction:'all',
                            displayField: 'value',
                            valueField: 'key',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [['10', '红票'],['20', '蓝票']]
                            })
                        },
                        {
                            xtype: 'button',
                            text: '查询',
                            style: 'padding-left : 10px;',
                            handler: function () {
                                main_store.removeAll();//移除原来的数据
                                main_store.load();//加载新搜索的数据
                            }
                        },
                        {
                            xtype: 'button',
                            text: '重置',
                            style: 'padding-left : 10px;',
                            handler: function () {
                                top_panel.getForm().reset();
                                main_store.removeAll();//移除原来的数据
                                main_store.load();//加载新搜索的数据
                            }
                        },
                    ]
            },
            // {
            //     xtype: 'compositefield',
            //     items:
            //         [
            //             {
            //                 xtype: 'displayfield',
            //                 value: '销&nbsp;售&nbsp;&nbsp;方&nbsp;名&nbsp;称：'
            //             },
            //             {
            //                 xtype: 'combo',
            //                 width: 140,
            //                 id: 'seller_name',
            //                 hiddenName: "seller_name",
            //                 editable: true,
            //                 emptyText: '请选择..',
            //                 triggerAction: 'all',
            //                 displayField: 'company_name',
            //                 valueField: 'company_name',
            //                 store: new Ext.data.Store({
            //                     url: '../inside.php?t=json&m=oil_inspect_tax&f=getOperatorsList&company_type=B&use_self=1',
            //                     reader: new Ext.data.JsonReader({
            //                         totalProperty: 'data.total',
            //                         root: 'data.data'
            //                     }, ['company_name', 'company_name'])
            //                 })
            //             },
            //
            //         ]}
        ]
});