var requireTip = {xtype: 'displayfield', html : '<font color="red">*</font>'};//input后红色星号必填项提示

/************************************* 上传图片预览 *********************************/
function previewImage(filename, file, imageShow_box_dom, hiddenName) {
    //上传图片类型,在前台的拦截
    var img_reg = /\.([jJ][pP][gG]){1}$|\.([jJ][pP][eE][gG]){1}$|\.([gG][iI][fF]){1}$|\.([pP][nN][gG]){1}$|\.([bB][mM][pP]){1}$/;
    if(filename != null && !Ext.isEmpty(filename)) {
        //是否是规定的图片类型
        if (img_reg.test(filename)) {
            var img_content;
            var file_size;
            var file_type;

            if (Ext.isIE) {//IE浏览器
                imageShow_box_dom.src = Ext.BLANK_IMAGE_URL;// 覆盖原来的图片
                imageShow_box_dom.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src = url;
            } else {
                file_size = file.size;
                file_type = file.type;

                var reader = new FileReader();
                reader.onload = function(evt){
                    imageShow_box_dom.src = evt.target.result;
                    var loadMask = new Ext.LoadMask('titleWin', {
                        msg: '正在上传，请稍候......',
                        removeMask: true // 完成后移除
                    });
                    loadMask.show();
                    //上传图片
                    Ext.Ajax.request({
                        url: '../inside.php?t=json&m=common&f=uploadImgByContentToOSS',
                        success: function(resp){
                            var result = Ext.decode(resp.responseText);
                            Ext.getCmp(hiddenName).setValue(result.url);
                            Ext.getCmp(hiddenName+'DelBtn').enable();
                            Ext.Msg.alert('系统提示', result.msg);
                            loadMask.hide();
                        },
                        failure: function(resp){
                            var result = Ext.decode(resp.responseText);
                            Ext.Msg.alert('系统提示', result.msg);
                        },
                        params: { content: imageShow_box_dom.src, size: file_size, type: file_type }
                    });
                }
                reader.readAsDataURL(file);
            }
        } else {
            Ext.Msg.alert('提示','请选择图片类型的文件！');
        }
    }
}
/****************************** 删除图片 ******************************/
function deleteImg(hiddenName,previewBoxId,id){
    id = id ? id : false;
    Ext.Msg.confirm('系统提示', '确定删除该图片？', function(btn){
        if(btn === 'yes'){
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=common&f=deleteImg',
                success: function(resp){
                    var result = Ext.decode(resp.responseText);
                    if(result.success){
                        Ext.getCmp(hiddenName+'DelBtn').disable();
                        Ext.getCmp(hiddenName).setValue('');
                        Ext.getCmp('img_'+hiddenName).reset();
                        Ext.getCmp(previewBoxId).getEl().dom.src = '';
                        main_store.reload();
                    }

                    Ext.Msg.alert('系统提示', result.msg);
                },
                failure: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                },
                params: { url: Ext.getCmp(hiddenName).getValue(),id:id, field:hiddenName }
            });
        }
    });
}
/****************************** 图片查看 ******************************/
var image_window = new Ext.Window({
    id: 'image-window',
    title: '图片查看',
    width: 750,
    height: 550,
    resizable: false,
    closeAction: 'hide',
    layout: 'border',
    modal : true,
    items: [
        {
            xtype: 'panel',
            region: 'center',
            layout: 'fit',
            bodyStyle: 'background-color:#E5E3DF;',
            frame: false,
            border: false,
            submitValue: false,
            html: '<div id="mapPic" style="text-align: center">'
            +'<img id="view-image" src="" border="0" style="cursor: move; vertical-align:middle;" ></div>'
            +'<div class="nav" id="img_nav">'
            +'<div class="up" id="up" style="cursor:pointer;">上移</div>'
            +'<div class="right" id="right" style="cursor:pointer;">右移</div>'
            +'<div class="down" id="down" style="cursor:pointer;">下移</div>'
            +'<div class="left" id="left" style="cursor:pointer;">左移</div>'
            +'<div class="zoom" id="zoom" style="cursor:pointer;">还原</div>'
            +'<div class="in" id="in" style="cursor:pointer;">放大</div>'
            +'<div class="out" id="out" style="cursor:pointer;">缩小</div>'
            +'<div class="clockwise" id="clockwise" style="cursor:pointer;">顺时针旋转</div>'
            +'<div class="anti_clockwise" id="anti_clockwise" style="cursor:pointer;">逆时针旋转</div>'
            +'</div>'
        },
    ],
    buttons: [{
        text: '取消',
        handler: function() {
            image_window.hide();
        }
    }],
    listeners: {
        'show': function(c) {
            var image = Ext.get('view-image');
            //可以拖动
            new Ext.dd.DD(image, 'pic');
        }
    }
});

/**
 * 初始化
 */
function pageInit(){
    var image = Ext.get('view-image');
    image.setStyle('width','');
    image.setStyle('height','');

    //解除所有绑定，避免重复绑定事件
    Ext.get('view-image').removeAllListeners();
    Ext.get('up').removeAllListeners();
    Ext.get('down').removeAllListeners();
    Ext.get('left').removeAllListeners();
    Ext.get('right').removeAllListeners();
    Ext.get('in').removeAllListeners();
    Ext.get('out').removeAllListeners();
    Ext.get('zoom').removeAllListeners();
    Ext.get('clockwise').removeAllListeners();
    Ext.get('anti_clockwise').removeAllListeners();
    Ext.get('anti_clockwise').removeAllListeners();

    if(image!=null){
        Ext.get('view-image').on({
            'dblclick':{fn:function(){
                zoom(image,true,1.2);
            }
            }});
        new Ext.dd.DD(image, 'pic');

        image.center();//图片居中

        //获得原始尺寸
        image.osize = {
            width:image.getWidth(),
            height:image.getHeight()
        };

        Ext.get('up').on('click',function(){imageMove('up',image);});       //向上移动
        Ext.get('down').on('click',function(){imageMove('down',image);});   //向下移动
        Ext.get('left').on('click',function(){imageMove('left',image);});   //左移
        Ext.get('right').on('click',function(){imageMove('right',image);}); //右移动
        Ext.get('in').on('click',function(){zoom(image,true,1.5);});        //放大
        Ext.get('out').on('click',function(){zoom(image,false,1.5);});      //缩小
        Ext.get('zoom').on('click',function(){restore(image);});            //还原
        Ext.get('clockwise').on('click', function(){rotate(image, 90)});//BOSSOPS-844 by wwy
        Ext.get('anti_clockwise').on('click', function(){rotate(image, -90)});//BOSSOPS-844 by wwy
        restore(image, true);
    }
};


/**
 * 图片移动
 */
function imageMove(direction, el) {
    el.move(direction, 50, true);
}


/**
 *
 * @param el 图片对象
 * @param type true放大,false缩小
 * @param offset 量
 */
function zoom(el,type,offset){
    var width = el.getWidth();
    var height = el.getHeight();
    var nwidth = type ? (width * offset) : (width / offset);
    var nheight = type ? (height * offset) : (height / offset);
    var left = type ? -((nwidth - width) / 2):((width - nwidth) / 2);
    var top =  type ? -((nheight - height) / 2):((height - nheight) / 2);
    el.animate(
        {
            height: {to: nheight, from: height},
            width: {to: nwidth, from: width},
            left: {by:left},
            top: {by:top}
        },
        null,
        null,
        'backBoth',
        'motion'
    );
}


/**
 * 图片还原
 */
function restore(el, noFadeOut) {
    var size = el.osize;

    //自定义回调函数
    function center(el,callback){
        el.center();
        callback(el);
    }
    if(noFadeOut){
        rotate(el, 0, true);
        el.setSize(size.width, size.height, {callback:function(){
            center(el,function(ee){//调用回调
                ee.show();
            });
        }});
    }else{
        el.fadeOut({callback:function(){
            rotate(el, 0, true);
            el.setSize(size.width, size.height, {callback:function(){
                center(el,function(ee){//调用回调
                    ee.fadeIn();
                });
            }});
        }
        });
    }

}

/**
 * 获取当前旋转图片的角度
 */
function getImgCurAngle() {
    var curTransform = Ext.get('view-image').dom.style.transform,
        reg       = /(rotate\((([\-]\d+|\d+)(deg))\))/i,
        matchData = curTransform.match (reg),
        curAngle  = matchData ? matchData[3] : 0;

    return curAngle;
}
/**
 * 旋转图片
 * @param image
 * @param angle
 * @param isRestore  是否还愿图片
 */
function rotate(image, angle, isRestore){
    if(isRestore){
        var setAngle = 0;
    }else{
        var curAngle  = getImgCurAngle(),
            setAngle  = Number(curAngle) + Number(angle);

        if(setAngle == 360 || setAngle == -360){
            setAngle = 0;
        }
    }

    image.dom.style.transform = 'rotate('+setAngle+'deg)';
}

function showImgWin(id){
    var url = Ext.getCmp(id).getValue();
    if(url){
        var timestamp=new Date().getTime();
        image_window.show();
        Ext.get('view-image').dom.src = '';
        Ext.get('view-image').dom.src = url
        setTimeout(pageInit,500);
    }
}
/****************************** 发票抬头 ******************************/
function isValidate(receipt_type){
    let corpAddrComponent = Ext.getCmp('corp_addr'),
        taxpayerNoComponent = Ext.getCmp('taxpayer_no'),
        bankNameComponent = Ext.getCmp('bank_name'),
        imgCorpLicenceComponent = Ext.getCmp('img_corp_licence'),
        imgTaxpayerLicenceComponent = Ext.getCmp('img_taxpayer_licence'),
        imgBankLicenceComponent = Ext.getCmp('img_bank_licence'),
        isThreeOneComponent = Ext.getCmp('is_three_one'),
        titleWinComponent = Ext.getCmp('titleWin'),
        moreComponent = Ext.getCmp('morePanel'),
        corpAddrRequiredComponent = Ext.getCmp('corp_addr_required'),
        taxpayerNoRequiredComponent = Ext.getCmp('taxpayer_no_required'),
        bankNameRequiredComponent = Ext.getCmp('bank_name_required')
    switch (receipt_type) {
        case '普票':
        case '普票（电子）':
        case '数电普票':
        case '数电专票':
            corpAddrRequiredComponent.hide()
            corpAddrComponent.allowBlank = true
            taxpayerNoRequiredComponent.show()
            taxpayerNoComponent.allowBlank = false
            bankNameRequiredComponent.hide()
            bankNameComponent.allowBlank = true
            break
        case '专票':
        case '专票（电子）':
            corpAddrRequiredComponent.show()
            corpAddrComponent.allowBlank = false
            taxpayerNoRequiredComponent.show()
            taxpayerNoComponent.allowBlank = false
            bankNameRequiredComponent.show()
            bankNameComponent.allowBlank = false
            break
    }
    imgCorpLicenceComponent.allowBlank = true
    imgTaxpayerLicenceComponent.allowBlank = true
    imgBankLicenceComponent.allowBlank = true
    isThreeOneComponent.allowBlank = true
    moreComponent.hide()
    titleWinComponent.setHeight(250)
    if (receipt_type.indexOf('专票') >= 0) {
        titleWinComponent.setHeight(560)
        moreComponent.show()
        Ext.getCmp('uploadFile').doLayout()
        imgCorpLicenceComponent.allowBlank = false
        imgTaxpayerLicenceComponent.allowBlank = false
        imgBankLicenceComponent.allowBlank = false
        isThreeOneComponent.allowBlank = false
    }
}

function receiptTitleWindow() {

    let preData  = arguments[0];

    if (preData) {

        Ext.Ajax.request({
            url:'/inside.php?t=json&m=oil_receipt_title&f=getList',
            async: false,
            params:{id:preData.id, mode:10},
            success: function (resp) {
                if (Ext.decode(resp.responseText).data.data.length > 0)
                {
                    preData = Ext.decode(resp.responseText).data.data[0];
                }
            },
            failure:function (resp) {
                Ext.Msg.alert('提示', Ext.decode(resp.responseText).msg);
            }
        })
    }
    var receiptTitleForm = new Ext.form.FormPanel({
        buttonAlign: 'center',
        hideLabels: true,
        fileUpload: true,
        width: 1000,
        frame: true,
        bodyStyle: 'background-color: white;',
        region: 'center',
        items: [
            {
                xtype: 'compositefield',
                style: 'margin-top: 10px',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '申请机构：',
                        style:'padding-left:12px;'
                    },
                    new Ext.form.ComboBox({
                        width: 200,
                        id: 'orgcode',
                        hiddenName: 'orgcode',
                        triggerAction: 'all',
                        forceSelection: true,
                        mode: 'remote',
                        queryParam:'keyword',
                        minChars:2,
                        displayField: 'org_name',//显示的值
                        valueField: 'orgcode',//后台接收的key
                        store: getOilOrg,
                        emptyText: '请选择..',
                        enableKeyEvents: true,
                        listeners: {
                            'select': function (combo, record, index) {
                                var orgcode = Ext.getCmp('orgcode').getValue();
                                //加载付款公司
                                getPayName.removeAll();
                                getPayName.load({
                                    params:{'orgcode':orgcode},
                                    callback:function () {
                                        receiptTitleForm.getForm().loadRecord({data : preData});
                                        Ext.getCmp('is_three_one').fireEvent('select');
                                        if(typeof(preData) != "undefined"){
                                            if(orgcode != preData.orgroot){
                                                Ext.getCmp('pay_company_id').setValue('');
                                                Ext.getCmp('corp_name').setValue('');
                                            }
                                        }
                                    }
                                });
                                Ext.getCmp('pay_company_id').setValue('');
                                Ext.getCmp('corp_name').setValue('');
                            },
                        },
                    }),
                    {
                        xtype: 'displayfield',
                        value: '付款公司：',
                        style: 'padding-left: 5px'
                    },
                    new Ext.form.ComboBox({
                        width: 180,
                        hiddenName: 'pay_company_id',
                        id: 'pay_company_id',
                        triggerAction: 'all',
                        forceSelection: true,
                        allowBlank: false,
                        blankText : '不能为空',
                        mode: 'local',
                        displayField: 'pay_name',//显示的值
                        valueField: 'id',//后台接收的key
                        store: getPayName,
                        emptyText: '请选择..',
                        enableKeyEvents: true,
                        listeners: {
                            'select': function (combo, record, index) {
                                //console.log('record--',record);
                                //console.log('record--',record.get('pay_name'));
                                Ext.getCmp('corp_name').setValue(record.get('company_name'))
                                var diff = record.get("is_diff");
                                if(diff == 1){
                                    Ext.getCmp('corp_name').setDisabled(false);
                                }else{
                                    Ext.getCmp('corp_name').setDisabled(true);
                                }
                                /*//加载付款公司
                                getPayName.removeAll();
                                getPayName.load({
                                    params:{'orgcode':Ext.getCmp('orgcode').getValue()},
                                    callback:function () {
                                        receiptTitleForm.getForm().loadRecord({data : preData});
                                        Ext.getCmp('is_three_one').fireEvent('select');
                                    }
                                });*/
                            },
                        },
                    }),
                    requireTip,
                    {
                        xtype: 'displayfield',
                        value: '发票抬头：',
                        style: 'padding-left: 14px'
                    },
                    {
                        xtype: 'textfield',
                        width: 200,
                        id: 'corp_name',
                        name: 'corp_name',
                        allowBlank: false,
                        disabled: true,
                        blankText: '不能为空',
                    },
                    requireTip,
                    {
                        xtype: 'displayfield',
                        value: '开票类型：',
                        style: 'padding-left: 12px'
                    },
                    {
                        xtype: 'combo',
                        width: 90,
                        id: 'receipt_type',
                        name: 'receipt_type',
                        hiddenName: 'receipt_type',
                        allowBlank: false,
                        blankText : '不能为空',
                        editable:false,
                        mode: 'local',
                        triggerAction:'all',
                        displayField: 'receipt_type',
                        valueField: 'receipt_type',
                        store: new Ext.data.SimpleStore({
                            fields: ['receipt_type', 'receipt_type'],
                            data: [
                                ['专票', '专票'],
                                ['普票', '普票'],
                                ['专票（电子）', '专票（电子）'],
                                ['普票（电子）', '普票（电子）'],
                                ['数电专票', '数电专票'],
                                ['数电普票', '数电普票']
                            ]
                        }),
                        listeners:{
                            select:function(combo, record, index){
                                isValidate(combo.getValue())
                            }
                        }
                    },
                    requireTip,
                    {
                        xtype: 'box',
                        autoEl: {
                            html:'<span>新建</span>',
                            onclick:'createPayCompany()'
                        },
                        style: 'padding-left: 5px;padding-top: 3px;color:blue;cursor:pointer;',
                    },
                ]
            },
            {
                xtype: 'compositefield',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '地址、电话：',
                        style: 'padding-left: 5px'

                    },
                    {
                        xtype: 'textfield',
                        width: 500,
                        name: 'corp_addr',
                        id: 'corp_addr',
                        allowBlank: false,
                        blankText: '不能为空'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'corp_addr_required',
                        html : '<font color="red">*</font>'
                    },
                    {
                        xtype: 'displayfield',
                        value: '纳税人识别号：',
                        style: 'padding-left: 17px'
                    },
                    {
                        xtype: 'textfield',
                        width: 300,
                        name: 'taxpayer_no',
                        id: 'taxpayer_no',
                        allowBlank: false,
                        blankText: '不能为空'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'taxpayer_no_required',
                        html : '<font color="red">*</font>'
                    },
                ]
            },
            {
                xtype: 'compositefield',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '开户行及账号：'
                    },
                    {
                        xtype: 'textfield',
                        width: 500,
                        name: 'bank_name',
                        id: 'bank_name',
                        allowBlank: false,
                        blankText: '不能为空'
                    },
                    {
                        xtype: 'displayfield',
                        id: 'bank_name_required',
                        html : '<font color="red">*</font>'
                    },
                ]
            },
            {
                xtype: 'fieldset',
                title: '增值税专用发票信息',
                layout:'form',
                hideLabels: true,
                autoHeight: true,
                id: "morePanel",
                hidden: true,
                style: 'margin: 5px',
                items: [
                    {
                        xtype: 'compositefield',
                        items: [
                            {
                                xtype: 'displayfield',
                                value: '资质类型：',
                                style: 'padding-left: 12px'
                            },
                            {
                                xtype: 'combo',
                                width: 150,
                                id: 'is_three_one',
                                hiddenName: 'is_three_one',
                                allowBlank: false,
                                blankText : '不能为空',
                                editable:false,
                                mode: 'local',
                                triggerAction:'all',
                                displayField: 'value',
                                valueField: 'key',
                                store: new Ext.data.SimpleStore({
                                    fields: ['key', 'value'],
                                    data: [['10', '三证合一'],['20', '非三证合一']]
                                }),
                                listeners:{
                                    select:function(combo, record, index){
                                        var key = Ext.getCmp('is_three_one').getValue();
                                        console.log('key--',key);
                                        if(key == '10'){//三证合一
                                            Ext.getCmp('moreImg').hide();
                                            Ext.getCmp('img_tax_licence').allowBlank = true;
                                        }else{
                                            Ext.getCmp('moreImg').show();
                                            Ext.getCmp('img_tax_licence').allowBlank = false;
                                        }
                                    }
                                }
                            },
                        ]
                    },
                    {
                        xtype: "panel",
                        border: false,
                        width: 1030,
                        hideLabels: true,
                        id:'uploadFile',
                        layout:'column',
                        autoScroll: true,
                        items: [
                            {
                                xtype:'fieldset',
                                columnWidth: 0.25,
                                title: '单位营业执照',
                                hideLabels: true,
                                layout:'form',
                                autoHeight:true,
                                style: 'padding-left: 12px;padding-top:10px;margin-top:10px;',
                                defaults: {
                                    anchor: '-20'
                                },
                                items :[
                                    {
                                        xtype:'box',
                                        width: 228,
                                        id:'previewCorpLicence',
                                        height: 182,
                                        style:'padding-bottom:5px;',
                                        autoEl: {
                                            tag: 'img',
                                            src: '',
                                            onclick:"showImgWin('corp_licence')",
                                        }
                                    },
                                    {
                                        xtype:'panel',
                                        layout:'column',
                                        style:'padding-bottom:5px;',
                                        items:[
                                            new Ext.ux.form.FileUploadField({
                                                id: 'img_corp_licence',
                                                buttonText: '浏览',
                                                columnWidth:.80,
                                                emptyText: '点击「浏览」上传图片',
                                                name: 'img_corp_licence',
                                                allowBlank: false,
                                                blankText: '不能为空',
                                                style:'margin-right:10px;',
                                                listeners:
                                                    {
                                                        'fileselected':function(o,v){
                                                            var file = o.fileInput.dom.files[0];
                                                            var imgDom = Ext.getCmp('previewCorpLicence').getEl().dom;
                                                            previewImage(v, file, imgDom,'corp_licence');
                                                        }
                                                    }
                                            }),
                                            requireTip,
                                            {xtype: 'hidden', id: 'corp_licence', name: 'corp_licence'},
                                            {
                                                xtype:'button',
                                                text:'删除',
                                                disabled:true,
                                                id:'corp_licenceDelBtn',
                                                style:'padding-left:10px;',
                                                columnWidth:.20,
                                                handler:function(){
                                                    if(preData){
                                                        deleteImg('corp_licence','previewCorpLicence',preData.id);
                                                    }else{
                                                        deleteImg('corp_licence','previewCorpLicence');
                                                    }
                                                }
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                xtype:'fieldset',
                                title: '一般纳税人资格证',
                                hideLabels: true,
                                autoHeight:true,
                                columnWidth: 0.25,
                                style: 'padding-left: 12px;padding-top:10px;margin-top:10px;',
                                defaults: {
                                    anchor: '-20'
                                },
                                items :[
                                    {
                                        xtype:'box',
                                        width: 228,
                                        height: 182,
                                        id:'previewTaxpayer',
                                        style:'padding-bottom:5px;',
                                        autoEl: {
                                            tag: 'img',
                                            src: '',
                                            onclick:"showImgWin('taxpayer_licence')",
                                        }
                                    },
                                    {
                                        xtype:'panel',
                                        layout:'column',
                                        style:'padding-bottom:5px;',
                                        items:[
                                            new Ext.ux.form.FileUploadField({
                                                id: 'img_taxpayer_licence',
                                                buttonText: '浏览',
                                                allowBlank: false,
                                                blankText: '不能为空',
                                                columnWidth:.80,
                                                emptyText: '点击「浏览」上传图片',
                                                name: 'img_taxpayer_licence',
                                                listeners:
                                                    {
                                                        'fileselected':function(o,v){
                                                            var file = o.fileInput.dom.files[0];
                                                            var imgDom = Ext.getCmp('previewTaxpayer').getEl().dom;
                                                            previewImage(v, file, imgDom,'taxpayer_licence');
                                                        }
                                                    }
                                            }),
                                            requireTip,
                                            {xtype: 'hidden', id: 'taxpayer_licence', name: 'taxpayer_licence'},
                                            {
                                                xtype:'button',
                                                text:'删除',
                                                id:'taxpayer_licenceDelBtn',
                                                disabled:true,
                                                style:'padding-left:10px;',
                                                columnWidth:.20,
                                                handler:function(){
                                                    if(preData){
                                                        deleteImg('taxpayer_licence','previewTaxpayer',preData.id);
                                                    }else{
                                                        deleteImg('taxpayer_licence','previewTaxpayer');
                                                    }
                                                }
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                xtype:'fieldset',
                                title: '银行开户许可证',
                                hideLabels: true,
                                autoHeight:true,
                                columnWidth: 0.25,
                                style: 'padding-left: 12px;padding-top:10px;margin-top:10px;',
                                defaults: {
                                    anchor: '-20'
                                },
                                items :[
                                    {
                                        xtype:'box',
                                        width: 228,
                                        height: 182,
                                        style:'padding-bottom:5px;',
                                        id:'previewBankLicence',
                                        autoEl: {
                                            tag: 'img',
                                            src: '',
                                            onclick:"showImgWin('bank_licence')",
                                        }
                                    },
                                    {
                                        xtype:'panel',
                                        layout:'column',
                                        style:'padding-bottom:5px;',
                                        items:[
                                            new Ext.ux.form.FileUploadField({
                                                id: 'img_bank_licence',
                                                buttonText: '浏览',
                                                allowBlank: false,
                                                blankText: '不能为空',
                                                columnWidth:.80,
                                                emptyText: '点击「浏览」上传图片',
                                                name: 'img_bank_licence',
                                                listeners:
                                                    {
                                                        'fileselected':function(o,v){
                                                            var file = o.fileInput.dom.files[0];
                                                            var imgDom = Ext.getCmp('previewBankLicence').getEl().dom;
                                                            previewImage(v, file, imgDom,'bank_licence');
                                                        }
                                                    }
                                            }),
                                            requireTip,
                                            {xtype: 'hidden', id: 'bank_licence', name: 'bank_licence'},
                                            {
                                                xtype:'button',
                                                text:'删除',
                                                id:'bank_licenceDelBtn',
                                                disabled:true,
                                                style:'padding-left:10px;',
                                                columnWidth:.20,
                                                handler:function(){
                                                    if(preData){
                                                        deleteImg('bank_licence','previewBankLicence',preData.id);
                                                    }else{
                                                        deleteImg('bank_licence','previewBankLicence');
                                                    }
                                                }
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                xtype:'panel',
                                id:'moreImg',
                                columnWidth: 0.25,
                                layout:'column',
                                hidden: true,
                                items:[
                                    {
                                        xtype:'fieldset',
                                        columnWidth: 1,
                                        title: '税务登记证',
                                        hideLabels: true,
                                        autoHeight:true,
                                        style: 'padding-left: 12px;padding-top:10px;margin-top:10px;',
                                        defaults: {
                                            anchor: '-20'
                                        },
                                        items :[
                                            {
                                                xtype:'box',
                                                width: 228,
                                                height: 182,
                                                style:'padding-bottom:5px;',
                                                id:'previewTaxLicence',
                                                autoEl: {
                                                    tag: 'img',
                                                    src: '',
                                                    onclick:"showImgWin('tax_licence')",
                                                }
                                            },
                                            {
                                                xtype:'panel',
                                                layout:'column',
                                                style:'padding-bottom:5px;',
                                                items:[
                                                    new Ext.ux.form.FileUploadField({
                                                        id: 'img_tax_licence',
                                                        buttonText: '浏览',
                                                        allowBlank: false,
                                                        blankText: '不能为空',
                                                        columnWidth:.80,
                                                        emptyText: '点击「浏览」上传图片',
                                                        name: 'img_tax_licence',
                                                        listeners:
                                                            {
                                                                'fileselected':function(o,v){
                                                                    var file = o.fileInput.dom.files[0];
                                                                    var imgDom = Ext.getCmp('previewTaxLicence').getEl().dom;
                                                                    previewImage(v, file, imgDom,'tax_licence');
                                                                }
                                                            }
                                                    }),
                                                    {xtype: 'hidden', id: 'tax_licence', name: 'tax_licence'},
                                                    {
                                                        xtype:'button',
                                                        text:'删除',
                                                        id:'tax_licenceDelBtn',
                                                        disabled:true,
                                                        style:'padding-left:10px;',
                                                        columnWidth:.20,
                                                        handler:function(){
                                                            if(preData){
                                                                deleteImg('tax_licence','previewTaxLicence',preData.id);
                                                            }else{
                                                                deleteImg('tax_licence','previewTaxLicence');
                                                            }
                                                        }
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                ]
            },
            {
                xtype: 'compositefield',
                style: 'margin-top:10px;',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '后台备注：',
                        style: 'padding-left: 10px;'
                    },
                    {
                        xtype: 'textfield',
                        width: 900,
                        name: 'admin_remark',
                    },
                ]
            },
            {
                xtype: 'compositefield',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '客户备注：',
                        style: 'padding-left: 10px'
                    },
                    {
                        xtype: 'textfield',
                        width: 900,
                        name: 'custom_remark',
                    },
                ]
            },
        ],
        buttons:[
            {
                text: '确定',
                handler: function(){
                    var _form = receiptTitleForm.getForm();
                    var info = Ext.getCmp('pay_company_id');
                    var text = info.lastSelectionText;
                    var arr = text.split(" ");

                    submitParams.corp_name = arr[1];

                    if(_form.isValid()){
                        _form.submit({
                            url     : url,
                            waitMsg : 'Saving Data...',
                            success : function(form, action){
                                Ext.MessageBox.alert("系统提示", action.result.msg);

                                receiptTitleWindow.destroy();

                                main_store.removeAll();
                                main_store.load();
                            },
                            failure : function (form, action){
                                Ext.MessageBox.alert("系统提示", action.result.msg);
                            },
                            params: submitParams
                        });
                    }else{
                        console.log('111--111')
                    }
                }
            },
            {
                text: '取消',
                handler: function() {
                    receiptTitleWindow.destroy();
                }
            }
        ]
    });
    var receiptTitleWindow = new Ext.Window({
        layout:"border",
        width:1100,
        id:'titleWin',
        x:115,y:40,
        title:'维护发票抬头',
        closeAction:'destroy',
        plain: true,
        modal: true,
        items:[receiptTitleForm]
    });
    receiptTitleWindow.show();

    if(preData){ //编辑操作
        getOilOrg.load({
            params:{orgcode:preData.orgroot},
            callback:function () {
                receiptTitleForm.getForm().setValues([
                    {id: 'orgcode', value: preData.orgroot },
                ]);
                Ext.getCmp('orgcode').fireEvent('select');
                Ext.getCmp('corp_name').setValue('');
            }
        });
        var url = '../inside.php?t=json&m=oil_receipt_title&f=receiptUpdate',
            submitParams = {id: preData.id,data_from:1};
        isValidate(preData.receipt_type);
        if(preData.receipt_type.indexOf("专票") >= 0){

            if (preData.corp_licence) {
                corp_licence = preData.corp_licence.split('/').pop();
                Ext.getCmp('img_corp_licence').setValue(decodeURI(corp_licence));
                Ext.getCmp('corp_licenceDelBtn').enable();
            }
            if (preData.taxpayer_licence) {
                taxpayer_licence = preData.taxpayer_licence.split('/').pop();
                Ext.getCmp('img_taxpayer_licence').setValue(decodeURI(taxpayer_licence));
                Ext.getCmp('taxpayer_licenceDelBtn').enable();
            }
            if (preData.tax_licence) {
                tax_licence = preData.tax_licence.split('/').pop();
                Ext.getCmp('img_tax_licence').setValue(decodeURI(tax_licence));
                Ext.getCmp('tax_licenceDelBtn').enable();
            }
            if (preData.bank_licence) {
                bank_licence = preData.bank_licence.split('/').pop();
                Ext.getCmp('img_bank_licence').setValue(decodeURI(bank_licence));
                Ext.getCmp('bank_licenceDelBtn').enable();
            }
            Ext.getCmp('previewCorpLicence').getEl().dom.src = preData.corp_licence;
            Ext.getCmp('previewTaxpayer').getEl().dom.src = preData.taxpayer_licence;
            Ext.getCmp('previewTaxLicence').getEl().dom.src = preData.tax_licence;
            Ext.getCmp('previewBankLicence').getEl().dom.src = preData.bank_licence;
        }
    }else{ //新增操作
        let receiptTypeComponent = Ext.getCmp('receipt_type')
        receiptTypeComponent.setValue('专票');
        receiptTypeComponent.fireEvent('select', receiptTypeComponent);
        Ext.getCmp('is_three_one').setValue('10');
        // Ext.getCmp('is_three_one').fireEvent('select');
        var url = '../inside.php?t=json&m=oil_receipt_title&f=receiptAdd',
            submitParams  = {data_from:1};
    }
}

//添加
function add(){
    receiptTitleWindow();
}

//编辑
function edit(){
    var preData = grid_list.getSelectionModel().getSelections()[0].data;
    receiptTitleWindow(preData);
}

//删除
function remove(){
    Ext.Msg.confirm('系统提示', '确定删除该条发票抬头？', function(btn){
        if(btn === 'yes'){
            var id = grid_list.getSelectionModel().getSelections()[0].data.id;
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=oil_receipt_title&f=receiptDelete',
                success: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                    main_store.reload();
                },
                failure: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                },
                params: { id: id }
            });
        }
    });
}

//审核
function audit(){
    Ext.Msg.confirm('系统提示', '确定审核该条发票抬头？', function(btn){
        if(btn === 'yes'){
            var id = grid_list.getSelectionModel().getSelections()[0].data.id;
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=oil_receipt_title&f=receiptTitleAudit',
                success: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                    main_store.reload();
                    grid_list.getSelectionModel().clearSelections();
                },
                failure: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                    main_store.reload();
                    grid_list.getSelectionModel().clearSelections();
                },
                params: { id: id }
            });
        }
    });
}
//销审
function unaudit(){
    Ext.Msg.confirm('系统提示', '确定销审该条发票抬头？', function(btn){
        if(btn === 'yes'){
            var id = grid_list.getSelectionModel().getSelections()[0].data.id;
            Ext.Ajax.request({
                url: '../inside.php?t=json&m=oil_receipt_title&f=receiptTitleUnAudit',
                success: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                    main_store.reload();
                    grid_list.getSelectionModel().clearSelections();
                },
                failure: function(resp){
                    var result = Ext.decode(resp.responseText);
                    Ext.Msg.alert('系统提示', result.msg);
                    main_store.reload();
                    grid_list.getSelectionModel().clearSelections();
                },
                params: { id: id }
            });
        }
    });
}
//驳回
function reject(){
    var reject_win;
    var form_panel = new Ext.form.FormPanel({
        region: 'center',
        hideLabels: true,
        bodyStyle: 'padding: 20px',
        height: 60,
        items: [
            {// 第１行
                xtype: 'compositefield',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '驳回原因：'
                    },
                    {
                        xtype: 'textarea',
                        id: 'reject_reason',
                        name: 'reject_reason',
                        width: 400,
                        height:100
                    },
                ]
            }
        ],
        buttons: [{
            text: "确定",
            handler: function () {
                Ext.Msg.confirm('系统提示', '确定驳回该条发票抬头？', function(btn){
                    if(btn === 'yes'){
                        var id = grid_list.getSelectionModel().getSelections()[0].data.id;
                        Ext.Ajax.request({
                            url: '../inside.php?t=json&m=oil_receipt_title&f=receiptTitleReject',
                            success: function(resp){
                                reject_win.destroy();
                                var result = Ext.decode(resp.responseText);
                                Ext.Msg.alert('系统提示', result.msg);
                                main_store.reload();
                                grid_list.getSelectionModel().clearSelections();
                            },
                            failure: function(resp){
                                reject_win.destroy();
                                var result = Ext.decode(resp.responseText);
                                Ext.Msg.alert('系统提示', result.msg);
                                main_store.reload();
                                grid_list.getSelectionModel().clearSelections();
                            },
                            params: { id: id,reject_reason:Ext.getCmp('reject_reason').getValue() }
                        });
                    }
                });
            }
        }, {
            text: "取消",
            handler: function () {
                reject_win.destroy();
            }
        }]
    });

    reject_win = new Ext.Window({
        layout:"border",
        width:550,
        height:200,
        title:'驳回发票抬头',
        closeAction:'destroy',
        plain: true,
        modal: true,
        items:[form_panel]
    });
    reject_win.show();
}

//跳转至油品机构维护
function createPayCompany() {
    Ext.Ajax.request({
        url: '../inside.php?t=json&m=common&f=getSourceInfo',
        method: 'post',
        params: {url: '/oil_org'},
        success: function(resp,opts){
            var re = Ext.decode(resp.responseText);
            if (re.data && parent && parent.main_panel) {
                var str = '<iframe scrolling="auto" frameborder="0" width="100%" height="100%" src="' + re.data.url + '" id="iframe' + re.data.id + '"></iframe>';
                var n = parent.main_panel.getComponent(re.data.id);
                if (!n) { //判断是否已经打开该面板
                    n = parent.main_panel.add({
                        id: 'treenode' + re.data.source_code,
                        title: re.data.source_name,
                        closable: true, //关闭按钮
                        html: str       //通过html载入目标页
                    });
                }
                parent.main_panel.setActiveTab(n);
            } else {
                Ext.MessageBox.alert('提示！', '资源不存在');
            }
        }
    });
};