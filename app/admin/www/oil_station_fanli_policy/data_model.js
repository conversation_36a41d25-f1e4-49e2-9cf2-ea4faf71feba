/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_station_fanli_policy';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id",'station_name','status_txt',"station_type_txt","rebate_grade","rebate_grade_txt","station_type","station_code",
            ,"provice_name","city_name","city_code","provice_code","dockor_name","station_classify_txt","ownner_name","ownner_email","ownner_phone",
            "station_id","fanli_type","profit_mode","fanli_shape","price_diff_desc","special_customer_desc","fanli_unit","fanli_money","fanli_way","fanli_level","policy_details","remark"
            ,"start_time","end_time","creator_id","creator","last_operator_id","last_operator","createtime","updatetime","fanli_type_txt","profit_mode_txt","fanli_shape_txt","fanli_unit_txt",
            "fanli_way_txt","fanli_level_txt",'fanli_status_txt',"deposit_card","special","upper_fanli_unit_txt","upper_fanli_money","lower_fanli_money","lower_fanli_level_txt",
            "lower_policy_details","lower_fanli_unit_txt","customer","diff_price","is_highway_txt","station_brand_txt","pcode","pcode_name"
        ]
    ),
});

//获取省
var getProvice = new Ext.data.Store({url: getUrl('oil_1st_station','getProvice'),
    // autoLoad:true,
    reader: new Ext.data.JsonReader({
        totalProperty:'',
        root:''
    },['key', 'value'])
});

//获取市
var getCity = new Ext.data.Store({url: getUrl('oil_1st_station','getProvice'),
    // autoLoad:true,
    reader: new Ext.data.JsonReader({
        totalProperty:'',
        root:''
    },['key', 'value'])
});

//返利档位
var fanliLevel = new Ext.data.Store({url: getUrl('oil_1st_station','classMap'),
    autoLoad:true,
    // params:{flag:5},
    reader: new Ext.data.JsonReader({
        totalProperty:'',
        root:''
    },['key', 'value']),
    listeners: {
        'beforeload': function () {
            this.baseParams.flag = 5;
        }
    }
});