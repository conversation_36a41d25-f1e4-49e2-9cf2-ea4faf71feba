//油品机构列表

var date = new Date();
var currentDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());

var lastMonthToday = function(){
    var now=new Date();
    var year = now.getFullYear();//getYear()+1900=getFullYear()
    var month = now.getMonth() +1;//0-11表示1-12月
    var day = now.getDate();
    if(parseInt(month)<10){
        month="0"+month;
    }
    if(parseInt(day)<10){
        day="0"+day;
    }

    now =year + '-'+ month + '-' + day;

    if (parseInt(month) ==1) {//如果是1月份，则取上一年的12月份
        return (parseInt(year) - 1) + '-12-' + day;
    }

    var  preSize= new Date(year, parseInt(month)-1, 0).getDate();//上月总天数
    if (preSize < parseInt(day)) {//上月总天数<本月日期，比如3月的30日，在2月中没有30
        return year + '-' + month + '-01';
    }

    if(parseInt(month) <=10){
        return year + '-0' + (parseInt(month)-1) + '-' + day;
    }else{
        return year + '-' + (parseInt(month)-1) + '-' + day;
    }

}

var top_panel = new Ext.form.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 50,
    items:
        [
            {
                xtype: 'compositefield',
                items:
                    [
                        {
                            xtype: 'displayfield',
                            value: '创建日期：'
                        },
                        {
                            xtype: "datefield",
                            width: 100,
                            name: 'createtimeGe',
                            format: 'Y-m-d',
                            value: '',//lastMonthToday()
                        },
                        {
                            xtype: 'displayfield',
                            value: '~'
                        },
                        {
                            xtype: "datefield",
                            width: 100,
                            format: 'Y-m-d',
                            value: currentDay,
                            name: 'createtimeLe'
                        },
                        {
                            xtype: 'displayfield',
                            value: '机构：',
                        },
                        new Ext.form.ComboBox({
                            width: 238,
                            id: 'orgroot',
                            hiddenName: 'orgroot',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'remote',
                            queryParam: 'keyword',
                            minChars: 2,
                            displayField: 'org_name',//显示的值
                            valueField: 'orgcode',//后台接收的key
                            store: getOilOrg,
                            emptyText: '请选择..',
                            enableKeyEvents: true
                        }),

                        {
                            xtype: 'displayfield',
                            value: '付款公司：',
                        },
                        new Ext.form.ComboBox({
                            width: 230,
                            hiddenName: 'id',
                            triggerAction: 'all',
                            forceSelection: true,
                            mode: 'remote',
                            queryParam: 'keyword',
                            minChars: 2,
                            displayField: 'pay_name',//显示的值
                            valueField: 'id',//后台接收的key
                            store: new Ext.data.Store({
                                proxy: new Ext.data.HttpProxy({
                                    url: getUrl('oil_pay_company', 'getPayNameByOrg'),
                                    method: "POST"
                                }),
                                reader: new Ext.data.JsonReader(
                                    {
                                        totalProperty: 'data',
                                        root: 'data'
                                    },
                                    ['id', 'pay_name']
                                )
                            }),
                            emptyText: '请选择..',
                            enableKeyEvents: true
                        }),

                        {
                            xtype: 'displayfield',
                            value: '状态：'
                        },
                        {
                            xtype: 'combo',
                            hiddenName: 'status',
                            mode: 'local',
                            width: 80,
                            emptyText: '请选择..',
                            triggerAction: 'all',
                            forceSelection: true,
                            displayField: 'value',
                            valueField: 'key',
                            value: '',
                            store: new Ext.data.SimpleStore({
                                fields: ['key', 'value'],
                                data: [['', '全部'], ['1', '正常'], ['2', '停用']]
                            }),
                        },

                        {
                            xtype: 'button',
                            text: '查询',
                            style: 'padding-left : 10px;',
                            handler: function () {
                                main_store.removeAll();//移除原来的数据
                                main_store.load();//加载新搜索的数据
                            }
                        },
                        {
                            xtype: 'button',
                            text: '重置',
                            style: 'padding-left : 10px;',
                            handler: function () {
                                top_panel.getForm().reset();
                                main_store.removeAll();//移除原来的数据
                                main_store.load();//加载新搜索的数据
                                btnInit();
                            }
                        },
                    ]
            }
        ]
});

//导出
this.export = function () {
    Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
            if (btn == 'yes') {
                var params = top_panel.getForm().getValues(true);
                window.location.href = '/inside.php?t=json&m=oil_receipt_quota_day&f=getList&_export=1' + '&' + params;
            }
        }
    );
};