/**
 * Created by wwy on 2015/8/1.
 *
 */

var requireTip = {xtype: 'displayfield', html : '<font color="red">*</font>'};//input后红色星号必填项提示
// 自定义验证
Ext.apply(Ext.form.VTypes, {
    IDCard:  function(v) {
        return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(v);
    },
    IDCardText: '身份证格式不正确',
    Mobile:  function(v) {
        return /^\d{11,15}$/.test(v);
    },
    MobileText: '手机号码格式不正确'
});

function addExtraInfo(){
    var selectRow   = grid_service.getSelectionModel().getSelections()[0],
        customer_id = selectRow.get('customer_id'),
        trucks_id   = selectRow.get('id');
    Ext.Ajax.request({  //检测能否添加操作
        url: '../inside.php?t=json&m=gps_truck&f=checkOperateInfo',
        params: {customer_id: customer_id, trucks_id: trucks_id, extra: true},
        success: function(resp, opts){
            var res = Ext.decode(resp.responseText);
            if(res.addAble_webank){//先检测是否添加微众银行开户信息
                if(!res.addAble_EI){//能否添加车辆附加信息
                    Ext.Ajax.request({
                        url: '../inside.php?t=json&m=gps_truck&f=getEIData',
                        params: {'customer_id': customer_id},
                        success: function(resp, opts){
                            var preData = Ext.decode(resp.responseText);
                            //从主表中获取行数据
                            preData['device_no']    = selectRow.get('installing_gpsno');   //安装时设备号
                            preData['brand']        = selectRow.get('brand');              //品牌型号
                            preData['model']        = selectRow.get('model_name');         //车辆型号
                            preData['plate_no']     = selectRow.get('truck_no');           //车牌号
                            preData['engine_no'] = selectRow.get('engine_no');             //发动机号码
                            preData['trucks_id']    = selectRow.get('id');                 //gps_truck主表对应的id
                            showExtraInfo(preData, 'add');
                        },
                        failure: function(resp, opts){
                            Ext.MessageBox.alert("消息!", '数据加载失败，请稍后再试！');
                        }
                    });
                }else{
                    Ext.MessageBox.alert("消息!", '车辆附加信息已添加，直接进行编辑操作！', function(){
                        editExtraInfo();
                    });
                }
            }else{
                Ext.MessageBox.alert("消息!", '该客户尚未同步微众银行开户信息！');
            };

        },
        failure: function(resp, opts){
            Ext.MessageBox.alert("消息!", '数据加载失败，请稍后再试！');
        }
    });

}

/**
 * 删除车辆档案
 */
function delExtraInfo(){
    var selectRow   = grid_service.getSelectionModel().getSelections()[0],
        customer_id = selectRow.get('customer_id'),
        trucks_id   = selectRow.get('id');
    Ext.Ajax.request({  //检测能否添加操作
        url: '../inside.php?t=json&m=gps_truck&f=checkOperateInfo',
        params: {customer_id: customer_id, trucks_id: trucks_id, extra: true},
        success: function(resp, opts){
            var res = Ext.decode(resp.responseText);
            if(res.addAble_EI){//能否添加车辆附加信息
                Ext.MessageBox.confirm('提示', '是否要删除当前选中的车辆信息？', function(val){
                    if(val == 'yes'){
                        var selectRow   = grid_service.getSelectionModel().getSelections()[0];
                        Ext.Ajax.request({
                            url: '../inside.php?t=json&m=gps_truck&f=delExtraInfo',
                            params: {'customer_id': customer_id, 'trucks_id': trucks_id},
                            success: function(resp, opts){
                                var res = Ext.decode(resp.responseText);
                                Ext.MessageBox.alert("消息!", res.msg);
                            },
                            failure: function(resp, opts){
                                Ext.MessageBox.alert("消息!", '删除失败，请稍后尝试！');
                            }
                        });
                    }
                });
            }else{
                Ext.MessageBox.alert("消息!", '车辆附加信息不存在，请先添加信息！' );
            }
        },
        failure: function(resp, opts){
            Ext.MessageBox.alert("消息!", '数据加载失败，请稍后再试！');
        }
    });
}

/**
 * 编辑车辆档案
 */
function editExtraInfo(){
    var selectRow   = grid_service.getSelectionModel().getSelections()[0],
        customer_id = selectRow.get('customer_id'),
        trucks_id   = selectRow.get('id');
    Ext.Ajax.request({  //检测能否添加操作
        url: '../inside.php?t=json&m=gps_truck&f=checkOperateInfo',
        params: {customer_id: customer_id, trucks_id: trucks_id, extra: true},
        success: function(resp, opts){
            var res = Ext.decode(resp.responseText);
            if(res.addAble_webank){//先检测是否添加微众银行开户信息
                if(res.addAble_EI){//能否添加车辆附加信息
                    Ext.Ajax.request({
                        url: '../inside.php?t=json&m=gps_truck&f=getEIData',
                        params: {'customer_id': selectRow.get('customer_id'), 'trucks_id': selectRow.get('id')},
                        success: function(resp, opts){
                            var preData = Ext.decode(resp.responseText);
                            showExtraInfo(preData, 'edit');
                        },
                        failure: function(resp, opts){
                            Ext.MessageBox.alert("消息!", '数据加载失败，请稍后再试！');
                        }
                    });
                }else{
                    Ext.MessageBox.alert("消息!", '车辆附加信息不存在，请先添加信息！' );
                }
            }else{
                Ext.MessageBox.alert("消息!", '该客户尚未同步微众银行开户信息！');
            };
        },
        failure: function(resp, opts){
            Ext.MessageBox.alert("消息!", '数据加载失败，请稍后再试！');
        }
    });

}
/**
 * 查看车辆档案
 */
function viewExtraInfo(){
    var selectRow   = grid_service.getSelectionModel().getSelections()[0],
        customer_id = selectRow.get('customer_id'),
        trucks_id   = selectRow.get('id');
    Ext.Ajax.request({  //检测能否添加操作
        url: '../inside.php?t=json&m=gps_truck&f=checkOperateInfo',
        params: {customer_id: customer_id, trucks_id: trucks_id, extra: true},
        success: function(resp, opts){
            var res = Ext.decode(resp.responseText);
            if(res.addAble_webank){//先检测是否添加微众银行开户信息
                if(res.addAble_EI){//能否添加车辆附加信息
                    Ext.Ajax.request({
                        url: '../inside.php?t=json&m=gps_truck&f=getEIData',
                        params: {'customer_id': selectRow.get('customer_id'), 'trucks_id': selectRow.get('id')},
                        success: function(resp, opts){
                            var preData = Ext.decode(resp.responseText);
                            showViewInfo(preData);
                        },
                        failure: function(resp, opts){
                            Ext.MessageBox.alert("消息!", '数据加载失败，请稍后再试！');
                        }
                    });
                }else{
                    Ext.MessageBox.alert("消息!", '车辆附加信息不存在，请先添加信息！' );
                }
            }else{
                Ext.MessageBox.alert("消息!", '该客户尚未同步微众银行开户信息！');
            };
        },
        failure: function(resp, opts){
            Ext.MessageBox.alert("消息!", '数据加载失败，请稍后再试！');
        }
    });


}
/**
 * 同步Link操作
 */
function submitToLink(){

    var selectRow   = grid_service.getSelectionModel().getSelections();

    Ext.MessageBox.confirm('提示', '当前选中的车辆附加信息是否要同步Link？', function(val) {
        if (val == 'yes') {
            var len  = selectRow.length;
            if(len > 200){
                Ext.MessageBox.alert("消息!", '一次同步Link数据不能超过200条');
            }else if(len == 1){
                var customer_id = selectRow[0].get('customer_id'),
                    trucks_id   = selectRow[0].get('id');
                Ext.Ajax.request({  //检测能否添加操作
                    url: '../inside.php?t=json&m=gps_truck&f=checkOperateInfo',
                    params: {customer_id: customer_id, trucks_id: trucks_id},
                    success: function(resp, opts){
                        var res = Ext.decode(resp.responseText);
                        if(res.addAble_EI){//能否添加车辆附加信息
                            linkOperator(selectRow);
                        }else{
                            Ext.MessageBox.alert("消息!", '车辆附加信息不存在，请先添加信息！' );
                        }
                    },
                    failure: function(resp, opts){
                        Ext.MessageBox.alert("消息!", '数据加载失败，请稍后再试！');
                    }
                });
            }else{
                linkOperator(selectRow);
            }
        }
    })
}
/**
 * 处理link同步方法
 * @param selectRow
 */
function linkOperator(selectRow){
    var loadMask = new Ext.LoadMask(Ext.getBody() ,{
            removeMask: true,
            msg : '正在同步Link中......'
        }),
        len        = selectRow.length,
        submitData = [];
    loadMask.show();
    for(var i = 0; i < len; i++){
        var jsonStr = '{"customer_id":'+selectRow[i].get('customer_id')+', "trucks_id":'+selectRow[i].get('id')+'}';
        submitData.push(jsonStr);
    }
    submitData = JSON.stringify(submitData);
    Ext.Ajax.request({
        url: '../inside.php?t=json&m=gps_truck&f=submitToLink',
        params: {'submitData': submitData},
        success: function(resp, opts){
            var res = Ext.decode(resp.responseText);
            Ext.MessageBox.alert("消息!", res.msg);
            loadMask.hide();
            store_service.reload();
            grid_service.getSelectionModel().clearSelections();
        },
        failure: function(resp, opts){
            Ext.MessageBox.alert("消息!", '同步Link失败，请稍后再试！');
            loadMask.hide();
        }
    });
}

/**
 * 添加/编辑 车辆附加信息 窗口显示
 */
function showExtraInfo(preData, action){
    if(action == 'add'){
        var url       = '../inside.php?t=json&m=gps_truck&f=addExtraInfo',
            action    = '添加',
            model     = preData.model ? preData.model : '',
            modelArr  = model.split('_');
        if(modelArr.length == 4){//判断车辆型号 是否在车型中按规则自动生成的
            preData.model = modelArr[0] + '_' + modelArr[1];
            preData.brand = modelArr[0];
        }
    }else if(action == 'edit'){
        var url       = '../inside.php?t=json&m=gps_truck&f=editExtraInfo',
            action    = '编辑';
    };
    var trucks_id = preData.trucks_id;

    var EI_form = new Ext.form.FormPanel({
        labelAlign: 'right',
        labelWidth: 130,
        buttonAlign: 'center',
        width: 1000,
        frame: true,
        bodyStyle : 'background-color: white;',
        items: [{
            layout: 'column',
            title : '车辆档案信息',
            bodyStyle: 'padding: 10px 0',
            items:[{
                columnWidth:.33,
                xtype:'fieldset',
                autoHeight:true,
                border: false,
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '顶级机构名称',
                            name: 'org_name',
                            allowBlank: false,
                            disabled  : true
                        },
                            requireTip]
            },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '车辆所有人',
                            name: 'owner',
                            allowBlank: false,
                            maxLength : 20,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项为必填项，最大不能超过20字。'
                                    });
                                }
                            }
                        },
                            requireTip]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '品牌型号',
                            name: 'brand',
                            allowBlank: false,
                            maxLength: 50,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项为必填项。最大不超过50字'
                                    });
                                }
                            }
                        },
                            requireTip]

                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'datefield',
                            format: 'Y-m-d',
                            editable: false,
                            width: 170,
                            fieldLabel: '注册日期',
                            name: 'registration_date',
                        }]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '车辆型号',
                            name: 'model',
                            allowBlank: false,
                            maxLength: 50,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项为必填项。最大不超过50字。'
                                    });
                                }
                            }
                        },
                            requireTip]

                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '发动机功率',
                            name: 'horsepower',
                            maxLength: 20,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项最大不超过20字。'
                                    });
                                }
                            }
                        }]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '发动机号码',
                            name: 'engine_no',
                            allowBlank: false,
                            maxLength : 20,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项为必填项。最大不超过20字。'
                                    });
                                }
                            }
                        },
                            requireTip]

                    }
                ]
            }, {
                columnWidth:.33,
                xtype:'fieldset',
                autoHeight:true,
                border: false,
                /*defaults: {width: 170},*/
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '车队管理人姓名',
                            name: 'manager_name',
                            allowBlank: false,
                            disabled : true
                        },
                            requireTip]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: 'G7盒子设备号',
                            name: 'device_no',
                            allowBlank: false,
                            maxLength: 20,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项为必填项。最大不超过20字。'
                                    });
                                }
                            }
                        },
                            requireTip]

                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '车辆所有人住址',
                            name: 'address',
                            allowBlank: false,
                            maxLength : 150,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项为必填项。最大不超过150字。'
                                    });
                                }
                            }
                        },
                            requireTip]

                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '车辆识别代码',
                            name: 'vin',
                            allowBlank: false,
                            maxLength : 20,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项为必填项。最大不超过20字。'
                                    });
                                }
                            }
                        },
                            requireTip]

                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'datefield',
                            format: 'Y-m-d',
                            editable: false,
                            width: 170,
                            fieldLabel: '发证日期',
                            name: 'issue_date',
                            allowBlank: false
                        },
                            requireTip]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '车牌号',
                            name: 'plate_no',
                            allowBlank: false,
                            maxLength: 20,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项为必填项。最大不超过20字。'
                                    });
                                }
                            }
                        },
                            requireTip]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '发动机型号',
                            name: 'engine_model',
                            maxLength: 20,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项最大不超过20字。'
                                    });
                                }
                            }
                        }]
                    }
                ]
            },{
                columnWidth:.33,
                xtype:'fieldset',
                autoHeight:true,
                border: false,
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '车队管理人身份证',
                            name: 'manager_id_no',
                            disabled : true,
                            vtype: 'IDCard'
                        },
                            requireTip]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '车辆类型',
                            name: 'vehicle_type',
                            maxLength: 150,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项最大不超过150字。'
                                    });
                                }
                            }
                        }]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '使用性质',
                            name: 'usage',
                            maxLength:20,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项最大不超过20字。'
                                    });
                                }
                            }
                        }]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '检验记录',
                            name: 'inspection_record',
                            maxLength: 150,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项最大不超过150字。'
                                    });
                                }
                            }
                        }]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'textfield',
                            width: 170,
                            fieldLabel: '发动机排量',
                            name: 'displacement',
                            maxLength: 20,
                            listeners: {
                                render: function (field) {
                                    Ext.QuickTips.register({
                                        target: field.el,
                                        title: field.fieldLabel,
                                        text: '此项最大不超过20字。'
                                    });
                                }
                            }
                        }]
                    },
                    {
                        xtype: 'compositefield',
                        style: 'margin-bottom: 5px',
                        items:[{
                            xtype: 'datefield',
                            format: 'Y-m-d',
                            editable: false,
                            width: 170,
                            fieldLabel: '制造年月',
                            name: 'manufacture_date'
                        }]
                    }
                ]
            }]
        },{
            layout: 'form',
            items :[{
                layout: 'column',
                title : '司机信息',
                bodyStyle: 'padding: 10px 0',
                items :[
                    {
                        columnWidth:.33,
                        xtype:'fieldset',
                        autoHeight:true,
                        border: false,
                        defaultType: 'textfield',
                        items: [
                            {
                                xtype: 'compositefield',
                                style: 'margin-bottom: 5px',
                                items:[{
                                    xtype: 'textfield',
                                    width: 170,
                                    fieldLabel: '司机姓名',
                                    name: 'driver',
                                    maxLength: 50,
                                    listeners: {
                                        render: function (field) {
                                            Ext.QuickTips.register({
                                                target: field.el,
                                                title: field.fieldLabel,
                                                text: '此项最大不超过50字。'
                                            });
                                        }
                                    }
                                }]
                            },
                            {
                                xtype: 'compositefield',
                                style: 'margin-bottom: 5px',
                                items:[{
                                    xtype: 'datefield',
                                    format: 'Y-m-d',
                                    editable: false,
                                    width: 170,
                                    fieldLabel: '司机身份证有效期',
                                    name: 'driver_id_expire'
                                }]
                            },
                            {
                                xtype: 'compositefield',
                                style: 'margin-bottom: 5px',
                                items:[{
                                    xtype: 'datefield',
                                    format: 'Y-m-d',
                                    editable: false,
                                    width: 170,
                                    fieldLabel: '司机驾驶证初次领证日',
                                    name: 'driver_licene_issued_date'
                                }]
                            }
                        ]
                    },{
                        columnWidth:.33,
                        xtype:'fieldset',
                        autoHeight:true,
                        border: false,
                        defaultType: 'textfield',
                        items: [
                            {
                                xtype: 'compositefield',
                                style: 'margin-bottom: 5px',
                                items:[{
                                    xtype: 'textfield',
                                    width: 170,
                                    fieldLabel: '司机身份证号',
                                    name: 'driver_id_no',
                                    vtype: 'IDCard',
                                    listeners: {
                                        render: function (field) {
                                            Ext.QuickTips.register({
                                                target: field.el,
                                                title: field.fieldLabel,
                                                text: '身份证号码为15位数字或17位数字加x。'
                                            });
                                        }
                                    }
                                }]
                            },
                            {
                                xtype: 'compositefield',
                                style: 'margin-bottom: 5px',
                                items:[{
                                    xtype: 'textfield',
                                    width: 170,
                                    fieldLabel: '司机住址',
                                    name: 'driver_address',
                                    maxLength: 100,
                                    listeners: {
                                        render: function (field) {
                                            Ext.QuickTips.register({
                                                target: field.el,
                                                title: field.fieldLabel,
                                                text: '此项最大不超过100字。'
                                            });
                                        }
                                    }
                                }]
                            },
                            {
                                xtype: 'compositefield',
                                style: 'margin-bottom: 5px',
                                items:[{
                                    xtype: 'textfield',
                                    width: 170,
                                    fieldLabel: '司机驾驶证准驾车型',
                                    name: 'driver_driving_type',
                                    maxLength: 20,
                                    listeners: {
                                        render: function (field) {
                                            Ext.QuickTips.register({
                                                target: field.el,
                                                title: field.fieldLabel,
                                                text: '此项最大不超过20字。'
                                            });
                                        }
                                    }
                                }]
                            }
                        ]
                    },{
                        columnWidth:.33,
                        xtype:'fieldset',
                        autoHeight:true,
                        border: false,
                        defaultType: 'textfield',
                        items: [
                            {
                                xtype: 'compositefield',
                                style: 'margin-bottom: 5px',
                                items:[{
                                    xtype: 'textfield',
                                    width: 170,
                                    fieldLabel: '司机驾驶证号',
                                    name: 'driver_licene_no',
                                    maxLength: 50,
                                    listeners: {
                                        render: function (field) {
                                            Ext.QuickTips.register({
                                                target: field.el,
                                                title: field.fieldLabel,
                                                text: '此项最大不超过50字。'
                                            });
                                        }
                                    }
                                }]
                            },
                            {
                                xtype: 'compositefield',
                                style: 'margin-bottom: 5px',
                                items:[{
                                    xtype: 'datefield',
                                    format: 'Y-m-d',
                                    editable: false,
                                    width: 170,
                                    fieldLabel: '司机驾驶证有效起始日',
                                    name: 'driver_licene_valid_from'
                                }]
                            },
                            {
                                xtype: 'compositefield',
                                style: 'margin-bottom: 5px',
                                items:[{
                                    xtype: 'datefield',
                                    format: 'Y-m-d',
                                    editable: false,
                                    width: 170,
                                    fieldLabel: '司机驾驶证有效期限',
                                    name: 'driver_licene_valid_expire'
                                }]
                            }
                        ]
                    }
                ]
            }]
        }],
        buttons:[
            {
                text: '确定',
                handler: function(){
                    var _form = EI_form.getForm();
                    if(_form.isValid()){
                        _form.submit({
                            url     : url,
                            waitMsg : 'Saving Data...',
                            params  :{trucks_id: trucks_id},
                            success : function(form, action){
                                Ext.MessageBox.alert("消息!", action.result.msg);
                                EI_window.destroy();
                            },
                            failure : function (form, action){
                                Ext.MessageBox.alert("消息!", '操作失败，请稍后再尝试！');
                            }
                        });
                    }
                }
            },
            {
                text: '取消',
                handler: function() {
                    EI_window.destroy();
                }
            }
        ]

    });

    EI_form.getForm().loadRecord({data : preData});

    var EI_window = new Ext.Window({
        title : action + '车辆档案信息',
        id    : 'EI_window',
        items :[
            EI_form
        ]
    });

    EI_window.show();

}


/**
 * 查看车辆附加信息  窗口显示
 */
function showViewInfo(preData){

    var carTable = new Ext.Panel({
        title: '车辆档案信息',
        layout:'table',
        defaults: {
            // 对每一个子面板都有效applied to each contained panel
            bodyStyle:'padding: 10px 2px; line-height: 15px; border: none'
        },
        layoutConfig: {
            // 这里指定总列数The total column count must be specified here
            columns: 6
        },
        items: [
            {html: '<p>顶级机构名称</p>', cellCls:'tdTitle'},
            {html:  preData.org_name ? preData.org_name : ' ', cellCls:'tdContent'},
            {html: '<p>车队管理人姓名</p>', cellCls:'tdTitle'},
            {html:  preData.manager_name ? preData.manager_name : ' ', cellCls:'tdContent'},
            {html: '<p>车队管理人身份证</p>', cellCls:'tdTitle'},
            {html:  preData.manager_id_no ? preData.manager_id_no : ' ', cellCls:'tdContent'},

            {html: '<p>车辆所有人</p>', cellCls:'tdTitle'},
            {html:  preData.address ? preData.address : ' ', cellCls:'tdContent'},
            {html: '<p>G7盒子设备号</p>', cellCls:'tdTitle'},
            {html:  preData.device_no ? preData.device_no : ' ', cellCls:'tdContent'},
            {html: '<p>车辆类型</p>', cellCls:'tdTitle'},
            {html:  preData.vehicle_type ? preData.vehicle_type : ' ', cellCls:'tdContent'},

            {html: '<p>品牌型号</p>', cellCls:'tdTitle'},
            {html:  preData.brand ? preData.brand : ' ', cellCls:'tdContent'},
            {html: '<p>车辆所有人住址</p>', cellCls:'tdTitle'},
            {html:  preData.address ? preData.address : ' ', cellCls:'tdContent'},
            {html: '<p>使用性质</p>', cellCls:'tdTitle'},
            {html:  preData.usage ? preData.usage : ' ', cellCls:'tdContent'},

            {html: '<p>注册日期</p>', cellCls:'tdTitle'},
            {html:  preData.registration_date ? preData.registration_date : ' ', cellCls:'tdContent'},
            {html: '<p>车辆识别代码</p>', cellCls:'tdTitle'},
            {html:  preData.vin ? preData.vin : ' ', cellCls:'tdContent'},
            {html: '<p>检验记录</p>', cellCls:'tdTitle'},
            {html:  preData.inspection_record ? preData.inspection_record : ' ', cellCls:'tdContent'},

            {html: '<p>车辆型号</p>', cellCls:'tdTitle'},
            {html:  preData.model ? preData.model : ' ', cellCls:'tdContent'},
            {html: '<p>发证日期</p>', cellCls:'tdTitle'},
            {html:  preData.issue_date ? preData.issue_date : ' ', cellCls:'tdContent'},
            {html: '<p>发动机排量</p>', cellCls:'tdTitle'},
            {html:  preData.displacement ? preData.displacement : ' ', cellCls:'tdContent'},

            {html: '<p>发动机功率</p>', cellCls:'tdTitle'},
            {html:  preData.horsepower ? preData.horsepower : ' ', cellCls:'tdContent',cellCls:'tdContent'},
            {html: '<p>车牌号</p>', cellCls:'tdTitle'},
            {html:  preData.plate_no ? preData.plate_no : ' ', cellCls:'tdContent'},
            {html: '<p>制造年月</p>', cellCls:'tdTitle'},
            {html:  preData.manufacture_date ? preData.manufacture_date : ' ', cellCls:'tdContent'},

            {html: '<p>发动机号码</p>', cellCls:'tdTitle'},
            {html:  preData.engine_no ? preData.engine_no : ' ', cellCls:'tdContent'},
            {html: '<p>发动机型号</p>', cellCls:'tdTitle'},
            {html:  preData.engine_model ? preData.engine_model : ' ', cellCls:'tdContent'}
        ]
    });

    var driverTable = new Ext.Panel({
        title: '司机信息',
        layout:'table',
        defaults: {
            // 对每一个子面板都有效applied to each contained panel
            bodyStyle:'padding: 10px 2px; line-height: 15px; border: none'
        },
        layoutConfig: {
            // 这里指定总列数The total column count must be specified here
            columns: 6
        },
        items: [
            {html: '<p>司机姓名</p>', cellCls:'tdTitle'},
            {html:  preData.driver ? preData.driver : ' ', cellCls:'tdContent'},
            {html: '<p>司机身份证号</p>', cellCls:'tdTitle'},
            {html:  preData.driver_id_no ? preData.driver_id_no : ' ', cellCls:'tdContent'},
            {html: '<p>司机驾驶证号</p>', cellCls:'tdTitle'},
            {html:  preData.driver_licene_no ? preData.driver_licene_no : ' ', cellCls:'tdContent'},

            {html: '<p>司机身份证有效期</p>', cellCls:'tdTitle'},
            {html:  preData.driver_id_expire ? preData.driver_id_expire : ' ', cellCls:'tdContent'},
            {html: '<p>司机住址</p>', cellCls:'tdTitle'},
            {html:  preData.driver_address ? preData.driver_address : ' ', cellCls:'tdContent'},
            {html: '<p>司机驾驶证有效起始日</p>', cellCls:'tdTitle'},
            {html:  preData.driver_licene_valid_from ? preData.driver_licene_valid_from : ' ', cellCls:'tdContent'},

            {html: '<p>司机驾驶证初次领证日</p>', cellCls:'tdTitle'},
            {html:  preData.driver_licene_issued_date ? preData.driver_licene_issued_date : ' ', cellCls:'tdContent'},
            {html: '<p>司机驾驶证准驾车型</p>', cellCls:'tdTitle'},
            {html:  preData.driver_driving_type ? preData.driver_driving_type : ' ', cellCls:'tdContent'},
            {html: '<p>司机驾驶证有效期限</p>', cellCls:'tdTitle'},
            {html:  preData.driver_licene_valid_expire ? preData.driver_licene_valid_expire : ' ', cellCls:'tdContent'}
        ]
    });

    var EI_view_window = new Ext.Window({
        title : '添加车辆档案信息',
        closeAction:'hide',
        items :[
            carTable, driverTable
        ]
    });

    EI_view_window.show();

}
