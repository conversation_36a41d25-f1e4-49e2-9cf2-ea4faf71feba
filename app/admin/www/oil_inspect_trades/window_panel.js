/**
 * 获取日期函数
 * @param $flag 1 为上月初，2为上月最后一天
 * @returns {String}
 */
function GetDateStr($flag)
{
	var dd = new Date();
	var y = dd.getFullYear();
	var m = dd.getMonth();//获取当前月份的日期
	if(m.toString().length==1){
		m = '0'+m;
	}
	var d = '';
	if($flag == 1)
		d = "01";
	else
	{
		var new_date = new Date(y,m,1);//取当年当月中的第一天
		d = (new Date(new_date.getTime()-1000*60*60*24)).getDate();
		if(d.toString().length==1){
			d = '0'+d;
		}
	}

	return y+"-"+m+"-"+d;
}

/**************************** 返利计算 **************************/
function showCalWin()
{
    Ext.MessageBox.confirm('提示', '确定要进行返利计算吗？', function showResult(btn) {
            if (btn == 'yes') {
                var myMask = new Ext.LoadMask(Ext.getBody(),{msg:"正在处理中..."});
                myMask.show();
                var params = top_panel.getForm().getValues();
                params.createtimeGe = params.createtimeGe ? params.createtimeGe + ' 00:00:00': '';
                params.createtimeLe = params.createtimeLe ? params.createtimeLe + ' 23:59:59': '';

                params.tradetimeGe = params.tradetimeGe ? params.tradetimeGe + ' 00:00:00': '';
                params.tradetimeLe = params.tradetimeLe ? params.tradetimeLe + ' 23:59:59': '';
                Ext.Ajax.request({
                    url: '../inside.php?t=json&m='+controlName+'&f=fanliCalculateNew',
                    params:params,
                    method: 'POST',
                    success: function (resp, opt) {
                        var result = Ext.decode(resp.responseText);
                        if(result.code == 0){
                            store_main.removeAll();
                            store_main.load();
                        }
                        myMask.hide();
                        Ext.Msg.alert('提示',result.msg);
                    },
                    failure: function () {
                        //alert('2222')
                    }
                })
            }
        }
    );
}

/**************************** 新版返利计算 **************************/
function showNewCalWin()
{
    Ext.MessageBox.confirm('提示', '确定要进行返利计算吗？', function showResult(btn) {
            if (btn == 'yes') {
                var myMask = new Ext.LoadMask(Ext.getBody(),{msg:"正在处理中..."});
                myMask.show();
                var params = top_panel.getForm().getValues();
                params.createtimeGe = params.createtimeGe ? params.createtimeGe + ' 00:00:00': '';
                params.createtimeLe = params.createtimeLe ? params.createtimeLe + ' 23:59:59': '';

                params.tradetimeGe = params.tradetimeGe ? params.tradetimeGe + ' 00:00:00': '';
                params.tradetimeLe = params.tradetimeLe ? params.tradetimeLe + ' 23:59:59': '';
                Ext.Ajax.request({
                    url: '../inside.php?t=json&m=oil_fanli_rule&f=ruleCalculate',
                    params:params,
                    method: 'POST',
                    success: function (resp, opt) {
                        var result = Ext.decode(resp.responseText);
                        if(result.code == 0){
                            store_main.removeAll();
                            store_main.load();
                        }
                        myMask.hide();
                        Ext.Msg.alert('提示',result.msg);
                    },
                    failure: function () {
                        //alert('2222')
                    }
                })
            }
        }
    );
}
/**************************** 编辑 **************************/
var smartwin1;//窗体
var add_win;//添加窗口


//主容器
Ext.QuickTips.init();

var panel_add = new Ext.FormPanel({
    region: "center",
    hideLabels: true,
    fileUpload: true,
    bodyStyle: 'padding: 10px',
    buttonAlign: 'center',
    width: 100,
    height: 50,
    minWidth: 200,
    minHeight: 100,
    trackResetOnLoad: true,
    labelWidth: 70,
    defaultType: 'textfield',
    border: false,
    items: [
        {
            xtype: 'compositefield',
            items: [
                {xtype: 'displayfield', value: '油品：', style:'text-align: right'},
                {
                    xtype : 'textfield',
                    id    : 'edit_oil_name',
                    name    : 'oil_name',
                    width : 150,
                    form_column: 'a.oil_name',
                    form_column_operator: 'like',
                },
            ]
        },
        {
            xtype: 'compositefield',
            style: 'padding-top: 10px;',
            items: [
                {xtype: 'displayfield', value: '地点：', style:'text-align: right'},
                {
                    xtype : 'textfield',
                    id    : 'edit_trade_place',
                    name    : 'trade_place',
                    width : 150,
                    form_column: 'a.trade_place',
                    form_column_operator: 'like',
                },
            ]
        },
        {
            xtype: 'compositefield',
            style: 'padding-top: 10px;',
            items: [
                {xtype: 'displayfield', value: '持卡人：', style:'text-align: right'},
                {
                    xtype : 'displayfield',
                    id    : 'card_owner',
                    width : 150,
                },
            ]
        },
        {
            xtype: 'compositefield',
            style: 'padding-top: 10px;',
            items: [
                {xtype: 'displayfield', value: '司机姓名：', style:'text-align: right'},
                {
                    xtype : 'displayfield',
                    id    : 'qz_drivername',
                    width : 150,
                },
            ]
        },
        {
            xtype: 'compositefield',
            style: 'padding-top: 10px;',
            items: [
                {xtype: 'displayfield', value: '司机手机号码：', style:'text-align: right'},
                {
                    xtype : 'displayfield',
                    id    : 'qz_drivertel',
                    width : 150
                },
            ]
        },
    ],
    buttons: [
        {
            text: '保存',
            handler: function () {
                formSubmit();
            }
        },{
            text: '取消',
            handler: function () {
                smartwin1.hide();
            }
        }],

})

//弹出窗体
if (!smartwin1) {//主窗体
    smartwin1 = new Ext.Window({
        layout:"border",
        width:300,
        height:270,
        title:'信息编辑',
        closeAction:'hide',
        modal:true,
        plain: true,
        items:[panel_add],
    });
}

//编辑
function showEdit(){
    var sm   = grid_service.getSelectionModel();
    var data = sm.getSelections();
    data[0] ? add_update_id = data[0].get("id") : '';

    Ext.Ajax.request({
        url:'/inside.php?t=json&m=oil_card_vice_trades&f=search',
        async: false,
        params:{id:add_update_id},
        success: function (resp,opts) {
            if (Ext.decode(resp.responseText).data.data.length > 0)
            {
                var record = Ext.decode(resp.responseText).data.data[0];

                panel_add.getForm().setValues([
                    {id: 'oil_name', value: record.oil_name},
                    {id: 'trade_place', value: record.trade_place},
                    {id: 'card_owner', value: record.card_owner},
                    {id: 'qz_drivername', value: record.qz_drivername},
                    {id: 'qz_drivertel', value: record.qz_drivertel},
                ]);
            }
        },
        failure:function (resp,opts) {
            console.log('``````````');
            Ext.Msg.alert('提示', Ext.decode(resp.responseText).msg);
        }
    })

   //  //修改
   // var num = store_main.getCount();//获取总个数
   //  for (var i = 0; i < num; i++) {
   //      var record = store_main.getAt(i);
   //      if (record.get('id') == add_update_id) {
   //          panel_add.getForm().setValues([
   //              {id: 'oil_name', value: record.get('oil_name')},
   //              {id: 'trade_place', value: record.get('trade_place')},
   //          ]);
   //      }
   //  }
    smartwin1.show();
}
function formSubmit(){
    var _form = panel_add.getForm();
    if (add_update_id) {
        _form.submit({
            url: '../inside.php?t=json&m=oil_card_vice_trades&f=update',
            params: {
                id: add_update_id,
                oil_name: Ext.getCmp('edit_oil_name').getValue(),
                trade_place: Ext.getCmp('edit_trade_place').getValue()
            },
            waitMsg: 'Saving Data...',
            success: function (form, action) {
                Ext.MessageBox.alert("系统提示!", action.result.msg);
                smartwin1.hide();
                store_main.load();

            },
            failure: function (form, action) {
                Ext.MessageBox.alert("消息!", action.result.msg);
            }
        });
    }
}

//释放额度
function quotaRelease() {
    var form = new Ext.form.FormPanel({
        baseCls: 'x-plain',labelAlign: 'right', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
        items: [
            new Ext.form.ComboBox({
                fieldLabel:'机构',
                width: 240,
                hiddenName: 'orgcode',
                allowBlank: false,
                blankText: '不能为空',
                triggerAction: 'all',
                forceSelection: true,
                mode: 'remote',
                queryParam:'keyword',
                minChars:2,
                displayField: 'org_name',//显示的值
                valueField: 'orgcode',//后台接收的key
                store: getOilOrg,
                emptyText: '请选择..',
                enableKeyEvents: true
            }),
            {//第3行
                xtype: 'compositefield',
                style: 'padding-top:5px;padding-left:6px;',
                hideLabel: true,
                items: [
                    {xtype: 'displayfield', value: '创建时间：'},
                    {
                        xtype: "datefield",
                        name: 'createtimeGe',
                        format: 'Y-m-d',
                        width: 100
                    },
                    {xtype: 'displayfield', value: '-'},
                    {
                        xtype: "datefield",
                        name: 'createtimeLe',
                        format: 'Y-m-d',
                        width: 100
                    },
                ]
            }
        ]
    });
    var fanliBoot = new Ext.Window({
        title: '释放额度',
        width: 400,
        height: 135,
        minWidth: 300,
        minHeight: 100,
        closeAction: 'destroy',
        modal: true,
        layout: 'fit',
        plain: true,
        bodyStyle: 'padding:5px;',
        buttonAlign: 'center',
        items: form,
        buttons: [{
            text: '确定',
            handler: function () {
                if (form.form.isValid()) {
                    form.getForm().submit({
                        url: getUrl('oil_invoice_quota_release','quotaRelease'),
                        success: function (form, action) {
                            var r = Ext.decode(action.response.responseText).msg;
                            Ext.MessageBox.alert('提示', r);
                            fanliBoot.destroy();
                            store_main.removeAll();
                            store_main.load();
                        },
                        failure: function (form, action) {
                            var r = Ext.decode(action.response.responseText).msg;
                            Ext.MessageBox.alert('提示', r);
                        }
                    })
                }else {
                    Ext.MessageBox.alert("提示", '输入有误，请检查');
                }
            }
        }, {
            text: '关闭',
            handler: function () {
                fanliBoot.destroy();
            }
        }]
    });
    fanliBoot.show();
}

//撤销释放额度
function revokeQuotaRelease() {
    Ext.MessageBox.confirm('提示', '确认撤销释放额度吗？', function showResult(btn){
        if (btn === 'yes'){
            Ext.Ajax.request({
                url:getUrl('oil_invoice_quota_release','revokeQuotaRelease'),
                method:'post',
                params:{},
                success: function sFn(response,options){
                    Ext.Msg.alert('系统提示', Ext.decode(response.responseText).msg);
                },
                failure: function (response, options) {
                    alert('222');
                }
            });
        }
    })
}
//供应商标记
function showMarkSupplier(){
    var sm   = grid_service.getSelectionModel();
    var data = sm.getSelections();
    var ids = '';
    for (var i = 0; i < data.length; i++) {
        (ids) ? ids = ids + ',' : '';
        ids += data[i].data.id;
    }
    var station_code = Ext.getCmp('s_station_code').getValue();
    //获取标记供应商
    getMarkSupplier = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: '../inside.php?t=json&m=oil_card_vice_trades&f=getMarkSuppliers',
            method: "POST",
        }),
        // autoLoad: true,
        reader: new Ext.data.JsonReader({
            totalProperty: '',
            root: 'data'
        }, [ 'supplier_name','supplier_id'])
    });
    var mark_supplier_win_panel = new Ext.FormPanel({
        region: "center",
        hideLabels: true,
        fileUpload: true,
        bodyStyle: 'padding: 10px',
        buttonAlign: 'center',
        width: 400,
        height: 250,
        trackResetOnLoad: true,
        defaultType: 'textfield',
        border: false,
        items: [
            {
                xtype: 'compositefield',
                items: [
                    {
                        xtype : 'displayfield',
                        id    : 'mark_trade_place',
                        name    : 'trade_place',
                        width : 200,
                        style: 'font-size:24px;color:#000000;',
                    },
                ]
            },
            {
                xtype: 'compositefield',
                items: [
                    {xtype: 'displayfield', value: '归属供应商：',width: 100},
                    new Ext.form.ComboBox({
                        width: 200,
                        id: 's_mark_supplier',
                        hiddenName: 'mark_supplier',
                        mode: 'local',
                        value: '',
                        buffer: 200,
                        triggerAction: 'all',
                        forceSelection: true,
                        displayField: 'supplier_name',//显示的值
                        valueField: 'supplier_id',//后台接收的key
                        store: getMarkSupplier,
                        emptyText: '请选择...',
                        enableKeyEvents: true,
                        listeners: {
                            'focus': function () {
                                getMarkSupplier.load({params:{ids:ids,station_code:station_code,type:'create'}})
                            },
                            'beforequery': function (e) {
                                var combo = e.combo;
                                if (!e.forceAll) {
                                    var input = e.query;
                                    // 检索的正则
                                    var regExp = new RegExp(".*" + input + ".*");
                                    // 执行检索
                                    combo.store.filterBy(function (record) {
                                        // 得到每个record的项目名称值
                                        var text = record.get(combo.displayField);
                                        return regExp.test(text);
                                    });
                                    combo.expand();
                                    return false;
                                }
                            }
                        }
                    }),
                ]
            },
            {
                xtype: 'compositefield',
                items: [
                    {
                        xtype : 'displayfield',
                        id    : 'mark_select_num',
                        width : 150,
                        style: 'font-size:12px;color:#ff0000;',
                    },
                ]
            },
        ],
    })
    var mark_supplier_win = new Ext.Window({
        layout:"border",
        width:400,
        height:250,
        title:'供应商标记',
        closeAction:'destroy',
        modal:true,
        plain: true,
        items:[mark_supplier_win_panel],
        buttons: [
            {
                text: '保存',
                handler: function () {
                    markSupplierFormSubmit(mark_supplier_win);
                }
            },{
                text: '取消',
                handler: function () {
                    mark_supplier_win.destroy();
                }
            }],
    });
    var mark_trade_place = Ext.getCmp('s_station_code').getRawValue();
    mark_supplier_win_panel.getForm().setValues([
        {id: 'mark_trade_place', value: mark_trade_place},
        {id: 'mark_select_num', value: '共选中'+data.length+'条消费'},
    ]);
    mark_supplier_win.show();
}
//标记供应商提交
function markSupplierFormSubmit(mark_supplier_win) {
    var sm   = grid_service.getSelectionModel();
    var data = sm.getSelections();
    var ids = '';
    for (var i = 0; i < data.length; i++) {
        (ids) ? ids = ids + ',' : '';
        ids += data[i].data.id;
    }
    var station_code = Ext.getCmp('s_station_code').getValue();
    var supplier_id =Ext.getCmp('s_mark_supplier').getValue();
    var supplier_name =Ext.getCmp('s_mark_supplier').getRawValue();
    if(supplier_id){
        Ext.Ajax.request({
            url: '../inside.php?t=json&m=oil_card_vice_trades&f=markTrade',
            waitMsg: 'Saving Data...',
            params: {ids: ids,supplier_id:supplier_id,supplier_name:supplier_name,'station_code':station_code},
            success: function (resp) {
                var result = Ext.decode(resp.responseText);
                mark_supplier_win.destroy()
                Ext.MessageBox.alert("消息!", result.msg);
                store_main.removeAll();
                store_main.load();
            },
            failure: function (resp) {
                mark_supplier_win.destroy()
                Ext.Msg.alert('系统提示', "系统错误");
            },
        });
    }else{
        Ext.Msg.alert('系统提示', "请选择归属供应商");
    }
}

//修改供应商标记
function updateMarkSupplier(){
    var sm   = grid_service.getSelectionModel();
    var data = sm.getSelections();
    var ids = data[0].data.id;
    var station_code = data[0].data.station_code;
    //获取标记供应商
    getUpdateMarkSupplier = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: '../inside.php?t=json&m=oil_card_vice_trades&f=getMarkSuppliers',
            method: "POST",
        }),
        // autoLoad: true,
        reader: new Ext.data.JsonReader({
            totalProperty: '',
            root: 'data'
        }, [ 'supplier_name','supplier_id'])
    });
    var update_mark_supplier_win_panel = new Ext.FormPanel({
        region: "center",
        hideLabels: true,
        fileUpload: true,
        bodyStyle: 'padding: 10px',
        buttonAlign: 'center',
        width: 400,
        height: 350,
        trackResetOnLoad: true,
        defaultType: 'textfield',
        border: false,
        items: [
            {
                xtype: 'compositefield',
                items: [
                    {
                        xtype : 'displayfield',
                        id    : 'mark_trade_place',
                        name    : 'trade_place',
                        width : 200,
                        style: 'font-size:24px;color:#000000;',
                    },
                ]
            },
            {
                xtype: 'compositefield',
                style:'padding-top:5px;',
                items: [
                    {xtype: 'displayfield', value: '原归属供应商：',width: 100},
                    {
                        xtype : 'displayfield',
                        id    : 'old_mark_supplier_name',
                        width : 200,
                    },
                    {
                        xtype : 'displayfield',
                        id    : 'old_mark_supplier_id',
                        hidden:true
                    },

                ]
            },
            {
                xtype: 'compositefield',
                style:'padding-top:5px;',
                items: [
                    {xtype: 'displayfield', value: '新归属供应商：',width: 100},
                    new Ext.form.ComboBox({
                        width: 200,
                        id: 's_mark_supplier',
                        hiddenName: 'mark_supplier',
                        mode: 'local',
                        value: '',
                        buffer: 200,
                        triggerAction: 'all',
                        forceSelection: true,
                        displayField: 'supplier_name',//显示的值
                        valueField: 'supplier_id',//后台接收的key
                        store: getUpdateMarkSupplier,
                        emptyText: '请选择...',
                        enableKeyEvents: true,
                        listeners: {
                            'focus': function () {
                                getUpdateMarkSupplier.load({params:{ids:ids,station_code:station_code,type:'update'}})
                            },
                            'beforequery': function (e) {
                                var combo = e.combo;
                                if (!e.forceAll) {
                                    var input = e.query;
                                    // 检索的正则
                                    var regExp = new RegExp(".*" + input + ".*");
                                    // 执行检索
                                    combo.store.filterBy(function (record) {
                                        // 得到每个record的项目名称值
                                        var text = record.get(combo.displayField);
                                        return regExp.test(text);
                                    });
                                    combo.expand();
                                    return false;
                                }
                            }
                        }
                    }),
                ]
            },
            {
                xtype: 'compositefield',
                style:'padding-top:5px;',
                items: [
                    {
                        xtype: 'displayfield',
                        value: '修改原因：'
                    },
                    {
                        xtype: 'textarea',
                        id: 's_update_reason',
                        name: 'update_reason',
                        width: 250,
                        height:60
                    },
                ]
            },
        ],
    })
    var update_mark_supplier_win = new Ext.Window({
        layout:"border",
        width:400,
        height:250,
        title:'修改归属供应商',
        closeAction:'destroy',
        modal:true,
        plain: true,
        items:[update_mark_supplier_win_panel],
        buttons: [
            {
                text: '保存',
                handler: function () {
                    updateMarkSupplierFormSubmit(update_mark_supplier_win);
                }
            },{
                text: '取消',
                handler: function () {
                    update_mark_supplier_win.destroy();
                }
            }],
    });
    var mark_trade_place = data[0].data.trade_place;
    var old_mark_supplier_name = data[0].data.mark_supplier_name;
    var old_mark_supplier_id = data[0].data.mark_supplier_id;
    update_mark_supplier_win_panel.getForm().setValues([
        {id: 'mark_trade_place', value: mark_trade_place},
        {id: 'old_mark_supplier_name', value: old_mark_supplier_name},
        {id: 'old_mark_supplier_id', value: old_mark_supplier_id},
    ]);
    update_mark_supplier_win.show();
}
//修改标记供应商提交
function updateMarkSupplierFormSubmit(update_mark_supplier_win) {
    var sm   = grid_service.getSelectionModel();
    var data = sm.getSelections();
    var id = data[0].data.id;
    var supplier_id =Ext.getCmp('s_mark_supplier').getValue();
    var supplier_name =Ext.getCmp('s_mark_supplier').getRawValue();
    var update_reason =Ext.getCmp('s_update_reason').getValue();
    var station_code = Ext.getCmp('s_station_code').getValue();
    var old_mark_supplier_id = Ext.getCmp('old_mark_supplier_id').getValue();
    if(supplier_id){
        if(update_reason == ''){
            Ext.Msg.alert('系统提示', "修改原因不能为空");
            return
        }
        if(update_reason.length <10){
            Ext.MessageBox.alert('提示', '修改原因必须大于10个');
            return
        }
        if(supplier_id == old_mark_supplier_id){
            Ext.Msg.alert('系统提示', "供应商相同，无需更改");
            return
        }
        Ext.Ajax.request({
            url: '../inside.php?t=json&m=oil_card_vice_trades&f=updateMarkSupplier',
            waitMsg: 'Saving Data...',
            params: {id: id,supplier_id:supplier_id,supplier_name:supplier_name,update_reason:update_reason,station_code:station_code},
            success: function (resp) {
                var result = Ext.decode(resp.responseText);
                Ext.MessageBox.alert("消息!", result.msg);
                update_mark_supplier_win.destroy()
                store_main.removeAll();
                store_main.load();
            },
            failure: function (resp) {
                mark_supplier_win.destroy()
                Ext.Msg.alert('系统提示', "系统错误");
            },
        });
    }else{
        Ext.Msg.alert('系统提示', "请选择归属供应商");
    }
}

//销售运营商
function updateOrgOperator() {
    var sm   = grid_service.getSelectionModel();
    var data = sm.getSelections();
    if ( data.length != 1 ){
        Ext.Msg.alert('系统提示', "请选择单条交易记录");
        return false;
    }
    console.log(data[0].data);
    var top_orgcode = data[0].data.top_orgcode;
    var tradeId = data[0].data.id;

    getSaleOperator = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: '../inside.php?t=json&m=oil_org_change_operator_log&f=getOrgOperator',
            method: "POST",
        }),
        // autoLoad: true,
        reader: new Ext.data.JsonReader({
            totalProperty: '',
            root: 'data'
        }, [ 'id','name'])
    });
    var operator_win_panel = new Ext.FormPanel({
        region: "center",
        hideLabels: true,
        fileUpload: true,
        bodyStyle: 'padding: 10px',
        buttonAlign: 'center',
        width: 400,
        height: 250,
        trackResetOnLoad: true,
        defaultType: 'textfield',
        border: false,
        items: [
            {
                xtype: 'compositefield',
                items: [
                    {xtype: 'displayfield', value: '销售运营商：',width: 100},
                    new Ext.form.ComboBox({
                        width: 250,
                        id: 's_new_operator_id',
                        hiddenName: 'new_operator_id',
                        mode: 'local',
                        value: '',
                        buffer: 200,
                        triggerAction: 'all',
                        forceSelection: true,
                        displayField: 'name',//显示的值
                        valueField: 'id',//后台接收的key
                        store: getSaleOperator,
                        emptyText: '请选择...',
                        enableKeyEvents: true,
                        listeners: {
                            'focus': function () {
                                getSaleOperator.load({params:{orgcode:top_orgcode}})
                            },
                            'beforequery': function (e) {
                                var combo = e.combo;
                                if (!e.forceAll) {
                                    var input = e.query;
                                    // 检索的正则
                                    var regExp = new RegExp(".*" + input + ".*");
                                    // 执行检索
                                    combo.store.filterBy(function (record) {
                                        // 得到每个record的项目名称值
                                        var text = record.get(combo.displayField);
                                        return regExp.test(text);
                                    });
                                    combo.expand();
                                    return false;
                                }
                            }
                        }
                    }),
                ]
            }
        ],
    })
    var operator_win = new Ext.Window({
        layout:"border",
        width:480,
        height:150,
        title:'修改销售运营商',
        closeAction:'destroy',
        modal:true,
        plain: true,
        items:[operator_win_panel],
        buttons: [
            {
                text: '保存',
                handler: function () {
                    //markSupplierFormSubmit(mark_supplier_win);
                    var operator_id = Ext.getCmp('s_new_operator_id').getValue();

                    Ext.Ajax.request({
                        url: '../inside.php?t=json&m='+controlName+'&f=setOrgOperatorId',
                        params:{id:tradeId,operator_id:operator_id},
                        method: 'POST',
                        success: function (resp, opt) {
                            var result = Ext.decode(resp.responseText);
                            if(result.code == 0){
                                store_main.removeAll();
                                store_main.load();
                            }
                            operator_win.destroy();
                            Ext.Msg.alert('提示',result.msg);
                        },
                        failure: function () {
                            //alert('2222')
                        }
                    })

                }
            },{
                text: '取消',
                handler: function () {
                    operator_win.destroy();
                }
            }],
    });

    Ext.getCmp("s_new_operator_id").setRawValue(data[0].data.oil_operator_name)
    Ext.getCmp("s_new_operator_id").setValue(data[0].data.org_operator_id)

    operator_win.show();

    Ext.getCmp("s_new_operator_id").setRawValue(data[0].data.oil_operator_name)
}
