var bottomTitle = '';
var detail_grid = new Ext.grid.GridPanel({
    region: 'center',
    loadMask: true,
    cm: new Ext.grid.ColumnModel([
        new Ext.grid.RowNumberer({header: '序号', width: 35}),
        {header: '付款单号', dataIndex: 'payment_no', width: 140},
        {header: '渠道（供应商）', dataIndex: 'supplier_name', width: 280},
        {header: '核算主体', dataIndex: 'service_area', width: 200},
        {header: '核算主体ID', dataIndex: 'settle_obj_id', width: 90},
        {header: '付款金额（元）', dataIndex: 'pay_amount', width: 180},
        {header: '明细ID', dataIndex: 'id', width: 140},
        {header: '创建时间', dataIndex: 'createtime', width: 120},
        {header: '备注', dataIndex: 'remark', width: 280},
    ]),
    store: detail_store,
    //分页
    bbar: new Ext.PagingToolbar({
        plugins: new Ext.ux.plugin.PagingToolbarResizer,
        pageSize: pagesize,
        displayInfo: true,
        displayMsg: '当前记录数: {0} - {1} 总记录数: {2}',
        emptyMsg: '没有符合条件的记录',
        store: detail_store
    }),
    tbar: []
});

//底部表格
var bottom_panel = new Ext.TabPanel({
    region:"south",
    activeTab:0,
    frame:true,
    height:210,
    items:
        [
            {
                xtype:'panel',
                title: '付款明细',
                layout:'border',
                items:[
                    detail_grid,
                ]
            },
        ],
    listeners:{
        tabchange:function(tp,p){
            bottomTitle = p.title;
            if(crow){
                loadBottomData(p.title);
            }

        }
    }
});

//加载底部数据
function loadBottomData(flag) {
    if (selectNum == 1) {
        if(flag === '付款明细'){
            console.log('切换======');
            console.log(crow);
            detail_store.removeAll();
            if (crow.id > 0) {
                detail_store.baseParams = {'payment_id':crow.id,'start':0,'limit':500};
                detail_store.load();
            }
        }
    }
}
