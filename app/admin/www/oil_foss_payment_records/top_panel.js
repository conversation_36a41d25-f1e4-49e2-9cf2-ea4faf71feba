function GetDate($flag) {
	var dd = new Date();
	var y = dd.getFullYear();
	var m = dd.getMonth() + 1;//获取当前月份的日期
	var d = '';
	if ($flag == 1) {
		d = "01";
	} else if ($flag == 2) {
		d = dd.getDate();
	} else if ($flag == 3) {
		var now = new Date();
		var date = new Date(now.getTime() - 5 * 24 * 3600 * 1000);
		y = date.getFullYear();
		m = date.getMonth() + 1;
		d = date.getDate();
	}
	if (m.toString().length == 1) {
		m = '0' + m;
	}
	if (d.toString().length == 1) {
		d = '0' + d;
	}

	return y + "-" + m + "-" + d;
}

//获取扣款账户列表
var  statusList = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: '../inside.php?t=json&m=oil_foss_payment_records&f=getStatusList', method: "POST"}),
    autoLoad: true,
    reader: new Ext.data.JsonReader({
        totalProperty: '',
        root: 'data'
    }, ['name', 'value'])
});

var top_panel = new Ext.form.FormPanel({
	region : 'north',
	hideLabels : true,
	bodyStyle : 'padding: 10px',
	height : 80,
	items  :
	[
		{
			xtype : 'compositefield',
			items :
			[
				{
					xtype: 'displayfield',
					value: '单号：'
				},
				{
						xtype: 'textfield',
						id: 's_no',
						name: 'no',
						width: 140
				},
				{
						xtype: 'button',
						text: '多选',
						listeners: {
								'click': function () {
										show_search_win('请输入付款单号，以回车换行：', 's_no', function () {
												main_store.removeAll();
												main_store.load();
										}, ',');
								}
						}
				},
				{
					xtype: 'displayfield',
					value: '创建时间：'
				},
				{
					xtype: "datefield",
					id: "startTime",
					name: 'createTimeGe',
					format: 'Y-m-d',
					value:GetDate(3),
					width: 100,
				},
				{xtype: 'displayfield', value: '-'},
				{
					xtype: "datefield",
					id: "endTime",
					name: 'createTimeLe',
					format: 'Y-m-d',
					value:GetDate(2),
					width: 100
				},
				{
					xtype: 'displayfield',
					value: '支付时间：'
				},
				{
					xtype: "datefield",
					id: "startPayTime",
					name: 'pay_timeGe',
					format: 'Y-m-d',
					//value:GetDate(3),
					width: 100,
				},
				{xtype: 'displayfield', value: '-'},
				{
					xtype: "datefield",
					id: "endPayTime",
					name: 'pay_timeLe',
					format: 'Y-m-d',
					//value:GetDate(2),
					width: 100
				},
				{
					xtype: 'displayfield',
					value: '采购渠道：'
				},
				new Ext.form.ComboBox({
					width: 222,
					id: 'channel',
					hiddenName: 'supplier_ids',
					triggerAction: 'all',
					forceSelection: true,
					mode: 'local',
					queryParam:'supplier_nameLike',
					minChars:2,
					displayField: 'supplier_name',//显示的值
					valueField: 'id',//后台接收的key
					store: getOilSupplier,
					emptyText: '请选择..',
					enableKeyEvents: true,
					listWidth:400,
					listeners: {
						'focus': function () {
							getOilSupplier.load();
						},
						'beforequery': function (e) {
							var combo = e.combo;
							if (!e.forceAll) {
								var input = e.query;
								// 检索的正则
								var regExp = new RegExp(".*" + input + ".*");
								// 执行检索
								combo.store.filterBy(function (record, id) {
									// 得到每个record的项目名称值
									var text = record.get(combo.displayField);
									return regExp.test(text);
								});
								combo.expand();
								return false;
							}
						}
					}
				}),
				{
					xtype: 'displayfield',
					value: '签约运营商：'
				},
				new Ext.form.ComboBox({
					width: 220,
					id: 'operator_id',
					hiddenName: "operator_id",
					blankText: '不能为空',
					mode: 'local',
					value: '',
					fieldLabel: '签约运营商',
					triggerAction: 'all',
					forceSelection: true,
					emptyText: '请选择..',
					displayField: 'company_name',
					valueField: 'id',
					store: b_operator
				}),
			]
		 },
		{
			xtype : 'compositefield',
			items :
				[
					{
						xtype: 'displayfield',
						value: '合作类型：',
					},
					{
						xtype: 'combo',
						width: 80,
						id   : 'cooperation_type',
						hiddenName : "cooperation_type",
						editable: true,
						emptyText : '请选择',
						mode: 'local',
						triggerAction:'all',
						displayField: 'name',
						valueField: 'value',
						store : new Ext.data.SimpleStore({
							fields: ['name', 'value'],
							data: [['平台', 10], ['站点', 20], ['主卡', 30], ['内部', 40]]
						})
					},
					{
						xtype: 'displayfield',
						value: '充值方式：',
					},
					{
						xtype: 'combo',
						width: 80,
						id   : 'recharge_type',
						hiddenName : "recharge_type",
						editable: true,
						emptyText : '请选择',
						mode: 'local',
						triggerAction:'all',
						displayField: 'name',
						valueField: 'value',
						store : new Ext.data.SimpleStore({
							fields: ['name', 'value'],
							data: [['油品线下', 1], ['油品线上', 2]]
						})
					},
					{
						xtype: 'displayfield',
						value: '状态：'
					},
					new Ext.form.MultiSelect({
						width: 80,
						editable: false,
						id: 's_status',
						hiddenName: 'status',
						store: statusList,
						emptyText: '请选择',
						blankText: '',
						mode: 'local',
						displayField: 'name',
						valueField: 'value',
						triggerAction: 'all',
						selectOnFocus: true,
					}),
					{
						xtype: 'displayfield',
						value: '创建人：',
						style: 'padding-left: 10px'
					},
					{
						xtype : 'textfield',
						id    : 'creator_name',
						name  : 'creator_name',
						width : 70
					},
					{
						xtype: 'displayfield',
						value: '内部公司交易：',
					},
					{
						xtype: 'combo',
						width: 100,
						id: "is_inner",
						hiddenName: 'is_inner',
						mode: 'local',
						emptyText: '请选择..',
						triggerAction: 'all',
						displayField: 'key',
						valueField: 'value',
						store: new Ext.data.SimpleStore({
							fields: ['key', 'value'],
							data: [['是', '1'], ['否', '2']]
						})
					},
					{
							xtype: 'displayfield',
							value: '运营对接人：',
							style: 'padding-left: 10px'
					},
					{
							xtype : 'textfield',
							id    : 'settlement_docker_like',
							name  : 'settlement_docker_like',
							width : 70
					},
					{
						xtype: 'displayfield',
						value: '交易流水号：',
						style: 'padding-left: 10px'
					},
					{
						xtype : 'textfield',
						id    : 'out_trans_id_like',
						name  : 'out_trans_id_like',
						width : 90
					},
					{
						xtype:'button',
						text:'查询',
						style : 'padding-left : 10px;',
						handler: function()
						{
							main_store.removeAll();//移除原来的数据
							main_store.load();//加载新搜索的数据
						}
					},
					{
						xtype: 'button',
						text: '重置',
						style: 'padding-left : 10px;',
						handler: function () {
							/*Ext.getCmp("no").setValue("");
              Ext.getCmp("channel").setValue("");
              Ext.getCmp("channel").setRawValue("");
              Ext.getCmp("status").setValue("");
              Ext.getCmp("status").setRawValue("");*/
							console.log('======');
							console.log(top_panel.getForm());
							top_panel.getForm().reset();
							main_store.removeAll();//移除原来的数据
							main_store.load();//加载新搜索的数据
						}
					},
				]
		}
	]
});
