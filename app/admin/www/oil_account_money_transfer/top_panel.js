//获取月初日期
/**
 * 获取日期函数
 * @param $flag 1 为月初，2为当前日期
 * @returns {String}
 */
function GetDateStr($flag) {
    var dd = new Date();
    var y = dd.getFullYear();
    var m = dd.getMonth() + 1;//获取当前月份的日期
    var h = dd.getHours();
    var i = dd.getMinutes();
    var s = dd.getSeconds();
    if (m.toString().length == 1) {
        m = '0' + m;
    }
    var d = '';
    if ($flag == 1)
        d = "01";
    else {
        //var new_date = new Date(y,m,1);//取当年当月中的第一天
        //d = (new Date(new_date.getTime()-1000*60*60*24)).getDate();
        //if(d.toString().length==1){
        //    d = '0'+d;
        d = dd.getDate();
        if (d.toString().length == 1) {
            d = '0' + d;
        }
    }

    return y + "-" + m + "-" + d + " " + h + ":" + i + ":" + s;
}

var top_panel = new Ext.FormPanel({
    region: 'north',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 95,
    items: [
        {//第一行
            xtype: 'compositefield',
            items: [
                {xtype: 'displayfield', value: '单号：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_no',
                    name: 'no',
                    width: 100,
                    listeners: {
                        'specialkey': function (field, e) {
                            if (e.getKey() == Ext.EventObject.ENTER) {//响应回车
                                store_service.removeAll();
                                store_service.load();
                                sm_service.clearSelections();
                            }
                        }
                    },
                },
                //机构要只展示一级、二级
                {
                    xtype: 'displayfield',
                    value: '转出机构：',
                },
                new Ext.form.ComboBox({
                    width: 240,
                    id:'s_oil_org',
                    hiddenName: 'from_orgcode',
                    triggerAction: 'all',
                    forceSelection: true,
                    mode: 'remote',
                    queryParam: 'keyword',
                    minChars: 2,
                    displayField: 'org_name',//显示的值
                    valueField: 'orgcode',//后台接收的key
                    store: getOilOrg,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                }),
                /*new Ext.form.ComboBox({
                    width: 240,
                    id: 's_oil_org',
                    hiddenName: 'from_orgcode',
                    mode: 'local',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'org_name',//显示的值
                    valueField: 'orgcode',//后台接收的key
                    store: getOilOrg,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        'focus': function () {
                            getOilOrg.load();
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                    }
                }),*/
                {
                    xtype: "checkbox",
                    id: "org_flag",
                    name: 'from_orgcode_flag',
                    checked: true,
                },
                {
                    xtype: 'displayfield',
                    value: '(包含下级)',
                },
                {
                    xtype: 'displayfield',
                    value: '转入机构：',
                },
                new Ext.form.ComboBox({
                    width: 240,
                    id:'into_orgcode',
                    hiddenName: 'into_orgcode',
                    triggerAction: 'all',
                    forceSelection: true,
                    mode: 'remote',
                    queryParam: 'keyword',
                    minChars: 2,
                    displayField: 'org_name',//显示的值
                    valueField: 'orgcode',//后台接收的key
                    store: getOilOrg,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                }),
                /*new Ext.form.ComboBox({
                    width: 240,
                    id: 'into_orgcode',
                    hiddenName: 'into_orgcode',
                    mode: 'local',
                    value: '',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'org_name',//显示的值
                    valueField: 'orgcode',//后台接收的key
                    store: getOilOrg,
                    emptyText: '请选择..',
                    enableKeyEvents: true,
                    listeners: {
                        'focus': function () {
                            getOilOrg.load();
                        },
                        'beforequery': function (e) {
                            var combo = e.combo;
                            if (!e.forceAll) {
                                var input = e.query;
                                // 检索的正则
                                var regExp = new RegExp(".*" + input + ".*");
                                // 执行检索
                                combo.store.filterBy(function (record, id) {
                                    // 得到每个record的项目名称值
                                    var text = record.get(combo.displayField);
                                    return regExp.test(text);
                                });
                                combo.expand();
                                return false;
                            }
                        },
                    }
                }),*/
                {
                    xtype: 'displayfield',
                    value: '申请时间：',
                },
                {
                    xtype: "datefield",
                    id: "s_start_time",
                    name: 'start_time',
                    format: 'Y-m-d',
                    width: 100,
                },
                {
                    xtype: 'displayfield',
                    value: '—'
                },
                {
                    xtype: "datefield",
                    id: "s_end_time",
                    name: 'end_time',
                    format: 'Y-m-d',
                    width: 100,
                },
            ]
        },
        {//第二行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '状态：',
                },
                {
                    xtype: 'combo',
                    hiddenName: 'status',
                    id: 's_status',
                    mode: 'local',
                    width: 100,
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'status_name',
                    valueField: 'status_id',
                    value: '',
                    store: new Ext.data.SimpleStore({
                        fields: ['status_id', 'status_name'],
                        data: [['0', '未审核'], ['1', '已审核'], ['-1', '驳回']]
                    }),
                },
                {
                    xtype: 'displayfield',
                    value: '数据来源：'
                },
                {
                    xtype: 'combo',
                    hiddenName: 'data_from',
                    id: 's_data_from',
                    mode: 'local',
                    width: 80,
                    emptyText: '请选择..',
                    triggerAction: 'all',
                    forceSelection: true,
                    displayField: 'data_source',
                    valueField: 'data_source_id',
                    value: '',
                    store: new Ext.data.SimpleStore({
                        fields: ['data_source_id', 'data_source'],
                        data: [['1', 'GSP'], ['2', 'WEB'], ['3', 'APP']]
                    }),
                },
                {
                    xtype: 'displayfield',
                    value: '是否为测试机构：',
                },
                {
                    xtype: 'combo',
                    width: 100,
                    hiddenName : "is_test",
                    id : "s_is_test",
                    editable: true,
                    emptyText : '全部',
                    mode: 'local',
                    triggerAction:'all',
                    displayField: 'name',
                    valueField: 'value',
                    store : new Ext.data.SimpleStore({
                        fields: ['name', 'value'],
                        data: [['全部', ''], ['否', '1'], ['是', '2']]
                    })
                },
                {
                    xtype: 'displayfield',
                    value: '转账类型：',
                },
                {
                    xtype: 'combo',
                    width: 100,
                    hiddenName : "no_type",
                    id : "no_type_top",
                    editable: true,
                    emptyText : '全部',
                    mode: 'local',
                    triggerAction:'all',
                    displayField: 'name',
                    valueField: 'value',
                    store : new Ext.data.SimpleStore({
                        fields: ['name', 'value'],
                        data: [['全部', ''], ['机构转账', 'ZZ'], ['油费划拨', 'HB']]
                    })
                },
                {xtype: 'displayfield', value: '付款账号：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_from_account_no',
                    name: 'from_account_no',
                    width: 100,
                },
            ]
        },{//第三行
            xtype: 'compositefield',
            items: [
                {xtype: 'displayfield', value: '收款账号：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_into_account_no',
                    name: 'into_account_no',
                    width: 100,
                },
                {xtype: 'displayfield', value: '申请人手机：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_from_phone',
                    name: 'from_phone',
                    width: 130,
                },
                {xtype: 'displayfield', value: '接收人手机：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 's_into_phone',
                    name: 'into_phone',
                    width: 130,
                },
                {xtype: 'displayfield', value: '申请人：', style: 'text-align: right'},
                {
                    xtype: 'textfield',
                    id: 'app_nameLk',
                    name: 'app_nameLk',
                    width: 130,
                },
                {
                    xtype: 'button',
                    text: '查询',
                    style: 'padding-left : 30px;',
                    handler: function () {
                        store_main.removeAll();
                        store_main.load();
                        (Ext.getCmp('btnDel')) ? Ext.getCmp('btnDel').disable() : '';//删除
                        (Ext.getCmp('btnEdit')) ? Ext.getCmp('btnEdit').disable() : '';//编辑
                    }
                },
                {
                    xtype: 'button',
                    text: '重置',
                    style: 'padding-left : 10px;',
                    handler: function () {
                        Ext.getCmp('s_no').setValue('');
                        Ext.getCmp('s_oil_org').setValue('');
                        Ext.getCmp('into_orgcode').setValue('');
                        Ext.getCmp('s_start_time').setValue(GetDateStr(1));
                        Ext.getCmp('s_end_time').setValue(GetDateStr(2));
                        Ext.getCmp('s_status').setValue('');
                        Ext.getCmp('s_data_from').setValue('');
                        Ext.getCmp('s_is_test').setValue('');
                        Ext.getCmp('app_nameLk').setValue('');
                        Ext.getCmp('s_into_phone').setValue('');
                        Ext.getCmp('s_from_phone').setValue('');
                        Ext.getCmp('s_from_account_no').setValue('');
                        Ext.getCmp('s_into_account_no').setValue('');
                        Ext.getCmp('org_flag').setValue(true);
                        store_main.removeAll();//移除原来的数据
                        store_main.load();//加载新搜索的数据
                        btnInit();
                    }
                },
            ]
        }
    ]
});

