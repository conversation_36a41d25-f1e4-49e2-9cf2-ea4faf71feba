var detail_grid = new Ext.grid.GridPanel({
    title: '受理日志',
    loadMask: true,
    store: detailStore,
    cm: new Ext.grid.ColumnModel([
        new Ext.grid.RowNumberer({header: '序号', width: 35}),
        {header: '受理时间',dataIndex: 'third_createtime', width: 150},
        {header: '受理状态', dataIndex: 'txt_status',align:'center', sortable: true, width: 150,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                var ss = record.data.status;
                if(ss == 3) {
                    return '<span style="color:green;">'+record.data.txt_status+'</span>';
                } else if(ss == 10) {
                    return '<span style="color:orangered;">'+record.data.txt_status+'</span>';
                } else {
                    return  '<span style="color:red;">'+record.data.txt_status+'</span>';
                }
        }},
        {header: '处理说明', dataIndex: 'content', sortable: true, width: 500},
        {header: '负责人', dataIndex: 'exclusive_custom_name', sortable: true, width: 100},
        {header: '已用时(分钟)', dataIndex: 'use_time', width: 100},
        {header: '更新人', dataIndex: 'operator', width: 90},
        {header: '回电客户', dataIndex: 'txt_callback', sortable: true, width: 120},
    ]),
    bbar: new Ext.PagingToolbar({
        pageSize: 100,
        store: detailStore,
        displayInfo: true,
        displayMsg: '显示第{0}条到{1}条记录,一共{2}条',
        emptyMsg: '没有记录'
    })
});
//底部表格
var bottom_panel = new Ext.TabPanel({
    region:"south",
    activeTab:0,
    frame:true,
    height:210,
    items:
        [
            detail_grid,
        ],
});

//点击列表，获取底部数据
function rowClick(thisGrid,rowIndex,e) {
    var record = thisGrid.getStore().getAt(rowIndex);
    detailStore.removeAll();
    detailStore.baseParams = {'issue_id':record.get('id'),'start':0,'limit':500};
    detailStore.load({callback:function () {
        for (var i = 0; i < detailStore.getCount(); i++) {
            var record = detailStore.getAt(i);
        }
    }});
}
