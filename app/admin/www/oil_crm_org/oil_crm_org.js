/**
 * 全局类
 */
function oiltest() {
    //列表表格
    this.getGridPanel = function () {
        var _sm =new Ext.grid.RowSelectionModel({
            singleSelect: true,
            listeners: {
                selectionchange: function(data) {
                    if (data.getCount()){
                        (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').enable() : '';//删除按钮
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').enable() : '';//修改按钮
                    }else{
                        (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').disable() : '';//删除按钮
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').disable() : '';//修改按钮
                    }
                }
            }
        });

        var grid_list = new Ext.grid.GridPanel({
            title	 :'CRM客户信息列表',
            region	 : 'center',
            loadMask : true,
            cm : new Ext.grid.ColumnModel([
            	                {header : '客户编号',dataIndex : 'accountnumber',width : 180},
            	                {header : '客户名',dataIndex : 'name',width : 180},
            	                {header : '机构编码',dataIndex : 'orgcode',width : 180},
            	                {header : '客户数据中心ID',dataIndex : 'cdc_id',width : 180},
            	                {header : '统一社会信用代码',dataIndex : 'companycode',width : 180},
            	                {header : '客户类型',dataIndex : '_new_accountgroup',width : 180},
            	                {header : '代理签约',dataIndex : '_new_whetherproxy',width : 180},
            	                {header : '客户属性',dataIndex : 'company_attribute',width : 180},
            	                {header : '签约销售',dataIndex : 'sign_aplowneridname',width : 180},
            	                {header : '现归属省区',dataIndex : 'new_department',width : 180},
            	                {header : '现归属大区',dataIndex : 'new_area',width : 180},
            	                {header : '油品支持人员',dataIndex : 'new_fossilsupporter',width : 180},
            	                {header : '产品线销售人员工号',dataIndex : 'aplownerid',width : 180},
            	                {header : '产品线销售人员',dataIndex : 'aplowneridname',width : 180},
            	                {header : '产品线销售邮箱',dataIndex : 'saleEmail',width : 180},
            	                {header : '费控系统预算部门编号',dataIndex : 'oil_centercode',width : 180},
            	                {header : '费控系统预算部门名称',dataIndex : 'oil_centername',width : 180},
            	                {header : '外部协销人员',dataIndex : 'external_cross_sale',width : 180},
            	                {header : '油品协销人员',dataIndex : 'oil_cross_sale',width : 180},
            	                {header : '油品协销人员工号',dataIndex : 'oil_cross_wordno',width : 180},
            	                {header : '油品协销邮箱',dataIndex : 'oil_cross_saleEmail',width : 180},
            	                {header : '油品协销费控系统预算部门编号',dataIndex : 'oil_cross_centercode',width : 180},
            	                {header : '油品协销费控系统预算部门名称',dataIndex : 'oil_cross_centername',width : 180},
            	                {header : '最后更新时间',dataIndex : 'lastupdatetime',width : 180},
            	                {header : '产品线',dataIndex : 'new_productline',width : 180},
            	                {header : '总条数',dataIndex : 'totalcount',width : 180},
            	            ]),
            store : main_store,
            sm :_sm,
            //分页
            bbar: new Ext.PagingToolbar({
                plugins: new Ext.ux.plugin.PagingToolbarResizer,
                pageSize: pagesize,
                displayInfo : true,
                displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
                emptyMsg : '没有符合条件的记录',
                store : main_store
            }),
            tbar : []
        });

        return grid_list;
    };

    //关闭窗口
    this.closeWin = function (id) {
        if (!id) return;
        var win = Ext.getCmp(id);
        win.destroy();
    };

    var _getWindow = function (config) {
        var width = config.width,
            height = config.height,
            title = config.title,
            id = config.id,
            button = config.button;

        button.push({
            text: '取消',
            id: 'button_cancer',
            handler: function () {
                var oilTest = new oiltest();
                oilTest.closeWin(id);
            }
        });
        var windows = new Ext.Window({
            layout: 'border',
            region: 'center',
            width: width,
            modal: true,//遮挡窗口后的内容
            id: id,
            height: height,
            title: title,
            closeAction: 'destroy',
            buttons: button
        });

        return windows;
    };

    //添加
    this.add = function () {
        var windows = _getWindow({
            title: '添加-oil_crm_org',
            id: 'add_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'add-form',
            items: addPanel.getPanel('add-form', 'add_win')
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };

    //编辑
    this.update = function () {
        var sm   = grid_list.getSelectionModel();
        var data = sm.getSelections();
        data[0] ? id = data[0].get("id") : '';

        var windows = _getWindow({
            title: '编辑-oil_crm_org',
            id: 'update_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'update-form',
            items: addPanel.getPanel('update-form', 'update_win',id)
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init(data[0]);
    };

    //删除
    this.delete = function () {
        Ext.MessageBox.confirm('删除', '删除之后该条纪录将不再显示，确定要删除？', function showResult(btn){
            if (btn == 'yes')
            {
                var sm   = grid_list.getSelectionModel();
                var data = sm.getSelections();
                var ids = data[0].get("id");
                Ext.Ajax.request({
                    url:getUrl(controlName,'remove'),
                    method:'post',
                    params:{ids:ids},
                    success: function sFn(response,options)
                    {
                        main_store.removeAll();
                        main_store.load();
                        Ext.Msg.alert('系统提示', '删除成功');
                    }
                });
            }
        });
    };
    //导出
    this.export = function () {
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn == 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = '/inside.php?t=json&m='+ controlName +'&f=getList&_export=1'+'&'+params;
                }
            }
        );
    };
}
var oilTest = new oiltest();