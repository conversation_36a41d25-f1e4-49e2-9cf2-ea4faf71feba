/**
 * 批量修改模板下载
 */
function batchEditTpl() {
    Ext.MessageBox.confirm('模板下载', '确认下载?', function showResult(btn) {
        if (btn == 'yes') {
            window.location.href = '/download/templates/ClaimMoneyTemplate.xlsx';
        }
    });
}

var isDirty = false;//用于判断批量修改是否有导入

var detail_record = Ext.data.Record.create([{name: 'id', type: 'string'}]);
//缓存的
var detail_record_store_main = new Ext.data.GroupingStore({
    reader: detail_record,
    proxy: new Ext.data.MemoryProxy(detail_record), AutoLoad: true, sortInfo: {}
});

var detail_add_editor = new Ext.ux.grid.RowEditor({
    saveText: '确定',
    clicksToEdit: 2,
    listeners: {
        beforeedit: function (rd, idx) {
            if (Ext.getCmp('_status').getValue() >= 1) {
                return false;
            }
            detail_edit = true;
            Ext.getCmp('del_detail').disable();
        },
        afteredit: function (rd, rec, reced, idx) {
            Ext.getCmp('btn_save').enable();
        },
        canceledit: function (rd, rec, reced, idx) {
            var num = detail_record_store_main.data.length;
            if (num > 0) {
                for (var i = 0; i < num; i++) {
                    if (detail_record_store_main.getAt(i)) {
                        if (!detail_record_store_main.getAt(i).get('bind_status')) {
                            Ext.getCmp('detail_bind_status').allowBlank = true;
                            Ext.getCmp('org_id').allowBlank = true;
                            var s = detail_add_panel.getSelectionModel().getSelections();
                            detail_record_store_main.remove(s[0]);
                        }
                    }
                }
                Ext.getCmp('btn_save').enable();
                Ext.getCmp('del_detail').enable();
            }
        }
    }
});

function detail_main_rand_num(n) {
    var rnd = "";
    for (var i = 0; i < n; i++)
        rnd += Math.floor(Math.random() * 10);
    return rnd;
}

//详情添加行事件
function detail_grid_cell_add() {
    Ext.getCmp('btn_save').disable();
    detail_edit = true;
    var e = new detail_record({
        id: detail_main_rand_num(10)
    });
    detail_add_editor.stopEditing();
    detail_record_store_main.add(e);
    detail_add_panel.getView().refresh();
    detail_add_panel.getSelectionModel().selectRow(detail_record_store_main.getCount() - 1);
    detail_add_editor.startEditing(detail_record_store_main.getCount() - 1);
}

var _sm = new Ext.grid.RowSelectionModel({
    listeners: {
        selectionchange: function (data) {
            if (data.getCount() && Ext.getCmp('_status').getValue() < 1) {
                Ext.getCmp('del_detail').enable();
            } else {
                Ext.getCmp('del_detail').disable();
            }
        }
    }
});

//批量修改表格
var batch_edit_panel2 = new Ext.grid.GridPanel({
    store: detail_record_store_main,
    region: 'center',
    height: 300,
    width: 650,
    autoScroll: true,
    enableColumnResize: false,
    sm: _sm,
    plugins: [detail_add_editor],
    tbar: [
        {
            iconCls: 'silk-export',
            text: '导入',
            id: 'import_detail',
            handler: function () {
                batchImport();
            }
        },
        {
            iconCls: 'silk-add',
            text: '模板下载',
            id: 'tpldownload',
            handler: function () {
                batchEditTpl();
            }
        }

    ],
    columns: [
        new Ext.grid.RowNumberer({header: '序号', width: 35}),
        {
            header: '机构编码',
            dataIndex: 'orgcode',
            width: 120,
            sortable: true,
        },
        {
            header: '机构名称',
            dataIndex: 'org_name',
            width: 180,
            sortable: true,
        },
        {
            header: '认领金额',
            dataIndex: 'claim_money',
            width: 80,
            sortable: true,
        },
        {
            header: '说明',
            dataIndex: 'remark',
            width: 150,
            sortable: true,
        }
    ]
});

/**
 * 批量导入
 */
function batchImport() {
    var form = new Ext.form.FormPanel({
        baseCls: 'x-plain', labelWidth: 70, fileUpload: true, defaultType: 'textfield',
        items: [
            {
                xtype: 'textfield', fieldLabel: '文件名称', name: 'userfile',
                id: 'userfile', inputType: 'file', blankText: '文件名不能为空',
                anchor: '100%'
            }
        ]
    });
    var import_win = new Ext.Window({
        title: '批量导入', width: 400, height: 105, minWidth: 300, minHeight: 100, modal: true,
        layout: 'fit', plain: true, bodyStyle: 'padding:5px;', buttonAlign: 'center', items: form,
        buttons: [{
            text: '导入',
            handler: function () {
                if (form.form.isValid()) {
                    if (Ext.getCmp('userfile').getValue() == '') {
                        Ext.Msg.alert('系统提示', '请选择你要上传的文件');
                        return;
                    }

                    var transfer_amount = Ext.getCmp('no_claim_money').getValue();
                    var payee_name = Ext.getCmp('payee_name').getValue();
                    var operators_id = Ext.getCmp('operator_id').getValue();
                    var pay_channel = Ext.getCmp('pay_channel').getValue();

                    console.log(transfer_amount);

                    isDirty = true;

                    form.getForm().submit({
                        url: '/inside.php?t=json&m=oil_bank_records&f=batchImport',
                        params:{transfer_amount:transfer_amount,payee_name:payee_name,operators_id:operators_id,pay_channel:pay_channel},
                        success: function (form, action) {
                            if(Ext.decode(action.response.responseText).code != 0){
                                Ext.Msg.alert('系统提示', Ext.decode(action.response.responseText).msg);
                            }else {
                                var r = Ext.decode(action.response.responseText).data;
                                if (r.length > 0) {
                                    detail_record_store_main.removeAll();
                                    for (var i = 0; i < r.length; i++) {
                                        var _rec = new detail_record({
                                            orgcode: r[i].orgcode,
                                            org_name: r[i].org_name,
                                            claim_money: r[i].claim_money,
                                            remark: r[i].remark,
                                        });

                                        detail_record_store_main.add(_rec);

                                        Ext.MessageBox.alert('提示', '导入成功');
                                    }
                                } else {
                                    Ext.MessageBox.alert('提示', '表格中没有有效数据');
                                }

                                import_win.close();
                            }
                        },
                        failure: function (form, action) {
                            Ext.MessageBox.alert('提示', Ext.decode(action.response.responseText).msg);
                        }
                    })
                }
            }
        }, {
            text: '关闭',
            handler: function () {
                import_win.close();
            }
        }]
    });
    import_win.show();
    detail_record_store_main.removeAll();
}