/*********************************
 *  数据中心
 *********************************/

var controlName = 'oil_receipt_trades';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "id","trades_id","sale_id","org_id","orgcode","top_org_id","top_orgcode","vice_no","trade_place","truck_no","qz_drivername","qz_drivertel","trade_price","trade_money","operator_money","trade_num","discount","mac_amount","consume_use_fee","use_fanli_money","oil_name","receipt_oil_name","document_type","trade_createtime","trade_tradetime","down_fanli_way","down_cal_rebate","straight_down_rebate","after_rebate","mark_rebate","supplier_id","up_operator_id","down_operator_id","open_money","open_num","open_discount","profit_rate","unit","createtime","updatetime",        ]
    ),
});