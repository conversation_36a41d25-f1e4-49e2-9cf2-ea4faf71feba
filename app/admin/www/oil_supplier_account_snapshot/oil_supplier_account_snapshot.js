/**
 * 全局类
 */
function oiltest() {
    //列表表格
    this.getGridPanel = function () {
        var _sm =new Ext.grid.RowSelectionModel({
            singleSelect: true,
            listeners: {
                selectionchange: function(data) {
                    if (data.getCount()){
                        (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').enable() : '';//删除按钮
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').enable() : '';//修改按钮
                    }else{
                        (Ext.getCmp('deleteBtn')) ? Ext.getCmp('deleteBtn').disable() : '';//删除按钮
                        (Ext.getCmp('editBtn')) ? Ext.getCmp('editBtn').disable() : '';//修改按钮
                    }
                }
            }
        });

        var grid_list = new Ext.grid.GridPanel({
            title	 :'油站供应商账户快照表',
            region	 : 'center',
            loadMask : true,
            cm : new Ext.grid.ColumnModel([
                {header:'<div align=center>账号</div>',dataIndex:'account_no',align:'center',sortable:true,width:80},
                {header:'<div align=center>快照日期</div>',dataIndex:'snapshot_date',sortable:true,width:100},
                {header: '<span title="对应核算主体的名称，供应商名称或服务区名称或主卡卡号或运营商名称" style="cursor:pointer;color:green">账户名称</span>', dataIndex: 'account_name',align:'left', sortable: true, width: 300,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                        return '<a title="' + record.data.account_name + '">' + record.data.account_name + '</a>';
                    }},
                {header:'<div align=center>账户类型</div>',dataIndex:'res_type_value',align:'center',sortable:true,width:80},
                {header:'<div align=center>账户等级</div>',dataIndex:'account_level_value',align:'center',sortable:true,width:80},
                {header:'<span title="供应商付款、返利、回票的核算主体" style="cursor:pointer;color:green">核算主体</span>',dataIndex:'settle_obj_value',align:'center',sortable:true,width:80},
                {header:'<span title="账户现金余额" style="cursor:pointer;color:red;font-weight:bold;">账户余额(元)</span>',dataIndex:'balance',align:'right',sortable:true,width:100,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                        return '<b>' + record.data.balance + '</b>';
                    }},
                {header:'<div align=center>账户状态</div>',dataIndex:'status_value',align:'center',sortable:true,width:80},
                {header:'<div align=center>供应商ID</div>',dataIndex:'supplier_id',align:'center',sortable:true,width:80},
                // {header:'<div align=center>供应商名称</div>',dataIndex:'supplier_name',sortable:true,width:320},
                {header: '供应商名称', dataIndex: 'supplier_name',align:'left', sortable: true, width: 300,renderer:function(value, cellMeta, record, rowIndex, columnIndex, store){
                        return '<a title="' + record.data.supplier_name + '">' + record.data.supplier_name + '</a>';
                    }},
                {header:'<div align=center>合作类型</div>',dataIndex:'cooperation_type_value',align:'center',sortable:true,width:100},
                {header:'<span title="包括付款中心充值、卡绑定、导流卡分配" style="cursor:pointer;color:green">累计充值(元)</span>',dataIndex:'recharge_total',align:'right',sortable:true,width:110},
                {header:'<span title="账户对应站点的电子卡交易总和" style="cursor:pointer;color:green">累计消费(元)</span>',dataIndex:'trade_total',align:'right',sortable:true,width:110},
                {header:'<span title="包括账户转账、转卡" style="cursor:pointer;color:green">累计转账(元)</span>',dataIndex:'tranfer_total',align:'right',sortable:true,width:110},
                {header:'<span title="账户调调账总额" style="cursor:pointer;color:green">累计调账(元)</span>',dataIndex:'adjust_total',align:'right',sortable:true,width:110},
                {header:'<span title="包括上游返利录入及消费实时返利" style="cursor:pointer;color:green">累计返利(元)</span>',dataIndex:'rebate_total',align:'right',sortable:true,width:110},
                {header:'<span title="包括账户预退款、退款、解绑卡" style="cursor:pointer;color:green">累计清分(元)</span>',dataIndex:'clearing_total',align:'right',sortable:true,width:110},
            	            ]),
            store : main_store,
            sm :_sm,
            //分页
            bbar: new Ext.PagingToolbar({
                plugins: new Ext.ux.plugin.PagingToolbarResizer,
                pageSize: pagesize,
                displayInfo : true,
                displayMsg : '当前记录数: {0} - {1} 总记录数: {2}',
                emptyMsg : '没有符合条件的记录',
                store : main_store
            }),
            tbar : []
        });

        return grid_list;
    };

    //关闭窗口
    this.closeWin = function (id) {
        if (!id) return;
        var win = Ext.getCmp(id);
        win.destroy();
    };

    var _getWindow = function (config) {
        var width = config.width,
            height = config.height,
            title = config.title,
            id = config.id,
            button = config.button;

        button.push({
            text: '取消',
            id: 'button_cancer',
            handler: function () {
                var oilTest = new oiltest();
                oilTest.closeWin(id);
            }
        });
        var windows = new Ext.Window({
            layout: 'border',
            region: 'center',
            width: width,
            modal: true,//遮挡窗口后的内容
            id: id,
            height: height,
            title: title,
            closeAction: 'destroy',
            buttons: button
        });

        return windows;
    };

    //添加
    this.add = function () {
        var windows = _getWindow({
            title: '添加-油站供应商账户快照表',
            id: 'add_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'add-form',
            items: addPanel.getPanel('add-form', 'add_win')
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init();
    };

    //编辑
    this.update = function () {
        var sm   = grid_list.getSelectionModel();
        var data = sm.getSelections();
        data[0] ? id = data[0].get("id") : '';

        var windows = _getWindow({
            title: '编辑-油站供应商账户快照表',
            id: 'update_win',
            width: 550,
            height:270,
            button: [{
                text: '确定',
                id: 'button_ok',
                handler: function () {
                    addPanel.submit();
                }
            }]
        });

        var panel = new Ext.FormPanel({
            labelAlign: 'right',
            labelWidth: 100,
            buttonAlign: 'center',
            frame: true,
            bodyStyle: 'background-color: white;padding-top:10px;',
            region: 'center',
            id: 'update-form',
            items: addPanel.getPanel('update-form', 'update_win',id)
        });

        windows.add(panel);
        windows.doLayout();
        windows.show();
        addPanel.init(data[0]);
    };

    //删除
    this.delete = function () {
        Ext.MessageBox.confirm('删除', '删除之后该条纪录将不再显示，确定要删除？', function showResult(btn){
            if (btn == 'yes')
            {
                var sm   = grid_list.getSelectionModel();
                var data = sm.getSelections();
                var ids = data[0].get("id");
                Ext.Ajax.request({
                    url:getUrl(controlName,'remove'),
                    method:'post',
                    params:{ids:ids},
                    success: function sFn(response,options)
                    {
                        main_store.removeAll();
                        main_store.load();
                        Ext.Msg.alert('系统提示', '删除成功');
                    }
                });
            }
        });
    };
    //导出
    this.export = function () {
        Ext.MessageBox.confirm('导出', "确定要导出吗？", function showResult(btn) {
                if (btn == 'yes') {
                    var params = top_panel.getForm().getValues(true);
                    window.location.href = '/inside.php?t=json&m='+ controlName +'&f=getList&_export=1'+'&'+params;
                }
            }
        );
    };
}
var oilTest = new oiltest();