/*********************************
 *  数据中心
 *********************************/

var controlName = 'foss_invoice_t';
var pagesize = 50;
/**
 * 生成URL
 */
function getUrl(m,f){
    return '../inside.php?t=json&m=' + m + '&f=' + f;
}
var main_store = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({url: getUrl(controlName,'getList'), method: "POST"}),
    reader: new Ext.data.JsonReader(
        {
            totalProperty: 'data.total',
            root: 'data.data'
        },
        [
            "CREAT_DATE","CARD_TYPE","PRODUCT_CODE","PRODUCT_NAME","FUNDING_PARTY","CUSTOMER_CODE","CUSTOMER_NAME","INVOICE_AMOUNT","ENTITY_CODE","ENTITY_NAME","ORDER_LABEL","BACKUP1","BACKUP2","BACKUP3","BACKUP4","BACKUP5","UPDATE_DATE",        ]
    ),
});