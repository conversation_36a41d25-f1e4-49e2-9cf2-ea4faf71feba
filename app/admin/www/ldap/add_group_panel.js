var add_group_win;//添加窗口
var add_update_group_id = '';//判断是添加还是修改
var add_group_kind = '' ; // 分组类别
resStore.load();
var resLdapCbox = new Ext.form.MultiSelect(
	    {
	            style: 'margin-left : 12px;',
	            width: 160,
	            editable: false,
	            id: 'g_memberUid',
	            store: resStore,
	            emptyText: '',
	            //allowBlank: false, 
	            blankText: '', 
	            mode: 'local',
	            displayField: 'uid',
	            valueField: 'uid',
	            triggerAction: 'all',
	            selectOnFocus: true,
	            listWidth: 200
	    });
var add_form_group_panel = new Ext.form.FormPanel({
    region: 'center',
    hideLabels: true,
    bodyStyle: 'padding: 10px',
    height: 40,
    items: [
        {// 第一行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '编码：',
                    id: 'g_cn_label',
                    width: 70
                },
                {
                    xtype: 'textfield',
                    id: 'g_cn',
                    width: 160,
                    regex: /^[a-zA-Z0-9]{1,32}$/,
                    style: 'margin-left : 12px;'              
                },
                {xtype: 'displayfield', style: 'margin-left : 10px;', value: ' <font color=red>*</font>'}
            ]
        },
        {// 第二行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value:  'ID：',
                    id: 'g_gid_label',
                    width: 70
                },
                {
                    xtype: 'textfield',
                    id: 'g_gidNumber',
                    width: 160,
                    regex: /^[0-9]{1,10}$/,
                    style: 'margin-left : 12px;'
                    
                },
                {xtype: 'displayfield', style: 'margin-left : 10px;', value: ' <font color=red>*</font>'}
            ]
        },
        {// 第三行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value:  '名称：',
                    id: 'g_des_label',
                    width: 70
                },
                {
                    xtype: 'textfield',
                    id: 'g_description',
                    width: 160,
                    regex: /^[a-zA-Z0-9\u4E00-\u9FA5]+$/,
                    style: 'margin-left : 12px;'                	
                },
                {xtype: 'displayfield', style: 'margin-left : 10px;', value: ' <font color=red>*</font>'}
            ]
        },
        {// 第四行
            xtype: 'compositefield',
            items: [
                {
                    xtype: 'displayfield',
                    value: '访问资源：',
                    width: 70
                },
                resLdapCbox,
            ]
        }
    ],
    buttons: [
        {
            text: "确定",
            handler: function () {
                if (check_group_input(add_group_kind)) {
                    var _form = this.ownerCt.ownerCt.getForm();
                    if (_form.isValid()) {
                        if (add_update_group_id) {
                            //修改
                            _form.submit({url: '../inside.php?t=json&m=' + controlName + '&f=groupsUpdate',
                                params: {
                                    cn: add_update_group_id,
                                    kind : add_group_kind,
                                    gidNumber: Ext.getCmp('g_gidNumber').getValue(),
                                    description: Ext.getCmp('g_description').getValue(),
                                    memberUid: Ext.getCmp('g_memberUid').getValue()
                                    
                                },
                                waitMsg: 'Saving Data...',
                                success: function (resp, opts) {
                                    if (Ext.decode(opts.response.responseText).msg == 1) {
                                        Ext.Msg.alert('提示', '修改成功!');
                                    }
                                    else if (Ext.decode(opts.response.responseText).msg == 2) {
                                        Ext.Msg.alert('提示', '修改失败!');
                                    }
                                    else if (Ext.decode(opts.response.responseText).msg == 3) {
                                        Ext.Msg.alert('提示', '修改失败，ldap发生错误! ');
                                    }
                                    else if (Ext.decode(opts.response.responseText).msg == 4) {
                                	employeeStore.load();
                                        Ext.Msg.alert('提示', '修改成功，已有用户被同步!');
                                    }
                                    //刷新页面
                                    if(add_group_kind =="dept") {
                                	deptStore.load();
                                    }
                                    else if(add_group_kind =="role") {
                                	roleStore.load();
                                    }
                                    add_group_win.hide();
                                }
                            });
                        }
                        else {
                            //新增
                            _form.submit({url: '../inside.php?t=json&m=' + controlName + '&f=groupsAdd',
                                params: {
                                    kind : add_group_kind,
                                    cn: Ext.getCmp('g_cn').getValue(),
                                    gidNumber: Ext.getCmp('g_gidNumber').getValue(),
                                    description: Ext.getCmp('g_description').getValue(),
                                    memberUid: Ext.getCmp('g_memberUid').getValue()
                                },
                                waitMsg: 'Saving Data...',
                                success: function (resp, opts) {
                                    if (Ext.decode(opts.response.responseText).msg == 0) {
                                	 if(add_group_kind =="dept") {
                                	     Ext.Msg.alert('提示', '已存在部门编码:[ ' + Ext.getCmp('g_cn').getValue() + ' ]或部门ID:[ ' + Ext.getCmp('g_gidNumber').getValue() + ' ],请手动更改!');
                                         }
                                         else if(add_group_kind =="role") {
                                             Ext.Msg.alert('提示', '已存在角色编码:[ ' + Ext.getCmp('g_cn').getValue() + ' ]或角色ID:[ ' + Ext.getCmp('g_gidNumber').getValue() + ' ],请手动更改!');
                                         }
                                        
                                        return false;
                                    } else if (Ext.decode(opts.response.responseText).msg == 1) {
                                        Ext.Msg.alert('提示', '新增成功!');
                                    }  else if (Ext.decode(opts.response.responseText).msg == 2){
                                        Ext.Msg.alert('提示', '新增失败');
                                    } else if (Ext.decode(opts.response.responseText).msg == 3){
                                        Ext.Msg.alert('提示', '新增失败, ldap发生错误!');
                                    }
                                    //刷新页面
                                    if(add_group_kind =="dept") {
                                	deptStore.load();
                                    }
                                    else if(add_group_kind =="role") {
                                	roleStore.load();
                                    }
                                    add_group_win.hide();
                                }
                            });
                        }
                    }
                }
            }
        },
        {
            text: "取消",
            handler: function () {
        	add_group_win.hide();
            }
        }
    ]
});

//主窗体
if (!add_group_win) {
    add_group_win = new Ext.Window({
        'id': 'add_group_win',
        layout: "border",
        width: 300,
        height: 200,
        title: '组管理',
        closeAction: 'hide',
        plain: true,
        items: [add_form_group_panel]
    });
}

//编辑
function groupUpdate(kind) {
    // first clean all item
    add_group_kind = kind;
    add_update_group_id = '';
    Ext.getCmp('g_cn').setValue('');
    Ext.getCmp('g_gidNumber').setValue('');
    Ext.getCmp('g_description').setValue('');
    Ext.getCmp('g_memberUid').setValue(''); 
    setPopWindowLabel("");
    
    var sm   = "";
    if(kind =="dept") { sm = _dept_grid.getSelectionModel();}
    if(kind =="role") { sm = _role_grid.getSelectionModel();}
    var data = sm.getSelections();
    if (data[0].get('cn')) add_update_group_id = data[0].get('cn');
    function fn() {
	Ext.getCmp('g_cn').disable();//修改时，不能修改
        Ext.getCmp('g_gidNumber').disable();//修改时，不能修改
        Ext.getCmp('g_cn').setValue(data[0].get('cn'));
        Ext.getCmp('g_gidNumber').setValue(data[0].get('gidnumber'));
        Ext.getCmp('g_description').setValue(data[0].get('description'));
        Ext.getCmp('g_memberUid').setValue(data[0].get('memberuid'));
        setPopWindowLabel(kind);
    }

    var task = new Ext.util.DelayedTask(fn);
    task.delay(50);
    add_group_win.show();
}

//添加
function groupAdd(kind) {
    add_group_kind = kind;
    add_update_group_id = '';
    Ext.getCmp('g_cn').enable();//增加时能修改
    Ext.getCmp('g_gidNumber').enable();//增加时能修改
    Ext.getCmp('g_cn').setValue('');
    Ext.getCmp('g_gidNumber').setValue('');
    Ext.getCmp('g_description').setValue('');
    Ext.getCmp('g_memberUid').setValue('');  
    setPopWindowLabel(kind);
    add_group_win.show();
}
//删除
function groupDelete(kind) {

    add_group_kind = kind
    var sm   = "";
    if(kind =="dept") 
    {
	sm = _dept_grid.getSelectionModel();
    }
    else if(kind =="role") 
    { 
	sm = _role_grid.getSelectionModel();
    }
    var data = sm.getSelections();
    var cn = data[0].get("cn");
    var description = data[0].get("description");
    var str  = '你确定要删除组 '+description+' ['+cn+']？'; 

    Ext.MessageBox.confirm('删除', str, function showResult(btn){
        if (btn == 'yes')
        {
            Ext.Ajax.request({
                url:'../inside.php?t=json&m='+ controlName +'&f=groupsDelete',
                method:'post',
                params:{'cn':cn,'kind':kind},
                success: function sFn(response,opts)
                {
                   
                    if (Ext.decode(response.responseText).msg == 1) {
                        Ext.Msg.alert('提示', '删除成功!');
                    }
                    else if (Ext.decode(response.responseText).msg == 2) {
                        Ext.Msg.alert('提示', '删除失败!');
                    }
                    else if (Ext.decode(response.responseText).msg == 3) {
                        Ext.Msg.alert('提示', '删除失败，ldap发生错误!');
                    }
                    else if (Ext.decode(response.responseText).msg == 4) {
                        Ext.Msg.alert('提示', '不能删除，有关联用户存在! ');
                    }
                   //刷新页面
                    if(add_group_kind =="dept") {
                	deptStore.load();
                    }
                    else if(add_group_kind =="role") {
                	roleStore.load();
                    }else{
                	deptStore.load();
                	roleStore.load();
                    }
                   
                }
            });
        }
    });

}

function setPopWindowLabel(kind)
{
    if(kind == "dept")
    {
	Ext.getCmp('g_cn_label').setValue('部门编码：'); 
	Ext.getCmp('g_gid_label').setValue('部门ID：'); 
	Ext.getCmp('g_des_label').setValue('部门名称：'); 
    }
    else if(kind = "role")
    {
	Ext.getCmp('g_cn_label').setValue('角色编码：'); 
	Ext.getCmp('g_gid_label').setValue('角色ID：'); 
	Ext.getCmp('g_des_label').setValue('角色名称：'); 
    }
    else
    {
	Ext.getCmp('g_cn_label').setValue(''); 
	Ext.getCmp('g_gid_label').setValue(''); 
	Ext.getCmp('g_des_label').setValue(''); 
    }
    
}

//表单验证
function check_group_input(kind) {
    var flag = true;
    var msg = '';
    var cn_regex = /^[a-zA-Z0-9]{1,32}$/;
    var gid_regex =  /^[0-9]{1,10}$/;
    var des_regex = /^[a-zA-Z0-9\u4E00-\u9FA5]+$/;
    var cn = Ext.getCmp('g_cn').getValue();
    var gidNumber = Ext.getCmp('g_gidNumber').getValue();
    var description = Ext.getCmp('g_description').getValue();
    var labelName = ""
    if(kind == "dept")
    {
	labelName = '部门';
    }
    else if(kind = "role")
    {
	labelName = '角色';
    }
    else
    {
	labelName = '分组';
    }

    if (cn == '') {
        msg = labelName+'编码必填！';
        flag = false;
    }
    if (cn != '' && !cn.match(cn_regex)) {
        msg = labelName+'编码只能为英文或数字，并且长度不能超过32位！';
        flag = false;
    }
    if (gidNumber == '') {
        msg = labelName+'ID必填！';
        flag = false;
    }
    if (gidNumber != '' && !gidNumber.match(gid_regex)) {
        msg = labelName+'ID只能为数字，并且长度不能超过10位！';
        flag = false;
    }   
    if (description == '') {
        msg = labelName+'名称必填！';
        flag = false;
    }
    if (description != '' && !description.match(des_regex)) {
        msg = labelName+'名称只能为汉字或英数字。';
        flag = false;
    }
    if (flag == false) {
        Ext.MessageBox.alert('提示', msg);
    }
    return flag;
}

