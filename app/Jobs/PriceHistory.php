<?php

namespace App\Jobs;

use App\Library\Helper\Common;
use App\Models\Gas\DictModel;
use App\Models\Gas\PriceHistoryModel;
use App\Models\Gas\PriceSaleExtModel;
use App\Models\Gas\PriceSaleModel;
use App\Models\Gas\PriceSalePlanModel;
use App\Models\Gas\StationModel;
use App\Repositories\Price\PriceRepository;
use App\Repositories\Station\StationRepository;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PriceHistory implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $from;

    private $object;

    private $data;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $retryAfter = 1;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Common::log('info','生效进价参数:',[$this->data]);
        $now = date('Y-m-d H:i:s');$query = PriceSaleModel::query();$historyPlatformParam = $platformParam = $historyPlatformRuleParam = [];
        $loseSupplierPriceId = ['id' => $this->data['lose_supplier_price_id']];
        Common::log('info','失效参数:'.$now,['loseSupplierPriceId' => $this->data['lose_supplier_price_id']]);
        // 失效进价
        $historySupplierPrice = PriceSaleModel::scopeWithCondition($query, $loseSupplierPriceId)->where('endtime', '<', $now)->get()->toArray();
        if (empty($historySupplierPrice) || !isset($historySupplierPrice[0])) {
            Common::log('info','失效进价为空:',[$this->data]);
            return;
        }
        $historySupplierPrice = collect($historySupplierPrice)->keyBy('id')->toArray();
        //批量改价这个地方有问题
        $stationIdList = [];
         if(is_array($this->data['lose_supplier_price_id'])) {
            $stationIdList = array_column($historySupplierPrice,'station_id');
         }else if(isset($historySupplierPrice[$this->data['lose_supplier_price_id']])) {
            $stationIdList =[$historySupplierPrice[$this->data['lose_supplier_price_id']]['station_id']];
        }

        if(empty($stationIdList)) {
            Common::log('info','stationId为空:',[$this->data]);
            return;
        }
        // 将失效销价计算价格放入历史表
        $supplierPrice = PriceRepository::getPriceByWhere(['id' => $this->data['future_supplier_price_id']]);
        $supplierPrice = !isset($supplierPrice[0]) ? [$supplierPrice] : $supplierPrice;
        if(empty($supplierPrice)) {
            Common::log('info','生效价格:',[$this->data]);
            return;
        }

        // 未来待生效的进价与已经失效的价格
        $losePlatformPrice = PriceRepository::getLosePlatformPriceBySupplierPrice($this->data['lose_supplier_price_id'], ['6']);
        foreach ($losePlatformPrice as $key => $losePlatformPriceItem) {
            $historyPlatformParam[] = collect($losePlatformPriceItem)->map(function($item) use ($historySupplierPrice, $supplierPrice){
//                $item['price'] = $historySupplierPrice[$item['supplier_price_id']]['price'] + $item['float_price'];
                $item['price'] = bcadd($historySupplierPrice[$item['supplier_price_id']]['price'], $item['float_price'],2);
                $item['pay_price'] = $historySupplierPrice[$item['supplier_price_id']]['price'];
                $item['endtime'] = $historySupplierPrice[$item['supplier_price_id']]['endtime'];
                $item['updatetime'] = date('Y-m-d H:i:s');
                $item['modifier'] = $supplierPrice[0]['modifier'];
                unset($item['discount_rate']);
                unset($item['rule_type']);
                unset($item['float_val']);
                unset($item['ndrc_price']);
                return $item;
            })->toArray();

            //失效的销价规则 仅写入特殊规则历史数据
            $historyPlatformSpecialPrice = collect($losePlatformPriceItem)->reject(function ($item) {
                return in_array($item['rule_type'], [PriceSaleExtModel::RULE_TYPE_FIXED,PriceSaleExtModel::RULE_TYPE_PRICE_FLOAT]);
            });
            $historyPlatformRuleParam[] = collect($historyPlatformSpecialPrice)->map(function($item) use ($historySupplierPrice){
                return [
                    'sale_price_id' => $item['id'],
                    'rule_type'     => $item['rule_type'],
                    'float_val'     => $item['float_val'],
                    'starttime'     => $item['starttime'],
                    'endtime'       => $historySupplierPrice[$item['supplier_price_id']]['endtime'],
                    'updatetime'    => date('Y-m-d H:i:s'),
                ];
            })->toArray();
        }
        $historyPlatformParam     = collect($historyPlatformParam)->flatten(1)->toArray();
        $historyPlatformRuleParam = collect($historyPlatformRuleParam)->flatten(1)->toArray();

        // 生效进价的销价变更
        // 销价收费方式如果出现变更
        $platformPrice = PriceRepository::getPlatformPriceBySupplierPrice($this->data['future_supplier_price_id'], ['6']);
        foreach ($platformPrice as $key => $platformPriceItem) {
            $platformParam[] = collect($platformPriceItem)->map(function($item){
                $value['id'] = $item['id'];
                $value['price'] = $item['platform_price'];
//                $value['pay_price'] = $item['platform_price'] - $item['float_price'];
                $value['pay_price'] = bcsub($item['platform_price'], $item['float_price'],2);
                $value['float_price'] = $item['float_price']; // 更新浮动价（支持百分比模式）
                // 如果有同时生效的销价，并发场景下 固定数值浮动 计算的不正确 重新获取   1分钟内
                if ($item['rule_type'] == PriceSaleExtModel::RULE_TYPE_PRICE_FLOAT) {
                    $planEndTime = date('Y-m-d H:i:s', strtotime($item['supplier_start_time'])+60);
                    $futurePlan =  PriceSalePlanModel::where('price_id', $item['platform_price_id'])
                        ->whereIn('status', [1,2])
                        ->where('effective_time', '>=', $item['supplier_start_time'])
                        ->where('effective_time', '<', $planEndTime)
                        ->where('rule_type', 6)->first();
                    if (!empty($futurePlan)) {
                        $futurePlan = $futurePlan->toArray();
                        $value['pay_price'] = bcsub($item['platform_price'], $futurePlan['float_price'],2);
                        $value['float_price'] = $futurePlan['float_price'];
                    }
                }
                $value['starttime'] = $item['supplier_start_time'];
                $value['updatetime'] = $item['supplier_update_time'];
                $value['modifier'] = $item['supplier_modifier'];
                return $value;
            })->toArray();
        }
        $platformParam = collect($platformParam)->flatten(1)->toArray();

        // 生效进价的油机价发生变更，变更其对应的销价油机价
        $macPriceParamAttribute = $macPriceParam = [];
        $supplierPriceByUniqueId = collect($supplierPrice)->keyBy('pcode_oil_id')->toArray();
        $changeMacPrice = collect($historySupplierPrice)->filter(function($item) use ($supplierPriceByUniqueId){
            return $item['mac_price'] != $supplierPriceByUniqueId[$item['pcode_oil_id']]['mac_price'];
        })->toArray();
        //油机价发生了变更，需要变更其对应的销价
        if (!empty($changeMacPrice)) {
            $losePlatformPrice = PriceRepository::getLosePlatformPriceBySupplierPrice(collect($changeMacPrice)->pluck('id')->toArray(), ['4','6','1']);
            $losePlatformPrice = collect($losePlatformPrice)->flatten(1)->toArray();
            $losePlatformPrice = collect($losePlatformPrice)->groupBy('supplier_price_id')->toArray();
            foreach ($changeMacPrice as $key => $changeMacPriceItem) {
                $macPriceParamAttribute[$key] = ['id' => collect($losePlatformPrice[$changeMacPriceItem['id']])->pluck('id')->toArray()];
                $macPriceParam[$key] = [
                    'mac_price' => $supplierPriceByUniqueId[$changeMacPriceItem['pcode_oil_id']]['mac_price'],
                    'modifier' => $supplierPriceByUniqueId[$changeMacPriceItem['pcode_oil_id']]['modifier'],
                ];
            }
        }
        Common::log('info','油机价变更参数:',['macPriceParamAttribute' => $macPriceParamAttribute,'data'=>$this->data]);

        // 非进价/油机价发生变更，无需更新销价
//        $changePriceLegal = collect($historySupplierPrice)->reject(function($item) use ($supplierPriceByUniqueId){
//            return $item['mac_price'] == $supplierPriceByUniqueId[$item['pcode_oil_id']]['mac_price'] && $item['price'] == $supplierPriceByUniqueId[$item['pcode_oil_id']]['price'];
//        });
//        if (!$changePriceLegal) {
//            $historyPlatformParam = $platformParam = [];
//        }
//        if ($historySupplierPrice['price'] == $supplierPrice['price'] && $historySupplierPrice['mac_price'] == $supplierPrice['mac_price']){
//            $historyPlatformParam = $platformParam = [];
//        }


        // 将失效的进价价格写入历史表，并删除
        DB::connection('mysql_gas')->beginTransaction();
        try{
            //失效进价删除且入历史表
            PriceRepository::historySupplierPriceCreateBatch($historySupplierPrice);
            PriceRepository::priceDelete(['id' => $this->data['lose_supplier_price_id']]);
            //失效销价入历史表
            PriceRepository::historyPlatformPriceCreate($historyPlatformParam);

            // 历史销价规则
            PriceRepository::historyPlatformPriceRuleCreate($historyPlatformRuleParam);

            //生效销价更新
            PriceRepository::platformPriceUpdate($platformParam);
            //油机价更新
            PriceRepository::priceUpdateBatch($macPriceParam, $macPriceParamAttribute);
            DB::connection('mysql_gas')->commit();
        }catch (\Exception $e) {
            Common::log('info','事务回滚:',["code"=>$e->getCode(),"msg"=>$e->getMessage(),"data"=>$this->data]);
            DB::connection('mysql_gas')->rollback();
        }
        Common::log('info','事务成功:',[$this->data]);
        foreach ($stationIdList as $item) {
            event(new \App\Events\StationEvent(['station_id' => $item,'push' => 'price']));
            event(new \App\Events\CacheStationPriceEvent($item));
        }
        Common::log('info','批量操作价格历史表添加成功_'.$now,['futureSupplierPriceId' => $this->data['future_supplier_price_id']]);
    }

    /**
     * The job failed to process.
     *
     * @param  Exception  $exception
     * @return void
     */
    public function failed(Exception $exception)
    {
        // 发送失败通知, etc...
        Common::log('error','批量操作价格历史表添加任务失败', ['message' => $exception->getMessage(),'line' => $exception->getLine()]);
    }
}
