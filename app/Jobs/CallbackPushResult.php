<?php

namespace App\Jobs;

use Request\FOSS_STATION;
use Throwable;


class CallbackPushResult extends BasicJob
{
    private $callbackData;

    /**
     * @param array $callbackData
     */
    public function __construct(array $callbackData)
    {
        $this->callbackData = $callbackData;
    }

    /**
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/10/16 5:49 下午
     */
    public function handle()
    {
        FOSS_STATION::handle('v1/stationorgrule/setPushStatus', [
            'task_id'    => $this->callbackData['station_task_id'],
            'status'     => $this->callbackData['push_status'],
            'station_id' => $this->callbackData['station_id'],
        ]);
    }
}
