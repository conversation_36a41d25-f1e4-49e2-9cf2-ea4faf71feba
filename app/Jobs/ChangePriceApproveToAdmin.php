<?php

namespace App\Jobs;

use App\Models\Gas\StationPriceWorkOrder;
use App\Repositories\WorkOrder\WorkOrderRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Library\Monitor\Falcon;

class ChangePriceApproveToAdmin implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $approveId;
    /**
     * Create a new job instance.
     *
     * @param $approveId int
     * @return void
     */
    public function __construct($approveId)
    {
        $this->approveId = $approveId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $approveInfo = app(WorkOrderRepository::class)->getOneWorkerOrderByParams([
            'id' => $this->approveId
        ]);
        if (empty($approveInfo)) {
            return;
        }
        // 审核通过的不再发送消息通知
        if (strcmp($approveInfo['status'], StationPriceWorkOrder::WAIT_APPROVE)) {
            return;
        }

        $extend = json_decode($approveInfo['extend'], true);
        $text = $this->getTitle($approveInfo['create_time'])."\n\n";
        $text .= $this->getMessage($approveInfo['create_time'])."\n";
        $text .= '油站：'.(empty($extend['remark_name']) ? $extend['station_name'] : $extend['remark_name'])."\n";
        $text .= '省市：'.$extend['province_name'].$extend['city_name']."\n";
        $text .= '商品：'.$extend['goods']."\n";
        $text .= '变更后的油机价：'.$approveInfo['after_price']."\n";
        $text .= '变更后的G7进价：'.$approveInfo['after_price']."\n";
        $text .= '变更后的生效时间：'.$approveInfo['start_time']."\n";
        $text .= '申请人员：'.$approveInfo['proposer']."\n";
        $text .= '申请时间：'.$approveInfo['create_time']."\n";

        Falcon::feishu(config('feishu.oil_price_change'), $text);
    }

    /**
     * 消息头
     *
     * @param $time
     * @return string
     */
    protected function getTitle($time)
    {
        $msg = '';
        if ((strtotime($time) - time()) <= 1800) {
            $msg = '价格变更信息——待审核，即将失效';
        } elseif (time() >= strtotime($time)) {
            $msg = '价格变更信息——待审核，已失效';
        }

        return $msg;
    }

    /**
     * 消息体
     *
     * @param $time
     * @return string
     */
    protected function getMessage($time)
    {
        $msg = '';
        if ((strtotime($time) - time()) <= 1800) {
            $msg = '价格时间即将过期，请及时审核并核对销价。';
        } elseif (time() >= strtotime($time)) {
            $msg = '价格时间已经过期，请及时审核！并修补期间产生的异常订单，并核对销价是否正确。';
        }

        return $msg;
    }
}
