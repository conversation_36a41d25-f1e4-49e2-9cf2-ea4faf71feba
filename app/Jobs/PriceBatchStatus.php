<?php

namespace App\Jobs;

use App\Library\Helper\Common;
use App\Models\Gas\DictTagModel;
use App\Models\Gas\PriceSaleExportTaskModel;
use App\Models\Gas\PriceSaleModel;
use App\Models\Gas\StationModel;
use App\Repositories\Tag\StationTagRepository;
use App\Services\Corner\ClientService;
use App\Services\Import\PriceBatchUploadService;
use App\Services\Station\StationBatchService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Servitization\OilAdapter;
use App\Servitization\Gos;

class PriceBatchStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $data;

    private $headerInfo;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $retryAfter = 1;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            $params = PriceBatchUploadService::checkUploadData($this->data['excelData'],'insert',$this->data['ineffective_supplier_price_starttime'],$this->data['batch_no']);

            $this->updateTask([
                'batch_no'=>$this->data['batch_no'],
                'type'=>$this->data['type'],
                'status'=>2,
                'error_msg'=>''
            ]);

        }catch (Exception $exception){
            //gas_price_sale_export_task
            $this->updateTask([
                'batch_no'=>$this->data['batch_no'],
                'type'=>$this->data['type'],
                'status'=>3,
                'error_msg'=>$exception->getMessage()
            ]);
        }
    }

    /**
     * The job failed to process.
     *
     * @param  Exception  $exception
     * @return void
     */
    public function updateTask($params)
    {
        PriceSaleExportTaskModel::where('batch_no',$params['batch_no'])->where('type',$params['type'])->update(
            ['status'=>$params['status'],'error_msg'=>$params['error_msg']]
        );
    }
}
