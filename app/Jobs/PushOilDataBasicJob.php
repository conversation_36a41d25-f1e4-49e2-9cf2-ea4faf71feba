<?php

namespace App\Jobs;

use App\Models\Data\AuthInfo as AuthInfoData;
use App\Models\Data\Common as CommonData;
use App\Models\Data\DockingPlatformInfo as DockingPlatformInfoData;
use App\Models\Data\Log\QueueLog as Log;
use App\Models\Data\OilMapping\Ad as AdData;
use App\Models\Data\OilMapping\AJSW as AJSWData;
use App\Models\Data\OilMapping\BBSP as BBSPData;
use App\Models\Data\OilMapping\BdtOrg as BdtOrgData;
use App\Models\Data\OilMapping\BQ as BQData;
use App\Models\Data\OilMapping\Cfhy as CfhyData;
use App\Models\Data\OilMapping\CFT as CFTData;
use App\Models\Data\OilMapping\CHTX as CHTXData;
use App\Models\Data\OilMapping\CIEC as CIECData;
use App\Models\Data\OilMapping\CN as CNData;
use App\Models\Data\OilMapping\Common as OilMappingCommonData;
use App\Models\Data\OilMapping\DDE as DDEData;
use App\Models\Data\OilMapping\DESP2A6LA9 as DESP2A6LA9Data;
use App\Models\Data\OilMapping\DESP2C637M as DESP2C637MData;
use App\Models\Data\OilMapping\DESP2H8BBD as DESP2H8BBDData;
use App\Models\Data\OilMapping\Fy as FyData;
use App\Models\Data\OilMapping\GBDW as GBDWData;
use App\Models\Data\OilMapping\Ghc as GhcData;
use App\Models\Data\OilMapping\HG as HGData;
use App\Models\Data\OilMapping\HLJH as HLJHData;
use App\Models\Data\OilMapping\HLL as HLLData;
use App\Models\Data\OilMapping\HR as HRData;
use App\Models\Data\OilMapping\HSL as HSLData;
use App\Models\Data\OilMapping\HYJY as HYJYData;
use App\Models\Data\OilMapping\HytOrg as HytOrgData;
use App\Models\Data\OilMapping\HZ as HZData;
use App\Models\Data\OilMapping\Jd as JdData;
use App\Models\Data\OilMapping\JDWC as JDWCData;
use App\Models\Data\OilMapping\JF as JFData;
use App\Models\Data\OilMapping\JTXY as JTXYData;
use App\Models\Data\OilMapping\KY as KYData;
use App\Models\Data\OilMapping\LF as LFData;
use App\Models\Data\OilMapping\Lhys as LhysData;
use App\Models\Data\OilMapping\LT as LTData;
use App\Models\Data\OilMapping\MB as MBData;
use App\Models\Data\OilMapping\MK as MKData;
use App\Models\Data\OilMapping\MTL as MTLData;
use App\Models\Data\OilMapping\MY as MYData;
use App\Models\Data\OilMapping\MYB as MYBData;
use App\Models\Data\OilMapping\MYCF as MYCFData;
use App\Models\Data\OilMapping\PCKJ as PCKJData;
use App\Models\Data\OilMapping\QDMY as QDMYData;
use App\Models\Data\OilMapping\RQ as RQData;
use App\Models\Data\OilMapping\RRS as RRSData;
use App\Models\Data\OilMapping\RY as RYData;
use App\Models\Data\OilMapping\SFFY as SFFYData;
use App\Models\Data\OilMapping\SFFYSIMPLE as SFFYSIMPLEData;
use App\Models\Data\OilMapping\SHENGMAN as SHENGMANData;
use App\Models\Data\OilMapping\SP as SPData;
use App\Models\Data\OilMapping\SPCJ as SPCJData;
use App\Models\Data\OilMapping\SQ as SQData;
use App\Models\Data\OilMapping\TC as TCData;
use App\Models\Data\OilMapping\WSY as WSYData;
use App\Models\Data\OilMapping\WZYT as WZYTData;
use App\Models\Data\OilMapping\XC as XCData;
use App\Models\Data\OilMapping\XM as XMData;
use App\Models\Data\OilMapping\XYDS as XYDSData;
use App\Models\Data\OilMapping\YB as YBData;
use App\Models\Data\OilMapping\YBT as YBTData;
use App\Models\Data\OilMapping\Ygj as YgjData;
use App\Models\Data\OilMapping\YGY as YGYData;
use App\Models\Data\OilMapping\YLZ as YLZData;
use App\Models\Data\OilMapping\YXT as YXTData;
use App\Models\Data\OilMapping\ZEY as ZEYData;
use App\Models\Data\OilMapping\ZJ as ZJData;
use App\Models\Data\OilMapping\ZJKA as ZJKAData;
use App\Models\Data\OilMapping\ZLGX as ZLGXData;
use App\Models\Data\OilMapping\ZZ as ZZData;
use App\Models\Data\OilMapping\ZZ_AH as ZZ_AHData;
use App\Models\Data\OilMapping\ZZ_BJ as ZZ_BJData;
use App\Models\Data\OilMapping\ZZ_TJ as ZZ_TJData;
use App\Models\Data\StationPushCondition as StationPushConditionData;
use App\Models\Data\StationPushRecord as StationPushRecordData;
use Exception;
use Illuminate\Support\Facades\Queue;
use Throwable;


abstract class PushOilDataBasicJob extends BasicJob
{
    public static $dataPrepareClassMapping = [
        'bbsp'          => BBSPData::class,
        'fy'            => FyData::class,
        'ghc'           => GhcData::class,
        'ygj'           => YgjData::class,
        'bdtOrg'        => BdtOrgData::class,
        'jd'            => JdData::class,
        'hljh'          => HLJHData::class,
        'lhys'          => LhysData::class,
        'hg'            => HGData::class,
        'yb'            => YBData::class,
        'jdwc'          => JDWCData::class,
        'mk'            => MKData::class,
        'xc'            => XCData::class,
        'dde'           => DDEData::class,
        'tc'            => TCData::class,
        'sq'            => SQData::class,
        'myb'           => MYBData::class,
        'cn'            => CNData::class,
        'sp|2036HG'     => SPData::class,
        'zey'           => ZEYData::class,
        'lf'            => LFData::class,
        'hyjy'          => HYJYData::class,
        'sp|200NYE0103' => SPData::class,
        'sp|200NYE0102' => SPData::class,
        'sp|2036HG02'   => SPData::class,
        'sp|203PMA'     => SPData::class,
        'sp|203GUN'     => SPData::class,
        'sp|203TC6'     => SPData::class,
        'sp|2046TU'     => SPCJData::class,
        'sp|20253S'     => SPData::class,
        'sp|204G5E'     => SPData::class,
        'sp|200F5803'   => SPData::class,
        'sp|204DN003'   => SPData::class,
        'sp|200RI9'     => SPData::class,
        'sp|204KGO'     => SPData::class,
        'sp|205CFZ'     => SPData::class,
        'sp|205CHR'     => SPData::class,
        'hytOrg'        => HytOrgData::class,
        'chtx'          => CHTXData::class,
        'pckj'          => PCKJData::class,
        'rq'            => RQData::class,
        'zlgx'          => ZLGXData::class,
        'xm'            => XMData::class,
        'jf'            => JFData::class,
        'sffy'          => SFFYData::class,
        'sffy_simple'   => SFFYSIMPLEData::class,
        'ajsw'          => AJSWData::class,
        'ygy'           => YGYData::class,
        'ciec'          => CIECData::class,
        'zz'            => ZZData::class,
        'desp|2H8BBD'   => DESP2H8BBDData::class,
        'desp|2A6LA9'   => DESP2A6LA9Data::class,
        'yxt'           => YXTData::class,
        'hr'            => HRData::class,
        'gbdw'          => GBDWData::class,
        'desp|2C637M'   => DESP2C637MData::class,
        'my'            => MYData::class,
        'zj'            => ZJData::class,
        'hll'           => HLLData::class,
        'wsy'           => WSYData::class,
        'bq'            => BQData::class,
        'zzah'          => ZZ_AHData::class,
        'zzbj'          => ZZ_BJData::class,
        'zztj'          => ZZ_TJData::class,
        'cft'           => CFTData::class,
        'mb'            => MBData::class,
        'ad'            => AdData::class,
        'lt'            => LTData::class,
        'mtl'           => MTLData::class,
        'cfhy'          => CfhyData::class,
        'ylz'           => YLZData::class,
        'ylzZj'         => YLZData::class,
        'zjka'          => ZJKAData::class,
        'jtxy'          => JTXYData::class,
        'mycf'          => MYCFData::class,
        'shengman'      => SHENGMANData::class,
        'ry'            => RYData::class,
        'ybt'           => YBTData::class,
        'hz'            => HZData::class,
        'rrs'           => RRSData::class,
        'xyds'          => XYDSData::class,
        'qdmy'          => QDMYData::class,
        'ky'            => KYData::class,
        'hsl'           => HSLData::class,
        'wzyt'          => WZYTData::class,
    ];
    protected     $oilData;
    protected     $cacheId;
    protected     $nameAbbreviation;
    protected     $tagList;
    protected     $platformName;
    protected     $initOilData;

    protected $stationCacheEncrypt = true;

    /**
     * PushOilDataBasicJob constructor.
     * @param array $oilData
     * @param string $nameAbbreviation
     * @throws Throwable
     */
    public function __construct(array $oilData, string $nameAbbreviation)
    {
        $this->initOilData = $oilData;
        $this->nameAbbreviation = $nameAbbreviation;
        $this->platformName = DockingPlatformInfoData::getPlatformNameByNameAbbreviation($this->nameAbbreviation);
        // 如果平台名称为空，属慧加油一类用油平台需特殊处理
        if (empty($this->platformName)) {
            $authInfo = AuthInfoData::getAuthInfoByRoleCode(
                explode(
                    "|",
                    $this->nameAbbreviation
                )[1] ?? '',
                true
            );
            $this->platformName = DockingPlatformInfoData::getPlatformNameByNameAbbreviation(
                explode(
                    '_',
                    $authInfo['name_abbreviation']
                )[0]
            );
        }
        $this->oilData = $oilData;
        $this->cacheId = $this->oilData['id'];
        $this->checkStationPushRule();

        try {
            OilMappingCommonData::getOilStationData($this->oilData);
            OilMappingCommonData::getSpecialPrice($this->oilData);
            if (method_exists(self::$dataPrepareClassMapping[$this->nameAbbreviation], 'getOilStationData')) {
                self::$dataPrepareClassMapping[$this->nameAbbreviation]::getOilStationData($this->oilData);
            }

            OilMappingCommonData::filterCommonField($this->oilData);
        } catch (Throwable $exception) {
            Log::handle("OIL STATION convert has exception.", [
                'data'      => $this->oilData,
                'exception' => $exception
            ], $this->platformName, 'oil_station_data', 'warning');
            throw $exception;
        }

        $this->checkRepeatPushOilStation();
        $this->tagList = $this->oilData['tag_list'] ?? [];
        unset($this->oilData['tag_list']); //删除标签以免影响油站变化的判断，目前对接平台不需要标签
    }

    public function handle()
    {
        try {
            $result = $this->push();
            $this->setCurrentOilStation();
            StationPushRecordData::handle(
                $this->platformName,
                $this->initOilData['id'],
                $this->initOilData['station_name'],
                1,
                date('Y-m-d H:i:s'),
                [
                    'push_data'   => $this->oilData,
                    'push_result' => $result
                ]
            );
            $this->callbackPushResult([
                'push_status' => 3,
            ]);
        } catch (Throwable $throwable) {
            StationPushRecordData::handle(
                $this->platformName,
                $this->initOilData['id'],
                $this->initOilData['station_name'],
                2,
                date('Y-m-d H:i:s'),
                [
                    'push_data'   => $this->oilData,
                    'push_result' => $throwable->getMessage()
                ]
            );
            $this->callbackPushResult([
                'push_status' => 2,
            ]);
        }
    }

    /**
     * @throws Exception
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-25 21:05
     */
    public function checkStationPushRule()
    {
        $pushRule = StationPushConditionData::getPushRuleByNameAbbreviation($this->nameAbbreviation);
        if (!empty($pushRule)) {
            $pushRule = json_decode($pushRule[0], true);
            $pushRule = checkIsAssocArray($pushRule) ? [$pushRule] : $pushRule;
            foreach ($pushRule as $value) {
                switch ($value['type']) {
                    case 1:

                        if (empty($value['val'])) {
                            break;
                        }
                        if (!in_array($this->oilData['pcode'], $value['val'])) {
                            Log::handle("The station doesn't meet the push conditions of the platform", [
                                'id'       => $this->oilData['id'],
                                'pcode'    => $this->oilData['pcode'],
                                'pushType' => $value['type'],
                                'pushRule' => $value['val'],
                            ], $this->platformName, 'oil_station_data', 'warning');
                            $this->tries = 0;
                            $this->callbackPushResult([
                                'push_status' => 4,
                            ]);
                            throw new Exception(
                                "The station doesn't meet the push conditions of the platform", 5000129
                            );
                        }
                        break;
                    case 2:

                        if ($this->oilData['trade_type'] == 5) {
                            $repeatTradeType = array_intersect(
                                $value['val'],
                                array_keys(
                                    CommonData::TRADE_TYPE_ENUM
                                )
                            );
                            $this->oilData['trade_type'] = $repeatTradeType[0] ?? '';
                            if (count($repeatTradeType) > 1 || empty($value['val'])) {
                                $this->oilData['trade_type'] = 1;
                            }
                        }
                        if (!empty($value['val']) and !in_array($this->oilData['trade_type'], $value['val'])) {
                            Log::handle("The station doesn't meet the push conditions of the platform", [
                                'id'         => $this->oilData['id'],
                                'trade_type' => $this->oilData['trade_type'],
                                'pushType'   => $value['type'],
                                'pushRule'   => $value['val'],
                            ], $this->platformName, 'oil_station_data', 'warning');
                            $this->tries = 0;
                            $this->callbackPushResult([
                                'push_status' => 4,
                            ]);
                            throw new Exception(
                                "The station doesn't meet the push conditions of the platform", 5000129
                            );
                        }
                        break;
                    case 3:

                        if (empty($value['val'])) {
                            break;
                        }
                        if (!in_array($this->oilData['oil_unit'], $value['val'])) {
                            Log::handle("The station doesn't meet the push conditions of the platform", [
                                'id'       => $this->oilData['id'],
                                'oil_unit' => $this->oilData['oil_unit'],
                                'pushType' => $value['type'],
                                'pushRule' => $value['val'],
                            ], $this->platformName, 'oil_station_data', 'warning');
                            $this->tries = 0;
                            $this->callbackPushResult([
                                'push_status' => 4,
                            ]);
                            throw new Exception(
                                "The station doesn't meet the push conditions of the platform", 5000129
                            );
                        }
                        break;
                }
            }
        }
    }

    /**
     * 检查油站信息变化,如无变化则不推送有变化推送
     * ---------------------------------------------------
     * @throws Exception
     * @since 2019-07-15 14:53
     * <AUTHOR> <<EMAIL>>
     */
    public function checkRepeatPushOilStation()
    {
        $oilStationCacheCheckKey = self::OIL_STATION_CACHE_CHECK . "_" . $this->nameAbbreviation;
        $data = app('redis')->hget($oilStationCacheCheckKey, $this->cacheId);
        if ($this->stationCacheEncrypt) {
            if (md5(json_encode($this->oilData)) == $data) {
                Log::handle("OIL STATION doesn't have change", [
                    'stationId' => $this->cacheId,
                    'data'      => md5(json_encode($this->oilData)),
                    'cacheData' => $data
                ], $this->platformName, 'oil_station_data', 'warning');
                $this->callbackPushResult([
                    'push_status' => 4,
                ]);
                throw new Exception("The station doesn't have change for the platform", 5000129);
            }
            return;
        }
        if ($this->oilData == json_decode($data, true)) {
            Log::handle("OIL STATION doesn't have change", [
                'data'      => $this->oilData,
                'cacheData' => $data
            ], $this->platformName, 'oil_station_data', 'warning');
            $this->callbackPushResult([
                'push_status' => 4,
            ]);
            throw new Exception("The station doesn't have change for the platform", 5000129);
        }
    }

    public function setCurrentOilStation()
    {
        $oilStationCacheCheckKey = self::OIL_STATION_CACHE_CHECK . "_" . $this->nameAbbreviation;
        if ($this->stationCacheEncrypt) {
            app('redis')->hset($oilStationCacheCheckKey, $this->cacheId, md5(json_encode($this->oilData)));
            return;
        }
        app('redis')->hset($oilStationCacheCheckKey, $this->cacheId, json_encode($this->oilData));
    }

    public static function checkWorkerExists(string $nameAbbreviation): array
    {
        if (isset(self::$dataPrepareClassMapping[$nameAbbreviation])) {
            return [
                $nameAbbreviation => self::$dataPrepareClassMapping[$nameAbbreviation]
            ];
        }
        foreach (self::$dataPrepareClassMapping as $k => $v) {
            if (strpos($k, $nameAbbreviation) !== false) {
                return [
                    $k => $v,
                ];
            }
        }
        return [];
    }

    public function callbackPushResult(array $callbackData)
    {
        if (!empty($this->initOilData['task_id'])) {
            $callbackData['station_task_id'] = $this->initOilData['task_id'];
            $callbackData['station_id'] = $this->initOilData['id'];
            Queue::push(new CallbackPushResult($callbackData), '', 'callback_push_result_queue');
        }
    }

    abstract public function push(): ?array;
}
