<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/11/1
 * Time: 14:43
 */

namespace App\Servitization;

use App\Library\Helper\Common;
use App\Library\Helper\KafkaLog;
use App\Models\OrderModel;
use G7\Tracing\TraceContext;
use Illuminate\Support\Facades\Log;

class Foss
{

    /**
     * 需转换为int类型的字段
     * @var array
     */
    static $intTypeFields = ['contact_id', 'addr_id', 'org_id', 'page_no', 'num',
                             'page_size', 'assign_num', 'id', 'assign_detail_id'];

    /**
     * 需转换的分页参数名
     * @var array
     */
    static $pageParamsFields = ['pageNo' => 'page_no', 'pageSize' => 'page_size'];

    /**
     * 需转换的分页参数名
     * @var array
     */
    static $floatTypeFields = ['moneyGe', 'moneyLe', 'assign_num_ge', 'assign_num_le', 'money_total_ge', 'money_total_le', 'trade_moneyGe', 'trade_moneyLe',
                               'card_remain_ge', 'jifen_total', 'card_remain_le', 'assign_jifen', 'reserve_remain_ge', 'reserve_remain_le', 'score_min', 'score_max'];

    static $orgService = NULL;


    private $config;

    public function __construct()
    {
        $this->config = config('foss');

    }

    public function createSign(array $postData)
    {
        $params = $postData;
        $params['app_key'] = $this->config['app_key'];
        $params['timestamp'] = date("Y-m-d H:i:s");
        $params['format'] = 'json';
        $params['data'] = urlencode(json_encode($params['data']));
        $params['method'] = $this->config['requestPrefix'] . $params['method'];
        $_params = self::paraFilter($params);
        $postStr = Common::createLinkString($_params);
        $params['sign'] = self::md5Sign($postStr);

        return $params;
    }


    /**
     * 除去数组中的空值和签名参数
     * @param $para
     * @return array
     */
    static protected function paraFilter($para)
    {
        $para_filter = [];
        foreach ($para as $key => $val) {
            if (in_array($key, ['method', 'app_key', 'app_secret', 'timestamp', 'format', 'sign', 'return_url', 'data'])) {
                $para_filter[$key] = $val;
            }
        }

        return self::argSort($para_filter);
    }

    /**
     * 对数组排序
     * @param $para
     * @return mixed
     */
    static protected function argSort($para)
    {
        ksort($para);
        reset($para);

        return $para;
    }

    /**
     * 签名字符串
     * @param $preStr
     * @return string
     */
    protected function md5Sign($preStr)
    {
        $key = $this->config['app_secret'];
        $preStr = $key . $preStr . $key;
        return strtoupper(md5($preStr));
    }

    /**
     * 执行
     * @param $type
     * @param $args
     * @param callable|NULL $callback
     * @param bool $addKafkaLog
     * @return mixed
     */
    protected function exec($type = NULL, $args, callable $callback = NULL, $addKafkaLog = false)
    {
        $params = $args;
        //将要求int类型的入参转换
        if (isset($params['data']) && $params['data']) {
            $params['data'] = self::convertFiledType($params['data']);
            if ($type == 'get') {
                $params['data'] = self::clearBlankParams($params['data']);
            }
        }
        try {
            $requestData = self::createSign($params);

            $startTime = microtime(true) * 1000;
            $data = Common::request($type, $this->config['apiUrl'], 60, $requestData);
            $cost = microtime(true) * 1000 - $startTime;
            Log::info('请求foss,runtime:'.$cost.',入数:' . json_encode($requestData, JSON_UNESCAPED_UNICODE) . '返回结果:' . $data);
            $data = json_decode($data, true);
            if ($addKafkaLog) {
                KafkaLog::thirdOperationLog($params['method'], 'foss', $params, $data, $cost);
            }
            $errCode = array_get($data, "code", 5140001);
            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new \RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', $errCode);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return $callback != NULL ? $callback($result) : $result;
        } catch (\Exception $e) {
            KafkaLog::thirdOperationLog($params['method'], 'foss', $params, $e->getMessage());
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    //使用curl,请求foss
    protected function execByCurl($type = 'post', $args)
    {
        $params = $args;
        //将要求int类型的入参转换
        if (isset($params['data']) && $params['data']) {
            $params['data'] = self::convertFiledType($params['data']);
            if ($type == 'get') {
                $params['data'] = self::clearBlankParams($params['data']);
            }
        }
        try {
            $requestData = self::createSign($params);

            $domain = $this->config['apiUrl'];
            if( $args['method'] == "g7s.trade.tradePayFosGms" ){
                $domain .= "?pay_no=".$args['data']['id'];
            }
            Log::info('execByCurl,domain:'.$domain.',参数:' . json_encode($requestData, JSON_UNESCAPED_UNICODE));
            $startTime = microtime(true) * 1000;
            $data = $this->curlPostFoss($domain, $requestData,$type,60);
            $cost = microtime(true) * 1000 - $startTime;
            Log::info('execByCurl,runtime:'.$cost.',参数:' . json_encode($requestData, JSON_UNESCAPED_UNICODE) . '返回结果:' . $data);
            $data = json_decode($data, true);
            $errCode = array_get($data, "code", 5140001);
            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new \RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', $errCode);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return  $result;
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    private function curlPostFoss($url, $params = [], $type = 'post', $timeout = 15, $failedTries = 0)
    {
        try {
            $ch = curl_init();
            if (false !== strpos($url, 'https://')) {
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);  // 跳过证书检查
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);  // 从证书中检查SSL加密算法是否存在
            }

            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            //curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);

            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);     // 设置超时限制防止死循环
            curl_setopt(
                $ch,
                CURLOPT_HTTPHEADER,
                Common::getTraceInjectHeaders(true)
            );
            $output = curl_exec($ch);
            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $output = strlen($output) > $headerSize ? substr($output, $headerSize) : $output;
            Log::info('curl-info:入参：' . json_encode($params, JSON_UNESCAPED_UNICODE) . ',curl-info:' . json_encode(curl_getinfo($ch)));

            $curlInfo = curl_getinfo($ch);
            $curlErrorMsg = curl_error($ch);
            $curlErrorNo = curl_errno($ch);
            Log::info(
                'curl-info:入参：' . json_encode($params, JSON_UNESCAPED_UNICODE) . ',curl-info:' . json_encode(
                    $curlInfo
                )
            );
            curl_close($ch);
            if ($curlInfo['http_code'] == 0 and $failedTries < 3) {
                return $this->curlPostFoss($url, $params, $type, $timeout, $failedTries + 1);
            }

            if ($curlErrorNo != 0) {
                if ($params['method'] == 'g7s.trade.tradePayFosGms') {
                    (new FeiShu())->sendOrderPayRetry([
                        'order_id' => json_decode(urldecode($params['data']), true)['id'] ?? '',
                        'cycle'    => $failedTries + 1,
                        'result'   => $curlErrorMsg,
                    ]);
                }
                throw new \RuntimeException($curlErrorMsg, $curlErrorNo);
            }
            Log::info('curlFoss,结果:' . json_encode($params, JSON_UNESCAPED_UNICODE) . '返回结果:' . $output);
            return $output;
        } catch (\Exception $exception) {
            Log::info('curlFossError:' . json_encode($exception->getMessage()) . ',返回结果:' . $exception->getCode());
            throw new \RuntimeException($exception->getMessage(), $exception->getCode());
        }
    }


    /**
     * 参数类型转换
     * @param array $params
     * @return array
     */
    static public function convertFiledType(array $params)
    {
        if ($params) {
            foreach ($params as $k => &$v) {

                if (in_array($k, array_keys(self::$pageParamsFields))) {
                    $params[self::$pageParamsFields[$k]] = $v;
                    unset($params[$k]);
                }

                if ($v != '' && in_array($k, self::$intTypeFields)) {
                    $v = intval($v);
                }

                if ($v != '' && in_array($k, self::$floatTypeFields)) {
                    $v = floatval($v);
                }
            }
        }

        return $params;
    }

    /**
     * 去除空参数
     * @param array $params
     * @return array
     */
    static public function clearBlankParams(array $params)
    {
        if ($params) {
            foreach ($params as $k => &$v) {
                //@todo 待观察
                if ($v === '') {
                    unset($params[$k]);
                }
            }
        }

        return $params;
    }

    //调用扣费接口
    public function deductMoney($params)
    {
        $data = self::exec('post', [
            'method' => 'gas.assign.gasDeductMoney',
            'data'   => $params
        ]);
        return $data;
    }

    //推送加油流水
    public function pushTrades($params)
    {
        $data = self::exec('post', [
            'method' => 'gas.trade.syncTradeData',
            'data'   => $params
        ]);
        return $data;
    }

    //调用卡设置接口
    public function cardSet($params)
    {
        $data = self::exec('post', [
            'method' => 'gas.update.cardset',
            'data'   => $params
        ]);
        return $data;
    }

    /**
     * 获取采购渠道对应的站点编码
     *
     * @param $params
     * @return mixed
     */
    public function getStationCodeByChannel($params)
    {
        $data = self::exec('get', [
            'method' => 'g7s.supplier.getStationCodes',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * 查询时间配置接口
     * @param $params
     * @return mixed
     */
    public function getStationTimeLimit($params)
    {
        $data = self::exec('post', [
            'method' => 'g7s.limit.getStationTimeLimit',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * 查询运营商是否平台
     * @param $params
     * @return mixed
     */
    public function checkLimitByPcode($params)
    {
        $data = self::exec('post', [
            'method' => 'g7s.limit.checkLimitByPcode',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * 获取油站运营商配置
     *
     * @param $params
     * @return mixed
     */
    public function getPcodeConf($params)
    {
        $data = self::execByCurl('post', [
            'method' => 'g7s.station.getCanPcode',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * 查询卡余额附带油站限制
     *
     * @param $params
     * @return mixed
     */
    public function getStationLimit($params)
    {
        $data = self::exec('post', [
            'method' => 'gas.org_account.account_balance',
            'data' => $params
        ]);

        return $data;
    }

    /**
     * FOSS扣款
     *
     * @param $params
     * @return array|mixed
     */
    public function tradePay($params)
    {
        if (empty($params)) {
            return [];
        }

        // 参数转换
        $payParams = [
            'stream_no'           => $params['order_no'],
            'id'                  => $params['order_no'],
            'client_order_id'     => $params['third_order_no'] ?? '',
            'card_no'             => $params['card_no'],
            'sale_type'           => $params['sale_type'],
            'unit'                => $params['unit'],
            'money'               => $params['pay_money'],
            'price'               => $params['pay_unit_price'],
            'oil_num'             => $params['oil_num'],
            'oil_name'            => $params['oil_name'],
            'oil_name_val'        => $params['oil_type_name'],
            'oil_time'            => $params['oil_time'],
            'pcode'               => $params['pcode'],
            'orgcode'             => $params['orgcode'],
            'provice_code'        => $params['province_code'],
            'provice_name'        => $params['province_name'],
            'city_code'           => $params['city_code'],
            'city_name'           => $params['city_name'],
            'open_check_password' => $params['open_check_password'],
            'station_id'          => $params['station_id'] ?? '',
            'station_name'        => $params['station_name'] ?? '',
            'station_code'        => $params['station_code'] ?? '',
            'service_money'       => $params['service_money'] ?? 0,
            'trade_place'         => $params['trade_place'] ?? '',
            'trade_address'       => $params['trade_address'] ?? '',
            'imgurl'              => $params['imgurl'] ?? '',
            'truck_no'            => $params['truck_no'] ?? '',
            'drivername'          => $params['drivername'] ?? '',
            'drivertel'           => $params['drivertel'] ?? '',
            'rebate_grade'        => $params['rebate_grade'] ?? '',
            'xpcode_pay_price'    => $params['xpcode_pay_price'] ?? '',
            'xpcode_pay_money'    => $params['xpcode_pay_money'] ?? '',
            'truename'            => $params['truename'] ?? '',
            'card_password'       => $params['card_password'] ?? '',
            'remark'              => $params['remark'] ?? '',
            'trade_from'          => $params['trade_from'] ?? '',
            'real_oil_num'        => $params['real_oil_num'],
            'mac_price'           => $params['mac_price'] ?? 0,
            'mac_amount'          => $params['mac_amount'] ?? 0,
            'price_id'            => $params['price_id'] ?? '',
            'original_order_id'   => $params['original_order_id'] ?? 0,
            'document_type'       => $params['document_type'] ?? 0,
            'order_type'          => $params['order_type'] ?? OrderModel::ORDER_SALE_TYPE_REFUEL,
            'ocr_truck_no_url'    => $params['ocr_truck_no_url'] ?? '',
            'pay_ext_info'        => $params['pay_ext_info'] ?? [],
        ];

        $data = self::execByCurl('post', [
            'method' => 'g7s.trade.tradePayFosGms',
            'data'   => $payParams,
        ]);

        return $data;
    }

    /**
     * 获取机构退款流水
     *
     * @param $params
     * @return mixed
     */
    public function getOrgRefundTrade($params)
    {
        $data = self::exec('post', [
            'method' => 'g7s.trade.getOrgRefundTrade',
            'data'   => $params
        ]);

        return $data;
    }

    public function chargeOff($orderId)
    {
        // 参数转换
        $params = [
            'api_id' => $orderId,
        ];

        $data = self::exec('post', [
            'method' => 'g7s.trade.chargeOff',
            'data'   => $params,
        ], NULL, []);

        return $data;
    }

    public function getAdditionalOrg()
    {
        // 参数转换
        $params = [
            'no_page'     => 1,
            'config_type' => 101,
            'status'      => 1,
        ];
        return self::exec('post', [
            'method' => 'gas.conf.customOrder',
            'data'   => $params,
        ], null, true);
    }

    /**
      *  获取副卡交易信息
      *  <AUTHOR> yanglei <<EMAIL>>
      *  @since  :2022年8月11日
      *
     */
    public function getViceInfoByOrderIds($params)
    {
        $data = self::exec('post', [
            'method' => 'g7s.trade.getViceInfoByOrderIds',
            'data'   => $params
        ]);

        return $data;
    }

    /**
     * @param $params
     * @return mixed
     */
    public function determineOrderIsDeductionFailed($params)
    {
        return self::exec('post', [
            'method' => 'g7s.trade.determineOrderIsDeductionFailed',
            'data'   => $params
        ]);
    }

    public function checkOrderIsReceipt($params)
    {
        return self::exec('post', [
            'method' => 'gas.check.tradeIsReceipt',
            'data'   => $params
        ]);
    }

    public function cancelTradePay($params)
    {
        return self::execByCurl('post', [
            'method' => 'g7s.trade.cancelTradePay',
            'data' => $params
        ]);
    }

    public function checkPayLimit($params)
    {
        return self::execByCurl('post', [
            'method' => 'g7s.trade.checkSysPayLimit',
            'data' => $params
        ]);
    }

    public function refundAndTradePay($params)
    {
        return self::execByCurl('post', [
            'method' => 'g7s.trade.modifyAndTradePay',
            'data' => $params
        ]);
    }

    public function getSupplierAndOrgInfoByOrderId($params)
    {
        return self::execByCurl('post', [
            'method' => 'g7s.trade.getSupplierAndOrgInfoByOrderId',
            'data' => $params
        ]);
    }

    public function cancelChargeOff($orderId)
    {
        return self::exec('post', [
            'method' => 'g7s.trade.cancelChargeOff',
            'data'   => [
                'api_id' => $orderId,
            ],
        ], NULL, []);
    }

    public function getCompanyListByOrgCode($params)
    {
        return self::execByCurl('post', [
            'method' => 'g7s.oil_pay_company.getListByOrgCode',
            'data' => $params
        ]);
    }
}
