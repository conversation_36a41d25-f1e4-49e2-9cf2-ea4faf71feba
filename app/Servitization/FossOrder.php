<?php


namespace App\Servitization;


use App\Library\Helper\Common;
use Illuminate\Support\Facades\Log;
use phpDocumentor\Reflection\Types\Self_;

class FossOrder
{
    public function __construct()
    {
    }

    /**
     * 交易流水导出
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function exportBusinessLog($params)
    {
        $path = '/api/export/v1/export/exportBusinessLog';

        return self::postRequest($path, $params);
    }

    /**
     * 交易流水列表
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function businessLogPaginate($params)
    {
        $path = '/api/business/v1/businessLog/getBusinessLogListByLimit';

        return self::postRequest($path, $params);
    }

    /**
     * 创建｜编辑补单信息
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function oneCardCreateOrUpdate($params)
    {
        $path = '/api/additional/v1/additional/oneCardCreateOrUpdate';
        return self::postRequest($path, $params);
    }

    /**
     * 审核补单申请
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function oneCardApprove($params)
    {
        $path = '/api/additional/v1/additional/oneCardApprove';
        return self::postRequest($path, $params);
    }

    /**
     * 获取补单详情
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function oneCardAdditionalDetail($params)
    {
        $path = '/api/additional/v1/additional/oneCardAdditionalDetail';
        return self::postRequest($path, $params);
    }

    /**
     * 分页获取补录列表
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function oneCardAdditionalPaginate($params)
    {
        $path = '/api/additional/v1/additional/oneCardAdditionalPaginate';
        return self::postRequest($path, $params);
    }

    /**
     * 获取补录机构白名单
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function additionalOrgFuzzySearch($params)
    {
        $path = '/api/additional/v1/additional/additionalOrgFuzzySearch';
        return self::postRequest($path, $params);
    }

    /**
     * 获取1号卡审核单列表
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function getApprovePaginate($params)
    {
        $path = '/api/business/v1/approve/getApprovePaginate';
        return self::postRequest($path, $params);
    }

    /**
     * 获取1号卡审核单详情
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function getApproveItem($params)
    {
        $path = '/api/business/v1/approve/getApproveItem';
        return self::postRequest($path, $params);
    }

    /**
     * 创建｜编辑1号卡补录申请单
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function addHistoryInsertOrUpdate($params)
    {
        $path = '/api/business/v1/approve/addHistoryInsertOrUpdate';
        return self::postRequest($path, $params);
    }

    /**
     * 创建1号卡退款申请单
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function cancelHistoryInsert($params)
    {
        $path = '/api/business/v1/approve/cancelHistoryInsert';
        return self::postRequest($path, $params);
    }

    /**
     * 1号卡申请单审核通过
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function approvePass($params)
    {
        $path = '/api/business/v1/approve/approvePass';
        return self::postRequest($path, $params);
    }

    /**
     * 1号卡申请单审核驳回
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public static function approveRefuse($params)
    {
        $path = '/api/business/v1/approve/approveRefuse';
        return self::postRequest($path, $params);
    }

    /**
     * 根据订单标识获取订单信息
     * @param $ids
     * @param $master
     * @return mixed
     * @throws \Exception
     */
    public static function batchGetByOrderId($ids, $master=false)
    {
        if (empty($ids))
            return [];

        $param = [
            'order_id'  => implode(',', array_unique($ids)),
            'master'    => $master
        ];

        $path = '/api/services/v1/order/batchGetByOrderId';

        return self::postRequest($path, $param);
    }

    /**
     * POST请求
     *
     * @param $path
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    protected static function postRequest($path, $params = [])
    {
        $url = self::getConfig()['url'].$path;
        try {
            $result = Common::request('POST', $url, $params);
            $result = json_decode($result, true);
        } catch (\Exception $e) {
            Log::error('请求FOSS_ORDER接口返回异常:'.$e->getMessage(), $params);
            throw new \Exception($e->getMessage(), $e->getCode());
        }
        if (array_get($result, 'success', false) == false) {
            throw new \Exception(
                array_get($result, 'msg', '请求FOSS_ORDER接口返回逻辑false'),
                array_get($result, 'code', 403)
            );
        }

        return array_get($result, 'data', []);
    }

    /**
     * GET请求
     *
     * @param $path
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    protected static function getRequest($path, $params = [])
    {
        $url = self::getConfig()['url'].$path;

        try {
            $result = Common::request('GET', $url, $params);
            $result = json_decode($result, true);
        } catch (\Exception $e) {
            Log::error('请求FOSS_ORDER接口返回异常:'.$e->getMessage(), $params);
            throw new \Exception($e->getMessage(), $e->getCode());
        }
        if (array_get($result, 'success', false) == false) {
            throw new \Exception(
                array_get($result, 'msg', '请求FOSS_ORDER接口返回逻辑false'),
                array_get($result, 'code', 403)
            );
        }

        return array_get($result, 'data', []);
    }

    /**
     * 获取foss-order配置
     *
     * @return \Illuminate\Config\Repository|mixed
     */
    protected static function getConfig()
    {
        return config('fossOrder');
    }
}
