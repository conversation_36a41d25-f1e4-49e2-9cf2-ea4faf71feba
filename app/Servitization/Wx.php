<?php

namespace App\Servitization;


use App\Library\Helper\Common;
use App\Library\Helper\KafkaLog;
use Illuminate\Support\Facades\Log;
use Throwable;


class Wx
{
    /**
     * @param string $url
     * @param array $params
     * @return array
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/15 11:24 上午
     */
    public static function requestWithAccessToken(string $url, $params = []): array
    {
        $response = null;

        try {

            $retryCycle = 0;
            loop:

            $parseUrlResult = parse_url($url);
            $queryStringParams = [];
            if (isset($parseUrlResult['query'])) {

                foreach (explode('&', $parseUrlResult['query']) as $param) {

                    $item = explode('=', $param);
                    $queryStringParams[$item[0]] = $item[1];
                }
            }

            $url = $parseUrlResult['scheme'] . '://' . $parseUrlResult['host'] . $parseUrlResult['path'] .
                '?' . http_build_query(array_merge($queryStringParams, [
                    'access_token' => static::getAccessToken(),
                ]));

            $response = Common::requestJson($url, $params);
            KafkaLog::thirdOperationLog($url, 'wechat', $params, $response);
            $result = json_decode($response, true);
            if (array_has($result, ['errcode']) and in_array($result['errcode'], [
                    '42001',
                    '40001',
                    '40014',
                ]) and $retryCycle == 0) {

                app("redis")->del(["wx_access_token"]);
                $retryCycle++;
                goto loop;
            }

            return $result;
        } catch (Throwable $exception) {
            KafkaLog::thirdOperationLog($url, 'wechat', $params, $exception->getMessage());
            Log::error("请求微信发生异常,输入:" . http_build_query(func_get_args()) .
                "输出:{$response}失败原因:" . $exception->getFile() . $exception->getLine() .
                $exception->getMessage());
            throw $exception;
        }
    }

    /**
     * @return string
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2020/9/15 11:04 上午
     */
    public static function getAccessToken(): string
    {
        $url = env("WX_TOKEN_URL");
        $appId = env("GASUSER_WX_APPID");
        $secret = env("GASUSER_WX_SECRET");
        $response = null;

        $params = [
            'grant_type' => 'client_credential',
            'appid'      => $appId,
            'secret'     => $secret,
        ];

        try {
            $redis = app('redis');
            $accessToken = $redis->get("wx_access_token");
            if (!empty($accessToken)) {
                return $accessToken;
            }
            $response = Common::request("get", $url,  $params);
            $result = json_decode($response, true);

            KafkaLog::thirdOperationLog($url, 'wechat', $params, $result);

            app('redis')->setex("wx_access_token", $result['expires_in'] - 10,
                $result['access_token']);
            return $result['access_token'];
        } catch (Throwable $exception) {
            KafkaLog::thirdOperationLog($url, 'wechat', $params, $exception->getMessage());

            Log::error("获取微信access_token失败,输入:" . http_build_query([
                    'appId'  => $appId,
                    'secret' => $secret
                ]) . "输出:{$response}失败原因:" . $exception->getFile() . $exception->getLine() .
                $exception->getMessage());
            throw $exception;
        }
    }
}
