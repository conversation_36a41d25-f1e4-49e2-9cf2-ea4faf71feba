<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/11/1
 * Time: 14:43
 */
namespace App\Servitization;
use App\Http\Defines\CommonError;
use App\Library\Helper\Common;
use App\Library\Helper\KafkaLog;
use Illuminate\Support\Facades\Log;
class Gas
{

    /**
     * 需转换为int类型的字段
     * @var array
     */
    static $intTypeFields = ['contact_id', 'addr_id', 'org_id', 'page_no', 'num',
        'page_size', 'assign_num', 'id', 'assign_detail_id'];

    /**
     * 需转换的分页参数名
     * @var array
     */
    static $pageParamsFields = ['pageNo' => 'page_no', 'pageSize' => 'page_size'];

    /**
     * 需转换的分页参数名
     * @var array
     */
    static $floatTypeFields = ['moneyGe', 'moneyLe', 'assign_num_ge', 'assign_num_le', 'money_total_ge', 'money_total_le', 'trade_moneyGe', 'trade_moneyLe',
        'card_remain_ge', 'jifen_total', 'card_remain_le', 'assign_jifen', 'reserve_remain_ge', 'reserve_remain_le', 'score_min', 'score_max'];

    static $orgService = NULL;


    private $config;

    public function __construct()
    {
        $this->config = config('gas');
    }

    public function createSign(array $postData)
    {
        $params = $postData;
        $params['app_key'] = $this->config['app_key'];
        $params['timestamp'] =  date("Y-m-d H:i:s");
        $params['format'] = 'json';
        $params['data'] = json_encode($params['data']);
        $params['method'] = $this->config['requestPrefix'] . $params['method'];
        $_params = self::paraFilter($params);
        $postStr = Common::createLinkString($_params);
        $params['sign'] = self::md5Sign($postStr);

        return $params;
    }



    /**
     * 除去数组中的空值和签名参数
     * @param $para
     * @return array
     */
    static protected function paraFilter($para)
    {
        $para_filter = [];
        foreach ($para as $key => $val) {
            if (in_array($key, ['method', 'app_key', 'app_secret', 'timestamp', 'format', 'sign', 'return_url', 'data'])) {
                $para_filter[$key] = $val;
            }
        }

        return self::argSort($para_filter);
    }

    /**
     * 对数组排序
     * @param $para
     * @return mixed
     */
    static protected function argSort($para)
    {
        ksort($para);
        reset($para);

        return $para;
    }

    /**
     * 签名字符串
     * @param $preStr
     * @return string
     */
    protected function md5Sign($preStr)
    {
        $key = $this->config['app_secret'];
        $preStr = $key . $preStr . $key;
        return strtoupper(md5($preStr));
    }

    /**
     * 执行
     * @param $type
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    protected function exec($type = NULL, $args, callable $callback = NULL,$timeout = 15)
    {
        $params = $args;
        try {
            $requestData = self::createSign($params);
            $data = Common::request($type, $this->config['apiUrl'],$timeout, $requestData);
            Log::info('请求gas,参数:' . json_encode($requestData, JSON_UNESCAPED_UNICODE) . '返回结果:'. $data);
            $data = json_decode($data, true);
            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new \RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', $data['code']);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return $callback != NULL ? $callback($result) : $result;
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }

    protected function gasExec($type = NULL, $args, callable $callback = NULL,$timeout = 15, $addKafkaLog = false)
    {
        $params = $args;
        try {
            $requestData = self::createSign($params);
            if($type == 'get'){
                $getUrl = $this->config['apiUrl']."/index.php?".http_build_query($requestData);

                $startTime = microtime(true)*1000;
                $data = Common::GasGetHttp($getUrl,$requestData);
                $cost = microtime(true)*1000 - $startTime;
            }elseif ($type == 'post'){
                $apiUrl = $this->config['apiUrl']."/index.php?method=".$params['method'];

                $startTime = microtime(true)*1000;
                $data = Common::GasPostHttp($apiUrl, $requestData,$timeout);
                $cost = $startTime = microtime(true)*1000 - $startTime;
            }else{
                throw new \RuntimeException('',CommonError::HTTP_REQUEST);
            }
            if ($addKafkaLog) {
                KafkaLog::thirdOperationLog($params['method'],'gas', $params, $data, $cost);
            }
            Log::info('请求gas,参数:' . json_encode($requestData, JSON_UNESCAPED_UNICODE) . '返回结果:'. var_export($data,true));
            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new \RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', CommonError::GAS_ERROR);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return $callback != NULL ? $callback($result) : $result;
        } catch (\Exception $e) {
            KafkaLog::thirdOperationLog($params['method'],'gas', $params, $e->getMessage());
            throw new \RuntimeException($e->getMessage(), CommonError::GAS_ERROR);
        }
    }

    /**
     * 参数类型转换
     * @param array $params
     * @return array
     */
    static public function convertFiledType(array $params)
    {
        if ($params) {
            foreach ($params as $k => &$v) {

                if (in_array($k, array_keys(self::$pageParamsFields))) {
                    $params[self::$pageParamsFields[$k]] = $v;
                    unset($params[$k]);
                }

                if ($v != '' && in_array($k, self::$intTypeFields)) {
                    $v = intval($v);
                }

                if ($v != '' && in_array($k, self::$floatTypeFields)) {
                    $v = floatval($v);
                }
            }
        }

        return $params;
    }

    /**
     * 去除空参数
     * @param array $params
     * @return array
     */
    static public function clearBlankParams(array $params)
    {
        if ($params) {
            foreach ($params as $k => &$v) {
                //@todo 待观察
                if ($v === '') {
                    unset($params[$k]);
                }
            }
        }

        return $params;
    }

    //检查站点限制
    public function stationLimit($params)
    {
        $data = self::gasExec('post', [
            'method' => 'gas.api.getNoOneCardOrgMoney',
            'data'   => $params
        ]);
        return $data;
    }

    //调用gas下单接口
    public function createTrades($params)
    {
        $data = self::gasExec('post', [
            'method' => 'gas.api.costAmountCharge',
            'data'   => $params
        ],null,60);
        return $data;
    }

    //获取当前价格
    public function getNowPrice($params)
    {
        $data = self::gasExec('post', [
            'method' => 'gas.api.getSmartPriceList',
            'data'   => $params
        ],null,60);
        return $data;
    }

    //支付完成待支付订单
    public function finishTrades($params)
    {
        $data = self::gasExec('post',[
            'method' => 'gas.external.payTrade',
            'data'   => $params
        ],null,60);
        return $data;
    }

    //获取单价
    public function getPrice($params)
    {
        $data = self::gasExec('post',[
            'method' => 'gas.api.getSmartPriceList',
            'data'   => $params
        ],null,60);
        return $data;
    }

    //车主邦预支付订单
    public function placeOrder($params)
    {
        $data = self::gasExec('post',[
            'method' => 'gas.api.placeOrder',
            'data'   => $params
        ],null,60);
        return $data;
    }

    //支付待支付订单
    public function submitOrder($params)
    {
        $data = self::gasExec('post',[
            'method' => 'gas.api.payOrder',
            'data'   => $params
        ],null,60);
        return $data;
    }

    //G7能源账户密码错误5次,账户锁定后,推送Foss
    public function editCard2Foss($params)
    {
        $data = self::gasExec('post',[
            'method' => 'gas.api.sendCardInfoToGos',
            'data'   => $params
        ],null,60);
        return $data;
    }

    /**
     * 撤销(作废)加油流水
     *
     * @param $params
     * @return mixed|void
     */
    public function makeOilRecordVoid($params)
    {
        $data = self::gasExec('post', [
            'method' => 'api.cost.makeOilRecordVoid',
            'data' => $params
        ], null, 60, true);

        return $data;
    }

    /**
     * G7能源账户-补录加油流水
     *
     * @param $params
     * @return mixed|void
     */
    public function makeNewOilRecord($params)
    {
        $data = self::gasExec('post', [
            'method' => 'api.cost.makeNewOilRecord',
            'data' => $params,
        ], null, 60, true);

        return $data;
    }

    /**
     * 向gas推消息
     *
     * @param $params
     * @return mixed|void
     */
    public function pushMsg($params)
    {
        $data = self::gasExec('post', [
            'method' => 'api.adapter.sendWsMessage',
            'data' => $params,
        ], null, 60, true);

        return $data;
    }

    /**
     * 验证车辆卡白名单
     * @param $mobile
     * @param $viceNo
     * @return array
     */
    public function checkTruckCardTake($mobile, $viceNo)
    {
        $data = self::gasExec('post', [
            'method' => 'gas.api.checkTruckCardTake',
            'data' => [
                'driver_phone' => $mobile,
                'card_no' => $viceNo,
            ],
        ], null, 60, true);

        return $data;
    }
}
