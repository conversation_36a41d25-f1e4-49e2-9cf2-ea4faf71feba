<?php


namespace App\Servitization;


use App\Library\Helper\Common;

class Fuel
{
    protected $config;

    public function __construct()
    {
        $this->config = config('fuel');
    }

    /**
     * 获取sso登录后的token
     *
     * @param $params
     * @return mixed
     */
    public function getSsoToken($params)
    {
        $data = self::exec('post', [
            'method' => '/api/user/makeAccessToken',
            'data' => [
                'uid' => array_get($params, 'uid', ''),
                'client' => array_get($params, 'client', ''),
            ]
        ]);

        return $data;
    }

    /**
     * 增加token
     * @Interface modifyAccessToken
     * @param array $params['uid']、$params['client']
     * @return mixed
     * @author: yuanzhi
     * @Time: 2020/9/4   10:38 上午
     */
    public  function modifyAccessToken($params = [])
    {

        return self::exec('post', [
            'method' => '/api/user/modifyAccessToken',
            'data' => [
                'uid' => array_get($params, 'uid', ''),
                'client' => array_get($params, 'client', ''),
                'namespace' => array_get($params, 'namespace', ''),
            ]
        ]);
    }

    /**
     * token过期
     *
     * @param $params
     * @return mixed
     */
    public function dropToken($params)
    {
        return self::exec('post', [
            'method' => '/api/user/deleteAccessToken',
            'data' => [
                'token' => array_get($params, 'token', ''),
            ]
        ]);
    }

    /**
     * token过期
     *
     * @param $params
     * @return mixed
     */
    public function dropTokenByUid($params)
    {
        return self::exec('post', [
            'method' => '/api/user/deleteTokenByUid',
            'data' => $params
        ]);
    }
    /**
     * 执行
     * @param $type
     * @param $args
     * @param callable|NULL $callback
     * @return mixed
     */
    protected function exec($type = NULL, $args, callable $callback = NULL)
    {
        $params = $args;
        //将要求int类型的入参转换

        if (isset($params['data']) && $params['data']) {

            $params['data'] = Foss::convertFiledType($params['data']);
            if ($type == 'get') {
                $params['data'] = Foss::clearBlankParams($params['data']);
            }
        }

        try {
            // 拼接url
            $url = $this->config['host'].array_get($params, 'method', '');

            Common::log('info','记录请求foss-api信息', ['param' => $params, 'url' => $url]);
            $data = Common::requestJson($url, $params['data']);
            $data = json_decode($data, true);
            Common::log('info','记录请求foss-api信息,返回结果', ['result' => $data]);
            //接口code异常时
            if (!$data || !isset($data['code']) || $data['code'] != 0) {
                throw new \RuntimeException(isset($data['msg']) ? $data['msg'] : '操作异常请联系管理员', $data['code']);
            }
            $result = isset($data['data']) ? $data['data'] : $data;
            return $callback != NULL ? $callback($result) : $result;
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), $e->getCode());
        }
    }
}
