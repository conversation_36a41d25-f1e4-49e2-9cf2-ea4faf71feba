<?php


namespace App\Servitization;


use App\Library\Helper\Common;
use Library\Monitor\Falcon;
use Library\Monitor\Report;

class FeiShu
{
    protected $feiShuConfig;

    public function __construct()
    {
        $this->feiShuConfig = config('feishu');
    }

    /**
     * 向油品服务生产环境报警群报警
     *
     * @param $exception
     */
    public function sendExceptionToFeiShu($exception)
    {
        $token = $this->feiShuConfig['token'];
        $extra = [
            '服务:' => env('APP_NAME'),
            '环境:' => App()->environment()
        ];
        Report::sendExceptionToFeishu($exception, $extra, $token);
    }

    public function sendRefundFailed(array $params)
    {
        $text = "退款失败\n";
        $text .= "分类：{$params['category']}\n";
        $text .= "订单号：{$params['order_id']}\n";
        $text .= "油站：{$params['station_name']}\n";
        $text .= "商品：{$params['goods']}\n";
        $text .= "司机支付金额：{$params['pay_money']}\n";
        $text .= "司机机构：{$params['org_name']}\n";
        $text .= "司机卡号：{$params['card_no']}\n";
        $text .= "司机手机号：{$params['driver_phone']}\n";
        $text .= "失败原因：{$params['failed_reason']}\n";
        Falcon::feishu($this->feiShuConfig['trade_refund'], $text);
    }

    public function sendMakeUpFailed(array $params)
    {
        $text = "补录失败\n";
        $text .= "分类：{$params['category']}\n";
        $text .= "订单号：{$params['order_id']}\n";
        $text .= "油站：{$params['station_name']}\n";
        $text .= "商品：{$params['goods']}\n";
        $text .= "司机支付金额：{$params['pay_money']}\n";
        $text .= "司机机构：{$params['org_name']}\n";
        $text .= "司机卡号：{$params['card_no']}\n";
        $text .= "司机手机号：{$params['driver_phone']}\n";
        $text .= "失败原因：{$params['failed_reason']}\n";
        Falcon::feishu($this->feiShuConfig['order_abnormal_change_patch'], $text);
    }

    public function sendOrderPayRetry(array $params)
    {
        $text = "扣费失败重试订单(" . (\App::environment('prod') ? '生产环境' : '测试环境') . ")\n";
        $text .= "订单号：{$params['order_id']}\n";
        $text .= "重试次数：{$params['cycle']}\n";
        $text .= "重试结果：{$params['result']}\n";
        Falcon::feishu($this->feiShuConfig['operating_personnel'], $text);
    }

    public function alarmExpiredSoonTripartiteCoupons(array $params)
    {
        $text = "电子券名称：{$params['coupon_name']}\n";
        $text .= "站点运营商：{$params['supplier_name']}\n";
        $text .= "面额：{$params['face_value']}\n";
        $text .= "到期时间：{$params['end_time']}\n";
        $text .= "温馨提示：请联系司机使用 或 退款\n";
        $receiver = explode(',', config("feishu")['expired_soon_tripartite_coupon_receiver']);
        foreach ($receiver as $value) {
            $nameEmail = explode(":", $value);
            $text .= "<at email=\"" . $nameEmail[1] . "\">" . $nameEmail[0] . "</at>";
        }
        Common::request(
            "POST",
            config("foss_task")['apiUrl'] . 'notify/sendByFeiShu',
            10,
            [
                'title'    => "电子券{$params['days']}天后过期提醒",
                'chat_id'  => config("feishu")['expired_soon_tripartite_coupon_chat_id'],
                'msg_type' => 'card',
                'content'  => $text,
            ]
        );
    }
}