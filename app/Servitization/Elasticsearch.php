<?php
namespace App\Servitization;
use Elasticsearch\ClientBuilder;
class Elasticsearch
{


    /**
     * 创建索引
     * @Interface createIndex
     * @param string $index
     * @param int $number_of_shards
     * @param int $number_of_replicas
     * @return array
     * @author: yuanzhi
     * @Time: 2020/9/14   4:54 下午
     */
    public static function createIndex(string $index, int $number_of_shards, int $number_of_replicas)
    {
        $client = ClientBuilder::create()->build();
        return $client->indices()->create([
            'index' => $index,
            'body' => [
                'settings' => [
                    'number_of_shards' => $number_of_shards,
                    'number_of_replicas' => $number_of_replicas
                ]
            ]
        ]);
    }

    /**
     * ES插入log记录
     * @Interface create
     * @param $index
     * @param $type
     * @param $id
     * @param $body
     * @return array|callable
     * @author: yuanzhi
     * @Time: 2020/9/14   4:50 下午
     */
    public static function create(string $index, string $type,string $id,array $body){
        $client = ClientBuilder::create()->build();
        $params = [
            'index' => $index,
            'type' => $type,
            'id' => $id,
            'body' => $body
        ];

        return $client->index($params);
    }


    /**
     * 按多条件搜索
     * @Interface search
     * @param string $index 索引名称
     * @param string $type 文档类型
     * @param array $body
     * @return array|callable
     * @author: yuanzhi
     * @Time: 2020/9/14   4:41 下午
     */
    public static function search(string $index, string $type, array $body, int $limit = 10, int $offset = 0)
    {
        $host = config('elasticsearch.hosts');
        $client = ClientBuilder::create()->setHosts($host)->build();
//        var_dump(json_encode($body));exit;
        return $client->search([
            'index' => $index,
            'type' => $type,
            'body' => $body,
            'size' => $limit,
            'from' => $offset
        ]);
    }

    /**
     * ES删除文档
     * @Interface delete
     * @param $index
     * @param $type
     * @param $id
     * @return array|callable
     * @author: yuanzhi
     * @Time: 2020/9/14   4:42 下午
     */
    public static function delete(string $index, string $type, string $id)
    {
        $client = ClientBuilder::create()->build();
        return $client->delete([
            'index' => $index,
            'type' => $type,
            'id' => $id
        ]);
    }

    /**
     * 按id搜索
     * @Interface get
     * @param $index
     * @param $type
     * @param $id
     * @return array|callable
     * @author: yuanzhi
     * @Time: 2020/9/14   4:43 下午
     */
    public static function get(string $index, string $type, string $id){
        $client = ClientBuilder::create()->build();
         return $client->get([
            'index' => $index,
            'type' => $type,
            'id' => $id
        ]);
    }
}
