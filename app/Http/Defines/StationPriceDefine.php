<?php

namespace App\Http\Defines;

use App\Library\Request;
use Illuminate\Support\Facades\App;

class StationPriceDefine
{
    //价格范围
    const MIN_PRICE = 2;
    const MAX_PRICE = 25;

    //销售改价，获取desp_id
    static public function getSalerId()
    {
        return Request::get("saler_desp_id",'') ?? '';
    }

    static public function getUserId()
    {
        return Request::get("uid",'').'-name'.Request::get("user_name","")."-header:".json_encode(Request::get("header_info",[]));
    }
}
