<?php
/**
 * Functions description
 * Created by PhpStorm.
 * User: youki
 * Date: 2018/4/11
 * Time: 上午10:02
 */

namespace App\Http\Defines;


use App\Repositories\StationOperatorsRepository;
use App\Servitization\Foss;

class CommonDefine
{
    // G7能源账户机构运营商
    const CARD_NUMBER_ONE = '20003JCP';

    //车主邦运营商
    const PRO_PCODE  = "2000233D";
    const TEST_PCODE = "2000STJC";
    //车主邦运营商
    const CZB_SQ_PRO_SUPPLIER_CODE  = "2000233DKMDLB5";
    const CZB_SQ_TEST_SUPPLIER_CODE = "2000STJC3BC5TK";

    //E站途运营商
    const E_PCODE      = '2000D23K';
    const E_TEST_PCODE = '2000UAT4';

    //固本能源运营商
    const GB_PROD_SUPPLIER_CODE = '2000DJTK22';
    const GB_TEST_SUPPLIER_CODE = '2000JTUSCC';
    //固本能源(天津)运营商
    const GB_TJ_PROD_SUPPLIER_CODE = '2000DJTK2255KACM';
    const GB_TJ_TEST_SUPPLIER_CODE = '2000AT422A';
    //智慧油客运营商
    const ZHYK_PROD_SUPPLIER_CODE = '2000JLUT';
    const ZHYK_TEST_SUPPLIER_CODE = '200033M5';

    //在线支付壳牌
    const GD_SHELL_PCODE      = '200044CD'; //广东壳牌
    const GD_TEST_SHELL_PCODE = '2000LTCS'; //广东壳牌

    const SC_SHELL_PCODE      = '2000L2VM'; //四川壳牌
    const SC_TEST_SHELL_PCODE = '2000K2TC'; //四川壳牌

    const SX_SHELL_PCODE      = '2000JBU5'; //陕西壳牌
    const SX_TEST_SHELL_PCODE = '20003VMM'; //测试陕西壳牌

    const FJ_SHELL_PCODE      = "20008QONJDCS";   //福建壳牌
    const FJ_TEST_SHELL_PCODE = "2000VSVCD4TMLV"; //福建壳牌

    const GS_SHELL_PCODE      = "20008QONTAUTLC"; //港森线上
    const GS_TEST_SHELL_PCODE = "2000DU2U";       //港森测试

    const ZY_PCODE      = '2000CVJJ'; //老吕（找油）线上
    const ZY_TEST_PCODE = '2000KTCD'; //老吕（找油）测试

    const ZY_LNG_PCODE      = '2000JM2M'; //老吕（找油LNG）线上
    const ZY_LNG_TEST_PCODE = '2000TASU'; //老吕（找油LNG）测试

    const HYT_PCODE      = "20008QONL4ST";//惠运通线上
    const HYT_TEST_PCODE = "2000JU53";    //惠运通测试

    // 山高
    const SG_PCODE      = '2000BUB2';
    const SG_TEST_PCODE = '20002HHQ';

    // 宝兑通
    const BDT_PCODE      = '2000MFNH';
    const BDT_TEST_PCODE = '2000MFNH';

    //星油
    const XY_PCODE      = '20008QON4UCTUA';
    const XY_TEST_PCODE = '200044SJ';

    //点滴
    const DD_PCODE      = '2000CD2K';
    const DD_TEST_PCODE = '200052UB';

    // 畅油
    const CY_PCODE      = '2000K3V2JT';
    const CY_TEST_PCODE = '2000CJDK';

    //华商云
    const HSY_PROD_SUPPLIER_CODE = '2000D33VD3';
    const HSY_TEST_SUPPLIER_CODE = '2000MVUA';

    //DT-浙江
    const DT_ZJ_PROD_SUPPLIER_CODE = '20008QONBCCVJA';
    const DT_ZJ_TEST_SUPPLIER_CODE = '2000VSVC3V3A4B';

    const G7_SUPPLIER_CODE = [
        "20008QON"
    ];
    const G7_TEST_SUPPLIER_CODE = [
        "2000VSVC",
        "20003JCP"
    ];

    //上汽复岭
    const SQ_FL_PROD_SUPPLIER_CODE = '20004L52MB';
    const SQ_FL_TEST_SUPPLIER_CODE = '2000VCCCKJ';
    //上汽安吉
    const SQ_AJ_PROD_SUPPLIER_CODE = '20004L52MBJ2K2VC';
    const SQ_AJ_TEST_SUPPLIER_CODE = '2000K35C';
    //上汽安吉四维
    const SQ_AJSW_PROD_SUPPLIER_CODE = '20004L52MBASSSSB';
    const SQ_AJSW_TEST_SUPPLIER_CODE = '2000KSKLU3';
    //上汽新安吉
    const SQ_NAJ_PROD_SUPPLIER_CODE = '20004L52MB2VSUK5';
    const SQ_NAJ_TEST_SUPPLIER_CODE = '2000ATTV3C';
    //上汽直连
    const SQ_ZL_PROD_SUPPLIER_CODE = '20005AVTKT';
    const SQ_ZL_TEST_SUPPLIER_CODE = '2000S2S4';
    //上汽中石化
    const SQ_ZSH_PROD_SUPPLIER_CODE = '2000SJUJMV';
    const SQ_ZSH_TEST_SUPPLIER_CODE = '2000SCCDV4';
    //掌多车
    const ZDC_PROD_SUPPLIER_CODE = '2000LVLMK5';
    const ZDC_TEST_SUPPLIER_CODE = '2000VSVCD2VAUU';
    //星油(新)
    const XYN_PROD_SUPPLIER_CODE = '2000TST3LC';
    const XYN_TEST_SUPPLIER_CODE = '2000TAKTJL';

    //河北中石油
    const HB_TEST_SUPPLIER_CODE = '2000VSVCS2V2VU';
    const HB_PROD_SUPPLIER_CODE = '20008QONSL2AD5';

    //湖北中石油
    const HB1_PROD_SUPPLIER_CODE = '20008QON4S4BAC';
    const HB1_TEST_SUPPLIER_CODE = '2000VSVC4LMD3V';

    //湖北中石油跨界券
    const HB2_PROD_SUPPLIER_CODE = '2000ASJCSU';
    const HB2_TEST_SUPPLIER_CODE = '20003CAMMM';

    //中物流
    const ZWL_PROD_SUPPLIER_CODE = '2000CMSAT2';
    const ZWL_TEST_SUPPLIER_CODE = '2000VSVCTKBLCL';

    //梦驼铃(上游)
    const MTL_SY_PROD_SUPPLIER_CODE = '2000C3J5KL';
    const MTL_SY_TEST_SUPPLIER_CODE = '2000C4C3U4';
    //善宏
    const SH_TEST_SUPPLIER_CODE = '2000CJKJTU';
    const SH_PROD_SUPPLIER_CODE = '20005A4S2V';
    //善宏(山西)
    const SH_SX_TEST_SUPPLIER_CODE = '2000L34SUB';
    const SH_SX_PROD_SUPPLIER_CODE = '2000C2K3MK';

    //江投
    const JT_PROD_SUPPLIER_CODE = '20004ACMBM';
    const JT_TEST_SUPPLIER_CODE = '2000MACU3S';

    const DH_PROD_SUPPLIER_CODE = '2000UAD2V5';
    const DH_TEST_SUPPLIER_CODE = '2000AAL223';

    //秒加
    const MJ_PROD_SUPPLIER_CODE = '2000U3ULD2';
    const MJ_TEST_SUPPLIER_CODE = '2000DC3344';

    const XMSK_PROD_SUPPLIER_CODE = '2000K4ULDV';
    const XMSK_TEST_SUPPLIER_CODE = '2000AD5TSU';

    const YUNDATONG_PROD_SUPPLIER_CODE = '2000C3CAVC';
    const YUNDATONG_TEST_SUPPLIER_CODE = '200044LCBK';

    const GAODENG_PROD_SUPPLIER_CODE = '2000JL3AMV';
    const GAODENG_TEST_SUPPLIER_CODE = '2000TS2KC5';

    const TBJX_PROD_SUPPLIER_CODE = '200022K3T5';
    const TBJX_TEST_SUPPLIER_CODE = '2000DTVCLV';

    const JQ_PROD_SUPPLIER_CODE = '2000JK5KUB';
    const JQ_TEST_SUPPLIER_CODE = '2000DDDKTC';

    const YKC_PROD_SUPPLIER_CODE = '';
    const YKC_TEST_SUPPLIER_CODE = '';

    //获取需要选择油枪运营商
    static public function getPcodeList()
    {
        if (\App::environment('prod')) {
            return [
                self::PRO_PCODE,
                self::CZB_SQ_PRO_SUPPLIER_CODE,
                self::SQ_FL_PROD_SUPPLIER_CODE,
                self::SQ_AJ_PROD_SUPPLIER_CODE,
                self::SQ_AJSW_PROD_SUPPLIER_CODE,
                self::SQ_NAJ_PROD_SUPPLIER_CODE,
                self::HSY_PROD_SUPPLIER_CODE,
                self::SQ_ZL_PROD_SUPPLIER_CODE,
                self::ZHYK_PROD_SUPPLIER_CODE,
                self::ZDC_PROD_SUPPLIER_CODE,
                self::XYN_PROD_SUPPLIER_CODE,
                self::MTL_SY_PROD_SUPPLIER_CODE,
                self::MJ_PROD_SUPPLIER_CODE,
                self::XMSK_PROD_SUPPLIER_CODE,
                self::YUNDATONG_PROD_SUPPLIER_CODE,
                self::GAODENG_PROD_SUPPLIER_CODE,
                self::JQ_PROD_SUPPLIER_CODE,
            ];
        } else {
            return [
                self::TEST_PCODE,
                self::CZB_SQ_TEST_SUPPLIER_CODE,
                self::SQ_FL_TEST_SUPPLIER_CODE,
                self::SQ_AJ_TEST_SUPPLIER_CODE,
                self::SQ_AJSW_TEST_SUPPLIER_CODE,
                self::SQ_NAJ_TEST_SUPPLIER_CODE,
                self::HSY_TEST_SUPPLIER_CODE,
                self::SQ_ZL_TEST_SUPPLIER_CODE,
                self::ZHYK_TEST_SUPPLIER_CODE,
                self::ZDC_TEST_SUPPLIER_CODE,
                self::XYN_TEST_SUPPLIER_CODE,
                self::MTL_SY_TEST_SUPPLIER_CODE,
                self::MJ_TEST_SUPPLIER_CODE,
                self::XMSK_TEST_SUPPLIER_CODE,
                self::YUNDATONG_TEST_SUPPLIER_CODE,
                self::GAODENG_TEST_SUPPLIER_CODE,
                self::JQ_TEST_SUPPLIER_CODE,
            ];
        }
    }

    //展示确认页 --- 小程序都出现二次确认页,不用在维护
    static public function getPcodePaySure()
    {
        if (\App::environment('prod')) {
            return [
                self::E_PCODE,
                self::PRO_PCODE,
                self::SC_SHELL_PCODE,
                self::GD_SHELL_PCODE,
                self::SX_SHELL_PCODE,
                self::FJ_SHELL_PCODE,
                self::GS_SHELL_PCODE,
                self::ZY_PCODE,
                self::HYT_PCODE,
                self::ZY_LNG_PCODE,
                self::GB_PROD_SUPPLIER_CODE,
                self::HB1_PROD_SUPPLIER_CODE,
                self::GB_TJ_PROD_SUPPLIER_CODE,
                self::ZHYK_PROD_SUPPLIER_CODE,
                self::ZWL_PROD_SUPPLIER_CODE,
                self::XYN_PROD_SUPPLIER_CODE,
                self::SQ_ZSH_PROD_SUPPLIER_CODE,
                self::MTL_SY_PROD_SUPPLIER_CODE,
                self::MJ_PROD_SUPPLIER_CODE,
                self::XMSK_PROD_SUPPLIER_CODE,
                self::YUNDATONG_PROD_SUPPLIER_CODE,
                self::GAODENG_PROD_SUPPLIER_CODE,
                self::JQ_PROD_SUPPLIER_CODE,
            ];
        } else {
            return [
                self::E_TEST_PCODE,
                self::TEST_PCODE,
                self::SC_TEST_SHELL_PCODE,
                self::GD_TEST_SHELL_PCODE,
                self::SX_TEST_SHELL_PCODE,
                self::FJ_TEST_SHELL_PCODE,
                self::GS_TEST_SHELL_PCODE,
                self::ZY_TEST_PCODE,
                self::HYT_TEST_PCODE,
                self::ZY_LNG_TEST_PCODE,
                self::GB_TEST_SUPPLIER_CODE,
                self::HB1_TEST_SUPPLIER_CODE,
                self::GB_TJ_TEST_SUPPLIER_CODE,
                self::ZHYK_TEST_SUPPLIER_CODE,
                self::ZWL_TEST_SUPPLIER_CODE,
                self::XYN_TEST_SUPPLIER_CODE,
                self::SQ_ZSH_TEST_SUPPLIER_CODE,
                self::MTL_SY_TEST_SUPPLIER_CODE,
                self::MJ_TEST_SUPPLIER_CODE,
                self::XMSK_TEST_SUPPLIER_CODE,
                self::YUNDATONG_TEST_SUPPLIER_CODE,
                self::GAODENG_TEST_SUPPLIER_CODE,
                self::JQ_TEST_SUPPLIER_CODE,
            ];
        }
    }

    //生成壳牌付款码及老吕的url
    static public function getPayMentPcode()
    {
        //todo 弃用接口查询，改用直接查询只读db
        /*if (\App::environment('prod')) {
            return [self::GD_SHELL_PCODE, self::SC_SHELL_PCODE, self::SX_SHELL_PCODE, self::FJ_SHELL_PCODE, self::GS_SHELL_PCODE,
                    self::ZY_PCODE, self::ZY_LNG_PCODE,];
        } else {
            return [self::GD_TEST_SHELL_PCODE, self::SC_TEST_SHELL_PCODE, self::SX_TEST_SHELL_PCODE, self::FJ_TEST_SHELL_PCODE,
                    self::GS_TEST_SHELL_PCODE, self::ZY_TEST_PCODE, self::ZY_LNG_TEST_PCODE,
            ];
        }*/

        $log['time'] = date("Y-m-d H:i:s");
        if (\App::environment('prod') || \App::environment('test')) {
            file_put_contents("/data/web_log/getPayMentPcode.txt", implode("\r\n", $log) . "\r\n", FILE_APPEND);
        } else {
            file_put_contents("./getPayMentPcode.txt", implode("\r\n", $log) . "\r\n", FILE_APPEND);
        }
        try {
            $pcode = app(StationOperatorsRepository::class)->getPcodeList(
                ['is_second_check' => 1, 'status' => 1, "is_show" => 1]
            );
            if (count($pcode) > 0) {
                return $pcode->toArray();
            } else {
                return [];
            }
        } catch (\Exception $exception) {
            throw new \RuntimeException('获取运营商error,[getPayMentPcode]', 900441);
        }
        /*$list = (new Foss())->getPcodeConf(['is_second_check'=>1,'status'=>1,"is_show"=>1]);
        if(count($list) > 0){
            foreach ($list as $_item){
                $result[] = $_item['pcode'];
            }
        }
        return $result;*/
    }

    //E站途排除出示核销二维码
    static public function exceptPcode()
    {
        return [
            self::E_PCODE,
            self::E_TEST_PCODE,
            self::GB_PROD_SUPPLIER_CODE,
            self::GB_TEST_SUPPLIER_CODE,
            self::GB_TJ_PROD_SUPPLIER_CODE,
            self::GB_TJ_TEST_SUPPLIER_CODE,
            self::ZHYK_PROD_SUPPLIER_CODE,
            self::ZHYK_TEST_SUPPLIER_CODE,
            self::ZWL_PROD_SUPPLIER_CODE,
            self::ZWL_TEST_SUPPLIER_CODE,
            self::SQ_ZSH_PROD_SUPPLIER_CODE,
            self::SQ_ZSH_TEST_SUPPLIER_CODE,
            self::MTL_SY_PROD_SUPPLIER_CODE,
            self::MTL_SY_TEST_SUPPLIER_CODE,
            self::YUNDATONG_PROD_SUPPLIER_CODE,
            self::YUNDATONG_TEST_SUPPLIER_CODE,
            self::GAODENG_PROD_SUPPLIER_CODE,
            self::GAODENG_TEST_SUPPLIER_CODE,
            self::TBJX_PROD_SUPPLIER_CODE,
            self::TBJX_TEST_SUPPLIER_CODE,
        ];
    }

    //老吕运营商,跳转新的页面
    static public function skipHtmlPcode()
    {
        if (\App::environment('prod')) {
            return [self::ZY_PCODE, self::ZY_LNG_PCODE];
        } else {
            return [self::ZY_TEST_PCODE, self::ZY_LNG_TEST_PCODE];
        }
    }

    //特殊运营商 :不用选择油枪，且走车主邦下单模式
    static public function getSpecialPcode()
    {
        if (\App::environment('prod')) {
            return [self::HYT_PCODE, self::JT_PROD_SUPPLIER_CODE,];
        } else {
            return [self::HYT_TEST_PCODE, self::JT_TEST_SUPPLIER_CODE,];
        }
    }


    //长连接消息类型
    static public function getTopic($msg_type = '')
    {
        $topic = "";
        switch ($msg_type) {
            case 2: //支付成功
                $topic = "pay_success";
                break;
            case 3: //支付失败
                $topic = "pay_error";
                break;
            case 4: //隐藏二维码
                $topic = "hide_qr_code";
                break;
            default: //卡密码
                $topic = "card_need_pass";
        }
        return $topic;
    }

    //祥辉无论机构调价配置
    static public function getSpecialList()
    {
        //float_price:浮动价格; special_orglist 配置的机构
        //'20320F':线上祥辉物流机构
        return [
            'FLOAT_PRICE'     => 0.15,//浮动价格
            'SPECIAL_ORGLIST' => [
                //'20320F', //online 2020.04.09 停用祥辉
                '200Q1F', //test
                //'2026BV' //online
            ]
        ];
    }

    static public function getOilUnit($oil_name)
    {
        if (stripos($oil_name, "天然气") !== false) {
            $unit = 'Kg';
            if (stripos($oil_name, "压缩天然气") !== false) {
                $unit = '立方';
            }
            if (stripos($oil_name, "液化天然气") !== false) {
                $unit = 'Kg';
            }
        } else {
            $unit = "升";
        }
        return $unit;
    }

    static public function getBannerList()
    {
        $banner = [
            /*["imgUrl"=>"https://oss.aliyuncs.com/gsp-fs/fuel/customer/153130-3997.png","pathUrl"=>'',"type"=>'',"platform_name_abbreviation"=>''],
            //暂时下线
            ["imgUrl"=>"https://oss.aliyuncs.com/gsp-fs/fuel/customer/173940-5557.jpg","pathUrl"=>'https://gas.test.chinawayltd.com/oilWeChat/coupons',"type"=>'',"platform_name_abbreviation"=>'tel'],
            ["imgUrl"=>"https://oss.aliyuncs.com/gsp-fs/fuel/customer/104656-6389.png","pathUrl"=>'',"type"=>'',"platform_name_abbreviation"=>''],
            ["imgUrl"=>"https://oss.aliyuncs.com/gsp-fs/fuel/customer/144544-4825.jpg","pathUrl"=>'https://mp.weixin.qq.com/s/-bBdYlIaVS2WUQuD7GLvnQ',"type"=>'',"platform_name_abbreviation"=>''],
            ["imgUrl"=>"https://oss.aliyuncs.com/gsp-fs/fuel/customer/162648-3023.png","pathUrl"=>'https://mp.weixin.qq.com/s/aLmUZaP3RBS6BxTevcTUjw',"type"=>'',"platform_name_abbreviation"=>''],
            */
            [
                "imgUrl"                     => "https://oss.aliyuncs.com/gsp-fs/fuel/customer/140216-9728.jpg",
                "pathUrl"                    => 'https://mp.weixin.qq.com/s/uP_Dax2HjNhJMH4e9E7WIw',
                "type"                       => '',
                "platform_name_abbreviation" => ''
            ],
            [
                "imgUrl"                     => "https://g7s-fs.oss-cn-hangzhou.aliyuncs.com/fuel-banner/customer/2021-12-07/15/1638860433998.jpeg",
                "pathUrl"                    => 'https://mp.weixin.qq.com/s/hE6ugi1TxGgPf9Fzghh31Q',
                "type"                       => '',
                "platform_name_abbreviation" => ''
            ],
            [
                "imgUrl"                     => "https://oss.aliyuncs.com/gsp-fs/fuel/customer/161035-4333.png",
                "pathUrl"                    => 'https://fuel.g7s.huoyunren.com/woa-app/#/questionnaire?from=oilWechat',
                "type"                       => '',
                "platform_name_abbreviation" => 'woa'
            ],
            //["imgUrl"=>"https://oss.aliyuncs.com/gsp-fs/fuel/customer/114809-9058.png","pathUrl"=>'https://kunlun.leishenai.com/h5',"type"=>'kunlun',"platform_name_abbreviation"=>'kl'],
            //["imgUrl"=>"https://oss.aliyuncs.com/gsp-fs/fuel/customer/114825-7660.png","pathUrl"=>'https://kunlun.leishenai.com/h5',"type"=>'kunlun',"platform_name_abbreviation"=>'kl'],
            //["imgUrl"=>"https://oss.aliyuncs.com/gsp-fs/fuel/customer/042659-8969.png","pathUrl"=>'https://mp.weixin.qq.com/s/x3eeFc3p5IaWJJnVaLgL4A',"type"=>'',"platform_name_abbreviation"=>''],
            [
                "imgUrl"                     => "https://oss.aliyuncs.com/gsp-fs/fuel/banner/yunka.png",
                "pathUrl"                    => 'https://webs.yunka-truck.cn/#/project',
                "type"                       => 'yunka',
                "platform_name_abbreviation" => 'yk'
            ]
        ];
        /*$expire = true;
        if(time() > strtotime("2021-05-31 16:00:00")){
            $expire = false;
            unset($banner[1]);
            $banner = array_values($banner);
        }*/
        if (in_array(App()->environment(), ['prod', 'pro', 'pre'])) {
            //$banner[3]['pathUrl'] = 'https://shop.mindakj.com/h5';
            //$banner[1]['pathUrl'] = 'https://mp.weixin.qq.com/s/aLmUZaP3RBS6BxTevcTUjw';
            /*if(!$expire){
                $banner[2]['pathUrl'] = 'https://shop.mindakj.com/h5';
                $banner[3]['pathUrl'] = 'https://shop.mindakj.com/h5';
            }*/
            unset($banner[count($banner) - 1]);
        }
        return $banner;
    }

    //特殊运营商 :下单已对方传入价格为准
    static public function getAcceptPricePcode()
    {
        if (\App::environment('prod')) {
            return [self::SG_PCODE, self::BDT_PCODE];
        } else {
            return [self::SG_TEST_PCODE, self::BDT_TEST_PCODE];
        }
    }

    //特殊运营商 :下单需比价
    static public function getComparePricePcode()
    {
        if (\App::environment('prod')) {
            return [self::ZY_PCODE, self::ZY_LNG_PCODE];
        } else {
            return [self::ZY_TEST_PCODE, self::ZY_LNG_TEST_PCODE];
        }
    }

    //新下单灰度机构
    static public function grayOrderOrg($orgcode = '')
    {
        if (empty($orgcode)) {
            return false;
        }
        $orgList = ['2025NI', '200NYJ'];
        if (in_array(substr($orgcode, 0, 6), $orgList)) {
            return true;
        }
        return false;
    }

    static public function checkIsG7SupplierCode(string $supplierCode): bool
    {
        if (\App::environment('prod')) {
            $checkData = CardTradeConf::$ownner_station_pcode;
        } else {
            $checkData = CardTradeConf::$ownner_station_pcode;
        }
        return in_array($supplierCode, $checkData);
    }

    //强制三方站点按照按升加油
    static public function forceOilUnitPcode()
    {
        if (\App::environment('prod')) {
            return [self::XY_PCODE, self::DD_PCODE, self::ZY_PCODE, self::ZY_LNG_PCODE];
        } else {
            return [self::XY_TEST_PCODE, self::DD_TEST_PCODE, self::ZY_TEST_PCODE, self::ZY_LNG_TEST_PCODE];
        }
    }

    /**
     * 一键付且需要走主动付款流程的运营商编码
     * @return array
     */
    static public function getYjfAndZdpPcodes()
    {
        if (\App::environment('prod')) {
            return [
                self::CY_PCODE,
                self::ZDC_PROD_SUPPLIER_CODE,
                self::JT_PROD_SUPPLIER_CODE,
            ];
        } else {
            return [
                self::CY_TEST_PCODE,
                self::ZDC_TEST_SUPPLIER_CODE,
                self::JT_TEST_SUPPLIER_CODE,
            ];
        }
    }
    // 联动退款审核的上游名单
    static public function linkedRefundApproveSupplier(): array
    {
        if (\App::environment('prod')) {
            return [
                self::SQ_ZL_PROD_SUPPLIER_CODE,
                self::ZDC_PROD_SUPPLIER_CODE,
                self::MTL_SY_PROD_SUPPLIER_CODE,
                self::SH_PROD_SUPPLIER_CODE,
                self::SH_SX_PROD_SUPPLIER_CODE,
                self::TBJX_PROD_SUPPLIER_CODE,
                self::JQ_PROD_SUPPLIER_CODE,
            ];
        } else {
            return [
                self::SQ_ZL_TEST_SUPPLIER_CODE,
                self::ZDC_TEST_SUPPLIER_CODE,
                self::MTL_SY_TEST_SUPPLIER_CODE,
                self::SH_TEST_SUPPLIER_CODE,
                self::SH_SX_TEST_SUPPLIER_CODE,
                self::TBJX_TEST_SUPPLIER_CODE,
                self::JQ_TEST_SUPPLIER_CODE,
            ];
        }
    }

    // 联动退款的上游名单
    static public function linkedRefundSupplier(): array
    {
        if (\App::environment('prod')) {
            return [
                CommonDefine::DT_ZJ_PROD_SUPPLIER_CODE => [
                    'wait_refund_result' => false
                ],
            ];
        } else {
            return [
                CommonDefine::DT_ZJ_TEST_SUPPLIER_CODE => [
                    'wait_refund_result' => false
                ],
            ];
        }
    }
    // 预约加油模式上游名单
    static public function getReservationRefuelSupplier(): array
    {
        if (\App::environment('prod')) {
            return [
                self::ZWL_PROD_SUPPLIER_CODE,
                self::SQ_ZSH_PROD_SUPPLIER_CODE,
            ];
        } else {
            return [
                self::ZWL_TEST_SUPPLIER_CODE,
                self::SQ_ZSH_TEST_SUPPLIER_CODE,
            ];
        }
    }

    static public function forceAcceptPricePcode()
    {
        if (\App::environment('prod')) {
            return [
                self::ZWL_PROD_SUPPLIER_CODE,
                self::SQ_ZSH_PROD_SUPPLIER_CODE,
            ];
        } else {
            return [
                self::ZWL_TEST_SUPPLIER_CODE,
                self::SQ_ZSH_TEST_SUPPLIER_CODE,
            ];
        }
    }

    /** 订单列表应付金额展示供应商原始数据的名单
     * @return string[]
     */
    static public function showOriginalSupplierMoney(): array
    {
        if (\App::environment('prod')) {
            return [
                self::ZWL_PROD_SUPPLIER_CODE,
                self::SQ_ZSH_PROD_SUPPLIER_CODE,
            ];
        } else {
            return [
                self::ZWL_TEST_SUPPLIER_CODE,
                self::SQ_ZSH_TEST_SUPPLIER_CODE,
            ];
        }
    }
    /** 不允许异常修改的运营商名单
     * @return string[]
     */
    static public function notAllowExceptionModification()
    {
        if (\App::environment('prod')) {
            return [
                self::SH_PROD_SUPPLIER_CODE,
                self::SH_SX_PROD_SUPPLIER_CODE,
            ];
        } else {
            return [
                self::SH_TEST_SUPPLIER_CODE,
                self::SH_SX_TEST_SUPPLIER_CODE,
            ];
        }
    }


    /**
     * 强制隐藏选枪运营商
     * 该方法根据应用的环境（生产环境或非生产环境），返回一个供应商代码数组。
     *
     * @return array 返回一个包含供应商代码的数组。如果在生产环境中，则返回生产环境的供应商代码；
     *               如果不在生产环境中，则返回测试环境的供应商代码。
     */
    static public function forceHideGunPcode()
    {
        // 检查应用环境，根据环境返回不同的供应商代码数组
        if (\App::environment('prod')) {
            // 在生产环境中，返回生产环境的供应商代码
            return [
                self::JT_PROD_SUPPLIER_CODE,
            ];
        }
        // 在非生产环境中，返回测试环境的供应商代码
        return [
            self::JT_TEST_SUPPLIER_CODE,
        ];
    }

    /**
     * 获取需要卡分配的供应商代码列表。
     *
     * 根据应用的环境（是否为生产环境），确定是否返回特定的供应商代码。
     * 无论环境如何，此方法始终返回DH_TEST_SUPPLIER_CODE。
     *
     * @return array 需要卡分配的供应商代码列表。
     */
    static public function needAssign(): array
    {
        // 检查当前应用环境是否为生产环境
        if (\App::environment('prod')) {
            // 在生产环境中，返回指定的供应商代码
            return [
                self::DH_PROD_SUPPLIER_CODE
            ];
        }
        // 在非生产环境中，同样返回指定的供应商代码
        return [
            self::DH_TEST_SUPPLIER_CODE
        ];
    }
}