<?php

namespace App\Http\Controllers\Api;

use App\Models\Logic\Code\GetSecondaryPaymentQrCode as GetSecondaryPaymentQrCodeLogic;
use App\Models\Logic\Code\Parse as ParseLogic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;


class CodeController extends BasicController
{
    public $customMessages = [
        'parse'                     => [
            'qr_code.required'      => 4120001,
            'station_id.alpha_dash' => 4120003,
            'station_id.required'   => 4120004,
        ],
        'parse_zy'                  => [
            'qrcode.required'    => 4120001,
            'stationId.numeric'  => 4120003,
            'stationId.required' => 4120004,
        ],
        'parse_wjy'                 => [
            'qrcode.required'    => 4120001,
            'stationId.numeric'  => 4120003,
            'stationId.required' => 4120004,
        ],
        'parse_hjy'                 => [
            'auth_code.required'    => 4120001,
            'station_id.alpha_dash' => 4120003,
            'station_id.required'   => 4120004,
        ],
        'getSecondaryPaymentQrCode' => [
            'order_id.required'        => 4120162,
            'supplier_code.required'   => 4120034,
            'supplier_code.alpha_dash' => 4120034,
        ],
    ];
    public $customRules    = [
        'parse'                     => [
            'qr_code'    => 'required',
            'station_id' => 'required|alpha_dash',
        ],
        'getSecondaryPaymentQrCode' => [
            'order_id'      => 'required',
            'supplier_code' => 'required|alpha_dash'
        ],
        'parse_zy'                  => [
            'qrcode'    => 'required',
            'stationId' => 'required|numeric',
        ],
        'parse_wjy'                 => [
            'qrcode'    => 'required',
            'stationId' => 'required|numeric',
        ],
        'parse_hjy'                 => [
            'auth_code'  => 'required',
            'station_id' => 'required|alpha_dash',
        ],
    ];

    public function __construct()
    {
        parent::__construct();
        $this->loadValidateCallback();
        $this->loadInputNoData();
    }

    public function loadValidateCallback()
    {
        $this->validateCallback['wjy'] = function ($responseCode) {
            response()->json([
                'code'    => $responseCode,
                'message' => config("error.$responseCode"),
            ])->send();
            exit();
        };
    }

    public function loadInputNoData()
    {
        $this->inputNoData = [
            'wjy' => true,
        ];
    }

    /**
     * @throws Throwable
     */
    public function parse(Request $request)
    {
        $this->validateRequestParam($request);
        return (new ParseLogic($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/4/21 1:20 上午
     */
    public function getSecondaryPaymentQrCode(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return (new GetSecondaryPaymentQrCodeLogic($this->requestData))->handle();
    }
}
