<?php

namespace App\Http\Controllers\Api;

use App\Models\Logic\Station\Main as QueryLogic;
use App\Models\Logic\Station\Receive\Main as ReceiveMainLogic;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class StationController extends BasicController
{
    public $stationOilFieldMapping = [
        'default'   => 'price_list',
        'zy'        => 'guns',
        'wjy'       => 'fuels',
        'saic'      => '',
        'gb'        => '',
        'gb_tj'     => '',
        'hsy'       => '',
        'sqzl'      => 'oilGunBeans',
        'zhyk'      => 'gunList',
        'zdc'       => '',
        'xyn'       => 'oilInfoDtos',
        'sqZsh'     => 'oilGunBeans',
        'mtlSy'     => '',
        'sh'        => '',
        'shsx'      => '',
        'jt'        => 'guns',
        'mj'        => 'stationPrices',
        'xmsk'      => 'gunList',
        'yundatong' => 'priceList',
        'gaodeng'   => 'oilInfoList',
    ];
    public $customMessages         = [
        'queryOne'              => [
            'station_id.required'  => 4120003,
            'station_id.alpha_num' => 4120004,
        ],
        'queryOne_ad'           => [
            'oilStationNo.required'  => 4120003,
            'oilStationNo.alpha_num' => 4120004,
        ],
        'receive'               => [
            'id.required'                 => 4120117,
            'id.alpha_dash'               => 4120118,
            'station_name.required'       => 4120137,
            'contact_phone.required'      => 4120119,
            'contact_phone.alpha_dash'    => 4120120,
            'lat.required'                => 4120121,
            'lat.numeric'                 => 4120122,
            'lng.required'                => 4120123,
            'lng.numeric'                 => 4120124,
            'province_code.required'      => 4120125,
            'province_code.numeric'       => 4120126,
            'city_code.required'          => 4120127,
            'city_code.numeric'           => 4120128,
            'address.required'            => 4120129,
            'station_brand_name.required' => 4120130,
            'rebate_grade.in'             => 4120131,
            'business_hours.string'       => 4120132,
            'is_stop.required'            => 4120133,
            'is_stop.in'                  => 4120134,
            'price_list.required'         => 4120135,
            'price_list.array'            => 4120135,
        ],
        'receive_sp'            => [
            'id.required'                 => 4120117,
            'id.alpha_dash'               => 4120118,
            'station_name.required'       => 4120137,
            'contact_phone.alpha_dash'    => 4120120,
            'lat.required'                => 4120121,
            'lat.numeric'                 => 4120122,
            'lng.required'                => 4120123,
            'lng.numeric'                 => 4120124,
            'province_code.required'      => 4120125,
            'province_code.numeric'       => 4120126,
            'city_code.required'          => 4120127,
            'city_code.numeric'           => 4120128,
            'address.required'            => 4120129,
            'station_brand_name.required' => 4120130,
            'rebate_grade.in'             => 4120131,
            'business_hours.string'       => 4120132,
            'is_stop.required'            => 4120133,
            'is_stop.in'                  => 4120134,
            'price_list.required'         => 4120135,
            'price_list.array'            => 4120135,
        ],
        'receive_zy'            => [
            'stationId.required'   => 4120117,
            'stationId.numeric'    => 4120118,
            'stationName.required' => 4120137,
            'tel.required'         => 4120119,
            'tel.alpha_dash'       => 4120120,
            'lat.required'         => 4120121,
            'lat.numeric'          => 4120122,
            'lng.required'         => 4120123,
            'lng.numeric'          => 4120124,
            'province.required'    => 4120125,
            'province.numeric'     => 4120126,
            'city.required'        => 4120127,
            'city.numeric'         => 4120128,
            'address.required'     => 4120129,
            'rebateType.in'        => 4120131,
            'opentime.string'      => 4120132,
            'status.required'      => 4120133,
            'status.in'            => 4120134,
            'guns.required'        => 4120135,
            'guns.json'            => 4120135,
            'tagType.in'           => 4120138,
        ],
        'receive_wjy'           => [
            'stationId.required'    => 4120117,
            'stationId.numeric'     => 4120118,
            'stationName.required'  => 4120137,
            'lat.required'          => 4120121,
            'lat.numeric'           => 4120122,
            'lng.required'          => 4120123,
            'lng.numeric'           => 4120124,
            'provinceCode.required' => 4120125,
            'provinceCode.numeric'  => 4120126,
            'cityCode.required'     => 4120127,
            'cityCode.numeric'      => 4120128,
            'address.required'      => 4120129,
            'opentime.string'       => 4120132,
            'isStop.required'       => 4120133,
            'isStop.in'             => 4120134,
            'fuels.required'        => 4120135,
            'fuels.array'           => 4120135,
            'isHighspeed.required'  => 4120138,
            'isHighspeed.in'        => 4120138,
        ],
        'receive_cy'            => [
            'id.required'                 => 4120117,
            'id.alpha_dash'               => 4120118,
            'station_name.required'       => 4120137,
            'contact_phone.present'       => 4120119,
            'contact_phone.alpha_dash'    => 4120120,
            'lat.required'                => 4120121,
            'lat.numeric'                 => 4120122,
            'lng.required'                => 4120123,
            'lng.numeric'                 => 4120124,
            'province_code.required'      => 4120125,
            'province_code.numeric'       => 4120126,
            'city_code.required'          => 4120127,
            'city_code.numeric'           => 4120128,
            'address.required'            => 4120129,
            'station_brand_name.required' => 4120130,
            'beginTime.required'          => 4120508,
            'endTime.required'            => 4120510,
            'is_stop.required'            => 4120133,
            'is_stop.in'                  => 4120134,
            'price_list.required'         => 4120135,
            'price_list.array'            => 4120135,
            'trade_type.required'         => 4120512,
            'trade_type.integer'          => 4120513,
            'oil_uint.required'           => 4120514,
            'oil_uint.integer'            => 4120515,
            'station_type.required'       => 4120202,
            'station_type.integer'        => 4120201,
            'is_highway.required'         => 4120516,
            'is_highway.integer'          => 4120138,
        ],
        'receive_saic'          => [
            'merchantGasStationIds.required' => 4120117,
        ],
        'receive_gb'            => [
            'merchantGasStationId.required' => 4120117,
        ],
        'receive_gb_tj'         => [
            'merchantGasStationId.required' => 4120117,
        ],
        'receive_hsy'           => [
        ],
        'receive_sqzl'          => [
            'stationId.required'   => 4120117,
            'stationId.alpha_num'  => 4120118,
            'stationName.required' => 4120137,
            'lat.required'         => 4120121,
            'lat.numeric'          => 4120122,
            'lng.required'         => 4120123,
            'lng.numeric'          => 4120124,
            'address.required'     => 4120129,
            'openTime.string'      => 4120132,
            'oilGunBeans.required' => 4120135,
            'oilGunBeans.array'    => 4120135,
            'type.present'         => 4120522,
            'status.required'      => 4120133,
        ],
        'receive_zhyk'          => [
            'stationCode.required'   => 4120117,
            'stationCode.alpha_num'  => 4120118,
            'stationName.required'   => 4120137,
            'latitude.required'      => 4120121,
            'latitude.numeric'       => 4120122,
            'longitude.required'     => 4120123,
            'longitude.numeric'      => 4120124,
            'address.required'       => 4120129,
            'provinceCode.required'  => 4120125,
            'provinceCode.numeric'   => 4120126,
            'cityCode.required'      => 4120127,
            'cityCode.numeric'       => 4120128,
            'brand.required'         => 4120130,
            'gunList.present'        => 4120135,
            'gunList.array'          => 4120135,
            'leaderName.present'     => 4120528,
            'leaderPhone.present'    => 4120119,
            'leaderPhone.alpha_dash' => 4120120,
            'refuelMode.required'    => 4120512,
            'refuelMode.integer'     => 4120513,
        ],
        'receive_zdc'           => [
            'changeType.required'         => 4120540,
            'changeType.in'               => 4120541,
            'stationIdList.required_if'   => 4120117,
            'stationIdList.array'         => 4120118,
            'stationIdList.*.required_if' => 4120117,
            'stationIdList.*.alpha_num'   => 4120118,
        ],
        'receive_xyn'           => [
            'data.*.id.required'                 => 4120117,
            'data.*.id.alpha_num'                => 4120118,
            'data.*.siteName.required'           => 4120137,
            'data.*.latitude.required'           => 4120121,
            'data.*.latitude.numeric'            => 4120122,
            'data.*.longitude.required'          => 4120123,
            'data.*.longitude.numeric'           => 4120124,
            'data.*.address.required'            => 4120129,
            'data.*.oilInfoDtos.required'        => 4120135,
            'data.*.oilInfoDtos.array'           => 4120135,
            'data.*.provinceCode.required'       => 4120125,
            'data.*.provinceCode.numeric'        => 4120126,
            'data.*.cityCode.required'           => 4120127,
            'data.*.cityCode.numeric'            => 4120128,
            'data.*.siteBrand.present'           => 4120130,
            'data.*.siteBrand.integer'           => 4120130,
            'data.*.siteContacts.present'        => 4120528,
            'data.*.siteContactPhone.present'    => 4120119,
            'data.*.siteContactPhone.alpha_dash' => 4120120,
            'data.*.highSpeedMark.required'      => 4120516,
            'data.*.highSpeedMark.in'            => 4120138,
            'data.*.qrcodePay.required'          => 4120574,
            'data.*.qrcodePay.in'                => 4120575,
            'data.*.qrcodeSitePay.required'      => 4120576,
            'data.*.qrcodeSitePay.in'            => 4120577,
            'data.*.activePay.required'          => 4120572,
            'data.*.activePay.in'                => 4120573,
            'data.*.globalEnable.required'       => 4120133,
            'data.*.globalEnable.in'             => 4120134,
            'data.*.putawayMark.required'        => 4120578,
            'data.*.putawayMark.in'              => 4120579,
        ],
        'receive_sqZsh'         => [
            'stationId.required'   => 4120117,
            'stationId.alpha_num'  => 4120118,
            'stationName.required' => 4120137,
            'lat.required'         => 4120121,
            'lat.numeric'          => 4120122,
            'lng.required'         => 4120123,
            'lng.numeric'          => 4120124,
            'address.required'     => 4120129,
            'openTime.string'      => 4120132,
            'oilGunBeans.required' => 4120135,
            'oilGunBeans.array'    => 4120135,
            'type.present'         => 4120522,
            'status.required'      => 4120133,
            'isHighspeed.present'  => 4120516,
        ],
        'receive_mtlSy'         => [
            'stationId.required' => 4120117,
        ],
        'receive_jt'            => [
            'stationId.required'          => 4120117,
            'stationId.numeric'           => 4120118,
            'stationName.required'        => 4120137,
            'tel.required'                => 4120119,
            'tel.alpha_dash'              => 4120120,
            'lat.required'                => 4120121,
            'lat.numeric'                 => 4120122,
            'lng.required'                => 4120123,
            'lng.numeric'                 => 4120124,
            'province.required'           => 4120125,
            'province.numeric'            => 4120126,
            'city.required'               => 4120127,
            'city.numeric'                => 4120128,
            'address.required'            => 4120129,
            'openTime.string'             => 4120132,
            'status.required'             => 4120133,
            'status.in'                   => 4120134,
            'guns.present'                => 4120135,
            'guns.json'                   => 4120135,
            'tagType.in'                  => 4120138,
            'stationBrand.present'        => 4120130,
            'isVerificationCode.required' => 4120592,
            'isVerificationCode.in'       => 4120593,
            'payType.required'            => 4120547,
            'payType.in'                  => 4120548,
        ],
        'receive_sh'            => [
            'id.required'       => 4120117,
            'id.alpha_num'      => 4120118,
            'title.required'    => 4120137,
            'lat.required'      => 4120121,
            'lat.numeric'       => 4120122,
            'lng.required'      => 4120123,
            'lng.numeric'       => 4120124,
            'address.present'   => 4120129,
            'gun.array'         => 4120135,
            'oil_brand.present' => 4120130,
            'status.required'   => 4120133,
            'status.in'         => 4120134,
        ],
        'receive_shsx'          => [
            'id.required'       => 4120117,
            'id.alpha_num'      => 4120118,
            'title.required'    => 4120137,
            'lat.required'      => 4120121,
            'lat.numeric'       => 4120122,
            'lng.required'      => 4120123,
            'lng.numeric'       => 4120124,
            'address.present'   => 4120129,
            'gun.array'         => 4120135,
            'oil_brand.present' => 4120130,
            'status.required'   => 4120133,
            'status.in'         => 4120134,
        ],
        'receive_mj'            => [
            'identifier.required'   => 4120117,
            'identifier.alpha_dash' => 4120118,
            'sname.required'        => 4120137,
            'latitude.required'     => 4120121,
            'latitude.numeric'      => 4120122,
            'longitude.required'    => 4120123,
            'longitude.numeric'     => 4120124,
            'saddress.present'      => 4120129,
            'open.required'         => 4120133,
            'open.in'               => 4120134,
            'tel.present'           => 4120119,
            'stationPrices.present' => 4120135,
            'stationPrices.array'   => 4120135,
        ],
        'receive_xmsk'          => [
            'oilStationCode.required' => 4120117,
            'oilStationCode.numeric'  => 4120118,
            'oilStationName.required' => 4120137,
            'contactNumber.required'  => 4120119,
            'latitude.required'       => 4120121,
            'latitude.numeric'        => 4120122,
            'longitude.required'      => 4120123,
            'longitude.numeric'       => 4120124,
            'address.required'        => 4120129,
            'businessHours.string'    => 4120132,
            'isStop.required'         => 4120133,
            'isStop.in'               => 4120134,
            'gunList.present'         => 4120135,
            'gunList.array'           => 4120135,
            'oilStationType.present'  => 4120130,
        ],
        'receive_yundatong'     => [
            'stationId.required'    => 4120117,
            'stationId.alpha_dash'  => 4120118,
            'stationName.required'  => 4120137,
            'lat.required'          => 4120121,
            'lat.numeric'           => 4120122,
            'lng.required'          => 4120123,
            'lng.numeric'           => 4120124,
            'address.required'      => 4120129,
            'provinceCode.required' => 4120125,
            'provinceCode.numeric'  => 4120126,
            'cityCode.required'     => 4120127,
            'cityCode.numeric'      => 4120128,
            'priceList.present'     => 4120135,
            'priceList.array'       => 4120135,
            'contactPhone.present'  => 4120119,
            'payMode.required'      => 4120512,
            'payMode.in'            => 4120513,
            'status.required'       => 4120133,
            'status.in'             => 4120134,
            'verifyMode.required'   => 4120592,
            'verifyMode.in'         => 4120593,
        ],
        'receive_gaodeng'       => [
            'oilStationNo.required'     => 4120117,
            'oilStationNo.alpha_dash'   => 4120118,
            'oilStationName.required'   => 4120137,
            'latitude.required'         => 4120121,
            'latitude.numeric'          => 4120122,
            'longitude.required'        => 4120123,
            'longitude.numeric'         => 4120124,
            'address.required'          => 4120129,
            'oilInfoList.present'       => 4120135,
            'oilInfoList.array'         => 4120135,
            'contactMobile.present'     => 4120119,
            'businessType.present'      => 4120202,
            'keepAccounts.required'     => 4120514,
            'keepAccounts.in'           => 4120515,
            'confirmMode.required'      => 4120512,
            'oilStationStatus.required' => 4120133,
            'oilStationStatus.in'       => 4120134,
            'provinceId.required'       => 4120125,
            'provinceId.numeric'        => 4120126,
            'cityId.required'           => 4120127,
            'cityId.numeric'            => 4120128,
        ],
        'receive_oil'           => [
            'oil_type.string'        => 4120011,
            'oil_name.required'      => 4120013,
            'oil_name.in'            => 4120014,
            'oil_level.string'       => 4120016,
            'price.required'         => 4120007,
            'price.numeric'          => 4120008,
            'listing_price.numeric'  => 4120008,
            'start_time.date_format' => 4120020,
            'end_time.date_format'   => 4120022,
        ],
        'receive_oil_zy'        => [
            'skuName.required'   => 4120013,
            'lvPrice.required'   => 4120007,
            'lvPrice.numeric'    => 4120008,
            'skuCode.required'   => 4120139,
            'skuCode.alpha_dash' => 4120140,
        ],
        'receive_oil_wjy'       => [
            'fuelName.required' => 4120013,
            'price.required'    => 4120007,
            'price.numeric'     => 4120008,
            'fuelNo.required'   => 4120139,
            'fuelNo.alpha_dash' => 4120140,
        ],
        'receive_oil_cy'        => [
            'gun_price.required'    => 4120484,
            'gun_price.numeric'     => 4120485,
            'price.required'        => 4120007,
            'price.numeric'         => 4120008,
            'oil_number.required'   => 4120139,
            'gun_numbers.required'  => 4120517,
            'gun_numbers.array'     => 4120518,
            'gun_numbers.*.integer' => 4120518,
        ],
        'receive_oil_sqzl'      => [
            'lvPrice.required'   => 4120484,
            'lvPrice.numeric'    => 4120485,
            'downValue.required' => 4120524,
            'downValue.numeric'  => 4120525,
            'fuelNo.required'    => 4120139,
            'fuelNo.alpha_dash'  => 4120140,
        ],
        'receive_oil_zhyk'      => [
            'oilPrice.required'         => 4120484,
            'oilPrice.numeric'          => 4120485,
            'discountOilPrice.required' => 4120007,
            'discountOilPrice.numeric'  => 4120008,
            'plateOilCode.required'     => 4120139,
            'plateOilCode.numeric'      => 4120140,
            'gunNo.required'            => 4120517,
        ],
        'receive_oil_xyn'       => [
            'oilsPrice.required'     => 4120007,
            'oilsPrice.numeric'      => 4120008,
            'oilsSitePrice.required' => 4120484,
            'oilsSitePrice.numeric'  => 4120485,
            'oilsCode.required'      => 4120466,
            'id.required'            => 4120139,
            'id.alpha_dash'          => 4120140,
            'enableMark.required'    => 4120580,
            'enableMark.in'          => 4120581,
        ],
        'receive_oil_sqZsh'     => [
            'lvPrice.required'  => 4120484,
            'lvPrice.numeric'   => 4120485,
            'fuelNo.required'   => 4120139,
            'fuelNo.alpha_dash' => 4120140,
        ],
        'receive_oil_jt'        => [
            'skuCode.required'       => 4120139,
            'skuCode.alpha_dash'     => 4120140,
            'gunId.present'          => 4120517,
            'discountPrice.required' => 4120007,
            'discountPrice.numeric'  => 4120008,
            'marketPrice.required'   => 4120484,
            'marketPrice.numeric'    => 4120485,
            'guidePrice.required'    => 4120594,
            'guidePrice.numeric'     => 4120595,
        ],
        'receive_oil_mj'        => [
            'gasType.required'    => 4120139,
            'gasType.alpha_dash'  => 4120140,
            'mjPrice.numeric'     => 4120008,
            'listedPrice.numeric' => 4120485,
        ],
        'receive_oil_xmsk'      => [
            'productNo.required'   => 4120139,
            'productNo.alpha_dash' => 4120140,
            'oilGunNo.required'    => 4120517,
            'gunPrice.required'    => 4120484,
            'gunPrice.numeric'     => 4120485,
        ],
        'receive_oil_yundatong' => [
            'oilNo.required'         => 4120139,
            'gunList.present'        => 4120517,
            'gunList.array'          => 4120518,
            'price.required'         => 4120007,
            'price.numeric'          => 4120008,
            'priceGun.required'      => 4120484,
            'priceGun.numeric'       => 4120485,
            'priceOfficial.required' => 4120594,
            'priceOfficial.numeric'  => 4120595,
        ],
        'receive_oil_gaodeng'   => [
            'oilNo.required'           => 4120139,
            'oilGunNoList.required'    => 4120517,
            'enterprisePrice.required' => 4120484,
            'enterprisePrice.numeric'  => 4120485,
        ],
    ];

    public $customRules = [
        'queryOne'              => [
            'station_id' => 'required|alpha_num',
        ],
        'queryOne_ad'           => [
            'oilStationNo' => 'required|alpha_num',
        ],
        'receive'               => [
            'id'                 => 'required|alpha_dash',
            'station_name'       => 'required',
            'contact_phone'      => 'required|alpha_dash',
            'lat'                => 'required|numeric',
            'lng'                => 'required|numeric',
            'province_code'      => 'required|numeric',
            'city_code'          => 'required|numeric',
            'address'            => 'required',
            'station_brand_name' => 'required',
            'rebate_grade'       => 'in:A,B,C,D,E',
            'business_hours'     => 'string',
            'is_stop'            => 'required|in:1,0',
            'price_list'         => 'required|array',
        ],
        'receive_sp'            => [
            'id'                 => 'required|alpha_dash',
            'station_name'       => 'required',
            'contact_phone'      => 'alpha_dash',
            'lat'                => 'required|numeric',
            'lng'                => 'required|numeric',
            'province_code'      => 'required|numeric',
            'city_code'          => 'required|numeric',
            'address'            => 'required',
            'station_brand_name' => 'required',
            'rebate_grade'       => 'in:A,B,C,D,E',
            'business_hours'     => 'string',
            'is_stop'            => 'required|in:1,0',
            'price_list'         => 'required|array',
        ],
        'receive_zy'            => [
            'stationId'   => 'required|numeric',
            'stationName' => 'required',
            'tel'         => 'required|alpha_dash',
            'lat'         => 'required|numeric',
            'lng'         => 'required|numeric',
            'province'    => 'required|numeric',
            'city'        => 'required|numeric',
            'address'     => 'required',
            'rebateType'  => 'in:A,B,C,D,E',
            'opentime'    => 'string',
            'status'      => 'required|in:online,pause,offline',
            'guns'        => 'required|json',
            'tagType'     => 'in:A,B',
        ],
        'receive_wjy'           => [
            'stationId'    => 'required|numeric',
            'stationName'  => 'required',
            'lat'          => 'required|numeric',
            'lng'          => 'required|numeric',
            'provinceCode' => 'required|numeric',
            'cityCode'     => 'required|numeric',
            'address'      => 'required',
            'opentime'     => 'string',
            'isStop'       => 'required|in:0,1',
            'fuels'        => 'required|array',
            'isHighspeed'  => 'required|in:0,1',
        ],
        'receive_cy'            => [
            'id'                 => 'required|alpha_dash',
            'station_name'       => 'required',
            'contact_phone'      => 'present|alpha_dash',
            'lat'                => 'required|numeric',
            'lng'                => 'required|numeric',
            'province_code'      => 'required|numeric',
            'city_code'          => 'required|numeric',
            'address'            => 'required',
            'station_brand_name' => 'required',
            'beginTime'          => 'required',
            'endTime'            => 'required',
            'is_stop'            => 'required|in:1,0',
            'trade_type'         => 'required|integer',
            'oil_uint'           => 'required|integer',
            'is_highway'         => 'required|integer',
            'station_type'       => 'required|integer',
            'price_list'         => 'required|array',
        ],
        'receive_saic'          => [
            'merchantGasStationIds' => 'required',
        ],
        'receive_gb'            => [
            'merchantGasStationId' => 'required',
        ],
        'receive_gb_tj'         => [
            'merchantGasStationId' => 'required',
        ],
        'receive_hsy'           => [
        ],
        'receive_sqzl'          => [
            'stationId'   => 'required|alpha_num',
            'stationName' => 'required',
            'lat'         => 'required|numeric',
            'lng'         => 'required|numeric',
            'address'     => 'required',
            'openTime'    => 'string',
            'oilGunBeans' => 'required|array',
            'type'        => 'present',
            'status'      => 'required',
        ],
        'receive_zhyk'          => [
            'stationCode'  => 'required|alpha_num',
            'stationName'  => 'required',
            'latitude'     => 'required|numeric',
            'longitude'    => 'required|numeric',
            'address'      => 'required',
            'gunList'      => 'present|array|nullable',
            'provinceCode' => 'required|numeric',
            'cityCode'     => 'required|numeric',
            'brand'        => 'required',
            'leaderName'   => 'present',
            'leaderPhone'  => 'present|alpha_dash',
            'refuelMode'   => 'required|integer',
        ],
        'receive_zdc'           => [
            'changeType'      => 'required|in:1,2',
            'stationIdList'   => 'required_if:changeType,2|array',
            'stationIdList.*' => 'required_if:changeType,2|alpha_num',
        ],
        'receive_xyn'           => [
            'data.*.id'               => 'required|alpha_num',
            'data.*.siteName'         => 'required',
            'data.*.latitude'         => 'required|numeric',
            'data.*.longitude'        => 'required|numeric',
            'data.*.address'          => 'required',
            'data.*.oilInfoDtos'      => 'required|array',
            'data.*.provinceCode'     => 'required|numeric',
            'data.*.cityCode'         => 'required|numeric',
            'data.*.siteBrand'        => 'present|nullable|integer',
            'data.*.siteContacts'     => 'present',
            'data.*.siteContactPhone' => 'present|alpha_dash',
            'data.*.highSpeedMark'    => 'required|in:0,1',
            'data.*.qrcodePay'        => 'required|in:0,1',
            'data.*.qrcodeSitePay'    => 'required|in:0,1',
            'data.*.activePay'        => 'required|in:0,1',
            'data.*.globalEnable'     => 'required|in:0,1',
            'data.*.putawayMark'      => 'required|in:0,1',
        ],
        'receive_sqZsh'         => [
            'stationId'   => 'required|alpha_num',
            'stationName' => 'required',
            'lat'         => 'required|numeric',
            'lng'         => 'required|numeric',
            'address'     => 'required',
            'openTime'    => 'string',
            'oilGunBeans' => 'required|array',
            'type'        => 'present',
            'status'      => 'required',
            'isHighspeed' => 'present',
        ],
        'receive_mtlSy'         => [
            'stationId' => 'required',
        ],
        'receive_sh'            => [
            'id'        => 'required|alpha_num',
            'title'     => 'required',
            'lat'       => 'required|numeric',
            'lng'       => 'required|numeric',
            'status'    => 'required|in:0,1,-1',
            'address'   => 'present',
            'gun'       => 'nullable|array',
            'oil_brand' => 'present',
        ],
        'receive_shsx'          => [
            'id'        => 'required|alpha_num',
            'title'     => 'required',
            'lat'       => 'required|numeric',
            'lng'       => 'required|numeric',
            'status'    => 'required|in:0,1,-1',
            'address'   => 'present',
            'gun'       => 'nullable|array',
            'oil_brand' => 'present',
        ],
        'receive_jt'            => [
            'stationId'          => 'required|numeric',
            'stationName'        => 'required',
            'tel'                => 'required|alpha_dash',
            'lat'                => 'required|numeric',
            'lng'                => 'required|numeric',
            'province'           => 'required|numeric',
            'city'               => 'required|numeric',
            'address'            => 'required',
            'openTime'           => 'string',
            'status'             => 'required|in:online,pause,offline',
            'guns'               => 'present|nullable|json',
            'tagType'            => 'in:A,B',
            'stationBrand'       => 'present|nullable',
            'payType'            => 'required|in:1,2,3,4',
            'isVerificationCode' => 'required|in:0,1',
        ],
        'receive_mj'            => [
            'identifier'    => 'required|alpha_dash',
            'sname'         => 'required',
            'latitude'      => 'required|numeric',
            'longitude'     => 'required|numeric',
            'saddress'      => 'present',
            'open'          => 'required|in:0,1,2',
            'tel'           => 'present',
            'stationPrices' => 'present|array',
        ],
        'receive_xmsk'          => [
            'oilStationCode' => 'required',
            'oilStationName' => 'required',
            'contactNumber'  => 'required',
            'latitude'       => 'required|numeric',
            'longitude'      => 'required|numeric',
            'address'        => 'required',
            'businessHours'  => 'string',
            'isStop'         => 'required|in:0,1',
            'gunList'        => 'present|nullable|array',
            'oilStationType' => 'present|nullable',
        ],
        'receive_yundatong'     => [
            'stationId'    => 'required|alpha_dash',
            'stationName'  => 'required',
            'lat'          => 'required|numeric',
            'lng'          => 'required|numeric',
            'address'      => 'required',
            'priceList'    => 'present|nullable|array',
            'provinceCode' => 'required|numeric',
            'cityCode'     => 'required|numeric',
            'contactPhone' => 'present|nullable',
            'payMode'      => 'required|in:0,1',
            'status'       => 'required|in:0,1',
            'verifyMode'   => 'required|in:0,1,2',
        ],
        'receive_gaodeng'       => [
            'oilStationNo'     => 'required|alpha_dash',
            'oilStationName'   => 'required',
            'latitude'         => 'required|numeric',
            'longitude'        => 'required|numeric',
            'address'          => 'required',
            'oilInfoList'      => 'present|nullable|array',
            'contactMobile'    => 'present|nullable',
            'businessType'     => 'present|nullable',
            'keepAccounts'     => 'required|in:1,2',
            'confirmMode'      => 'required',
            'oilStationStatus' => 'required|in:1,2',
            'provinceId'       => 'required|numeric',
            'cityId'           => 'required|numeric',
        ],
        'receive_oil'           => [
            'oil_name'      => 'required',
            'oil_type'      => 'string',
            'oil_level'     => 'string',
            'price'         => 'required|numeric',
            'listing_price' => 'numeric',
            'start_time'    => 'date_format:"Y-m-d H:i:s"',
            'end_time'      => 'date_format:"Y-m-d H:i:s"',
        ],
        'receive_oil_zy'        => [
            'skuName' => 'required',
            'lvPrice' => 'required|numeric',
            'skuCode' => 'required|alpha_dash',
        ],
        'receive_oil_wjy'       => [
            'fuelName' => 'required',
            'price'    => 'required|numeric',
            'fuelNo'   => 'required|alpha_dash',
        ],
        'receive_oil_cy'        => [
            'gun_price'     => 'required|numeric',
            'price'         => 'required|numeric',
            'oil_number'    => 'required',
            'gun_numbers'   => 'required|array',
            'gun_numbers.*' => 'integer',
        ],
        'receive_oil_sqzl'      => [
            'lvPrice'   => 'required|numeric',
            'downValue' => 'numeric',
            'fuelNo'    => 'required|alpha_dash',
        ],
        'receive_oil_zhyk'      => [
            'oilPrice'         => 'required|numeric',
            'discountOilPrice' => 'required|numeric',
            'gunNo'            => 'required',
            'plateOilCode'     => 'required|numeric',
        ],
        'receive_oil_xyn'       => [
            'oilsPrice'     => 'required|numeric',
            'oilsSitePrice' => 'required|numeric',
            'oilsCode'      => 'required',
            'id'            => 'required|alpha_dash',
            'enableMark'    => 'required|in:0,1',
        ],
        'receive_oil_sqZsh'     => [
            'lvPrice' => 'required|numeric',
            'fuelNo'  => 'required|alpha_dash',
        ],
        'receive_oil_jt'        => [
            'skuCode'       => 'required|alpha_dash',
            'gunId'         => 'present',
            'discountPrice' => 'required|numeric',
            'marketPrice'   => 'required|numeric',
            'guidePrice'    => 'required|numeric',
        ],
        'receive_oil_mj'        => [
            'gasType'     => 'required|alpha_dash',
            'mjPrice'     => 'numeric',
            'listedPrice' => 'numeric',
        ],
        'receive_oil_xmsk'      => [
            'productNo' => 'required|alpha_dash',
            'oilGunNo'  => 'required',
            'gunPrice'  => 'required|numeric',
        ],
        'receive_oil_yundatong' => [
            'oilNo'         => 'required',
            'gunList'       => 'present|array|nullable',
            'price'         => 'required|numeric',
            'priceGun'      => 'required|numeric',
            'priceOfficial' => 'required|numeric',
        ],
        'receive_oil_gaodeng'   => [
            'enterprisePrice' => 'required|numeric',
            'oilGunNoList'    => 'required',
            'oilNo'           => 'required',
        ],
    ];

    public function __construct()
    {
        parent::__construct();
        $this->loadInputNoData();
        $this->loadValidateCallback();
    }

    public function loadInputNoData()
    {
        $this->inputNoData = [
            'wjy'   => true,
            'saic'  => true,
            'gb'    => true,
            'gb_tj' => true,
            'ygy'   => true,
            'hsy'   => true,
            'zhyk'  => true,
            'zdc'   => true,
        ];
    }

    public function loadValidateCallback()
    {
        $this->validateCallback['wjy'] = function ($responseCode) {
            responseFormatForWjy($responseCode, null, true);
        };
        $this->validateCallback['saic'] = function ($responseCode) {
            responseFormatForSaic($responseCode, null, true);
        };
        $this->validateCallback['gb'] = function ($responseCode) {
            responseFormatForGb($responseCode, null, true);
        };
        $this->validateCallback['gb_tj'] = function ($responseCode) {
            responseFormatForGb($responseCode, null, true);
        };
        $this->validateCallback['zdc'] = function ($responseCode) {
            responseFormatForZdc($responseCode, null, '', true);
        };
        $this->validateCallback['ad'] = function ($responseCode) {
            responseFormatForAd($responseCode, null, true);
        };
        $this->validateCallback['xyn'] = function ($responseCode) {
            responseFormatForXyn($responseCode, null, true);
        };
        $this->validateCallback['sqzl'] = $this->validateCallback['sqZsh'] = function ($responseCode) {
            responseFormatForSqzl($responseCode, null, true);
        };
        $this->validateCallback['sh'] = $this->validateCallback['shsx'] = function ($responseCode) {
            responseFormatForSh($responseCode);
        };
        $this->validateCallback['jt'] = function ($responseCode) {
            responseFormatForJt($responseCode, null, true);
        };
        $this->validateCallback['xmsk'] = function ($responseCode) {
            responseFormatForXmsk($responseCode, null, true);
        };
        $this->validateCallback['yundatong'] = function ($responseCode) {
            responseFormatForYUNDATONG($responseCode, null);
        };
        $this->validateCallback['gaodeng'] = function ($responseCode) {
            responseFormatForGaoDeng($responseCode, null);
        };
    }

    /**
     * 查询站点及价格数据
     * @param Request $request
     * @return JsonResponse
     * ---------------------------------------------------
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2019/11/6 4:39 下午
     */
    public function queryOne(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return (new QueryLogic($this->requestData))->queryOne();
    }

    /**
     * 查询全量站点及价格数据
     * @param Request $request
     * @return Response
     * ---------------------------------------------------
     * @throws Throwable
     * @since 2019/11/6 4:39 下午
     * <AUTHOR> <<EMAIL>>
     */
    public function queryAll(Request $request): Response
    {
        $this->validateRequestParam($request);
        return (new QueryLogic($this->requestData))->queryAll();
    }

    /**
     * 接收外部供应商站点数据增量推送
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/18 2:17 下午
     */
    public function receive(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);

        //check油站价格及油品信息字段名,默认为标准字段price_list,找油传入guns
        $stationOilCheckField = $this->stationOilFieldMapping[$this->requestData['auth_data']['name_abbreviation']] ?? $this->stationOilFieldMapping['default'];
        if (!empty($stationOilCheckField)) {
            //找油网推送过来的站点数据需要再反序列化一次油品数据字段
            if ($this->requestData['auth_data']['name_abbreviation'] == 'zy') {
                $this->requestData['guns'] = json_decode($this->requestData['guns'], true);
            }
            $checkRequestData = $this->requestData;
            if ($this->requestData['auth_data']['name_abbreviation'] == 'jt') {
                $this->requestData['data']['guns'] = json_decode($this->requestData['data']['guns'], true) ?? [];
                $checkRequestData = $this->requestData['data'];
            }
            if ($this->requestData['auth_data']['name_abbreviation'] == 'xyn') {
                $checkRequestData = $this->requestData['data'];
            }
            unset($checkRequestData['auth_data']);
            if (checkIsAssocArray($checkRequestData)) {
                $checkRequestData = [$checkRequestData];
            }
            foreach ($checkRequestData as $cv) {
                if (!empty($cv[$stationOilCheckField]) and checkIsAssocArray($cv[$stationOilCheckField])) {
                    if (isset($this->validateCallback[$this->requestData['auth_data']['name_abbreviation'] ?? ''])
                        and $callback = $this->validateCallback[$this->requestData['auth_data']['name_abbreviation'] ?? '']) {
                        return $callback(4120135);
                    }
                    return responseFormat(4120135);
                }

                foreach ($cv[$stationOilCheckField] as $v) {
                    $validate = $this->customRules['receive_oil'];
                    //添加对能源种类的准确判断
                    $validate['oil_name'] = 'required|in:' . implode(',', array_keys(config("oil.oil_type")));
                    $messages = $this->customMessages['receive_oil'];
                    $customRuleKey = "receive_oil_{$this->requestData['auth_data']['name_abbreviation']}";

                    if (isset($this->customRules[$customRuleKey]) and isset($this->customMessages[$customRuleKey])) {
                        $validate = $this->customRules[$customRuleKey];
                        $messages = $this->customMessages[$customRuleKey];
                    }

                    $validator = Validator::make($v, $validate, $messages);

                    if ($validator->fails()) {
                        if (isset($this->validateCallback[$this->requestData['auth_data']['name_abbreviation'] ?? ''])
                            and $callback = $this->validateCallback[$this->requestData['auth_data']['name_abbreviation'] ?? '']) {
                            return $callback($validator->errors()->first());
                        }
                        responseFormat($validator->errors()->first(), [], true);
                    }
                }
            }
        }
        return (new ReceiveMainLogic($this->requestData))->handle();
    }
}
