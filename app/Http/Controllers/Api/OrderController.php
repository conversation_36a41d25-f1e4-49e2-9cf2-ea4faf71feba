<?php

namespace App\Http\Controllers\Api;

use App\Models\Data\Log\ResponseLog;
use App\Models\Logic\Code\GetSecondaryPaymentQrCode as GetSecondaryPaymentQrCodeLogic;
use App\Models\Logic\Order\Cancel as CancelLogic;
use App\Models\Logic\Order\GetFlow\Main as GetFlowLogic;
use App\Models\Logic\Order\Query as QueryLogic;
use App\Models\Logic\Order\Refund\Main as RefundLogic;
use App\Models\Logic\Order\RefundApplication\Main as RefundApplicationMainLogic;
use App\Models\Logic\Order\RefundCallback\Main as RefundCallbackMainLogic;
use App\Models\Logic\Order\ReToBePaid as ReToBePaidLogic;
use App\Models\Logic\Order\Split\Main as SplitLogic;
use App\Models\Logic\Order\StatusChange\Main as StatusChangeLogic;
use App\Models\Logic\Order\ToBePaid as ToBePaidLogic;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class OrderController extends BasicController
{
    public $customMessages = [
        'query'                          => [
            'start_time.required_without' => 4120019,
            'start_time.date_format'      => 4120020,
            'end_time.required_without'   => 4120021,
            'end_time.date_format'        => 4120022,
            'pay_status.alpha_num'        => 4120023,
            'order_id.alpha_dash'         => 4120018,
            'gps_no.alpha_num'            => 4120024,
            'oil_time.date_format'        => 4120026,
        ],
        'query_yc'                       => [
            'orderid.required'     => 4120018,
            'orderid.numeric'      => 4120018,
            'siteid.required'      => 4120160,
            'siteid.alpha_dash'    => 4120160,
            'productList.required' => 4120161,
            'productList.array'    => 4120161,
        ],
        'query_sc'                       => [
            'siteID.required'                      => 4120160,
            'siteID.alpha_dash'                    => 4120160,
            'productList.required'                 => 4120161,
            'productList.array'                    => 4120161,
            'stan.required'                        => 4120163,
            'stan.numeric'                         => 4120163,
            'shellDiscount.required'               => 4120010,
            'shellDiscount.numeric'                => 4120010,
            'productList.*.productCode.required'   => 4120164,
            'productList.*.productCode.alpha_dash' => 4120164,
            'productList.*.amount.required'        => 4120165,
            'productList.*.amount.numeric'         => 4120165,
            'qrCode.required'                      => 4120002,
            'qrCode.alpha_dash'                    => 4120002,
        ],
        'query_sx'                       => [
            'siteID.required'                      => 4120160,
            'siteID.alpha_dash'                    => 4120160,
            'productList.required'                 => 4120161,
            'productList.array'                    => 4120161,
            'stan.required'                        => 4120163,
            'stan.numeric'                         => 4120163,
            'shellDiscount.required'               => 4120010,
            'shellDiscount.numeric'                => 4120010,
            'productList.*.productCode.required'   => 4120164,
            'productList.*.productCode.alpha_dash' => 4120164,
            'productList.*.amount.required'        => 4120165,
            'productList.*.amount.numeric'         => 4120165,
            'qrCode.required'                      => 4120002,
            'qrCode.alpha_dash'                    => 4120002,
        ],
        'query_fo'                       => [
            'order_id.required'   => 4120162,
            'order_id.alpha_dash' => 4120162,
        ],
        'query_gs'                       => [
            'serial_no.required'   => 4120451,
            'serial_no.alpha_dash' => 4120452,
        ],
        'query_ygy'                      => [
            'orderSn.required'   => 4120017,
            'orderSn.alpha_dash' => 4120018,
        ],
        'query_ygyDemo'                  => [
            'orderSn.required'   => 4120017,
            'orderSn.alpha_dash' => 4120018,
        ],
        'query_mb'                       => [
            'originalOrderNo.required_without' => 4120162,
            'originalOrderNo.alpha_dash'       => 4120162,
            'orderNo.required_without'         => 4120050,
            'orderNo.alpha_dash'               => 4120051,
        ],
        'query_ky'                       => [
            'order_id.required'   => 4120162,
            'order_id.alpha_dash' => 4120162,
        ],
        'toBePaid'                       => [
            'station_id.required'   => 4120003,
            'station_id.alpha_dash' => 4120004,
            'order_id.required'     => 4120017,
            'order_id.alpha_dash'   => 4120018,
            'oil_num.required'      => 4120005,
            'oil_num.numeric'       => 4120006,
            'price.required'        => 4120007,
            'price.numeric'         => 4120008,
            'money.required'        => 4120009,
            'money.numeric'         => 4120010,
            'oil_name.required'     => 4120013,
            'oil_level.string'      => 4120015,
            'oil_type.string'       => 4120011,
            'card_no.required'      => 4120062,
            'card_no.alpha_num'     => 4120063,
        ],
        'toBePaid_ds'                    => [
            'station_id.required'     => 4120003,
            'station_id.alpha_dash'   => 4120004,
            'order_id.required'       => 4120017,
            'order_id.alpha_dash'     => 4120018,
            'oil_num.required'        => 4120005,
            'oil_num.numeric'         => 4120006,
            'price.required'          => 4120007,
            'price.numeric'           => 4120008,
            'money.required'          => 4120009,
            'money.numeric'           => 4120010,
            'oil_name.required'       => 4120013,
            'oil_level.string'        => 4120015,
            'oil_type.string'         => 4120011,
            'driver_phone.regex'      => 4120463,
            'deduction_mode.required' => 4120464,
            'deduction_mode.in'       => 4120465,
        ],
        'toBePaid_sffy'                  => [
            'station_id.required'     => 4120003,
            'station_id.alpha_dash'   => 4120004,
            'order_id.required'       => 4120017,
            'order_id.alpha_dash'     => 4120018,
            'oil_num.required'        => 4120005,
            'oil_num.numeric'         => 4120006,
            'price.required'          => 4120007,
            'price.numeric'           => 4120008,
            'money.required'          => 4120009,
            'money.numeric'           => 4120010,
            'oil_code.required'       => 4120139,
            'driver_phone.numeric'    => 4120463,
            'deduction_mode.required' => 4120464,
            'deduction_mode.in'       => 4120465,
        ],
        'toBePaid_jd'                    => [
            'station_id.required'        => 4120003,
            'station_id.alpha_dash'      => 4120004,
            'oil_num.required'           => 4120005,
            'oil_num.numeric'            => 4120006,
            'price.required'             => 4120007,
            'price.numeric'              => 4120008,
            'money.required'             => 4120009,
            'money.numeric'              => 4120010,
            'oil_type.required'          => 4120011,
            'oil_name.required'          => 4120013,
            'oil_level.required'         => 4120016,
            'order_id.required'          => 4120017,
            'order_id.alpha_dash'        => 4120018,
            'refueling_time.required'    => 4120114,
            'refueling_time.date_format' => 4120113,
        ],
        'toBePaid_htx'                   => [
            'station_id.required'           => 4120003,
            'station_id.alpha_dash'         => 4120004,
            'oil_num.required'              => 4120005,
            'oil_num.numeric'               => 4120006,
            'price.required'                => 4120007,
            'price.numeric'                 => 4120008,
            'money.required'                => 4120009,
            'money.numeric'                 => 4120010,
            'oil_name.required'             => 4120013,
            'order_id.required'             => 4120017,
            'order_id.alpha_dash'           => 4120018,
            'refueling_time.required'       => 4120114,
            'refueling_time.date_format'    => 4120113,
            'company_subject_name.required' => 4120468,
            'driver_phone.required'         => 4120404,
        ],
        'toBePaid_kl'                    => [
            'money.required'      => 4120009,
            'money.numeric'       => 4120010,
            'order_id.required'   => 4120017,
            'order_id.alpha_dash' => 4120018,
            'token.required'      => 4120421,
        ],
        'toBePaid_yk'                    => [
            'money.required'         => 4120009,
            'money.numeric'          => 4120010,
            'order_id.required'      => 4120017,
            'order_id.alpha_dash'    => 4120018,
            'order_sign.required_if' => 4120427,
            'token.required'         => 4120421,
            'order_type.required'    => 4120428,
            'order_type.in'          => 4120429,
        ],
        'toBePaid_zy'                    => [
            'stationId.required' => 4120003,
            'stationId.numeric'  => 4120004,
            'oilNum.required'    => 4120005,
            'oilNum.numeric'     => 4120006,
            'price.required'     => 4120007,
            'price.numeric'      => 4120008,
            'money.required'     => 4120009,
            'money.numeric'      => 4120010,
            'oilName.required'   => 4120013,
            'oilLevel.string'    => 4120015,
            'oilType.string'     => 4120011,
            'orderId.required'   => 4120017,
            'orderId.alpha_dash' => 4120018,
            'cardNo.required'    => 4120062,
            'cardNo.numeric'     => 4120063,
        ],
        'toBePaid_wjy'                   => [
            'oilsStationId.required'     => 4120003,
            'oilsStationId.numeric'      => 4120004,
            'count.required'             => 4120005,
            'count.numeric'              => 4120006,
            'price.required'             => 4120007,
            'price.numeric'              => 4120008,
            'totalPrice.required'        => 4120009,
            'totalPrice.numeric'         => 4120010,
            'orderSn.required'           => 4120050,
            'orderSn.alpha_dash'         => 4120051,
            'foreign_unique_id.required' => 4120052,
            'fuelNo.required'            => 4120466,
            'fuelNo.alpha_dash'          => 4120467,
        ],
        'toBePaid_rq'                    => [
            'station_id.required'            => 4120003,
            'station_id.alpha_dash'          => 4120004,
            'order_id.required'              => 4120017,
            'order_id.alpha_dash'            => 4120018,
            'oil_num.required'               => 4120005,
            'oil_num.numeric'                => 4120006,
            'price.required'                 => 4120007,
            'price.numeric'                  => 4120008,
            'money.required'                 => 4120009,
            'money.numeric'                  => 4120010,
            'oil_name.required'              => 4120013,
            'oil_level.string'               => 4120015,
            'oil_type.string'                => 4120011,
            'driver_phone.numeric'           => 4120463,
            'deduction_mode.required'        => 4120464,
            'deduction_mode.in'              => 4120465,
            'legalList.required'             => 4120480,
            'legalList.array'                => 4120481,
            'legalList.*.legalCode.required' => 4120482,
            'legalList.*.money.required'     => 4120483,
        ],
        'toBePaid_ygy'                   => [
            'stationId.required'     => 4120003,
            'stationId.alpha_dash'   => 4120004,
            'orderSn.required'       => 4120017,
            'orderSn.alpha_dash'     => 4120018,
            'count.required'         => 4120005,
            'count.numeric'          => 4120006,
            'discountPrice.required' => 4120007,
            'discountPrice.numeric'  => 4120008,
            'price.required'         => 4120484,
            'price.numeric'          => 4120485,
            'totalPrice.required'    => 4120009,
            'totalPrice.numeric'     => 4120010,
            'amountPrice.required'   => 4120493,
            'amountPrice.numeric'    => 4120494,
            'fuelNo.required'        => 4120139,
            'driverPhone.numeric'    => 4120463,
            'gunNumber.numeric'      => 4120495,
            'companyId.required'     => 4120496,
            'companyId.alpha_num'    => 4120497,
            'deductionMode.required' => 4120464,
            'deductionMode.in'       => 4120465,
        ],
        'toBePaid_ygyDemo'               => [
            'stationId.required'     => 4120003,
            'stationId.alpha_dash'   => 4120004,
            'orderSn.required'       => 4120017,
            'orderSn.alpha_dash'     => 4120018,
            'count.required'         => 4120005,
            'count.numeric'          => 4120006,
            'discountPrice.required' => 4120007,
            'discountPrice.numeric'  => 4120008,
            'price.required'         => 4120484,
            'price.numeric'          => 4120485,
            'totalPrice.required'    => 4120009,
            'totalPrice.numeric'     => 4120010,
            'amountPrice.required'   => 4120493,
            'amountPrice.numeric'    => 4120494,
            'fuelNo.required'        => 4120139,
            'driverPhone.numeric'    => 4120463,
            'gunNumber.numeric'      => 4120495,
            'companyId.required'     => 4120496,
            'companyId.alpha_num'    => 4120497,
            'deductionMode.required' => 4120464,
            'deductionMode.in'       => 4120465,
        ],
        'toBePaid_desp|2H8BBD'           => [
            'station_id.required'     => 4120003,
            'station_id.alpha_dash'   => 4120004,
            'order_id.required'       => 4120017,
            'order_id.alpha_dash'     => 4120018,
            'oil_num.required'        => 4120005,
            'oil_num.numeric'         => 4120006,
            'price.required'          => 4120007,
            'price.numeric'           => 4120008,
            'money.required'          => 4120009,
            'money.numeric'           => 4120010,
            'oil_name.required'       => 4120139,
            'driver_phone.required'   => 4120404,
            'driver_phone.numeric'    => 4120463,
            'deduction_mode.required' => 4120464,
            'deduction_mode.in'       => 4120465,
        ],
        'toBePaid_desp|2A6LA9'           => [
            'station_id.required'     => 4120003,
            'station_id.alpha_dash'   => 4120004,
            'order_id.required'       => 4120017,
            'order_id.alpha_dash'     => 4120018,
            'oil_num.required'        => 4120005,
            'oil_num.numeric'         => 4120006,
            'price.required'          => 4120007,
            'price.numeric'           => 4120008,
            'money.required'          => 4120009,
            'money.numeric'           => 4120010,
            'oil_name.required'       => 4120139,
            'driver_phone.required'   => 4120404,
            'driver_phone.numeric'    => 4120463,
            'deduction_mode.required' => 4120464,
            'deduction_mode.in'       => 4120465,
        ],
        'toBePaid_gbdw'                  => [
            'station_id.required'     => 4120003,
            'station_id.alpha_dash'   => 4120004,
            'order_id.required'       => 4120017,
            'order_id.alpha_dash'     => 4120018,
            'oil_num.required'        => 4120005,
            'oil_num.numeric'         => 4120006,
            'price.required'          => 4120007,
            'price.numeric'           => 4120008,
            'money.required'          => 4120009,
            'money.numeric'           => 4120010,
            'oil_type_id.required'    => 4120139,
            'deduction_mode.required' => 4120464,
            'deduction_mode.in'       => 4120465,
            'driver_phone.required'   => 4120404,
            'driver_phone.numeric'    => 4120463,
        ],
        'toBePaid_fy'                    => [
            'station_id.required'     => 4120003,
            'station_id.alpha_dash'   => 4120004,
            'order_id.required'       => 4120017,
            'order_id.alpha_dash'     => 4120018,
            'oil_num.required'        => 4120005,
            'oil_num.numeric'         => 4120006,
            'price.required'          => 4120007,
            'price.numeric'           => 4120008,
            'money.required'          => 4120009,
            'money.numeric'           => 4120010,
            'oil_name.required'       => 4120013,
            'oil_level.string'        => 4120015,
            'oil_type.string'         => 4120011,
            'driver_code.required'    => 4120430,
            'deduction_mode.required' => 4120464,
            'deduction_mode.in'       => 4120465,
        ],
        'toBePaid_zj'                    => [
            'station_id.required'     => 4120003,
            'station_id.alpha_dash'   => 4120004,
            'order_id.required'       => 4120017,
            'order_id.alpha_dash'     => 4120018,
            'oil_num.required'        => 4120005,
            'oil_num.numeric'         => 4120006,
            'price.required'          => 4120007,
            'price.numeric'           => 4120008,
            'money.required'          => 4120009,
            'money.numeric'           => 4120010,
            'oil_name.required'       => 4120013,
            'oil_level.string'        => 4120015,
            'oil_type.string'         => 4120011,
            'driver_phone.numeric'    => 4120463,
            'deduction_mode.required' => 4120464,
            'deduction_mode.in'       => 4120465,
            'driver_id.required'      => 4120522,
        ],
        'toBePaid_hr'                    => [
            'station_id.required'     => 4120003,
            'station_id.alpha_dash'   => 4120004,
            'order_id.required'       => 4120017,
            'order_id.alpha_dash'     => 4120018,
            'oil_num.required'        => 4120005,
            'oil_num.numeric'         => 4120006,
            'price.required'          => 4120007,
            'price.numeric'           => 4120008,
            'money.required'          => 4120009,
            'money.numeric'           => 4120010,
            'oil_code.required'       => 4120139,
            'driver_phone.numeric'    => 4120463,
            'deduction_mode.required' => 4120464,
            'deduction_mode.in'       => 4120465,
        ],
        'toBePaid_hll'                   => [
            'station_id.required'     => 4120003,
            'station_id.alpha_dash'   => 4120004,
            'order_id.required'       => 4120017,
            'order_id.alpha_dash'     => 4120018,
            'oil_num.required'        => 4120005,
            'oil_num.numeric'         => 4120006,
            'price.required'          => 4120007,
            'price.numeric'           => 4120008,
            'money.required'          => 4120009,
            'money.numeric'           => 4120010,
            'oil_name.required'       => 4120013,
            'oil_level.string'        => 4120015,
            'oil_type.string'         => 4120011,
            'deduction_mode.required' => 4120464,
            'deduction_mode.in'       => 4120465,
        ],
        'toBePaid_ad'                    => [
            'thirdStationNo.required'   => 4120003,
            'thirdStationNo.alpha_dash' => 4120004,
            'orderNo.required'          => 4120017,
            'orderNo.alpha_dash'        => 4120018,
            'goodsNumber.required'      => 4120005,
            'goodsNumber.numeric'       => 4120006,
            'settleUnitPrice.required'  => 4120007,
            'settleUnitPrice.numeric'   => 4120008,
            'orderUnitPrice.required'   => 4120484,
            'orderUnitPrice.numeric'    => 4120485,
            'settleAmount.required'     => 4120009,
            'settleAmount.numeric'      => 4120010,
            'orderAmount.required'      => 4120493,
            'orderAmount.numeric'       => 4120494,
            'goodsNo.required'          => 4120139,
            'driverPhone.numeric'       => 4120463,
            'gunNumber.numeric'         => 4120495,
        ],
        'toBePaid_zey'                   => [
            'station_id.required'         => 4120003,
            'station_id.alpha_dash'       => 4120004,
            'refueling_subject.required'  => 4120570,
            'refueling_subject.alpha_num' => 4120571,
            'order_id.required'           => 4120017,
            'order_id.alpha_dash'         => 4120018,
            'money.required'              => 4120009,
            'money.numeric'               => 4120010,
            'oil_id.required'             => 4120013,
            'oil_id.string'               => 4120015,
            'deduction_mode.required'     => 4120464,
            'deduction_mode.in'           => 4120465,
            'gun_money.required'          => 4120493,
            'gun_money.numeric'           => 4120494,
            'oil_num.required_if'         => 4120005,
            'oil_num.numeric'             => 4120006,
        ],
        'toBePaid_hjy'                   => [
            'station_id.required'   => 4120003,
            'station_id.alpha_dash' => 4120004,
            'order_sn.required'     => 4120017,
            'order_sn.alpha_dash'   => 4120018,
            'oil_num.required'      => 4120005,
            'oil_num.numeric'       => 4120006,
            'pay_price.required'    => 4120007,
            'pay_price.numeric'     => 4120008,
            'price.required'        => 4120484,
            'price.numeric'         => 4120485,
            'pay_money.required'    => 4120449,
            'pay_money.numeric'     => 4120450,
            'money.required'        => 4120493,
            'money.numeric'         => 4120494,
            'goods_no.required'     => 4120139,
            'extends.required'      => 4120440,
        ],
        'reToBePaid'                     => [
            'station_id.required'        => 4120003,
            'station_id.alpha_dash'      => 4120004,
            'order_id.required'          => 4120017,
            'order_id.alpha_dash'        => 4120018,
            'oil_num.required'           => 4120005,
            'oil_num.numeric'            => 4120006,
            'price.required'             => 4120007,
            'price.numeric'              => 4120008,
            'money.required'             => 4120009,
            'money.numeric'              => 4120010,
            'oil_name.required'          => 4120013,
            'oil_level.string'           => 4120015,
            'oil_type.string'            => 4120011,
            'orderId.required'           => 4120017,
            'orderId.alpha_dash'         => 4120018,
            'card_no.required'           => 4120062,
            'card_no.numeric'            => 4120063,
            'refueling_time.required'    => 4120114,
            'refueling_time.date_format' => 4120113,
        ],
        'reToBePaid_fo'                  => [
            'station_id.required'         => 4120003,
            'station_id.alpha_dash'       => 4120004,
            'orgcode.alpha_num'           => 4120402,
            'orgcode.required'            => 4120401,
            'gun_id.alpha_dash'           => 4120400,
            'oil_num.required'            => 4120005,
            'oil_num.numeric'             => 4120006,
            'oil_sale_price.required'     => 4120007,
            'oil_sale_price.numeric'      => 4120008,
            'oil_money.required'          => 4120009,
            'oil_money.numeric'           => 4120010,
            'oil_name.required'           => 4120013,
            'oil_level.string'            => 4120015,
            'oil_type.string'             => 4120011,
            'oil_time.required'           => 4120114,
            'oil_time.date_format'        => 4120113,
            'driver_phone.required'       => 4120404,
            'truck_no.required'           => 4120403,
            'third_account_no.alpha_dash' => 4120405,
            'ticket_image.url'            => 4120406,
            'truck_image.url'             => 4120407,
            'other_image.url'             => 4120408,
            'province_code.required'      => 4120125,
            'province_code.numeric'       => 4120126,
            'city_code.required'          => 4120127,
            'city_code.numeric'           => 4120128,
        ],
        'reToBePaid_jd'                  => [
            'station_id.required'        => 4120003,
            'station_id.alpha_dash'      => 4120004,
            'oil_num.required'           => 4120005,
            'oil_num.numeric'            => 4120006,
            'price.required'             => 4120007,
            'price.numeric'              => 4120008,
            'money.required'             => 4120009,
            'money.numeric'              => 4120010,
            'oil_type.required'          => 4120011,
            'oil_name.required'          => 4120013,
            'oil_level.required'         => 4120016,
            'order_id.required'          => 4120017,
            'order_id.alpha_dash'        => 4120018,
            'refueling_time.required'    => 4120114,
            'refueling_time.date_format' => 4120113,
        ],
        'reToBePaid_zy'                  => [
            'stationId.required'  => 4120003,
            'stationId.numeric'   => 4120004,
            'oilNum.required'     => 4120005,
            'oilNum.numeric'      => 4120006,
            'price.required'      => 4120007,
            'price.numeric'       => 4120008,
            'money.required'      => 4120009,
            'money.numeric'       => 4120010,
            'oilName.required'    => 4120013,
            'oilLevel.string'     => 4120015,
            'oilType.string'      => 4120011,
            'orderId.required'    => 4120017,
            'orderId.alpha_dash'  => 4120018,
            'cardNo.required'     => 4120062,
            'cardNo.numeric'      => 4120063,
            'oilTime.required'    => 4120114,
            'oilTime.date_format' => 4120113,
        ],
        'cancel'                         => [
            'order_id.required'   => 4120017,
            'order_id.alpha_dash' => 4120018,
        ],
        'cancel_zy'                      => [
            'orderId.required'   => 4120017,
            'orderId.alpha_dash' => 4120018,
        ],
        'cancel_ad'                      => [
            'orderNo.required'           => 4120017,
            'orderNo.alpha_dash'         => 4120018,
            'supplierOrderNo.required'   => 4120052,
            'supplierOrderNo.alpha_dash' => 4120053,
        ],
        'getSecondaryPaymentCertificate' => [
            'order_id.required' => 4120162,
        ],
        'queryFlow_ygy'                  => [
            'companyId.required'    => 4120496,
            'companyId.alpha_num'   => 4120497,
            'startTime.required'    => 4120019,
            'startTime.date_format' => 4120020,
            'endTime.required'      => 4120021,
            'endTime.date_format'   => 4120022,
        ],
        'queryFlow_ygyDemo'              => [
            'companyId.required'    => 4120496,
            'companyId.alpha_num'   => 4120497,
            'startTime.required'    => 4120019,
            'startTime.date_format' => 4120020,
            'endTime.required'      => 4120021,
            'endTime.date_format'   => 4120022,
        ],
        'refundApplication'              => [
            'order_id.required' => 4120162,
            'order_id.numeric'  => 4120162,
        ],
        'refundApplication_mb'           => [
            'originalOrderNo.required'  => 4120162,
            'originalOrderNo.alpha_num' => 4120162,
        ],
        'statusChange_sqZsh'             => [
            'uniqueStr.required'   => 4120017,
            'uniqueStr.alpha_dash' => 4120018,
            'orderState.required'  => 4120545,
            'orderState.in'        => 4120546,
        ],
        'statusChange_xmsk'              => [
            'orderNo.required'    => 4120017,
            'orderNo.alpha_dash'  => 4120018,
            'orderState.required' => 4120545,
            'orderState.in'       => 4120546,
        ],
        'refundCallback_ygy'             => [
            'foreignUniqueId.required'  => 4120052,
            'foreignUniqueId.alpha_num' => 4120053,
            'approvalResult.required'   => 4120542,
            'approvalResult.in'         => 4120543,
        ],
        'refundCustomerForOrderCenter'   => [
            'org_code.alpha_num'  => 4120402,
            'org_code.required'   => 4120401,
            'order_id.alpha_dash' => 4120053,
            'order_id.required'   => 4120052,
            'reason.required'     => 4120488,
        ],
        'split_my'                       => [
            'order_id.required'                  => 4120052,
            'order_id.alpha_num'                 => 4120053,
            'customer_order_id.required'         => 4120050,
            'customer_order_id.alpha_dash'       => 4120051,
            'details.required'                   => 4120601,
            'details.array'                      => 4120602,
            'details.*.pay_company_id.required'  => 4120603,
            'details.*.pay_company_id.alpha_num' => 4120604,
            'details.*.sub_gun_money.required'   => 4120605,
            'details.*.sub_gun_money.numeric'    => 4120606,
            'details.*.sub_money.required'       => 4120607,
            'details.*.sub_money.numeric'        => 4120608,
        ],
    ];
    public $customRules    = [
        'query'                          => [
            'start_time' => 'required_without:order_id|date_format:"Y-m-d H:i:s"',
            'end_time'   => 'required_without:order_id|date_format:"Y-m-d H:i:s"',
            'pay_status' => 'alpha_num',
            'order_id'   => 'alpha_dash',
            'card_no'    => 'integer',
            'oil_time'   => 'date_format:"Y-m-d H:i:s"',
            'gps_no'     => 'alpha_num',
        ],
        'query_yc'                       => [
            'orderid'     => 'required|numeric',
            'siteid'      => 'required|alpha_dash',
            'productList' => 'required|array',
        ],
        'query_sc'                       => [
            'stan'                      => 'required|numeric',
            'siteID'                    => 'required|alpha_dash',
            'shellDiscount'             => 'required|numeric',
            'productList'               => 'required|array',
            'productList.*.productCode' => 'required|alpha_dash',
            'productList.*.amount'      => 'required|numeric',
            'qrCode'                    => 'required|alpha_dash',
        ],
        'query_sx'                       => [
            'stan'                      => 'required|numeric',
            'siteID'                    => 'required|alpha_dash',
            'shellDiscount'             => 'required|numeric',
            'productList'               => 'required|array',
            'productList.*.productCode' => 'required|alpha_dash',
            'productList.*.amount'      => 'required|numeric',
            'qrCode'                    => 'required|alpha_dash',
        ],
        'query_fo'                       => [
            'order_id' => 'required|alpha_dash',
        ],
        'query_gs'                       => [
            'serial_no' => 'required|alpha_dash',
        ],
        'query_ygy'                      => [
            'orderSn' => "required|alpha_dash",
        ],
        'query_mb'                       => [
            'originalOrderNo' => "required_without:orderNo|alpha_dash",
            'orderNo'         => "required_without:originalOrderNo|alpha_dash",
        ],
        'query_ygyDemo'                  => [
            'orderSn' => "required|alpha_dash",
        ],
        'query_ky'                       => [
            'order_id' => "required|alpha_dash",
        ],
        'toBePaid'                       => [
            'card_no'    => 'required|alpha_num',
            'station_id' => 'required|alpha_dash',
            'oil_num'    => 'required|numeric',
            'price'      => 'required|numeric',
            'money'      => 'required|numeric',
            'oil_type'   => 'string',
            'oil_name'   => 'required',
            'oil_level'  => 'string',
            'order_id'   => 'required|alpha_dash',
        ],
        'toBePaid_ds'                    => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_type'       => 'string',
            'oil_name'       => 'required',
            'oil_level'      => 'string',
            'order_id'       => 'required|alpha_dash',
            'driver_phone'   => [
                'regex:/^([\d]+|[\d]{3}[\d*]{4}[\d]{4})$/'
            ],
            'deduction_mode' => 'required|in:1,2',
        ],
        'toBePaid_sffy'                  => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_code'       => 'required',
            'order_id'       => 'required|alpha_dash',
            'driver_phone'   => 'numeric',
            'deduction_mode' => 'required|in:1,2',
        ],
        'toBePaid_rq'                    => [
            'station_id'            => 'required|alpha_dash',
            'oil_num'               => 'required|numeric',
            'price'                 => 'required|numeric',
            'money'                 => 'required|numeric',
            'oil_type'              => 'string',
            'oil_name'              => 'required',
            'oil_level'             => 'string',
            'order_id'              => 'required|alpha_dash',
            'driver_phone'          => 'numeric',
            'deduction_mode'        => 'required|in:1,2',
            'legalList'             => 'required|array',
            'legalList.*.legalCode' => 'required',
            'legalList.*.money'     => 'required',
        ],
        'toBePaid_jd'                    => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_name'       => 'required',
            'order_id'       => 'required|alpha_dash',
            'refueling_time' => 'required|date_format:"Y-m-d H:i:s"',
        ],
        'toBePaid_htx'                   => [
            'station_id'           => 'required|alpha_dash',
            'oil_num'              => 'required|numeric',
            'price'                => 'required|numeric',
            'money'                => 'required|numeric',
            'oil_name'             => 'required',
            'order_id'             => 'required|alpha_dash',
            'refueling_time'       => 'required|date_format:"Y-m-d H:i:s"',
            'company_subject_name' => 'required',
            'driver_phone'         => 'required',
        ],
        'toBePaid_kl'                    => [
            'money'    => 'required|numeric',
            'order_id' => 'required|alpha_dash',
            'token'    => 'required',
        ],
        'toBePaid_yk'                    => [
            'money'      => 'required|numeric',
            'order_id'   => 'required|alpha_dash',
            'order_sign' => 'required_if:orderType,2',
            'token'      => 'required',
            'order_type' => 'required|in:1,2',
        ],
        'toBePaid_zy'                    => [
            'stationId' => 'required|numeric',
            'oilNum'    => 'required|numeric',
            'price'     => 'required|numeric',
            'money'     => 'required|numeric',
            'oilName'   => 'required',
            'oilLevel'  => 'string',
            'oilType'   => 'string',
            'orderId'   => 'required|alpha_dash',
            'cardNo'    => 'required|numeric',
        ],
        'toBePaid_wjy'                   => [
            'oilsStationId'     => 'required|numeric',
            'count'             => 'required|numeric',
            'price'             => 'required|numeric',
            'totalPrice'        => 'required|numeric',
            'foreign_unique_id' => 'required',
            'orderSn'           => 'required|alpha_dash',
            'fuelNo'            => 'required|alpha_dash',
        ],
        'toBePaid_ygy'                   => [
            'stationId'     => 'required|alpha_dash',
            'orderSn'       => 'required|alpha_dash',
            'count'         => 'required|numeric',
            'discountPrice' => 'required|numeric',
            'price'         => 'required|numeric',
            'totalPrice'    => 'required|numeric',
            'amountPrice'   => 'required|numeric',
            'fuelNo'        => 'required',
            'driverPhone'   => 'numeric',
            'gunNumber'     => 'numeric',
            'companyId'     => 'required|alpha_num',
            'deductionMode' => 'required|in:1,2',
        ],
        'toBePaid_ygyDemo'               => [
            'stationId'     => 'required|alpha_dash',
            'orderSn'       => 'required|alpha_dash',
            'count'         => 'required|numeric',
            'discountPrice' => 'required|numeric',
            'price'         => 'required|numeric',
            'totalPrice'    => 'required|numeric',
            'amountPrice'   => 'required|numeric',
            'fuelNo'        => 'required',
            'driverPhone'   => 'numeric',
            'gunNumber'     => 'numeric',
            'companyId'     => 'required|alpha_num',
            'deductionMode' => 'required|in:1,2',
        ],
        'toBePaid_desp|2H8BBD'           => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_name'       => 'required',
            'order_id'       => 'required|alpha_dash',
            'driver_phone'   => 'required|numeric',
            'deduction_mode' => 'required|in:1,2',
        ],
        'toBePaid_desp|2A6LA9'           => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_name'       => 'required',
            'order_id'       => 'required|alpha_dash',
            'driver_phone'   => 'required|numeric',
            'deduction_mode' => 'required|in:1,2',
        ],
        'toBePaid_fy'                    => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_type'       => 'string',
            'oil_name'       => 'required',
            'oil_level'      => 'string',
            'order_id'       => 'required|alpha_dash',
            'driver_code'    => 'required',
            'deduction_mode' => 'required|in:1,2',
        ],
        'toBePaid_gbdw'                  => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_type_id'    => 'required',
            'order_id'       => 'required|alpha_dash',
            'deduction_mode' => 'required|in:1,2',
            'driver_phone'   => 'required|numeric',
        ],
        'toBePaid_zj'                    => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_type'       => 'string',
            'oil_name'       => 'required',
            'oil_level'      => 'string',
            'order_id'       => 'required|alpha_dash',
            'driver_phone'   => 'numeric',
            'deduction_mode' => 'required|in:1,2',
            'driver_id'      => 'required',
        ],
        'toBePaid_hr'                    => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_type'       => 'string',
            'oil_name'       => 'required',
            'oil_level'      => 'string',
            'order_id'       => 'required|alpha_dash',
            'driver_phone'   => 'numeric',
            'deduction_mode' => 'required|in:1,2',
            'driver_id'      => 'required',
        ],
        'toBePaid_hll'                   => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_type'       => 'string',
            'oil_name'       => 'required',
            'oil_level'      => 'string',
            'order_id'       => 'required|alpha_dash',
            'deduction_mode' => 'required|in:1,2',
        ],
        'toBePaid_ad'                    => [
            'thirdStationNo'  => 'required|alpha_dash',
            'orderNo'         => 'required|alpha_dash',
            'goodsNumber'     => 'required|numeric',
            'settleAmount'    => 'required|numeric',
            'orderAmount'     => 'required|numeric',
            'orderUnitPrice'  => 'required|numeric',
            'settleUnitPrice' => 'required|numeric',
            'goodsNo'         => 'required',
            'driverPhone'     => 'numeric',
            'gunNumber'       => 'numeric',
        ],
        'toBePaid_zey'                   => [
            'station_id'        => 'required|alpha_dash',
            'refueling_subject' => 'required|alpha_num',
            'order_id'          => 'required|alpha_dash',
            'money'             => 'required|numeric',
            'oil_id'            => 'required|string',
            'deduction_mode'    => 'required|in:1,2',
            'gun_money'         => 'required|numeric',
            'oil_num'           => 'required_if:deduction_mode,2|numeric',
        ],
        'toBePaid_hjy'                   => [
            'station_id' => 'required|alpha_dash',
            'order_sn'   => 'required|alpha_dash',
            'oil_num'    => 'required|numeric',
            'pay_price'  => 'required|numeric',
            'price'      => 'required|numeric',
            'pay_money'  => 'required|numeric',
            'money'      => 'required|numeric',
            'goods_no'   => 'required',
            'extends'    => 'required',
        ],
        'reToBePaid'                     => [
            'card_no'        => 'required|numeric',
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_type'       => 'string',
            'oil_name'       => 'required',
            'oil_level'      => 'string',
            'order_id'       => 'required|alpha_dash',
            'refueling_time' => 'required|date_format:"Y-m-d H:i:s"',
        ],
        'reToBePaid_fo'                  => [
            'station_id'     => 'required|alpha_dash',
            'orgcode'        => 'alpha_num|required',
            'gun_id'         => 'alpha_dash',
            'oil_num'        => 'required|numeric',
            'oil_sale_price' => 'required|numeric',
            'oil_money'      => 'required|numeric',
            'oil_name'       => 'required',
            'oil_level'      => 'string',
            'oil_type'       => 'string',
            'oil_time'       => 'required|date_format:"Y-m-d H:i:s"',
            'driver_phone'   => 'required',
            'truck_no'       => 'required',
            'ticket_image'   => 'url',
            'truck_image'    => 'url',
            'other_image'    => 'url',
            'third_card_no'  => 'alpha_dash',
            'city_code'      => 'required|numeric',
            'province_code'  => 'required|numeric',
        ],
        'reToBePaid_jd'                  => [
            'station_id'     => 'required|alpha_dash',
            'oil_num'        => 'required|numeric',
            'price'          => 'required|numeric',
            'money'          => 'required|numeric',
            'oil_name'       => 'required',
            'order_id'       => 'required|alpha_dash',
            'refueling_time' => 'required|date_format:"Y-m-d H:i:s"',
        ],
        'reToBePaid_zy'                  => [
            'stationId' => 'required|numeric',
            'oilNum'    => 'required|numeric',
            'price'     => 'required|numeric',
            'money'     => 'required|numeric',
            'oilName'   => 'required',
            'oilLevel'  => 'string',
            'oilType'   => 'string',
            'orderId'   => 'required|alpha_dash',
            'cardNo'    => 'required|numeric',
            'oilTime'   => 'required|date_format:"Y-m-d H:i:s"',
        ],
        'cancel'                         => [
            'order_id' => 'required|alpha_dash',
        ],
        'cancel_zy'                      => [
            'orderId' => 'required|alpha_dash',
        ],
        'cancel_ad'                      => [
            'orderNo'         => 'required|alpha_dash',
            'supplierOrderNo' => 'required|alpha_dash',
        ],
        'getSecondaryPaymentCertificate' => [
            'order_id' => 'required',
        ],
        'queryFlow_ygy'                  => [
            'companyId' => 'required|alpha_num',
            'startTime' => 'required|date_format:"Y-m-d H:i:s"',
            'endTime'   => 'required|date_format:"Y-m-d H:i:s"',
        ],
        'queryFlow_ygyDemo'              => [
            'companyId' => 'required|alpha_num',
            'startTime' => 'required|date_format:"Y-m-d H:i:s"',
            'endTime'   => 'required|date_format:"Y-m-d H:i:s"',
        ],
        'refundApplication'              => [
            'order_id' => 'required|numeric',
        ],
        'refundApplication_mb'           => [
            'originalOrderNo' => 'required|alpha_num',
        ],
        'statusChange_sqZsh'             => [
            'uniqueStr'  => 'required|alpha_dash',
            'orderState' => 'required|in:1,2',
        ],
        'statusChange_xmsk'              => [
            'orderNo'    => 'required|alpha_dash',
            'orderState' => 'required|in:1,2,3,4,5,6,7,8',
        ],
        'refundCallback_ygy'             => [
            'foreignUniqueId' => 'required|alpha_num',
            'approvalResult'  => 'required|in:1,2',
        ],
        'refundCustomerForOrderCenter'   => [
            'org_code' => 'alpha_num|required',
            'order_id' => 'alpha_dash|required',
            'reason'   => 'required',
        ],
        'split_my'                       => [
            'order_id'                 => 'required|alpha_num',
            'customer_order_id'        => 'required|alpha_dash',
            'details'                  => 'required|array',
            'details.*.pay_company_id' => 'required|alpha_num',
            'details.*.sub_gun_money'  => 'required|numeric',
            'details.*.sub_money'      => 'required|numeric',
        ],
    ];

    public function __construct()
    {
        parent::__construct();
        $this->loadValidateCallback();
        $this->loadInputNoData();
    }

    public function loadValidateCallback()
    {
        $this->validateCallback['yc'] = function ($responseCode) {
            response()->json([
                'respCode' => '40999',
                'respMsg'  => config("error.$responseCode"),
                'status'   => 2,
            ])->send();
            exit();
        };
        $this->validateCallback['sc'] = function ($responseCode) {
            ResponseLog::handle([
                'data' => $this->requestData,
                'msg'  => config("error.$responseCode"),
                'code' => $responseCode,
            ]);
            response()->json([
                'respCode' => '40000',
                'respMsg'  => "消息格式不正确",
                'status'   => 2,
            ])->send();
            exit();
        };
        $this->validateCallback['gs'] = function ($responseCode) {
            response()->json([
                'errorcode' => $responseCode,
                'errormsg'  => config("error.$responseCode"),
                'success'   => false,
            ])->send();
            exit();
        };
        $this->validateCallback['wjy'] = function ($responseCode) {
            ResponseLog::handle([
                'data' => $this->requestData,
                'msg'  => config("error.$responseCode"),
                'code' => $responseCode,
            ]);
            response()->json([
                'code'    => $responseCode,
                'message' => config("error.$responseCode"),
                'result'  => null,
            ])->send();
            exit();
        };
        $this->validateCallback['sffy'] = function ($responseCode) {
            ResponseLog::handle([
                'data' => $this->requestData,
                'msg'  => config("error.$responseCode"),
                'code' => $responseCode,
            ]);
            response()->json([
                'code' => $responseCode,
                'msg'  => config("error.$responseCode"),
                'data' => null,
            ])->send();
            exit();
        };
        $this->validateCallback['ad'] = function ($responseCode) {
            responseFormatForAd($responseCode, null, true);
        };
        $this->validateCallback['xmsk'] = function ($responseCode) {
            responseFormatForXmsk($responseCode, null, true);
        };
    }

    public function loadInputNoData()
    {
        $this->inputNoData = [
            'yc'      => true,
            'sc'      => true,
            'wjy'     => true,
            'ygy'     => true,
            'ygyDemo' => true,
        ];
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-23 19:14
     */
    public function toBePaid(Request $request): JsonResponse
    {
        $authData = $request->input("auth_data");
        // 如果请求下单接口的用户身份为6(下游客户)，那么走下游客户通用验证处理
        if ($authData['role'] == 6) {
            $authData['real_name_abbreviation'] = $authData['name_abbreviation'];
            if (!isset($this->customRules[ACTION_NAME . '_' . $authData['name_abbreviation']])) {
                $authData['name_abbreviation'] = 'ds';
            }
            $request->merge([
                'auth_data' => $authData,
            ]);
        }
        $this->validateRequestParam($request);
        return (new ToBePaidLogic($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-07-23 19:14
     */
    public function reToBePaid(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return (new ReToBePaidLogic($this->requestData))->handle();
    }

    /**
     * 查询订单信息
     * @param Request $request
     * @return JsonResponse|Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019/12/20 5:22 下午
     */
    public function query(Request $request)
    {
        $this->validateRequestParam($request);

        return (new QueryLogic($this->requestData))->handle();
    }

    /**
     * 撤销待付款订单
     * @param Request $request
     * @return Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2019-08-07 16:25
     */
    public function cancel(Request $request): Response
    {
        $this->validateRequestParam($request);
        if (!isset($this->requestData['order_id']) and isset($this->requestData['orderId'])) {
            $this->requestData['order_id'] = $this->requestData['orderId'];
        }

        return (new CancelLogic($this->requestData))->handle();
    }

    /**
     * @throws Throwable
     */
    public function getSecondaryPaymentCertificate(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        $response = (new GetSecondaryPaymentQrCodeLogic($this->requestData))->handle();
        $responseData = $response->getData(true);
        if ($responseData['code'] != 0) {
            return $response;
        }
        return responseFormat(0, [
            'certificate_type'     => $responseData['data']['certificate_type'],
            'certificate_resource' => $responseData['data']['certificate_resource'],
            'expiration'           => $responseData['data']['expiration'],
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2022/2/19 6:11 PM
     */
    public function queryFlow(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return (new GetFlowLogic($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     * <AUTHOR> <<EMAIL>>
     * @since 2022/2/19 6:11 PM
     */
    public function refundApplication(Request $request): Response
    {
        $this->validateRequestParam($request);
        return (new RefundApplicationMainLogic($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function statusChange(Request $request): JsonResponse
    {
        $this->validateRequestParam($request);
        return (new StatusChangeLogic($this->requestData))->handle();
    }

    /**
     * @param Request $request
     * @return Response
     * @throws Exception
     */
    public function refundCallback(Request $request): Response
    {
        $this->validateRequestParam($request);
        return (new RefundCallbackMainLogic($this->requestData))->handle();
    }

    /**
     * @throws Throwable
     */
    public function refundCustomerForOrderCenter(Request $request): Response
    {
        $this->validateRequestParam($request);
        $this->requestData['skip_init'] = true;
        return (new RefundLogic($this->requestData))->refundCustomerForOrderCenter();
    }

    /**
     * @throws Throwable
     */
    public function split(Request $request): Response
    {
        $this->validateRequestParam($request);
        return (new SplitLogic($this->requestData))->handle();
    }
}
