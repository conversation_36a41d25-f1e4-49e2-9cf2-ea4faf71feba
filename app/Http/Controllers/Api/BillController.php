<?php

namespace App\Http\Controllers\Api;

use App\Models\Logic\Bill\Query\Main as QueryMainLogic;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class BillController extends BasicController
{
    public $customMessages = [
        'query'    => [
            'create_start_time.required_without_all' => 4120143,
            'create_start_time.date_format'          => 4120144,
            'create_end_time.required_without_all'   => 4120145,
            'create_end_time.date_format'            => 4120146,
            'trade_start_time.required_without_all'  => 4120143,
            'trade_start_time.date_format'           => 4120144,
            'trade_end_time.required_without_all'    => 4120145,
            'trade_end_time.date_format'             => 4120146,
        ],
        'query_ad' => [
            'billDate.required'    => 4120582,
            'billDate.date_format' => 4120583,
        ],
    ];

    public $customRules = [
        'query'    => [
            'create_start_time' => 'required_without_all:trade_start_time,trade_end_time|date_format:"Y-m-d H:i:s"',
            'create_end_time'   => 'required_without_all:trade_start_time,trade_end_time|date_format:"Y-m-d H:i:s"',
            'trade_start_time'  => 'required_without_all:create_start_time,create_end_time|date_format:"Y-m-d H:i:s"',
            'trade_end_time'    => 'required_without_all:create_start_time,create_end_time|date_format:"Y-m-d H:i:s"',
        ],
        'query_ad' => [
            'billDate' => 'required|date_format:"Y-m-d"',
        ],
    ];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param Request $request
     * @return Response
     * @throws Throwable
     * ---------------------------------------------------
     * <AUTHOR> <<EMAIL>>
     * @since 2020/6/5 4:20 下午
     */
    public function query(Request $request): Response
    {
        $this->validateRequestParam($request);
        $this->requestData['cardNo'] = $this->authData['card_no'];
        $this->requestData['orgCode'] = $this->authData['role_code'];
        $this->requestData['auth_data'] = $this->authData;
        return (new QueryMainLogic($this->requestData))->handle();
    }
}
