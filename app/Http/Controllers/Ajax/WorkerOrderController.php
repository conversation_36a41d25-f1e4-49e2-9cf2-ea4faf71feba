<?php

namespace App\Http\Controllers\Ajax;

use App\Http\Controllers\Controller;
use App\Http\Defines\StationPriceDefine;
use App\Library\Request;
use App\Services\WorkOrder\WorkOrderService;

class WorkerOrderController extends Controller {

    /**
     * 工单审核通过
     * @Notes:
     * @Interface priceApprovedPass
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     * @throws \Illuminate\Validation\ValidationException
     * @author: yuanzhi
     * @Time: 2020/11/11   9:48 上午
     */
    public function pass()
    {
        $rule = [
            'id' => 'required|integer|min:1',
            'marker' => 'string|max:500',
        ];
        $params = Request::trimNull(array_keys($rule));
        $this->validator($params, $rule);
        $result = WorkOrderService::pass($params);

        if($result)
            return $this->success(['id' => $params['id']], false, '审核通过');
        else
            return $this->fail(5043, '审核失败，请重试');


    }

    /**
     * @Notes:改价工单拒绝
     * @Interface refuse
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     * @throws \Illuminate\Validation\ValidationException
     * @throws \Throwable
     * @author: yuanzhi
     * @Time: 2020/11/11   5:10 下午
     */
    public function refuse()
    {
        $rule = [
            'id' => 'required|integer|min:1',
            'marker' => 'string|max:500',
        ];
        $params = Request::trimNull(array_keys($rule));
        $this->validator($params, $rule);
        $result = WorkOrderService::refuse($params);
        if($result)
            return $this->success(['id' => $params['id']], false, '成功驳回');
        else
            return $this->fail('5041', '操作失败，请重试或联系技术人员');
    }

    /**
     * 创建变价申请单
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     */
    public function createChangePriceApprove()
    {
        $rule = [
            'station_id' => 'required|alpha_num',
            'supplier_price_id' => 'required|alpha_num',
            'ineffective_mac_price' => 'required|money|range_price',
            'ineffective_supplier_price_starttime' => 'required|date_format:Y-m-d H:i:s',
            'force' => 'nullable|in:0,1'
        ];
        $message = [
            'ineffective_mac_price.gte' => '油机价不得低于'.StationPriceDefine::MIN_PRICE.'元',
            'ineffective_mac_price.lte' => '油机价不得高于'.StationPriceDefine::MAX_PRICE.'元',
        ];
        $params = Request::only(array_keys($rule));
        try {
            $this->validator($params, $rule, $message);
            if (!in_array($params['station_id'], Request::get('doper_station_ids'))) {
                throw new \Exception('无权限编辑该站点的油机价');
            }
            $result = WorkOrderService::createChangePriceApprove($params);
            return $this->success($result, true, config('errormsg.MSG_OK_ZH'));
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();
            if ($code == '31054031201') {
                $msg = json_decode($e->getMessage(), true);
                return $this->fail($code, $msg['msg'], $msg);
            }
            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * 手机管站获取审核列表
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     */
    public function getMtPriceApprovePaginate()
    {
        $rule = [
            'station_id' => 'nullable|alpha_num',
            'province_code' => 'nullable|numeric',
            'city_code' => 'nullable|numeric',
            'status' => 'nullable|in:0,1,2,3',
            'ge_create_time' => 'nullable|date_format:Y-m-d',
            'le_start_time' => 'nullable|date_format:Y-m-d',
            'page' => 'nullable|integer',
            'limit' => 'nullable|integer',

        ];
        $params = Request::only(array_keys($rule));
        try {
            $params['source'] = Request::get('source');
            $result = WorkOrderService::getPriceApprovePaginate($params);
            return $this->success($result);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }

    /**
     * PDA-H5获取审核列表
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     */
    public function getPdaPriceApprovePaginate()
    {
        $rule = [
            'station_id' => 'nullable|alpha_num',
            'ge_create_time' => 'nullable|date_format:Y-m-d',
            'le_start_time' => 'nullable|date_format:Y-m-d',
            'page' => 'nullable|integer',
            'limit' => 'nullable|integer',

        ];
        $params = Request::only(array_keys($rule));
        try {
            if (!empty($params['station_id']) && !in_array($params['station_id'], Request::get('doper_station_ids'))) {
                throw new \Exception('无权限编查看站点的油机价审核列表');
            }
            $params['station_id'] = empty($params['station_id']) ? Request::get('doper_station_ids') : $params['station_id'];
            $params['source'] = Request::get('source');
            $result = WorkOrderService::getPriceApprovePaginate($params);
            return $this->success($result);
        } catch (\Exception $e) {
            $code = empty($e->getCode()) ? 403 : $e->getCode();

            return $this->fail($code, $e->getMessage());
        }
    }
}
