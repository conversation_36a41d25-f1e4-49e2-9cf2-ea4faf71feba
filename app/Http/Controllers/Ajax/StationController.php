<?php

namespace App\Http\Controllers\Ajax;

use App\Events\SomeStationEvent;
use App\Events\StationEvent;
use App\Exceptions\ParamInvalidException;
use App\Exceptions\ReponseException;
use App\Exports\StationExport;
use App\Library\Alarm\FeiShu;
use App\Library\Helper\Common;
use App\Models\Gas\GasBatchAddDeleteStationOilTask;
use App\Models\Gas\PriceSaleExtModel;
use App\Models\Gas\StationModel;
use App\Repositories\GasUser\GasUserRepository;
use App\Repositories\Price\PriceRepository;
use App\Repositories\Station\StationRepository;
use App\Services\Admin\UsersService;
use App\Services\Coupon\CouponTypeService;
use App\Services\Goods\OilService;
use App\Services\Import\StationBatchUploadService;
use App\Services\Station\StationService;
use App\Http\Controllers\Controller;
use App\Library\Request;
use App\Services\Price\PriceService;
use App\Servitization\Foss;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use RuntimeException;
use App\Models\Gas\SupplierModel;
use stdClass;
use Throwable;

class StationController extends Controller
{
    protected $couponTypeService;

    public function __construct(CouponTypeService $couponTypeService)
    {
        $this->couponTypeService = $couponTypeService;
    }

    /**
     * 站点编辑-后台
     * @return JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     * @throws \App\Exceptions\ReponseException
     * @throws ValidationException
     */
    public function stationUpOrCreate()
    {
        $rule = [
            'app_key'                      => 'nullable|alpha_dash', //用户
            'id'                           => 'present|alpha_num', //站点ID
            'station_name'                 => 'required|string|no_html_trim|max:100', //站点名称
            'remark_name'                  => 'present|string|no_html_trim|max:100', //显示名称
            'pcode'                        => 'required|alpha_num|not_in:20003JCP', //站点运营商
            'station_type'                 => 'required|numeric|between:1,5', //站点类型
            'is_stop'                      => 'required|in:1,0', //是否停用
            'station_brand'                => 'required|numeric|between:1,28', //站点品牌
            'business_hours'               => 'present|string|no_html_trim|max:50', //营业时间
            'trade_type'                   => 'required|in:1,3,4,5', //收款方式
            'print_key'                    => 'present|required_if:trade_type,4|string', //云打印key
            'print_sn'                     => 'present|required_if:trade_type,4|string', //云打印sn
            'allow_switch_unit'            => 'required|in:0,1', //是否允许加油员切换加注模式 1是 0否
            'station_oil_unit'             => 'in:1,2', //加注模式 1按金额 2按升
            'is_test'                      => 'required|in:1,0', //是否测试站
            'app_station_id'               => 'present|string|max:50', //三方站点ID
            'is_highway'                   => 'required|in:1,0', //是否高速站
            'provice_code'                 => 'required|numeric', //所属省
            'city_code'                    => 'required|numeric', // 所属市
            'lngAndLat'                    => 'required|lngAndLat', //经纬度
            'lng'                          => 'nullable|lng', //经度（保留）
            'lat'                          => 'nullable|lat', //维度（保留）
            'address'                      => 'required|string|no_html_trim|max:200', //地址
            'rebate_grade'                 => 'required|alpha|in:A,B,C,D,E', //返利等级
            'contact'                      => 'required|string|no_html_trim|max:20', //联系人
            'contact_phone'                => 'required|string|no_html_trim|max:50', //联系人电话
            'station_tag'                  => 'present|array|min:1|max:100', //站点标签
            'station_tag.*'                => 'present|alpha_num|distinct', //站点标签
            'station_line'                 => 'present|array|distinct|min:1|max:100', //线路标签
            'station_line.*'               => 'present|alpha_num|distinct', //线路标签
            'station_area'                 => 'present|array|distinct|min:1|max:100', //区域标签
            'station_area.*'               => 'present|alpha_num|distinct', //区域标签
            'station_xc'                   => 'present|min:1|max:100', //宣传标签
            'station_xc.*'                 => 'present|alpha_num|distinct', //宣传标签
            'intro_img'                    => 'present|string|no_html_trim|max:255', //站点缩略图
            'detail_img'                   => 'present|string|no_html_trim|max:255', //站点详情图
            'channel_id'                   => 'integer', //供应商ID
            'service_area_id'              => 'integer', //服务区ID
            'price_list'                   => 'required|array|min:1|max:100',
            'price_list.*.mac_price'       => 'required|money|lte_price', //油机价
            'price_list.*.supplier_price'  => 'required|money|lte_price', //价格 小数点后两位
            'price_list.*.platform_price'  => 'required|money|lte_price',
            'price_list.*.price_source'    => 'required|in:4,6',
            'price_list.*.float_price'     => 'present|required_if:price_list.*.price_source,6|money',
            'price_list.*.oil_type'        => 'present|alpha_num', // 油品类型需要按现有逻辑校验
            'price_list.*.oil_name'        => 'required|alpha_num', //库存存储时验证
            'price_list.*.oil_level'       => 'present|alpha_num',
            'price_list.*.rule_type'       => 'required_if:price_list.*.price_source,6|in:4,6,61,62',
            'price_list.*.gun'             => 'required|array|min:1|max:100',
            'price_list.*.gun.*.gun_id'    => 'nullable|string',
            'price_list.*.gun.*.gun_name'  => 'required|string|no_html_trim',
            'price_list.*.gun.*.tank_id'   => 'nullable|string',
            'price_list.*.gun.*.tank_name' => 'nullable|string',//油罐表ID 商品罐号
            'network_docker_id'            => 'integer',//网络负责人
            'master_card_no'               => 'nullable',
            'supplementary_card_no'        => 'nullable',
            'card_user_id'                 => 'nullable',
            'card_assign_method'           => 'required|in:1,2,3,4',
        ];
        $params = Request::only(array_keys($rule));

        $message = [
            'allow_switch_unit.required'      => '是否允许加油员修改加注模式必填:1允许 0不允许',
            'price_list.*.gun.max'            => '每个商品最多100把枪',
            'price_list.*.mac_price.lte'      => '价格存在异常,请仔细核对',
            'price_list.*.supplier_price.lte' => '价格存在异常,请仔细核对',
            'price_list.*.platform_price.lte' => '价格存在异常,请仔细核对',
            'channel_id.integer'              => '请选择油站供应商',
            'service_area_id.integer'         => '请选择服务区',
        ];

        //如果是自建站 必须有网络负责人
        if (empty($params['network_docker_id'])) {
            $supplierInfo = SupplierModel::where('scode', $params['pcode'])->first();
            if (isset($supplierInfo->is_self_station) && $supplierInfo->is_self_station == 1) {
                throw new ParamInvalidException("自建站点网络负责人信息必填");
            }
        }

        if (in_array($params['pcode'], config('pcode.NEED_MAINTAIN_ASSIGN_DATA'))) {
            $this->validator($params, [
                'master_card_no'        => [
                    'required',
                    'regex:/^[\d]{1,30}$/'
                ],
                'supplementary_card_no' => [
                    'required',
                    'regex:/^[\d]{1,30}$/'
                ],
                'card_user_id'          => [
                    'required',
                    'regex:/^[\d]{1,30}$/'
                ],
            ], [
                'master_card_no.required'        => '请输入主卡号',
                'supplementary_card_no.required' => '请输入副卡号',
                'card_user_id.required'          => '请输入卡用户ID',
                'master_card_no.regex'           => '主卡号应为1~30个数字',
                'supplementary_card_no.regex'    => '副卡号应为1~30个数字',
                'card_user_id.regex'             => '卡用户ID应为1~30个数字',
            ]);
        }

        $this->validator($params, $rule, $message);
        foreach ($params['price_list'] as $v) {
            //固定价格模式定价时,浮动规则类型为固定
            if ($v['price_source'] == PriceSaleExtModel::RULE_TYPE_FIXED) {
                if (!empty($v['rule_type']) and $v['rule_type'] != PriceSaleExtModel::RULE_TYPE_FIXED) {
                    throw new ParamInvalidException("浮动规则不正确");
                }
            }
            //固定价格模式定价时,浮动规则类型为按固定金额浮动、按百分比四舍五入浮动、按百分比进一法浮动
            if ($v['price_source'] == PriceSaleExtModel::RULE_TYPE_PRICE_FLOAT) {
                if (!in_array($v['rule_type'], [
                    PriceSaleExtModel::RULE_TYPE_PRICE_FLOAT,
                    PriceSaleExtModel::RULE_TYPE_PERCENT_ROUND_FLOAT,
                    PriceSaleExtModel::RULE_TYPE_PERCENT_CEIL_FLOAT
                ])) {
                    throw new ParamInvalidException("浮动规则不正确");
                }
                if (bccomp(
                        $v['platform_price'],
                        PriceRepository::calSalePriceByRuleType(
                            $v['supplier_price'],
                            $v['rule_type'],
                            $v['float_price']
                        ),
                        2
                    ) !== 0) {
                    throw new RuntimeException("系统异常,请稍后再试");
                }
            }
        }
        Common::log('info', '创建站参数信息', ['param' => $params]);
        $stationId = StationService::stationUpdateOrCreate($params);
        return $this->success(['station_id' => $stationId], true, config('errormsg.MSG_OK_ZH'));
    }

    /**
     * 站点详情-后台
     * @return JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     * @throws ValidationException
     */
    public function getStation(\Illuminate\Http\Request $request)
    {
        $rule = [
            'id' => 'required|alpha_num'
        ];
        $params = Request::only(array_keys($rule));
        $this->validator($params, $rule);
        $_sys_from = 1;
        if(stripos($request->path(),"order/") !==false ){
            $_sys_from = 2;
        }
        $station = StationService::getStationById($params,$_sys_from);
        return $this->success($station);
    }


    /**
     * Desc: 站点查询--专用站点设置
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 29/3/23 下午4:40
     * @return JsonResponse
     * @throws ParamInvalidException
     */
    public function getStationOrgList()
    {
        try{
            $rule = [
                'id' => 'required|alpha_num'
            ];
            /*$params = [
                'id' => '787ff0461ec311eda2bafa163eb21e65'
            ];*/
            $params = Request::only(array_keys($rule));
            $this->validator($params, $rule);
            $station = StationService::getStationOrgList($params);
            return $this->success($station);
        }catch (Exception $exception){
            dd($exception->getMessage().$exception->getLine().$exception->getFile());
        }

    }



    /**
     * Desc: 站点查询--专用站点设置
     * ---------------------------------------------
     * author  : sixueyu<<EMAIL>>
     * DateTime: 29/3/23 下午4:40
     * @return JsonResponse
     * @throws ParamInvalidException
     */
    public function setStationOrgList()
    {
        try{
            $params = Request::all();

            /*$params = [
                'id' => '787ff0461ec311eda2bafa163eb21e65',
                'orgcode' => '202FOP,20358A',
                'is_white_station' => '2'
            ];*/

            $rule = isset($params['is_white_station']) && $params['is_white_station'] == 2 ? [
                'id' => 'required|alpha_num',
                'orgcode' => 'required',
                'is_white_station' => 'required|in:1,2'
            ] : [
                'id' => 'required|alpha_num',
                'is_white_station' => 'required|in:1,2'
            ];
            $this->validator($params, $rule);
            $station = StationService::setStationOrgList($params);
            return $this->success($station);
        }catch (Exception $exception){
            return $this->fail(100,$exception->getMessage().$exception->getFile().$exception->getLine());
        }
    }

    /**
     * 站点列表-后台
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getStationPaginate()
    {
        $rule = [
            'pcode'              => 'nullable|alpha_dash',
            'id'                 => 'nullable',
            'station_id'         => 'nullable',
            'station_code'       => 'nullable',
            'provice_code'       => 'nullable|numeric',
            'city_code'          => 'nullable|numeric',
            'is_test'            => 'nullable|in:1,0',
            'is_highway'         => 'nullable|in:1,0',
            'station_brand'      => 'nullable|numeric',
            'rebate_grade'       => 'nullable|alpha|in:A,B,C,D,E,未知',
            'is_stop'            => 'nullable|in:1,0',
            'trade_type'         => 'nullable|in:1,3,4,5',
            'channel_id'         => 'nullable|integer',
            'network_docker_id'  => 'nullable|integer',
            'limit'              => 'nullable|numeric|between:1,100',
            'page'               => 'nullable|numeric',
            'card_classify'      => 'array',
            'card_classify.*'    => 'in:1,2,3',
            'is_white_station'   => 'in:1,2',
            'card_assign_method' => 'in:1,2,3,4',
            'station_oil_unit'   => 'in:1,2',
        ];
        try{
            $params = Request::trimNull(array_keys($rule));
            $params['card_classify'] = array_filter(explode(',', $params['card_classify'] ?? ''));
            $this->validator($params, $rule);
            $stationList = StationService::getStationPaginate($params);
        }catch (Exception $exception){
            dd($exception->getMessage().$exception->getLine().$exception->getFile());
        }

        return $this->success($stationList);
    }

    /**
     * 批量上线操作-后台
     * @return JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     * @throws \App\Exceptions\ReponseException
     * @throws ValidationException
     * @throws Throwable
     */
    public function batchShelves()
    {
        $rule = [
            'id_list'      => 'required|array|min:1|max:50',
            'id_list.*.id' => 'required|alpha_num',
            'reason'       => 'required|in:' . implode(
                    ',',
                    StationModel::$cardClassifyEditReasonList[StationModel::STATION_ONLINE]
                ),
            'remark'       => 'required_if:reason,其他',
            'pushMsgToFs'  => 'numeric|nullable',
        ];
        $params = Request::only(array_keys($rule));
        $this->validator($params, $rule, [
            'reason.required'     => '原因不允许为空',
            'reason.in'           => '原因不正确',
            'remark.required_if'  => '备注不允许为空',
            'pushMsgToFs.numeric' => '是否推送销售标识类型错误',
        ]);
        $data = StationService::batchShelves($params);
        $stationEvent['station_id'] = collect($data)->pluck('id')->toArray();
        event(new StationEvent($stationEvent));
        if ($params['pushMsgToFs'] ?? 0 == 1) {
            FeiShu::stationOnlineOrOfflineAndStatusChange(
                StationRepository::getStationAndSupplierInfoById(
                    collect($data)->pluck('id')->toArray(),
                    [
                        'isstop' => 0
                    ]
                )->toArray(),
                '站点上线通知',
                '人工-' . Request::get('user_name'),
                $params['reason'] ?? '',
                $params['remark'] ?? ''
            );
        }
        return $this->success($data, true, config('errormsg.MSG_OK_ZH'));
    }

    /**
     * 批量下架操作-后台
     * @return JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     * @throws \App\Exceptions\ReponseException
     * @throws ValidationException
     * @throws Throwable
     */
    public function batchOffShelves()
    {
        $rule = [
            'id_list'      => 'required|array|min:1|max:50',
            'id_list.*.id' => 'required|alpha_num',
            'reason'       => 'required|in:' . implode(
                    ',',
                    StationModel::$cardClassifyEditReasonList[StationModel::STATION_OFFLINE]
                ),
            'remark'       => 'required_if:reason,其他',
            'pushMsgToFs'  => 'numeric|nullable',
        ];
        $params = Request::only(array_keys($rule));
        $this->validator($params, $rule, [
            'reason.required'     => '原因不允许为空',
            'reason.in'           => '原因不正确',
            'remark.required_if'  => '备注不允许为空',
            'pushMsgToFs.numeric' => '是否推送销售标识类型错误',
        ]);
        $alarmStationInfo = StationRepository::getStationAndSupplierInfoById(
            $params['id_list'],
            [
                'isstop' => 0,
                'card_classify'  => 2,
            ]
        )->toArray();
        $data = StationService::batchOffShelves($params);
        $stationEvent['station_id'] = collect($data)->pluck('id')->toArray();
        event(new SomeStationEvent(array_merge($stationEvent, ['field' => 'card_classify', 'value' => 1])));
        if ($params['pushMsgToFs'] ?? 0 == 1) {
            FeiShu::stationOnlineOrOfflineAndStatusChange(
                $alarmStationInfo,
                '站点下线通知',
                '人工-' . Request::get('user_name'),
                $params['reason'] ?? '',
                $params['remark'] ?? ''
            );
        }
        return $this->success($data, true, config('errormsg.MSG_OK_ZH'));
    }

    /**
     * @throws ValidationException
     * @throws ReponseException
     * @throws ParamInvalidException
     */
    public function batchForeverOffline(): JsonResponse
    {
        $rule = [
            'id_list'      => 'required|array|min:1|max:50',
            'id_list.*.id' => 'required|alpha_num',
            'reason'       => 'required|in:' . implode(
                    ',',
                    StationModel::$cardClassifyEditReasonList[StationModel::STATION_FOREVER_OFFLINE]
                ),
            'remark'       => 'required_if:reason,其他',
        ];
        $params = Request::only(array_keys($rule));
        $this->validator($params, $rule, [
            'reason.required'    => '原因不允许为空',
            'reason.in'          => '原因不正确',
            'remark.required_if' => '备注不允许为空',
        ]);
        $data = StationService::batchForeverOffline($params);
        return $this->success($data, true, config('errormsg.MSG_OK_ZH'));
    }

    /**
     * @throws ValidationException
     * @throws ParamInvalidException
     */
    public function getStationStatusOperationReasonList(): JsonResponse
    {
        $rule = [
            'status' => 'required|in:' . implode(',', array_keys(StationModel::CARD_CLASSIFY_DESC)),
        ];
        $params = Request::only(array_keys($rule));
        $this->validator($params, $rule);
        return $this->success(
            collect(StationModel::$cardClassifyEditReasonList[$params['status']])->flip()->map(function ($v, $k) {
                return [
                    'key'   => $k,
                    'value' => $k,
                ];
            })->values(),
            true,
            config('errormsg.MSG_OK_ZH')
        );
    }

    /**
     * @throws ValidationException
     * @throws ParamInvalidException
     */
    public function getStationStatusOperationList(): JsonResponse
    {
        $rule = [
            'data_unique' => 'required|alpha_num',
            'page'       => 'nullable|integer',
            'limit'      => 'nullable|integer',
        ];
        $params = Request::only(array_keys($rule));
        $this->validator($params, $rule);
        $data = StationService::getStationStatusOperationList($params);
        return $this->success($data, true, config('errormsg.MSG_OK_ZH'));
    }

    /**
     * 批量变更交易类型-后台
     * @return JsonResponse
     * @throws \App\Exceptions\ParamInvalidException
     * @throws \App\Exceptions\ReponseException
     * @throws ValidationException
     */
    public function batchUpTradeType()
    {
        $rule = [
            'pcode_list'         => 'required|array|min:1|max:50',
            'pcode_list.*.pcode' => 'required|alpha_num',
            'trade_type'         => 'required|in:1,3,5'
        ];
        $params = Request::only(array_keys($rule));
        $this->validator($params, $rule);
        $data = StationService::batchUpTradeType($params);
        $stationEvent['station_id'] = array_unique(collect($data)->pluck('id')->toArray());
        event(new SomeStationEvent(array_merge($stationEvent, ['field' => 'card_classify', 'value' => 1])));
        event(
            new StationEvent(
                array_merge($stationEvent, [
                    'push'       => 'station',
                    'start_time' => date("Y-m-d H:i:s", strtotime("+10 minute")),
                ])
            )
        );
        return $this->success($data, true, config('errormsg.MSG_OK_ZH'));
    }

    /**
     * @Notes:加油员读取站价列表
     * @Interface getStationPricePaginate
     * @return JsonResponse
     * @throws ParamInvalidException
     * @throws ValidationException
     * @author: yuanzhi
     * @Time: 2020/11/13   11:02 上午
     */
    public function getStationPricePaginate()
    {
        $rule = [
            'limit' => 'nullable|numeric|between:1,50',
            'page'  => 'nullable|numeric',
        ];
        $params = Request::trimNull(array_keys($rule));
        $this->validator($params, $rule);
        $params['station_id'] = Request::get('doper_station_ids');
        $stationList = PriceService::getStationSupplierPricePaginate($params);
        return $this->success($stationList);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR> <<EMAIL>>
     * @since 2021/4/25 5:03 下午
     */
    public function getStationSpecialPriceForOrg(): JsonResponse
    {
        $rule = [
            'station_id'    => 'required|alpha_num',
            'org_tree_code' => 'alpha_num',
        ];
        $params = Request::trimNull(array_keys($rule));
        $this->validator($params, $rule);
        $responseData = PriceService::getStationSpecialPriceForOrg($params);
        return $this->success($responseData);
    }


    public function getUsersPaginate()
    {
        $rule = [
            'like_username'  => 'nullable|string',
            'like_nick_name' => 'nullable|string',
            'status'         => 'nullable|in:1,0',
            'limit'          => 'nullable|numeric|between:1,100',
            'page'           => 'nullable|numeric'
        ];
        $params = Request::trimNull(array_keys($rule));
        $this->validator($params, $rule);
        $params['login'] = "GMS";
        $data = UsersService::getUsersPaginateNoPermission($params);
        return $this->success($data);
    }

    /**
     * @return JsonResponse
     * <AUTHOR> <<EMAIL>>
     * @since 2022/3/16 11:10 AM
     */
    public function getStationLimitConfiguration(): JsonResponse
    {
        return $this->success(
            StationService::getStationLimitConfiguration(Request::all()),
            true,
            '',
            JSON_FORCE_OBJECT
        );
    }

    /**
     * @return JsonResponse
     * <AUTHOR> <<EMAIL>>
     * @since 2022/3/24 11:50 AM
     */
    public function addOrEditStationLimitConfiguration(): JsonResponse
    {
        $params = Request::all();
        $params['updater'] = $params['user_name'];
        return $this->success(StationService::addOrEditStationLimitConfiguration($params));
    }


    public function getUseCoupon(): JsonResponse
    {
        $rule = [
            'supplier_code' => 'required|alpha_num',
            'station_id'    => 'required|alpha_num',
            'oil_name'      => 'alpha_num',
        ];
        $params = Request::trimNull(array_keys($rule));
        $this->validator($params, $rule, [
            'supplier_code.required'  => '运营商代码不能为空',
            'supplier_code.alpha_num' => '运营商代码不正确',
            'station_id.required'     => '站点ID不能为空',
            'station_id.alpha_num'    => '站点ID不正确',
            'oil_name.alpha_num'      => '油品名称编码不正确',
        ]);
        return $this->success(
            $this->couponTypeService->getUseCoupon(
                $params['supplier_code'] ?? '',
                $params['station_id'] ?? '',
                $params['oil_name'] ?? ''
            )
        );
    }

    /**
     * @return JsonResponse
     * @throws ValidationException|Exception
     */
    public function getStationLimitResult(): JsonResponse
    {
        $rule = [
            'org_code'     => 'alpha_num',
            'station_id'   => 'alpha_num',
            'station_code' => 'alpha_num',
            'page'         => 'integer',
            'limit'        => 'integer',
            'station_name' => '',
        ];
        $params = Request::trimNull(array_keys($rule));
        $this->validator($params, $rule, [
            'org_code.alpha_num'     => '机构代码不正确',
            'station_id.alpha_num'   => '站点ID不正确',
            'station_code.alpha_num' => '站点代码不正确',
            'page.integer'           => '页码不正确',
            'limit.integer'          => '每页显示数不正确',
        ]);
        foreach ($params as $k => $v) {
            if (empty($v)) {
                unset($k);
            }
        }
        return $this->success(
            StationService::getStationLimitResult($params),
            true
        );
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getBatchAddOrDeleteStationOilList(): JsonResponse
    {
        $rules = [
            'page'      => 'integer',
            'limit'     => 'integer',
            'oil_name'  => 'alpha_num',
            'oil_type'  => 'alpha_num',
            'oil_level' => 'alpha_num',
            'operate'   => 'in:' . implode(
                    ',',
                    [
                        GasBatchAddDeleteStationOilTask::ADD,
                        GasBatchAddDeleteStationOilTask::DELETE
                    ]
                ),
            'status'    => 'in:' . implode(',', array_keys(GasBatchAddDeleteStationOilTask::STATUS_DESC)),
            'reason'    => 'in:' . implode(
                    ',',
                    collect(GasBatchAddDeleteStationOilTask::REASON_LIST)->flatten()->toArray()
                ),
        ];
        $params = Request::trimNull(array_keys($rules));;
        $this->validator($params, $rules, [
            'page.integer'        => '页码不正确',
            'limit.integer'       => '每页显示数不正确',
            'oil_name.alpha_num'  => '油品类型不正确',
            'oil_type.alpha_num'  => '油品标号不正确',
            'oil_level.alpha_num' => '油品级别不正确',
            'operate.in'          => '操作类型不正确',
            'status.in'           => '执行状态不正确',
            'reason.in'           => '操作原因不正确',
        ]);
        $result = OilService::getBatchAddOrDeleteList($params);
        if (!isset($result) or $result['code'] != 0) {
            return $this->fail($result['code'], $result['msg'], []);
        }
        return $this->success($result['data'], true);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getBatchAddOrDeleteStationOilDetail(): JsonResponse
    {
        $rules = [
            'task_id' => 'required|numeric',
            'page'    => 'integer',
            'limit'   => 'integer',
        ];
        $params = Request::trimNull(array_keys($rules));
        $this->validator($params, $rules, [
            'task_id.required' => '唯一编号不允许为空',
            'task_id.numeric'  => '唯一编号不正确',
            'page.integer'     => '页码不正确',
            'limit.integer'    => '每页显示数不正确',
        ]);
        $result = OilService::getBatchAddOrDeleteDetail($params);
        if (!isset($result) or $result['code'] != 0) {
            return $this->fail($result['code'], $result['msg'], []);
        }
        return $this->success($result['data'], true);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     */
    public function batchAddOrDeleteStationOil(): JsonResponse
    {
        $params = Request::all();
        $params['station_codes'] = explode("\n", $params['station_codes'] ?? '');
        $params['station_codes'] = array_map(function ($v) {
            return trim($v);
        }, $params['station_codes']);
        $params['station_codes'] = array_filter($params['station_codes']);
        $this->validator($params, [
            'operate'         => 'required|in:' . implode(
                    ',',
                    array_keys(GasBatchAddDeleteStationOilTask::OPERATE_DESC)
                ),
            'oil_name'        => 'required|alpha_num',
            'oil_type'        => 'nullable|alpha_num',
            'oil_level'       => 'nullable|alpha_num',
            'station_codes'   => 'required|array',
            'station_codes.*' => 'alpha_num',
            'reason'          => 'in:' . implode(
                    ',',
                    GasBatchAddDeleteStationOilTask::REASON_LIST[$params['operate'] ?? -1] ?? []
                ),
            'mac_price'       => 'required_if:operate,' . GasBatchAddDeleteStationOilTask::ADD . '|nullable|numeric|between:2,25',
            'remark'          => '',
        ], [
            'operate.required'          => '操作类型不允许为空',
            'operate.in'                => '操作类型不正确',
            'oil_name.required'         => '油品类型不允许为空',
            'oil_name.alpha_num'        => '油品类型不正确',
            'oil_type.alpha_num'        => '油品标号不正确',
            'oil_level.alpha_num'       => '油品级别不正确',
            'station_codes.required'    => '站点编码不允许为空',
            'station_codes.array'       => '站点编码格式不正确',
            'station_codes.*.alpha_num' => '站点编码不正确',
            'reason.in'                 => '操作原因不正确',
            'mac_price.required_if'     => '油机价不允许为空',
            'mac_price.numeric'         => '油机价不正确',
            'mac_price.between'         => '油机价未在指定范围内(2~25)',
        ]);
        $params['oil_type'] = $params['oil_type'] ?? '';
        $params['oil_level'] = $params['oil_level'] ?? '';
        $result = OilService::batchAddOrDelete($params);
        if (!isset($result) or $result['code'] != 0) {
            return $this->fail($result['code'], $result['msg'], new stdClass());
        }
        return $this->success($result['data'], true);
    }

    /**
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getBatchAddOrDeleteStationOilReasonList(): JsonResponse
    {
        $rules = [
            'operate' => 'required|in:' . implode(
                    ',',
                    array_keys(GasBatchAddDeleteStationOilTask::OPERATE_DESC)
                ),
        ];
        $params = Request::trimNull(array_keys($rules));
        $this->validator($params, $rules, [
            'operate.required' => '操作类型不允许为空',
            'operate.in'       => '操作类型不正确',
        ]);
        return $this->success(
            array_values(
                collect(GasBatchAddDeleteStationOilTask::REASON_LIST[$params['operate']])->map(function ($item) {
                    return [
                        'key'   => $item,
                        'value' => $item
                    ];
                })->toArray()
            ),
            true
        );
    }

    public function getBatchAddOrDeleteStationOilStatusList(): JsonResponse
    {
        return $this->success(
            array_values(
                collect(GasBatchAddDeleteStationOilTask::STATUS_DESC)->map(function ($item, $key) {
                    return [
                        'key'   => $item,
                        'value' => $key
                    ];
                })->toArray()
            ),
            true
        );
    }

    public function getBatchAddOrDeleteStationOilOperateList(): JsonResponse
    {
        return $this->success(
            array_values(
                collect(GasBatchAddDeleteStationOilTask::OPERATE_DESC)->map(function ($item, $key) {
                    return [
                        'key'   => $item,
                        'value' => $key
                    ];
                })->toArray()
            ),
            true
        );
    }

    /**
     *
     */
    public function queryGaodeId()
    {
        $params = Request::all();
        $idList = $params['id_list'];

        if(empty($idList)) {
            throw new RuntimeException('站点不能为空');
        }

        if(is_array($idList) && count($idList)>50) {
            throw new RuntimeException('请选择50条以内的站点数据', 442);
        }

        StationService::queryGaodeId($idList);

        return $this->success('', true);
    }

    /**
     * 线路站点匹配工具
     */
    public function stationFormat()
    {
        $data = false;
        $rules = [
            'email' => 'required',
        ];
        $params = Request::trimNull(array_keys($rules));
        $this->validator($params, $rules, [
            'email.required' => '邮箱地址必填',
        ]);

        //上传文件
        $upload = StationBatchUploadService::onlyUploadExcel('stationFormatFile',['xlsx','xls']);

        $ossPath = $upload['path'];
        if(!$ossPath){
            throw new RuntimeException('上传oss失败', 2);
        }

        //请求foss-task
        $url = env("FOSS_TASK_URL");
        //$url = 'http://foss.chinawayltd.com/foss-task';
        $url = $url.'/oil_xzx_station/lineStationNew';

        //请求ldap
        try {
            $req = Common::request('get',$url,['oss_url'=>$upload['ossUrl'],'email'=>$params['email']]);
            $result = json_decode($req, true);
            if($result['code'] == 0){
                $data = true;
            }else{
                throw new Exception($result['message'],2);
            }
        }catch (\Exception $e){
            throw new Exception('失败，请重试', 403);
        }

        return $this->success($data, true);

        //return $this->success(app(Foss::class)->getDataFromFoss('g7s.station.stationFormat'), true);
    }

    /**
     * 中心点油站匹配工具
     */
    public function stationPointFormat()
    {
        $data = false;
        $rules = [
            'email_p' => 'required',
            'distance' => 'required',
        ];
        $params = Request::trimNull(array_keys($rules));
        $this->validator($params, $rules, [
            'email_p.required' => '邮箱地址必填',
            'distance.required' => '距离必填',
        ]);

        //上传文件
        $upload = StationBatchUploadService::onlyUploadExcel('pointFormatFile',['xlsx','xls']);

        $ossPath = $upload['path'];
        if(!$ossPath){
            throw new \RuntimeException('上传oss失败', 2);
        }

        //请求foss-task
        $url = env("FOSS_TASK_URL");
        //$url = 'http://foss.chinawayltd.com/foss-task';
        $url = $url.'/oil_xzx_station/pointStation';

        //请求ldap
        try {
            $req = Common::request('get',$url,['oss_url'=>$upload['ossUrl'],'email'=>$params['email_p'],"distance"=>$params['distance']]);
            $result = json_decode($req, true);
            if($result['code'] == 0){
                $data = true;
            }else{
                throw new Exception($result['message'],2);
            }
        }catch (\Exception $e){
            throw new Exception('失败，请重试', 403);
        }

        return $this->success($data, true);
        //return $this->success(app(Foss::class)->getDataFromFoss('g7s.station.stationPointFormat'), true);
    }
}
