<?php

namespace App\Http\Controllers\Export;

use App\Exports\StationPriceExport;
use App\Http\Controllers\Controller;
use App\Library\Request;
use App\Services\Export\StationBatchService;
use App\Services\Import\StationBatchUploadService;
use Maatwebsite\Excel\Facades\Excel;

class StationBatchExport extends Controller
{

    public function __construct()
    {
    }

    public function stationBatchExcelHeader()
    {
        $result = StationBatchService::getStationBatchExcelHeader();
        return Excel::download(new StationPriceExport($result['header'], $result['data']), $result['fileName']);
    }

}
