<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/11/15
 * Time: 17:35
 */

namespace App\Http\Middleware;

use App\Library\Helper\Common;
use Closure;
use Illuminate\Http\Request;
use Library\Monitor\Falcon;

class AfterMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->setTag('http.status_code', $response->getStatusCode());
            $rootSpan->log([
                'response_data' => $response->getContent(),
            ]);
        }
        Common::log('info', '响应请求日志', [
            'path'           => $request->path(),
            'headers'        => $request->header(),
            'response_data'  => $response->getContent(),
            'current_time'   => microtime(true),
            'opcache_status' => opcache_get_status(),
        ]);
        return $response;
    }
}
