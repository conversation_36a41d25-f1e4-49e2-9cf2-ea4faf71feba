<?php

namespace App\Listeners;

use App\Events\ChangePriceApprove;
use App\Repositories\WorkOrder\WorkOrderRepository;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Library\Monitor\Falcon;

class SendChangePriceApproveToAdmin implements ShouldQueue
{
    public $queue = 'message-queue';

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ChangePriceApprove  $event
     * @return void
     */
    public function handle(ChangePriceApprove $event)
    {
        $approveId = $event->approveId;
        $approveInfo = app(WorkOrderRepository::class)->getOneWorkerOrderByParams([
            'id' => $approveId
        ]);
        if (empty($approveInfo)) {
            return;
        }

        $extend = json_decode($approveInfo['extend'], true);
        $text = $this->getTitle($approveInfo['status'])."\n\n";
        $text .= $this->getMessage($approveInfo['status'])."\n";
        $text .= '油站：'.(empty($extend['remark_name']) ? $extend['station_name'] : $extend['remark_name'])."\n";
        $text .= '省市：'.$extend['province_name'].$extend['city_name']."\n";
        $text .= '商品：'.$extend['goods']."\n";
        $text .= '变更后的油机价：'.$approveInfo['after_price']."\n";
        $text .= '变更后的G7进价：'.$approveInfo['after_price']."\n";
        $text .= '变更后的生效时间：'.$approveInfo['start_time']."\n";
        $text .= '申请人员：'.$approveInfo['proposer']."\n";
        $text .= '申请时间：'.$approveInfo['create_time']."\n";

        Falcon::feishu(config('feishu.oil_price_change'), $text);
    }

    /**
     * 消息标题
     *
     * @param $status
     * @return string
     */
    protected function getTitle($status)
    {
        switch ($status) {
            case 0:
                $title = '油价变更审核———待审核';
                break;
            case 1:
                $title = '油价变更审核———待生效';
                break;
            case 2:
                $title = '油价变更审核———已生效';
                break;
            case 3:
                $title = '油价变更审核———已驳回';
                break;
            default:
                $title = '';
        }
        return $title;
    }

    /**
     * 消息内容
     *
     * @param $status
     * @return string
     */
    protected function getMessage($status)
    {
        switch ($status) {
            case 0:
                $msg = '站点提交了一份变价申请，请及时审核！并仔细核对销价是否正确。';
                break;
            case 1:
                $msg = '站点提交的变价申请，已审核通过，待生效。';
                break;
            case 2:
                $msg = '站点提交的变价申请，已审核通过并已生效。';
                break;
            case 3:
                $msg = '站点提交的变价申请已驳回。';
                break;
            default:
                $msg = '';
        }

        return $msg;
    }
}
