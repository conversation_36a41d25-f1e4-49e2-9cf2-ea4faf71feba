<?php

namespace App\Listeners;

use App\Events\StationEvent;
use App\Jobs\Push;
use App\Jobs\StationPushLog;
use App\Library\Helper\Common;
use App\Models\Gas\StationModel;
use App\Repositories\Station\StationRepository;
use App\Services\Station\StationBatchService;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

use function foo\func;

class SendPushNotification implements ShouldQueue
{

    public $connection = 'redis';

    public $queue = "batch-price-quick";

    public $tries = 3;

    public $timeout = 60;

    public $retryAfter = 1;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param StationEvent $event
     * @return void
     */
    public function handle(StationEvent $event)
    {
        $delay = abs((strtotime($event->startTime) - time()));
        $stationId = !is_array($event->stationId) ? [$event->stationId] : $event->stationId;
        $stationData = collect(stationRepository::getStation($stationId))->keyBy('id');
        $task_id     = $event->task_id;//推送id
        $stationId = collect($stationId)->filter(function ($item) use ($stationData,$task_id) {
            //不存在的站直接标记删除不推送
            if(!isset($stationData[$item])){
                Common::log('info', '已经删除的站点无须触发推送', ['station_id' => $item]);
                if($task_id){
                    StationPushLog::dispatch([
                        'task_id' => $task_id,
                        'station_id' =>$item,
                        'status'=>6
                    ])->onQueue('long-time-queue');
                }
                return false;
            }
            if ($stationData[$item]['card_classify'] == StationModel::STATION_FOREVER_OFFLINE) {
                Common::log('info', '站点永久下线无须触发推送', [
                    'station_id' => $item
                ]);

                //永久下线（跳过）站点永久下线推送状态更新
                if($task_id){
                    StationPushLog::dispatch([
                        'task_id' => $task_id,
                        'station_id' =>$item,
                        'status'=>5
                    ])->onQueue('long-time-queue');
                }
                return false;
            }
            return true;
        })->toArray();
        switch ($event->push) {
            case 'station':
                $stationList = StationBatchService::getStation(
                    $stationId,
                    $event->caller,
                    '20003JCP',
                    $event->startTime
                );
                break;
            case 'price':
                $stationList = StationBatchService::getPriceForDownStream(
                    $stationId,
                    $event->caller,
                    '20003JCP',
                    $event->startTime
                );
                break;
            default:
                $stationList = StationBatchService::getStationPrice(
                    $stationId,
                    $event->caller,
                    '20003JCP',
                    $event->startTime
                );
        }
        $stationList = count($stationId) == 1 ? [$stationList] : $stationList;
        foreach ($stationList as $item) {
            if (!empty($item['OA'])) {
                $item['OA']['push'] = $event->push;
                $item['OA']['push_target'] = $event->push_target;//定向推送
                $item['OA']['task_id']     = $event->task_id;//推送id
                $item['OA']['header_info'] = $event->headerInfo;

                Push::dispatch('OA', $item['OA'])->delay($delay)->onQueue('batch-price-push-quick');

                Common::log(
                    'info',
                    '价格计算任务成功_StationId_OA_starttime_' . $event->startTime . '_delay_' . $delay,
                    ['param' => $item['OA']['id'], 'value' => $item['OA']]
                );
            }

            //定向推，是推向机构的不能推送GOS
            if (!empty($item['GOS']) && !$event->push_target) {
                $item['GOS']['push'] = $event->push;
                $item['GOS']['header_info'] = $event->headerInfo;
                Push::dispatch('GOS', $item['GOS'])->delay($delay)->onQueue('batch-price-push-quick');
                Common::log(
                    'info',
                    '价格计算任务成功_StationId_GOS',
                    ['param' => $item['GOS']['id'], 'value' => $item['GOS']]
                );
            }
        }
    }
}
