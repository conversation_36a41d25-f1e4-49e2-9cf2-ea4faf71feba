<?php

namespace App\Library;

use App\Library\Helper\Common;
use Illuminate\Database\Connectors\MySqlConnector;
use Illuminate\Support\Facades\Log;
use PDO;

class CustomMysqlConnector extends MySqlConnector
{
    /**
     * Establish a database connection.
     *
     * @param array $config
     * @return PDO
     */
    public function connect(array $config)
    {
        $startTime = microtime(true);
        $pdo = parent::connect($config);
        $endTime = microtime(true);
        $connectionTime = bcsub($endTime, $startTime, 10);
        global $rootSpan;
        if ($rootSpan) {
            $rootSpan->log([
                'start_time'               => $startTime,
                'end_time'                 => $endTime,
                'database_config'          => $config,
                'database_connection_time' => $connectionTime . 's',
            ]);
        }
        Common::log('info', "数据库连接记录", [
            'start_time'               => $startTime,
            'end_time'                 => $endTime,
            'database_connection_time' => $connectionTime . 's',
            'database_config'          => $config,
        ]);
        return $pdo;
    }
}