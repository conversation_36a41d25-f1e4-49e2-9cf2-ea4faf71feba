<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2016/3/1/001
 * Time: 14:00
 */

namespace App\Library\Helper;


class StationPolicy
{
    const FANLI_TYPE    = 1; //返利类别
    const FANLI_PROFIT_MODE    = 2; // 收益模式
    const FANLI_SHAPE    = 3;//返利形式
    const FANLI_UNIT = 4; //返利单位
    const FANLI_LEVEL    = 5; // 返利档位
    const FANLI_WAY    = 6; // 返利发放形式

    const STATION_CLASSIFY = 7; //站点性质

    static public $list = [
        self::FANLI_TYPE    => [
            111=>"有收益（上游有优惠，部分让利下游）",
            112=>"有收益（上游有优惠，下游无优惠）",
            113=>"无收益（上游有优惠，全部让利下游）",

            121=>"有收益（上游无优惠，下游抬高价格）",
            131=>"无收益（上游无优惠，下游无优惠）"
        ],
        self::FANLI_PROFIT_MODE    => [
            1101=>"少扣款模式",
            1102=>"后返利模式",
            1103=>"充值返利模式",
            1104=>"差价模式",

            1301=>"无收益",
            1302 => '差价模式',

            1201 => '差价模式',
            1202 => '无收益',
        ],
        self::FANLI_SHAPE    => [
            110101=>"单笔数量折让",
            110102=>"单笔金额折让",
            110103=>"单笔数量阶梯折让",
            110104=>"单笔金额阶梯折让",

            110201=>"单笔数量返利",
            110202=>"单笔金额返利",
            110203=>"单笔数量阶梯返利",
            110204=>"单笔金额阶梯返利",
            110205=>"月末累计数量返利",
            110206=>"月末累计金额返利",
            110207=>"月末累计数量阶梯返利",
            110208=>"月末累计金额阶梯返利",

            110301=>"充值返利（无最低限额）",
            110302=>"充值返利（有最低限额）",

            110401=>'系统设置单价差',

            120101=>"系统设置单价差",

            120201=>"无优惠",

            130101=>"无优惠",

            130201 => '系统设置单价差'
        ],
        self::FANLI_UNIT => [
            1=>"百分比",
            2=>"元/升",
            3=>"元/公斤",
            6=>'元/立方米',
            4=>"其它",
            5=>"无",
        ],
        self::FANLI_WAY    => [
            6=>"现金",
            7=>"积分",
        ],
        self::FANLI_LEVEL    => [
            8=>"A档",
            9=>"B档",
            10=>"C档",
            11=>"D档",
            12=>"E档",
        ],
        self::STATION_CLASSIFY => [
            1=>"普通自营站点",
            2=>"九鼎指定站点",
            3=>"一客一站（城市合伙人）",
        ],
    ];

    static public function getById($id)
    {
        return isset(self::$list[$id]) ? self::$list[$id] : NULL;
    }


    static public function getAll()
    {
        return self::$list;
    }

    static public function getFirstOilCom()
    {
        return [20,21,22,23,30];
    }
}
