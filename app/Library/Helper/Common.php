<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/10/24
 * Time: 16:38
 */
namespace App\Library\Helper;

use G7\Tracing\TraceContext;
use Illuminate\Support\Facades\Log;
use Library\Monitor\Kafka;
use Library\Monitor\Request;
use App\Library\Request as ClientRequest;
class Common
{
    public static function getDockerName()
    {
        $url = "http://rancher-metadata/latest/self/container/name";
        $result = self::request('GET', $url);
        return $result;
    }

    public static function request($method, $requestUrl,$timeout = 15, $param = [] , $headers = [])
    {
        $startTime = microtime(true);
        $headers = array_merge(
            $headers,
            self::getTraceInjectHeaders()
        );
        $result = Request::requestData($method, $requestUrl, $param, $headers, $timeout,0,function ($req,$result){
            Log::info('Request-requestData,结果:' . json_encode($req, JSON_UNESCAPED_UNICODE) . '返回结果:' . $result);
        });
        $message = [
            'response_time'  => microtime(true) - $startTime,
            'request_uri'    => $requestUrl,
            'request_header' => $headers,
            'request_body'   => $param,
            'response_body'  => json_decode($result, true)
        ];
        Common::log('info', '接口请求日志', $message);
        return $result;
    }


    public static function requestJson($requestUrl, $param = [] , $headers = [])
    {
        $startTime = microtime(true);
        $headers = array_merge(
            $headers,
            self::getTraceInjectHeaders()
        );
        $result = Request::requestJson($requestUrl, $param, $headers, 15, 0);
        $context = [
            'response_time'  => microtime(true) - $startTime,
            'request_uri'    => $requestUrl,
            'request_header' => $headers,
            'request_body'   => $param,
            'response_body'  => json_decode($result, true)
        ];
        self::log('info', 'api请求日志', $context);
        return $result;

    }


    //转成数组
    public static function stdToArray($data)
    {
        return json_decode(json_encode($data), true);
    }

    //  生成UUID，并去掉分割符
    static public function uuid()
    {
        if (function_exists('com_create_guid')) {
            $uuid = com_create_guid();
        } else {
            mt_srand((double)microtime() * 10000);//optional for php 4.2.0 and up.
            $charid = \strtolower(md5(uniqid(rand(), TRUE)));
            $hyphen = chr(45);// "-"
            $uuid   = chr(123)// "{"
                . substr($charid, 0, 8) . $hyphen
                . substr($charid, 8, 4) . $hyphen
                . substr($charid, 12, 4) . $hyphen
                . substr($charid, 16, 4) . $hyphen
                . substr($charid, 20, 12)
                . chr(125);// "}"
        }
        $uuid = str_replace(['{', '}'], '', $uuid);
        return $uuid;
    }


    //获取当前服务IP
    public static function getServerIp()
    {
        $url = "http://rancher-metadata/latest/self/host/agent_ip";
        $ip = self::request('GET', $url);
        return trim($ip);
    }

    /**
     * 把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
     * @param $para
     * @return string
     */
    public static  function createLinkString($para)
    {
        $_tmp = [];
        foreach ($para as $key => $val) {
            $_tmp[] = $key . "" . $val;
        }
        $arg = implode("", $_tmp);

        //如果存在转义字符，那么去掉转义
        //7.4以后不再支持
        if (@get_magic_quotes_gpc()) {
            $arg = stripslashes($arg);
        }

        return $arg;
    }



    //获取api的hosturl地址
    public static function getOpenapiBaseUrl()
    {
        return env('OPENAPI_BASEURL');

    }

    /**
     * 校验是否是子级机构
     * @param $childOrgCode
     * @param $parentCode
     * @return bool
     */
    public static function isChildOrg($parentCode, $childOrgCode)
    {
        if (strpos($childOrgCode, $parentCode) !== false) {
            return true;
        }
        return false;
    }


    //数组多列格式化
    public static function array_column_multi(array $input, array $column_keys) {
        $result = array();
        $column_keys = array_flip($column_keys);
        foreach($input as $key => $el) {
            $result[$key] = array_intersect_key($el, $column_keys);
        }
        return $result;
    }

    //创建分页参数
    public static function createPaginate($total, $per_page, $current_page)
    {
        return [
            'total'        => $total,
            'per_page'     => $per_page,
            'current_page' => $current_page
        ];

    }

    /**
     * php日志统一输出
     * @param string $level
     * @param $message
     * @param array $context
     * @return mixed
     */
    public static function log($level = 'info', $message, $context = [])
    {
        return \Log::$level($message, $context);
    }

    /* 是否在机构树中
    * @param $parentCode
    * @param $childOrgCode
    * @return bool
    */
    public static function inOrgTree($parentCode, $childOrgCode)
    {
        if (strpos($childOrgCode, substr($parentCode, 0, 6)) !== false) {
            return true;
        }
        return false;
    }

    public static function nowTime()
    {
        return date("Y-m-d H:i:s",time());
    }

    /**
     * 获取唯一ID
     *
     * @return string
     */
    static public function getOnlyId()
    {
        return md5(self::getIp() . $_SERVER['HTTP_USER_AGENT'] . gettimeofday(true) . lcg_value());
    }

    /**
     * 获取IP
     */
    static public function getIp()
    {
        if (getenv("HTTP_CLIENT_IP"))
            $ip = getenv("HTTP_CLIENT_IP");
        else if (getenv("HTTP_X_FORWARDED_FOR"))
            $ip = getenv("HTTP_X_FORWARDED_FOR");
        else if (getenv("REMOTE_ADDR"))
            $ip = getenv("REMOTE_ADDR");
        else
            $ip = "UNKNOWN";
        return $ip;
    }

    /**
     * 计算两点地理坐标之间的距离
     * @param Decimal $longitude1 起点经度
     * @param Decimal $latitude1 起点纬度
     * @param Decimal $longitude2 终点经度
     * @param Decimal $latitude2 终点纬度
     * @param Int $unit 单位 1:米 2:公里
     * @param Int $decimal 精度 保留小数位数
     * @return Decimal
     */
    static public function getDistance($longitude1, $latitude1, $longitude2, $latitude2, $unit = 2, $decimal = 2)
    {

        $EARTH_RADIUS = 6370.996; // 地球半径系数
        $PI           = 3.1415926;

        $radLat1 = $latitude1 * $PI / 180.0;
        $radLat2 = $latitude2 * $PI / 180.0;

        $radLng1 = $longitude1 * $PI / 180.0;
        $radLng2 = $longitude2 * $PI / 180.0;

        $a = $radLat1 - $radLat2;
        $b = $radLng1 - $radLng2;

        $distance = 2 * asin(sqrt(pow(sin($a / 2), 2) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2)));
        $distance = $distance * $EARTH_RADIUS * 1000;

        if ($unit == 2) {
            $distance = $distance / 1000;
        }
        return number_format($distance, $decimal, ".", "");
    }


    public static function GasGetHttp($url)
    {
        try{
            $output = file_get_contents($url);
            $result = json_decode($output);
            if(json_last_error() !== JSON_ERROR_NONE) {
                throw new \RuntimeException(json_last_error_msg(),2);
            }
        }catch (\Exception $e){
            throw new \RuntimeException($e->getMessage(),$e->getCode());
        }
    }

    /**
     * 调用GAS系统使用，使用GuzzleHttp方式，gas系统获取不到参数
     * Curl Post
     * @return mixed
     */
    public static function GasPostHttp($url,$postData,$timeout=15)
    {
        $startTime  = microtime(true);
        parse_str($_SERVER['QUERY_STRING'], $arrQuery);
        $headers = self::getTraceInjectHeaders();
        $headers['logid'] = isset($_SERVER['HTTP_LOGID']) ? $_SERVER['HTTP_LOGID'] : (isset($arrQuery['logid']) ? $arrQuery['logid'] : uniqid() . rand(1, 1000));
        $headers['trace'] = isset($_SERVER['HTTP_TRACE']) ? intval($_SERVER['HTTP_TRACE']) + 1 : (isset($arrQuery['trace']) ? intval($arrQuery['trace']) + 1 : 0);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 'application/x-www-form-urlencoded');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        if (false !== strpos($url, 'https://')) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0); // 跳过证书检查
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);  // 从证书中检查SSL加密算法是否存在
        }

        $output = curl_exec($ch);
        $curlError = curl_error($ch);
        $curlErrorNo = curl_errno($ch);
        curl_close($ch);
        Request::requestToFalcon($url,(microtime(true) - $startTime)*1000, $curlErrorNo);

        if ($curlErrorNo) {
            Log::info('curlError:' . $curlError);
            throw new \RuntimeException($curlError, $curlErrorNo);
        }
        $result = json_decode($output,true);
        if(json_last_error() !== JSON_ERROR_NONE) {
            Log::info('json解析错误:' . json_last_error_msg());
            throw new \RuntimeException(json_last_error_msg(),2);
        }
        return $result;
    }

    /**
     * 保留N     * 位小数
     * @param float $num
     * @param string $dot
     * @param string $flag true四舍五入，false舍去不入
     * @return unknown|string
     */
    public static function xsprintf($num, $dot = '2', $flag = true)
    {

        if (true === $flag) {
            if ($dot < 1) {
                return round($num, 0);
            }
            $r_num = round($num, $dot);
            $num_arr = explode('.', "$r_num");
            if (count($num_arr) == 1) {
                return "$r_num" . '.' . str_repeat('0', $dot);
            }
            $point_str = "$num_arr[1]";
            if (strlen($point_str) < $dot) {
                $point_str = str_pad($point_str, $dot, '0');
            }
            $num = $num_arr[0] . '.' . $point_str;
        } else {
            $d = $dot + 1;
            $num = substr(sprintf("%.{$d}f", $num), 0, -1);
        }

        return $num;
    }

    /**
     * 对数字字符串保留前几位，后几位，剩下替换成*
     * @param $string string 需要处理的字符串
     * @param $start int 保留前几位
     * @param $end int 保留后几位
     * @return string
     */
    public static function secretString($string,$start=5,$end=4)
    {
        $strlen = strlen($string);
        $lastLen = $strlen-$start-$end;
        $secretStr ='';
        for($i=0;$i<$lastLen;$i++) {
            $secretStr .='*';
        }
        $startString=substr($string,0,$start);
        $endString=substr($string,-$end);
        $resultString = $startString.$secretStr.$endString;
        return $resultString;
    }

    /**
     * 必填参数检测
     * @param array $checkFields
     * @param array $args
     * @param bool $is_null
     * @param int $error_code
     * @return bool|void
     * @throws \Exception
     */
    public static function argumentCheck($checkFields = [], $args = [], $is_null = TRUE, $error_code = 404002)
    {
        if (!$checkFields) {
            return;
        } elseif (!$args) {
            throw new \RuntimeException ('参数全部为空', $error_code);
        }
        
        $args = (array)$args;
        
        foreach ($checkFields as $key => $value) {
            if (!array_key_exists($value, $args)) {
                throw new \RuntimeException ($value . '缺失', $error_code);
            } elseif (array_key_exists($value, $args) && $is_null) {
                if (is_null($args [$value]) || $args[$value] === '')
                    throw new \RuntimeException ($value . '不能为空', $error_code);
            }
        }
        
        return TRUE;
    }

    public static function getTraceInjectHeaders(bool $curlFormat = false): array
    {
        if (php_sapi_name() === 'cli' || php_sapi_name() === 'phpdbg') {
            return [];
        }
        $injectHeaders = TraceContext::instance()->getTracer()->getInjectHeaders();
        if ($curlFormat) {
            return array_map(function ($v, $k) {
                return "$k: $v";
            }, $injectHeaders, array_keys($injectHeaders));
        }
        return $injectHeaders;
    }
}