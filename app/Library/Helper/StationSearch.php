<?php


namespace App\Library\Helper;


class StationSearch
{
    /**
     * 按半径获取经纬度区间
     *
     * @param $lng
     * @param $lat
     * @param $distance
     * @return array
     */
    static public function getLngLatBetween($lng, $lat, $distance)
    {
        // 地球半径
        $earthGirth = 40030173;
        // 经度半径
        $lngGirth = $earthGirth * cos($lat/180);
        // 经度范围
        $perLng = (360 / $lngGirth) * $distance;
        // 纬度范围
        $perLat = (360/ $earthGirth) * $distance;
        return [
            'ge_lng' => round($lng - $perLng, 6),
            'le_lng' => round($lng + $perLng, 6),
            'ge_lat' => round($lat - $perLat, 6),
            'le_lat' => round($lat + $perLat, 6),
        ];
    }

    /**
     * 根据距离计算纬度差
     *
     * @param $distance
     * @return int|string|null
     */
    public static function getLatByDistance($distance)
    {
        if (empty($distance)) {
            return 0;
        }
        // 1纬度 = 111111.11米
        return bcdiv($distance, 111111.11, 6);
    }
}
