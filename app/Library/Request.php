<?php
/**
 * Created by PhpStorm.
 * User: yuanzhi
 * Date: 2019/10/21
 * Time: 20:24
 */
namespace App\Library;
use Request as pRequest;

class Request extends pRequest {

    public  static function all()
    {
        if(pRequest::isJson()) {
            $json = pRequest::getContent();
            return json_decode($json, true);
        }else {
            return pRequest::all();
        }
    }
}