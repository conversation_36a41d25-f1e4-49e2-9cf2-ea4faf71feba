replicaCount: 1

env:
- name: SIDECAR__PORT
  value: '80'
- name: SIDECAR__LISTEN_PORT
  value: '30081'
- name: aliyun_logs_k8s-petroleum-card-logs-topic
  value: stdout
- name: aliyun_logs_k8s-petroleum-card-logs-topic_tags
  value: env=dev,product=petroleum-card,appid=foss-user-web

image:
  repository: registry.cn-beijing.aliyuncs.com/php-spec/foss-user-web:c992ec0b.2

nameOverride: "foss-user-web"
fullnameOverride: "foss-user-web"

configmap:
  envdata:
    APPLICATION__NAME: "foss-user-web"
    PHPENV: "local"
    APP__NAME: "foss-user"
    APP__ENV: "dev"
    CRONTAB__ENABLE: "on"
    RUN_SCRIPTS: "1"
    SUPERVISOR__ENABLE: "on"
    PHP_ERRORS_STDERR: "1"
    PHP_MEM_LIMIT: "1024"
    PHP_POST_MAX_SIZE: "2000"
    SERVICE_GROUP: "web"
resources:
  limits:
    cpu: 500m
    memory: 1000Mi
  requests:
    cpu: 200m
    memory: 200Mi

ports:
  - name: http
    containerPort: 80
    protocol: TCP

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: ingress-inner
  hosts:
    - host: foss.dev.chinawayltd.com
      paths:
        - /user