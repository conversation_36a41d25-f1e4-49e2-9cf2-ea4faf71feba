<?php

namespace Tests\Unit;

use App\Models\Gas\StationOrgRuleListModel;
use App\Models\Gas\StationOrgRuleModel;
use App\Services\Rule\RuleFilterService;
use App\Services\Rule\RuleService;
use App\Services\StationOrgRule\StationOrgRuleService;
use Tests\TestCase;

class StationRuleTest extends TestCase
{

    public function testGetFilterStationId()
    {

        $orgCode = '200I1A0101';
//        $orgCode = '200I1A01';
        $result = StationOrgRuleService::getFilterStationId($orgCode);
        dd($result);
    }

    public function testGetFilterStationRule()
    {

        $orgCode = '200I1A0101';
//        $orgCode = '200I1A01';
        $result1 = StationOrgRuleService::getFilterStationRule($orgCode);
        $result2 = StationOrgRuleService::getFilterStationRule($orgCode);
        dump(json_encode($result1));
        dd(json_encode($result2));
    }


    public function testStationRuleV1toV2()
    {

        $params = [
            'orgcodeList' => ['200NW5']
        ];
        request()->offsetSet('user_name', 'system');
        RuleService::syncV1RuleToV2($params);
        dd(1);
    }



    public function testPrice()
    {
        //"station_id":"18a89006f8c711ebbf8cb6fab27f9f6d","orgcode":"200NW5","limit_orgcode":"200NW502"
        $param = [
            'station_id'=>'18a89006f8c711ebbf8cb6fab27f9f6d',
            'orgcode'=>'200NW5',
            'limit_orgcode' => '200NW502',
        ];
        $result = RuleFilterService::getOilListAndPrice($param);
        dd($result);
    }


    public function testPushRuleOAParams()
    {
        request()->offsetSet('user_name', 'system');
        $rule_id = "6558350e-efce-4ef2-1ded-68d71cd3c51e";
        $msg =[];
        $info = StationOrgRuleModel::where('id',$rule_id)->first();

//        dd($info);
        if (!empty($info) && $info->sync_push == 2) {
//            $update['push_end_time'] = $now;
//            $update['sync_push'] = 2;
//            StationOrgRuleModel::where('id',$rule_id)->update($update);
            // 推送OA
            $rule_list = StationOrgRuleListModel::getListByRuleId($rule_id);
//            $this->sendOARule($rule_id, $info->orgcode, $rule_list);

            $push_list = StationOrgRuleModel::PUSHOALIST;//推送的数据
            $push_rule = [];
            foreach ($push_list as $key){
                $push_rule[] = ['type'=>$key,'value'=> isset($rule_list[$key]) && $rule_list[$key]['has_rule'] == 2 ? explode(',',$rule_list[$key]['value']) : []];
            }
            $msg = [
                'id'=>$rule_id,
                'org_code'=>$info->orgcode,
                'push_value' =>$push_rule
            ];

        }
        dd($msg);
    }

}
