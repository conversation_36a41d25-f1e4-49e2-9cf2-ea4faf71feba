#user  nobody;
worker_rlimit_nofile 65535;
worker_processes  8;

#error_log  /data/log/nginx/error.log warn;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        run/nginx.pid;


events {
    worker_connections  65535;
    use epoll;
    multi_accept on;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    connection_pool_size        1024;
    client_body_buffer_size     16k;
    client_body_temp_path       /var/cache/nginx/client_temp 1 2;
    client_body_timeout         30;
    client_header_buffer_size   4k;
    large_client_header_buffers 4 4k;
    client_header_timeout       30;
    keepalive_requests 20000;
    send_timeout 30;
    tcp_nopush  off;
    server_names_hash_max_size      512;
    server_names_hash_bucket_size   128;
    open_file_cache off;
    index   index.php index.html index.htm;
    fastcgi_connect_timeout     300;
    fastcgi_read_timeout        300;
    fastcgi_send_timeout        300;
    fastcgi_temp_path           /var/cache/nginx/fastcgi_temp 1 2;
    fastcgi_buffer_size         4k;
    fastcgi_buffers             16 4k;
    fastcgi_busy_buffers_size   8k;
    fastcgi_temp_file_write_size 8k;
    fastcgi_max_temp_file_size  256k;
    fastcgi_intercept_errors    on;
    fastcgi_index               index.php;
    proxy_temp_path             /var/cache/nginx/proxy_temp;
    proxy_buffer_size           4k;
    proxy_buffering             on;
    proxy_buffers               256 4k;
    proxy_busy_buffers_size     8k;
    gzip                on;
    gzip_buffers        16 4k;
    gzip_comp_level     1;
    gzip_http_version   1.1;
    gzip_min_length     1024;
    gzip_types          text/css text/xml text/plain text/vnd.wap.wml application/x-javascript  application/rss+xml application/xhtml+xml;
    proxy_set_header   X-Real-IP        $remote_addr;
    proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
    add_header X-Cost $request_time;

    log_format main '$remote_addr - $remote_user [$time_local] $request '
                    '"$status" $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    log_format moss '$remote_addr | $http_x_forwarded_for | $remote_user | [$time_local] |'
                    ' "$request" | $status | $body_bytes_sent |'
                    ' "$http_referer" | "$http_user_agent" | $request_time | $upstream_response_time';

    log_format full '$remote_addr | $http_x_forwarded_for | $remote_user | [$time_local] |'
                        ' "$request" | $request_body | $status | $body_bytes_sent |'
                        ' "$http_referer" | "$http_user_agent" | $request_time | $upstream_response_time';

    log_format json '{"request_id": "$request_id", "type": "pa_nginx", "remote_addr": "$remote_addr", "http_host": "$http_host", "time_local": "$time_local", "request": "$request", "status": "$status", "content_length": "$content_length", "body_bytes_sent": "$body_bytes_sent", "http_referer": "$http_referer", "http_user_agent": "$http_user_agent", "http_x_forwarded_for": "$http_x_forwarded_for", "request_time": "$request_time", "upstream_response_time": "$upstream_response_time"}';

    log_format json_full '{"request_id": "$request_id", "type": "pa_nginx", "remote_addr": "$remote_addr", "http_host": "$http_host", "time_local": "$time_local", "request": "$request", "status": "$status", "content_length": "$content_length", "body_bytes_sent": "$body_bytes_sent", "http_referer": "$http_referer", "http_user_agent": "$http_user_agent", "http_x_forwarded_for": "$http_x_forwarded_for", "request_time": "$request_time", "upstream_response_time": "$upstream_response_time", "http_cookie": "$http_cookie", "sent_http_set_cookie": "$sent_http_set_cookie", "sent_http_location": "$sent_http_location", "request_body": "$request_body"}';


    # Name servers used to resolve names of upstream servers into addresses.
    # It's also needed when using tcpsocket and udpsocket in Lua modules.
    #resolver ************** **************;

    # Don't tell nginx version to clients.
    server_tokens off;

    # Specifies the maximum accepted body size of a client request, as
    # indicated by the request header Content-Length. If the stated content
    # length is greater than this size, then the client receives the HTTP
    # error code 413. Set to 0 to disable.
    client_max_body_size 32m;

    # Timeout for keep-alive connections. Server will close connections after
    # this time.
    keepalive_timeout 120s 120s;

    # Sendfile copies data between one FD and other from within the kernel,
    # which is more efficient than read() + write().
    sendfile on;

    # Don't buffer data-sends (disable Nagle algorithm).
    # Good for sending frequent small bursts of data in real time.
    tcp_nodelay on;

    # Causes nginx to attempt to send its HTTP response head in one packet,
    # instead of using partial frames.
    #tcp_nopush on;


    # Path of the file with Diffie-Hellman parameters for EDH ciphers.
    #ssl_dhparam /etc/ssl/nginx/dh2048.pem;

    # Specifies that our cipher suits should be preferred over client ciphers.
    ssl_prefer_server_ciphers on;

    # Enables a shared SSL cache with size that can hold around 8000 sessions.
    ssl_session_cache shared:SSL:2m;

    # Set the Vary HTTP header as defined in the RFC 2616.
    gzip_vary on;

    reset_timedout_connection on;

    include /etc/nginx/sites-enabled/*;
}
#daemon off;
