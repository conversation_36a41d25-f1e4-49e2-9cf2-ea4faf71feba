<?php

return [
    'allowPassCheckSign'      => [
        "api" => [
            "CommonController@sign",
            "CommonController@gasTest",
            "CommonController@test",
            "CommonController@uuid",
            "<PERSON>Controller@imitateWrittenOff",
            "<PERSON><PERSON><PERSON>roller@cancelZwlOrder",
            "OilController@getGunNosByStationAndOil",
            "OilController@getGunNosByStation",
            "OilController@getGunNosByStationIds",
            "CodeController@getSecondaryPaymentQrCode",
            "CouponController@proxy",
            "StationController@getInterfaceForElectricity",
        ],
        "web" => [
            "LoginController@index",
            "LoginController@in",
            "LoginController@getCaptcha",
        ]
    ],
    'allowPassCheckFrequency' => [
        'api' => [
            "SignController@make",
            "DataController@push",
            "DataController@getStationWhiteList",
            "DataController@getStopStationAndAvailableStationForCustomerConfig",
            "DataController@receiveStationBlackAndWhiteList",
            "DataController@receiveStationPushRule",
            "OrderController@refundForOrderCenter",
        ],
    ],
];
